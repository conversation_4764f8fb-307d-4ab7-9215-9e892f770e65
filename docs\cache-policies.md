# Políticas de Cache - Estação da Alfabetização

## Visão Geral

Este documento descreve as políticas de cache implementadas no projeto Estação da Alfabetização. As políticas de cache são configurações que determinam como os dados são armazenados, expirados e invalidados no sistema de cache Valkey.

## Tipos de Políticas

### 1. Políticas de Expiração

As políticas de expiração determinam por quanto tempo os dados permanecem no cache antes de serem considerados obsoletos.

#### Estratégias de Expiração

| Estratégia | Descrição | Uso Recomendado |
|------------|-----------|-----------------|
| **FIXED** | Tempo de expiração fixo | Dados com ciclo de vida previsível |
| **SLIDING** | Tempo renovado a cada acesso | Dados acessados frequentemente |
| **ADAPTIVE** | Tempo ajustado com base na frequência de acesso | Dados com padrão de acesso variável |
| **NONE** | Sem expiração automática | Dados que só devem ser invalidados explicitamente |

#### Configuração por Tipo de Dado

| Tipo de Dado | Estratégia | TTL Padrão | Descrição |
|--------------|------------|------------|-----------|
| `user` | SLIDING | 30 minutos | Dados de perfil e preferências de usuário |
| `session` | SLIDING | 30 minutos | Dados de sessão de usuário |
| `token` | FIXED | 15 minutos | Tokens de acesso |
| `refresh_token` | FIXED | 7 dias | Tokens de refresh |
| `product` | ADAPTIVE | 1 hora | Dados de produtos |
| `category` | FIXED | 2 horas | Dados de categorias |
| `search` | FIXED | 5 minutos | Resultados de busca |
| `order` | SLIDING | 30 minutos | Dados de pedidos |
| `payment` | FIXED | 30 minutos | Dados de pagamentos |
| `static` | FIXED | 7 dias | Conteúdo estático |
| `config` | FIXED | 1 hora | Dados de configuração |

### 2. Políticas de Invalidação

As políticas de invalidação determinam quando os dados no cache devem ser removidos antes de sua expiração natural.

#### Estratégias de Invalidação

| Estratégia | Descrição | Uso Recomendado |
|------------|-----------|-----------------|
| **TIME_BASED** | Invalidação baseada em tempo (TTL) | Dados com ciclo de vida previsível |
| **EVENT_BASED** | Invalidação baseada em eventos | Dados que mudam em resposta a eventos específicos |
| **VERSION_BASED** | Invalidação baseada em versão | Dados com controle de versão |
| **HASH_BASED** | Invalidação baseada em hash | Dados cujo conteúdo determina a validade |
| **DEPENDENCY_BASED** | Invalidação baseada em dependências | Dados que dependem de outros dados |
| **MANUAL** | Invalidação manual | Dados que só devem ser invalidados explicitamente |

#### Eventos de Invalidação

| Tipo de Dado | Eventos de Invalidação |
|--------------|------------------------|
| `user` | `user:update`, `user:delete`, `user:preferences:update` |
| `product` | `product:update`, `product:delete`, `product:price:update`, `product:stock:update` |
| `category` | `category:update`, `category:delete`, `category:order:update` |
| `order` | `order:update`, `order:status:update`, `order:payment:update` |
| `payment` | `payment:update`, `payment:status:update` |
| `session` | `user:logout`, `user:password:update`, `session:revoke` |
| `search` | `product:update`, `product:delete`, `category:update`, `category:delete` |
| `page` | `content:update`, `content:delete`, `settings:update` |

### 3. Limites de Memória

Os limites de memória determinam quanto espaço o cache pode ocupar e como os itens são removidos quando o limite é atingido.

#### Configurações por Ambiente

| Ambiente | Limite de Memória | Política de Evicção | Descrição |
|----------|-------------------|---------------------|-----------|
| `development` | 100MB | ALLKEYS_LRU | Configuração para ambiente de desenvolvimento |
| `test` | 200MB | ALLKEYS_LRU | Configuração para ambiente de teste |
| `production` | 1GB | VOLATILE_LFU | Configuração para ambiente de produção |
| `production-high-load` | 4GB | VOLATILE_LFU | Configuração para ambiente de produção com alta carga |

#### Políticas de Evicção

| Política | Descrição | Uso Recomendado |
|----------|-----------|-----------------|
| **LRU** | Remove os itens menos recentemente acessados | Dados com acesso temporal |
| **LFU** | Remove os itens menos frequentemente acessados | Dados com acesso baseado em popularidade |
| **RANDOM** | Remove itens aleatoriamente | Quando não há padrão claro de acesso |
| **TTL** | Remove os itens mais próximos de expirar | Quando a expiração é mais importante |
| **NO_EVICTION** | Não remove itens, retorna erro | Quando todos os dados são críticos |

## Implementação

### Arquivos de Configuração

- `src/config/cache/expiration-policy.config.ts`: Configurações de expiração
- `src/config/cache/invalidation-strategy.config.ts`: Configurações de invalidação
- `src/config/cache/memory-limits.config.ts`: Configurações de limites de memória

### Serviços

- `src/services/cachePolicyService.ts`: Implementação das políticas de cache
- `src/services/cacheService.ts`: Serviço de cache com integração das políticas

### Uso no Código

```typescript
// Armazenar um valor no cache com política de expiração
await cacheService.set('user', 'user:123', userData);

// Recuperar um valor do cache (atualiza TTL para estratégias deslizantes)
const userData = await cacheService.get('user', 'user:123');

// Invalidar cache com base em um evento
await cacheService.invalidateOnEvent('user:update', { id: '123' });
```

## Monitoramento e Manutenção

### Monitoramento de Uso de Memória

O sistema monitora automaticamente o uso de memória do cache e toma ações quando os limites são atingidos:

- **Aviso**: Quando o uso atinge o limite de aviso (configurável por ambiente)
- **Crítico**: Quando o uso atinge o limite crítico, inicia limpeza de emergência

### Limpeza de Emergência

Quando o uso de memória atinge o limite crítico, o sistema realiza uma limpeza de emergência:

1. Remove caches temporários (`temp`, `metrics`)
2. Remove caches de curta duração (`search`, `query`)
3. Remove caches de página (`page`)

### Comandos Úteis para Manutenção

```bash
# Verificar estatísticas do cache
valkey-cli info memory

# Limpar todo o cache
valkey-cli flushdb

# Limpar apenas chaves com um padrão específico
valkey-cli keys "user:*" | xargs valkey-cli del
```

## Boas Práticas

1. **Escolha TTLs Apropriados**: Use TTLs mais curtos para dados que mudam frequentemente e TTLs mais longos para dados estáticos.

2. **Use Invalidação Baseada em Eventos**: Sempre que possível, use invalidação baseada em eventos para manter o cache atualizado.

3. **Monitore o Uso de Memória**: Acompanhe regularmente o uso de memória do cache para evitar problemas de performance.

4. **Teste com Carga Real**: Teste as políticas de cache com padrões de acesso realistas para garantir que funcionem conforme esperado.

5. **Documente Eventos de Invalidação**: Mantenha uma lista atualizada de eventos que invalidam o cache para facilitar a manutenção.

## Referências

- [Documentação do Valkey](https://valkey.io/docs/)
- [Estratégias de Cache](https://codeahoy.com/2017/08/11/caching-strategies-and-how-to-choose-the-right-one/)
- [Políticas de Evicção do Redis](https://redis.io/docs/reference/eviction/)
