---
import { PageTransition } from '../../../components/transitions';
import DaisyCard from '../../../components/ui/DaisyCard.astro';
import Breadcrumbs from '../../../components/ui/navigation/Breadcrumbs.astro';
/**
 * Página de Envio de Notificações
 *
 * Interface para enviar notificações para usuários.
 * Parte da implementação da tarefa 8.5.2 - Notificações externas
 */
import BaseLayout from '../../../layouts/BaseLayout.astro';
import { Container, Section } from '../../../layouts/grid';

// Título da página
const title = 'Enviar Notificações';

// Breadcrumbs para a página atual
const breadcrumbItems = [
  { href: '/', label: 'Início' },
  { href: '/admin', label: 'Administração' },
  { href: '/admin/notifications', label: 'Notificações' },
  { label: 'Enviar' },
];

// Tipos de notificação
const notificationTypes = [
  { id: 'info', label: 'Informações', icon: 'info-circle' },
  { id: 'success', label: 'Sucesso', icon: 'check-circle' },
  { id: 'warning', label: 'Avisos', icon: 'alert-triangle' },
  { id: 'error', label: 'Erros', icon: 'x-circle' },
  { id: 'system', label: 'Sistema', icon: 'settings' },
  { id: 'payment', label: 'Pagamentos', icon: 'credit-card' },
  { id: 'order', label: 'Pedidos', icon: 'shopping-bag' },
  { id: 'product', label: 'Produtos', icon: 'package' },
  { id: 'content', label: 'Conteúdo', icon: 'file-text' },
  { id: 'message', label: 'Mensagens', icon: 'message-circle' },
];

// Canais de notificação
const notificationChannels = [
  { id: 'in_app', label: 'No aplicativo', icon: 'bell' },
  { id: 'email', label: 'E-mail', icon: 'mail' },
  { id: 'push', label: 'Push', icon: 'smartphone' },
  { id: 'sms', label: 'SMS', icon: 'message-square' },
];

// Templates de e-mail disponíveis
const emailTemplates = [
  { id: 'notification', label: 'Notificação Padrão' },
  { id: 'welcome', label: 'Boas-vindas' },
  { id: 'password_reset', label: 'Redefinição de Senha' },
  { id: 'order_confirmation', label: 'Confirmação de Pedido' },
  { id: 'payment_confirmation', label: 'Confirmação de Pagamento' },
  { id: 'new_content', label: 'Novo Conteúdo' },
];

// Grupos de usuários
const userGroups = [
  { id: 'all', label: 'Todos os usuários' },
  { id: 'active', label: 'Usuários ativos' },
  { id: 'inactive', label: 'Usuários inativos' },
  { id: 'premium', label: 'Usuários premium' },
  { id: 'free', label: 'Usuários gratuitos' },
  { id: 'new', label: 'Novos usuários (últimos 30 dias)' },
];

// Processar o formulário
let formSubmitted = false;
let formSuccess = false;
let formError = '';

if (Astro.request.method === 'POST') {
  try {
    // Em um cenário real, aqui seria implementada a lógica para:
    // 1. Receber os dados do formulário
    // 2. Validar os dados
    // 3. Enviar as notificações
    // 4. Redirecionar ou exibir mensagem de sucesso

    formSubmitted = true;
    formSuccess = true;

    // Por enquanto, apenas simulamos o sucesso
  } catch (error) {
    console.error('Erro ao enviar notificações:', error);
    formSubmitted = true;
    formError = 'Ocorreu um erro ao enviar as notificações. Por favor, tente novamente.';
  }
}
---

<BaseLayout title={title} enableViewTransitions={true} transitionType="fade">
  <Container>
    <PageTransition type="fade" duration="0.5s">
      <Section>
        <Breadcrumbs items={breadcrumbItems} class="mb-6" />
        
        <h1 class="text-3xl font-bold mb-6">{title}</h1>
        
        {formSubmitted && formSuccess && (
          <div class="alert alert-success mb-6">
            <i class="icon icon-check-circle"></i>
            <span>Notificações enviadas com sucesso!</span>
          </div>
        )}
        
        {formError && (
          <div class="alert alert-error mb-6">
            <i class="icon icon-alert-circle"></i>
            <span>{formError}</span>
          </div>
        )}
        
        <form action="/admin/notifications/send" method="POST" class="space-y-6">
          <DaisyCard>
            <div class="p-6">
              <h2 class="text-xl font-bold mb-4">Destinatários</h2>
              
              <div class="space-y-4">
                <div class="form-control">
                  <label class="label">
                    <span class="label-text">Método de Seleção</span>
                  </label>
                  <div class="flex flex-wrap gap-4">
                    <label class="flex items-center gap-2 cursor-pointer">
                      <input 
                        type="radio" 
                        name="recipient_method" 
                        value="group" 
                        class="radio radio-primary" 
                        checked
                      />
                      <span>Grupo de Usuários</span>
                    </label>
                    
                    <label class="flex items-center gap-2 cursor-pointer">
                      <input 
                        type="radio" 
                        name="recipient_method" 
                        value="specific" 
                        class="radio radio-primary"
                      />
                      <span>Usuários Específicos</span>
                    </label>
                  </div>
                </div>
                
                <div id="group-selection" class="form-control">
                  <label class="label">
                    <span class="label-text">Grupo de Usuários</span>
                  </label>
                  <select name="user_group" class="select select-bordered w-full">
                    {userGroups.map(group => (
                      <option value={group.id}>{group.label}</option>
                    ))}
                  </select>
                </div>
                
                <div id="specific-users" class="form-control hidden">
                  <label class="label">
                    <span class="label-text">IDs ou E-mails dos Usuários</span>
                    <span class="label-text-alt">Um por linha</span>
                  </label>
                  <textarea 
                    name="specific_users" 
                    class="textarea textarea-bordered h-24" 
                    placeholder="user123&#10;<EMAIL>"
                  ></textarea>
                </div>
              </div>
            </div>
          </DaisyCard>
          
          <DaisyCard>
            <div class="p-6">
              <h2 class="text-xl font-bold mb-4">Conteúdo da Notificação</h2>
              
              <div class="space-y-4">
                <div class="form-control">
                  <label class="label">
                    <span class="label-text">Tipo de Notificação</span>
                  </label>
                  <select name="notification_type" class="select select-bordered w-full">
                    {notificationTypes.map(type => (
                      <option value={type.id}>{type.label}</option>
                    ))}
                  </select>
                </div>
                
                <div class="form-control">
                  <label class="label">
                    <span class="label-text">Título</span>
                  </label>
                  <input 
                    type="text" 
                    name="title" 
                    class="input input-bordered w-full" 
                    placeholder="Título da notificação"
                    required
                  />
                </div>
                
                <div class="form-control">
                  <label class="label">
                    <span class="label-text">Conteúdo</span>
                  </label>
                  <textarea 
                    name="content" 
                    class="textarea textarea-bordered h-32" 
                    placeholder="Conteúdo da notificação"
                    required
                  ></textarea>
                </div>
                
                <div class="form-control">
                  <label class="label">
                    <span class="label-text">URL de Ação (opcional)</span>
                  </label>
                  <input 
                    type="url" 
                    name="action_url" 
                    class="input input-bordered w-full" 
                    placeholder="https://exemplo.com/pagina"
                  />
                </div>
                
                <div class="form-control">
                  <label class="label">
                    <span class="label-text">Texto do Botão de Ação (opcional)</span>
                  </label>
                  <input 
                    type="text" 
                    name="action_label" 
                    class="input input-bordered w-full" 
                    placeholder="Saiba mais"
                  />
                </div>
                
                <div class="form-control">
                  <label class="label">
                    <span class="label-text">URL da Imagem (opcional)</span>
                  </label>
                  <input 
                    type="url" 
                    name="image_url" 
                    class="input input-bordered w-full" 
                    placeholder="https://exemplo.com/imagem.jpg"
                  />
                </div>
              </div>
            </div>
          </DaisyCard>
          
          <DaisyCard>
            <div class="p-6">
              <h2 class="text-xl font-bold mb-4">Canais de Envio</h2>
              
              <div class="space-y-4">
                <div class="form-control">
                  <label class="label">
                    <span class="label-text">Selecione os canais de envio</span>
                  </label>
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {notificationChannels.map(channel => (
                      <label class="flex items-center gap-2 cursor-pointer">
                        <input 
                          type="checkbox" 
                          name="channels[]" 
                          value={channel.id} 
                          class="checkbox checkbox-primary" 
                          checked={channel.id === 'in_app'}
                        />
                        <i class={`icon icon-${channel.icon}`}></i>
                        <span>{channel.label}</span>
                      </label>
                    ))}
                  </div>
                </div>
                
                <div id="email-options" class="space-y-4 border-t border-base-300 pt-4 mt-4">
                  <h3 class="font-bold">Opções de E-mail</h3>
                  
                  <div class="form-control">
                    <label class="label">
                      <span class="label-text">Template de E-mail</span>
                    </label>
                    <select name="email_template" class="select select-bordered w-full">
                      {emailTemplates.map(template => (
                        <option value={template.id}>{template.label}</option>
                      ))}
                    </select>
                  </div>
                </div>
              </div>
            </div>
          </DaisyCard>
          
          <DaisyCard>
            <div class="p-6">
              <h2 class="text-xl font-bold mb-4">Opções de Agendamento</h2>
              
              <div class="space-y-4">
                <div class="form-control">
                  <label class="flex items-center gap-2 cursor-pointer">
                    <input 
                      type="checkbox" 
                      name="schedule_enabled" 
                      id="schedule-checkbox"
                      class="checkbox checkbox-primary" 
                    />
                    <span>Agendar envio</span>
                  </label>
                </div>
                
                <div id="schedule-options" class="hidden space-y-4">
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="form-control">
                      <label class="label">
                        <span class="label-text">Data</span>
                      </label>
                      <input 
                        type="date" 
                        name="schedule_date" 
                        class="input input-bordered" 
                      />
                    </div>
                    
                    <div class="form-control">
                      <label class="label">
                        <span class="label-text">Hora</span>
                      </label>
                      <input 
                        type="time" 
                        name="schedule_time" 
                        class="input input-bordered" 
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </DaisyCard>
          
          <div class="flex justify-end">
            <div class="flex gap-2">
              <a href="/admin/notifications" class="btn btn-ghost">Cancelar</a>
              <button type="submit" class="btn btn-primary">Enviar Notificações</button>
            </div>
          </div>
        </form>
      </Section>
    </PageTransition>
  </Container>
</BaseLayout>

<script>
  // Script para funcionalidade da página de envio de notificações
  document.addEventListener('DOMContentLoaded', () => {
    // Alternar entre seleção de grupo e usuários específicos
    const recipientMethodRadios = document.querySelectorAll('input[name="recipient_method"]');
    const groupSelection = document.getElementById('group-selection');
    const specificUsers = document.getElementById('specific-users');
    
    recipientMethodRadios.forEach(radio => {
      radio.addEventListener('change', () => {
        const method = (radio as HTMLInputElement).value;
        
        if (method === 'group') {
          groupSelection?.classList.remove('hidden');
          specificUsers?.classList.add('hidden');
        } else if (method === 'specific') {
          groupSelection?.classList.add('hidden');
          specificUsers?.classList.remove('hidden');
        }
      });
    });
    
    // Mostrar/ocultar opções de agendamento
    const scheduleCheckbox = document.getElementById('schedule-checkbox') as HTMLInputElement;
    const scheduleOptions = document.getElementById('schedule-options');
    
    if (scheduleCheckbox && scheduleOptions) {
      scheduleCheckbox.addEventListener('change', () => {
        if (scheduleCheckbox.checked) {
          scheduleOptions.classList.remove('hidden');
        } else {
          scheduleOptions.classList.add('hidden');
        }
      });
    }
    
    // Mostrar/ocultar opções de e-mail
    const emailCheckbox = document.querySelector('input[name="channels[]"][value="email"]') as HTMLInputElement;
    const emailOptions = document.getElementById('email-options');
    
    if (emailCheckbox && emailOptions) {
      emailCheckbox.addEventListener('change', () => {
        if (emailCheckbox.checked) {
          emailOptions.classList.remove('hidden');
        } else {
          emailOptions.classList.add('hidden');
        }
      });
      
      // Verificar estado inicial
      if (!emailCheckbox.checked) {
        emailOptions.classList.add('hidden');
      }
    }
  });
</script>
