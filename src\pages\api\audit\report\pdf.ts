/**
 * API para geração de relatórios de auditoria em PDF
 */

import { auditRepository } from '@repository/auditRepository';
import { AuditEventType, AuditSeverity } from '@services/auditService';
import { getCurrentUser } from '@utils/authUtils';
import { logger } from '@utils/logger';
import type { APIRoute } from 'astro';
import PDFDocument from 'pdfkit';

export const GET: APIRoute = async ({ url, cookies }) => {
  try {
    // Verificar autenticação
    const user = await getCurrentUser(cookies);

    if (!user) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Não autorizado',
        }),
        {
          status: 401,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    // Obter parâmetros da consulta
    const reportType = url.searchParams.get('reportType');
    const startDateStr = url.searchParams.get('startDate');
    const endDateStr = url.searchParams.get('endDate');
    const userId = url.searchParams.get('userId') || undefined;
    const resource = url.searchParams.get('resource') || undefined;
    const eventTypesParam = url.searchParams.getAll('eventTypes');
    const severitiesParam = url.searchParams.getAll('severities');

    // Validar parâmetros obrigatórios
    if (!startDateStr || !endDateStr || !reportType) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Parâmetros insuficientes',
        }),
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    // Converter strings de data para objetos Date
    const startDate = new Date(startDateStr);
    const endDate = new Date(endDateStr);

    // Determinar tipos de eventos com base no tipo de relatório
    let eventTypes: string[] = [];

    if (eventTypesParam.length > 0) {
      // Usar tipos de eventos especificados
      eventTypes = eventTypesParam;
    } else if (reportType) {
      // Usar tipos de eventos predefinidos com base no tipo de relatório
      switch (reportType) {
        case 'activity':
          eventTypes = [
            AuditEventType.LOGIN_SUCCESS,
            AuditEventType.LOGIN_FAILURE,
            AuditEventType.LOGOUT,
            AuditEventType.RESOURCE_ACCESSED,
          ];
          break;

        case 'security':
          eventTypes = [
            AuditEventType.LOGIN_FAILURE,
            AuditEventType.PERMISSION_DENIED,
            AuditEventType.SYSTEM_ERROR,
            AuditEventType.SYSTEM_WARNING,
          ];
          break;

        case 'access':
          eventTypes = [
            AuditEventType.RESOURCE_ACCESSED,
            AuditEventType.PERMISSION_GRANTED,
            AuditEventType.PERMISSION_DENIED,
          ];
          break;

        case 'admin':
          eventTypes = [
            AuditEventType.USER_CREATED,
            AuditEventType.USER_UPDATED,
            AuditEventType.USER_DELETED,
            AuditEventType.ROLE_CREATED,
            AuditEventType.ROLE_UPDATED,
            AuditEventType.ROLE_DELETED,
            AuditEventType.PERMISSION_CREATED,
            AuditEventType.PERMISSION_UPDATED,
            AuditEventType.PERMISSION_DELETED,
          ];
          break;
      }
    }

    // Determinar severidades
    const severities: string[] =
      severitiesParam.length > 0
        ? severitiesParam
        : [AuditSeverity.INFO, AuditSeverity.WARNING, AuditSeverity.ERROR, AuditSeverity.CRITICAL];

    // Definir título e descrição do relatório
    let reportTitle = '';
    let reportDescription = '';

    switch (reportType) {
      case 'activity':
        reportTitle = 'Relatório de Atividade de Usuários';
        reportDescription = 'Eventos de atividade dos usuários no sistema';
        break;

      case 'security':
        reportTitle = 'Relatório de Eventos de Segurança';
        reportDescription = 'Eventos relacionados à segurança do sistema';
        break;

      case 'access':
        reportTitle = 'Relatório de Acessos a Recursos';
        reportDescription = 'Eventos de acesso a recursos do sistema';
        break;

      case 'admin':
        reportTitle = 'Relatório de Ações Administrativas';
        reportDescription = 'Eventos de ações administrativas no sistema';
        break;

      case 'custom':
        reportTitle = 'Relatório Personalizado';
        reportDescription = 'Relatório personalizado com filtros específicos';
        break;
    }

    // Adicionar período ao título
    reportDescription += ` (${startDate.toLocaleDateString()} a ${endDate.toLocaleDateString()})`;

    // Buscar eventos para o relatório
    const limit = 1000; // Limite de eventos para o relatório
    const result = await auditRepository.read(
      undefined,
      eventTypes.length > 0 ? eventTypes : undefined,
      userId,
      resource,
      undefined,
      severities.length > 0 ? severities : undefined,
      startDate,
      endDate,
      limit
    );

    // Processar resultados
    const reportData = result.rows.map((row) => ({
      ...row,
      metadata: row.metadata ? JSON.parse(row.metadata) : null,
      created_at: new Date(row.created_at).toLocaleString(),
    }));

    // Contar total de eventos
    const totalEvents = await auditRepository.count(
      eventTypes.length > 0 ? eventTypes : undefined,
      userId,
      resource,
      undefined,
      severities.length > 0 ? severities : undefined,
      startDate,
      endDate
    );

    // Obter configurações de auditoria para descrições de eventos
    const configResult = await auditRepository.getConfig();
    const auditConfig = configResult.rows;

    // Mapear descrições para tipos de eventos
    const eventTypeDescriptions: Record<string, string> = {};
    auditConfig.forEach((config) => {
      eventTypeDescriptions[config.event_type] = config.description;
    });

    // Criar documento PDF
    const doc = new PDFDocument({ margin: 50 });

    // Configurar resposta
    const chunks: Buffer[] = [];
    doc.on('data', (chunk) => chunks.push(chunk));

    // Função para finalizar o documento e retornar a resposta
    const getResponse = () => {
      return new Promise<Response>((resolve) => {
        doc.on('end', () => {
          const result = Buffer.concat(chunks);
          resolve(
            new Response(result, {
              status: 200,
              headers: {
                'Content-Type': 'application/pdf',
                'Content-Disposition': `attachment; filename="audit-report-${new Date().toISOString().slice(0, 10)}.pdf"`,
              },
            })
          );
        });
        doc.end();
      });
    };

    // Adicionar cabeçalho
    doc.fontSize(20).text(reportTitle, { align: 'center' });
    doc.moveDown();
    doc.fontSize(12).text(reportDescription, { align: 'center' });
    doc.moveDown();

    // Adicionar informações do relatório
    doc.fontSize(10).text(`Gerado por: ${user.name}`);
    doc.fontSize(10).text(`Data de geração: ${new Date().toLocaleString()}`);
    doc.fontSize(10).text(`Total de eventos: ${totalEvents}`);
    if (totalEvents > reportData.length) {
      doc.fontSize(10).text(`Exibindo apenas os primeiros ${reportData.length} eventos.`);
    }
    doc.moveDown();

    // Adicionar tabela de eventos
    if (reportData.length > 0) {
      // Definir colunas da tabela
      const tableTop = doc.y;
      const tableColumnWidth = (doc.page.width - 100) / 5;

      // Cabeçalho da tabela
      doc.font('Helvetica-Bold');
      doc.fontSize(8);
      doc.text('Data/Hora', 50, tableTop, { width: tableColumnWidth, align: 'left' });
      doc.text('Evento', 50 + tableColumnWidth, tableTop, {
        width: tableColumnWidth,
        align: 'left',
      });
      doc.text('Usuário', 50 + tableColumnWidth * 2, tableTop, {
        width: tableColumnWidth,
        align: 'left',
      });
      doc.text('Recurso/Ação', 50 + tableColumnWidth * 3, tableTop, {
        width: tableColumnWidth,
        align: 'left',
      });
      doc.text('Severidade/Resultado', 50 + tableColumnWidth * 4, tableTop, {
        width: tableColumnWidth,
        align: 'left',
      });

      // Linha horizontal após cabeçalho
      doc
        .moveTo(50, tableTop + 15)
        .lineTo(doc.page.width - 50, tableTop + 15)
        .stroke();

      // Conteúdo da tabela
      doc.font('Helvetica');
      let tableRowY = tableTop + 20;

      for (const event of reportData) {
        // Verificar se precisa de nova página
        if (tableRowY > doc.page.height - 50) {
          doc.addPage();
          tableRowY = 50;
        }

        // Dados do evento
        doc.fontSize(8);
        doc.text(event.created_at, 50, tableRowY, { width: tableColumnWidth, align: 'left' });
        doc.text(
          eventTypeDescriptions[event.event_type] || event.event_type,
          50 + tableColumnWidth,
          tableRowY,
          { width: tableColumnWidth, align: 'left' }
        );
        doc.text(event.user_name || '-', 50 + tableColumnWidth * 2, tableRowY, {
          width: tableColumnWidth,
          align: 'left',
        });

        const resourceAction = `${event.resource || '-'}\n${event.action || '-'}`;
        doc.text(resourceAction, 50 + tableColumnWidth * 3, tableRowY, {
          width: tableColumnWidth,
          align: 'left',
        });

        const severityResult = `${event.severity}\n${event.result || '-'}`;
        doc.text(severityResult, 50 + tableColumnWidth * 4, tableRowY, {
          width: tableColumnWidth,
          align: 'left',
        });

        // Linha horizontal após cada linha
        tableRowY += 25;
        doc
          .moveTo(50, tableRowY - 5)
          .lineTo(doc.page.width - 50, tableRowY - 5)
          .stroke();
      }
    } else {
      doc
        .fontSize(12)
        .text('Nenhum evento encontrado para os critérios selecionados.', { align: 'center' });
    }

    // Adicionar rodapé
    doc.fontSize(8);
    const pageCount = doc.bufferedPageRange().count;
    for (let i = 0; i < pageCount; i++) {
      doc.switchToPage(i);
      doc.text(`Página ${i + 1} de ${pageCount}`, 50, doc.page.height - 50, { align: 'center' });
    }

    // Retornar resposta
    return await getResponse();
  } catch (error) {
    logger.error('Erro ao gerar relatório PDF:', error);

    return new Response(
      JSON.stringify({
        success: false,
        error: 'Erro ao gerar relatório PDF',
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  }
};
