// src/helpers/csrfHelper.ts
import crypto from 'node:crypto';
import type { AstroGlobal } from 'astro';

/**
 * Helper para gerenciamento de tokens CSRF
 */
export const csrfHelper = {
  /**
   * Gera um token CSRF e o armazena na sessão
   * @param Astro - Contexto Astro global
   * @returns Token CSRF gerado
   */
  generateToken: async (Astro: AstroGlobal): Promise<string> => {
    // Gerar token aleatório
    const token = crypto.randomBytes(32).toString('hex');

    // Armazenar na sessão
    await Astro.session.set('csrfToken', token);

    return token;
  },

  /**
   * Valida um token CSRF contra o armazenado na sessão
   * @param Astro - Contexto Astro global
   * @param token - Token CSRF a ser validado
   * @returns Verdadeiro se o token for válido
   */
  validateToken: async (context: any, token: string): Promise<boolean> => {
    // Obter sessão do contexto (compatível com Astro e ActionContext)
    const session = context.session || context;

    if (!session) {
      return false;
    }

    const storedToken = await session.get('csrfToken');

    if (!storedToken || !token) {
      return false;
    }

    // Comparação segura contra timing attacks
    return crypto.timingSafeEqual(Buffer.from(token), Buffer.from(storedToken));
  },

  /**
   * Rotaciona o token CSRF, gerando um novo e invalidando o anterior
   * @param Astro - Contexto Astro global
   * @returns Novo token CSRF
   */
  rotateToken: async (context: any): Promise<string> => {
    // Obter sessão do contexto (compatível com Astro e ActionContext)
    const session = context.session || context;

    if (!session) {
      throw new Error('Sessão não disponível');
    }

    // Remover token antigo
    await session.delete('csrfToken');

    // Gerar novo token (se for um contexto Astro)
    if ('locals' in context) {
      return await csrfHelper.generateToken(context);
    }

    // Caso seja um contexto de ação, gerar token diretamente
    const token = crypto.randomBytes(32).toString('hex');
    await session.set('csrfToken', token);
    return token;
  },
};
