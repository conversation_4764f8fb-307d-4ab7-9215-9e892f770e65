// Simple test to check if actions can be imported
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('Testing actions import...');

try {
  // Test if we can import the actions index
  console.log('Attempting to import actions...');

  const actionsPath = path.join(__dirname, 'src', 'actions', 'index.ts');

  if (fs.existsSync(actionsPath)) {
    console.log('✓ Actions index file exists');

    const content = fs.readFileSync(actionsPath, 'utf8');
    console.log('✓ Actions index file can be read');

    // Check if it has the expected exports
    if (content.includes('export const server')) {
      console.log('✓ Actions index has server export');
    } else {
      console.log('✗ Actions index missing server export');
    }

  } else {
    console.log('✗ Actions index file does not exist');
  }

  // Check individual action files
  const actionFiles = [
    'authAction.ts',
    'categoryAction.ts',
    'informationSchemaColumnAction.ts',
    'invoiceAction.ts',
    'invoiceItemAction.ts',
    'orderAction.ts',
    'orderItemAction.ts',
    'paymentAction.ts',
    'paymentTypeAction.ts',
    'postAction.ts',
    'productAction.ts',
    'schoolTypeAction.ts',
    'userAction.ts',
    'userTypeAction.ts'
  ];

  let allFilesExist = true;

  for (const file of actionFiles) {
    const filePath = path.join(__dirname, 'src', 'actions', file);
    if (fs.existsSync(filePath)) {
      console.log(`✓ ${file} exists`);
    } else {
      console.log(`✗ ${file} missing`);
      allFilesExist = false;
    }
  }

  if (allFilesExist) {
    console.log('✓ All action files exist');
  } else {
    console.log('✗ Some action files are missing');
  }

  console.log('Actions test completed');

} catch (error) {
  console.error('Error testing actions:', error.message);
}
