/**
 * Twilio SMS Service
 *
 * Implementação do serviço de SMS usando Twilio.
 * Parte da implementação da tarefa 8.5.2 - Notificações externas
 */

import { Twilio } from 'twilio';
import { SmsOptions, SmsResult, SmsService } from '../../domain/services/SmsService';

export class TwilioSmsService implements SmsService {
  private client: Twilio;
  private defaultFrom: string;
  private scheduledSms: Map<string, NodeJS.Timeout> = new Map();

  constructor(config: {
    accountSid: string;
    authToken: string;
    defaultFrom: string;
  }) {
    this.client = new Twilio(config.accountSid, config.authToken);
    this.defaultFrom = config.defaultFrom;
  }

  async sendSms(options: SmsOptions): Promise<SmsResult> {
    try {
      // Verificar se é um SMS agendado
      if (options.scheduledAt && options.scheduledAt > new Date()) {
        return this.scheduleSms(options, options.scheduledAt);
      }

      // Normalizar destinatários
      const recipients = Array.isArray(options.to) ? options.to : [options.to];

      // Enviar SMS para cada destinatário
      const results = await Promise.all(
        recipients.map(async (to) => {
          try {
            const message = await this.client.messages.create({
              body: options.message,
              from: options.from || this.defaultFrom,
              to,
              statusCallback: options.callbackUrl,
            });

            return {
              success: true,
              messageId: message.sid,
              segments: message.numSegments ? Number.parseInt(message.numSegments) : 1,
              cost: message.price ? Number.parseFloat(message.price) : undefined,
            };
          } catch (error) {
            console.error(`Erro ao enviar SMS para ${to}:`, error);

            return {
              success: false,
              error: error instanceof Error ? error.message : 'Erro desconhecido ao enviar SMS',
            };
          }
        })
      );

      // Calcular resultado agregado
      const successCount = results.filter((r) => r.success).length;
      const allSuccess = successCount === recipients.length;

      // Gerar ID de mensagem para múltiplos destinatários
      const messageId =
        results.length === 1 && results[0].messageId
          ? results[0].messageId
          : `batch_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;

      // Calcular custo total
      const totalCost = results.reduce((sum, r) => sum + (r.cost || 0), 0);

      return {
        success: allSuccess,
        messageId,
        error: allSuccess
          ? undefined
          : `Falha ao enviar SMS para ${recipients.length - successCount} de ${recipients.length} destinatários`,
        timestamp: new Date(),
        segments: results.reduce((sum, r) => sum + (r.segments || 0), 0),
        cost: totalCost > 0 ? totalCost : undefined,
      };
    } catch (error) {
      console.error('Erro ao enviar SMS:', error);

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Erro desconhecido ao enviar SMS',
        timestamp: new Date(),
      };
    }
  }

  async sendNotificationSms(
    to: string | string[],
    message: string,
    options?: Partial<SmsOptions>
  ): Promise<SmsResult> {
    const smsOptions: SmsOptions = {
      to,
      message,
      ...options,
    };

    return this.sendSms(smsOptions);
  }

  async scheduleSms(options: SmsOptions, scheduledAt: Date): Promise<SmsResult> {
    try {
      const now = new Date();

      if (scheduledAt <= now) {
        // Se a data agendada já passou, enviar imediatamente
        return this.sendSms({
          ...options,
          scheduledAt: undefined,
        });
      }

      // Gerar ID para o SMS agendado
      const messageId = `scheduled_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;

      // Calcular o atraso em milissegundos
      const delay = scheduledAt.getTime() - now.getTime();

      // Agendar o envio
      const timeout = setTimeout(async () => {
        await this.sendSms({
          ...options,
          scheduledAt: undefined,
        });

        // Remover da lista de SMS agendados
        this.scheduledSms.delete(messageId);
      }, delay);

      // Armazenar o timeout para possível cancelamento
      this.scheduledSms.set(messageId, timeout);

      return {
        success: true,
        messageId,
        timestamp: now,
      };
    } catch (error) {
      console.error('Erro ao agendar SMS:', error);

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Erro desconhecido ao agendar SMS',
        timestamp: new Date(),
      };
    }
  }

  async cancelScheduledSms(messageId: string): Promise<boolean> {
    const timeout = this.scheduledSms.get(messageId);

    if (timeout) {
      clearTimeout(timeout);
      this.scheduledSms.delete(messageId);
      return true;
    }

    return false;
  }

  async getSmsStatus(messageId: string): Promise<{
    status: 'sent' | 'delivered' | 'failed' | 'scheduled' | 'cancelled' | 'unknown';
    timestamp?: Date;
    details?: Record<string, any>;
  }> {
    try {
      // Verificar se é um SMS agendado
      if (messageId.startsWith('scheduled_') && this.scheduledSms.has(messageId)) {
        return {
          status: 'scheduled',
          timestamp: new Date(),
        };
      }

      // Verificar se é um ID de lote
      if (messageId.startsWith('batch_')) {
        return {
          status: 'unknown',
          details: {
            message: 'Não é possível verificar o status de um lote de SMS',
          },
        };
      }

      // Consultar status na API do Twilio
      const message = await this.client.messages(messageId).fetch();

      // Mapear status do Twilio para nosso formato
      let status: 'sent' | 'delivered' | 'failed' | 'scheduled' | 'cancelled' | 'unknown' =
        'unknown';

      switch (message.status) {
        case 'queued':
        case 'sending':
        case 'sent':
          status = 'sent';
          break;
        case 'delivered':
          status = 'delivered';
          break;
        case 'undelivered':
        case 'failed':
          status = 'failed';
          break;
        case 'canceled':
          status = 'cancelled';
          break;
      }

      return {
        status,
        timestamp: message.dateUpdated ? new Date(message.dateUpdated) : undefined,
        details: {
          twilioStatus: message.status,
          errorCode: message.errorCode,
          errorMessage: message.errorMessage,
        },
      };
    } catch (error) {
      console.error('Erro ao verificar status do SMS:', error);

      return {
        status: 'unknown',
        details: {
          error: error instanceof Error ? error.message : 'Erro desconhecido ao verificar status',
        },
      };
    }
  }

  async getSmsStats(
    startDate?: Date,
    endDate?: Date
  ): Promise<{
    sent: number;
    delivered: number;
    failed: number;
    deliveryRate: number;
    totalCost: number;
  }> {
    // Em um cenário real, aqui seria feita uma consulta à API do Twilio
    // Por enquanto, retornamos estatísticas fictícias
    return {
      sent: 100,
      delivered: 95,
      failed: 5,
      deliveryRate: 95,
      totalCost: 10.5,
    };
  }

  async validatePhoneNumber(phoneNumber: string): Promise<{
    isValid: boolean;
    formattedNumber?: string;
    countryCode?: string;
    carrier?: string;
  }> {
    try {
      // Em um cenário real, aqui seria feita uma consulta à API do Twilio Lookup
      // Por enquanto, fazemos uma validação básica

      // Remover caracteres não numéricos
      const cleanNumber = phoneNumber.replace(/\D/g, '');

      // Verificar se o número tem pelo menos 10 dígitos (código de área + número)
      if (cleanNumber.length < 10) {
        return {
          isValid: false,
        };
      }

      // Formatar o número (exemplo simples)
      let formattedNumber = cleanNumber;

      // Adicionar código do país se não estiver presente
      if (!cleanNumber.startsWith('55')) {
        formattedNumber = `55${cleanNumber}`;
      }

      return {
        isValid: true,
        formattedNumber: `+${formattedNumber}`,
        countryCode: 'BR',
        carrier: 'Desconhecido', // Em um cenário real, seria obtido da API
      };
    } catch (error) {
      console.error('Erro ao validar número de telefone:', error);

      return {
        isValid: false,
      };
    }
  }
}
