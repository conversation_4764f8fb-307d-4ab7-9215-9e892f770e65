/**
 * API para obter detalhes de um evento de auditoria
 */

import { auditRepository } from '@repository/auditRepository';
import { getCurrentUser } from '@utils/authUtils';
import { logger } from '@utils/logger';
import type { APIRoute } from 'astro';

export const GET: APIRoute = async ({ params, cookies, request }) => {
  try {
    // Verificar autenticação
    const user = await getCurrentUser(cookies);

    if (!user) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Não autorizado',
        }),
        {
          status: 401,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    // Obter ID do evento
    const { id } = params;

    if (!id) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'ID do evento não fornecido',
        }),
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    // Buscar evento
    const result = await auditRepository.read(id);

    if (result.rowCount === 0) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Evento não encontrado',
        }),
        {
          status: 404,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    // Processar evento
    const event = result.rows[0];

    // Converter metadados de string JSON para objeto
    if (event.metadata) {
      try {
        event.metadata = JSON.parse(event.metadata);
      } catch (error) {
        logger.warn('Erro ao fazer parse dos metadados do evento:', error);
        event.metadata = {};
      }
    }

    return new Response(
      JSON.stringify({
        success: true,
        event,
      }),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  } catch (error) {
    logger.error('Erro ao obter detalhes do evento de auditoria:', error);

    return new Response(
      JSON.stringify({
        success: false,
        error: 'Erro ao processar requisição',
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  }
};
