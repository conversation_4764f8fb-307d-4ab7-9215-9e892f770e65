/**
 * API de Login Administrativo
 *
 * Endpoint para autenticação de usuários administradores.
 * Parte da implementação da tarefa 8.8.2 - Gestão de usuários
 */

import type { APIRoute } from 'astro';
import { AdminUserRepository } from '../../../../domain/repositories/AdminUserRepository';
import { PasswordService } from '../../../../domain/services/PasswordService';
import { TokenService } from '../../../../domain/services/TokenService';
import { AdminLoginUseCase } from '../../../../domain/usecases/admin/AdminLoginUseCase';
import { PostgresAdminUserRepository } from '../../../../infrastructure/database/repositories/PostgresAdminUserRepository';
import { BcryptPasswordService } from '../../../../infrastructure/services/BcryptPasswordService';
import { JwtTokenService } from '../../../../infrastructure/services/JwtTokenService';

// Inicializar repositório
const adminUserRepository: AdminUserRepository = new PostgresAdminUserRepository();

// Inicializar serviços
const passwordService: PasswordService = new BcryptPasswordService();
const tokenService: TokenService = new JwtTokenService(
  process.env.JWT_SECRET || 'default-secret-key',
  process.env.PASSWORD_RESET_SECRET || 'password-reset-secret-key',
  process.env.EMAIL_VERIFICATION_SECRET || 'email-verification-secret-key'
);

// Inicializar caso de uso
const adminLoginUseCase = new AdminLoginUseCase(adminUserRepository, passwordService, tokenService);

export const POST: APIRoute = async ({ request }) => {
  try {
    // Obter dados do corpo da requisição
    const body = await request.json();

    // Validar dados
    if (!body.username || !body.password) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Nome de usuário e senha são obrigatórios.',
        }),
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    // Executar caso de uso
    const result = await adminLoginUseCase.execute({
      username: body.username,
      password: body.password,
    });

    if (result.success && result.data) {
      // Remover senha hash da resposta
      const { user, token } = result.data;
      const { passwordHash, ...userWithoutPassword } = user;

      return new Response(
        JSON.stringify({
          success: true,
          data: {
            user: userWithoutPassword,
            token,
          },
        }),
        {
          status: 200,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }
    return new Response(
      JSON.stringify({
        success: false,
        error: result.error || 'Erro desconhecido ao autenticar usuário.',
      }),
      {
        status: 401,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  } catch (error) {
    console.error('Erro ao processar login administrativo:', error);

    return new Response(
      JSON.stringify({
        success: false,
        error: 'Ocorreu um erro ao processar o login. Por favor, tente novamente.',
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  }
};
