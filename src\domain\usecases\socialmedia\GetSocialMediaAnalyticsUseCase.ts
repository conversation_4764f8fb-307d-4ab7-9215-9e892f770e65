/**
 * Get Social Media Analytics Use Case
 *
 * Caso de uso para obter análises das redes sociais.
 * Parte da implementação da tarefa 8.5.3 - Integração com redes sociais
 */

import {
  SocialMediaAnalytics,
  SocialMediaPlatform,
  SocialMediaService,
} from '../../services/SocialMediaService';

export interface GetSocialMediaAnalyticsRequest {
  platforms: SocialMediaPlatform[];
  startDate?: Date;
  endDate?: Date;
}

export interface PlatformAnalytics {
  platform: SocialMediaPlatform;
  analytics: SocialMediaAnalytics;
  topPosts: Array<{
    postId: string;
    content: string;
    url: string;
    analytics: SocialMediaAnalytics;
  }>;
  error?: string;
}

export interface GetSocialMediaAnalyticsResponse {
  success: boolean;
  results: PlatformAnalytics[];
  aggregated: SocialMediaAnalytics;
  error?: string;
}

export class GetSocialMediaAnalyticsUseCase {
  private socialMediaServices: Record<SocialMediaPlatform, SocialMediaService>;

  constructor(socialMediaServices: Record<SocialMediaPlatform, SocialMediaService>) {
    this.socialMediaServices = socialMediaServices;
  }

  async execute(request: GetSocialMediaAnalyticsRequest): Promise<GetSocialMediaAnalyticsResponse> {
    try {
      // Validar os dados de entrada
      if (!this.validateRequest(request)) {
        return {
          success: false,
          results: [],
          aggregated: this.getEmptyAnalytics(),
          error: 'Dados inválidos para obtenção de análises das redes sociais.',
        };
      }

      // Obter análises de cada plataforma solicitada
      const results: PlatformAnalytics[] = [];
      let overallSuccess = true;

      for (const platform of request.platforms) {
        const service = this.socialMediaServices[platform];

        if (!service) {
          results.push({
            platform,
            analytics: this.getEmptyAnalytics(),
            topPosts: [],
            error: `Serviço para a plataforma ${platform} não disponível.`,
          });
          overallSuccess = false;
          continue;
        }

        try {
          // Verificar se a plataforma está conectada
          const isConnected = await service.isPlatformConnected(platform);

          if (!isConnected) {
            results.push({
              platform,
              analytics: this.getEmptyAnalytics(),
              topPosts: [],
              error: `Plataforma ${platform} não está conectada.`,
            });
            overallSuccess = false;
            continue;
          }

          // Obter análises da conta
          const accountAnalytics = await service.getAccountAnalytics(
            platform,
            request.startDate,
            request.endDate
          );

          results.push({
            platform,
            analytics: accountAnalytics,
            topPosts: accountAnalytics.topPosts,
          });
        } catch (error) {
          results.push({
            platform,
            analytics: this.getEmptyAnalytics(),
            topPosts: [],
            error:
              error instanceof Error
                ? error.message
                : `Erro desconhecido ao obter análises do ${platform}.`,
          });
          overallSuccess = false;
        }
      }

      // Calcular análises agregadas
      const aggregated = this.aggregateAnalytics(results.map((r) => r.analytics));

      return {
        success: overallSuccess,
        results,
        aggregated,
      };
    } catch (error) {
      console.error('Erro ao obter análises das redes sociais:', error);

      return {
        success: false,
        results: [],
        aggregated: this.getEmptyAnalytics(),
        error:
          error instanceof Error
            ? error.message
            : 'Erro desconhecido ao obter análises das redes sociais.',
      };
    }
  }

  private validateRequest(request: GetSocialMediaAnalyticsRequest): boolean {
    // Validar plataformas
    if (!request.platforms || request.platforms.length === 0) {
      return false;
    }

    // Validar plataformas suportadas
    const supportedPlatforms: SocialMediaPlatform[] = [
      'facebook',
      'instagram',
      'twitter',
      'linkedin',
      'youtube',
      'tiktok',
    ];

    for (const platform of request.platforms) {
      if (!supportedPlatforms.includes(platform)) {
        return false;
      }
    }

    // Validar datas
    if (request.startDate && request.endDate && request.startDate > request.endDate) {
      return false;
    }

    return true;
  }

  private getEmptyAnalytics(): SocialMediaAnalytics {
    return {
      impressions: 0,
      engagements: 0,
      clicks: 0,
      shares: 0,
      likes: 0,
      comments: 0,
      reach: 0,
    };
  }

  private aggregateAnalytics(analyticsList: SocialMediaAnalytics[]): SocialMediaAnalytics {
    const aggregated = this.getEmptyAnalytics();

    for (const analytics of analyticsList) {
      aggregated.impressions += analytics.impressions;
      aggregated.engagements += analytics.engagements;
      aggregated.clicks += analytics.clicks;
      aggregated.shares += analytics.shares;
      aggregated.likes += analytics.likes;
      aggregated.comments += analytics.comments;
      aggregated.reach += analytics.reach;
    }

    return aggregated;
  }
}
