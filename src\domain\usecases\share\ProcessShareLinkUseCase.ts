/**
 * Process Share Link Use Case
 *
 * Caso de uso para processar um link de compartilhamento quando acessado.
 * Parte da implementação da tarefa 8.4.2 - Compartilhamento
 */

import { ShareLink } from '../../entities/ShareLink';
import { AffiliateRepository } from '../../repositories/AffiliateRepository';
import { ShareLinkRepository } from '../../repositories/ShareLinkRepository';

export interface ProcessShareLinkRequest {
  code: string;
  ipAddress?: string;
  userAgent?: string;
  referrer?: string;
  userId?: string;
}

export interface ProcessShareLinkResponse {
  success: boolean;
  shareLink?: ShareLink;
  redirectUrl?: string;
  isAffiliate?: boolean;
  error?: string;
}

export class ProcessShareLinkUseCase {
  constructor(
    private shareLinkRepository: ShareLinkRepository,
    private affiliateRepository: AffiliateRepository,
    private baseUrl: string
  ) {}

  async execute(request: ProcessShareLinkRequest): Promise<ProcessShareLinkResponse> {
    try {
      // Buscar o link pelo código
      const shareLink = await this.shareLinkRepository.getByCode(request.code);

      // Verificar se o link existe
      if (!shareLink) {
        return {
          success: false,
          error: 'Link de compartilhamento não encontrado.',
        };
      }

      // Verificar se o link está válido
      if (!shareLink.isValid()) {
        return {
          success: false,
          shareLink,
          error: 'Este link de compartilhamento não está mais disponível.',
        };
      }

      // Registrar o clique
      const clickRegistered = await this.shareLinkRepository.registerClick(shareLink.id);

      if (!clickRegistered) {
        console.warn(`Falha ao registrar clique para o link ${shareLink.id}`);
      }

      // Verificar se é um link de afiliado
      let isAffiliate = false;

      if (shareLink.utmSource === 'affiliate') {
        // Buscar afiliado pelo código (assumindo que utmContent contém o código do afiliado)
        const affiliateCode = shareLink.utmContent;

        if (affiliateCode) {
          const affiliate = await this.affiliateRepository.getByCode(affiliateCode);

          if (affiliate && affiliate.status === 'active') {
            isAffiliate = true;

            // Registrar clique para o afiliado
            await this.affiliateRepository.registerClick(affiliate.id);
          }
        }
      }

      // Construir URL de redirecionamento
      let redirectUrl = '';

      switch (shareLink.target.type) {
        case 'product':
          redirectUrl = `${this.baseUrl}/products/${shareLink.target.slug || shareLink.target.id}`;
          break;
        case 'content':
          redirectUrl = `${this.baseUrl}/content/${shareLink.target.slug || shareLink.target.id}`;
          break;
        case 'document':
          redirectUrl = `${this.baseUrl}/documents/view/${shareLink.target.id}`;
          break;
        case 'collection':
          redirectUrl = `${this.baseUrl}/collections/${shareLink.target.slug || shareLink.target.id}`;
          break;
        case 'custom':
          redirectUrl = `${this.baseUrl}/${shareLink.target.slug || ''}`;
          break;
        default:
          redirectUrl = this.baseUrl;
      }

      // Adicionar parâmetros UTM à URL de redirecionamento
      const utmParams = [];

      if (shareLink.utmSource)
        utmParams.push(`utm_source=${encodeURIComponent(shareLink.utmSource)}`);
      if (shareLink.utmMedium)
        utmParams.push(`utm_medium=${encodeURIComponent(shareLink.utmMedium)}`);
      if (shareLink.utmCampaign)
        utmParams.push(`utm_campaign=${encodeURIComponent(shareLink.utmCampaign)}`);
      if (shareLink.utmTerm) utmParams.push(`utm_term=${encodeURIComponent(shareLink.utmTerm)}`);
      if (shareLink.utmContent)
        utmParams.push(`utm_content=${encodeURIComponent(shareLink.utmContent)}`);

      // Adicionar parâmetro de rastreamento de compartilhamento
      utmParams.push(`share=${shareLink.code}`);

      // Adicionar parâmetros à URL
      if (utmParams.length > 0) {
        redirectUrl += `?${utmParams.join('&')}`;
      }

      return {
        success: true,
        shareLink,
        redirectUrl,
        isAffiliate,
      };
    } catch (error) {
      console.error('Erro ao processar link de compartilhamento:', error);
      return {
        success: false,
        error: 'Ocorreu um erro ao processar o link de compartilhamento.',
      };
    }
  }
}
