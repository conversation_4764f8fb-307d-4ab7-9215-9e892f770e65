/**
 * API de Documentos Fiscais do Cliente
 *
 * Endpoint para obter documentos fiscais de um cliente.
 * Parte da implementação da tarefa 8.7.3 - Portal do cliente para documentos fiscais
 */

import type { APIRoute } from 'astro';
import {
  FiscalDocumentStatus,
  FiscalDocumentType,
} from '../../../../domain/entities/FiscalDocument';
import { FiscalDocumentRepository } from '../../../../domain/repositories/FiscalDocumentRepository';
import { GetCustomerFiscalDocumentsUseCase } from '../../../../domain/usecases/fiscal/GetCustomerFiscalDocumentsUseCase';
import { PostgresFiscalDocumentRepository } from '../../../../infrastructure/database/repositories/PostgresFiscalDocumentRepository';

// Inicializar repositório
const fiscalDocumentRepository: FiscalDocumentRepository = new PostgresFiscalDocumentRepository();

// Inicializar caso de uso
const getCustomerFiscalDocumentsUseCase = new GetCustomerFiscalDocumentsUseCase(
  fiscalDocumentRepository
);

export const GET: APIRoute = async ({ request }) => {
  try {
    const url = new URL(request.url);
    const customerId = url.searchParams.get('customerId');
    const type = url.searchParams.get('type') as FiscalDocumentType;
    const status = url.searchParams.get('status') as FiscalDocumentStatus;
    const startDate = url.searchParams.get('startDate');
    const endDate = url.searchParams.get('endDate');
    const page = Number.parseInt(url.searchParams.get('page') || '1');
    const limit = Number.parseInt(url.searchParams.get('limit') || '10');

    if (!customerId) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'ID do cliente é obrigatório.',
        }),
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    // Obter documentos fiscais do cliente
    const result = await getCustomerFiscalDocumentsUseCase.execute({
      customerId,
      type: type || undefined,
      status: status || undefined,
      startDate: startDate ? new Date(startDate) : undefined,
      endDate: endDate ? new Date(endDate) : undefined,
      pagination: {
        page,
        limit,
      },
    });

    if (result.success) {
      return new Response(
        JSON.stringify({
          success: true,
          data: result.data,
        }),
        {
          status: 200,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }
    return new Response(
      JSON.stringify({
        success: false,
        error: result.error || 'Erro desconhecido ao obter documentos fiscais do cliente.',
      }),
      {
        status: 400,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  } catch (error) {
    console.error('Erro ao processar obtenção de documentos fiscais do cliente:', error);

    return new Response(
      JSON.stringify({
        success: false,
        error:
          'Ocorreu um erro ao processar a obtenção dos documentos fiscais do cliente. Por favor, tente novamente.',
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  }
};
