// src/actions/processPaymentAction.ts
import { defineAction } from 'astro:actions';
import { z } from 'astro:schema';
import { validateCsrfToken } from '@middleware/csrfMiddleware';
import { PaymentType, paymentService } from '@services/paymentService';

export const processPaymentAction = defineAction({
  accept: 'form',
  input: z.object({
    orderId: z.string(),
    userId: z.string(),
    value: z.number(),
    paymentType: z.enum([PaymentType.PIX, PaymentType.CREDIT_CARD, PaymentType.BILLET]),
    description: z.string().optional(),
    customerName: z.string().optional(),
    customerCpf: z.string().optional(),
    customerEmail: z.string().optional(),
    customerBirthDate: z.string().optional(),
    customerPhone: z.string().optional(),
    paymentToken: z.string().optional(),
    installments: z.number().optional(),
    expirationDate: z.string().optional(),
    csrf_token: z.string(),
  }),
  async handler({ request, session, input }) {
    try {
      // Validar token CSRF
      const csrfValidation = await validateCsrfToken({ session }, await request.formData());

      if (!csrfValidation.isValid) {
        return {
          success: false,
          error: {
            message: csrfValidation.error || 'Erro de validação CSRF',
          },
        };
      }

      // Validar dados específicos por tipo de pagamento
      if (input.paymentType === PaymentType.CREDIT_CARD) {
        if (!input.paymentToken) {
          return {
            success: false,
            error: {
              message: 'Token de pagamento é obrigatório para pagamento com cartão de crédito',
            },
          };
        }

        if (
          !input.customerName ||
          !input.customerCpf ||
          !input.customerEmail ||
          !input.customerBirthDate
        ) {
          return {
            success: false,
            error: {
              message: 'Dados do cliente incompletos para pagamento com cartão de crédito',
            },
          };
        }
      } else if (input.paymentType === PaymentType.BILLET) {
        if (!input.customerName || !input.customerCpf || !input.customerEmail) {
          return {
            success: false,
            error: {
              message: 'Dados do cliente incompletos para pagamento com boleto',
            },
          };
        }

        if (!input.expirationDate) {
          return {
            success: false,
            error: {
              message: 'Data de vencimento é obrigatória para pagamento com boleto',
            },
          };
        }
      }

      // Preparar dados do cliente se fornecidos
      const customerData = input.customerName
        ? {
            name: input.customerName,
            cpf: input.customerCpf!,
            email: input.customerEmail!,
            birthDate: input.customerBirthDate,
            phone: input.customerPhone,
          }
        : undefined;

      // Processar pagamento
      const result = await paymentService.processPayment({
        orderId: input.orderId,
        userId: input.userId,
        value: input.value,
        paymentType: input.paymentType,
        description: input.description,
        customerData,
        paymentToken: input.paymentToken,
        installments: input.installments,
        expirationDate: input.expirationDate,
      });

      return {
        success: true,
        data: result,
      };
    } catch (error) {
      console.error('Erro ao processar pagamento:', error);

      return {
        success: false,
        error: {
          message: error instanceof Error ? error.message : 'Erro ao processar pagamento',
        },
      };
    }
  },
});
