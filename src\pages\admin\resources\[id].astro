---
/**
 * Página de detalhes e edição de recurso
 *
 * Esta página permite visualizar e editar detalhes de um recurso,
 * incluindo suas permissões associadas.
 */

import Notification from '@components/admin/Notification.astro';
import PermissionBulkEditor from '@components/admin/PermissionBulkEditor.astro';
import PermissionGate from '@components/auth/PermissionGate.astro';
import PolicyGate from '@components/auth/PolicyGate.astro';
// Importações
import MainLayout from '@layouts/MainLayout.astro';
import { permissionRepository } from '@repository/permissionRepository';
import { resourceRepository } from '@repository/resourceRepository';
import { getCurrentUser } from '@utils/authUtils';

// Obter parâmetros da rota
const { id } = Astro.params;

// Verificar autenticação
const user = await getCurrentUser(Astro.cookies);

// Redirecionar se não estiver autenticado
if (!user) {
  return Astro.redirect(`/signin?redirect=/admin/resources/${id}`);
}

// Verificar se o ID foi fornecido
if (!id) {
  return Astro.redirect('/admin/permissions');
}

// Buscar dados do recurso
const resourceResult = await resourceRepository.read(id);

// Verificar se o recurso existe
if (resourceResult.rowCount === 0) {
  return Astro.redirect('/admin/permissions?error=resource-not-found');
}

const resource = resourceResult.rows[0];

// Buscar permissões do recurso
const resourcePermissions = await resourceRepository.getResourcePermissions(id);

// Buscar papéis que têm acesso a este recurso
const resourceRoles = await resourceRepository.getResourceRoles(id);

// Obter parâmetros de consulta para mensagens
const success = Astro.url.searchParams.get('success');
const error = Astro.url.searchParams.get('error');

// Título da página
const title = `Recurso: ${resource.name}`;

// Processar formulário
if (Astro.request.method === 'POST') {
  try {
    const formData = await Astro.request.formData();
    const action = formData.get('action') as string;

    if (action === 'update-resource') {
      // Atualizar informações básicas do recurso
      const name = formData.get('name') as string;
      const description = formData.get('description') as string;
      const active = formData.get('active') === 'true';

      if (name) {
        await resourceRepository.update(id, name, description, active);
        return Astro.redirect(`/admin/resources/${id}?success=updated`);
      }
    } else if (action === 'create-permission') {
      // Criar nova permissão para o recurso
      const name = formData.get('name') as string;
      const description = formData.get('description') as string;
      const action = formData.get('action') as string;

      if (name && action) {
        // Criar permissão
        const permissionResult = await permissionRepository.create(name, description, action);
        const permissionId = permissionResult.rows[0].ulid_permission;

        // Associar ao recurso
        await permissionRepository.associateWithResource(id, permissionId);

        return Astro.redirect(`/admin/resources/${id}?success=permission-created`);
      }
    }
  } catch (error) {
    console.error('Erro ao processar formulário:', error);
    return Astro.redirect(`/admin/resources/${id}?error=update-failed`);
  }
}
---

<MainLayout title={title}>
  <main class="container mx-auto px-4 py-8">
    <PermissionGate resource="permissions" action="read">
      <div class="flex items-center justify-between mb-6">
        <h1 class="text-3xl font-bold">{title}</h1>
        
        <div class="flex space-x-2">
          <a 
            href="/admin/permissions" 
            class="bg-gray-200 text-gray-800 px-4 py-2 rounded hover:bg-gray-300 transition"
          >
            Voltar
          </a>
        </div>
      </div>
      
      {success && (
        <div class="mb-6">
          {success === 'updated' && (
            <Notification 
              type="success" 
              title="Recurso atualizado" 
              message="As informações do recurso foram atualizadas com sucesso."
            />
          )}
          {success === 'permission-created' && (
            <Notification 
              type="success" 
              title="Permissão criada" 
              message="A nova permissão foi criada e associada ao recurso com sucesso."
            />
          )}
          {success === 'permissions-updated' && (
            <Notification 
              type="success" 
              title="Permissões atualizadas" 
              message="As permissões foram atualizadas com sucesso para todos os papéis."
            />
          )}
        </div>
      )}
      
      {error && (
        <div class="mb-6">
          {error === 'update-failed' && (
            <Notification 
              type="error" 
              title="Erro ao atualizar" 
              message="Ocorreu um erro ao atualizar as informações. Tente novamente."
            />
          )}
        </div>
      )}
      
      <div class="grid grid-cols-1 md:grid-cols-12 gap-6">
        <!-- Informações do recurso -->
        <div class="md:col-span-4">
          <div class="bg-white rounded-lg shadow p-6">
            <h2 class="text-xl font-semibold mb-4">Informações do Recurso</h2>
            
            <PolicyGate resource="resources" action="update">
              <form method="POST" class="space-y-4">
                <input type="hidden" name="action" value="update-resource">
                
                <div>
                  <label for="name" class="block text-sm font-medium text-gray-700 mb-1">Nome</label>
                  <input 
                    type="text" 
                    id="name" 
                    name="name" 
                    value={resource.name}
                    class="w-full px-3 py-2 border border-gray-300 rounded-md"
                    required
                  >
                </div>
                
                <div>
                  <label for="description" class="block text-sm font-medium text-gray-700 mb-1">Descrição</label>
                  <textarea 
                    id="description" 
                    name="description" 
                    class="w-full px-3 py-2 border border-gray-300 rounded-md"
                    rows="3"
                  >{resource.description}</textarea>
                </div>
                
                <div>
                  <label class="flex items-center">
                    <input 
                      type="checkbox" 
                      name="active" 
                      value="true"
                      checked={resource.active}
                      class="h-4 w-4 text-blue-600 border-gray-300 rounded"
                    >
                    <span class="ml-2 text-sm text-gray-700">Ativo</span>
                  </label>
                </div>
                
                <div class="pt-2">
                  <button 
                    type="submit"
                    class="w-full bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition"
                  >
                    Salvar Alterações
                  </button>
                </div>
              </form>
              
              <slot name="fallback">
                <div class="space-y-4">
                  <div>
                    <h3 class="text-sm font-medium text-gray-700 mb-1">Nome</h3>
                    <p class="px-3 py-2 bg-gray-50 rounded-md">{resource.name}</p>
                  </div>
                  
                  <div>
                    <h3 class="text-sm font-medium text-gray-700 mb-1">Descrição</h3>
                    <p class="px-3 py-2 bg-gray-50 rounded-md">{resource.description || 'Sem descrição'}</p>
                  </div>
                  
                  <div>
                    <h3 class="text-sm font-medium text-gray-700 mb-1">Status</h3>
                    <p class="px-3 py-2 bg-gray-50 rounded-md">
                      {resource.active ? 'Ativo' : 'Inativo'}
                    </p>
                  </div>
                </div>
              </slot>
            </PolicyGate>
            
            <div class="mt-6">
              <h3 class="text-lg font-medium mb-2">Detalhes</h3>
              
              <div class="space-y-2">
                <p class="text-sm">
                  <span class="font-medium">Criado em:</span> 
                  {new Date(resource.created_at).toLocaleString()}
                </p>
                
                <p class="text-sm">
                  <span class="font-medium">Atualizado em:</span> 
                  {new Date(resource.updated_at).toLocaleString()}
                </p>
              </div>
            </div>
          </div>
          
          <!-- Criar nova permissão -->
          <PolicyGate resource="permissions" action="create">
            <div class="mt-6 bg-white rounded-lg shadow p-6">
              <h2 class="text-xl font-semibold mb-4">Criar Nova Permissão</h2>
              
              <form method="POST" class="space-y-4">
                <input type="hidden" name="action" value="create-permission">
                
                <div>
                  <label for="permission-name" class="block text-sm font-medium text-gray-700 mb-1">Nome</label>
                  <input 
                    type="text" 
                    id="permission-name" 
                    name="name" 
                    placeholder="Ex: criar_usuario"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md"
                    required
                  >
                </div>
                
                <div>
                  <label for="permission-description" class="block text-sm font-medium text-gray-700 mb-1">Descrição</label>
                  <textarea 
                    id="permission-description" 
                    name="description" 
                    placeholder="Ex: Permite criar novos usuários"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md"
                    rows="2"
                  ></textarea>
                </div>
                
                <div>
                  <label for="permission-action" class="block text-sm font-medium text-gray-700 mb-1">Ação</label>
                  <select 
                    id="permission-action" 
                    name="action"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md"
                    required
                  >
                    <option value="">Selecione uma ação</option>
                    <option value="create">create (criar)</option>
                    <option value="read">read (ler)</option>
                    <option value="update">update (atualizar)</option>
                    <option value="delete">delete (excluir)</option>
                    <option value="list">list (listar)</option>
                    <option value="export">export (exportar)</option>
                    <option value="import">import (importar)</option>
                    <option value="approve">approve (aprovar)</option>
                    <option value="reject">reject (rejeitar)</option>
                    <option value="publish">publish (publicar)</option>
                    <option value="unpublish">unpublish (despublicar)</option>
                    <option value="assign">assign (atribuir)</option>
                    <option value="revoke">revoke (revogar)</option>
                  </select>
                </div>
                
                <div class="pt-2">
                  <button 
                    type="submit"
                    class="w-full bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 transition"
                  >
                    Criar Permissão
                  </button>
                </div>
              </form>
            </div>
            
            <slot name="fallback"></slot>
          </PolicyGate>
        </div>
        
        <!-- Permissões e Papéis -->
        <div class="md:col-span-8">
          <!-- Permissões do recurso -->
          <div class="bg-white rounded-lg shadow p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4">Permissões ({resourcePermissions.rows.length})</h2>
            
            {resourcePermissions.rows.length > 0 ? (
              <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                  <thead class="bg-gray-50">
                    <tr>
                      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Nome
                      </th>
                      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Ação
                      </th>
                      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Descrição
                      </th>
                      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                    </tr>
                  </thead>
                  <tbody class="bg-white divide-y divide-gray-200">
                    {resourcePermissions.rows.map(permission => (
                      <tr>
                        <td class="px-6 py-4 whitespace-nowrap">
                          <div class="text-sm font-medium text-gray-900">{permission.name}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                          <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                            {permission.action}
                          </span>
                        </td>
                        <td class="px-6 py-4">
                          <div class="text-sm text-gray-500">{permission.description || '-'}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                          <span class={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${permission.active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                            {permission.active ? 'Ativo' : 'Inativo'}
                          </span>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <p class="text-gray-500">Este recurso não possui permissões definidas.</p>
            )}
          </div>
          
          <!-- Editor em massa de permissões -->
          <div class="bg-white rounded-lg shadow overflow-hidden">
            <div class="p-6 bg-gray-50 border-b">
              <h2 class="text-xl font-semibold">Atribuição de Permissões por Papel</h2>
              <p class="mt-1 text-gray-600">
                Use esta tabela para atribuir permissões deste recurso a diferentes papéis.
              </p>
            </div>
            
            <div class="p-6">
              <PermissionBulkEditor resourceId={id} />
            </div>
          </div>
        </div>
      </div>
      
      <slot name="fallback">
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mt-6">
          <p>Você não tem permissão para acessar esta página.</p>
          <p class="mt-2">
            <a href="/" class="text-red-700 font-medium hover:underline">Voltar para a página inicial</a>
          </p>
        </div>
      </slot>
    </PermissionGate>
  </main>
</MainLayout>

<script>
  // Script para interatividade no cliente
  document.addEventListener('DOMContentLoaded', () => {
    // Implementar funcionalidades interativas aqui
  });
</script>

<style>
  /* Estilos específicos da página */
</style>
