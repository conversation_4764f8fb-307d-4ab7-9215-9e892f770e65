/**
 * Testes de integração para API de usuários
 * 
 * Este arquivo contém testes que verificam a integração entre a API de usuários
 * e os serviços relacionados.
 * Parte da implementação da tarefa 9.1.2 - Testes de integração
 */

import { test, expect } from '@playwright/test';

// Dados de teste
const testAdmin = {
  email: '<EMAIL>',
  password: 'Admin@123456',
  name: 'Admin Integration User',
  role: 'admin'
};

const testUser = {
  email: '<EMAIL>',
  password: 'User@123456',
  name: 'User Integration Test',
  role: 'user'
};

// Configuração para testes de API
test.describe('API de Usuários', () => {
  // Variáveis para armazenar tokens
  let adminToken: string;
  let userToken: string;
  let userId: string;
  
  // Antes de todos os testes, criar usuários de teste
  test.beforeAll(async ({ request }) => {
    // Criar usuário admin
    try {
      const adminRegisterResponse = await request.post('/api/auth/register', {
        data: {
          ...testAdmin,
          role: 'user' // Inicialmente registrar como usuário normal
        }
      });
      
      if (adminRegisterResponse.ok()) {
        // Fazer login como admin
        const adminLoginResponse = await request.post('/api/auth/login', {
          data: {
            email: testAdmin.email,
            password: testAdmin.password
          }
        });
        
        if (adminLoginResponse.ok()) {
          const adminData = await adminLoginResponse.json();
          adminToken = adminData.accessToken;
          
          // Promover a admin usando uma rota especial de teste
          // Nota: Em um ambiente real, isso seria feito por um superadmin ou diretamente no banco de dados
          await request.post('/api/test/promote-to-admin', {
            headers: {
              'Authorization': `Bearer ${adminToken}`,
              'X-Test-Key': process.env.TEST_SECRET_KEY || 'test-integration-key'
            },
            data: {
              userId: adminData.user.id
            }
          });
          
          // Fazer login novamente para obter token com permissões de admin
          const newAdminLoginResponse = await request.post('/api/auth/login', {
            data: {
              email: testAdmin.email,
              password: testAdmin.password
            }
          });
          
          if (newAdminLoginResponse.ok()) {
            const newAdminData = await newAdminLoginResponse.json();
            adminToken = newAdminData.accessToken;
          }
        }
      }
    } catch (error) {
      console.error('Erro ao configurar usuário admin:', error);
    }
    
    // Criar usuário normal
    try {
      // Limpar usuário de teste se existir
      const userLoginResponse = await request.post('/api/auth/login', {
        data: {
          email: testUser.email,
          password: testUser.password
        }
      });
      
      if (userLoginResponse.ok()) {
        const userData = await userLoginResponse.json();
        userToken = userData.accessToken;
        
        // Excluir usuário
        await request.delete('/api/users/me', {
          headers: {
            'Authorization': `Bearer ${userToken}`
          }
        });
      }
      
      // Criar novo usuário de teste
      const userRegisterResponse = await request.post('/api/auth/register', {
        data: testUser
      });
      
      if (userRegisterResponse.ok()) {
        // Fazer login como usuário
        const newUserLoginResponse = await request.post('/api/auth/login', {
          data: {
            email: testUser.email,
            password: testUser.password
          }
        });
        
        if (newUserLoginResponse.ok()) {
          const userData = await newUserLoginResponse.json();
          userToken = userData.accessToken;
          userId = userData.user.id;
        }
      }
    } catch (error) {
      console.error('Erro ao configurar usuário normal:', error);
    }
  });
  
  // Após todos os testes, excluir os usuários de teste
  test.afterAll(async ({ request }) => {
    // Excluir usuário normal
    if (userToken) {
      await request.delete('/api/users/me', {
        headers: {
          'Authorization': `Bearer ${userToken}`
        }
      });
    }
    
    // Excluir usuário admin
    if (adminToken) {
      await request.delete('/api/users/me', {
        headers: {
          'Authorization': `Bearer ${adminToken}`
        }
      });
    }
  });
  
  test('deve obter perfil do usuário autenticado', async ({ request }) => {
    // Garantir que temos um token
    expect(userToken).toBeDefined();
    
    const response = await request.get('/api/users/me', {
      headers: {
        'Authorization': `Bearer ${userToken}`
      }
    });
    
    expect(response.status()).toBe(200);
    
    const userData = await response.json();
    expect(userData).toHaveProperty('id');
    expect(userData).toHaveProperty('email', testUser.email);
    expect(userData).toHaveProperty('name', testUser.name);
    expect(userData).toHaveProperty('role', 'user');
    expect(userData).not.toHaveProperty('password');
  });
  
  test('deve atualizar perfil do usuário', async ({ request }) => {
    // Garantir que temos um token
    expect(userToken).toBeDefined();
    
    const updatedName = 'Updated User Name';
    
    const response = await request.put('/api/users/me', {
      headers: {
        'Authorization': `Bearer ${userToken}`
      },
      data: {
        name: updatedName
      }
    });
    
    expect(response.status()).toBe(200);
    
    const userData = await response.json();
    expect(userData).toHaveProperty('name', updatedName);
    
    // Verificar se a atualização foi persistida
    const profileResponse = await request.get('/api/users/me', {
      headers: {
        'Authorization': `Bearer ${userToken}`
      }
    });
    
    const profileData = await profileResponse.json();
    expect(profileData).toHaveProperty('name', updatedName);
    
    // Restaurar nome original
    await request.put('/api/users/me', {
      headers: {
        'Authorization': `Bearer ${userToken}`
      },
      data: {
        name: testUser.name
      }
    });
  });
  
  test('admin deve listar todos os usuários', async ({ request }) => {
    // Garantir que temos um token de admin
    expect(adminToken).toBeDefined();
    
    const response = await request.get('/api/users', {
      headers: {
        'Authorization': `Bearer ${adminToken}`
      }
    });
    
    expect(response.status()).toBe(200);
    
    const usersData = await response.json();
    expect(Array.isArray(usersData.users)).toBeTruthy();
    expect(usersData.users.length).toBeGreaterThan(0);
    
    // Verificar se nossos usuários de teste estão na lista
    const adminUser = usersData.users.find((u: any) => u.email === testAdmin.email);
    const normalUser = usersData.users.find((u: any) => u.email === testUser.email);
    
    expect(adminUser).toBeDefined();
    expect(normalUser).toBeDefined();
    expect(adminUser.role).toBe('admin');
    expect(normalUser.role).toBe('user');
  });
  
  test('usuário normal não deve listar todos os usuários', async ({ request }) => {
    // Garantir que temos um token de usuário
    expect(userToken).toBeDefined();
    
    const response = await request.get('/api/users', {
      headers: {
        'Authorization': `Bearer ${userToken}`
      }
    });
    
    expect(response.status()).toBe(403);
  });
  
  test('admin deve obter detalhes de um usuário específico', async ({ request }) => {
    // Garantir que temos um token de admin e ID de usuário
    expect(adminToken).toBeDefined();
    expect(userId).toBeDefined();
    
    const response = await request.get(`/api/users/${userId}`, {
      headers: {
        'Authorization': `Bearer ${adminToken}`
      }
    });
    
    expect(response.status()).toBe(200);
    
    const userData = await response.json();
    expect(userData).toHaveProperty('id', userId);
    expect(userData).toHaveProperty('email', testUser.email);
    expect(userData).toHaveProperty('name', testUser.name);
    expect(userData).not.toHaveProperty('password');
  });
  
  test('usuário normal não deve obter detalhes de outro usuário', async ({ request }) => {
    // Garantir que temos um token de usuário
    expect(userToken).toBeDefined();
    
    // Tentar obter detalhes do usuário admin
    const adminId = (await (await request.get('/api/users/me', {
      headers: {
        'Authorization': `Bearer ${adminToken}`
      }
    })).json()).id;
    
    const response = await request.get(`/api/users/${adminId}`, {
      headers: {
        'Authorization': `Bearer ${userToken}`
      }
    });
    
    expect(response.status()).toBe(403);
  });
  
  test('admin deve atualizar um usuário específico', async ({ request }) => {
    // Garantir que temos um token de admin e ID de usuário
    expect(adminToken).toBeDefined();
    expect(userId).toBeDefined();
    
    const updatedData = {
      name: 'Admin Updated Name',
      role: 'editor'
    };
    
    const response = await request.put(`/api/users/${userId}`, {
      headers: {
        'Authorization': `Bearer ${adminToken}`
      },
      data: updatedData
    });
    
    expect(response.status()).toBe(200);
    
    const userData = await response.json();
    expect(userData).toHaveProperty('name', updatedData.name);
    expect(userData).toHaveProperty('role', updatedData.role);
    
    // Restaurar dados originais
    await request.put(`/api/users/${userId}`, {
      headers: {
        'Authorization': `Bearer ${adminToken}`
      },
      data: {
        name: testUser.name,
        role: 'user'
      }
    });
  });
  
  test('admin deve desativar e reativar um usuário', async ({ request }) => {
    // Garantir que temos um token de admin e ID de usuário
    expect(adminToken).toBeDefined();
    expect(userId).toBeDefined();
    
    // Desativar usuário
    const deactivateResponse = await request.post(`/api/users/${userId}/deactivate`, {
      headers: {
        'Authorization': `Bearer ${adminToken}`
      }
    });
    
    expect(deactivateResponse.status()).toBe(200);
    
    // Verificar que o usuário está desativado
    const userResponse = await request.get(`/api/users/${userId}`, {
      headers: {
        'Authorization': `Bearer ${adminToken}`
      }
    });
    
    const userData = await userResponse.json();
    expect(userData).toHaveProperty('isActive', false);
    
    // Tentar login com usuário desativado
    const loginResponse = await request.post('/api/auth/login', {
      data: {
        email: testUser.email,
        password: testUser.password
      }
    });
    
    expect(loginResponse.status()).toBe(401);
    
    // Reativar usuário
    const activateResponse = await request.post(`/api/users/${userId}/activate`, {
      headers: {
        'Authorization': `Bearer ${adminToken}`
      }
    });
    
    expect(activateResponse.status()).toBe(200);
    
    // Verificar que o usuário está ativo
    const updatedUserResponse = await request.get(`/api/users/${userId}`, {
      headers: {
        'Authorization': `Bearer ${adminToken}`
      }
    });
    
    const updatedUserData = await updatedUserResponse.json();
    expect(updatedUserData).toHaveProperty('isActive', true);
    
    // Verificar que pode fazer login novamente
    const newLoginResponse = await request.post('/api/auth/login', {
      data: {
        email: testUser.email,
        password: testUser.password
      }
    });
    
    expect(newLoginResponse.status()).toBe(200);
    
    // Atualizar token do usuário
    userToken = (await newLoginResponse.json()).accessToken;
  });
});
