/**
 * Fábrica de loggers
 *
 * Esta classe é responsável por criar instâncias de loggers.
 */

import {
  LoggerFactory as ILoggerFactory,
  LogLevel,
  Logger,
  LoggerConfig,
} from '../../domain/interfaces/Logger';
import { CompositeLogger } from './CompositeLogger';
import { ConsoleLogger } from './ConsoleLogger';
import { FileLogger } from './FileLogger';

/**
 * Configuração padrão para loggers
 */
const DEFAULT_CONFIG: LoggerConfig = {
  minLevel: LogLevel.INFO,
  includeTimestamp: true,
  includeContext: true,
  format: 'text',
  destinations: [
    {
      type: 'console',
      minLevel: LogLevel.INFO,
    },
  ],
};

/**
 * Fábrica de loggers
 */
export class LoggerFactory implements ILoggerFactory {
  /**
   * Configuração global para loggers
   */
  private readonly config: LoggerConfig;

  /**
   * Cache de loggers
   */
  private readonly loggers: Map<string, Logger> = new Map();

  /**
   * Logger global
   */
  private globalLogger: Logger | null = null;

  /**
   * Cria uma nova instância de LoggerFactory
   * @param config - Configuração para loggers
   */
  constructor(config: Partial<LoggerConfig> = {}) {
    this.config = {
      ...DEFAULT_CONFIG,
      ...config,
      destinations: [...(DEFAULT_CONFIG.destinations || []), ...(config.destinations || [])],
    };
  }

  /**
   * Cria um logger para um contexto específico
   * @param context - Contexto do logger
   * @returns Logger configurado para o contexto
   */
  public createLogger(context: string): Logger {
    // Verificar se já existe um logger para este contexto
    if (this.loggers.has(context)) {
      return this.loggers.get(context)!;
    }

    // Criar loggers para cada destino
    const loggers: Logger[] = [];

    for (const destination of this.config.destinations || []) {
      // Criar configuração específica para este destino
      const destinationConfig: LoggerConfig = {
        ...this.config,
        minLevel: destination.minLevel || this.config.minLevel,
      };

      // Criar logger apropriado
      let logger: Logger;

      switch (destination.type) {
        case 'console':
          logger = new ConsoleLogger(context, destinationConfig);
          break;
        case 'file':
          logger = new FileLogger(context, destinationConfig);
          break;
        default:
          console.warn(`Unknown logger destination type: ${destination.type}`);
          continue;
      }

      loggers.push(logger);
    }

    // Se não houver loggers, criar um logger de console padrão
    if (loggers.length === 0) {
      loggers.push(new ConsoleLogger(context, this.config));
    }

    // Se houver apenas um logger, usá-lo diretamente
    let logger: Logger;
    if (loggers.length === 1) {
      logger = loggers[0];
    } else {
      // Caso contrário, criar um logger composto
      logger = new CompositeLogger(loggers);
    }

    // Armazenar no cache
    this.loggers.set(context, logger);

    return logger;
  }

  /**
   * Obtém o logger global
   * @returns Logger global
   */
  public getGlobalLogger(): Logger {
    if (!this.globalLogger) {
      this.globalLogger = this.createLogger('global');
    }

    return this.globalLogger;
  }

  /**
   * Limpa o cache de loggers
   */
  public clearLoggers(): void {
    this.loggers.clear();
    this.globalLogger = null;
  }
}
