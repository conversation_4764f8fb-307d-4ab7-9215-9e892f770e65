/**
 * Tipos seguros para autenticação e autorização
 * Seguindo princípios de type safety para dados críticos de segurança
 */

import { z } from 'zod';

// Schemas de validação para entrada de dados
export const LoginSchema = z.object({
  email: z.string().email('Email inválido').min(1, 'Email é obrigatório'),
  password: z.string().min(8, 'Senha deve ter pelo menos 8 caracteres'),
  rememberMe: z.boolean().optional().default(false),
});

export const RegisterSchema = z.object({
  name: z.string().min(2, 'Nome deve ter pelo menos 2 caracteres'),
  email: z.string().email('Email inválido'),
  password: z.string()
    .min(8, 'Senha deve ter pelo menos 8 caracteres')
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, 'Senha deve conter ao menos uma letra minúscula, maiúscula e um número'),
  confirmPassword: z.string(),
  acceptTerms: z.boolean().refine(val => val === true, 'Deve aceitar os termos'),
}).refine(data => data.password === data.confirmPassword, {
  message: 'Senhas não coincidem',
  path: ['confirmPassword'],
});

export const PasswordResetSchema = z.object({
  email: z.string().email('Email inválido'),
});

export const PasswordChangeSchema = z.object({
  currentPassword: z.string().min(1, 'Senha atual é obrigatória'),
  newPassword: z.string()
    .min(8, 'Nova senha deve ter pelo menos 8 caracteres')
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, 'Nova senha deve conter ao menos uma letra minúscula, maiúscula e um número'),
  confirmNewPassword: z.string(),
}).refine(data => data.newPassword === data.confirmNewPassword, {
  message: 'Novas senhas não coincidem',
  path: ['confirmNewPassword'],
});

// Tipos inferidos dos schemas
export type LoginData = z.infer<typeof LoginSchema>;
export type RegisterData = z.infer<typeof RegisterSchema>;
export type PasswordResetData = z.infer<typeof PasswordResetSchema>;
export type PasswordChangeData = z.infer<typeof PasswordChangeSchema>;

// Interfaces para dados do usuário
export interface UserProfile {
  readonly id: string;
  readonly email: string;
  readonly name: string;
  readonly role: UserRole;
  readonly isActive: boolean;
  readonly emailVerified: boolean;
  readonly createdAt: Date;
  readonly updatedAt: Date;
  readonly lastLoginAt: Date | null;
}

// Interface para dados do usuário do banco de dados (compatível com schema existente)
export interface UserData {
  readonly ulid_user: string;
  readonly ulid_user_type: string;
  readonly ulid_school_type: string;
  readonly email: string;
  readonly password?: string; // Opcional para não expor em respostas
  readonly is_teacher: boolean;
  readonly name: string;
  readonly state: string;
  readonly county: string;
  readonly active: boolean;
  readonly is_active?: boolean; // Alias para compatibilidade
  readonly created_at: Date;
  readonly updated_at: Date;
  readonly last_login?: Date;
}

// Interface para dados seguros do usuário (sem senha)
export interface SafeUserData extends Omit<UserData, 'password'> {
  readonly ulid_user: string;
  readonly ulid_user_type: string;
  readonly ulid_school_type: string;
  readonly email: string;
  readonly is_teacher: boolean;
  readonly name: string;
  readonly state: string;
  readonly county: string;
  readonly active: boolean;
  readonly created_at: Date;
  readonly updated_at: Date;
  readonly last_login?: Date;
}

export interface UserSession {
  readonly userId: string;
  readonly sessionId: string;
  readonly email: string;
  readonly name: string;
  readonly role: UserRole;
  readonly permissions: Permission[];
  readonly expiresAt: Date;
  readonly issuedAt: Date;
}

// Enums para roles e permissões
export enum UserRole {
  STUDENT = 'student',
  TEACHER = 'teacher',
  ADMIN = 'admin',
  CONTENT_CREATOR = 'content_creator',
  SUPER_ADMIN = 'super_admin',
}

export enum Permission {
  // Permissões de conteúdo
  CONTENT_READ = 'content:read',
  CONTENT_CREATE = 'content:create',
  CONTENT_UPDATE = 'content:update',
  CONTENT_DELETE = 'content:delete',

  // Permissões de usuário
  USER_READ = 'user:read',
  USER_CREATE = 'user:create',
  USER_UPDATE = 'user:update',
  USER_DELETE = 'user:delete',

  // Permissões administrativas
  ADMIN_DASHBOARD = 'admin:dashboard',
  ADMIN_REPORTS = 'admin:reports',
  ADMIN_SETTINGS = 'admin:settings',

  // Permissões de sistema
  SYSTEM_MONITOR = 'system:monitor',
  SYSTEM_BACKUP = 'system:backup',
  SYSTEM_MAINTENANCE = 'system:maintenance',
}

// Interfaces para JWT
export interface JWTPayload {
  readonly sub: string; // User ID
  readonly email: string;
  readonly role: UserRole;
  readonly permissions: Permission[];
  readonly sessionId: string;
  readonly iat: number;
  readonly exp: number;
  readonly iss: string;
  readonly aud: string;
}

export interface RefreshTokenPayload {
  readonly sub: string; // User ID
  readonly sessionId: string;
  readonly tokenVersion: number;
  readonly iat: number;
  readonly exp: number;
}

// Interfaces para respostas de autenticação
export interface AuthResponse {
  readonly success: boolean;
  readonly user: UserProfile;
  readonly accessToken: string;
  readonly refreshToken: string;
  readonly expiresIn: number;
}

export interface AuthError {
  readonly success: false;
  readonly error: string;
  readonly code: AuthErrorCode;
  readonly details?: Record<string, string[]>;
}

export enum AuthErrorCode {
  INVALID_CREDENTIALS = 'INVALID_CREDENTIALS',
  USER_NOT_FOUND = 'USER_NOT_FOUND',
  USER_INACTIVE = 'USER_INACTIVE',
  EMAIL_NOT_VERIFIED = 'EMAIL_NOT_VERIFIED',
  TOKEN_EXPIRED = 'TOKEN_EXPIRED',
  TOKEN_INVALID = 'TOKEN_INVALID',
  SESSION_EXPIRED = 'SESSION_EXPIRED',
  PERMISSION_DENIED = 'PERMISSION_DENIED',
  RATE_LIMIT_EXCEEDED = 'RATE_LIMIT_EXCEEDED',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
}

// Type guards para validação de tipos
export function isUserProfile(obj: unknown): obj is UserProfile {
  return typeof obj === 'object' &&
         obj !== null &&
         'id' in obj &&
         'email' in obj &&
         'role' in obj;
}

export function isJWTPayload(obj: unknown): obj is JWTPayload {
  return typeof obj === 'object' &&
         obj !== null &&
         'sub' in obj &&
         'email' in obj &&
         'role' in obj &&
         'permissions' in obj;
}

// Utilitários para permissões
export function hasPermission(user: UserSession, permission: Permission): boolean {
  return user.permissions.includes(permission);
}

export function hasAnyPermission(user: UserSession, permissions: Permission[]): boolean {
  return permissions.some(permission => user.permissions.includes(permission));
}

export function hasAllPermissions(user: UserSession, permissions: Permission[]): boolean {
  return permissions.every(permission => user.permissions.includes(permission));
}

// Mapeamento de roles para permissões padrão
export const ROLE_PERMISSIONS: Record<UserRole, Permission[]> = {
  [UserRole.STUDENT]: [
    Permission.CONTENT_READ,
  ],
  [UserRole.TEACHER]: [
    Permission.CONTENT_READ,
    Permission.CONTENT_CREATE,
    Permission.CONTENT_UPDATE,
    Permission.USER_READ,
  ],
  [UserRole.CONTENT_CREATOR]: [
    Permission.CONTENT_READ,
    Permission.CONTENT_CREATE,
    Permission.CONTENT_UPDATE,
    Permission.CONTENT_DELETE,
  ],
  [UserRole.ADMIN]: [
    Permission.CONTENT_READ,
    Permission.CONTENT_CREATE,
    Permission.CONTENT_UPDATE,
    Permission.CONTENT_DELETE,
    Permission.USER_READ,
    Permission.USER_CREATE,
    Permission.USER_UPDATE,
    Permission.ADMIN_DASHBOARD,
    Permission.ADMIN_REPORTS,
  ],
  [UserRole.SUPER_ADMIN]: Object.values(Permission),
};
