/**
 * Configuração de estratégias de retry para produtores Kafka
 * 
 * Este arquivo contém as configurações para estratégias de retry
 * de produtores Kafka, incluindo políticas de retry, dead letter queues
 * e tratamento de erros.
 */

/**
 * Estratégias de retry disponíveis
 */
export enum RetryStrategy {
  /**
   * Retry exponencial com jitter
   * Aumenta o tempo de espera exponencialmente entre tentativas,
   * com um componente aleatório para evitar tempestades de retry
   */
  EXPONENTIAL_BACKOFF = 'exponential_backoff',
  
  /**
   * Retry com intervalo fixo
   * Usa o mesmo intervalo de tempo entre todas as tentativas
   */
  FIXED_INTERVAL = 'fixed_interval',
  
  /**
   * Retry com intervalo linear
   * Aumenta o tempo de espera linearmente entre tentativas
   */
  LINEAR_BACKOFF = 'linear_backoff',
  
  /**
   * Retry imediato
   * Tenta novamente imediatamente após uma falha
   */
  IMMEDIATE = 'immediate',
  
  /**
   * Estratégia personalizada
   * Permite implementar uma estratégia de retry personalizada
   */
  CUSTOM = 'custom',
}

/**
 * Tipos de erros que podem ocorrer durante a produção de mensagens
 */
export enum ProducerErrorType {
  /**
   * Erro de conexão com o broker
   */
  CONNECTION = 'connection',
  
  /**
   * Erro de autenticação
   */
  AUTHENTICATION = 'authentication',
  
  /**
   * Erro de autorização
   */
  AUTHORIZATION = 'authorization',
  
  /**
   * Erro de serialização da mensagem
   */
  SERIALIZATION = 'serialization',
  
  /**
   * Erro de validação da mensagem
   */
  VALIDATION = 'validation',
  
  /**
   * Erro de timeout
   */
  TIMEOUT = 'timeout',
  
  /**
   * Erro de broker não disponível
   */
  BROKER_NOT_AVAILABLE = 'broker_not_available',
  
  /**
   * Erro de tópico não existente
   */
  TOPIC_NOT_EXISTS = 'topic_not_exists',
  
  /**
   * Erro de partição não disponível
   */
  PARTITION_NOT_AVAILABLE = 'partition_not_available',
  
  /**
   * Erro de mensagem muito grande
   */
  MESSAGE_TOO_LARGE = 'message_too_large',
  
  /**
   * Erro desconhecido
   */
  UNKNOWN = 'unknown',
}

/**
 * Configuração de dead letter queue
 */
export interface DeadLetterQueueConfig {
  /**
   * Se a dead letter queue está habilitada
   */
  enabled: boolean;
  
  /**
   * Tópico para a dead letter queue
   */
  topic: string;
  
  /**
   * Se deve incluir a mensagem original
   */
  includeOriginalMessage: boolean;
  
  /**
   * Se deve incluir a stack trace do erro
   */
  includeStackTrace: boolean;
  
  /**
   * Se deve incluir metadados adicionais
   */
  includeMetadata: boolean;
  
  /**
   * Número máximo de tentativas antes de enviar para a dead letter queue
   */
  maxRetries?: number;
}

/**
 * Configuração de retry para um tipo de erro específico
 */
export interface ErrorTypeRetryConfig {
  /**
   * Número máximo de tentativas
   */
  maxRetries: number;
  
  /**
   * Intervalo inicial entre tentativas (ms)
   */
  initialRetryTimeMs: number;
  
  /**
   * Intervalo máximo entre tentativas (ms)
   */
  maxRetryTimeMs: number;
  
  /**
   * Fator de multiplicação para backoff exponencial
   */
  multiplier: number;
  
  /**
   * Fator de jitter (0-1) para adicionar aleatoriedade
   */
  jitter: number;
  
  /**
   * Se deve usar dead letter queue após esgotar as tentativas
   */
  useDeadLetterQueue: boolean;
}

/**
 * Configuração de retry para produtores
 */
export interface ProducerRetryConfig {
  /**
   * Estratégia de retry global
   */
  strategy: RetryStrategy;
  
  /**
   * Número máximo de tentativas global
   */
  maxRetries: number;
  
  /**
   * Intervalo inicial entre tentativas (ms)
   */
  initialRetryTimeMs: number;
  
  /**
   * Intervalo máximo entre tentativas (ms)
   */
  maxRetryTimeMs: number;
  
  /**
   * Fator de multiplicação para backoff exponencial
   */
  multiplier: number;
  
  /**
   * Fator de jitter (0-1) para adicionar aleatoriedade
   */
  jitter: number;
  
  /**
   * Configuração de dead letter queue
   */
  deadLetterQueue: DeadLetterQueueConfig;
  
  /**
   * Configurações específicas por tipo de erro
   */
  errorTypeConfigs?: Partial<Record<ProducerErrorType, ErrorTypeRetryConfig>>;
  
  /**
   * Configurações específicas por tópico
   */
  topicConfigs?: Record<string, Partial<ProducerRetryConfig>>;
}

/**
 * Configuração padrão de retry para produtores
 */
const defaultProducerRetryConfig: ProducerRetryConfig = {
  strategy: RetryStrategy.EXPONENTIAL_BACKOFF,
  maxRetries: 5,
  initialRetryTimeMs: 100,
  maxRetryTimeMs: 30000, // 30 segundos
  multiplier: 2,
  jitter: 0.2,
  deadLetterQueue: {
    enabled: true,
    topic: 'dead-letter-queue',
    includeOriginalMessage: true,
    includeStackTrace: true,
    includeMetadata: true,
  },
  errorTypeConfigs: {
    [ProducerErrorType.CONNECTION]: {
      maxRetries: 10,
      initialRetryTimeMs: 1000,
      maxRetryTimeMs: 60000, // 1 minuto
      multiplier: 1.5,
      jitter: 0.3,
      useDeadLetterQueue: true,
    },
    [ProducerErrorType.BROKER_NOT_AVAILABLE]: {
      maxRetries: 10,
      initialRetryTimeMs: 1000,
      maxRetryTimeMs: 60000, // 1 minuto
      multiplier: 1.5,
      jitter: 0.3,
      useDeadLetterQueue: true,
    },
    [ProducerErrorType.TOPIC_NOT_EXISTS]: {
      maxRetries: 2,
      initialRetryTimeMs: 500,
      maxRetryTimeMs: 2000,
      multiplier: 2,
      jitter: 0.1,
      useDeadLetterQueue: true,
    },
    [ProducerErrorType.SERIALIZATION]: {
      maxRetries: 0, // Não tentar novamente para erros de serialização
      initialRetryTimeMs: 0,
      maxRetryTimeMs: 0,
      multiplier: 1,
      jitter: 0,
      useDeadLetterQueue: true,
    },
    [ProducerErrorType.VALIDATION]: {
      maxRetries: 0, // Não tentar novamente para erros de validação
      initialRetryTimeMs: 0,
      maxRetryTimeMs: 0,
      multiplier: 1,
      jitter: 0,
      useDeadLetterQueue: true,
    },
    [ProducerErrorType.MESSAGE_TOO_LARGE]: {
      maxRetries: 0, // Não tentar novamente para mensagens muito grandes
      initialRetryTimeMs: 0,
      maxRetryTimeMs: 0,
      multiplier: 1,
      jitter: 0,
      useDeadLetterQueue: true,
    },
  },
  // Configurações específicas por tópico
  topicConfigs: {
    'payment.transaction.created': {
      maxRetries: 10, // Mais tentativas para transações de pagamento
      initialRetryTimeMs: 200,
      maxRetryTimeMs: 60000, // 1 minuto
    },
    'payment.transaction.updated': {
      maxRetries: 10,
      initialRetryTimeMs: 200,
      maxRetryTimeMs: 60000,
    },
    'order.created': {
      maxRetries: 8,
      initialRetryTimeMs: 200,
      maxRetryTimeMs: 45000,
    },
  },
};

/**
 * Carrega configuração de retry do ambiente
 */
function loadRetryConfigFromEnv(): Partial<ProducerRetryConfig> {
  return {
    strategy: (process.env.KAFKA_PRODUCER_RETRY_STRATEGY as RetryStrategy) || RetryStrategy.EXPONENTIAL_BACKOFF,
    maxRetries: parseInt(process.env.KAFKA_PRODUCER_MAX_RETRIES || '5', 10),
    initialRetryTimeMs: parseInt(process.env.KAFKA_PRODUCER_INITIAL_RETRY_TIME_MS || '100', 10),
    maxRetryTimeMs: parseInt(process.env.KAFKA_PRODUCER_MAX_RETRY_TIME_MS || '30000', 10),
    multiplier: parseFloat(process.env.KAFKA_PRODUCER_MULTIPLIER || '2'),
    jitter: parseFloat(process.env.KAFKA_PRODUCER_JITTER || '0.2'),
    deadLetterQueue: {
      enabled: process.env.KAFKA_PRODUCER_DLQ_ENABLED === 'true',
      topic: process.env.KAFKA_PRODUCER_DLQ_TOPIC || 'dead-letter-queue',
      includeOriginalMessage: process.env.KAFKA_PRODUCER_DLQ_INCLUDE_ORIGINAL_MESSAGE !== 'false',
      includeStackTrace: process.env.KAFKA_PRODUCER_DLQ_INCLUDE_STACK_TRACE !== 'false',
      includeMetadata: process.env.KAFKA_PRODUCER_DLQ_INCLUDE_METADATA !== 'false',
    },
  };
}

// Mesclar configuração padrão com configuração do ambiente
const envConfig = loadRetryConfigFromEnv();
export const kafkaProducerRetryConfig: ProducerRetryConfig = {
  ...defaultProducerRetryConfig,
  ...envConfig,
  deadLetterQueue: {
    ...defaultProducerRetryConfig.deadLetterQueue,
    ...envConfig.deadLetterQueue,
  },
};

/**
 * Obtém configuração de retry para um tópico específico
 * @param topicName - Nome do tópico
 * @returns Configuração de retry para o tópico
 */
export function getTopicRetryConfig(topicName: string): ProducerRetryConfig {
  const topicConfig = kafkaProducerRetryConfig.topicConfigs?.[topicName];
  
  if (!topicConfig) {
    return kafkaProducerRetryConfig;
  }
  
  return {
    ...kafkaProducerRetryConfig,
    ...topicConfig,
    deadLetterQueue: {
      ...kafkaProducerRetryConfig.deadLetterQueue,
      ...(topicConfig.deadLetterQueue || {}),
    },
  };
}

/**
 * Mapeia erros do Kafka para tipos de erro do produtor
 * @param error - Erro do Kafka
 * @returns Tipo de erro do produtor
 */
export function mapKafkaErrorToProducerErrorType(error: Error): ProducerErrorType {
  const errorMessage = error.message.toLowerCase();
  
  if (errorMessage.includes('connection') || errorMessage.includes('econnrefused') || errorMessage.includes('etimedout')) {
    return ProducerErrorType.CONNECTION;
  }
  
  if (errorMessage.includes('authentication') || errorMessage.includes('sasl')) {
    return ProducerErrorType.AUTHENTICATION;
  }
  
  if (errorMessage.includes('authorization') || errorMessage.includes('access') || errorMessage.includes('permission')) {
    return ProducerErrorType.AUTHORIZATION;
  }
  
  if (errorMessage.includes('serialization') || errorMessage.includes('parse') || errorMessage.includes('json')) {
    return ProducerErrorType.SERIALIZATION;
  }
  
  if (errorMessage.includes('validation') || errorMessage.includes('schema') || errorMessage.includes('invalid')) {
    return ProducerErrorType.VALIDATION;
  }
  
  if (errorMessage.includes('timeout')) {
    return ProducerErrorType.TIMEOUT;
  }
  
  if (errorMessage.includes('broker') || errorMessage.includes('leader')) {
    return ProducerErrorType.BROKER_NOT_AVAILABLE;
  }
  
  if (errorMessage.includes('topic') && (errorMessage.includes('not found') || errorMessage.includes('does not exist'))) {
    return ProducerErrorType.TOPIC_NOT_EXISTS;
  }
  
  if (errorMessage.includes('partition') && errorMessage.includes('not available')) {
    return ProducerErrorType.PARTITION_NOT_AVAILABLE;
  }
  
  if (errorMessage.includes('message') && errorMessage.includes('size') && errorMessage.includes('larger')) {
    return ProducerErrorType.MESSAGE_TOO_LARGE;
  }
  
  return ProducerErrorType.UNKNOWN;
}
