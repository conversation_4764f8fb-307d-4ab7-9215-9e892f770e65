/**
 * API para limpar estatísticas de cache
 *
 * Este endpoint permite limpar as estatísticas de cache coletadas.
 * Apenas administradores autenticados podem acessar este endpoint.
 */

import type { APIRoute } from 'astro';
import { clearCacheStats } from '../../../infrastructure/cache/LayeredCacheService';

// Configuração da API
const API_CONFIG = {
  // Chave secreta para autenticação (deve ser configurada como variável de ambiente em produção)
  secretKey: import.meta.env.ADMIN_SECRET_KEY || 'estacao-alfabetizacao-admin-secret',
};

/**
 * Verifica se a chave secreta é válida
 * @param secret Chave secreta fornecida
 * @returns Verdadeiro se a chave secreta é válida
 */
function isValidSecretKey(secret: string | null): boolean {
  return secret === API_CONFIG.secretKey;
}

/**
 * Endpoint para limpar estatísticas de cache
 */
export const POST: APIRoute = async ({ request }) => {
  try {
    // Verificar método
    if (request.method !== 'POST') {
      return new Response(
        JSON.stringify({
          success: false,
          message: 'Método não permitido. Use POST.',
        }),
        {
          status: 405,
          headers: {
            'Content-Type': 'application/json',
            Allow: 'POST',
          },
        }
      );
    }

    // Obter dados da requisição
    const data = await request.json().catch(() => ({}));

    // Verificar chave secreta
    const secret = data.secret || null;
    if (!isValidSecretKey(secret)) {
      return new Response(
        JSON.stringify({
          success: false,
          message: 'Chave secreta inválida ou não fornecida.',
        }),
        {
          status: 401,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    // Limpar estatísticas de cache
    clearCacheStats();

    // Retornar resposta de sucesso
    return new Response(
      JSON.stringify({
        success: true,
        message: 'Estatísticas de cache limpas com sucesso.',
        timestamp: new Date().toISOString(),
      }),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  } catch (error) {
    // Retornar resposta de erro
    return new Response(
      JSON.stringify({
        success: false,
        message: 'Erro ao processar requisição.',
        error: error instanceof Error ? error.message : String(error),
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  }
};
