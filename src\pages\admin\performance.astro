---
/**
 * Página de Dashboard de Performance
 *
 * Esta página exibe métricas de performance do site, incluindo Core Web Vitals.
 */

import WebVitalsDashboard from '../../components/performance/WebVitalsDashboard.astro';
import Layout from '../../layouts/AdminLayout.astro';
import { isAuthenticated } from '../../utils/auth';

// Verificar autenticação
const user = await isAuthenticated(Astro.request);

// Redirecionar se não estiver autenticado
if (!user || !user.isAdmin) {
  return Astro.redirect('/login?redirect=/admin/performance');
}

// Obter parâmetros da URL
const { searchParams } = Astro.url;
const pageId = searchParams.get('pageId');
const period = searchParams.get('period') ? Number.parseInt(searchParams.get('period')) : 7;
---

<Layout title="Dashboard de Performance">
  <div class="performance-dashboard">
    <header class="dashboard-header">
      <h1>Dashboard de Performance</h1>
      <p class="description">
        Monitore as métricas de performance do site, incluindo Core Web Vitals.
      </p>
    </header>
    
    <section class="filters">
      <h2>Filtros</h2>
      <form id="filters-form" class="filters-form">
        <div class="form-group">
          <label for="pageId">Página:</label>
          <select id="pageId" name="pageId">
            <option value="">Todas as páginas</option>
            <option value="/" selected={pageId === '/'}>Página inicial</option>
            <option value="/dashboard" selected={pageId === '/dashboard'}>Dashboard</option>
            <option value="/admin" selected={pageId === '/admin'}>Admin</option>
            <option value="/login" selected={pageId === '/login'}>Login</option>
            <option value="/register" selected={pageId === '/register'}>Registro</option>
          </select>
        </div>
        
        <div class="form-group">
          <label for="period">Período:</label>
          <select id="period" name="period">
            <option value="1" selected={period === 1}>Último dia</option>
            <option value="7" selected={period === 7}>Últimos 7 dias</option>
            <option value="30" selected={period === 30}>Últimos 30 dias</option>
            <option value="90" selected={period === 90}>Últimos 90 dias</option>
          </select>
        </div>
        
        <button type="submit" class="apply-filters">Aplicar filtros</button>
      </form>
    </section>
    
    <section class="web-vitals-section">
      <h2>Core Web Vitals</h2>
      <WebVitalsDashboard 
        pageId={pageId} 
        period={period} 
        autoRefresh={true} 
        refreshInterval={300}
        showDetails={true}
      />
    </section>
    
    <section class="performance-tips">
      <h2>Dicas de Otimização</h2>
      
      <div class="tips-grid">
        <div class="tip-card">
          <h3>Largest Contentful Paint (LCP)</h3>
          <p>Tempo até o maior elemento de conteúdo ser renderizado.</p>
          <ul>
            <li>Otimize e comprima imagens</li>
            <li>Implemente lazy loading para imagens abaixo da dobra</li>
            <li>Remova recursos que bloqueiam a renderização</li>
            <li>Utilize CDN para recursos estáticos</li>
            <li>Minimize CSS e JavaScript</li>
          </ul>
        </div>
        
        <div class="tip-card">
          <h3>First Input Delay (FID)</h3>
          <p>Tempo até o navegador responder à primeira interação.</p>
          <ul>
            <li>Divida código JavaScript em chunks menores</li>
            <li>Remova JavaScript não utilizado</li>
            <li>Minimize trabalho na thread principal</li>
            <li>Utilize Web Workers para tarefas pesadas</li>
            <li>Reduza o tempo de execução de JavaScript</li>
          </ul>
        </div>
        
        <div class="tip-card">
          <h3>Cumulative Layout Shift (CLS)</h3>
          <p>Soma de todas as mudanças inesperadas de layout.</p>
          <ul>
            <li>Sempre inclua dimensões para imagens e vídeos</li>
            <li>Reserve espaço para anúncios e embeds</li>
            <li>Evite inserir conteúdo acima do conteúdo existente</li>
            <li>Prefira transformações CSS para animações</li>
            <li>Carregue fontes web de forma otimizada</li>
          </ul>
        </div>
        
        <div class="tip-card">
          <h3>Time to First Byte (TTFB)</h3>
          <p>Tempo até o primeiro byte da resposta.</p>
          <ul>
            <li>Otimize o servidor (cache, compressão)</li>
            <li>Utilize CDN para conteúdo estático</li>
            <li>Estabeleça conexões antecipadas com preconnect</li>
            <li>Reduza o tempo de resposta do servidor</li>
            <li>Implemente cache no servidor e no cliente</li>
          </ul>
        </div>
      </div>
    </section>
    
    <section class="resources">
      <h2>Recursos Úteis</h2>
      <ul class="resources-list">
        <li>
          <a href="https://web.dev/vitals/" target="_blank" rel="noopener">
            Web Vitals - web.dev
          </a>
        </li>
        <li>
          <a href="https://pagespeed.web.dev/" target="_blank" rel="noopener">
            PageSpeed Insights
          </a>
        </li>
        <li>
          <a href="https://developer.chrome.com/docs/lighthouse/" target="_blank" rel="noopener">
            Lighthouse
          </a>
        </li>
        <li>
          <a href="https://web.dev/measure/" target="_blank" rel="noopener">
            Measure - web.dev
          </a>
        </li>
      </ul>
    </section>
  </div>
</Layout>

<script>
  // Configurar formulário de filtros
  document.addEventListener('DOMContentLoaded', () => {
    const form = document.getElementById('filters-form');
    
    if (form) {
      form.addEventListener('submit', (event) => {
        event.preventDefault();
        
        // Obter valores
        const pageId = document.getElementById('pageId').value;
        const period = document.getElementById('period').value;
        
        // Construir URL
        const url = new URL(window.location.href);
        
        if (pageId) {
          url.searchParams.set('pageId', pageId);
        } else {
          url.searchParams.delete('pageId');
        }
        
        if (period) {
          url.searchParams.set('period', period);
        } else {
          url.searchParams.delete('period');
        }
        
        // Navegar para URL com filtros
        window.location.href = url.toString();
      });
    }
  });
</script>

<style>
  .performance-dashboard {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem 1rem;
  }
  
  .dashboard-header {
    margin-bottom: 2rem;
  }
  
  .dashboard-header h1 {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    color: #333;
  }
  
  .description {
    font-size: 1.1rem;
    color: #666;
    max-width: 800px;
  }
  
  section {
    margin-bottom: 3rem;
  }
  
  section h2 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: #333;
    border-bottom: 2px solid #f0f0f0;
    padding-bottom: 0.5rem;
  }
  
  .filters {
    background-color: #f9f9f9;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 2rem;
  }
  
  .filters-form {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    align-items: flex-end;
  }
  
  .form-group {
    display: flex;
    flex-direction: column;
    min-width: 200px;
  }
  
  .form-group label {
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
    color: #555;
  }
  
  .form-group select {
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 1rem;
  }
  
  .apply-filters {
    background-color: #4ECDC4;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 0.5rem 1rem;
    font-size: 1rem;
    cursor: pointer;
    transition: background-color 0.2s;
  }
  
  .apply-filters:hover {
    background-color: #3dbeb6;
  }
  
  .tips-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-top: 1.5rem;
  }
  
  .tip-card {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    padding: 1.5rem;
  }
  
  .tip-card h3 {
    font-size: 1.2rem;
    margin-bottom: 0.5rem;
    color: #333;
  }
  
  .tip-card p {
    font-size: 0.9rem;
    color: #666;
    margin-bottom: 1rem;
  }
  
  .tip-card ul {
    padding-left: 1.5rem;
  }
  
  .tip-card li {
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
    color: #555;
  }
  
  .resources-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1rem;
    list-style: none;
    padding: 0;
  }
  
  .resources-list li {
    background-color: #f0f0f0;
    border-radius: 4px;
    padding: 1rem;
    transition: background-color 0.2s;
  }
  
  .resources-list li:hover {
    background-color: #e0e0e0;
  }
  
  .resources-list a {
    color: #333;
    text-decoration: none;
    display: block;
    font-weight: 500;
  }
  
  @media (max-width: 768px) {
    .filters-form {
      flex-direction: column;
      align-items: stretch;
    }
    
    .form-group {
      width: 100%;
    }
    
    .tips-grid {
      grid-template-columns: 1fr;
    }
  }
</style>
