/**
 * Default Sitemap Service
 *
 * Implementação padrão do serviço de sitemap.
 * Parte da implementação da tarefa 8.9.1 - Otimização para buscadores
 */

import axios from 'axios';
import { ChangeFrequency, SitemapUrl } from '../../domain/entities/Sitemap';
import { ContentRepository } from '../../domain/repositories/ContentRepository';
import { SitemapRepository } from '../../domain/repositories/SitemapRepository';
import { SitemapService } from '../../domain/services/SitemapService';

export class DefaultSitemapService implements SitemapService {
  private sitemapRepository: SitemapRepository;
  private contentRepository: ContentRepository;
  private baseUrl: string;
  private searchEngines: string[];

  constructor(
    sitemapRepository: SitemapRepository,
    contentRepository: ContentRepository,
    baseUrl: string,
    searchEngines: string[] = [
      'https://www.google.com/ping?sitemap=',
      'https://www.bing.com/ping?sitemap=',
    ]
  ) {
    this.sitemapRepository = sitemapRepository;
    this.contentRepository = contentRepository;
    this.baseUrl = baseUrl;
    this.searchEngines = searchEngines;
  }

  /**
   * Gera o sitemap para o site
   */
  async generateSitemap(): Promise<string> {
    try {
      return await this.sitemapRepository.generateXml();
    } catch (error) {
      console.error('Erro ao gerar sitemap:', error);
      return '';
    }
  }

  /**
   * Gera o índice de sitemaps
   */
  async generateSitemapIndex(): Promise<string> {
    try {
      return await this.sitemapRepository.generateSitemapIndex();
    } catch (error) {
      console.error('Erro ao gerar índice de sitemaps:', error);
      return '';
    }
  }

  /**
   * Adiciona uma URL ao sitemap
   */
  async addUrl(
    url: string,
    lastmod?: Date,
    changefreq?: ChangeFrequency,
    priority?: number,
    alternateLanguages?: Record<string, string>,
    sitemapName?: string
  ): Promise<boolean> {
    try {
      // Normalizar URL
      const normalizedUrl = this.normalizeUrl(url);

      // Criar objeto de URL do sitemap
      const sitemapUrl: SitemapUrl = {
        loc: normalizedUrl,
        lastmod,
        changefreq,
        priority,
        alternateLanguages,
      };

      // Adicionar URL ao sitemap
      return await this.sitemapRepository.addUrl(sitemapUrl, sitemapName);
    } catch (error) {
      console.error('Erro ao adicionar URL ao sitemap:', error);
      return false;
    }
  }

  /**
   * Remove uma URL do sitemap
   */
  async removeUrl(url: string, sitemapName?: string): Promise<boolean> {
    try {
      // Normalizar URL
      const normalizedUrl = this.normalizeUrl(url);

      // Remover URL do sitemap
      return await this.sitemapRepository.removeUrl(normalizedUrl, sitemapName);
    } catch (error) {
      console.error('Erro ao remover URL do sitemap:', error);
      return false;
    }
  }

  /**
   * Atualiza uma URL no sitemap
   */
  async updateUrl(
    url: string,
    updates: Partial<Omit<SitemapUrl, 'loc'>>,
    sitemapName?: string
  ): Promise<boolean> {
    try {
      // Normalizar URL
      const normalizedUrl = this.normalizeUrl(url);

      // Atualizar URL no sitemap
      return await this.sitemapRepository.updateUrl(normalizedUrl, updates, sitemapName);
    } catch (error) {
      console.error('Erro ao atualizar URL no sitemap:', error);
      return false;
    }
  }

  /**
   * Obtém todas as URLs do sitemap
   */
  async getAllUrls(sitemapName?: string): Promise<SitemapUrl[]> {
    try {
      return await this.sitemapRepository.getAllUrls(sitemapName);
    } catch (error) {
      console.error('Erro ao obter URLs do sitemap:', error);
      return [];
    }
  }

  /**
   * Verifica se uma URL existe no sitemap
   */
  async urlExists(url: string, sitemapName?: string): Promise<boolean> {
    try {
      // Normalizar URL
      const normalizedUrl = this.normalizeUrl(url);

      // Verificar se a URL existe no sitemap
      return await this.sitemapRepository.urlExists(normalizedUrl, sitemapName);
    } catch (error) {
      console.error('Erro ao verificar existência de URL no sitemap:', error);
      return false;
    }
  }

  /**
   * Atualiza a data de última modificação de uma URL
   */
  async updateLastmod(url: string, lastmod: Date, sitemapName?: string): Promise<boolean> {
    try {
      // Normalizar URL
      const normalizedUrl = this.normalizeUrl(url);

      // Atualizar data de última modificação
      return await this.sitemapRepository.updateLastmod(normalizedUrl, lastmod, sitemapName);
    } catch (error) {
      console.error('Erro ao atualizar data de última modificação:', error);
      return false;
    }
  }

  /**
   * Gera o sitemap para conteúdo específico
   */
  async generateContentSitemap(): Promise<string> {
    try {
      // Obter todo o conteúdo publicado
      const content = await this.contentRepository.getPublished();

      // Criar sitemap para conteúdo
      for (const item of content.items) {
        const url = `${this.baseUrl}/${item.type}/${item.slug}`;

        await this.addUrl(url, item.updatedAt, 'weekly', 0.8, undefined, 'content');
      }

      // Gerar XML do sitemap
      return await this.sitemapRepository.generateXml('content');
    } catch (error) {
      console.error('Erro ao gerar sitemap de conteúdo:', error);
      return '';
    }
  }

  /**
   * Gera o sitemap para produtos
   */
  async generateProductSitemap(): Promise<string> {
    try {
      // Obter produtos publicados
      const products = await this.contentRepository.getByType('product', ['published']);

      // Criar sitemap para produtos
      for (const product of products.items) {
        const url = `${this.baseUrl}/produtos/${product.slug}`;

        await this.addUrl(url, product.updatedAt, 'daily', 0.9, undefined, 'products');
      }

      // Gerar XML do sitemap
      return await this.sitemapRepository.generateXml('products');
    } catch (error) {
      console.error('Erro ao gerar sitemap de produtos:', error);
      return '';
    }
  }

  /**
   * Gera o sitemap para categorias
   */
  async generateCategorySitemap(): Promise<string> {
    try {
      // Implementação específica para categorias
      // Esta é uma implementação simplificada

      // Gerar XML do sitemap
      return await this.sitemapRepository.generateXml('categories');
    } catch (error) {
      console.error('Erro ao gerar sitemap de categorias:', error);
      return '';
    }
  }

  /**
   * Gera o sitemap para páginas estáticas
   */
  async generateStaticPagesSitemap(): Promise<string> {
    try {
      // Lista de páginas estáticas
      const staticPages = [
        { url: '', changefreq: 'daily' as ChangeFrequency, priority: 1.0 },
        { url: 'sobre', changefreq: 'monthly' as ChangeFrequency, priority: 0.8 },
        { url: 'contato', changefreq: 'monthly' as ChangeFrequency, priority: 0.8 },
        { url: 'termos-de-uso', changefreq: 'yearly' as ChangeFrequency, priority: 0.5 },
        { url: 'politica-de-privacidade', changefreq: 'yearly' as ChangeFrequency, priority: 0.5 },
        { url: 'faq', changefreq: 'monthly' as ChangeFrequency, priority: 0.7 },
      ];

      // Criar sitemap para páginas estáticas
      for (const page of staticPages) {
        const url = `${this.baseUrl}/${page.url}`;

        await this.addUrl(url, new Date(), page.changefreq, page.priority, undefined, 'static');
      }

      // Gerar XML do sitemap
      return await this.sitemapRepository.generateXml('static');
    } catch (error) {
      console.error('Erro ao gerar sitemap de páginas estáticas:', error);
      return '';
    }
  }

  /**
   * Atualiza todos os sitemaps
   */
  async updateAllSitemaps(): Promise<boolean> {
    try {
      // Gerar sitemaps específicos
      await this.generateContentSitemap();
      await this.generateProductSitemap();
      await this.generateCategorySitemap();
      await this.generateStaticPagesSitemap();

      // Gerar índice de sitemaps
      await this.generateSitemapIndex();

      return true;
    } catch (error) {
      console.error('Erro ao atualizar todos os sitemaps:', error);
      return false;
    }
  }

  /**
   * Notifica os motores de busca sobre a atualização do sitemap
   */
  async notifySearchEngines(): Promise<boolean> {
    try {
      const sitemapUrl = `${this.baseUrl}/sitemap.xml`;

      // Notificar cada motor de busca
      const promises = this.searchEngines.map((engine) => {
        const pingUrl = `${engine}${encodeURIComponent(sitemapUrl)}`;
        return axios.get(pingUrl);
      });

      // Aguardar todas as notificações
      await Promise.all(promises);

      return true;
    } catch (error) {
      console.error('Erro ao notificar motores de busca:', error);
      return false;
    }
  }

  /**
   * Normaliza uma URL
   */
  private normalizeUrl(url: string): string {
    // Verificar se a URL já tem o domínio
    if (url.startsWith('http://') || url.startsWith('https://')) {
      return url;
    }

    // Remover barra no início se existir
    const path = url.startsWith('/') ? url.substring(1) : url;

    // Adicionar domínio
    return `${this.baseUrl}/${path}`;
  }
}
