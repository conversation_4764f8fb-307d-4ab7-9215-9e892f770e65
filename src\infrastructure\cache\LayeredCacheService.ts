/**
 * Serviço de Cache em Camadas
 *
 * Este serviço implementa um sistema de cache em múltiplas camadas:
 * 1. Cache em memória (para itens frequentemente acessados)
 * 2. <PERSON><PERSON> distribuído (Valkey/Redis)
 *
 * Isso melhora a performance reduzindo a latência para itens frequentemente acessados
 * e diminui a carga no serviço de cache distribuído.
 */

import { createClient } from '@valkey/client';
import LRUCache from 'lru-cache';
import { LogLevel } from '../../application/interfaces/services/Logger';
import { ConsoleLogger } from '../logging/ConsoleLogger';

// Inicializar logger
const logger = new ConsoleLogger('LayeredCacheService', {
  level: LogLevel.INFO,
  useColors: true,
  format: 'text',
});

// Configuração do cache
const cacheConfig = {
  // Configuração do cache distribuído (Valkey/Redis)
  distributed: {
    url: process.env.REDIS_URL || 'redis://localhost:6379',
    ttl: Number.parseInt(process.env.CACHE_TTL || '3600', 10), // Tempo de vida padrão em segundos (1 hora)
    prefix: process.env.CACHE_PREFIX || 'estacao:',
  },

  // Configuração do cache em memória
  memory: {
    maxSize: Number.parseInt(process.env.MEMORY_CACHE_MAX_SIZE || '100', 10), // Número máximo de itens
    ttl: Number.parseInt(process.env.MEMORY_CACHE_TTL || '300', 10), // Tempo de vida padrão em segundos (5 minutos)
    updateAgeOnGet: true, // Atualizar idade do item ao acessá-lo
  },

  // Configuração de métricas
  metrics: {
    enabled: process.env.CACHE_METRICS_ENABLED === 'true',
    sampleRate: Number.parseFloat(process.env.CACHE_METRICS_SAMPLE_RATE || '0.1'), // Taxa de amostragem (10%)
  },
};

// Criar cliente Redis
const distributedClient = createClient({
  url: cacheConfig.distributed.url,
});

// Conectar ao Redis
distributedClient.connect().catch((err) => {
  logger.error('Erro ao conectar ao Redis', err);
});

// Criar cache em memória usando LRU
const memoryCache = new LRUCache<string, any>({
  max: cacheConfig.memory.maxSize,
  ttl: cacheConfig.memory.ttl * 1000, // Converter para milissegundos
  updateAgeOnGet: cacheConfig.memory.updateAgeOnGet,
});

// Métricas de cache
const metrics = {
  hits: {
    memory: 0,
    distributed: 0,
  },
  misses: {
    memory: 0,
    distributed: 0,
  },
  sets: {
    memory: 0,
    distributed: 0,
  },
  errors: {
    memory: 0,
    distributed: 0,
  },
};

/**
 * Registrar métrica de cache
 *
 * @param type Tipo de métrica (hit, miss, set, error)
 * @param layer Camada de cache (memory, distributed)
 */
function recordMetric(
  type: 'hits' | 'misses' | 'sets' | 'errors',
  layer: 'memory' | 'distributed'
): void {
  if (cacheConfig.metrics.enabled && Math.random() < cacheConfig.metrics.sampleRate) {
    metrics[type][layer]++;
  }
}

/**
 * Obter um valor do cache em camadas
 *
 * @param key Chave do cache
 * @returns Valor armazenado ou null se não encontrado
 */
export async function getLayeredCache<T>(key: string): Promise<T | null> {
  // Adicionar prefixo à chave para o cache distribuído
  const distributedKey = `${cacheConfig.distributed.prefix}${key}`;

  try {
    // Verificar primeiro no cache em memória
    if (memoryCache.has(key)) {
      const value = memoryCache.get(key);
      recordMetric('hits', 'memory');
      return value as T;
    }

    recordMetric('misses', 'memory');

    // Se não estiver no cache em memória, verificar no cache distribuído
    const distributedValue = await distributedClient.get(distributedKey);

    if (!distributedValue) {
      recordMetric('misses', 'distributed');
      return null;
    }

    recordMetric('hits', 'distributed');

    // Tentar converter para objeto
    let parsedValue: T;
    try {
      parsedValue = JSON.parse(distributedValue) as T;
    } catch {
      // Se não for um JSON válido, retornar como string
      parsedValue = distributedValue as unknown as T;
    }

    // Armazenar no cache em memória para acessos futuros
    memoryCache.set(key, parsedValue);
    recordMetric('sets', 'memory');

    return parsedValue;
  } catch (error) {
    // Registrar erro
    logger.error(`Erro ao obter valor do cache para chave ${key}`, error as Error);
    recordMetric('errors', 'distributed');

    // Em caso de erro, retornar null
    return null;
  }
}

/**
 * Define um valor no cache em camadas
 *
 * @param key Chave do cache
 * @param value Valor a ser armazenado
 * @param ttl Tempo de vida em segundos (opcional)
 * @param options Opções adicionais
 * @returns Verdadeiro se o valor foi armazenado com sucesso
 */
export async function setLayeredCache<T>(
  key: string,
  value: T,
  ttl?: number,
  options: {
    memoryOnly?: boolean;
    distributedOnly?: boolean;
    memoryTTL?: number;
    distributedTTL?: number;
  } = {}
): Promise<boolean> {
  // Adicionar prefixo à chave para o cache distribuído
  const distributedKey = `${cacheConfig.distributed.prefix}${key}`;

  try {
    // Converter valor para string
    const stringValue = typeof value === 'string' ? value : JSON.stringify(value);

    // Definir tempos de vida
    const memoryTTL = options.memoryTTL || ttl || cacheConfig.memory.ttl;
    const distributedTTL = options.distributedTTL || ttl || cacheConfig.distributed.ttl;

    // Armazenar no cache em memória (a menos que distributedOnly seja true)
    if (!options.distributedOnly) {
      memoryCache.set(key, value, { ttl: memoryTTL * 1000 }); // Converter para milissegundos
      recordMetric('sets', 'memory');
    }

    // Armazenar no cache distribuído (a menos que memoryOnly seja true)
    if (!options.memoryOnly) {
      await distributedClient.set(distributedKey, stringValue, { EX: distributedTTL });
      recordMetric('sets', 'distributed');
    }

    return true;
  } catch (error) {
    // Registrar erro
    logger.error(`Erro ao definir valor no cache para chave ${key}`, error as Error);
    recordMetric('errors', 'distributed');

    // Em caso de erro, retornar false
    return false;
  }
}

/**
 * Remove um valor do cache em camadas
 *
 * @param key Chave do cache
 * @param options Opções adicionais
 * @returns Verdadeiro se o valor foi removido com sucesso
 */
export async function deleteLayeredCache(
  key: string,
  options: {
    memoryOnly?: boolean;
    distributedOnly?: boolean;
  } = {}
): Promise<boolean> {
  // Adicionar prefixo à chave para o cache distribuído
  const distributedKey = `${cacheConfig.distributed.prefix}${key}`;

  try {
    // Remover do cache em memória (a menos que distributedOnly seja true)
    if (!options.distributedOnly) {
      memoryCache.delete(key);
    }

    // Remover do cache distribuído (a menos que memoryOnly seja true)
    if (!options.memoryOnly) {
      await distributedClient.del(distributedKey);
    }

    return true;
  } catch (error) {
    // Registrar erro
    logger.error(`Erro ao remover valor do cache para chave ${key}`, error as Error);

    // Em caso de erro, retornar false
    return false;
  }
}

/**
 * Obter estatísticas do cache em camadas
 *
 * @returns Estatísticas do cache
 */
export function getCacheStats(): typeof metrics {
  return { ...metrics };
}

/**
 * Limpar estatísticas do cache
 */
export function clearCacheStats(): void {
  metrics.hits.memory = 0;
  metrics.hits.distributed = 0;
  metrics.misses.memory = 0;
  metrics.misses.distributed = 0;
  metrics.sets.memory = 0;
  metrics.sets.distributed = 0;
  metrics.errors.memory = 0;
  metrics.errors.distributed = 0;
}

/**
 * Verificar se o serviço de cache está funcionando
 *
 * @returns Verdadeiro se o serviço estiver funcionando
 */
export async function verifyLayeredCacheService(): Promise<{
  memory: boolean;
  distributed: boolean;
}> {
  const result = {
    memory: false,
    distributed: false,
  };

  // Verificar cache em memória
  try {
    const testKey = 'test-memory';
    memoryCache.set(testKey, 'test');
    result.memory = memoryCache.get(testKey) === 'test';
  } catch (error) {
    logger.error('Erro ao verificar cache em memória', error as Error);
  }

  // Verificar cache distribuído
  try {
    await distributedClient.set('test-distributed', 'test');
    const value = await distributedClient.get('test-distributed');
    result.distributed = value === 'test';
  } catch (error) {
    logger.error('Erro ao verificar cache distribuído', error as Error);
  }

  return result;
}
