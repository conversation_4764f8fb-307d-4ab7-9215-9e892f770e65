---
/**
 * Componente de Item de Grid
 *
 * Este componente cria um item dentro de um grid com configurações de span e posicionamento.
 */

interface SpanConfig {
  base?: number;
  sm?: number;
  md?: number;
  lg?: number;
  xl?: number;
  '2xl'?: number;
}

interface StartConfig {
  base?: number;
  sm?: number;
  md?: number;
  lg?: number;
  xl?: number;
  '2xl'?: number;
}

interface Props {
  colSpan?: number | SpanConfig;
  rowSpan?: number | SpanConfig;
  colStart?: number | StartConfig;
  rowStart?: number | StartConfig;
  order?: number;
  class?: string;
  id?: string;
}

const { colSpan, rowSpan, colStart, rowStart, order, class: className = '', id } = Astro.props;

// Função para gerar classes de span
function getSpanClasses(span: number | SpanConfig, type: 'col' | 'row'): string {
  if (!span) return '';

  if (typeof span === 'number') {
    return `${type}-span-${span}`;
  }

  const classes = [];

  if (span.base) {
    classes.push(`${type}-span-${span.base}`);
  }

  Object.entries(span).forEach(([breakpoint, value]) => {
    if (breakpoint !== 'base' && value) {
      classes.push(`${breakpoint}:${type}-span-${value}`);
    }
  });

  return classes.filter(Boolean).join(' ');
}

// Função para gerar classes de start
function getStartClasses(start: number | StartConfig, type: 'col' | 'row'): string {
  if (!start) return '';

  if (typeof start === 'number') {
    return `${type}-start-${start}`;
  }

  const classes = [];

  if (start.base) {
    classes.push(`${type}-start-${start.base}`);
  }

  Object.entries(start).forEach(([breakpoint, value]) => {
    if (breakpoint !== 'base' && value) {
      classes.push(`${breakpoint}:${type}-start-${value}`);
    }
  });

  return classes.filter(Boolean).join(' ');
}

// Gerar classes para o item
const colSpanClass = getSpanClasses(colSpan, 'col');
const rowSpanClass = getSpanClasses(rowSpan, 'row');
const colStartClass = getStartClasses(colStart, 'col');
const rowStartClass = getStartClasses(rowStart, 'row');
const orderClass = order ? `order-${order}` : '';

// Combinar todas as classes
const itemClasses = [
  colSpanClass,
  rowSpanClass,
  colStartClass,
  rowStartClass,
  orderClass,
  className,
]
  .filter(Boolean)
  .join(' ');
---

<div class={itemClasses} id={id}>
  <slot />
</div>
