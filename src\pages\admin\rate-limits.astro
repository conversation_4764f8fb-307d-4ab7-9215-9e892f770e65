---
/**
 * Página de monitoramento de rate limiting
 *
 * Esta página exibe estatísticas e configurações de rate limiting.
 */

// Importações
import AdminLayout from '@layouts/AdminLayout.astro';
import { RateLimitType, rateLimitService } from '@services/rateLimitService';
import { getCurrentUser } from '@utils/authUtils';
import { logger } from '@utils/logger';
import { hasPermission } from '@utils/permissionUtils';

// Verificar autenticação e permissão
const user = await getCurrentUser(Astro.cookies);

// Redirecionar se não estiver autenticado ou não tiver permissão
if (!user || !(await hasPermission(user.ulid_user, 'security:manage'))) {
  return Astro.redirect('/admin/login?redirect=/admin/rate-limits');
}

// Obter bloqueios ativos
let blockedIPs = [];
let error = '';

try {
  // Obter bloqueios ativos do cache
  const blockKeys = await rateLimitService.getActiveBlocks();

  // Processar bloqueios
  blockedIPs = await Promise.all(
    blockKeys.map(async (key) => {
      const parts = key.split(':');
      const type = parts[2];
      const ip = parts.slice(3).join(':');

      // Obter tempo restante
      const blockInfo = await rateLimitService.isBlocked(ip, type as RateLimitType);

      return {
        ip,
        type,
        expiresInSeconds: blockInfo.expiresInSeconds,
        formattedExpiration: formatTime(blockInfo.expiresInSeconds),
      };
    })
  );

  // Ordenar por tempo de expiração
  blockedIPs.sort((a, b) => a.expiresInSeconds - b.expiresInSeconds);
} catch (e) {
  logger.error('Erro ao obter bloqueios de rate limit:', e);
  error = 'Não foi possível carregar os bloqueios ativos. Tente novamente mais tarde.';
}

// Processar ações
if (Astro.request.method === 'POST') {
  try {
    const formData = await Astro.request.formData();
    const action = formData.get('action');

    if (action === 'unblock') {
      const ip = formData.get('ip') as string;
      const type = formData.get('type') as RateLimitType;

      if (ip && type) {
        // Desbloquear IP
        await rateLimitService.unblockIdentifier(ip, type);

        // Redirecionar para atualizar a página
        return Astro.redirect('/admin/rate-limits?success=unblocked');
      }
    } else if (action === 'unblock-all') {
      // Desbloquear todos os IPs
      for (const block of blockedIPs) {
        await rateLimitService.unblockIdentifier(block.ip, block.type as RateLimitType);
      }

      // Redirecionar para atualizar a página
      return Astro.redirect('/admin/rate-limits?success=unblocked-all');
    }
  } catch (e) {
    logger.error('Erro ao processar ação de rate limit:', e);
    error = 'Ocorreu um erro ao processar sua solicitação. Tente novamente.';
  }
}

// Obter mensagens de sucesso
const success = Astro.url.searchParams.get('success');

let successMessage = '';

if (success === 'unblocked') {
  successMessage = 'IP desbloqueado com sucesso.';
} else if (success === 'unblocked-all') {
  successMessage = 'Todos os IPs foram desbloqueados com sucesso.';
}

// Formatar tempo em formato legível
function formatTime(seconds: number): string {
  if (seconds < 60) {
    return `${seconds} segundos`;
  }
  if (seconds < 3600) {
    const minutes = Math.floor(seconds / 60);
    return `${minutes} ${minutes === 1 ? 'minuto' : 'minutos'}`;
  }
  if (seconds < 86400) {
    const hours = Math.floor(seconds / 3600);
    return `${hours} ${hours === 1 ? 'hora' : 'horas'}`;
  }
  const days = Math.floor(seconds / 86400);
  return `${days} ${days === 1 ? 'dia' : 'dias'}`;
}

// Obter configurações de rate limiting
const limitConfigs = [
  {
    type: RateLimitType.GLOBAL,
    name: 'Global',
    description: 'Limite global para todas as requisições',
    config: rateLimitService.getConfig(RateLimitType.GLOBAL),
  },
  {
    type: RateLimitType.LOGIN,
    name: 'Login',
    description: 'Limite para tentativas de login',
    config: rateLimitService.getConfig(RateLimitType.LOGIN),
  },
  {
    type: RateLimitType.SIGNUP,
    name: 'Cadastro',
    description: 'Limite para criação de contas',
    config: rateLimitService.getConfig(RateLimitType.SIGNUP),
  },
  {
    type: RateLimitType.CONTACT,
    name: 'Contato',
    description: 'Limite para envio de formulários de contato',
    config: rateLimitService.getConfig(RateLimitType.CONTACT),
  },
  {
    type: RateLimitType.API,
    name: 'API',
    description: 'Limite para requisições à API',
    config: rateLimitService.getConfig(RateLimitType.API),
  },
  {
    type: RateLimitType.UPLOAD,
    name: 'Upload',
    description: 'Limite para upload de arquivos',
    config: rateLimitService.getConfig(RateLimitType.UPLOAD),
  },
  {
    type: RateLimitType.PAYMENT,
    name: 'Pagamento',
    description: 'Limite para requisições de pagamento',
    config: rateLimitService.getConfig(RateLimitType.PAYMENT),
  },
];

// Título da página
const title = 'Monitoramento de Rate Limiting';
---

<AdminLayout title={title}>
  <main class="container mx-auto px-4 py-8">
    <div class="max-w-6xl mx-auto">
      <h1 class="text-3xl font-bold mb-6">{title}</h1>
      
      {error && (
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
          <p>{error}</p>
        </div>
      )}
      
      {successMessage && (
        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6">
          <p>{successMessage}</p>
        </div>
      )}
      
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <!-- Bloqueios Ativos -->
        <div class="bg-white rounded-lg shadow p-6">
          <h2 class="text-xl font-semibold mb-4">Bloqueios Ativos</h2>
          
          {blockedIPs.length > 0 ? (
            <div class="overflow-x-auto">
              <table class="min-w-full divide-y divide-gray-200">
                <thead>
                  <tr>
                    <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Endereço IP
                    </th>
                    <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Tipo
                    </th>
                    <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Expira em
                    </th>
                    <th class="px-6 py-3 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Ações
                    </th>
                  </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                  {blockedIPs.map((block) => (
                    <tr>
                      <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        {block.ip}
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {block.type}
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {block.formattedExpiration}
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <form method="POST" class="inline">
                          <input type="hidden" name="action" value="unblock">
                          <input type="hidden" name="ip" value={block.ip}>
                          <input type="hidden" name="type" value={block.type}>
                          
                          <button 
                            type="submit"
                            class="text-indigo-600 hover:text-indigo-900"
                          >
                            Desbloquear
                          </button>
                        </form>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <div class="text-center py-4 text-gray-500">
              <p>Nenhum bloqueio ativo no momento.</p>
            </div>
          )}
          
          {blockedIPs.length > 0 && (
            <div class="mt-4 pt-4 border-t">
              <form method="POST">
                <input type="hidden" name="action" value="unblock-all">
                
                <button 
                  type="submit"
                  class="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700 transition"
                >
                  Desbloquear Todos
                </button>
              </form>
            </div>
          )}
        </div>
        
        <!-- Estatísticas -->
        <div class="bg-white rounded-lg shadow p-6">
          <h2 class="text-xl font-semibold mb-4">Estatísticas</h2>
          
          <div class="space-y-4">
            <div class="bg-gray-50 p-4 rounded">
              <p class="text-sm text-gray-600">Total de Bloqueios Ativos</p>
              <p class="text-2xl font-bold">{blockedIPs.length}</p>
            </div>
            
            <div class="grid grid-cols-2 gap-4">
              <div class="bg-blue-50 p-4 rounded">
                <p class="text-sm text-gray-600">Bloqueios de Login</p>
                <p class="text-2xl font-bold">{blockedIPs.filter(b => b.type === RateLimitType.LOGIN).length}</p>
              </div>
              
              <div class="bg-green-50 p-4 rounded">
                <p class="text-sm text-gray-600">Bloqueios de API</p>
                <p class="text-2xl font-bold">{blockedIPs.filter(b => b.type === RateLimitType.API).length}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Configurações -->
      <div class="bg-white rounded-lg shadow p-6">
        <h2 class="text-xl font-semibold mb-4">Configurações de Rate Limiting</h2>
        
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead>
              <tr>
                <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Tipo
                </th>
                <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Descrição
                </th>
                <th class="px-6 py-3 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Máx. Requisições
                </th>
                <th class="px-6 py-3 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Janela de Tempo
                </th>
                <th class="px-6 py-3 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Duração do Bloqueio
                </th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              {limitConfigs.map((limitConfig) => (
                <tr>
                  <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    {limitConfig.name}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {limitConfig.description}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-right">
                    {limitConfig.config.maxRequests}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-right">
                    {formatTime(limitConfig.config.windowSizeInSeconds)}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-right">
                    {limitConfig.config.blockDurationInSeconds ? formatTime(limitConfig.config.blockDurationInSeconds) : 'Sem bloqueio'}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </main>
</AdminLayout>
