---
import { actions } from 'astro:actions';
import ControlButtons from '@components/form/ControlButtons.astro';
import FormBase from '@components/form/FormBase.astro';
import InputCheckbox from '@components/form/InputCheckbox.astro';
import InputHidden from '@components/form/InputHidden.astro';
import InputText from '@components/form/InputText.astro';
import AdminLayout from '@layouts/AdminLayout.astro';
import type { CategoryData } from 'src/database/interfacesHelper';

// Obter parâmetro da URL
const { ulid_category = '' } = Astro.params;

// Buscar dados da categoria
const categoryResult = await actions.categoryAction.read({
  ulid_category: ulid_category,
});

// Buscar lista de categorias para parent
const categoriesResult = await actions.categoryAction.read({
  active: true,
});

if (!categoryResult.data?.success) {
  return Astro.redirect('/admin/register/category?error=load');
}

const category = categoryResult.data?.data || [];
const currentCategory = category[0] || {};
const categories = categoriesResult.data?.data || [];

const formValidation = (form: HTMLFormElement) => {
  const input = form.querySelector('input[name="name"]') as HTMLInputElement;
  if (!input?.value.trim()) {
    alert('O nome da categoria é obrigatório');
    return false;
  }
  return true;
};
---

<AdminLayout title="Editar Categoria">
  <div class="container mx-auto p-4">
    <h1 class="text-2xl font-bold mb-4">Editar Categoria</h1>

    <FormBase
      action={actions.categoryAction.update}
      onSubmitValidation={formValidation}
    >
      <!-- Campos ocultos -->
      <InputHidden field="ulid_category" ulid={currentCategory.ulid_category} />

      <!-- Nome -->
      <InputText 
        label="Nome" 
        name="name" 
        value={currentCategory.name} 
        required={true}
      />

      <!-- Status -->
      <InputCheckbox 
        label="Ativo" 
        name="active" 
        checked={currentCategory.active}
      />

      <!-- Categoria Pai -->
      <div class="form-control">
        <label class="label">
          <span class="label-text">Categoria Pai</span>
        </label>
        <select 
          name="ulid_parent"
          class="select select-bordered w-full"
        >
          <option value="">Selecione uma categoria pai</option>
          {categories.map((cat: CategoryData) => (
            cat.ulid_category !== currentCategory.ulid_category && (
              <option 
                value={cat.ulid_category}
                selected={cat.ulid_category === currentCategory.ulid_parent}
              >
                {cat.name}
              </option>
            )
          ))}
        </select>
      </div>

      <!-- Botões de controle -->
      <ControlButtons 
        saveLabel="Salvar"
        cancelHref="/admin/register/category"
      />
    </FormBase>
  </div>
</AdminLayout>