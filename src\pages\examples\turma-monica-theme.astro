---
import DaisyButton from '../../components/ui/DaisyButton.astro';
import DaisyCard from '../../components/ui/DaisyCard.astro';
import BaseLayout from '../../layouts/BaseLayout.astro';

// Importar configuração de temas
import themeConfig from '../../themes/themeConfig';
import { colorPalette } from '../../themes/turmaDaMonica.js';

const title = 'Tema Inspirado na Turma da Mônica';

// Te<PERSON> da Turma da Mônica
const turmaDaMonicaThemes = [
  {
    id: 'turma_monica',
    name: '<PERSON><PERSON> Claro',
    description: 'Tema claro inspirado nas cores da Turma da Mônica',
  },
  {
    id: 'turma_monica_dark',
    name: '<PERSON><PERSON> E<PERSON>curo',
    description: 'Versão escura do tema da Turma da Mônica',
  },
  {
    id: 'turma_monica_kids',
    name: '<PERSON><PERSON> Infantil',
    description: 'Tema colorido e lúdico para o público infantil',
  },
];
---

<BaseLayout title={title}>
  <div class="container mx-auto px-4 py-8">
    <h1 class="text-4xl font-bold mb-8 text-center">{title}</h1>

    <section class="mb-12">
      <h2 class="text-2xl font-bold mb-4">Sobre o Tema</h2>

      <div class="card p-6 mb-8">
        <p class="mb-4">
          Este tema foi inspirado nas cores vibrantes e alegres da Turma da Mônica,
          adaptado para o contexto educacional da Estação da Alfabetização.
          Utilizamos as cores características, mas sem fazer referência direta aos personagens.
        </p>

        <p>
          O tema está disponível em três variações: claro, escuro e infantil,
          cada um adaptado para diferentes contextos de uso e preferências dos usuários.
        </p>
      </div>
    </section>

    <section class="mb-12">
      <h2 class="text-2xl font-bold mb-4">Paleta de Cores</h2>

      <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
        <div class="card p-4 flex flex-col items-center">
          <div class="w-16 h-16 rounded-full mb-2" style="background-color: #0066CC;"></div>
          <span class="font-bold">Azul Forte</span>
          <span class="text-sm">#0066CC</span>
        </div>

        <div class="card p-4 flex flex-col items-center">
          <div class="w-16 h-16 rounded-full mb-2" style="background-color: #FF0000;"></div>
          <span class="font-bold">Vermelho Vivo</span>
          <span class="text-sm">#FF0000</span>
        </div>

        <div class="card p-4 flex flex-col items-center">
          <div class="w-16 h-16 rounded-full mb-2" style="background-color: #66CC66;"></div>
          <span class="font-bold">Verde Claro</span>
          <span class="text-sm">#66CC66</span>
        </div>

        <div class="card p-4 flex flex-col items-center">
          <div class="w-16 h-16 rounded-full mb-2" style="background-color: #FFCC00;"></div>
          <span class="font-bold">Amarelo Vivo</span>
          <span class="text-sm">#FFCC00</span>
        </div>

        <div class="card p-4 flex flex-col items-center">
          <div class="w-16 h-16 rounded-full mb-2" style="background-color: #FF9933;"></div>
          <span class="font-bold">Laranja</span>
          <span class="text-sm">#FF9933</span>
        </div>

        <div class="card p-4 flex flex-col items-center">
          <div class="w-16 h-16 rounded-full mb-2" style="background-color: #FF66CC;"></div>
          <span class="font-bold">Rosa</span>
          <span class="text-sm">#FF66CC</span>
        </div>

        <div class="card p-4 flex flex-col items-center">
          <div class="w-16 h-16 rounded-full mb-2" style="background-color: #9966CC;"></div>
          <span class="font-bold">Roxo</span>
          <span class="text-sm">#9966CC</span>
        </div>

        <div class="card p-4 flex flex-col items-center">
          <div class="w-16 h-16 rounded-full mb-2" style="background-color: #66CCFF;"></div>
          <span class="font-bold">Azul Claro</span>
          <span class="text-sm">#66CCFF</span>
        </div>
      </div>
    </section>

    <section class="mb-12">
      <h2 class="text-2xl font-bold mb-4">Variações do Tema</h2>

      <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        {turmaDaMonicaThemes.map(theme => (
          <div class="card bordered" data-theme={theme.id}>
            <div class="card-body">
              <h3 class="card-title">{theme.name}</h3>
              <p>{theme.description}</p>

              <div class="flex flex-wrap gap-2 my-4">
                <div class="badge badge-primary">primary</div>
                <div class="badge badge-secondary">secondary</div>
                <div class="badge badge-accent">accent</div>
                <div class="badge badge-neutral">neutral</div>
              </div>

              <div class="flex flex-wrap gap-2">
                <DaisyButton variant="primary">Botão</DaisyButton>
                <DaisyButton variant="secondary">Botão</DaisyButton>
                <DaisyButton variant="accent">Botão</DaisyButton>
              </div>
            </div>
          </div>
        ))}
      </div>
    </section>

    <section class="mb-12">
      <h2 class="text-2xl font-bold mb-4">Exemplos de Componentes</h2>

      <div data-theme="turma_monica">
        <h3 class="text-xl font-bold mb-4">Tema Claro da Turma da Mônica</h3>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
          <DaisyCard
            title="Card de Exemplo"
            image={{
              src: "https://placehold.co/600x400/0066CC/FFFFFF.png?text=Estação+da+Alfabetização",
              alt: "Placeholder"
            }}
          >
            <p>Este card usa o tema claro inspirado na Turma da Mônica.</p>
            <div slot="actions" class="card-actions justify-end">
              <DaisyButton variant="primary">Ação</DaisyButton>
              <DaisyButton variant="secondary">Cancelar</DaisyButton>
            </div>
          </DaisyCard>

          <div class="card">
            <div class="card-body">
              <h3 class="card-title">Elementos de UI</h3>

              <div class="form-control w-full max-w-xs mb-4">
                <label class="label">
                  <span class="label-text">Nome</span>
                </label>
                <input type="text" placeholder="Digite seu nome" class="input input-bordered w-full max-w-xs" />
              </div>

              <div class="form-control mb-4">
                <label class="cursor-pointer label">
                  <span class="label-text">Lembrar de mim</span>
                  <input type="checkbox" class="checkbox checkbox-primary" />
                </label>
              </div>

              <div class="flex flex-wrap gap-2">
                <button class="btn">Normal</button>
                <button class="btn btn-primary">Primary</button>
                <button class="btn btn-secondary">Secondary</button>
                <button class="btn btn-accent">Accent</button>
                <button class="btn btn-outline">Outline</button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div data-theme="turma_monica_kids" class="mt-8">
        <h3 class="text-xl font-bold mb-4">Tema Infantil da Turma da Mônica</h3>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <DaisyCard
            title="Card Infantil"
            image={{
              src: "https://placehold.co/600x400/FF66CC/FFFFFF.png?text=Estação+da+Alfabetização",
              alt: "Placeholder"
            }}
          >
            <p>Este card usa o tema infantil inspirado na Turma da Mônica, com cantos mais arredondados e cores mais vibrantes.</p>
            <div slot="actions" class="card-actions justify-end">
              <DaisyButton variant="primary">Jogar</DaisyButton>
              <DaisyButton variant="secondary">Voltar</DaisyButton>
            </div>
          </DaisyCard>

          <div class="card">
            <div class="card-body">
              <h3 class="card-title">Elementos Lúdicos</h3>

              <div class="flex flex-wrap gap-2 mb-4">
                <div class="badge badge-lg">Grande</div>
                <div class="badge">Normal</div>
                <div class="badge badge-sm">Pequeno</div>
                <div class="badge badge-primary badge-outline">Contorno</div>
              </div>

              <progress class="progress progress-primary w-full mb-4" value="70" max="100"></progress>

              <div class="flex flex-wrap gap-2">
                <button class="btn btn-circle">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
                <button class="btn btn-circle btn-primary">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                  </svg>
                </button>
                <button class="btn btn-circle btn-secondary">?</button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <section class="mb-12">
      <h2 class="text-2xl font-bold mb-4">Como Usar o Tema</h2>

      <div class="card p-6">
        <h3 class="text-xl font-bold mb-4">Aplicando o Tema</h3>

        <p class="mb-4">Para usar o tema da Turma da Mônica em toda a aplicação:</p>

        <pre class="bg-base-200 p-4 rounded-lg mb-4 overflow-x-auto"><code>&lt;html data-theme="turma_monica"&gt;
  &lt;!-- Conteúdo da página --&gt;
&lt;/html&gt;</code></pre>

        <p class="mb-4">Para aplicar o tema a um elemento específico:</p>

        <pre class="bg-base-200 p-4 rounded-lg mb-4 overflow-x-auto"><code>&lt;div data-theme="turma_monica_kids"&gt;
  &lt;!-- Este conteúdo usará o tema infantil --&gt;
&lt;/div&gt;</code></pre>

        <p>Para alternar entre os temas, use o componente ThemeSwitcher ou defina o atributo data-theme via JavaScript:</p>

        <pre class="bg-base-200 p-4 rounded-lg overflow-x-auto"><code>// Definir tema via JavaScript
document.documentElement.setAttribute('data-theme', 'turma_monica');</code></pre>
      </div>
    </section>
  </div>
</BaseLayout>
