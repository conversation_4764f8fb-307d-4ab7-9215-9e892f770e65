---
import FinancialNav from '@components/navigation/FinancialNav.astro';
import { queryHelper } from '@db/queryHelper';
import { checkAdmin } from '@helpers/authGuard';
import AdminLayout from '@layouts/AdminLayout.astro';

// Protege a rota verificando se o usuário é admin
const authRedirect = await checkAdmin(Astro);
if (authRedirect) return authRedirect;

// Obter ID da configuração de alerta
const { id } = Astro.params;

// Buscar configuração de alerta
const alertConfig = await queryHelper.queryOne(
  'SELECT * FROM tab_alert_config WHERE ulid_alert_config = $1',
  [id]
);

// Verificar se a configuração existe
if (!alertConfig) {
  return Astro.redirect('/admin/financial/alerts');
}

// Mapear tipos de alertas para nomes amigáveis
const alertTypeNames = {
  high_value_transaction: 'Transação de Alto Valor',
  suspicious_activity: 'Atividade Suspeita',
  payment_failure: 'Falha no Pagamento',
  chargeback_risk: 'Risco de Chargeback',
  payment_success: 'Pagamento Aprovado',
  refund_processed: 'Reembolso Processado',
};

// Buscar usuários administradores para a lista de destinatários
const adminUsers = await queryHelper.query(
  `SELECT ulid_user, name, email FROM tab_user WHERE role = 'admin' ORDER BY name`
);

// Processar formulário
if (Astro.request.method === 'POST') {
  try {
    const formData = await Astro.request.formData();

    // Obter valores do formulário
    const enabled = formData.get('enabled') === 'on';
    const threshold = formData.get('threshold')
      ? Number.parseFloat(formData.get('threshold').toString())
      : null;
    const notifyCustomer = formData.get('notify_customer') === 'on';
    const recipients = formData.getAll('recipients').join(',');
    const template = formData.get('template')?.toString() || alertConfig.template;

    // Atualizar configuração
    await queryHelper.query(
      `UPDATE tab_alert_config 
       SET enabled = $1, 
           threshold = $2, 
           notify_customer = $3, 
           recipients = $4, 
           template = $5,
           updated_at = NOW()
       WHERE ulid_alert_config = $6`,
      [enabled, threshold, notifyCustomer, recipients, template, id]
    );

    // Redirecionar para a lista de alertas
    return Astro.redirect('/admin/financial/alerts?success=true');
  } catch (error) {
    console.error('Erro ao atualizar configuração de alerta:', error);
  }
}

// Obter lista de destinatários atual
const currentRecipients = alertConfig.recipients ? alertConfig.recipients.split(',') : [];
---

<AdminLayout title={`Editar Alerta - ${alertTypeNames[alertConfig.type] || alertConfig.type}`}>
  <div class="container mx-auto p-4">
    <h1 class="text-2xl font-bold mb-4">Gestão Financeira</h1>
    
    <FinancialNav />
    
    <div class="flex items-center mb-6">
      <a href="/admin/financial/alerts" class="btn btn-ghost btn-sm mr-2">
        <i class="fas fa-arrow-left"></i> Voltar
      </a>
      <h2 class="text-xl font-bold">Editar Configuração de Alerta</h2>
    </div>
    
    <!-- Formulário de Edição -->
    <div class="card bg-base-100 shadow-xl">
      <div class="card-body">
        <h2 class="card-title">{alertTypeNames[alertConfig.type] || alertConfig.type}</h2>
        <p class="text-sm mb-4">{alertConfig.description}</p>
        
        <form method="POST" class="space-y-4">
          <!-- Status do Alerta -->
          <div class="form-control">
            <label class="label cursor-pointer justify-start">
              <input 
                type="checkbox" 
                name="enabled" 
                class="toggle toggle-primary mr-2" 
                checked={alertConfig.enabled}
              />
              <span class="label-text">Ativar este alerta</span>
            </label>
          </div>
          
          <!-- Limite (para alertas que usam threshold) -->
          {['high_value_transaction'].includes(alertConfig.type) && (
            <div class="form-control">
              <label class="label">
                <span class="label-text">Limite de Valor (R$)</span>
              </label>
              <input 
                type="number" 
                name="threshold" 
                value={alertConfig.threshold || ''} 
                step="0.01" 
                min="0" 
                class="input input-bordered"
                placeholder="Ex: 1000.00"
              />
              <label class="label">
                <span class="label-text-alt">Valor mínimo para disparar o alerta</span>
              </label>
            </div>
          )}
          
          <!-- Notificar Cliente -->
          <div class="form-control">
            <label class="label cursor-pointer justify-start">
              <input 
                type="checkbox" 
                name="notify_customer" 
                class="toggle toggle-info mr-2" 
                checked={alertConfig.notify_customer}
              />
              <span class="label-text">Enviar notificação para o cliente</span>
            </label>
          </div>
          
          <!-- Destinatários (Administradores) -->
          <div class="form-control">
            <label class="label">
              <span class="label-text">Destinatários (Administradores)</span>
            </label>
            <select name="recipients" class="select select-bordered" multiple>
              {adminUsers.rows.map(user => (
                <option 
                  value={user.email} 
                  selected={currentRecipients.includes(user.email)}
                >
                  {user.name} ({user.email})
                </option>
              ))}
            </select>
            <label class="label">
              <span class="label-text-alt">Segure Ctrl para selecionar múltiplos destinatários</span>
            </label>
          </div>
          
          <!-- Template de E-mail -->
          <div class="form-control">
            <label class="label">
              <span class="label-text">Template de E-mail</span>
            </label>
            <textarea 
              name="template" 
              class="textarea textarea-bordered h-32"
              placeholder="Template HTML do e-mail"
            >{alertConfig.template}</textarea>
            <label class="label">
              <span class="label-text-alt">Use {{variáveis}} para inserir dados dinâmicos</span>
            </label>
          </div>
          
          <!-- Botões de Ação -->
          <div class="card-actions justify-end">
            <a href="/admin/financial/alerts" class="btn btn-ghost">Cancelar</a>
            <button type="submit" class="btn btn-primary">Salvar Alterações</button>
          </div>
        </form>
      </div>
    </div>
  </div>
</AdminLayout>

<script>
  // Inicializar componentes interativos
  document.addEventListener('DOMContentLoaded', () => {
    // Código de inicialização, se necessário
  });
</script>
