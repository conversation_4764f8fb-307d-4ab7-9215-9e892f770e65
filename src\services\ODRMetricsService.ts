/**
 * Serviço de Métricas para On-Demand Rendering
 *
 * Este serviço coleta, armazena e analisa métricas de performance para o ODR.
 * Ele permite monitorar o desempenho do sistema e identificar problemas.
 */

import { createClient } from '@valkey/client';
import { LogLevel } from '../application/interfaces/services/Logger';
import { ConsoleLogger } from '../infrastructure/logging/ConsoleLogger';

// Inicializar logger
const logger = new ConsoleLogger('ODRMetricsService', {
  level: LogLevel.INFO,
  useColors: true,
  format: 'text',
});

// Configuração do serviço de métricas
const metricsConfig = {
  // Prefixo para chaves de métricas
  prefix: 'metrics:odr:',

  // Tempo de retenção de métricas (em segundos)
  retention: {
    raw: 60 * 60 * 24 * 7, // 7 dias para dados brutos
    hourly: 60 * 60 * 24 * 30, // 30 dias para agregações horárias
    daily: 60 * 60 * 24 * 90, // 90 dias para agregações diárias
  },

  // Taxa de amostragem (percentual de requisições a serem registradas)
  sampleRate: Number.parseFloat(process.env.ODR_METRICS_SAMPLE_RATE || '0.1'), // 10% por padrão

  // Limites para alertas
  thresholds: {
    renderTime: Number.parseInt(process.env.ODR_THRESHOLD_RENDER_TIME || '500', 10), // 500ms
    cacheHitRate: Number.parseFloat(process.env.ODR_THRESHOLD_CACHE_HIT_RATE || '0.8'), // 80%
  },
};

// Criar cliente Redis para armazenamento de métricas
const redisClient = createClient({
  url: process.env.REDIS_URL || 'redis://localhost:6379',
});

// Conectar ao Redis
redisClient.connect().catch((err) => {
  logger.error('Erro ao conectar ao Redis para métricas', err);
});

/**
 * Tipos de métricas coletadas
 */
export enum MetricType {
  RENDER_TIME = 'render_time',
  CACHE_HIT = 'cache_hit',
  CACHE_MISS = 'cache_miss',
  REVALIDATION = 'revalidation',
  ERROR = 'error',
}

/**
 * Interface para métricas de ODR
 */
export interface ODRMetric {
  type: MetricType;
  value: number;
  timestamp: number;
  path: string;
  contentType?: string;
  contentId?: string;
  userAgent?: string;
  statusCode?: number;
  error?: string;
}

/**
 * Registrar uma métrica de ODR
 *
 * @param metric Métrica a ser registrada
 * @returns Verdadeiro se a métrica foi registrada com sucesso
 */
export async function recordODRMetric(metric: ODRMetric): Promise<boolean> {
  // Aplicar taxa de amostragem
  if (Math.random() > metricsConfig.sampleRate) {
    return true; // Ignorar esta métrica devido à taxa de amostragem
  }

  try {
    // Adicionar timestamp se não fornecido
    if (!metric.timestamp) {
      metric.timestamp = Date.now();
    }

    // Serializar métrica
    const serializedMetric = JSON.stringify(metric);

    // Gerar chaves para diferentes agregações
    const rawKey = `${metricsConfig.prefix}raw:${metric.type}:${Date.now()}`;
    const pathKey = `${metricsConfig.prefix}path:${metric.path}:${metric.type}`;
    const hourlyKey = `${metricsConfig.prefix}hourly:${metric.type}:${getHourlyTimeBucket(metric.timestamp)}`;
    const dailyKey = `${metricsConfig.prefix}daily:${metric.type}:${getDailyTimeBucket(metric.timestamp)}`;

    // Armazenar métrica bruta
    await redisClient.set(rawKey, serializedMetric, { EX: metricsConfig.retention.raw });

    // Incrementar contadores para agregações
    await redisClient.incr(pathKey);
    await redisClient.expire(pathKey, metricsConfig.retention.raw);

    // Adicionar à lista de métricas horárias
    await redisClient.lPush(hourlyKey, serializedMetric);
    await redisClient.expire(hourlyKey, metricsConfig.retention.hourly);

    // Adicionar à lista de métricas diárias
    await redisClient.lPush(dailyKey, serializedMetric);
    await redisClient.expire(dailyKey, metricsConfig.retention.daily);

    // Verificar limites para alertas
    checkThresholds(metric);

    return true;
  } catch (error) {
    logger.error('Erro ao registrar métrica de ODR', error as Error);
    return false;
  }
}

/**
 * Obter métricas de ODR para um período
 *
 * @param type Tipo de métrica
 * @param period Período (hourly ou daily)
 * @param count Número de períodos a retornar
 * @returns Métricas agregadas
 */
export async function getODRMetrics(
  type: MetricType,
  period: 'hourly' | 'daily',
  count = 24
): Promise<Array<{ timestamp: number; count: number; avg: number }>> {
  try {
    const now = Date.now();
    const result = [];

    // Obter métricas para cada período
    for (let i = 0; i < count; i++) {
      const timestamp =
        period === 'hourly'
          ? now - i * 60 * 60 * 1000 // Subtrair horas
          : now - i * 24 * 60 * 60 * 1000; // Subtrair dias

      const timeBucket =
        period === 'hourly' ? getHourlyTimeBucket(timestamp) : getDailyTimeBucket(timestamp);

      const key = `${metricsConfig.prefix}${period}:${type}:${timeBucket}`;

      // Obter métricas do período
      const metrics = await redisClient.lRange(key, 0, -1);

      if (metrics.length > 0) {
        // Calcular média e contagem
        let sum = 0;
        let validCount = 0;

        for (const metricStr of metrics) {
          try {
            const metric = JSON.parse(metricStr) as ODRMetric;
            sum += metric.value;
            validCount++;
          } catch (e) {
            // Ignorar métricas inválidas
          }
        }

        result.push({
          timestamp:
            period === 'hourly' ? new Date(timeBucket).getTime() : new Date(timeBucket).getTime(),
          count: metrics.length,
          avg: validCount > 0 ? sum / validCount : 0,
        });
      } else {
        // Sem métricas para este período
        result.push({
          timestamp:
            period === 'hourly' ? new Date(timeBucket).getTime() : new Date(timeBucket).getTime(),
          count: 0,
          avg: 0,
        });
      }
    }

    // Ordenar por timestamp (mais recente primeiro)
    return result.sort((a, b) => b.timestamp - a.timestamp);
  } catch (error) {
    logger.error(`Erro ao obter métricas de ODR (${type}, ${period})`, error as Error);
    return [];
  }
}

/**
 * Obter taxa de acerto de cache
 *
 * @param period Período (hourly ou daily)
 * @returns Taxa de acerto de cache
 */
export async function getCacheHitRate(period: 'hourly' | 'daily'): Promise<number> {
  try {
    // Obter métricas de cache hit e miss
    const hits = await getODRMetrics(MetricType.CACHE_HIT, period, 1);
    const misses = await getODRMetrics(MetricType.CACHE_MISS, period, 1);

    // Calcular taxa de acerto
    const hitCount = hits.length > 0 ? hits[0].count : 0;
    const missCount = misses.length > 0 ? misses[0].count : 0;
    const total = hitCount + missCount;

    return total > 0 ? hitCount / total : 0;
  } catch (error) {
    logger.error(`Erro ao calcular taxa de acerto de cache (${period})`, error as Error);
    return 0;
  }
}

/**
 * Obter métricas por caminho
 *
 * @param path Caminho da URL
 * @returns Métricas para o caminho
 */
export async function getMetricsByPath(path: string): Promise<Record<string, number>> {
  try {
    const result: Record<string, number> = {};

    // Obter métricas para cada tipo
    for (const type of Object.values(MetricType)) {
      const key = `${metricsConfig.prefix}path:${path}:${type}`;
      const count = await redisClient.get(key);

      result[type] = count ? Number.parseInt(count, 10) : 0;
    }

    return result;
  } catch (error) {
    logger.error(`Erro ao obter métricas por caminho (${path})`, error as Error);
    return {};
  }
}

/**
 * Verificar se uma métrica ultrapassa os limites definidos
 *
 * @param metric Métrica a ser verificada
 */
function checkThresholds(metric: ODRMetric): void {
  // Verificar tempo de renderização
  if (
    metric.type === MetricType.RENDER_TIME &&
    metric.value > metricsConfig.thresholds.renderTime
  ) {
    logger.warn(`Tempo de renderização acima do limite para ${metric.path}: ${metric.value}ms`);

    // Aqui poderia ser implementado um sistema de alertas
  }
}

/**
 * Obter bucket de tempo horário
 *
 * @param timestamp Timestamp em milissegundos
 * @returns String no formato YYYY-MM-DD-HH
 */
function getHourlyTimeBucket(timestamp: number): string {
  const date = new Date(timestamp);
  return `${date.getFullYear()}-${padZero(date.getMonth() + 1)}-${padZero(date.getDate())}-${padZero(date.getHours())}`;
}

/**
 * Obter bucket de tempo diário
 *
 * @param timestamp Timestamp em milissegundos
 * @returns String no formato YYYY-MM-DD
 */
function getDailyTimeBucket(timestamp: number): string {
  const date = new Date(timestamp);
  return `${date.getFullYear()}-${padZero(date.getMonth() + 1)}-${padZero(date.getDate())}`;
}

/**
 * Adicionar zero à esquerda para números menores que 10
 *
 * @param num Número a ser formatado
 * @returns String formatada
 */
function padZero(num: number): string {
  return num < 10 ? `0${num}` : `${num}`;
}
