/**
 * Serviço de Otimização de Frontend
 *
 * Este serviço implementa funcionalidades para otimização de assets,
 * carregamento de componentes e estratégias de cache para o frontend.
 *
 * Características:
 * - Otimização de carregamento de assets
 * - Implementação de lazy loading
 * - Configuração de compressão de resposta
 * - Implementação de cache de frontend
 */

import { existsSync, readFileSync, writeFileSync } from 'node:fs';
import { join } from 'node:path';
import { applicationMonitoringService } from '@services/applicationMonitoringService';
import { cachePolicyService } from '@services/cachePolicyService';
import { logger } from '@utils/logger';

/**
 * Interface para configuração de otimização
 */
export interface OptimizationConfig {
  /**
   * Se deve minificar CSS e JavaScript
   * @default true
   */
  minify: boolean;

  /**
   * Se deve otimizar imagens
   * @default true
   */
  optimizeImages: boolean;

  /**
   * Se deve implementar lazy loading
   * @default true
   */
  lazyLoading: boolean;

  /**
   * Se deve implementar compressão de resposta
   * @default true
   */
  compression: boolean;

  /**
   * Se deve implementar cache de frontend
   * @default true
   */
  caching: boolean;

  /**
   * Tempo de vida do cache em segundos
   * @default 86400 (24 horas)
   */
  cacheTTL: number;
}

/**
 * Interface para estatísticas de assets
 */
export interface AssetStats {
  /**
   * Tamanho total de JavaScript em bytes
   */
  jsSize: number;

  /**
   * Tamanho total de CSS em bytes
   */
  cssSize: number;

  /**
   * Tamanho total de imagens em bytes
   */
  imageSize: number;

  /**
   * Tamanho total de fontes em bytes
   */
  fontSize: number;

  /**
   * Tamanho total de outros assets em bytes
   */
  otherSize: number;

  /**
   * Número total de arquivos JavaScript
   */
  jsCount: number;

  /**
   * Número total de arquivos CSS
   */
  cssCount: number;

  /**
   * Número total de imagens
   */
  imageCount: number;

  /**
   * Número total de fontes
   */
  fontCount: number;

  /**
   * Número total de outros assets
   */
  otherCount: number;
}

/**
 * Interface para configuração de lazy loading
 */
export interface LazyLoadingConfig {
  /**
   * Se deve aplicar lazy loading a imagens
   * @default true
   */
  images: boolean;

  /**
   * Se deve aplicar lazy loading a iframes
   * @default true
   */
  iframes: boolean;

  /**
   * Se deve aplicar lazy loading a componentes
   * @default true
   */
  components: boolean;

  /**
   * Distância de carregamento antecipado em pixels
   * @default 200
   */
  rootMargin: number;

  /**
   * Limiar de interseção para carregamento
   * @default 0.1
   */
  threshold: number;
}

/**
 * Serviço de otimização de frontend
 */
export const frontendOptimizationService = {
  /**
   * Configuração padrão
   */
  DEFAULT_CONFIG: {
    minify: true,
    optimizeImages: true,
    lazyLoading: true,
    compression: true,
    caching: true,
    cacheTTL: 86400, // 24 horas
  } as OptimizationConfig,

  /**
   * Configuração de lazy loading padrão
   */
  DEFAULT_LAZY_LOADING_CONFIG: {
    images: true,
    iframes: true,
    components: true,
    rootMargin: 200,
    threshold: 0.1,
  } as LazyLoadingConfig,

  /**
   * Inicializa o serviço de otimização de frontend
   */
  async initialize(): Promise<void> {
    try {
      logger.info('Inicializando serviço de otimização de frontend');

      // Registrar métricas
      applicationMonitoringService.registerMetric(
        'frontend_js_size',
        'Tamanho total de JavaScript em KB'
      );
      applicationMonitoringService.registerMetric(
        'frontend_css_size',
        'Tamanho total de CSS em KB'
      );
      applicationMonitoringService.registerMetric(
        'frontend_image_size',
        'Tamanho total de imagens em KB'
      );
      applicationMonitoringService.registerMetric(
        'frontend_total_size',
        'Tamanho total de assets em KB'
      );
      applicationMonitoringService.registerMetric(
        'frontend_load_time',
        'Tempo de carregamento da página em ms'
      );

      // Configurar políticas de cache para assets estáticos
      await this.configureCachePolicies();

      logger.info('Serviço de otimização de frontend inicializado com sucesso');
    } catch (error) {
      logger.error('Erro ao inicializar serviço de otimização de frontend:', error);
      throw error;
    }
  },

  /**
   * Configura políticas de cache para assets estáticos
   */
  async configureCachePolicies(): Promise<void> {
    try {
      // Configurar política para arquivos JavaScript
      await cachePolicyService.createPolicy({
        name: 'static-js',
        patterns: ['/assets/*.js', '/_astro/*.js'],
        headers: {
          'Cache-Control': 'public, max-age=31536000, immutable',
          Vary: 'Accept-Encoding',
        },
        ttl: 31536000, // 1 ano
      });

      // Configurar política para arquivos CSS
      await cachePolicyService.createPolicy({
        name: 'static-css',
        patterns: ['/assets/*.css', '/_astro/*.css'],
        headers: {
          'Cache-Control': 'public, max-age=31536000, immutable',
          Vary: 'Accept-Encoding',
        },
        ttl: 31536000, // 1 ano
      });

      // Configurar política para imagens
      await cachePolicyService.createPolicy({
        name: 'static-images',
        patterns: [
          '/assets/images/*',
          '/assets/*.png',
          '/assets/*.jpg',
          '/assets/*.jpeg',
          '/assets/*.gif',
          '/assets/*.webp',
          '/assets/*.svg',
        ],
        headers: {
          'Cache-Control': 'public, max-age=604800, stale-while-revalidate=86400',
          Vary: 'Accept-Encoding',
        },
        ttl: 604800, // 1 semana
      });

      // Configurar política para fontes
      await cachePolicyService.createPolicy({
        name: 'static-fonts',
        patterns: [
          '/assets/fonts/*',
          '/assets/*.woff',
          '/assets/*.woff2',
          '/assets/*.ttf',
          '/assets/*.eot',
        ],
        headers: {
          'Cache-Control': 'public, max-age=31536000, immutable',
          Vary: 'Accept-Encoding',
        },
        ttl: 31536000, // 1 ano
      });

      logger.info('Políticas de cache para assets estáticos configuradas com sucesso');
    } catch (error) {
      logger.error('Erro ao configurar políticas de cache para assets estáticos:', error);
      throw error;
    }
  },

  /**
   * Gera configuração para lazy loading de imagens
   * @param config - Configuração de lazy loading
   * @returns Código JavaScript para lazy loading
   */
  generateLazyLoadingScript(config: LazyLoadingConfig = this.DEFAULT_LAZY_LOADING_CONFIG): string {
    return `
      document.addEventListener('DOMContentLoaded', function() {
        // Configuração do Intersection Observer
        const options = {
          rootMargin: '${config.rootMargin}px',
          threshold: ${config.threshold}
        };

        const observer = new IntersectionObserver((entries, observer) => {
          entries.forEach(entry => {
            if (entry.isIntersecting) {
              const element = entry.target;
              
              // Processar imagens
              if (element.tagName === 'IMG' && element.dataset.src) {
                element.src = element.dataset.src;
                if (element.dataset.srcset) {
                  element.srcset = element.dataset.srcset;
                }
                element.classList.add('loaded');
              }
              
              // Processar iframes
              if (element.tagName === 'IFRAME' && element.dataset.src) {
                element.src = element.dataset.src;
                element.classList.add('loaded');
              }
              
              // Processar componentes lazy
              if (element.classList.contains('lazy-component') && element.dataset.component) {
                const componentId = element.dataset.component;
                import(/* @vite-ignore */ componentId)
                  .then(module => {
                    // Componente carregado
                    element.classList.add('loaded');
                  })
                  .catch(error => {
                    console.error('Erro ao carregar componente:', error);
                  });
              }
              
              // Parar de observar após carregar
              observer.unobserve(element);
            }
          });
        }, options);

        // Observar imagens com data-src
        if (${config.images}) {
          document.querySelectorAll('img[data-src]').forEach(img => {
            observer.observe(img);
          });
        }
        
        // Observar iframes com data-src
        if (${config.iframes}) {
          document.querySelectorAll('iframe[data-src]').forEach(iframe => {
            observer.observe(iframe);
          });
        }
        
        // Observar componentes lazy
        if (${config.components}) {
          document.querySelectorAll('.lazy-component').forEach(component => {
            observer.observe(component);
          });
        }
      });
    `;
  },

  /**
   * Gera configuração para compressão de resposta
   * @returns Configuração para compressão
   */
  generateCompressionConfig(): string {
    return `
      # Configuração de compressão para Nginx
      gzip on;
      gzip_comp_level 6;
      gzip_min_length 256;
      gzip_proxied any;
      gzip_vary on;
      gzip_types
        application/atom+xml
        application/javascript
        application/json
        application/ld+json
        application/manifest+json
        application/rss+xml
        application/vnd.geo+json
        application/vnd.ms-fontobject
        application/x-font-ttf
        application/x-web-app-manifest+json
        application/xhtml+xml
        application/xml
        font/opentype
        image/bmp
        image/svg+xml
        image/x-icon
        text/cache-manifest
        text/css
        text/plain
        text/vcard
        text/vnd.rim.location.xloc
        text/vtt
        text/x-component
        text/x-cross-domain-policy;

      # Configuração de Brotli (se disponível)
      brotli on;
      brotli_comp_level 6;
      brotli_types
        application/atom+xml
        application/javascript
        application/json
        application/ld+json
        application/manifest+json
        application/rss+xml
        application/vnd.geo+json
        application/vnd.ms-fontobject
        application/x-font-ttf
        application/x-web-app-manifest+json
        application/xhtml+xml
        application/xml
        font/opentype
        image/bmp
        image/svg+xml
        image/x-icon
        text/cache-manifest
        text/css
        text/plain
        text/vcard
        text/vnd.rim.location.xloc
        text/vtt
        text/x-component
        text/x-cross-domain-policy;
    `;
  },

  /**
   * Gera configuração para cache de frontend
   * @param ttl - Tempo de vida do cache em segundos
   * @returns Configuração para cache
   */
  generateCacheConfig(ttl: number = this.DEFAULT_CONFIG.cacheTTL): string {
    return `
      # Configuração de cache para Nginx
      location ~* \\.(?:css|js)$ {
        expires 1y;
        access_log off;
        add_header Cache-Control "public, immutable";
      }

      location ~* \\.(?:jpg|jpeg|gif|png|ico|webp|svg)$ {
        expires 1w;
        access_log off;
        add_header Cache-Control "public, stale-while-revalidate=86400";
      }

      location ~* \\.(?:woff|woff2|ttf|otf|eot)$ {
        expires 1y;
        access_log off;
        add_header Cache-Control "public, immutable";
      }
    `;
  },

  /**
   * Obtém estatísticas de assets
   * @param publicDir - Diretório público
   * @returns Estatísticas de assets
   */
  async getAssetStats(publicDir = 'public'): Promise<AssetStats> {
    try {
      // Implementação simplificada para demonstração
      // Em um cenário real, seria necessário percorrer o diretório e calcular os tamanhos

      // Valores fictícios para demonstração
      const stats: AssetStats = {
        jsSize: 1024 * 1024, // 1 MB
        cssSize: 512 * 1024, // 512 KB
        imageSize: 5 * 1024 * 1024, // 5 MB
        fontSize: 2 * 1024 * 1024, // 2 MB
        otherSize: 256 * 1024, // 256 KB
        jsCount: 15,
        cssCount: 5,
        imageCount: 50,
        fontCount: 8,
        otherCount: 10,
      };

      // Registrar métricas
      applicationMonitoringService.recordMetric('frontend_js_size', stats.jsSize / 1024);
      applicationMonitoringService.recordMetric('frontend_css_size', stats.cssSize / 1024);
      applicationMonitoringService.recordMetric('frontend_image_size', stats.imageSize / 1024);
      applicationMonitoringService.recordMetric(
        'frontend_total_size',
        (stats.jsSize + stats.cssSize + stats.imageSize + stats.fontSize + stats.otherSize) / 1024
      );

      return stats;
    } catch (error) {
      logger.error('Erro ao obter estatísticas de assets:', error);
      throw error;
    }
  },

  /**
   * Gera HTML para lazy loading de imagens
   * @param src - URL da imagem
   * @param alt - Texto alternativo
   * @param width - Largura
   * @param height - Altura
   * @param className - Classes CSS
   * @returns HTML para imagem com lazy loading
   */
  generateLazyImageHTML(
    src: string,
    alt = '',
    width?: number,
    height?: number,
    className = ''
  ): string {
    // Placeholder para imagens (1x1 pixel transparente)
    const placeholder =
      'data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7';

    return `
      <img 
        src="${placeholder}" 
        data-src="${src}" 
        alt="${alt}" 
        ${width ? `width="${width}"` : ''} 
        ${height ? `height="${height}"` : ''} 
        class="lazy-image ${className}" 
        loading="lazy"
      />
    `;
  },

  /**
   * Gera HTML para lazy loading de componentes
   * @param componentId - ID do componente
   * @param placeholder - HTML de placeholder
   * @param className - Classes CSS
   * @returns HTML para componente com lazy loading
   */
  generateLazyComponentHTML(componentId: string, placeholder = '', className = ''): string {
    return `
      <div 
        class="lazy-component ${className}" 
        data-component="${componentId}"
      >
        ${placeholder}
      </div>
    `;
  },
};
