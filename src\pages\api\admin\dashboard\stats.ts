/**
 * API de Estatísticas do Dashboard Administrativo
 *
 * Endpoint para obter estatísticas do dashboard administrativo.
 * Parte da implementação da tarefa 8.8.1 - Painel administrativo
 */

import type { APIRoute } from 'astro';
import { DashboardService } from '../../../../domain/services/DashboardService';
import { GetDashboardStatsUseCase } from '../../../../domain/usecases/admin/GetDashboardStatsUseCase';
import { PostgresDashboardService } from '../../../../infrastructure/services/PostgresDashboardService';

// Inicializar serviço
const dashboardService: DashboardService = new PostgresDashboardService();

// Inicializar caso de uso
const getDashboardStatsUseCase = new GetDashboardStatsUseCase(dashboardService);

export const GET: APIRoute = async ({ request }) => {
  try {
    const url = new URL(request.url);

    // Obter parâmetros de consulta
    const startDate = url.searchParams.get('startDate');
    const endDate = url.searchParams.get('endDate');
    const userRoles = url.searchParams.get('userRoles');
    const contentTypes = url.searchParams.get('contentTypes');
    const productIds = url.searchParams.get('productIds');
    const fiscalDocumentTypes = url.searchParams.get('fiscalDocumentTypes');

    // Construir filtro
    const filter: any = {};

    if (startDate) {
      filter.startDate = new Date(startDate);
    }

    if (endDate) {
      filter.endDate = new Date(endDate);
    }

    if (userRoles) {
      filter.userRoles = userRoles.split(',');
    }

    if (contentTypes) {
      filter.contentTypes = contentTypes.split(',');
    }

    if (productIds) {
      filter.productIds = productIds.split(',');
    }

    if (fiscalDocumentTypes) {
      filter.fiscalDocumentTypes = fiscalDocumentTypes.split(',');
    }

    // Obter estatísticas do dashboard
    const result = await getDashboardStatsUseCase.execute({
      filter: Object.keys(filter).length > 0 ? filter : undefined,
    });

    if (result.success) {
      return new Response(
        JSON.stringify({
          success: true,
          data: result.data,
        }),
        {
          status: 200,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }
    return new Response(
      JSON.stringify({
        success: false,
        error: result.error || 'Erro desconhecido ao obter estatísticas do dashboard.',
      }),
      {
        status: 400,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  } catch (error) {
    console.error('Erro ao processar obtenção de estatísticas do dashboard:', error);

    return new Response(
      JSON.stringify({
        success: false,
        error:
          'Ocorreu um erro ao processar a obtenção das estatísticas do dashboard. Por favor, tente novamente.',
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  }
};
