---
import { actions } from 'astro:actions';
import ControlButtons from '@components/form/ControlButtons.astro';
import FormBase from '@components/form/FormBase.astro';
import InputHidden from '@components/form/InputHidden.astro';
import InputText from '@components/form/InputText.astro';
import AdminLayout from '@layouts/AdminLayout.astro';
import type { PaymentStatusData } from 'src/database/interfacesHelper';

// Obter parâmetro da URL
const { ulid_payment_status = '' } = Astro.params;

// Busca dados do status de pagamento
const paymentStatusResult = await actions.paymentStatusAction.read({
  filter: 'ulid_payment_status',
  ulid_payment_status,
});

if (!paymentStatusResult.data?.success) {
  return Astro.redirect('/admin/register/payment/status?error=load');
}

const paymentStatus = paymentStatusResult.data.data;

const formValidation = (form: HTMLFormElement) => {
  const statusInput = form.querySelector('input[name="status"]') as HTMLInputElement;
  if (!statusInput || !statusInput.value.trim()) {
    alert('O status de pagamento é obrigatório');
    return false;
  }
  return true;
};

const onFormSubmitted = (e: CustomEvent) => {
  const { success, error } = e.detail;
  if (success) {
    window.location.href = '/admin/register/payment/status?success=save';
  } else if (error) {
    alert(`Erro ao salvar: ${error}`);
  }
};

const paymentStatusData = Astro.props.data;
---
<AdminLayout title="Editar Status de Pagamento">
  <div class="container mx-auto p-4">
    <h1 class="text-2xl font-bold mb-4">
      {ulid_payment_status ? "Editar" : "Novo"} Status de Pagamento
    </h1>

    <FormBase
      action="?/update"
      formType="edit"
      onSubmitValidation={formValidation}
      onFormSubmitted={onFormSubmitted}
    >
      <!-- Campos ocultos -->
      <InputHidden field="ulid_payment_status" ulid={paymentStatusData.ulid_payment_status ?? ""} />

      <!-- Campo nome -->
      <InputText 
        label="Status" 
        name="status" 
        value={paymentStatusData.status} 
        required={true}
      />

      <ControlButtons 
        saveLabel="Salvar" 
        cancelHref="/admin/register/payment/status"
      />
    </FormBase>
  </div>
</AdminLayout>