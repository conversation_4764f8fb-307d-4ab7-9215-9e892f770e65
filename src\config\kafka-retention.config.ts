/**
 * Configuração de retenção de mensagens para o Kafka
 * 
 * Este arquivo contém as configurações de retenção de mensagens para os tópicos Kafka,
 * incluindo políticas de retenção, períodos de retenção e estratégias de compactação.
 */

// Tipos de políticas de limpeza
export enum CleanupPolicy {
  DELETE = 'delete',
  COMPACT = 'compact',
  COMPACT_DELETE = 'compact,delete',
}

// Níveis de importância para retenção de dados
export enum DataImportance {
  CRITICAL = 'critical',   // Dados críticos para o negócio (ex: transações financeiras)
  HIGH = 'high',           // Dados importantes (ex: pedidos, usuários)
  MEDIUM = 'medium',       // Dados de importância média (ex: notificações)
  LOW = 'low',             // Dados de baixa importância (ex: analytics)
  TEMPORARY = 'temporary', // Dados temporários (ex: logs de debug)
}

// Interface para configuração de retenção
export interface RetentionConfig {
  retentionMs: number;     // Tempo de retenção em milissegundos
  cleanupPolicy: CleanupPolicy;
  segmentMs?: number;      // Tempo de segmento em milissegundos
  minCompactionLagMs?: number; // Tempo mínimo antes da compactação
  maxCompactionLagMs?: number; // Tempo máximo antes da compactação
  deleteRetentionMs?: number;  // Tempo de retenção após exclusão (para tópicos compactados)
  minCleanableDirtyRatio?: number; // Proporção mínima de dados "sujos" para acionar limpeza
  dataImportance: DataImportance;
  description: string;
}

// Configurações de retenção por domínio
const retentionConfigs: Record<string, RetentionConfig> = {
  // Domínio: Pagamentos
  'payment.transaction.created': {
    retentionMs: 365 * 24 * 60 * 60 * 1000, // 365 dias
    cleanupPolicy: CleanupPolicy.COMPACT_DELETE,
    segmentMs: 7 * 24 * 60 * 60 * 1000, // 7 dias
    minCompactionLagMs: 60 * 60 * 1000, // 1 hora
    deleteRetentionMs: 24 * 60 * 60 * 1000, // 24 horas
    minCleanableDirtyRatio: 0.5,
    dataImportance: DataImportance.CRITICAL,
    description: 'Eventos de criação de transações de pagamento',
  },
  'payment.transaction.updated': {
    retentionMs: 365 * 24 * 60 * 60 * 1000, // 365 dias
    cleanupPolicy: CleanupPolicy.COMPACT_DELETE,
    segmentMs: 7 * 24 * 60 * 60 * 1000, // 7 dias
    minCompactionLagMs: 60 * 60 * 1000, // 1 hora
    deleteRetentionMs: 24 * 60 * 60 * 1000, // 24 horas
    minCleanableDirtyRatio: 0.5,
    dataImportance: DataImportance.CRITICAL,
    description: 'Eventos de atualização de transações de pagamento',
  },
  'payment.transaction.failed': {
    retentionMs: 90 * 24 * 60 * 60 * 1000, // 90 dias
    cleanupPolicy: CleanupPolicy.COMPACT_DELETE,
    segmentMs: 7 * 24 * 60 * 60 * 1000, // 7 dias
    minCompactionLagMs: 60 * 60 * 1000, // 1 hora
    deleteRetentionMs: 24 * 60 * 60 * 1000, // 24 horas
    minCleanableDirtyRatio: 0.5,
    dataImportance: DataImportance.HIGH,
    description: 'Eventos de falha em transações de pagamento',
  },
  'payment.refund.requested': {
    retentionMs: 365 * 24 * 60 * 60 * 1000, // 365 dias
    cleanupPolicy: CleanupPolicy.COMPACT_DELETE,
    segmentMs: 7 * 24 * 60 * 60 * 1000, // 7 dias
    minCompactionLagMs: 60 * 60 * 1000, // 1 hora
    deleteRetentionMs: 24 * 60 * 60 * 1000, // 24 horas
    minCleanableDirtyRatio: 0.5,
    dataImportance: DataImportance.CRITICAL,
    description: 'Eventos de solicitação de reembolso',
  },
  'payment.refund.processed': {
    retentionMs: 365 * 24 * 60 * 60 * 1000, // 365 dias
    cleanupPolicy: CleanupPolicy.COMPACT_DELETE,
    segmentMs: 7 * 24 * 60 * 60 * 1000, // 7 dias
    minCompactionLagMs: 60 * 60 * 1000, // 1 hora
    deleteRetentionMs: 24 * 60 * 60 * 1000, // 24 horas
    minCleanableDirtyRatio: 0.5,
    dataImportance: DataImportance.CRITICAL,
    description: 'Eventos de processamento de reembolso',
  },
  'payment.webhook.received': {
    retentionMs: 30 * 24 * 60 * 60 * 1000, // 30 dias
    cleanupPolicy: CleanupPolicy.DELETE,
    segmentMs: 3 * 24 * 60 * 60 * 1000, // 3 dias
    dataImportance: DataImportance.MEDIUM,
    description: 'Eventos de recebimento de webhooks de pagamento',
  },
  
  // Domínio: Pedidos
  'order.created': {
    retentionMs: 365 * 24 * 60 * 60 * 1000, // 365 dias
    cleanupPolicy: CleanupPolicy.COMPACT_DELETE,
    segmentMs: 7 * 24 * 60 * 60 * 1000, // 7 dias
    minCompactionLagMs: 60 * 60 * 1000, // 1 hora
    deleteRetentionMs: 24 * 60 * 60 * 1000, // 24 horas
    minCleanableDirtyRatio: 0.5,
    dataImportance: DataImportance.HIGH,
    description: 'Eventos de criação de pedidos',
  },
  'order.updated': {
    retentionMs: 365 * 24 * 60 * 60 * 1000, // 365 dias
    cleanupPolicy: CleanupPolicy.COMPACT_DELETE,
    segmentMs: 7 * 24 * 60 * 60 * 1000, // 7 dias
    minCompactionLagMs: 60 * 60 * 1000, // 1 hora
    deleteRetentionMs: 24 * 60 * 60 * 1000, // 24 horas
    minCleanableDirtyRatio: 0.5,
    dataImportance: DataImportance.HIGH,
    description: 'Eventos de atualização de pedidos',
  },
  'order.status.changed': {
    retentionMs: 365 * 24 * 60 * 60 * 1000, // 365 dias
    cleanupPolicy: CleanupPolicy.COMPACT_DELETE,
    segmentMs: 7 * 24 * 60 * 60 * 1000, // 7 dias
    minCompactionLagMs: 60 * 60 * 1000, // 1 hora
    deleteRetentionMs: 24 * 60 * 60 * 1000, // 24 horas
    minCleanableDirtyRatio: 0.5,
    dataImportance: DataImportance.HIGH,
    description: 'Eventos de mudança de status de pedidos',
  },
  
  // Domínio: Usuários
  'user.registered': {
    retentionMs: 365 * 24 * 60 * 60 * 1000, // 365 dias
    cleanupPolicy: CleanupPolicy.COMPACT,
    segmentMs: 7 * 24 * 60 * 60 * 1000, // 7 dias
    minCompactionLagMs: 60 * 60 * 1000, // 1 hora
    deleteRetentionMs: 24 * 60 * 60 * 1000, // 24 horas
    minCleanableDirtyRatio: 0.5,
    dataImportance: DataImportance.HIGH,
    description: 'Eventos de registro de usuários',
  },
  'user.profile.updated': {
    retentionMs: 180 * 24 * 60 * 60 * 1000, // 180 dias
    cleanupPolicy: CleanupPolicy.COMPACT,
    segmentMs: 7 * 24 * 60 * 60 * 1000, // 7 dias
    minCompactionLagMs: 60 * 60 * 1000, // 1 hora
    deleteRetentionMs: 24 * 60 * 60 * 1000, // 24 horas
    minCleanableDirtyRatio: 0.5,
    dataImportance: DataImportance.MEDIUM,
    description: 'Eventos de atualização de perfil de usuários',
  },
  
  // Domínio: Notificações
  'notification.email.queued': {
    retentionMs: 7 * 24 * 60 * 60 * 1000, // 7 dias
    cleanupPolicy: CleanupPolicy.DELETE,
    segmentMs: 1 * 24 * 60 * 60 * 1000, // 1 dia
    dataImportance: DataImportance.MEDIUM,
    description: 'Eventos de emails enfileirados para envio',
  },
  'notification.email.sent': {
    retentionMs: 30 * 24 * 60 * 60 * 1000, // 30 dias
    cleanupPolicy: CleanupPolicy.DELETE,
    segmentMs: 3 * 24 * 60 * 60 * 1000, // 3 dias
    dataImportance: DataImportance.MEDIUM,
    description: 'Eventos de emails enviados',
  },
  'notification.email.failed': {
    retentionMs: 30 * 24 * 60 * 60 * 1000, // 30 dias
    cleanupPolicy: CleanupPolicy.DELETE,
    segmentMs: 3 * 24 * 60 * 60 * 1000, // 3 dias
    dataImportance: DataImportance.MEDIUM,
    description: 'Eventos de falha no envio de emails',
  },
  
  // Domínio: Analytics
  'analytics.page.viewed': {
    retentionMs: 90 * 24 * 60 * 60 * 1000, // 90 dias
    cleanupPolicy: CleanupPolicy.DELETE,
    segmentMs: 1 * 24 * 60 * 60 * 1000, // 1 dia
    dataImportance: DataImportance.LOW,
    description: 'Eventos de visualização de páginas',
  },
  'analytics.product.viewed': {
    retentionMs: 90 * 24 * 60 * 60 * 1000, // 90 dias
    cleanupPolicy: CleanupPolicy.DELETE,
    segmentMs: 1 * 24 * 60 * 60 * 1000, // 1 dia
    dataImportance: DataImportance.LOW,
    description: 'Eventos de visualização de produtos',
  },
};

// Configurações padrão por nível de importância
const defaultRetentionByImportance: Record<DataImportance, RetentionConfig> = {
  [DataImportance.CRITICAL]: {
    retentionMs: 365 * 24 * 60 * 60 * 1000, // 365 dias
    cleanupPolicy: CleanupPolicy.COMPACT_DELETE,
    segmentMs: 7 * 24 * 60 * 60 * 1000, // 7 dias
    minCompactionLagMs: 60 * 60 * 1000, // 1 hora
    deleteRetentionMs: 24 * 60 * 60 * 1000, // 24 horas
    minCleanableDirtyRatio: 0.5,
    dataImportance: DataImportance.CRITICAL,
    description: 'Configuração padrão para dados críticos',
  },
  [DataImportance.HIGH]: {
    retentionMs: 180 * 24 * 60 * 60 * 1000, // 180 dias
    cleanupPolicy: CleanupPolicy.COMPACT_DELETE,
    segmentMs: 7 * 24 * 60 * 60 * 1000, // 7 dias
    minCompactionLagMs: 60 * 60 * 1000, // 1 hora
    deleteRetentionMs: 24 * 60 * 60 * 1000, // 24 horas
    minCleanableDirtyRatio: 0.5,
    dataImportance: DataImportance.HIGH,
    description: 'Configuração padrão para dados importantes',
  },
  [DataImportance.MEDIUM]: {
    retentionMs: 90 * 24 * 60 * 60 * 1000, // 90 dias
    cleanupPolicy: CleanupPolicy.DELETE,
    segmentMs: 3 * 24 * 60 * 60 * 1000, // 3 dias
    dataImportance: DataImportance.MEDIUM,
    description: 'Configuração padrão para dados de importância média',
  },
  [DataImportance.LOW]: {
    retentionMs: 30 * 24 * 60 * 60 * 1000, // 30 dias
    cleanupPolicy: CleanupPolicy.DELETE,
    segmentMs: 1 * 24 * 60 * 60 * 1000, // 1 dia
    dataImportance: DataImportance.LOW,
    description: 'Configuração padrão para dados de baixa importância',
  },
  [DataImportance.TEMPORARY]: {
    retentionMs: 7 * 24 * 60 * 60 * 1000, // 7 dias
    cleanupPolicy: CleanupPolicy.DELETE,
    segmentMs: 1 * 24 * 60 * 60 * 1000, // 1 dia
    dataImportance: DataImportance.TEMPORARY,
    description: 'Configuração padrão para dados temporários',
  },
};

// Exportar configuração de retenção
export const kafkaRetentionConfig = {
  /**
   * Obtém a configuração de retenção para um tópico específico
   * @param topicName - Nome do tópico
   * @returns Configuração de retenção para o tópico
   */
  getTopicRetentionConfig(topicName: string): RetentionConfig | null {
    return retentionConfigs[topicName] || null;
  },

  /**
   * Obtém a configuração padrão para um nível de importância
   * @param importance - Nível de importância
   * @returns Configuração padrão para o nível de importância
   */
  getDefaultRetentionConfig(importance: DataImportance): RetentionConfig {
    return defaultRetentionByImportance[importance];
  },

  /**
   * Obtém todas as configurações de retenção
   * @returns Configurações de retenção para todos os tópicos
   */
  getAllRetentionConfigs(): Record<string, RetentionConfig> {
    return retentionConfigs;
  },
};
