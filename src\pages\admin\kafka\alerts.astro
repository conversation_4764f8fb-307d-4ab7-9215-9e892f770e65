---
import { queryHelper } from '@helpers/queryHelper';
import AdminLayout from '@layouts/AdminLayout.astro';
import { kafkaAlertService } from '@services/kafka-alerts.service';
import { formatDate } from '@utils/formatters';

// Obter parâmetros da query
const { status = 'all', severity = 'all', type = 'all', days = '7' } = Astro.url.searchParams;

// Obter alertas do banco de dados
const result = await queryHelper.query(
  `SELECT
    alert_id,
    type,
    severity,
    timestamp,
    message,
    details,
    status,
    resolved_at,
    acknowledged_by,
    acknowledged_at
  FROM
    tab_kafka_alerts
  WHERE
    timestamp > NOW() - INTERVAL '${days} days'
    ${status !== 'all' ? `AND status = '${status}'` : ''}
    ${severity !== 'all' ? `AND severity = '${severity}'` : ''}
    ${type !== 'all' ? `AND type = '${type}'` : ''}
  ORDER BY
    timestamp DESC`
);

const alerts = result.rows;

// Obter contagem de alertas por status
const statusCountResult = await queryHelper.query(
  `SELECT
    status,
    COUNT(*) as count
  FROM
    tab_kafka_alerts
  WHERE
    timestamp > NOW() - INTERVAL '${days} days'
  GROUP BY
    status`
);

const statusCounts = statusCountResult.rows.reduce((acc, row) => {
  acc[row.status] = Number.parseInt(row.count, 10);
  return acc;
}, {});

const totalAlerts = Object.values(statusCounts).reduce(
  (sum: number, count: number) => sum + count,
  0
);

// Obter contagem de alertas por severidade
const severityCountResult = await queryHelper.query(
  `SELECT
    severity,
    COUNT(*) as count
  FROM
    tab_kafka_alerts
  WHERE
    timestamp > NOW() - INTERVAL '${days} days'
  GROUP BY
    severity`
);

const severityCounts = severityCountResult.rows.reduce((acc, row) => {
  acc[row.severity] = Number.parseInt(row.count, 10);
  return acc;
}, {});

// Obter contagem de alertas por tipo
const typeCountResult = await queryHelper.query(
  `SELECT
    type,
    COUNT(*) as count
  FROM
    tab_kafka_alerts
  WHERE
    timestamp > NOW() - INTERVAL '${days} days'
  GROUP BY
    type`
);

const typeCounts = typeCountResult.rows.reduce((acc, row) => {
  acc[row.type] = Number.parseInt(row.count, 10);
  return acc;
}, {});

// Obter tipos de alertas únicos
const types = Object.keys(typeCounts).sort();

// Função para obter classe CSS com base na severidade
function getSeverityClass(severity: string): string {
  switch (severity.toLowerCase()) {
    case 'critical':
      return 'bg-red-100 text-red-800';
    case 'error':
      return 'bg-orange-100 text-orange-800';
    case 'warning':
      return 'bg-yellow-100 text-yellow-800';
    case 'info':
      return 'bg-blue-100 text-blue-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
}

// Função para obter classe CSS com base no status
function getStatusClass(status: string): string {
  switch (status.toLowerCase()) {
    case 'active':
      return 'bg-red-100 text-red-800';
    case 'acknowledged':
      return 'bg-yellow-100 text-yellow-800';
    case 'resolved':
      return 'bg-green-100 text-green-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
}

// Função para construir URL de filtro
function getFilterUrl(params: Record<string, string>): string {
  const url = new URL(Astro.url);

  for (const [key, value] of Object.entries(params)) {
    if (value === 'all') {
      url.searchParams.delete(key);
    } else {
      url.searchParams.set(key, value);
    }
  }

  return url.pathname + url.search;
}

// Função para verificar se um filtro está ativo
function isFilterActive(param: string, value: string): boolean {
  return Astro.url.searchParams.get(param) === value;
}
---

<AdminLayout title="Alertas Kafka">
  <div class="container mx-auto px-4 py-8">
    <h1 class="text-3xl font-bold mb-8">Alertas Kafka</h1>
    
    <div class="bg-white rounded-lg shadow overflow-hidden mb-8">
      <div class="p-6 border-b">
        <div class="flex items-center justify-between">
          <h2 class="text-xl font-semibold">Visão Geral</h2>
          <span class="text-lg font-medium">{totalAlerts} alertas nos últimos {days} dias</span>
        </div>
      </div>
      
      <div class="p-6">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <!-- Alertas ativos -->
          <div class="bg-red-50 rounded-lg p-4">
            <div class="text-red-800 text-sm font-medium mb-2">Alertas Ativos</div>
            <div class="text-2xl font-bold text-red-900">{statusCounts['active'] || 0}</div>
          </div>
          
          <!-- Alertas reconhecidos -->
          <div class="bg-yellow-50 rounded-lg p-4">
            <div class="text-yellow-800 text-sm font-medium mb-2">Alertas Reconhecidos</div>
            <div class="text-2xl font-bold text-yellow-900">{statusCounts['acknowledged'] || 0}</div>
          </div>
          
          <!-- Alertas resolvidos -->
          <div class="bg-green-50 rounded-lg p-4">
            <div class="text-green-800 text-sm font-medium mb-2">Alertas Resolvidos</div>
            <div class="text-2xl font-bold text-green-900">{statusCounts['resolved'] || 0}</div>
          </div>
          
          <!-- Alertas críticos -->
          <div class="bg-purple-50 rounded-lg p-4">
            <div class="text-purple-800 text-sm font-medium mb-2">Alertas Críticos</div>
            <div class="text-2xl font-bold text-purple-900">{severityCounts['critical'] || 0}</div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Filtros -->
    <div class="bg-white rounded-lg shadow overflow-hidden mb-8">
      <div class="p-4 bg-gray-50 border-b">
        <h2 class="text-lg font-semibold">Filtros</h2>
      </div>
      
      <div class="p-6">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
          <!-- Filtro de status -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Status</label>
            <div class="flex flex-wrap gap-2">
              <a href={getFilterUrl({ status: 'all' })} class={`px-3 py-1 rounded-full text-xs font-medium ${isFilterActive('status', 'all') || !Astro.url.searchParams.has('status') ? 'bg-blue-600 text-white' : 'bg-gray-100 text-gray-800'}`}>
                Todos
              </a>
              <a href={getFilterUrl({ status: 'active' })} class={`px-3 py-1 rounded-full text-xs font-medium ${isFilterActive('status', 'active') ? 'bg-blue-600 text-white' : 'bg-red-100 text-red-800'}`}>
                Ativos
              </a>
              <a href={getFilterUrl({ status: 'acknowledged' })} class={`px-3 py-1 rounded-full text-xs font-medium ${isFilterActive('status', 'acknowledged') ? 'bg-blue-600 text-white' : 'bg-yellow-100 text-yellow-800'}`}>
                Reconhecidos
              </a>
              <a href={getFilterUrl({ status: 'resolved' })} class={`px-3 py-1 rounded-full text-xs font-medium ${isFilterActive('status', 'resolved') ? 'bg-blue-600 text-white' : 'bg-green-100 text-green-800'}`}>
                Resolvidos
              </a>
            </div>
          </div>
          
          <!-- Filtro de severidade -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Severidade</label>
            <div class="flex flex-wrap gap-2">
              <a href={getFilterUrl({ severity: 'all' })} class={`px-3 py-1 rounded-full text-xs font-medium ${isFilterActive('severity', 'all') || !Astro.url.searchParams.has('severity') ? 'bg-blue-600 text-white' : 'bg-gray-100 text-gray-800'}`}>
                Todas
              </a>
              <a href={getFilterUrl({ severity: 'critical' })} class={`px-3 py-1 rounded-full text-xs font-medium ${isFilterActive('severity', 'critical') ? 'bg-blue-600 text-white' : 'bg-red-100 text-red-800'}`}>
                Crítica
              </a>
              <a href={getFilterUrl({ severity: 'error' })} class={`px-3 py-1 rounded-full text-xs font-medium ${isFilterActive('severity', 'error') ? 'bg-blue-600 text-white' : 'bg-orange-100 text-orange-800'}`}>
                Erro
              </a>
              <a href={getFilterUrl({ severity: 'warning' })} class={`px-3 py-1 rounded-full text-xs font-medium ${isFilterActive('severity', 'warning') ? 'bg-blue-600 text-white' : 'bg-yellow-100 text-yellow-800'}`}>
                Aviso
              </a>
              <a href={getFilterUrl({ severity: 'info' })} class={`px-3 py-1 rounded-full text-xs font-medium ${isFilterActive('severity', 'info') ? 'bg-blue-600 text-white' : 'bg-blue-100 text-blue-800'}`}>
                Info
              </a>
            </div>
          </div>
          
          <!-- Filtro de tipo -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Tipo</label>
            <select
              id="type-filter"
              class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
              onchange="window.location.href = this.value"
            >
              <option value={getFilterUrl({ type: 'all' })} selected={type === 'all' || !Astro.url.searchParams.has('type')}>
                Todos os tipos
              </option>
              {types.map((t) => (
                <option value={getFilterUrl({ type: t })} selected={type === t}>
                  {t} ({typeCounts[t] || 0})
                </option>
              ))}
            </select>
          </div>
          
          <!-- Filtro de período -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Período</label>
            <select
              id="days-filter"
              class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
              onchange="window.location.href = this.value"
            >
              <option value={getFilterUrl({ days: '1' })} selected={days === '1'}>
                Último dia
              </option>
              <option value={getFilterUrl({ days: '7' })} selected={days === '7' || !Astro.url.searchParams.has('days')}>
                Últimos 7 dias
              </option>
              <option value={getFilterUrl({ days: '30' })} selected={days === '30'}>
                Últimos 30 dias
              </option>
              <option value={getFilterUrl({ days: '90' })} selected={days === '90'}>
                Últimos 90 dias
              </option>
            </select>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Lista de alertas -->
    <div class="bg-white rounded-lg shadow overflow-hidden">
      {alerts.length === 0 ? (
        <div class="p-8 text-center text-gray-500">
          Nenhum alerta encontrado com os filtros selecionados.
        </div>
      ) : (
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Timestamp</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Severidade</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tipo</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Mensagem</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ações</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              {alerts.map((alert) => (
                <tr>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {formatDate(new Date(alert.timestamp))}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span class={`px-2 py-1 rounded-full text-xs font-medium ${getSeverityClass(alert.severity)}`}>
                      {alert.severity}
                    </span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {alert.type}
                  </td>
                  <td class="px-6 py-4 text-sm text-gray-900">
                    {alert.message}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span class={`px-2 py-1 rounded-full text-xs font-medium ${getStatusClass(alert.status)}`}>
                      {alert.status}
                    </span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm">
                    <a href={`/admin/kafka/alert/${alert.alert_id}`} class="text-blue-600 hover:text-blue-900 mr-3">
                      Detalhes
                    </a>
                    {alert.status === 'active' && (
                      <button
                        data-alert-id={alert.alert_id}
                        class="acknowledge-btn text-yellow-600 hover:text-yellow-900"
                      >
                        Reconhecer
                      </button>
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
    
    <div class="mt-8 flex justify-between items-center">
      <a href="/admin/kafka/dashboard" class="text-blue-600 hover:text-blue-800">
        &larr; Voltar para Dashboard
      </a>
      
      <a href={Astro.url.pathname + Astro.url.search} class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded">
        Atualizar Lista
      </a>
    </div>
  </div>
  
  <script>
    // Função para reconhecer alerta
    document.querySelectorAll('.acknowledge-btn').forEach(button => {
      button.addEventListener('click', async () => {
        const alertId = button.getAttribute('data-alert-id');
        
        if (!alertId) return;
        
        try {
          const response = await fetch(`/api/kafka/alerts/${alertId}/acknowledge`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              acknowledgedBy: 'admin', // Em um sistema real, seria o usuário logado
            }),
          });
          
          if (response.ok) {
            // Recarregar a página para mostrar o status atualizado
            window.location.reload();
          } else {
            alert('Erro ao reconhecer alerta');
          }
        } catch (error) {
          console.error('Erro ao reconhecer alerta:', error);
          alert('Erro ao reconhecer alerta');
        }
      });
    });
  </script>
</AdminLayout>
---
