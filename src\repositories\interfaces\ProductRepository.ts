/**
 * Interface de Repositório de Produtos
 *
 * Esta interface define os métodos para acesso e manipulação de produtos
 * no sistema, seguindo o princípio de inversão de dependência.
 */

import { Product, ProductCategory } from '../../domain/entities/Product';

/**
 * Opções de filtro para busca de produtos
 */
export interface ProductFilterOptions {
  category?: ProductCategory;
  featured?: boolean;
  active?: boolean;
  search?: string;
  minPrice?: number;
  maxPrice?: number;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

/**
 * Resultado paginado de produtos
 */
export interface PaginatedProducts {
  items: Product[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

/**
 * Interface do Repositório de Produtos
 */
export interface ProductRepository {
  /**
   * Busca um produto pelo ID
   *
   * @param id ID do produto
   * @returns Produto encontrado ou null se não existir
   */
  findById(id: string): Promise<Product | null>;

  /**
   * Busca produtos com filtros
   *
   * @param options Opções de filtro
   * @returns Resultado paginado de produtos
   */
  findAll(options?: ProductFilterOptions): Promise<PaginatedProducts>;

  /**
   * Busca produtos por categoria
   *
   * @param category Categoria para filtro
   * @param options Opções de filtro adicionais
   * @returns Resultado paginado de produtos
   */
  findByCategory(
    category: ProductCategory,
    options?: Omit<ProductFilterOptions, 'category'>
  ): Promise<PaginatedProducts>;

  /**
   * Busca produtos em destaque
   *
   * @param options Opções de filtro adicionais
   * @returns Resultado paginado de produtos
   */
  findFeatured(options?: Omit<ProductFilterOptions, 'featured'>): Promise<PaginatedProducts>;

  /**
   * Busca produtos relacionados a um produto específico
   *
   * @param productId ID do produto de referência
   * @param limit Limite de produtos a serem retornados
   * @returns Lista de produtos relacionados
   */
  findRelated(productId: string, limit?: number): Promise<Product[]>;

  /**
   * Busca produtos recentes
   *
   * @param limit Limite de produtos a serem retornados
   * @returns Lista de produtos recentes
   */
  findRecent(limit?: number): Promise<Product[]>;

  /**
   * Salva um produto (cria ou atualiza)
   *
   * @param product Produto a ser salvo
   * @returns Produto salvo
   */
  save(product: Product): Promise<Product>;

  /**
   * Remove um produto
   *
   * @param id ID do produto a ser removido
   * @returns true se removido com sucesso, false caso contrário
   */
  remove(id: string): Promise<boolean>;

  /**
   * Atualiza o estoque de um produto
   *
   * @param id ID do produto
   * @param quantity Nova quantidade em estoque
   * @returns Produto atualizado
   */
  updateStock(id: string, quantity: number): Promise<Product>;
}
