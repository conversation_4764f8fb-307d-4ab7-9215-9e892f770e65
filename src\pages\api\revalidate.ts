/**
 * API de Revalidação de Conteúdo
 *
 * Este endpoint permite a revalidação programada de conteúdo em cache.
 * Pode ser chamado por sistemas externos ou webhooks para invalidar o cache
 * quando o conteúdo é atualizado.
 */

import type { APIRoute } from 'astro';
import { deleteCache } from '../../infrastructure/cache/CacheService';

// Configuração da API de revalidação
const REVALIDATION_CONFIG = {
  // Chave secreta para revalidação (deve ser configurada como variável de ambiente em produção)
  secretKey: import.meta.env.REVALIDATION_SECRET_KEY || 'estacao-alfabetizacao-secret',

  // Prefixos de cache para revalidação
  cachePrefixes: ['odr:', 'odr:content:', 'odr:search:'],

  // Mapeamento de tipos de conteúdo para padrões de cache
  contentTypePatterns: {
    atividade: 'odr:content:/conteudos/',
    jogo: 'odr:content:/conteudos/',
    material: 'odr:content:/conteudos/',
    video: 'odr:content:/conteudos/',
    audio: 'odr:content:/conteudos/',
    all: 'odr:',
  },
};

/**
 * Verifica se a chave secreta é válida
 * @param secret Chave secreta fornecida
 * @returns Verdadeiro se a chave secreta é válida
 */
function isValidSecretKey(secret: string | null): boolean {
  return secret === REVALIDATION_CONFIG.secretKey;
}

/**
 * Gera um padrão de chave de cache para revalidação
 * @param path Caminho opcional
 * @param contentId ID do conteúdo opcional
 * @param contentType Tipo de conteúdo opcional
 * @returns Padrão de chave de cache
 */
function generateCacheKeyPattern(path?: string, contentId?: string, contentType?: string): string {
  // Se um caminho específico for fornecido
  if (path) {
    // Remover parâmetros da URL
    const cleanPath = path.split('?')[0];
    return `odr:${cleanPath}`;
  }

  // Se um ID de conteúdo específico for fornecido
  if (contentId) {
    return `odr:content:/conteudos/${contentId}`;
  }

  // Se um tipo de conteúdo for fornecido
  if (contentType && contentType in REVALIDATION_CONFIG.contentTypePatterns) {
    return REVALIDATION_CONFIG.contentTypePatterns[
      contentType as keyof typeof REVALIDATION_CONFIG.contentTypePatterns
    ];
  }

  // Padrão padrão para revalidar todo o cache
  return REVALIDATION_CONFIG.contentTypePatterns.all;
}

/**
 * Revalida o conteúdo em cache
 * @param keyPattern Padrão de chave de cache
 * @returns Número de chaves revalidadas
 */
async function revalidateCache(keyPattern: string): Promise<number> {
  try {
    // Em uma implementação real, isso usaria algo como KEYS ou SCAN do Redis
    // para encontrar todas as chaves que correspondem ao padrão

    // Para simplificar, vamos apenas excluir a chave exata
    await deleteCache(keyPattern);

    // Retornar 1 para indicar que uma chave foi revalidada
    return 1;
  } catch (error) {
    console.error('Erro ao revalidar cache:', error);
    return 0;
  }
}

/**
 * Endpoint de revalidação
 */
export const POST: APIRoute = async ({ request }) => {
  try {
    // Verificar método
    if (request.method !== 'POST') {
      return new Response(
        JSON.stringify({
          success: false,
          message: 'Método não permitido. Use POST.',
        }),
        {
          status: 405,
          headers: {
            'Content-Type': 'application/json',
            Allow: 'POST',
          },
        }
      );
    }

    // Obter dados da requisição
    const data = await request.json();

    // Verificar chave secreta
    const secret = data.secret || null;
    if (!isValidSecretKey(secret)) {
      return new Response(
        JSON.stringify({
          success: false,
          message: 'Chave secreta inválida.',
        }),
        {
          status: 401,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    // Obter parâmetros de revalidação
    const path = data.path || null;
    const contentId = data.contentId || null;
    const contentType = data.contentType || null;

    // Gerar padrão de chave de cache
    const keyPattern = generateCacheKeyPattern(path, contentId, contentType);

    // Revalidar o cache
    const revalidatedCount = await revalidateCache(keyPattern);

    // Retornar resposta de sucesso
    return new Response(
      JSON.stringify({
        success: true,
        message: `Cache revalidado com sucesso. ${revalidatedCount} chaves afetadas.`,
        pattern: keyPattern,
        timestamp: new Date().toISOString(),
      }),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  } catch (error) {
    // Retornar resposta de erro
    return new Response(
      JSON.stringify({
        success: false,
        message: 'Erro ao processar requisição de revalidação.',
        error: error instanceof Error ? error.message : String(error),
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  }
};
