---
/**
 * Página de detalhes e edição de papel (role)
 *
 * Esta página permite visualizar e editar detalhes de um papel,
 * incluindo suas permissões associadas.
 */

import InheritedPermissionsViewer from '@components/admin/InheritedPermissionsViewer.astro';
import Notification from '@components/admin/Notification.astro';
import RoleHierarchyManager from '@components/admin/RoleHierarchyManager.astro';
import PermissionGate from '@components/auth/PermissionGate.astro';
import PolicyGate from '@components/auth/PolicyGate.astro';
// Importações
import MainLayout from '@layouts/MainLayout.astro';
import { permissionRepository } from '@repository/permissionRepository';
import { resourceRepository } from '@repository/resourceRepository';
import { roleRepository } from '@repository/roleRepository';
import { authorizationService } from '@services/authorizationService';
import { getCurrentUser } from '@utils/authUtils';

// Obter parâmetros da rota
const { id } = Astro.params;

// Verificar autenticação
const user = await getCurrentUser(Astro.cookies);

// Redirecionar se não estiver autenticado
if (!user) {
  return Astro.redirect(`/signin?redirect=/admin/roles/${id}`);
}

// Verificar se o ID foi fornecido
if (!id) {
  return Astro.redirect('/admin/permissions');
}

// Buscar dados do papel
const roleResult = await roleRepository.read(id);

// Verificar se o papel existe
if (roleResult.rowCount === 0) {
  return Astro.redirect('/admin/permissions?error=role-not-found');
}

const role = roleResult.rows[0];

// Buscar permissões do papel
const rolePermissions = await permissionRepository.getRolePermissions(id);

// Buscar permissões herdadas
const inheritedPermissions = await authorizationService.getRolePermissionsWithInheritance(id);

// Buscar todos os recursos com permissões
const resourcesWithPermissions = await resourceRepository.getResourcesWithPermissions();

// Obter parâmetros de consulta para mensagens
const success = Astro.url.searchParams.get('success');
const error = Astro.url.searchParams.get('error');

// Agrupar permissões por recurso
const permissionsByResource = resourcesWithPermissions.rows.reduce(
  (acc, row) => {
    const resourceId = row.ulid_resource;
    const resourceName = row.name;

    if (!acc[resourceId]) {
      acc[resourceId] = {
        id: resourceId,
        name: resourceName,
        description: row.description,
        permissions: [],
      };
    }

    // Verificar se o papel já tem esta permissão
    const hasPermission = rolePermissions.rows.some(
      (p) => p.ulid_permission === row.ulid_permission
    );

    acc[resourceId].permissions.push({
      id: row.ulid_permission,
      resourcePermissionId: row.ulid_resource_permission,
      name: row.permission_name,
      action: row.action,
      hasPermission,
    });

    return acc;
  },
  {} as Record<string, any>
);

// Buscar papéis pais e filhos
const parentRoles = await roleRepository.getParentRoles(id);
const childRoles = await roleRepository.getChildRoles(id);

// Buscar usuários com este papel
const usersWithRole = await roleRepository.getUsersWithRole(id);

// Título da página
const title = `Papel: ${role.name}`;

// Verificar se há mensagens de sucesso ou erro nos parâmetros de consulta
let successMessage = '';
let errorMessage = '';

if (success) {
  switch (success) {
    case 'updated':
      successMessage = 'Papel atualizado com sucesso.';
      break;
    case 'permissions-updated':
      successMessage = 'Permissões atualizadas com sucesso.';
      break;
    case 'parent-added':
      successMessage = 'Papel superior adicionado com sucesso.';
      break;
    case 'child-added':
      successMessage = 'Papel subordinado adicionado com sucesso.';
      break;
    case 'parent-removed':
      successMessage = 'Papel superior removido com sucesso.';
      break;
    case 'child-removed':
      successMessage = 'Papel subordinado removido com sucesso.';
      break;
    case 'created':
      successMessage = 'Papel criado com sucesso.';
      break;
  }
}

if (error) {
  switch (error) {
    case 'update-failed':
      errorMessage = 'Erro ao atualizar papel.';
      break;
    case 'hierarchy-cycle':
      errorMessage = 'Não foi possível adicionar relação de hierarquia pois criaria um ciclo.';
      break;
  }
}

// Processar formulário
if (Astro.request.method === 'POST') {
  try {
    const formData = await Astro.request.formData();
    const action = formData.get('action') as string;

    if (action === 'update-role') {
      // Atualizar informações básicas do papel
      const name = formData.get('name') as string;
      const description = formData.get('description') as string;
      const active = formData.get('active') === 'true';

      if (name) {
        await roleRepository.update(id, name, description, active);
        return Astro.redirect(`/admin/roles/${id}?success=updated`);
      }
    } else if (action === 'update-permissions') {
      // Atualizar permissões do papel
      const permissionIds = formData.getAll('permission') as string[];

      // Remover todas as permissões atuais
      // (Na prática, seria melhor fazer um diff e atualizar apenas o necessário)
      for (const row of resourcesWithPermissions.rows) {
        await resourceRepository.dissociateFromRole(id, row.ulid_resource_permission);
      }

      // Adicionar novas permissões
      for (const permissionId of permissionIds) {
        const [resourceId, resourcePermissionId] = permissionId.split(':');
        await resourceRepository.associateWithRole(id, resourcePermissionId);
      }

      return Astro.redirect(`/admin/roles/${id}?success=permissions-updated`);
    }
  } catch (error) {
    console.error('Erro ao processar formulário:', error);
    return Astro.redirect(`/admin/roles/${id}?error=update-failed`);
  }
}
---

<MainLayout title={title}>
  <main class="container mx-auto px-4 py-8">
    <PermissionGate resource="roles" action="read">
      <div class="flex items-center justify-between mb-6">
        <h1 class="text-3xl font-bold">{title}</h1>

        <div class="flex space-x-2">
          <a
            href="/admin/permissions"
            class="bg-gray-200 text-gray-800 px-4 py-2 rounded hover:bg-gray-300 transition"
          >
            Voltar
          </a>

          <PolicyGate
            resource="roles"
            action="delete"
            metadata={{ isSystem: role.is_system }}
            showFallback={false}
          >
            <button
              type="button"
              class="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700 transition"
              data-role-id={id}
              id="delete-role-btn"
            >
              Excluir
            </button>
          </PolicyGate>
        </div>
      </div>

      {successMessage && (
        <div class="mb-6">
          <Notification
            type="success"
            message={successMessage}
          />
        </div>
      )}

      {errorMessage && (
        <div class="mb-6">
          <Notification
            type="error"
            message={errorMessage}
          />
        </div>
      )}

      <div class="grid grid-cols-1 md:grid-cols-12 gap-6">
        <!-- Informações do papel -->
        <div class="md:col-span-4">
          <div class="bg-white rounded-lg shadow p-6">
            <h2 class="text-xl font-semibold mb-4">Informações do Papel</h2>

            <PolicyGate
              resource="roles"
              action="update"
              metadata={{ isSystem: role.is_system }}
            >
              <form method="POST" class="space-y-4">
                <input type="hidden" name="action" value="update-role">

                <div>
                  <label for="name" class="block text-sm font-medium text-gray-700 mb-1">Nome</label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    value={role.name}
                    class="w-full px-3 py-2 border border-gray-300 rounded-md"
                    required
                    disabled={role.is_system}
                  >
                </div>

                <div>
                  <label for="description" class="block text-sm font-medium text-gray-700 mb-1">Descrição</label>
                  <textarea
                    id="description"
                    name="description"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md"
                    rows="3"
                    disabled={role.is_system}
                  >{role.description}</textarea>
                </div>

                <div>
                  <label class="flex items-center">
                    <input
                      type="checkbox"
                      name="active"
                      value="true"
                      checked={role.active}
                      class="h-4 w-4 text-blue-600 border-gray-300 rounded"
                      disabled={role.is_system}
                    >
                    <span class="ml-2 text-sm text-gray-700">Ativo</span>
                  </label>
                </div>

                <div class="pt-2">
                  <button
                    type="submit"
                    class="w-full bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition"
                    disabled={role.is_system}
                  >
                    Salvar Alterações
                  </button>

                  {role.is_system && (
                    <p class="mt-2 text-sm text-gray-500">
                      Este é um papel do sistema e não pode ser modificado.
                    </p>
                  )}
                </div>
              </form>

              <slot name="fallback">
                <div class="space-y-4">
                  <div>
                    <h3 class="text-sm font-medium text-gray-700 mb-1">Nome</h3>
                    <p class="px-3 py-2 bg-gray-50 rounded-md">{role.name}</p>
                  </div>

                  <div>
                    <h3 class="text-sm font-medium text-gray-700 mb-1">Descrição</h3>
                    <p class="px-3 py-2 bg-gray-50 rounded-md">{role.description || 'Sem descrição'}</p>
                  </div>

                  <div>
                    <h3 class="text-sm font-medium text-gray-700 mb-1">Status</h3>
                    <p class="px-3 py-2 bg-gray-50 rounded-md">
                      {role.active ? 'Ativo' : 'Inativo'}
                    </p>
                  </div>

                  {role.is_system && (
                    <p class="mt-2 text-sm text-gray-500">
                      Este é um papel do sistema e não pode ser modificado.
                    </p>
                  )}
                </div>
              </slot>
            </PolicyGate>

            <div class="mt-6">
              <h3 class="text-lg font-medium mb-2">Detalhes</h3>

              <div class="space-y-2">
                <p class="text-sm">
                  <span class="font-medium">Criado em:</span>
                  {new Date(role.created_at).toLocaleString()}
                </p>

                <p class="text-sm">
                  <span class="font-medium">Atualizado em:</span>
                  {new Date(role.updated_at).toLocaleString()}
                </p>

                <p class="text-sm">
                  <span class="font-medium">Tipo:</span>
                  {role.is_system ? 'Sistema' : 'Personalizado'}
                </p>
              </div>
            </div>
          </div>

          <!-- Usuários com este papel -->
          <div class="mt-6 bg-white rounded-lg shadow p-6">
            <h2 class="text-xl font-semibold mb-4">Usuários ({usersWithRole.rows.length})</h2>

            {usersWithRole.rows.length > 0 ? (
              <ul class="divide-y">
                {usersWithRole.rows.map((user) => (
                  <li class="py-2">
                    <div class="flex items-center justify-between">
                      <div>
                        <p class="font-medium">{user.name}</p>
                        <p class="text-sm text-gray-600">{user.email}</p>
                      </div>

                      <PolicyGate resource="roles" action="revoke">
                        <button
                          type="button"
                          class="text-red-600 hover:text-red-800"
                          data-user-id={user.ulid_user}
                          data-role-id={id}
                        >
                          Remover
                        </button>
                      </PolicyGate>
                    </div>
                  </li>
                ))}
              </ul>
            ) : (
              <p class="text-gray-500">Nenhum usuário com este papel.</p>
            )}

            <PolicyGate resource="roles" action="assign">
              <div class="mt-4">
                <a
                  href={`/admin/roles/${id}/assign-users`}
                  class="text-blue-600 hover:underline"
                >
                  Atribuir a usuários
                </a>
              </div>
            </PolicyGate>
          </div>
        </div>

        <!-- Conteúdo principal -->
        <div class="md:col-span-8">
          <!-- Abas de navegação -->
          <div class="mb-6 border-b">
            <nav class="-mb-px flex space-x-8" aria-label="Tabs">
              <a href="#permissions" class="tab-link border-blue-500 text-blue-600 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm" data-target="permissions-tab">
                Permissões Diretas
              </a>
              <a href="#inherited" class="tab-link border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm" data-target="inherited-tab">
                Permissões Herdadas
              </a>
              <a href="#hierarchy" class="tab-link border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm" data-target="hierarchy-tab">
                Hierarquia
              </a>
            </nav>
          </div>

          <!-- Conteúdo das abas -->
          <div class="tab-content">
            <!-- Aba de permissões diretas -->
            <div id="permissions-tab" class="tab-pane">
              <div class="bg-white rounded-lg shadow p-6">
                <h2 class="text-xl font-semibold mb-4">Permissões Diretas</h2>

                <PolicyGate resource="roles" action="update">
                  <form method="POST" class="space-y-6">
                    <input type="hidden" name="action" value="update-permissions">

                    {Object.values(permissionsByResource).map((resource: any) => (
                      <div class="border rounded-lg p-4">
                        <h3 class="text-lg font-medium mb-2">{resource.name}</h3>
                        <p class="text-gray-600 mb-4">{resource.description}</p>

                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                          {resource.permissions.map((permission: any) => (
                            <label class="flex items-start p-2 bg-gray-50 rounded">
                              <input
                                type="checkbox"
                                name="permission"
                                value={`${resource.id}:${permission.resourcePermissionId}`}
                                checked={permission.hasPermission}
                                class="h-4 w-4 mt-1 text-blue-600 border-gray-300 rounded"
                                disabled={role.is_system}
                              >
                              <div class="ml-2">
                                <div class="font-medium">{permission.name}</div>
                                <div class="text-xs text-gray-500">{permission.action}</div>
                              </div>
                            </label>
                          ))}
                        </div>
                      </div>
                    ))}

                    <div class="pt-2">
                      <button
                        type="submit"
                        class="bg-blue-600 text-white px-6 py-2 rounded hover:bg-blue-700 transition"
                        disabled={role.is_system}
                      >
                        Salvar Permissões
                      </button>

                      {role.is_system && (
                        <p class="mt-2 text-sm text-gray-500">
                          Este é um papel do sistema e suas permissões não podem ser modificadas.
                        </p>
                      )}
                    </div>
                  </form>

                  <slot name="fallback">
                    <div class="space-y-6">
                      {Object.values(permissionsByResource).map((resource: any) => {
                        // Filtrar apenas permissões que o papel possui
                        const rolePermissionsForResource = resource.permissions.filter(
                          (p: any) => p.hasPermission
                        );

                        if (rolePermissionsForResource.length === 0) {
                          return null;
                        }

                        return (
                          <div class="border rounded-lg p-4">
                            <h3 class="text-lg font-medium mb-2">{resource.name}</h3>
                            <p class="text-gray-600 mb-4">{resource.description}</p>

                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                              {rolePermissionsForResource.map((permission: any) => (
                                <div class="p-2 bg-gray-50 rounded">
                                  <div class="font-medium">{permission.name}</div>
                                  <div class="text-xs text-gray-500">{permission.action}</div>
                                </div>
                              ))}
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </slot>
                </PolicyGate>
              </div>
            </div>

            <!-- Aba de permissões herdadas -->
            <div id="inherited-tab" class="tab-pane hidden">
              <div class="bg-white rounded-lg shadow p-6">
                <InheritedPermissionsViewer roleId={id} />
              </div>
            </div>

            <!-- Aba de hierarquia -->
            <div id="hierarchy-tab" class="tab-pane hidden">
              <div class="bg-white rounded-lg shadow p-6">
                <RoleHierarchyManager roleId={id} />
              </div>
            </div>
          </div>
        </div>
      </div>

      <slot name="fallback">
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mt-6">
          <p>Você não tem permissão para acessar esta página.</p>
          <p class="mt-2">
            <a href="/" class="text-red-700 font-medium hover:underline">Voltar para a página inicial</a>
          </p>
        </div>
      </slot>
    </PermissionGate>
  </main>
</MainLayout>

<script>
  // Script para interatividade no cliente
  document.addEventListener('DOMContentLoaded', () => {
    // Botão de exclusão de papel
    const deleteBtn = document.getElementById('delete-role-btn');

    if (deleteBtn) {
      deleteBtn.addEventListener('click', async () => {
        if (confirm('Tem certeza que deseja excluir este papel? Esta ação não pode ser desfeita.')) {
          const roleId = deleteBtn.getAttribute('data-role-id');

          try {
            const response = await fetch(`/api/rbac/roles/${roleId}`, {
              method: 'DELETE',
              headers: {
                'Content-Type': 'application/json'
              }
            });

            const data = await response.json();

            if (data.success) {
              window.location.href = '/admin/permissions?success=role-deleted';
            } else {
              alert(`Erro ao excluir papel: ${data.error}`);
            }
          } catch (error) {
            alert('Erro ao excluir papel. Tente novamente.');
            console.error(error);
          }
        }
      });
    }

    // Botões de remoção de usuário do papel
    const removeUserBtns = document.querySelectorAll('[data-user-id]');

    removeUserBtns.forEach(btn => {
      btn.addEventListener('click', async () => {
        if (confirm('Remover este usuário do papel?')) {
          const userId = btn.getAttribute('data-user-id');
          const roleId = btn.getAttribute('data-role-id');

          try {
            const response = await fetch(`/api/rbac/roles/${roleId}/users/${userId}`, {
              method: 'DELETE',
              headers: {
                'Content-Type': 'application/json'
              }
            });

            const data = await response.json();

            if (data.success) {
              // Recarregar a página para atualizar a lista
              window.location.reload();
            } else {
              alert(`Erro ao remover usuário: ${data.error}`);
            }
          } catch (error) {
            alert('Erro ao remover usuário. Tente novamente.');
            console.error(error);
          }
        }
      });
    });

    // Navegação por abas
    const tabLinks = document.querySelectorAll('.tab-link');
    const tabPanes = document.querySelectorAll('.tab-pane');

    // Função para ativar uma aba
    const activateTab = (targetId) => {
      // Desativar todas as abas
      tabLinks.forEach(link => {
        link.classList.remove('border-blue-500', 'text-blue-600');
        link.classList.add('border-transparent', 'text-gray-500');
      });

      tabPanes.forEach(pane => {
        pane.classList.add('hidden');
      });

      // Ativar a aba selecionada
      const selectedLink = document.querySelector(`[data-target="${targetId}"]`);
      if (selectedLink) {
        selectedLink.classList.remove('border-transparent', 'text-gray-500');
        selectedLink.classList.add('border-blue-500', 'text-blue-600');
      }

      const selectedPane = document.getElementById(targetId);
      if (selectedPane) {
        selectedPane.classList.remove('hidden');
      }
    };

    // Adicionar evento de clique às abas
    tabLinks.forEach(link => {
      link.addEventListener('click', (e) => {
        e.preventDefault();
        const targetId = link.getAttribute('data-target');
        activateTab(targetId);

        // Atualizar URL com hash
        const hash = link.getAttribute('href');
        history.pushState(null, null, hash);
      });
    });

    // Verificar hash na URL para ativar a aba correspondente
    const hash = window.location.hash;
    if (hash) {
      const targetLink = document.querySelector(`[href="${hash}"]`);
      if (targetLink) {
        const targetId = targetLink.getAttribute('data-target');
        activateTab(targetId);
      }
    }
  });
</script>

<style>
  /* Estilos específicos da página */
</style>
