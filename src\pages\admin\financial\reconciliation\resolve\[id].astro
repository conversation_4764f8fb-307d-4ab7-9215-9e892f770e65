---
import FinancialNav from '@components/navigation/FinancialNav.astro';
import { queryHelper } from '@db/queryHelper';
import { checkAdmin } from '@helpers/authGuard';
import { csrfHelper } from '@helpers/csrfHelper';
import AdminLayout from '@layouts/AdminLayout.astro';
import { reconciliationService } from '@services/reconciliationService';

// Protege a rota verificando se o usuário é admin
const authRedirect = await checkAdmin(Astro);
if (authRedirect) return authRedirect;

// Obter ID da discrepância
const { id } = Astro.params;

// Buscar detalhes da discrepância
const discrepancy = await queryHelper.queryOne(
  'SELECT * FROM tab_reconciliation_discrepancy WHERE ulid_discrepancy = $1',
  [id]
);

// Verificar se a discrepância existe
if (!discrepancy) {
  return Astro.redirect('/admin/financial/reconciliation?error=not_found');
}

// Se a discrepância já foi resolvida, redirecionar
if (discrepancy.resolved) {
  return Astro.redirect('/admin/financial/reconciliation?error=already_resolved');
}

// Buscar detalhes do pagamento, se disponível
let payment = null;
if (discrepancy.ulid_payment) {
  payment = await queryHelper.queryOne(
    `SELECT p.*, pt.type as payment_type, s.status as status_name
     FROM tab_payment p
     JOIN tab_payment_type pt ON p.ulid_payment_type = pt.ulid_payment_type
     JOIN tab_status s ON p.cod_status = s.cod_status
     WHERE p.ulid_payment = $1`,
    [discrepancy.ulid_payment]
  );
}

// Buscar detalhes do pedido, se disponível
let order = null;
if (discrepancy.ulid_order) {
  order = await queryHelper.queryOne(
    `SELECT o.*, u.name as user_name, u.email as user_email
     FROM tab_order o
     JOIN tab_user u ON o.ulid_user = u.ulid_user
     WHERE o.ulid_order = $1`,
    [discrepancy.ulid_order]
  );
}

// Gerar token CSRF
const csrfToken = await csrfHelper.generateToken(Astro);

// Processar formulário
if (Astro.request.method === 'POST') {
  try {
    const formData = await Astro.request.formData();

    // Validar token CSRF
    const token = formData.get('csrf_token');
    const isValidToken = await csrfHelper.validateToken(Astro, token);

    if (!isValidToken) {
      return Astro.redirect(`/admin/financial/reconciliation/resolve/${id}?error=invalid_token`);
    }

    // Obter dados do formulário
    const resolutionNotes = formData.get('resolution_notes')?.toString() || '';
    const action = formData.get('action')?.toString() || '';

    if (action === 'resolve') {
      // Resolver discrepância
      await reconciliationService.resolveDiscrepancy(id, resolutionNotes);

      // Redirecionar para a página de reconciliação
      return Astro.redirect('/admin/financial/reconciliation?success=true');
    }
  } catch (error) {
    console.error('Erro ao resolver discrepância:', error);
  }
}

// Formatar valores monetários
const formatCurrency = (value) => {
  return new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: 'BRL',
  }).format(value || 0);
};

// Formatar data
const formatDate = (dateString) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  return `${date.toLocaleDateString('pt-BR')} ${date.toLocaleTimeString('pt-BR')}`;
};

// Formatar tipo de discrepância
const formatDiscrepancyType = (type) => {
  const types = {
    status: 'Status',
    value: 'Valor',
    missing_system: 'Ausente no Sistema',
    missing_provider: 'Ausente no Provedor',
  };
  return types[type] || type;
};

// Verificar se há parâmetro de erro na URL
const error = Astro.url.searchParams.get('error');
---

<AdminLayout title="Resolver Discrepância">
  <div class="container mx-auto p-4">
    <h1 class="text-2xl font-bold mb-4">Gestão Financeira</h1>
    
    <FinancialNav />
    
    <div class="flex items-center mb-6">
      <a href="/admin/financial/reconciliation" class="btn btn-ghost btn-sm mr-2">
        <i class="fas fa-arrow-left"></i> Voltar
      </a>
      <h2 class="text-xl font-bold">Resolver Discrepância</h2>
    </div>
    
    {error === 'invalid_token' && (
      <div class="alert alert-error mb-4">
        <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
        <span>Token inválido. Por favor, tente novamente.</span>
      </div>
    )}
    
    <!-- Detalhes da Discrepância -->
    <div class="card bg-base-100 shadow-xl mb-6">
      <div class="card-body">
        <h2 class="card-title">Detalhes da Discrepância</h2>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div>
            <p><strong>ID da Discrepância:</strong> {discrepancy.ulid_discrepancy}</p>
            <p><strong>Tipo:</strong> {formatDiscrepancyType(discrepancy.discrepancy_type)}</p>
            <p><strong>Data de Detecção:</strong> {formatDate(discrepancy.created_at)}</p>
            <p><strong>ID Externo:</strong> {discrepancy.external_id}</p>
          </div>
          <div>
            <p><strong>Status no Sistema:</strong> {discrepancy.system_status}</p>
            <p><strong>Status no Provedor:</strong> {discrepancy.provider_status}</p>
            <p><strong>Valor no Sistema:</strong> {formatCurrency(discrepancy.system_value)}</p>
            <p><strong>Valor no Provedor:</strong> {formatCurrency(discrepancy.provider_value)}</p>
          </div>
        </div>
        
        {payment && (
          <div class="bg-base-200 p-4 rounded-lg mb-4">
            <h3 class="font-bold mb-2">Detalhes do Pagamento</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <p><strong>ID do Pagamento:</strong> {payment.ulid_payment}</p>
                <p><strong>Tipo de Pagamento:</strong> {payment.payment_type}</p>
                <p><strong>Status:</strong> {payment.status_name}</p>
                <p><strong>Valor:</strong> {formatCurrency(payment.value)}</p>
              </div>
              <div>
                <p><strong>ID Externo:</strong> {payment.external_id}</p>
                <p><strong>Data de Criação:</strong> {formatDate(payment.created_at)}</p>
                <p><strong>Última Atualização:</strong> {formatDate(payment.updated_at)}</p>
              </div>
            </div>
          </div>
        )}
        
        {order && (
          <div class="bg-base-200 p-4 rounded-lg mb-4">
            <h3 class="font-bold mb-2">Detalhes do Pedido</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <p><strong>ID do Pedido:</strong> {order.ulid_order}</p>
                <p><strong>Cliente:</strong> {order.user_name}</p>
                <p><strong>Email:</strong> {order.user_email}</p>
              </div>
              <div>
                <p><strong>Valor Total:</strong> {formatCurrency(order.total)}</p>
                <p><strong>Data de Criação:</strong> {formatDate(order.created_at)}</p>
              </div>
            </div>
          </div>
        )}
        
        <!-- Formulário de Resolução -->
        <form method="POST" class="mt-6">
          <input type="hidden" name="csrf_token" value={csrfToken} />
          
          <div class="form-control mb-4">
            <label class="label">
              <span class="label-text">Notas de Resolução</span>
            </label>
            <textarea 
              name="resolution_notes" 
              class="textarea textarea-bordered h-32" 
              placeholder="Descreva como a discrepância foi resolvida..."
              required
            ></textarea>
          </div>
          
          <div class="form-control mb-4">
            <label class="label cursor-pointer justify-start">
              <input type="checkbox" class="checkbox mr-2" required />
              <span class="label-text">Confirmo que esta discrepância foi verificada e resolvida adequadamente</span>
            </label>
          </div>
          
          <div class="flex justify-end gap-2">
            <a href="/admin/financial/reconciliation" class="btn btn-ghost">Cancelar</a>
            <button type="submit" name="action" value="resolve" class="btn btn-primary">Marcar como Resolvida</button>
          </div>
        </form>
      </div>
    </div>
  </div>
</AdminLayout>

<script>
  // Inicializar componentes interativos
  document.addEventListener('DOMContentLoaded', () => {
    // Código de inicialização, se necessário
  });
</script>
