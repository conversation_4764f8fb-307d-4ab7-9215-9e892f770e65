/**
 * Controlador de Produtos
 *
 * Este controlador implementa a lógica de interface para manipulação de produtos,
 * seguindo os princípios da Clean Architecture.
 */

import { ListProductsUseCase } from '../../application/usecases/product/ListProductsUseCase';
import { ProductFilterOptions } from '../../domain/repositories/ProductRepository';

/**
 * Parâmetros de URL para produtos
 */
export interface ProductUrlParams {
  page?: string;
  categoria?: string;
  destaque?: string;
  ordenar?: string;
  precoMin?: string;
  precoMax?: string;
  busca?: string;
}

/**
 * Controlador de Produtos
 */
export class ProductController {
  /**
   * Cria uma nova instância do controlador
   *
   * @param listProductsUseCase Caso de uso de listagem de produtos
   */
  constructor(private readonly listProductsUseCase: ListProductsUseCase) {}

  /**
   * Lista produtos com base nos parâmetros de URL
   *
   * @param params Parâmetros de URL
   * @returns Resultado da listagem de produtos
   */
  async listProducts(params: ProductUrlParams) {
    try {
      // Converter parâmetros de URL para opções de filtro
      const options: ProductFilterOptions = {
        page: params.page ? Number.parseInt(params.page) : 1,
        limit: 12,
        active: true,
      };

      // Adicionar categoria se fornecida
      if (params.categoria && params.categoria !== 'todas') {
        options.category = params.categoria;
      }

      // Adicionar destaque se fornecido
      if (params.destaque) {
        options.featured = params.destaque === 'true';
      }

      // Adicionar ordenação se fornecida
      if (params.ordenar) {
        options.sortBy = params.ordenar;

        // Determinar ordem de classificação com base no campo
        if (params.ordenar === 'price') {
          options.sortOrder = 'asc';
        } else if (params.ordenar === 'name') {
          options.sortOrder = 'asc';
        } else {
          options.sortOrder = 'desc';
        }
      }

      // Adicionar faixa de preço se fornecida
      if (params.precoMin) {
        options.minPrice = Number.parseFloat(params.precoMin);
      }

      if (params.precoMax) {
        options.maxPrice = Number.parseFloat(params.precoMax);
      }

      // Adicionar termo de busca se fornecido
      if (params.busca) {
        options.search = params.busca;
      }

      // Executar caso de uso de listagem de produtos
      const result = await this.listProductsUseCase.execute(options);

      return {
        success: true,
        data: result,
      };
    } catch (error) {
      console.error('Erro ao listar produtos:', error);

      // Retornar erro genérico
      return {
        success: false,
        error: 'Ocorreu um erro ao listar os produtos. Tente novamente.',
      };
    }
  }

  /**
   * Obtém produtos em destaque
   *
   * @param limit Limite de produtos a serem retornados
   * @returns Produtos em destaque
   */
  async getFeaturedProducts(limit = 6) {
    try {
      // Executar caso de uso para obter produtos em destaque
      const result = await this.listProductsUseCase.getFeaturedProducts(limit);

      return {
        success: true,
        data: result.items,
      };
    } catch (error) {
      console.error('Erro ao obter produtos em destaque:', error);

      // Retornar erro genérico
      return {
        success: false,
        error: 'Ocorreu um erro ao obter os produtos em destaque. Tente novamente.',
      };
    }
  }

  /**
   * Obtém produtos recentes
   *
   * @param limit Limite de produtos a serem retornados
   * @returns Produtos recentes
   */
  async getRecentProducts(limit = 6) {
    try {
      // Executar caso de uso para obter produtos recentes
      const result = await this.listProductsUseCase.getRecentProducts(limit);

      return {
        success: true,
        data: result.items,
      };
    } catch (error) {
      console.error('Erro ao obter produtos recentes:', error);

      // Retornar erro genérico
      return {
        success: false,
        error: 'Ocorreu um erro ao obter os produtos recentes. Tente novamente.',
      };
    }
  }

  /**
   * Constrói URL com filtros
   *
   * @param baseUrl URL base
   * @param params Parâmetros de URL
   * @returns URL com filtros
   */
  buildFilterUrl(baseUrl: string, params: ProductUrlParams): string {
    const url = new URL(baseUrl);

    // Adicionar parâmetros à URL
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        url.searchParams.set(key, value);
      }
    });

    return url.toString();
  }
}
