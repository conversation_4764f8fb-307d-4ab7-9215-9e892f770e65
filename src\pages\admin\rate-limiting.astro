---
/**
 * Página de Administração de Rate Limiting
 * 
 * Esta página permite visualizar e gerenciar as configurações de rate limiting,
 * incluindo bloqueios ativos, estatísticas e configurações.
 */

// Importações
import AdminLayout from '@layouts/AdminLayout.astro';
import { valkeyRateLimitService, RateLimitType } from '@services/valkeyRateLimitService';
import { getCurrentUser } from '@utils/authUtils';
import { hasPermission } from '@utils/permissionUtils';
import { logger } from '@utils/logger';
import { applicationMonitoringService } from '@services/applicationMonitoringService';

// Verificar autenticação e permissão
const user = await getCurrentUser(Astro.cookies);

// Redirecionar se não estiver autenticado ou não tiver permissão
if (!user || !await hasPermission(user.ulid_user, 'security:manage')) {
  return Astro.redirect('/admin/login?redirect=/admin/rate-limiting');
}

// Formatar tempo em formato legível
function formatTime(seconds: number): string {
  if (seconds < 60) {
    return `${seconds} segundos`;
  } else if (seconds < 3600) {
    return `${Math.floor(seconds / 60)} minutos e ${seconds % 60} segundos`;
  } else {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return `${hours} horas e ${minutes} minutos`;
  }
}

// Obter bloqueios ativos
let blockedIPs = [];
let error = '';

try {
  // Obter bloqueios ativos do cache
  const blockKeys = await valkeyRateLimitService.getActiveBlocks();
  
  // Processar bloqueios
  blockedIPs = await Promise.all(
    blockKeys.map(async (key) => {
      const parts = key.split(':');
      const type = parts[2];
      const ip = parts.slice(3).join(':');
      
      // Obter tempo restante
      const blockInfo = await valkeyRateLimitService.isBlocked(ip, type as RateLimitType);
      
      return {
        ip,
        type,
        expiresInSeconds: blockInfo.expiresInSeconds,
        formattedExpiration: formatTime(blockInfo.expiresInSeconds)
      };
    })
  );
} catch (err) {
  error = err.message;
  logger.error('Erro ao obter bloqueios ativos:', err);
}

// Processar ações
if (Astro.request.method === 'POST') {
  try {
    const formData = await Astro.request.formData();
    const action = formData.get('action');
    
    if (action === 'unblock') {
      const ip = formData.get('ip') as string;
      const type = formData.get('type') as RateLimitType;
      
      if (ip && type) {
        // Desbloquear IP
        await valkeyRateLimitService.unblockIdentifier(ip, type);
        
        // Registrar ação
        logger.info(`IP desbloqueado manualmente: ${ip} (${type})`, {
          user: user.ulid_user,
          ip,
          type
        });
        
        // Redirecionar para atualizar a página
        return Astro.redirect('/admin/rate-limiting?success=unblocked');
      }
    } else if (action === 'unblock-all') {
      // Desbloquear todos os IPs
      for (const block of blockedIPs) {
        await valkeyRateLimitService.unblockIdentifier(block.ip, block.type as RateLimitType);
      }
      
      // Registrar ação
      logger.info(`Todos os IPs desbloqueados manualmente`, {
        user: user.ulid_user,
        count: blockedIPs.length
      });
      
      // Redirecionar para atualizar a página
      return Astro.redirect('/admin/rate-limiting?success=unblocked-all');
    }
  } catch (err) {
    error = err.message;
    logger.error('Erro ao processar ação:', err);
  }
}

// Obter estatísticas de rate limiting
const stats = {
  totalBlocked: blockedIPs.length,
  byType: {
    [RateLimitType.GLOBAL]: applicationMonitoringService.getCounterValue(`ratelimit_${RateLimitType.GLOBAL}_limited`),
    [RateLimitType.LOGIN]: applicationMonitoringService.getCounterValue(`ratelimit_${RateLimitType.LOGIN}_limited`),
    [RateLimitType.SIGNUP]: applicationMonitoringService.getCounterValue(`ratelimit_${RateLimitType.SIGNUP}_limited`),
    [RateLimitType.CONTACT]: applicationMonitoringService.getCounterValue(`ratelimit_${RateLimitType.CONTACT}_limited`),
    [RateLimitType.API]: applicationMonitoringService.getCounterValue(`ratelimit_${RateLimitType.API}_limited`),
    [RateLimitType.UPLOAD]: applicationMonitoringService.getCounterValue(`ratelimit_${RateLimitType.UPLOAD}_limited`),
    [RateLimitType.PAYMENT]: applicationMonitoringService.getCounterValue(`ratelimit_${RateLimitType.PAYMENT}_limited`),
  }
};

// Obter configurações de rate limiting
const limitConfigs = [
  {
    type: RateLimitType.GLOBAL,
    name: 'Global',
    description: 'Limite global para todas as requisições',
    config: valkeyRateLimitService.getConfig(RateLimitType.GLOBAL)
  },
  {
    type: RateLimitType.LOGIN,
    name: 'Login',
    description: 'Limite para tentativas de login',
    config: valkeyRateLimitService.getConfig(RateLimitType.LOGIN)
  },
  {
    type: RateLimitType.SIGNUP,
    name: 'Cadastro',
    description: 'Limite para criação de contas',
    config: valkeyRateLimitService.getConfig(RateLimitType.SIGNUP)
  },
  {
    type: RateLimitType.CONTACT,
    name: 'Contato',
    description: 'Limite para envio de formulários de contato',
    config: valkeyRateLimitService.getConfig(RateLimitType.CONTACT)
  },
  {
    type: RateLimitType.API,
    name: 'API',
    description: 'Limite para requisições à API',
    config: valkeyRateLimitService.getConfig(RateLimitType.API)
  },
  {
    type: RateLimitType.UPLOAD,
    name: 'Upload',
    description: 'Limite para upload de arquivos',
    config: valkeyRateLimitService.getConfig(RateLimitType.UPLOAD)
  },
  {
    type: RateLimitType.PAYMENT,
    name: 'Pagamento',
    description: 'Limite para requisições de pagamento',
    config: valkeyRateLimitService.getConfig(RateLimitType.PAYMENT)
  }
];

// Título da página
const title = 'Gerenciamento de Rate Limiting';

// Verificar mensagem de sucesso
const success = Astro.url.searchParams.get('success');
let successMessage = '';

if (success === 'unblocked') {
  successMessage = 'IP desbloqueado com sucesso!';
} else if (success === 'unblocked-all') {
  successMessage = 'Todos os IPs foram desbloqueados com sucesso!';
}
---

<AdminLayout title={title}>
  <div class="container mx-auto px-4 py-8">
    <h1 class="text-3xl font-bold mb-6">{title}</h1>
    
    {error && (
      <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6" role="alert">
        <p class="font-bold">Erro</p>
        <p>{error}</p>
      </div>
    )}
    
    {successMessage && (
      <div class="bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mb-6" role="alert">
        <p>{successMessage}</p>
      </div>
    )}
    
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
      <div class="bg-white rounded-lg shadow-md p-6">
        <h2 class="text-xl font-semibold mb-4">Estatísticas</h2>
        <div class="grid grid-cols-2 gap-4">
          <div class="bg-blue-50 p-4 rounded-lg">
            <p class="text-sm text-gray-600">IPs Bloqueados</p>
            <p class="text-2xl font-bold text-blue-600">{stats.totalBlocked}</p>
          </div>
          
          {Object.entries(stats.byType).map(([type, count]) => (
            <div class="bg-blue-50 p-4 rounded-lg">
              <p class="text-sm text-gray-600">Limitados ({type})</p>
              <p class="text-2xl font-bold text-blue-600">{count}</p>
            </div>
          ))}
        </div>
      </div>
      
      <div class="bg-white rounded-lg shadow-md p-6">
        <h2 class="text-xl font-semibold mb-4">Ações</h2>
        <div class="space-y-4">
          <div>
            <h3 class="font-medium text-gray-700 mb-2">Desbloquear todos os IPs</h3>
            <form method="POST">
              <input type="hidden" name="action" value="unblock-all" />
              <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white py-2 px-4 rounded-md"
                disabled={blockedIPs.length === 0}
              >
                Desbloquear Todos ({blockedIPs.length})
              </button>
            </form>
          </div>
        </div>
      </div>
    </div>
    
    <div class="bg-white rounded-lg shadow-md p-6 mb-8">
      <h2 class="text-xl font-semibold mb-4">IPs Bloqueados</h2>
      
      {blockedIPs.length === 0 ? (
        <p class="text-gray-500">Nenhum IP bloqueado no momento.</p>
      ) : (
        <div class="overflow-x-auto">
          <table class="min-w-full bg-white">
            <thead>
              <tr class="bg-gray-100 text-gray-600 uppercase text-sm leading-normal">
                <th class="py-3 px-6 text-left">IP</th>
                <th class="py-3 px-6 text-left">Tipo</th>
                <th class="py-3 px-6 text-left">Expira em</th>
                <th class="py-3 px-6 text-center">Ações</th>
              </tr>
            </thead>
            <tbody class="text-gray-600 text-sm">
              {blockedIPs.map((block) => (
                <tr class="border-b border-gray-200 hover:bg-gray-50">
                  <td class="py-3 px-6 text-left whitespace-nowrap">
                    <div class="font-medium">{block.ip}</div>
                  </td>
                  <td class="py-3 px-6 text-left">
                    <span class="bg-blue-100 text-blue-800 py-1 px-3 rounded-full text-xs">
                      {block.type}
                    </span>
                  </td>
                  <td class="py-3 px-6 text-left">
                    {block.formattedExpiration}
                  </td>
                  <td class="py-3 px-6 text-center">
                    <form method="POST" class="inline">
                      <input type="hidden" name="action" value="unblock" />
                      <input type="hidden" name="ip" value={block.ip} />
                      <input type="hidden" name="type" value={block.type} />
                      <button 
                        type="submit" 
                        class="bg-red-500 hover:bg-red-600 text-white py-1 px-3 rounded-md text-xs"
                      >
                        Desbloquear
                      </button>
                    </form>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
    
    <div class="bg-white rounded-lg shadow-md p-6">
      <h2 class="text-xl font-semibold mb-4">Configurações de Rate Limiting</h2>
      
      <div class="overflow-x-auto">
        <table class="min-w-full bg-white">
          <thead>
            <tr class="bg-gray-100 text-gray-600 uppercase text-sm leading-normal">
              <th class="py-3 px-6 text-left">Tipo</th>
              <th class="py-3 px-6 text-left">Descrição</th>
              <th class="py-3 px-6 text-center">Limite</th>
              <th class="py-3 px-6 text-center">Janela</th>
              <th class="py-3 px-6 text-center">Bloqueio</th>
            </tr>
          </thead>
          <tbody class="text-gray-600 text-sm">
            {limitConfigs.map((config) => (
              <tr class="border-b border-gray-200 hover:bg-gray-50">
                <td class="py-3 px-6 text-left whitespace-nowrap">
                  <div class="font-medium">{config.name}</div>
                  <div class="text-xs text-gray-500">{config.type}</div>
                </td>
                <td class="py-3 px-6 text-left">
                  {config.description}
                </td>
                <td class="py-3 px-6 text-center">
                  {config.config.maxRequests} requisições
                </td>
                <td class="py-3 px-6 text-center">
                  {formatTime(config.config.windowSizeInSeconds)}
                </td>
                <td class="py-3 px-6 text-center">
                  {config.config.blockDurationInSeconds ? 
                    formatTime(config.config.blockDurationInSeconds) : 
                    <span class="text-gray-400">Sem bloqueio</span>
                  }
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  </div>
</AdminLayout>

<script>
  // Atualizar a página a cada 30 segundos para manter os dados atualizados
  setTimeout(() => {
    window.location.reload();
  }, 30000);
</script>
