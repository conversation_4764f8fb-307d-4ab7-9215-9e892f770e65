/**
 * Faixa etária para conteúdos educacionais
 *
 * Este objeto de valor representa uma faixa etária para conteúdos educacionais.
 */

export class AgeRange {
  /**
   * Cria uma nova instância de faixa etária
   *
   * @param min Idade mínima
   * @param max Idade máxima
   */
  constructor(
    public readonly min: number,
    public readonly max: number
  ) {
    this.validate();
  }

  /**
   * Valida a faixa etária
   *
   * @throws Error se a faixa etária for inválida
   */
  private validate(): void {
    if (this.min < 0) {
      throw new Error('A idade mínima não pode ser negativa');
    }

    if (this.max < this.min) {
      throw new Error('A idade máxima não pode ser menor que a idade mínima');
    }

    if (this.max > 18) {
      throw new Error('A idade máxima não pode ser maior que 18 anos');
    }
  }

  /**
   * Verifica se uma idade está dentro da faixa etária
   *
   * @param age Idade a ser verificada
   * @returns Verdadeiro se a idade estiver dentro da faixa etária
   */
  includes(age: number): boolean {
    return age >= this.min && age <= this.max;
  }

  /**
   * Verifica se esta faixa etária se sobrepõe a outra
   *
   * @param other Outra faixa etária
   * @returns Verdadeiro se houver sobreposição
   */
  overlaps(other: AgeRange): boolean {
    return this.max >= other.min && this.min <= other.max;
  }

  /**
   * Obtém a descrição da faixa etária
   *
   * @returns Descrição da faixa etária (ex: "6-8 anos")
   */
  getDescription(): string {
    return `${this.min}-${this.max} anos`;
  }

  /**
   * Obtém a categoria da faixa etária
   *
   * @returns Categoria da faixa etária
   */
  getCategory(): string {
    if (this.max <= 5) {
      return 'Educação Infantil';
    }
    if (this.max <= 10) {
      return 'Ensino Fundamental I';
    }
    if (this.max <= 14) {
      return 'Ensino Fundamental II';
    }
    return 'Ensino Médio';
  }

  /**
   * Cria uma faixa etária a partir de uma string
   *
   * @param value String no formato "min-max"
   * @returns Nova instância de faixa etária
   * @throws Error se a string for inválida
   */
  static fromString(value: string): AgeRange {
    const parts = value.split('-');

    if (parts.length !== 2) {
      throw new Error('Formato inválido. Use "min-max"');
    }

    const min = Number.parseInt(parts[0], 10);
    const max = Number.parseInt(parts[1], 10);

    if (Number.isNaN(min) || Number.isNaN(max)) {
      throw new Error('Valores inválidos. Use números inteiros');
    }

    return new AgeRange(min, max);
  }

  /**
   * Converte a faixa etária para string
   *
   * @returns String no formato "min-max"
   */
  toString(): string {
    return `${this.min}-${this.max}`;
  }
}
