---
import DaisyButton from '../../components/ui/DaisyButton.astro';
import DaisyCard from '../../components/ui/DaisyCard.astro';
import BaseLayout from '../../layouts/BaseLayout.astro';

const title = 'Exemplos de DaisyUI';
---

<BaseLayout title={title}>
  <div class="container mx-auto px-4 py-8">
    <h1 class="text-4xl font-bold mb-8 text-center">{title}</h1>
    
    <section class="mb-12">
      <h2 class="text-2xl font-bold mb-4">Botões</h2>
      
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
        <div class="card p-4">
          <h3 class="font-bold mb-2">Variantes</h3>
          <div class="flex flex-wrap gap-2">
            <DaisyButton>Default</DaisyButton>
            <DaisyButton variant="primary">Primary</DaisyButton>
            <DaisyButton variant="secondary">Secondary</DaisyButton>
            <DaisyButton variant="accent">Accent</DaisyButton>
            <DaisyButton variant="neutral">Neutral</DaisyButton>
            <DaisyButton variant="info">Info</DaisyButton>
            <DaisyButton variant="success">Success</DaisyButton>
            <DaisyButton variant="warning">Warning</DaisyButton>
            <DaisyButton variant="error">Error</DaisyButton>
          </div>
        </div>
        
        <div class="card p-4">
          <h3 class="font-bold mb-2">Tamanhos</h3>
          <div class="flex flex-wrap items-center gap-2">
            <DaisyButton size="xs">Extra Small</DaisyButton>
            <DaisyButton size="sm">Small</DaisyButton>
            <DaisyButton>Normal</DaisyButton>
            <DaisyButton size="lg">Large</DaisyButton>
          </div>
        </div>
        
        <div class="card p-4">
          <h3 class="font-bold mb-2">Estilos</h3>
          <div class="flex flex-wrap gap-2">
            <DaisyButton outline>Outline</DaisyButton>
            <DaisyButton variant="primary" outline>Primary</DaisyButton>
            <DaisyButton variant="secondary" outline>Secondary</DaisyButton>
            <DaisyButton glass>Glass</DaisyButton>
            <DaisyButton variant="ghost">Ghost</DaisyButton>
            <DaisyButton variant="link">Link</DaisyButton>
          </div>
        </div>
        
        <div class="card p-4">
          <h3 class="font-bold mb-2">Estados</h3>
          <div class="flex flex-wrap gap-2">
            <DaisyButton loading>Carregando</DaisyButton>
            <DaisyButton disabled>Desabilitado</DaisyButton>
            <DaisyButton noAnimation>Sem Animação</DaisyButton>
          </div>
        </div>
      </div>
      
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div class="card p-4">
          <h3 class="font-bold mb-2">Formatos</h3>
          <div class="flex flex-wrap gap-2">
            <DaisyButton wide>Wide</DaisyButton>
            <DaisyButton block>Block</DaisyButton>
            <DaisyButton circle>
              <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
              </svg>
            </DaisyButton>
            <DaisyButton square>
              <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </DaisyButton>
          </div>
        </div>
        
        <div class="card p-4">
          <h3 class="font-bold mb-2">Links</h3>
          <div class="flex flex-wrap gap-2">
            <DaisyButton href="/examples">Link Interno</DaisyButton>
            <DaisyButton href="https://daisyui.com" variant="secondary">Link Externo</DaisyButton>
            <DaisyButton href="/examples" disabled>Link Desabilitado</DaisyButton>
          </div>
        </div>
      </div>
    </section>
    
    <section class="mb-12">
      <h2 class="text-2xl font-bold mb-4">Cards</h2>
      
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <DaisyCard title="Card Básico">
          <p>Este é um card básico com título e conteúdo.</p>
          <div slot="actions" class="card-actions justify-end">
            <DaisyButton variant="primary">Ação</DaisyButton>
          </div>
        </DaisyCard>
        
        <DaisyCard 
          title="Card com Imagem" 
          image={{ 
            src: "https://placehold.co/600x400/0066CC/FFFFFF.png?text=Estação+da+Alfabetização", 
            alt: "Placeholder" 
          }}
        >
          <p>Este card inclui uma imagem no topo.</p>
          <div slot="actions" class="card-actions justify-end">
            <DaisyButton variant="primary">Ver mais</DaisyButton>
            <DaisyButton variant="ghost">Cancelar</DaisyButton>
          </div>
        </DaisyCard>
        
        <DaisyCard 
          title="Card com Imagem Inferior" 
          image={{ 
            src: "https://placehold.co/600x400/FF6B6B/FFFFFF.png?text=Estação+da+Alfabetização", 
            alt: "Placeholder" 
          }}
          imagePosition="bottom"
        >
          <p>Este card tem a imagem na parte inferior.</p>
          <div slot="actions" class="card-actions justify-end">
            <DaisyButton variant="secondary">Ação</DaisyButton>
          </div>
        </DaisyCard>
        
        <DaisyCard title="Card Compacto" compact>
          <p>Este é um card compacto com menos padding.</p>
          <div slot="actions" class="card-actions justify-end">
            <DaisyButton variant="accent" size="sm">Ação</DaisyButton>
          </div>
        </DaisyCard>
        
        <DaisyCard title="Card com Borda" bordered>
          <p>Este card tem uma borda ao redor.</p>
          <div slot="actions" class="card-actions justify-end">
            <DaisyButton variant="info">Ação</DaisyButton>
          </div>
        </DaisyCard>
        
        <DaisyCard title="Card de Vidro" glass>
          <p>Este card tem um efeito de vidro (glass).</p>
          <div slot="actions" class="card-actions justify-end">
            <DaisyButton glass>Ação</DaisyButton>
          </div>
        </DaisyCard>
      </div>
    </section>
    
    <section class="mb-12">
      <h2 class="text-2xl font-bold mb-4">Tema</h2>
      
      <div class="card bordered p-6">
        <h3 class="font-bold mb-4">Alternar Tema</h3>
        <p class="mb-4">O DaisyUI suporta temas claros e escuros. Clique no botão abaixo para alternar entre os temas.</p>
        
        <div class="flex justify-center">
          <label class="swap swap-rotate">
            <input type="checkbox" class="theme-controller" value="estacao_alfabetizacao_dark" />
            <svg class="swap-on fill-current w-10 h-10" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M5.64,17l-.71.71a1,1,0,0,0,0,1.41,1,1,0,0,0,1.41,0l.71-.71A1,1,0,0,0,5.64,17ZM5,12a1,1,0,0,0-1-1H3a1,1,0,0,0,0,2H4A1,1,0,0,0,5,12Zm7-7a1,1,0,0,0,1-1V3a1,1,0,0,0-2,0V4A1,1,0,0,0,12,5ZM5.64,7.05a1,1,0,0,0,.7.29,1,1,0,0,0,.71-.29,1,1,0,0,0,0-1.41l-.71-.71A1,1,0,0,0,4.93,6.34Zm12,.29a1,1,0,0,0,.7-.29l.71-.71a1,1,0,1,0-1.41-1.41L17,5.64a1,1,0,0,0,0,1.41A1,1,0,0,0,17.66,7.34ZM21,11H20a1,1,0,0,0,0,2h1a1,1,0,0,0,0-2Zm-9,8a1,1,0,0,0-1,1v1a1,1,0,0,0,2,0V20A1,1,0,0,0,12,19ZM18.36,17A1,1,0,0,0,17,18.36l.71.71a1,1,0,0,0,1.41,0,1,1,0,0,0,0-1.41ZM12,6.5A5.5,5.5,0,1,0,17.5,12,5.51,5.51,0,0,0,12,6.5Zm0,9A3.5,3.5,0,1,1,15.5,12,3.5,3.5,0,0,1,12,15.5Z"/></svg>
            <svg class="swap-off fill-current w-10 h-10" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M21.64,13a1,1,0,0,0-1.05-.14,8.05,8.05,0,0,1-3.37.73A8.15,8.15,0,0,1,9.08,5.49a8.59,8.59,0,0,1,.25-2A1,1,0,0,0,8,2.36,10.14,10.14,0,1,0,22,14.05,1,1,0,0,0,21.64,13Zm-9.5,6.69A8.14,8.14,0,0,1,7.08,5.22v.27A10.15,10.15,0,0,0,17.22,15.63a9.79,9.79,0,0,0,2.1-.22A8.11,8.11,0,0,1,12.14,19.73Z"/></svg>
          </label>
        </div>
      </div>
    </section>
  </div>
</BaseLayout>

<script>
  // Script para controlar o tema
  document.addEventListener('DOMContentLoaded', () => {
    const themeController = document.querySelector('.theme-controller');
    
    if (themeController) {
      // Verificar tema atual
      const currentTheme = localStorage.getItem('theme') || 'estacao_alfabetizacao';
      document.documentElement.setAttribute('data-theme', currentTheme);
      
      // Atualizar checkbox com base no tema atual
      if (themeController instanceof HTMLInputElement) {
        themeController.checked = currentTheme === 'estacao_alfabetizacao_dark';
      }
      
      // Adicionar listener para alternar tema
      themeController.addEventListener('change', (e) => {
        if (e.target instanceof HTMLInputElement) {
          const newTheme = e.target.checked ? 'estacao_alfabetizacao_dark' : 'estacao_alfabetizacao';
          document.documentElement.setAttribute('data-theme', newTheme);
          localStorage.setItem('theme', newTheme);
        }
      });
    }
  });
</script>
