---
/**
 * Dashboard de Monitoramento
 * 
 * Este componente exibe métricas e estatísticas de monitoramento
 * do sistema em um painel administrativo.
 */

// Importar serviços e helpers
import { applicationMonitoringService, MetricType } from '@services/applicationMonitoringService';
import { formatNumber, formatDate, formatDuration } from '@utils/formatters';

// Obter estatísticas do sistema
const systemStats = applicationMonitoringService.getSystemStats();

// Obter alertas ativos
const activeAlerts = applicationMonitoringService.getActiveAlerts();

// Obter métricas recentes para CPU e memória
const cpuMetrics = applicationMonitoringService.getMetrics(MetricType.CPU_USAGE, 20);
const memoryMetrics = applicationMonitoringService.getMetrics(MetricType.MEMORY_USAGE, 20);

// Obter métricas de requisições
const requestDurationMetrics = applicationMonitoringService.getMetrics(MetricType.REQUEST_DURATION, 20);
const errorRateMetrics = applicationMonitoringService.getMetrics(MetricType.ERROR_RATE, 20);

// Obter métricas de banco de dados
const dbQueryDurationMetrics = applicationMonitoringService.getMetrics(MetricType.DB_QUERY_DURATION, 20);
const dbConnectionCountMetrics = applicationMonitoringService.getMetrics(MetricType.DB_CONNECTION_COUNT, 20);

// Obter métricas de cache
const cacheHitRateMetrics = applicationMonitoringService.getMetrics(MetricType.CACHE_HIT_RATE, 20);
const cacheSizeMetrics = applicationMonitoringService.getMetrics(MetricType.CACHE_SIZE, 20);

// Obter métricas de negócio
const activeUsersMetrics = applicationMonitoringService.getMetrics(MetricType.ACTIVE_USERS, 20);
const transactionCountMetrics = applicationMonitoringService.getMetrics(MetricType.TRANSACTION_COUNT, 20);

// Função para obter classe CSS com base na severidade
function getSeverityClass(value: number, thresholds: { warning: number, error: number, critical: number }) {
  if (value >= thresholds.critical) return 'critical';
  if (value >= thresholds.error) return 'error';
  if (value >= thresholds.warning) return 'warning';
  return 'normal';
}

// Função para obter classe CSS com base na severidade invertida (menor é pior)
function getInverseSeverityClass(value: number, thresholds: { warning: number, error: number, critical: number }) {
  if (value <= thresholds.critical) return 'critical';
  if (value <= thresholds.error) return 'error';
  if (value <= thresholds.warning) return 'warning';
  return 'normal';
}

// Obter classe para CPU
const cpuClass = getSeverityClass(systemStats.cpu, { warning: 70, error: 85, critical: 95 });

// Obter classe para memória
const memoryClass = getSeverityClass(systemStats.memory, { warning: 70, error: 85, critical: 95 });

// Obter classe para taxa de erro
const errorRateClass = getSeverityClass(systemStats.errorRate, { warning: 1, error: 5, critical: 10 });

// Obter classe para taxa de acertos de cache
const cacheHitRateClass = getInverseSeverityClass(
  applicationMonitoringService.getLatestMetricValue(MetricType.CACHE_HIT_RATE) || 0,
  { warning: 80, error: 60, critical: 40 }
);
---

<div class="monitoring-dashboard">
  <h1>Dashboard de Monitoramento</h1>
  
  <!-- Resumo do sistema -->
  <div class="stats-summary">
    <div class="stats-card">
      <h3>CPU</h3>
      <div class={`stats-value ${cpuClass}`}>{formatNumber(systemStats.cpu, 1)}%</div>
      <div class="stats-chart">
        <div class="chart-container" id="cpu-chart" data-values={JSON.stringify(cpuMetrics.map(m => m.value))}></div>
      </div>
    </div>
    
    <div class="stats-card">
      <h3>Memória</h3>
      <div class={`stats-value ${memoryClass}`}>{formatNumber(systemStats.memory, 1)}%</div>
      <div class="stats-chart">
        <div class="chart-container" id="memory-chart" data-values={JSON.stringify(memoryMetrics.map(m => m.value))}></div>
      </div>
    </div>
    
    <div class="stats-card">
      <h3>Taxa de Erro</h3>
      <div class={`stats-value ${errorRateClass}`}>{formatNumber(systemStats.errorRate, 2)}%</div>
      <div class="stats-chart">
        <div class="chart-container" id="error-rate-chart" data-values={JSON.stringify(errorRateMetrics.map(m => m.value))}></div>
      </div>
    </div>
    
    <div class="stats-card">
      <h3>Usuários Ativos</h3>
      <div class="stats-value">{formatNumber(systemStats.activeUsers, 0)}</div>
      <div class="stats-chart">
        <div class="chart-container" id="active-users-chart" data-values={JSON.stringify(activeUsersMetrics.map(m => m.value))}></div>
      </div>
    </div>
  </div>
  
  <!-- Alertas ativos -->
  <div class="alerts-section">
    <h2>Alertas Ativos ({activeAlerts.length})</h2>
    
    {activeAlerts.length === 0 ? (
      <div class="no-alerts">Nenhum alerta ativo no momento.</div>
    ) : (
      <div class="alerts-list">
        {activeAlerts.map(alert => (
          <div class={`alert-item alert-${alert.severity}`}>
            <div class="alert-header">
              <span class="alert-severity">{alert.severity.toUpperCase()}</span>
              <span class="alert-timestamp">{formatDate(alert.timestamp)}</span>
            </div>
            <div class="alert-description">{alert.description}</div>
            <div class="alert-details">
              {alert.metricType}: {formatNumber(alert.value, 2)} (limite: {formatNumber(alert.threshold, 2)})
            </div>
          </div>
        ))}
      </div>
    )}
  </div>
  
  <!-- Métricas de aplicação -->
  <div class="metrics-section">
    <h2>Métricas de Aplicação</h2>
    
    <div class="metrics-grid">
      <div class="metric-card">
        <h3>Duração de Requisições</h3>
        <div class="metric-value">{formatNumber(applicationMonitoringService.getLatestMetricValue(MetricType.REQUEST_DURATION) || 0, 2)} ms</div>
        <div class="metric-chart">
          <div class="chart-container" id="request-duration-chart" data-values={JSON.stringify(requestDurationMetrics.map(m => m.value))}></div>
        </div>
      </div>
      
      <div class="metric-card">
        <h3>Requisições</h3>
        <div class="metric-value">{formatNumber(systemStats.requestCount, 0)}</div>
        <div class="metric-details">
          <div class="metric-detail">Erros: {formatNumber(systemStats.errorCount, 0)}</div>
          <div class="metric-detail">Taxa de Erro: {formatNumber(systemStats.errorRate, 2)}%</div>
        </div>
      </div>
      
      <div class="metric-card">
        <h3>Duração de Consultas DB</h3>
        <div class="metric-value">{formatNumber(applicationMonitoringService.getLatestMetricValue(MetricType.DB_QUERY_DURATION) || 0, 2)} ms</div>
        <div class="metric-chart">
          <div class="chart-container" id="db-query-duration-chart" data-values={JSON.stringify(dbQueryDurationMetrics.map(m => m.value))}></div>
        </div>
      </div>
      
      <div class="metric-card">
        <h3>Conexões DB</h3>
        <div class="metric-value">{formatNumber(applicationMonitoringService.getLatestMetricValue(MetricType.DB_CONNECTION_COUNT) || 0, 0)}</div>
        <div class="metric-chart">
          <div class="chart-container" id="db-connection-chart" data-values={JSON.stringify(dbConnectionCountMetrics.map(m => m.value))}></div>
        </div>
      </div>
    </div>
  </div>
  
  <!-- Métricas de cache -->
  <div class="metrics-section">
    <h2>Métricas de Cache</h2>
    
    <div class="metrics-grid">
      <div class="metric-card">
        <h3>Taxa de Acertos</h3>
        <div class={`metric-value ${cacheHitRateClass}`}>
          {formatNumber(applicationMonitoringService.getLatestMetricValue(MetricType.CACHE_HIT_RATE) || 0, 2)}%
        </div>
        <div class="metric-chart">
          <div class="chart-container" id="cache-hit-rate-chart" data-values={JSON.stringify(cacheHitRateMetrics.map(m => m.value))}></div>
        </div>
      </div>
      
      <div class="metric-card">
        <h3>Tamanho do Cache</h3>
        <div class="metric-value">{formatNumber(applicationMonitoringService.getLatestMetricValue(MetricType.CACHE_SIZE) || 0, 0)} chaves</div>
        <div class="metric-chart">
          <div class="chart-container" id="cache-size-chart" data-values={JSON.stringify(cacheSizeMetrics.map(m => m.value))}></div>
        </div>
      </div>
      
      <div class="metric-card">
        <h3>Acertos / Falhas</h3>
        <div class="metric-value">
          {formatNumber(applicationMonitoringService.getCounterValue(MetricType.CACHE_HIT_COUNT), 0)} / 
          {formatNumber(applicationMonitoringService.getCounterValue(MetricType.CACHE_MISS_COUNT), 0)}
        </div>
      </div>
    </div>
  </div>
  
  <!-- Informações do sistema -->
  <div class="system-info-section">
    <h2>Informações do Sistema</h2>
    
    <div class="system-info-grid">
      <div class="system-info-card">
        <h3>Servidor</h3>
        <div class="system-info-details">
          <div class="system-info-detail">
            <span class="detail-label">Plataforma:</span>
            <span class="detail-value">{systemStats.os.platform} ({systemStats.os.arch})</span>
          </div>
          <div class="system-info-detail">
            <span class="detail-label">Node.js:</span>
            <span class="detail-value">{systemStats.os.version}</span>
          </div>
          <div class="system-info-detail">
            <span class="detail-label">CPUs:</span>
            <span class="detail-value">{systemStats.os.cpus}</span>
          </div>
          <div class="system-info-detail">
            <span class="detail-label">Memória Total:</span>
            <span class="detail-value">{formatNumber(systemStats.os.totalMemory / 1024 / 1024 / 1024, 2)} GB</span>
          </div>
          <div class="system-info-detail">
            <span class="detail-label">Uptime:</span>
            <span class="detail-value">{formatDuration(systemStats.os.uptime)}</span>
          </div>
        </div>
      </div>
      
      <div class="system-info-card">
        <h3>Processo</h3>
        <div class="system-info-details">
          <div class="system-info-detail">
            <span class="detail-label">PID:</span>
            <span class="detail-value">{systemStats.process.pid}</span>
          </div>
          <div class="system-info-detail">
            <span class="detail-label">Uptime:</span>
            <span class="detail-value">{formatDuration(systemStats.process.uptime)}</span>
          </div>
          <div class="system-info-detail">
            <span class="detail-label">Heap Usado:</span>
            <span class="detail-value">{formatNumber(systemStats.process.memoryUsage.heapUsed / 1024 / 1024, 2)} MB</span>
          </div>
          <div class="system-info-detail">
            <span class="detail-label">Heap Total:</span>
            <span class="detail-value">{formatNumber(systemStats.process.memoryUsage.heapTotal / 1024 / 1024, 2)} MB</span>
          </div>
          <div class="system-info-detail">
            <span class="detail-label">RSS:</span>
            <span class="detail-value">{formatNumber(systemStats.process.memoryUsage.rss / 1024 / 1024, 2)} MB</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  // Função para criar gráficos simples
  function createSimpleChart(elementId, values, color = '#4CAF50', fillColor = 'rgba(76, 175, 80, 0.2)') {
    const element = document.getElementById(elementId);
    if (!element) return;
    
    const valuesData = element.dataset.values;
    if (!valuesData) return;
    
    const chartValues = JSON.parse(valuesData);
    const canvas = document.createElement('canvas');
    element.appendChild(canvas);
    
    const ctx = canvas.getContext('2d');
    if (!ctx) return;
    
    const width = element.clientWidth;
    const height = element.clientHeight;
    canvas.width = width;
    canvas.height = height;
    
    const max = Math.max(...chartValues, 1);
    const min = Math.min(...chartValues, 0);
    const range = max - min;
    
    ctx.strokeStyle = color;
    ctx.fillStyle = fillColor;
    ctx.lineWidth = 2;
    ctx.beginPath();
    
    // Desenhar linha
    chartValues.forEach((value, index) => {
      const x = (index / (chartValues.length - 1)) * width;
      const y = height - ((value - min) / range) * height;
      
      if (index === 0) {
        ctx.moveTo(x, y);
      } else {
        ctx.lineTo(x, y);
      }
    });
    
    // Desenhar área preenchida
    ctx.stroke();
    ctx.lineTo(width, height);
    ctx.lineTo(0, height);
    ctx.closePath();
    ctx.fill();
  }
  
  // Criar gráficos quando o DOM estiver pronto
  document.addEventListener('DOMContentLoaded', () => {
    // Gráficos de resumo
    createSimpleChart('cpu-chart', [], '#FF5722', 'rgba(255, 87, 34, 0.2)');
    createSimpleChart('memory-chart', [], '#2196F3', 'rgba(33, 150, 243, 0.2)');
    createSimpleChart('error-rate-chart', [], '#F44336', 'rgba(244, 67, 54, 0.2)');
    createSimpleChart('active-users-chart', [], '#9C27B0', 'rgba(156, 39, 176, 0.2)');
    
    // Gráficos de aplicação
    createSimpleChart('request-duration-chart', [], '#FF9800', 'rgba(255, 152, 0, 0.2)');
    createSimpleChart('db-query-duration-chart', [], '#795548', 'rgba(121, 85, 72, 0.2)');
    createSimpleChart('db-connection-chart', [], '#607D8B', 'rgba(96, 125, 139, 0.2)');
    
    // Gráficos de cache
    createSimpleChart('cache-hit-rate-chart', [], '#4CAF50', 'rgba(76, 175, 80, 0.2)');
    createSimpleChart('cache-size-chart', [], '#3F51B5', 'rgba(63, 81, 181, 0.2)');
  });
  
  // Atualizar dados a cada 30 segundos
  setInterval(() => {
    window.location.reload();
  }, 30000);
</script>

<style>
  .monitoring-dashboard {
    padding: 1rem;
  }
  
  h1, h2, h3 {
    margin-bottom: 1rem;
  }
  
  .stats-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
  }
  
  .stats-card {
    background-color: #fff;
    border-radius: 8px;
    padding: 1rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
  
  .stats-value {
    font-size: 2rem;
    font-weight: bold;
    margin: 0.5rem 0;
  }
  
  .stats-value.normal { color: #4CAF50; }
  .stats-value.warning { color: #FF9800; }
  .stats-value.error { color: #F44336; }
  .stats-value.critical { color: #B71C1C; }
  
  .stats-chart, .metric-chart {
    height: 60px;
    margin-top: 0.5rem;
  }
  
  .chart-container {
    width: 100%;
    height: 100%;
  }
  
  .alerts-section {
    background-color: #fff;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 2rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
  
  .no-alerts {
    padding: 1rem;
    text-align: center;
    color: #4CAF50;
  }
  
  .alerts-list {
    display: grid;
    gap: 0.5rem;
  }
  
  .alert-item {
    padding: 0.75rem;
    border-radius: 4px;
    border-left: 4px solid #ccc;
  }
  
  .alert-item.alert-info { border-left-color: #2196F3; background-color: rgba(33, 150, 243, 0.1); }
  .alert-item.alert-warning { border-left-color: #FF9800; background-color: rgba(255, 152, 0, 0.1); }
  .alert-item.alert-error { border-left-color: #F44336; background-color: rgba(244, 67, 54, 0.1); }
  .alert-item.alert-critical { border-left-color: #B71C1C; background-color: rgba(183, 28, 28, 0.1); }
  
  .alert-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
  }
  
  .alert-severity {
    font-weight: bold;
  }
  
  .alert-timestamp {
    font-size: 0.875rem;
    color: #666;
  }
  
  .alert-description {
    font-weight: bold;
    margin-bottom: 0.25rem;
  }
  
  .alert-details {
    font-size: 0.875rem;
    color: #666;
  }
  
  .metrics-section {
    background-color: #fff;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 2rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
  
  .metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
  }
  
  .metric-card {
    padding: 1rem;
    border-radius: 4px;
    background-color: #f9f9f9;
  }
  
  .metric-value {
    font-size: 1.5rem;
    font-weight: bold;
    margin: 0.5rem 0;
  }
  
  .metric-details {
    margin-top: 0.5rem;
    font-size: 0.875rem;
  }
  
  .system-info-section {
    background-color: #fff;
    border-radius: 8px;
    padding: 1rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
  
  .system-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1rem;
  }
  
  .system-info-card {
    padding: 1rem;
    border-radius: 4px;
    background-color: #f9f9f9;
  }
  
  .system-info-details {
    display: grid;
    gap: 0.5rem;
  }
  
  .system-info-detail {
    display: flex;
    justify-content: space-between;
  }
  
  .detail-label {
    font-weight: bold;
    color: #666;
  }
  
  @media (max-width: 768px) {
    .stats-summary,
    .metrics-grid,
    .system-info-grid {
      grid-template-columns: 1fr;
    }
  }
</style>
