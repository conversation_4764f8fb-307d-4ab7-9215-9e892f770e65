/**
 * Cache Estratégico
 *
 * Implementa estratégias avançadas de cache para otimizar a performance
 * do On-Demand Rendering (ODR) e outras funcionalidades.
 */

import { deleteCache, deleteCacheByPattern, getCache, setCache } from './cacheClient';

// Tipos de cache
export enum CacheStrategy {
  // Cache padrão - TTL fixo
  STANDARD = 'standard',

  // Cache com stale-while-revalidate
  STALE_WHILE_REVALIDATE = 'stale-while-revalidate',

  // Cache com invalidação por evento
  EVENT_BASED = 'event-based',

  // Cache com variação por usuário
  USER_AWARE = 'user-aware',

  // Cache com variação por dispositivo/navegador
  DEVICE_AWARE = 'device-aware',

  // Cache com variação por localização
  GEO_AWARE = 'geo-aware',
}

// Configuração de cache por tipo de conteúdo
interface CacheConfig {
  // Prefixo para chaves de cache
  keyPrefix: string;

  // TTL padrão em segundos
  defaultTTL: number;

  // Estratégia de cache
  strategy: CacheStrategy;

  // Tempo de stale em segundos (para STALE_WHILE_REVALIDATE)
  staleTTL?: number;

  // Eventos que invalidam o cache (para EVENT_BASED)
  invalidationEvents?: string[];

  // Função para gerar chave de cache
  keyGenerator?: (params: Record<string, any>) => string;
}

// Configurações de cache por tipo de conteúdo
const cacheConfigs: Record<string, CacheConfig> = {
  // Páginas de cursos
  courses: {
    keyPrefix: 'courses',
    defaultTTL: 60 * 60, // 1 hora
    strategy: CacheStrategy.STALE_WHILE_REVALIDATE,
    staleTTL: 60 * 60 * 24, // 24 horas
    invalidationEvents: ['course:update', 'course:delete'],
    keyGenerator: (params) => `${params.courseId || 'list'}:${params.page || '1'}`,
  },

  // Materiais didáticos
  materials: {
    keyPrefix: 'materials',
    defaultTTL: 60 * 60, // 1 hora
    strategy: CacheStrategy.STALE_WHILE_REVALIDATE,
    staleTTL: 60 * 60 * 12, // 12 horas
    invalidationEvents: ['material:update', 'material:delete'],
    keyGenerator: (params) =>
      `${params.materialId || 'list'}:${params.category || 'all'}:${params.page || '1'}`,
  },

  // Perfil de usuário
  profile: {
    keyPrefix: 'profile',
    defaultTTL: 60 * 5, // 5 minutos
    strategy: CacheStrategy.USER_AWARE,
    invalidationEvents: ['user:update', 'user:preferences'],
    keyGenerator: (params) => `${params.userId}`,
  },

  // Resultados de busca
  search: {
    keyPrefix: 'search',
    defaultTTL: 60 * 2, // 2 minutos
    strategy: CacheStrategy.STANDARD,
    keyGenerator: (params) => `${params.query}:${params.filters || 'none'}:${params.page || '1'}`,
  },

  // Conteúdo estático
  static: {
    keyPrefix: 'static',
    defaultTTL: 60 * 60 * 24 * 7, // 7 dias
    strategy: CacheStrategy.STANDARD,
    keyGenerator: (params) => `${params.path}`,
  },
};

/**
 * Gera uma chave de cache com base no tipo de conteúdo e parâmetros
 * @param contentType Tipo de conteúdo
 * @param params Parâmetros para geração da chave
 * @returns Chave de cache
 */
export function generateCacheKey(contentType: string, params: Record<string, any> = {}): string {
  const config = cacheConfigs[contentType] || cacheConfigs.static;

  // Gerar parte específica da chave
  let specificKey = '';

  if (config.keyGenerator) {
    specificKey = config.keyGenerator(params);
  } else {
    // Chave padrão baseada em parâmetros
    specificKey = Object.entries(params)
      .map(([key, value]) => `${key}:${value}`)
      .join(':');
  }

  // Adicionar variação por usuário se necessário
  if (config.strategy === CacheStrategy.USER_AWARE && params.userId) {
    return `${config.keyPrefix}:user:${params.userId}:${specificKey}`;
  }

  // Adicionar variação por dispositivo se necessário
  if (config.strategy === CacheStrategy.DEVICE_AWARE && params.deviceType) {
    return `${config.keyPrefix}:device:${params.deviceType}:${specificKey}`;
  }

  // Adicionar variação por localização se necessário
  if (config.strategy === CacheStrategy.GEO_AWARE && params.region) {
    return `${config.keyPrefix}:geo:${params.region}:${specificKey}`;
  }

  // Chave padrão
  return `${config.keyPrefix}:${specificKey}`;
}

/**
 * Obtém o TTL para um tipo de conteúdo
 * @param contentType Tipo de conteúdo
 * @returns TTL em segundos
 */
export function getCacheTTL(contentType: string): number {
  const config = cacheConfigs[contentType] || cacheConfigs.static;
  return config.defaultTTL;
}

/**
 * Obtém a estratégia de cache para um tipo de conteúdo
 * @param contentType Tipo de conteúdo
 * @returns Estratégia de cache
 */
export function getCacheStrategy(contentType: string): CacheStrategy {
  const config = cacheConfigs[contentType] || cacheConfigs.static;
  return config.strategy;
}

/**
 * Obtém dados do cache com suporte a stale-while-revalidate
 * @param contentType Tipo de conteúdo
 * @param params Parâmetros para geração da chave
 * @returns Dados do cache ou null se não encontrado
 */
export async function getStrategicCache<T>(
  contentType: string,
  params: Record<string, any> = {}
): Promise<{
  data: T | null;
  isStale: boolean;
}> {
  const config = cacheConfigs[contentType] || cacheConfigs.static;
  const cacheKey = generateCacheKey(contentType, params);

  try {
    // Obter dados do cache
    const cachedData = await getCache<{
      data: T;
      timestamp: number;
      isStale?: boolean;
    }>(cacheKey);

    if (!cachedData) {
      return { data: null, isStale: false };
    }

    // Verificar se os dados estão obsoletos (stale)
    const now = Date.now();
    const age = (now - cachedData.timestamp) / 1000; // em segundos

    // Verificar se está usando stale-while-revalidate e se os dados estão obsoletos
    if (
      config.strategy === CacheStrategy.STALE_WHILE_REVALIDATE &&
      config.staleTTL &&
      age > config.defaultTTL &&
      age <= config.staleTTL
    ) {
      return { data: cachedData.data, isStale: true };
    }

    // Dados frescos
    return { data: cachedData.data, isStale: false };
  } catch (error) {
    console.error(`Erro ao obter cache estratégico para ${contentType}:`, error);
    return { data: null, isStale: false };
  }
}

/**
 * Armazena dados no cache com metadados adicionais
 * @param contentType Tipo de conteúdo
 * @param params Parâmetros para geração da chave
 * @param data Dados a serem armazenados
 * @returns Verdadeiro se o cache foi armazenado com sucesso
 */
export async function setStrategicCache<T>(
  contentType: string,
  params: Record<string, any>,
  data: T
): Promise<boolean> {
  const config = cacheConfigs[contentType] || cacheConfigs.static;
  const cacheKey = generateCacheKey(contentType, params);

  try {
    // Armazenar dados com timestamp
    await setCache(
      cacheKey,
      {
        data,
        timestamp: Date.now(),
        isStale: false,
      },
      config.defaultTTL
    );

    return true;
  } catch (error) {
    console.error(`Erro ao armazenar cache estratégico para ${contentType}:`, error);
    return false;
  }
}

/**
 * Invalida cache com base em eventos
 * @param event Nome do evento
 * @param params Parâmetros adicionais do evento
 */
export async function invalidateCacheByEvent(
  event: string,
  params: Record<string, any> = {}
): Promise<void> {
  // Encontrar todos os tipos de conteúdo afetados pelo evento
  const affectedTypes = Object.entries(cacheConfigs)
    .filter(([_, config]) => config.invalidationEvents?.includes(event))
    .map(([type, _]) => type);

  // Invalidar cache para cada tipo afetado
  for (const contentType of affectedTypes) {
    const config = cacheConfigs[contentType];

    // Determinar padrão de invalidação
    let pattern: string;

    if (params.id) {
      // Invalidar apenas entradas específicas
      pattern = `${config.keyPrefix}:*${params.id}*`;
    } else {
      // Invalidar todas as entradas deste tipo
      pattern = `${config.keyPrefix}:*`;
    }

    // Executar invalidação
    await deleteCacheByPattern(pattern);
    console.log(`Cache invalidado para ${contentType} pelo evento ${event}`);
  }
}
