/**
 * Caso de uso para registro de usuário
 *
 * Este caso de uso implementa a lógica de negócio para registrar um novo usuário
 * no sistema. Seguindo a Clean Architecture, ele depende apenas de abstrações
 * (interfaces) e não de implementações concretas.
 */

import { User, UserRole } from '../../domain/entities/User';
import { UserRepository } from '../interfaces/repositories/UserRepository';
import { Logger } from '../../domain/interfaces/Logger';
import { nanoid } from 'nanoid';

/**
 * Dados para registro de usuário
 */
export interface RegisterUserData {
  /**
   * Nome do usuário
   */
  name: string;

  /**
   * Email do usuário
   */
  email: string;

  /**
   * Senha do usuário
   */
  password: string;

  /**
   * Papel do usuário (opcional, padrão: 'student')
   */
  role?: UserRole;
}

/**
 * Resultado do registro de usuário
 */
export interface RegisterUserResult {
  /**
   * Se o registro foi bem-sucedido
   */
  success: boolean;

  /**
   * Usuário registrado (se bem-sucedido)
   */
  user?: User;

  /**
   * Erro (se não for bem-sucedido)
   */
  error?: string;

  /**
   * Erros de validação (se não for bem-sucedido)
   */
  validationErrors?: Record<string, string>;
}

/**
 * Caso de uso para registro de usuário
 */
export class RegisterUserUseCase {
  /**
   * Cria uma nova instância de RegisterUserUseCase
   * @param userRepository - Repositório de usuários
   * @param passwordHasher - Serviço para hash de senha
   * @param logger - Serviço de logging
   */
  constructor(
    private readonly userRepository: UserRepository,
    private readonly passwordHasher: (password: string) => string,
    private readonly logger: Logger
  ) {}

  /**
   * Executa o caso de uso
   * @param data - Dados para registro de usuário
   * @returns Resultado do registro
   */
  async execute(data: RegisterUserData): Promise<RegisterUserResult> {
    try {
      this.logger.info('Registering new user', { email: data.email });

      // Validar dados
      const validationErrors = this.validateData(data);
      if (Object.keys(validationErrors).length > 0) {
        this.logger.warn('Validation errors during user registration', {
          email: data.email,
          errors: validationErrors
        });

        return {
          success: false,
          validationErrors
        };
      }

      // Verificar se o email já está em uso
      const existingUser = await this.userRepository.findByEmail(data.email);
      if (existingUser) {
        this.logger.warn('Email already in use', { email: data.email });

        return {
          success: false,
          error: 'Email already in use'
        };
      }

      // Criar hash da senha
      const passwordHash = this.passwordHasher(data.password);

      // Criar usuário
      const now = new Date();
      const user = new User(
        nanoid(),
        data.name,
        data.email,
        passwordHash,
        data.role || 'student',
        now,
        now
      );

      // Salvar usuário
      await this.userRepository.save(user);

      this.logger.info('User registered successfully', {
        userId: user.id,
        email: user.email
      });

      return {
        success: true,
        user
      };
    } catch (error) {
      this.logger.error('Error registering user', {
        email: data.email,
        error: error instanceof Error ? error.message : String(error)
      });

      return {
        success: false,
        error: 'Failed to register user'
      };
    }
  }

  /**
   * Valida os dados de registro
   * @param data - Dados para registro de usuário
   * @returns Erros de validação
   */
  private validateData(data: RegisterUserData): Record<string, string> {
    const errors: Record<string, string> = {};

    // Validar nome
    if (!data.name || data.name.trim().length < 3) {
      errors.name = 'Name must have at least 3 characters';
    }

    // Validar email
    if (!data.email) {
      errors.email = 'Email is required';
    } else {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(data.email)) {
        errors.email = 'Invalid email format';
      }
    }

    // Validar senha
    if (!data.password) {
      errors.password = 'Password is required';
    } else if (data.password.length < 6) {
      errors.password = 'Password must have at least 6 characters';
    }

    // Validar papel
    if (data.role && !['admin', 'teacher', 'student', 'parent'].includes(data.role)) {
      errors.role = 'Invalid role';
    }

    return errors;
  }
}
