---
import Dashboard from '@components/data/Dashboard.astro';
import { checkAdmin } from '@helpers/authGuard';
/**
 * Página de Dashboard Administrativo
 *
 * Dashboard principal do painel administrativo.
 * Parte da implementação da tarefa 8.8.1 - Painel administrativo
 */
import AdminLayout from '@layouts/AdminLayout.astro';

// Protege a rota verificando se o usuário é admin
const authRedirect = await checkAdmin(Astro);
if (authRedirect) return authRedirect;
---

<AdminLayout title="Painel Principal">
  <Dashboard />
</AdminLayout>