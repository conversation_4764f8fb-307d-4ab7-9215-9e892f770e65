---
import InputText from '@components/form/InputText.astro';
import AdminLayout from '@layouts/AdminLayout.astro';
---
<AdminLayout title="Cadastro de Tipo de Usuário">
  <template>
    <div class="search-container flex justify-between mb-4">
      <InputText id="search-input" label="Pesquisar" name="search" placeholder="Pesquisar tipos de usuário..." />
      <div>
        <button class="btn btn-primary" id="handleSearch">Pesquisar</button>
        <button class="btn btn-secondary ml-2" id="navigateToNewUserType">Novo</button>
      </div>
    </div>

    <ul class="user-type-list list-disc pl-5">
    </ul>
  </template>

  <style>
    .search-container {
      display: flex;
      justify-content: space-between;
      margin-bottom: 20px;
    }
    .user-type-list {
      list-style: none;
      padding: 0;
    }
    .user-type-list li {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10px;
    }
  </style>
</AdminLayout>

<script>
  import { actions } from "astro:actions";

  interface UserType {
    ulid_user_type: string;
    type: string;
  }

  let userTypes: UserType[] = []; // Declare and initialize the 'userTypes' variable

  async function handleSearch() {
    const searchInputElement = document.getElementById('search-input') as HTMLInputElement;
    if (searchInputElement) {
      const searchInput = searchInputElement.value;
      if (searchInput !== null && searchInput !== undefined) {
        // Lógica para buscar tipos de usuário com base no input
        const param = { filter: "type", type: searchInput };
        loadUserTypes(param);
      } else {
        console.error('O valor do input de pesquisa é nulo ou indefinido.');
      }
    } else {
      console.error('Elemento de input de pesquisa não encontrado.');
    }
  }

  function navigateToNewUserType() {
    // Lógica para redirecionar para o novo tipo de usuário
    window.location.href = '/admin/register/user/type/new';
  }

  async function handleEdit(ulid_user_type: string) {
    // Lógica para redirecionar para o tipo de usuário
    window.location.href = `/admin/register/user/type/${ulid_user_type}`;
  }

  async function handleDelete(ulid_user_type: string) {
    // Lógica para excluir o tipo de usuário
    if (!confirm('Tem certeza que deseja excluir este tipo de usuário?')) return;
    await actions.userTypeAction.delete({ ulid_user_type });
    await loadUserTypes({ filter: "all" }); // Recarregar tipos de usuário após exclusão
  }

  async function loadUserTypes(params: any) {
    const result = await actions.userTypeAction.read(params);
    userTypes = result.data || [];

    // Apagar os elementos da lista
    const userTypesList = document.querySelector('.user-type-list');

    if (userTypesList) {
      userTypesList.innerHTML = '';

      // Inserir o cabeçalho
      const titleRow = document.createElement('li');
      titleRow.innerHTML = `
          <strong>Nome</strong>
          <strong>Ações</strong>
      `;
      userTypesList.appendChild(titleRow);

      if (userTypes.length === 0) {
        const noDataMessage = document.createElement('li');
        noDataMessage.textContent = 'Nenhum resultado encontrado.';
        userTypesList.appendChild(noDataMessage);
        return;
      }

      // Inserir novos tipos de usuário
      userTypes.forEach((userType: UserType) => {
        const listItem = document.createElement('li');
        listItem.innerHTML = `
            <span>${userType.type}</span>
            <div>
                <button type="button" class="btn btn-warning mr-2" onclick="handleEdit('${userType.ulid_user_type}')">Alterar</button>
                <button type="button" class="btn btn-error" onclick="handleDelete('${userType.ulid_user_type}')">Excluir</button>
            </div>
        `;
        userTypesList.appendChild(listItem);
      });
    }
  }

  loadUserTypes({ filter: "all" });
</script>