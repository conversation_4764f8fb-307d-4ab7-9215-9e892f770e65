/**
 * Repositório de Produtos com Cache
 *
 * Esta implementação do repositório de produtos utiliza cache para melhorar
 * a performance, seguindo os princípios da Clean Architecture.
 */

import { CacheService } from '../../../application/interfaces/CacheService';
import { Product, ProductCategory } from '../../../domain/entities/Product';
import {
  PaginatedProducts,
  ProductFilterOptions,
  ProductRepository,
} from '../../interfaces/ProductRepository';

/**
 * Repositório de Produtos com Cache
 */
export class CachedProductRepository implements ProductRepository {
  /**
   * Cria uma nova instância do repositório
   *
   * @param repository Repositório de produtos subjacente
   * @param cacheService Serviço de cache
   * @param cacheTtl Tempo de vida do cache em segundos
   */
  constructor(
    private readonly repository: ProductRepository,
    private readonly cacheService: CacheService,
    private readonly cacheTtl: number = 300 // 5 minutos
  ) {}

  /**
   * Constrói uma chave de cache baseada nas opções de filtro
   *
   * @param options Opções de filtro
   * @returns Chave de cache
   */
  private buildCacheKey(options?: ProductFilterOptions): string {
    if (!options) {
      return 'all';
    }

    return Object.entries(options)
      .filter(([_, value]) => value !== undefined)
      .map(([key, value]) => `${key}:${JSON.stringify(value)}`)
      .join('|');
  }

  /**
   * Busca um produto pelo ID
   *
   * @param id ID do produto
   * @returns Produto encontrado ou null se não existir
   */
  async findById(id: string): Promise<Product | null> {
    // Chave de cache
    const cacheKey = `product:${id}`;

    // Tentar obter do cache
    const cachedProduct = await this.cacheService.get<Product>(cacheKey);

    if (cachedProduct) {
      return cachedProduct;
    }

    // Buscar do repositório
    const product = await this.repository.findById(id);

    // Armazenar no cache se encontrado
    if (product) {
      await this.cacheService.set(cacheKey, product, this.cacheTtl);
    }

    return product;
  }

  /**
   * Busca produtos com filtros
   *
   * @param options Opções de filtro
   * @returns Resultado paginado de produtos
   */
  async findAll(options?: ProductFilterOptions): Promise<PaginatedProducts> {
    // Chave de cache
    const cacheKey = `products:${this.buildCacheKey(options)}`;

    // Tentar obter do cache
    const cachedProducts = await this.cacheService.get<PaginatedProducts>(cacheKey);

    if (cachedProducts) {
      return cachedProducts;
    }

    // Buscar do repositório
    const products = await this.repository.findAll(options);

    // Armazenar no cache
    await this.cacheService.set(cacheKey, products, this.cacheTtl);

    return products;
  }

  /**
   * Busca produtos por categoria
   *
   * @param category Categoria para filtro
   * @param options Opções de filtro adicionais
   * @returns Resultado paginado de produtos
   */
  async findByCategory(
    category: ProductCategory,
    options?: Omit<ProductFilterOptions, 'category'>
  ): Promise<PaginatedProducts> {
    return this.findAll({ ...options, category });
  }

  /**
   * Busca produtos em destaque
   *
   * @param options Opções de filtro adicionais
   * @returns Resultado paginado de produtos
   */
  async findFeatured(options?: Omit<ProductFilterOptions, 'featured'>): Promise<PaginatedProducts> {
    return this.findAll({ ...options, featured: true });
  }

  /**
   * Busca produtos relacionados a um produto específico
   *
   * @param productId ID do produto de referência
   * @param limit Limite de produtos a serem retornados
   * @returns Lista de produtos relacionados
   */
  async findRelated(productId: string, limit?: number): Promise<Product[]> {
    // Chave de cache
    const cacheKey = `products:related:${productId}:${limit || 'all'}`;

    // Tentar obter do cache
    const cachedProducts = await this.cacheService.get<Product[]>(cacheKey);

    if (cachedProducts) {
      return cachedProducts;
    }

    // Buscar do repositório
    const products = await this.repository.findRelated(productId, limit);

    // Armazenar no cache
    await this.cacheService.set(cacheKey, products, this.cacheTtl);

    return products;
  }

  /**
   * Busca produtos recentes
   *
   * @param limit Limite de produtos a serem retornados
   * @returns Lista de produtos recentes
   */
  async findRecent(limit?: number): Promise<Product[]> {
    // Chave de cache
    const cacheKey = `products:recent:${limit || 'all'}`;

    // Tentar obter do cache
    const cachedProducts = await this.cacheService.get<Product[]>(cacheKey);

    if (cachedProducts) {
      return cachedProducts;
    }

    // Buscar do repositório
    const products = await this.repository.findRecent(limit);

    // Armazenar no cache
    await this.cacheService.set(cacheKey, products, this.cacheTtl);

    return products;
  }

  /**
   * Salva um produto (cria ou atualiza)
   *
   * @param product Produto a ser salvo
   * @returns Produto salvo
   */
  async save(product: Product): Promise<Product> {
    // Salvar no repositório
    const savedProduct = await this.repository.save(product);

    // Invalidar caches
    await this.cacheService.delete(`product:${savedProduct.id}`);
    await this.cacheService.deletePattern('products:*');

    return savedProduct;
  }

  /**
   * Remove um produto
   *
   * @param id ID do produto a ser removido
   * @returns true se removido com sucesso, false caso contrário
   */
  async remove(id: string): Promise<boolean> {
    // Remover do repositório
    const result = await this.repository.remove(id);

    // Invalidar caches
    if (result) {
      await this.cacheService.delete(`product:${id}`);
      await this.cacheService.deletePattern('products:*');
    }

    return result;
  }

  /**
   * Atualiza o estoque de um produto
   *
   * @param id ID do produto
   * @param quantity Nova quantidade em estoque
   * @returns Produto atualizado
   */
  async updateStock(id: string, quantity: number): Promise<Product> {
    // Atualizar no repositório
    const updatedProduct = await this.repository.updateStock(id, quantity);

    // Invalidar caches
    await this.cacheService.delete(`product:${id}`);
    await this.cacheService.deletePattern('products:*');

    return updatedProduct;
  }
}
