/**
 * Firebase Push Notification Service
 *
 * Implementação do serviço de notificações push usando Firebase Cloud Messaging.
 * Parte da implementação da tarefa 8.5.2 - Notificações externas
 */

import * as admin from 'firebase-admin';
import {
  PushNotificationAction,
  PushNotificationOptions,
  PushNotificationResult,
  PushNotificationService,
  PushNotificationTarget,
} from '../../domain/services/PushNotificationService';

export class FirebasePushNotificationService implements PushNotificationService {
  private app: admin.app.App;
  private scheduledNotifications: Map<string, NodeJS.Timeout> = new Map();

  constructor(config: {
    projectId: string;
    clientEmail: string;
    privateKey: string;
    databaseURL?: string;
  }) {
    this.app = admin.initializeApp({
      credential: admin.credential.cert({
        projectId: config.projectId,
        clientEmail: config.clientEmail,
        privateKey: config.privateKey.replace(/\\n/g, '\n'),
      }),
      databaseURL: config.databaseURL,
    });
  }

  async sendPushNotification(
    target: PushNotificationTarget,
    options: PushNotificationOptions
  ): Promise<PushNotificationResult> {
    try {
      // Verificar se é uma notificação agendada
      if (options.scheduledAt && options.scheduledAt > new Date()) {
        return this.schedulePushNotification(target, options, options.scheduledAt);
      }

      // Preparar a mensagem
      const message = this.buildMessage(target, options);

      // Enviar a notificação
      let response;

      if (target.deviceToken) {
        // Enviar para um único dispositivo
        response = await admin.messaging().send(message);

        return {
          success: true,
          messageId: response,
          timestamp: new Date(),
          deviceCount: 1,
          successCount: 1,
          failureCount: 0,
        };
      }
      if (target.deviceTokens && target.deviceTokens.length > 0) {
        // Enviar para múltiplos dispositivos
        response = await admin.messaging().sendMulticast({
          tokens: target.deviceTokens,
          ...message,
        });

        return {
          success: response.failureCount === 0,
          messageId: `batch_${Date.now()}`,
          timestamp: new Date(),
          deviceCount: response.responses.length,
          successCount: response.successCount,
          failureCount: response.failureCount,
          failureDetails: response.responses
            .map((resp, index) => ({
              deviceToken: target.deviceTokens![index],
              error: resp.error?.message || '',
            }))
            .filter((detail) => detail.error),
        };
      }
      if (target.topic) {
        // Enviar para um tópico
        response = await admin.messaging().send({
          topic: target.topic,
          ...message,
        });

        return {
          success: true,
          messageId: response,
          timestamp: new Date(),
          deviceCount: undefined, // Não sabemos quantos dispositivos estão inscritos no tópico
          successCount: undefined,
          failureCount: 0,
        };
      }
      if (target.condition) {
        // Enviar com base em uma condição
        response = await admin.messaging().send({
          condition: target.condition,
          ...message,
        });

        return {
          success: true,
          messageId: response,
          timestamp: new Date(),
          deviceCount: undefined,
          successCount: undefined,
          failureCount: 0,
        };
      }
      throw new Error(
        'Alvo de notificação inválido. Forneça deviceToken, deviceTokens, topic ou condition.'
      );
    } catch (error) {
      console.error('Erro ao enviar notificação push:', error);

      return {
        success: false,
        error:
          error instanceof Error ? error.message : 'Erro desconhecido ao enviar notificação push',
        timestamp: new Date(),
        deviceCount: 0,
        successCount: 0,
        failureCount: 1,
      };
    }
  }

  async sendPushNotificationToUser(
    userId: string,
    options: PushNotificationOptions
  ): Promise<PushNotificationResult> {
    try {
      // Obter tokens de dispositivo do usuário
      const deviceTokens = await this.getUserDeviceTokens(userId);

      if (deviceTokens.length === 0) {
        return {
          success: false,
          error: 'Usuário não possui dispositivos registrados',
          timestamp: new Date(),
          deviceCount: 0,
          successCount: 0,
          failureCount: 0,
        };
      }

      // Enviar notificação para todos os dispositivos do usuário
      return this.sendPushNotification({ deviceTokens }, options);
    } catch (error) {
      console.error(`Erro ao enviar notificação push para usuário ${userId}:`, error);

      return {
        success: false,
        error:
          error instanceof Error ? error.message : 'Erro desconhecido ao enviar notificação push',
        timestamp: new Date(),
        deviceCount: 0,
        successCount: 0,
        failureCount: 1,
      };
    }
  }

  async sendPushNotificationToTopic(
    topic: string,
    options: PushNotificationOptions
  ): Promise<PushNotificationResult> {
    return this.sendPushNotification({ topic }, options);
  }

  async schedulePushNotification(
    target: PushNotificationTarget,
    options: PushNotificationOptions,
    scheduledAt: Date
  ): Promise<PushNotificationResult> {
    try {
      const now = new Date();

      if (scheduledAt <= now) {
        // Se a data agendada já passou, enviar imediatamente
        return this.sendPushNotification(target, {
          ...options,
          scheduledAt: undefined,
        });
      }

      // Gerar ID para a notificação agendada
      const messageId = `scheduled_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;

      // Calcular o atraso em milissegundos
      const delay = scheduledAt.getTime() - now.getTime();

      // Agendar o envio
      const timeout = setTimeout(async () => {
        await this.sendPushNotification(target, {
          ...options,
          scheduledAt: undefined,
        });

        // Remover da lista de notificações agendadas
        this.scheduledNotifications.delete(messageId);
      }, delay);

      // Armazenar o timeout para possível cancelamento
      this.scheduledNotifications.set(messageId, timeout);

      return {
        success: true,
        messageId,
        timestamp: now,
        deviceCount: undefined,
        successCount: undefined,
        failureCount: 0,
      };
    } catch (error) {
      console.error('Erro ao agendar notificação push:', error);

      return {
        success: false,
        error:
          error instanceof Error ? error.message : 'Erro desconhecido ao agendar notificação push',
        timestamp: new Date(),
        deviceCount: 0,
        successCount: 0,
        failureCount: 1,
      };
    }
  }

  async cancelScheduledPushNotification(messageId: string): Promise<boolean> {
    const timeout = this.scheduledNotifications.get(messageId);

    if (timeout) {
      clearTimeout(timeout);
      this.scheduledNotifications.delete(messageId);
      return true;
    }

    return false;
  }

  async registerDeviceToken(
    userId: string,
    deviceToken: string,
    deviceInfo?: {
      platform: 'ios' | 'android' | 'web';
      model?: string;
      osVersion?: string;
      appVersion?: string;
    }
  ): Promise<boolean> {
    try {
      // Em um cenário real, aqui seria feito o registro no Firestore ou Realtime Database
      // Por enquanto, apenas simulamos o registro
      console.log(`Registrando token ${deviceToken} para usuário ${userId}`);

      return true;
    } catch (error) {
      console.error(`Erro ao registrar token para usuário ${userId}:`, error);
      return false;
    }
  }

  async unregisterDeviceToken(userId: string, deviceToken: string): Promise<boolean> {
    try {
      // Em um cenário real, aqui seria feita a remoção no Firestore ou Realtime Database
      // Por enquanto, apenas simulamos a remoção
      console.log(`Removendo token ${deviceToken} para usuário ${userId}`);

      return true;
    } catch (error) {
      console.error(`Erro ao remover token para usuário ${userId}:`, error);
      return false;
    }
  }

  async subscribeToTopic(userIds: string | string[], topic: string): Promise<boolean> {
    try {
      // Em um cenário real, aqui seria feita a inscrição no tópico
      // Por enquanto, apenas simulamos a inscrição
      const users = Array.isArray(userIds) ? userIds : [userIds];
      console.log(`Inscrevendo usuários ${users.join(', ')} no tópico ${topic}`);

      return true;
    } catch (error) {
      console.error(`Erro ao inscrever usuários no tópico ${topic}:`, error);
      return false;
    }
  }

  async unsubscribeFromTopic(userIds: string | string[], topic: string): Promise<boolean> {
    try {
      // Em um cenário real, aqui seria feito o cancelamento da inscrição no tópico
      // Por enquanto, apenas simulamos o cancelamento
      const users = Array.isArray(userIds) ? userIds : [userIds];
      console.log(`Cancelando inscrição dos usuários ${users.join(', ')} no tópico ${topic}`);

      return true;
    } catch (error) {
      console.error(`Erro ao cancelar inscrição de usuários no tópico ${topic}:`, error);
      return false;
    }
  }

  async getUserDeviceTokens(userId: string): Promise<string[]> {
    try {
      // Em um cenário real, aqui seria feita a consulta no Firestore ou Realtime Database
      // Por enquanto, retornamos tokens fictícios
      return [`device_token_${userId}_1`, `device_token_${userId}_2`];
    } catch (error) {
      console.error(`Erro ao obter tokens do usuário ${userId}:`, error);
      return [];
    }
  }

  async getPushNotificationStats(
    startDate?: Date,
    endDate?: Date
  ): Promise<{
    sent: number;
    delivered: number;
    opened: number;
    failed: number;
    deliveryRate: number;
    openRate: number;
  }> {
    // Em um cenário real, aqui seria feita uma consulta à API do Firebase
    // Por enquanto, retornamos estatísticas fictícias
    return {
      sent: 100,
      delivered: 95,
      opened: 60,
      failed: 5,
      deliveryRate: 95,
      openRate: 60,
    };
  }

  private buildMessage(
    target: PushNotificationTarget,
    options: PushNotificationOptions
  ): admin.messaging.Message {
    // Construir a notificação
    const notification: admin.messaging.Notification = {
      title: options.title,
      body: options.body,
      imageUrl: options.image,
    };

    // Construir os dados adicionais
    const data: Record<string, string> = {
      ...options.data,
      title: options.title,
      body: options.body,
      timestamp: options.timestamp?.toString() || Date.now().toString(),
    };

    // Adicionar ações, se fornecidas
    if (options.actions && options.actions.length > 0) {
      data.actions = JSON.stringify(options.actions);
    }

    // Construir as opções do Android
    const android: admin.messaging.AndroidConfig = {
      priority: options.priority === 'high' ? 'high' : 'normal',
      ttl: options.ttl ? options.ttl * 1000 : undefined, // Converter para milissegundos
      notification: {
        icon: options.icon,
        sound: options.sound,
        clickAction: 'FLUTTER_NOTIFICATION_CLICK',
      },
    };

    // Construir as opções do iOS
    const apns: admin.messaging.ApnsConfig = {
      payload: {
        aps: {
          badge: options.badge ? Number.parseInt(options.badge) : undefined,
          sound: options.sound || 'default',
          contentAvailable: true,
          mutableContent: true,
        },
      },
    };

    // Construir a mensagem completa
    const message: admin.messaging.Message = {
      notification,
      data,
      android,
      apns,
    };

    // Adicionar o destino
    if (target.deviceToken) {
      message.token = target.deviceToken;
    } else if (target.topic) {
      message.topic = target.topic;
    } else if (target.condition) {
      message.condition = target.condition;
    }

    return message;
  }
}
