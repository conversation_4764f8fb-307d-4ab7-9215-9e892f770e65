-- Migração para criar tabelas de logging do Kafka

-- Tabela para armazenar logs do Kafka
CREATE TABLE IF NOT EXISTS tab_kafka_logs (
  log_id UUID PRIMARY KEY,
  timestamp TIMESTAMP NOT NULL,
  level VARCHAR(10) NOT NULL,
  component VARCHAR(100) NOT NULL,
  message TEXT NOT NULL,
  hostname VARCHAR(255),
  process_id INTEGER,
  metadata JSONB,
  created_at TIMESTAMP NOT NULL DEFAULT NOW()
);

-- Índices para melhorar performance de consultas
CREATE INDEX IF NOT EXISTS idx_kafka_logs_timestamp ON tab_kafka_logs (timestamp);
CREATE INDEX IF NOT EXISTS idx_kafka_logs_level ON tab_kafka_logs (level);
CREATE INDEX IF NOT EXISTS idx_kafka_logs_component ON tab_kafka_logs (component);
CREATE INDEX IF NOT EXISTS idx_kafka_logs_hostname ON tab_kafka_logs (hostname);

-- Função para limpar logs antigos
CREATE OR REPLACE FUNCTION fn_clean_kafka_logs(p_days INTEGER)
RETURNS INTEGER AS $$
DECLARE
  v_count INTEGER;
BEGIN
  DELETE FROM tab_kafka_logs
  WHERE timestamp < NOW() - (p_days || ' days')::INTERVAL;
  
  GET DIAGNOSTICS v_count = ROW_COUNT;
  RETURN v_count;
END;
$$ LANGUAGE plpgsql;

-- Visualização para resumo de logs por nível
CREATE OR REPLACE VIEW vw_kafka_logs_summary AS
SELECT
  date_trunc('hour', timestamp) AS hour,
  level,
  component,
  COUNT(*) AS log_count
FROM
  tab_kafka_logs
WHERE
  timestamp > NOW() - INTERVAL '7 days'
GROUP BY
  date_trunc('hour', timestamp),
  level,
  component
ORDER BY
  hour DESC,
  level,
  component;

-- Visualização para erros recentes
CREATE OR REPLACE VIEW vw_kafka_recent_errors AS
SELECT
  log_id,
  timestamp,
  level,
  component,
  message,
  hostname,
  process_id,
  metadata
FROM
  tab_kafka_logs
WHERE
  level IN ('ERROR', 'FATAL')
  AND timestamp > NOW() - INTERVAL '24 hours'
ORDER BY
  timestamp DESC;

-- Função para obter logs por componente
CREATE OR REPLACE FUNCTION fn_get_kafka_logs_by_component(
  p_component VARCHAR,
  p_start_time TIMESTAMP,
  p_end_time TIMESTAMP,
  p_min_level VARCHAR DEFAULT 'INFO'
)
RETURNS TABLE (
  log_id UUID,
  timestamp TIMESTAMP,
  level VARCHAR,
  component VARCHAR,
  message TEXT,
  hostname VARCHAR,
  process_id INTEGER,
  metadata JSONB
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    kl.log_id,
    kl.timestamp,
    kl.level,
    kl.component,
    kl.message,
    kl.hostname,
    kl.process_id,
    kl.metadata
  FROM
    tab_kafka_logs kl
  WHERE
    kl.component LIKE p_component || '%'
    AND kl.timestamp BETWEEN p_start_time AND p_end_time
    AND (
      p_min_level = 'DEBUG' OR
      (p_min_level = 'INFO' AND kl.level IN ('INFO', 'WARN', 'ERROR', 'FATAL')) OR
      (p_min_level = 'WARN' AND kl.level IN ('WARN', 'ERROR', 'FATAL')) OR
      (p_min_level = 'ERROR' AND kl.level IN ('ERROR', 'FATAL')) OR
      (p_min_level = 'FATAL' AND kl.level = 'FATAL')
    )
  ORDER BY
    kl.timestamp DESC;
END;
$$ LANGUAGE plpgsql;

-- Função para obter contagem de logs por nível
CREATE OR REPLACE FUNCTION fn_get_kafka_log_counts(
  p_start_time TIMESTAMP,
  p_end_time TIMESTAMP
)
RETURNS TABLE (
  level VARCHAR,
  log_count BIGINT
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    kl.level,
    COUNT(*) AS log_count
  FROM
    tab_kafka_logs kl
  WHERE
    kl.timestamp BETWEEN p_start_time AND p_end_time
  GROUP BY
    kl.level
  ORDER BY
    CASE
      WHEN kl.level = 'DEBUG' THEN 1
      WHEN kl.level = 'INFO' THEN 2
      WHEN kl.level = 'WARN' THEN 3
      WHEN kl.level = 'ERROR' THEN 4
      WHEN kl.level = 'FATAL' THEN 5
      ELSE 6
    END;
END;
$$ LANGUAGE plpgsql;
