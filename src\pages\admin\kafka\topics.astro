---
import AdminLayout from '@layouts/AdminLayout.astro';
import { kafka } from '@config/kafka';
import { formatNumber, formatDate } from '@utils/formatters';

// Obter lista de tópicos
const admin = kafka.admin();
await admin.connect();

const metadata = await admin.fetchTopicMetadata();
const topics = metadata.topics;

// Ordenar tópicos por nome
topics.sort((a, b) => a.name.localeCompare(b.name));

// Agrupar tópicos por domínio
const topicsByDomain = topics.reduce((acc, topic) => {
  const parts = topic.name.split('.');
  const domain = parts.length > 0 ? parts[0] : 'outros';
  
  if (!acc[domain]) {
    acc[domain] = [];
  }
  
  acc[domain].push(topic);
  
  return acc;
}, {});

// Ordenar domínios
const domains = Object.keys(topicsByDomain).sort();

await admin.disconnect();
---

<AdminLayout title="Tópicos Kafka">
  <div class="container mx-auto px-4 py-8">
    <h1 class="text-3xl font-bold mb-8">Tópicos Kafka</h1>
    
    <div class="bg-white rounded-lg shadow overflow-hidden mb-8">
      <div class="p-6 border-b">
        <div class="flex items-center justify-between">
          <h2 class="text-xl font-semibold">Visão Geral</h2>
          <span class="text-lg font-medium">{topics.length} tópicos</span>
        </div>
      </div>
      
      <div class="p-6">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <!-- Total de tópicos -->
          <div class="bg-blue-50 rounded-lg p-4">
            <div class="text-blue-800 text-sm font-medium mb-2">Total de Tópicos</div>
            <div class="text-2xl font-bold text-blue-900">{topics.length}</div>
          </div>
          
          <!-- Total de partições -->
          <div class="bg-green-50 rounded-lg p-4">
            <div class="text-green-800 text-sm font-medium mb-2">Total de Partições</div>
            <div class="text-2xl font-bold text-green-900">
              {topics.reduce((sum, topic) => sum + topic.partitions.length, 0)}
            </div>
          </div>
          
          <!-- Fator de replicação médio -->
          <div class="bg-purple-50 rounded-lg p-4">
            <div class="text-purple-800 text-sm font-medium mb-2">Fator de Replicação Médio</div>
            <div class="text-2xl font-bold text-purple-900">
              {(topics.reduce((sum, topic) => {
                const replicationFactor = topic.partitions.length > 0 
                  ? topic.partitions[0].replicas.length 
                  : 0;
                return sum + replicationFactor;
              }, 0) / (topics.length || 1)).toFixed(1)}
            </div>
          </div>
          
          <!-- Domínios -->
          <div class="bg-yellow-50 rounded-lg p-4">
            <div class="text-yellow-800 text-sm font-medium mb-2">Domínios</div>
            <div class="text-2xl font-bold text-yellow-900">{domains.length}</div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Tópicos por domínio -->
    <div class="space-y-6">
      {domains.map((domain) => (
        <div class="bg-white rounded-lg shadow overflow-hidden">
          <div class="p-4 bg-gray-50 border-b">
            <h2 class="text-lg font-semibold">{domain}</h2>
          </div>
          
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Nome</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Partições</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Replicação</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ISRs</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ações</th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                {topicsByDomain[domain].map((topic) => {
                  // Calcular estatísticas do tópico
                  const partitionCount = topic.partitions.length;
                  const replicationFactor = partitionCount > 0 ? topic.partitions[0].replicas.length : 0;
                  
                  // Verificar se há partições sub-replicadas
                  const underReplicatedPartitions = topic.partitions.filter(
                    partition => partition.replicas.length > partition.isr.length
                  ).length;
                  
                  // Verificar se há partições offline
                  const offlinePartitions = topic.partitions.filter(
                    partition => partition.isr.length === 0 || partition.leader < 0
                  ).length;
                  
                  return (
                    <tr>
                      <td class="px-6 py-4 whitespace-nowrap">
                        <a href={`/admin/kafka/topic/${topic.name}`} class="text-blue-600 hover:text-blue-900">
                          {topic.name}
                        </a>
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap">{partitionCount}</td>
                      <td class="px-6 py-4 whitespace-nowrap">
                        <span class={replicationFactor < 2 ? 'text-red-600' : 'text-green-600'}>
                          {replicationFactor}x
                        </span>
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap">
                        {underReplicatedPartitions > 0 || offlinePartitions > 0 ? (
                          <div class="flex items-center">
                            {underReplicatedPartitions > 0 && (
                              <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-yellow-100 text-yellow-800 mr-2">
                                {underReplicatedPartitions} sub-replicada(s)
                              </span>
                            )}
                            {offlinePartitions > 0 && (
                              <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800">
                                {offlinePartitions} offline
                              </span>
                            )}
                          </div>
                        ) : (
                          <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                            Saudável
                          </span>
                        )}
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm">
                        <a href={`/admin/kafka/topic/${topic.name}`} class="text-blue-600 hover:text-blue-900 mr-3">
                          Detalhes
                        </a>
                        <a href={`/admin/kafka/topic/${topic.name}/messages`} class="text-blue-600 hover:text-blue-900">
                          Mensagens
                        </a>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        </div>
      ))}
    </div>
    
    <div class="mt-8 flex justify-between items-center">
      <a href="/admin/kafka/dashboard" class="text-blue-600 hover:text-blue-800">
        &larr; Voltar para Dashboard
      </a>
      
      <a href="/admin/kafka/topics" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded">
        Atualizar Lista
      </a>
    </div>
  </div>
</AdminLayout>
---
