/**
 * API de Visualização de FAQ
 *
 * Endpoint para registrar visualizações de itens de FAQ.
 * Parte da implementação da tarefa 8.7.1 - Implementação de FAQ interativo
 */

import type { APIRoute } from 'astro';
import { FaqRepository } from '../../../domain/repositories/FaqRepository';
import { PostgresFaqRepository } from '../../../infrastructure/database/repositories/PostgresFaqRepository';

// Inicializar repositório
const faqRepository: FaqRepository = new PostgresFaqRepository();

export const POST: APIRoute = async ({ request }) => {
  try {
    // Obter dados da requisição
    const body = await request.json();
    const { id } = body;

    // Validar dados básicos
    if (!id) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'ID do item de FAQ é obrigatório.',
        }),
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    // Verificar se o item existe
    const faqItem = await faqRepository.getById(id);

    if (!faqItem) {
      return new Response(
        JSON.stringify({
          success: false,
          error: `Item de FAQ com ID ${id} não encontrado.`,
        }),
        {
          status: 404,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    // Incrementar o contador de visualizações
    const success = await faqRepository.incrementViewCount(id);

    if (success) {
      return new Response(
        JSON.stringify({
          success: true,
        }),
        {
          status: 200,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }
    return new Response(
      JSON.stringify({
        success: false,
        error: `Falha ao incrementar contador de visualizações para o item ${id}.`,
      }),
      {
        status: 400,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  } catch (error) {
    console.error('Erro ao processar visualização de FAQ:', error);

    return new Response(
      JSON.stringify({
        success: false,
        error: 'Ocorreu um erro ao processar a visualização. Por favor, tente novamente.',
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  }
};
