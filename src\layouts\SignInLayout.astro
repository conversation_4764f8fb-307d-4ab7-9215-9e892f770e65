---
import { actions } from 'astro:actions';
import SignContainer from '@components/form/SignContainer.astro';
import MainHeaderSign from '@components/layout/MainHeaderSign.astro';
import BaseLayout from './BaseLayout.astro';

const { redirect } = Astro;
// Ainda usando authAction até que jwtAuthAction seja registrado
const actionResult = await Astro.getActionResult(actions.authAction.signin);

// Se já estiver autenticado, redireciona para a página principal
if (Astro.locals.user) {
  return redirect('/');
}

// Se o login foi bem sucedido, redireciona para a página principal
if (actionResult?.data?.success) {
  return redirect('/');
}

const errorMessage = actionResult?.data?.error;
---

<script>
  const emailInput = document.querySelector('input[name="email"]') as HTMLInputElement;
  const passwordInput = document.querySelector('input[name="password"]') as HTMLInputElement;
  const loginForm = document.querySelector('form') as HTMLFormElement;
  const loginButton = document.querySelector('button[type="submit"]') as HTMLButtonElement;
  const errorElement = document.getElementById('login-error') as HTMLDivElement;

  let isSubmitting = false;

  function validateEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  function clientValidation(event: Event): boolean {
    const email = emailInput.value.trim();
    const password = passwordInput.value;

    // Validação básica
    if (!email || !password) {
      showError("Por favor, preencha todos os campos");
      event.preventDefault();
      return false;
    }

    if (!validateEmail(email)) {
      showError("Por favor, insira um email válido");
      event.preventDefault();
      return false;
    }

    if (isSubmitting) {
      event.preventDefault();
      return false;
    }

    isSubmitting = true;
    loginButton.classList.add('loading');

    // Permitir o envio do formulário
    return true;
  }

  function showError(message: string) {
    if (errorElement) {
      errorElement.textContent = message;
      errorElement.removeAttribute('hidden');
    }
  }

  // Atribuir validação ao formulário
  if (loginForm) {
    loginForm.addEventListener('submit', clientValidation);
  }
</script>

<BaseLayout title="Entrar">
  <MainHeaderSign/>
  <SignContainer title="Acesse sua conta!">
    <form method="POST" action={actions.authAction.signin}>
      <div class="c-email">
        <label class="input input-bordered input-primary input-sm flex items-center gap-2 c-shadowed">
          <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 16 16"
          fill="currentColor"
          class="h-4 w-4 opacity-70">
          <path
            d="M2.5 3A1.5 1.5 0 0 0 1 4.5v.793c.**************.076.032L7.674 8.51c.206.1.446.1.652 0l6.598-3.185A.755.755 0 0 1 15 5.293V4.5A1.5 1.5 0 0 0 13.5 3h-11Z" />
          <path
            d="M15 6.954 8.978 9.86a2.25 2.25 0 0 1-1.956 0L1 6.954V11.5A1.5 1.5 0 0 0 2.5 13h11a1.5 1.5 0 0 0 1.5-1.5V6.954Z" />
          </svg>
          <input type="text" name="email" class="grow" placeholder="Email" />
        </label>
      </div>
      <div class="text-right c-password">
        <label class="input input-bordered input-primary input-sm flex items-center gap-2 c-shadowed">
          <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 16 16"
          fill="currentColor"
          class="h-4 w-4 opacity-70">
          <path
          fill-rule="evenodd"
              d="M14 6a4 4 0 0 1-4.899 3.899l-1.955 1.955a.5.5 0 0 1-.353.146H5v1.5a.5.5 0 0 1-.5.5h-2a.5.5 0 0 1-.5-.5v-2.293a.5.5 0 0 1 .146-.353l3.955-3.955A4 4 0 1 1 14 6Zm-4-2a.75.75 0 0 0 0 ******* 0 0 1 .********* 0 0 0 1.5 0 2 2 0 0 0-2-2Z"
              clip-rule="evenodd" />
          </svg>
          <input type="password" name="password" class="grow" placeholder="Senha" />
        </label>
        <a href="/recuperar-senha" class="text-sm c-text-shadow-sign-link">Esqueci a senha</a>
      </div>
      <div class="text-error text-sm mt-2" id="login-error" hidden>

      </div>
      {errorMessage && (
        <div class="text-error text-sm mt-2" id="login-error">
          {errorMessage}
        </div>
      )}
      <div class="card-actions l-btn-commands flex justify-between">
        <a href="/signup" class="btn btn-error btn-sm c-shadowed">Cadastre-se</a>
        <button type="submit" class="btn btn-error btn-sm c-shadowed">Confirmar</button>
      </div>
    </form>
  </SignContainer>
</BaseLayout>

<style>
  a {
    width: 48%;
  }

  .l-btn-commands {
    margin: 8px 0px 0px 0px;
  }

  .l-link {
    text-decoration: none;
  }
  /* @media (width > 320px) {
    .figure {
      width: calc(
             ((var(--image-max-width) - var(--image-min-width)) /
             (100vw - 320px)));
    }
  } */
</style>