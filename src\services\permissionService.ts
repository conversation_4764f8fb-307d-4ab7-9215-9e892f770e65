/**
 * Serviço de verificação de permissões
 *
 * Este serviço é responsável por verificar se um usuário tem permissão
 * para realizar uma ação específica em um recurso.
 */

import type {
  FullPermissionData,
  PermissionCheckData,
  PermissionCheckResult,
} from '@repository/interfaces/rbacInterfaces';
import { permissionRepository } from '@repository/permissionRepository';
import { logger } from '@utils/logger';
import { permissionCacheService } from './permissionCacheService';

/**
 * Serviço de permissões
 */
export const permissionService = {
  /**
   * Verifica se um usuário tem permissão para uma ação específica
   * @param userId - ID do usuário
   * @param resource - Nome do recurso
   * @param action - Ação a ser verificada
   * @returns Resultado da verificação
   */
  async checkPermission(
    userId: string,
    resource: string,
    action: string
  ): Promise<PermissionCheckResult> {
    try {
      // Verificar cache primeiro
      const cachedResult = await permissionCacheService.getPermissionFromCache(
        userId,
        resource,
        action
      );

      if (cachedResult !== null) {
        return { granted: cachedResult };
      }

      // Verificar permissão no banco de dados
      const hasPermission = await permissionRepository.checkUserPermission(
        userId,
        resource,
        action
      );

      // Armazenar resultado em cache
      await permissionCacheService.cachePermissionResult(userId, resource, action, hasPermission);

      return {
        granted: hasPermission,
        reason: hasPermission
          ? undefined
          : `Usuário não tem permissão para ${action} em ${resource}`,
      };
    } catch (error) {
      logger.error('Erro ao verificar permissão:', error);
      return {
        granted: false,
        reason: 'Erro ao verificar permissão',
      };
    }
  },

  /**
   * Verifica múltiplas permissões de uma vez
   * @param userId - ID do usuário
   * @param permissions - Lista de permissões a verificar
   * @returns Mapa de resultados por permissão
   */
  async checkMultiplePermissions(
    userId: string,
    permissions: PermissionCheckData[]
  ): Promise<Record<string, PermissionCheckResult>> {
    const results: Record<string, PermissionCheckResult> = {};

    for (const perm of permissions) {
      const key = `${perm.resource}:${perm.action}`;
      results[key] = await this.checkPermission(userId, perm.resource, perm.action);
    }

    return results;
  },

  /**
   * Obtém todas as permissões de um usuário
   * @param userId - ID do usuário
   * @returns Lista de permissões completas
   */
  async getUserPermissions(userId: string): Promise<FullPermissionData[]> {
    try {
      const result = await permissionRepository.getUserPermissions(userId);

      return result.rows.map((row) => ({
        ulid_permission: row.ulid_permission,
        name: row.name,
        description: row.description,
        action: row.action,
        active: row.active,
        created_at: row.created_at,
        updated_at: row.updated_at,
        resource_name: row.resource_name,
      }));
    } catch (error) {
      logger.error('Erro ao obter permissões do usuário:', error);
      return [];
    }
  },
};
