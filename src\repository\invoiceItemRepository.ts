import type { QueryResult } from "pg";
import { pgH<PERSON>per } from "./pgHelper";

async function create(
  ulid_invoice: string,
  ulid_product: string,
  qty: number,
  price: number
): Promise<QueryResult> {
  return await pgHelper.query(
    `INSERT INTO tab_invoice_item (
       ulid_invoice, 
       ulid_product, 
       qty, 
       price) 
     VALUES ('$1', '$2', $3, $4)
     RETURNING *`,
    [ulid_invoice, ulid_product, qty, price]
  )
}

async function read(
  ulid_invoice_item?: string,
  ulid_invoice?: string,
  ulid_product?: string
): Promise<QueryResult> {
  return await pgHelper.query(
    `SELECT * 
       FROM tab_invoice_item 
      WHERE TRUE 
        ${ulid_invoice_item ? 'AND ulid_invoice_item = $1' : ''}
        ${ulid_invoice ? 'AND ulid_invoice = $2' : ''}
        ${ulid_product ? 'AND ulid_product = $3' : ''}`,
    [ulid_invoice_item, ulid_invoice, ulid_product]
  )
}

async function update(
  ulid_invoice_item: string,
  qty: number
): Promise<QueryResult> {
  return await pgHelper.query(
    `UPDATE tab_invoice_item 
        SET qty               = $2,
            updated_at        = NOW() 
      WHERE ulid_invoice_item = $1 
     RETURNING *`,
    [ulid_invoice_item, qty]
  )
}

async function deleteByUlid(
  ulid_invoice_item: string
): Promise<QueryResult> {
  return await pgHelper.query(
    `DELETE FROM tab_invoice_item 
      WHERE ulid_invoice_item = $1 
     RETURNING *`,
    [ulid_invoice_item]
  )
}

export const invoiceItemRepository = {
  create,
  read,
  update,
  deleteByUlid
}
