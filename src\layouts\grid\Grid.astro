---
/**
 * Componente Grid
 *
 * Sistema de grid responsivo baseado em CSS Grid
 */

interface GridBreakpoints {
  base?: number;
  sm?: number;
  md?: number;
  lg?: number;
  xl?: number;
  '2xl'?: number;
}

interface Props {
  /**
   * Número de colunas em diferentes breakpoints
   * @example { base: 1, sm: 2, md: 3, lg: 4 }
   */
  cols?: number | GridBreakpoints;

  /**
   * Espaçamento entre itens
   */
  gap?: number;

  /**
   * Espaçamento entre linhas
   */
  rowGap?: number;

  /**
   * Espaçamento entre colunas
   */
  colGap?: number;

  /**
   * Alinhamento horizontal dos itens
   */
  justifyItems?: 'start' | 'end' | 'center' | 'stretch';

  /**
   * Alinhamento vertical dos itens
   */
  alignItems?: 'start' | 'end' | 'center' | 'stretch';

  /**
   * Classe CSS adicional
   */
  class?: string;
}

const {
  cols = { base: 1, sm: 2, md: 3, lg: 4 },
  gap = 4,
  rowGap,
  colGap,
  justifyItems = 'stretch',
  alignItems = 'stretch',
  class: className = '',
} = Astro.props;

// Converter número para objeto de breakpoints
const colsObj = typeof cols === 'number' ? { base: cols } : cols;

// Gerar classes CSS para grid
const gridClasses = [
  'grid',
  `gap-${gap}`,
  rowGap ? `row-gap-${rowGap}` : '',
  colGap ? `col-gap-${colGap}` : '',
  `justify-items-${justifyItems}`,
  `items-${alignItems}`,
  className,
]
  .filter(Boolean)
  .join(' ');

// Gerar estilos CSS para grid responsivo
const gridStyles = [
  // Base (mobile first)
  colsObj.base ? `grid-template-columns: repeat(${colsObj.base}, minmax(0, 1fr));` : '',

  // Small (sm)
  colsObj.sm
    ? `@media (min-width: 640px) { grid-template-columns: repeat(${colsObj.sm}, minmax(0, 1fr)); }`
    : '',

  // Medium (md)
  colsObj.md
    ? `@media (min-width: 768px) { grid-template-columns: repeat(${colsObj.md}, minmax(0, 1fr)); }`
    : '',

  // Large (lg)
  colsObj.lg
    ? `@media (min-width: 1024px) { grid-template-columns: repeat(${colsObj.lg}, minmax(0, 1fr)); }`
    : '',

  // Extra Large (xl)
  colsObj.xl
    ? `@media (min-width: 1280px) { grid-template-columns: repeat(${colsObj.xl}, minmax(0, 1fr)); }`
    : '',

  // 2xl
  colsObj['2xl']
    ? `@media (min-width: 1536px) { grid-template-columns: repeat(${colsObj['2xl']}, minmax(0, 1fr)); }`
    : '',
]
  .filter(Boolean)
  .join('\n');
---

<div class={gridClasses} style={gridStyles}>
  <slot />
</div>
