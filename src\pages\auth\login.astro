---
import { loginAction } from '../../actions/auth/loginAction';

// Process login form submission
let formResult = null;
const formData = null;

if (Astro.request.method === 'POST') {
  formResult = await loginAction(Astro);

  if (formResult.success) {
    // Redirect to dashboard on successful login
    return Astro.redirect('/dashboard');
  }
}
---

<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Login | Estação da Alfabetização</title>
  <style>
    :root {
      --color-primary: #4a6cf7;
      --color-primary-dark: #3a5ce5;
      --color-text: #334155;
      --color-text-light: #64748b;
      --color-text-dark: #1e293b;
      --color-bg: #f8fafc;
      --color-bg-dark: #f1f5f9;
      --color-error: #ef4444;
    }
    
    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }
    
    body {
      font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      line-height: 1.6;
      color: var(--color-text);
      background-color: var(--color-bg);
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 100vh;
      padding: 2rem;
    }
    
    .login-container {
      width: 100%;
      max-width: 400px;
      background-color: white;
      border-radius: 8px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      padding: 2rem;
    }
    
    .login-header {
      text-align: center;
      margin-bottom: 2rem;
    }
    
    .login-title {
      color: var(--color-text-dark);
      font-size: 1.5rem;
      margin-bottom: 0.5rem;
    }
    
    .login-subtitle {
      color: var(--color-text-light);
      font-size: 0.875rem;
    }
    
    .login-form {
      display: flex;
      flex-direction: column;
      gap: 1.5rem;
    }
    
    .form-group {
      display: flex;
      flex-direction: column;
      gap: 0.5rem;
    }
    
    .form-label {
      font-weight: 600;
      font-size: 0.875rem;
      color: var(--color-text-dark);
    }
    
    .form-input {
      padding: 0.75rem;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 1rem;
      font-family: inherit;
    }
    
    .form-input:focus {
      outline: none;
      border-color: var(--color-primary);
      box-shadow: 0 0 0 2px rgba(74, 108, 247, 0.2);
    }
    
    .form-error {
      color: var(--color-error);
      font-size: 0.875rem;
      margin-top: 0.25rem;
    }
    
    .form-checkbox {
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }
    
    .form-checkbox input {
      width: 1rem;
      height: 1rem;
    }
    
    .form-checkbox label {
      font-size: 0.875rem;
    }
    
    .form-actions {
      display: flex;
      flex-direction: column;
      gap: 1rem;
    }
    
    .login-button {
      padding: 0.75rem;
      background-color: var(--color-primary);
      color: white;
      border: none;
      border-radius: 4px;
      font-size: 1rem;
      font-weight: 600;
      cursor: pointer;
      transition: background-color 0.2s ease;
    }
    
    .login-button:hover {
      background-color: var(--color-primary-dark);
    }
    
    .register-link {
      text-align: center;
      font-size: 0.875rem;
    }
    
    .register-link a {
      color: var(--color-primary);
      text-decoration: none;
      font-weight: 600;
    }
    
    .register-link a:hover {
      text-decoration: underline;
    }
    
    .form-error-message {
      padding: 0.75rem;
      background-color: rgba(239, 68, 68, 0.1);
      border: 1px solid rgba(239, 68, 68, 0.2);
      border-radius: 4px;
      color: var(--color-error);
      margin-bottom: 1.5rem;
    }
  </style>
</head>
<body>
  <div class="login-container">
    <div class="login-header">
      <h1 class="login-title">Bem-vindo de volta</h1>
      <p class="login-subtitle">Entre com suas credenciais para acessar sua conta</p>
    </div>
    
    {formResult && !formResult.success && formResult.errors?.form && (
      <div class="form-error-message">
        <p>{formResult.errors.form}</p>
      </div>
    )}
    
    <form method="post" class="login-form">
      <div class="form-group">
        <label for="email" class="form-label">Email</label>
        <input 
          type="email" 
          id="email" 
          name="email" 
          class="form-input" 
          value={formData?.get('email')?.toString() || ''} 
          required
        />
        {formResult && formResult.errors?.email && (
          <p class="form-error">{formResult.errors.email}</p>
        )}
      </div>
      
      <div class="form-group">
        <label for="password" class="form-label">Senha</label>
        <input 
          type="password" 
          id="password" 
          name="password" 
          class="form-input" 
          required
        />
        {formResult && formResult.errors?.password && (
          <p class="form-error">{formResult.errors.password}</p>
        )}
      </div>
      
      <div class="form-checkbox">
        <input type="checkbox" id="rememberMe" name="rememberMe" />
        <label for="rememberMe">Lembrar de mim</label>
      </div>
      
      <div class="form-actions">
        <button type="submit" class="login-button">Entrar</button>
        <p class="register-link">
          Não tem uma conta? <a href="/auth/register">Registre-se</a>
        </p>
      </div>
    </form>
  </div>
</body>
</html>
