/**
 * Fábrica de serviços de notificação
 *
 * Esta fábrica cria instâncias dos diferentes serviços de notificação,
 * cada um implementando apenas as interfaces necessárias (ISP).
 */

import {
  EmailNotificationChannel,
  InAppNotificationChannel,
  PushNotificationChannel,
  SmsNotificationChannel,
  WebhookNotificationChannel,
} from '../../domain/interfaces/NotificationChannels';
import { logger } from '../../utils/logger';
import { EmailNotificationService } from './EmailNotificationService';
import { PushNotificationService } from './PushNotificationService';

/**
 * Fábrica de serviços de notificação
 */
export class NotificationServiceFactory {
  /**
   * Cache de instâncias de serviços
   */
  private static instances: Map<string, any> = new Map();

  /**
   * Obtém serviço de notificação por e-mail
   * @returns Serviço de notificação por e-mail
   */
  public static getEmailService(): EmailNotificationChannel {
    const serviceKey = 'email';

    if (!NotificationServiceFactory.instances.has(serviceKey)) {
      logger.debug('Creating new EmailNotificationService instance');
      NotificationServiceFactory.instances.set(serviceKey, new EmailNotificationService());
    }

    return NotificationServiceFactory.instances.get(serviceKey) as EmailNotificationChannel;
  }

  /**
   * Obtém serviço de notificação push
   * @returns Serviço de notificação push
   */
  public static getPushService(): PushNotificationChannel {
    const serviceKey = 'push';

    if (!NotificationServiceFactory.instances.has(serviceKey)) {
      logger.debug('Creating new PushNotificationService instance');
      NotificationServiceFactory.instances.set(serviceKey, new PushNotificationService());
    }

    return NotificationServiceFactory.instances.get(serviceKey) as PushNotificationChannel;
  }

  /**
   * Obtém serviço de notificação SMS
   * @returns Serviço de notificação SMS
   */
  public static getSmsService(): SmsNotificationChannel {
    const serviceKey = 'sms';

    if (!NotificationServiceFactory.instances.has(serviceKey)) {
      logger.debug('Creating new SmsNotificationService instance');
      // Implementação real seria criada aqui
      throw new Error('SmsNotificationService not implemented yet');
    }

    return NotificationServiceFactory.instances.get(serviceKey) as SmsNotificationChannel;
  }

  /**
   * Obtém serviço de notificação in-app
   * @returns Serviço de notificação in-app
   */
  public static getInAppService(): InAppNotificationChannel {
    const serviceKey = 'inapp';

    if (!NotificationServiceFactory.instances.has(serviceKey)) {
      logger.debug('Creating new InAppNotificationService instance');
      // Implementação real seria criada aqui
      throw new Error('InAppNotificationService not implemented yet');
    }

    return NotificationServiceFactory.instances.get(serviceKey) as InAppNotificationChannel;
  }

  /**
   * Obtém serviço de notificação webhook
   * @returns Serviço de notificação webhook
   */
  public static getWebhookService(): WebhookNotificationChannel {
    const serviceKey = 'webhook';

    if (!NotificationServiceFactory.instances.has(serviceKey)) {
      logger.debug('Creating new WebhookNotificationService instance');
      // Implementação real seria criada aqui
      throw new Error('WebhookNotificationService not implemented yet');
    }

    return NotificationServiceFactory.instances.get(serviceKey) as WebhookNotificationChannel;
  }

  /**
   * Limpa o cache de instâncias
   * Útil para testes e reinicialização
   */
  public static clearInstances(): void {
    NotificationServiceFactory.instances.clear();
    logger.debug('Notification service instances cleared');
  }
}
