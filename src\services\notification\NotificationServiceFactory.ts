/**
 * Fábrica de serviços de notificação
 *
 * Esta fábrica cria instâncias dos diferentes serviços de notificação,
 * cada um implementando apenas as interfaces necessárias (ISP).
 */

import {
  EmailNotificationChannel,
  InAppNotificationChannel,
  PushNotificationChannel,
  SmsNotificationChannel,
  WebhookNotificationChannel,
} from '../../domain/interfaces/NotificationChannels';
import { logger } from '../../utils/logger';
import { EmailNotificationService } from './EmailNotificationService';
import { PushNotificationService } from './PushNotificationService';

/**
 * Fábrica de serviços de notificação
 */
export namespace NotificationServiceFactory {
  /**
   * Cache de instâncias de serviços
   */
  const instances: Map<string, any> = new Map();

  /**
   * Obtém serviço de notificação por e-mail
   * @returns Serviço de notificação por e-mail
   */
  export function getEmailService(): EmailNotificationChannel {
    const serviceKey = 'email';

    if (!instances.has(serviceKey)) {
      logger.debug('Creating new EmailNotificationService instance');
      instances.set(serviceKey, new EmailNotificationService());
    }

    return instances.get(serviceKey) as EmailNotificationChannel;
  }

  /**
   * Obtém serviço de notificação push
   * @returns Serviço de notificação push
   */
  export function getPushService(): PushNotificationChannel {
    const serviceKey = 'push';

    if (!instances.has(serviceKey)) {
      logger.debug('Creating new PushNotificationService instance');
      instances.set(serviceKey, new PushNotificationService());
    }

    return instances.get(serviceKey) as PushNotificationChannel;
  }

  /**
   * Obtém serviço de notificação SMS
   * @returns Serviço de notificação SMS
   */
  export function getSmsService(): SmsNotificationChannel {
    const serviceKey = 'sms';

    if (!instances.has(serviceKey)) {
      logger.debug('Creating new SmsNotificationService instance');
      // Implementação real seria criada aqui
      throw new Error('SmsNotificationService not implemented yet');
    }

    return instances.get(serviceKey) as SmsNotificationChannel;
  }

  /**
   * Obtém serviço de notificação in-app
   * @returns Serviço de notificação in-app
   */
  export function getInAppService(): InAppNotificationChannel {
    const serviceKey = 'inapp';

    if (!instances.has(serviceKey)) {
      logger.debug('Creating new InAppNotificationService instance');
      // Implementação real seria criada aqui
      throw new Error('InAppNotificationService not implemented yet');
    }

    return instances.get(serviceKey) as InAppNotificationChannel;
  }

  /**
   * Obtém serviço de notificação webhook
   * @returns Serviço de notificação webhook
   */
  export function getWebhookService(): WebhookNotificationChannel {
    const serviceKey = 'webhook';

    if (!instances.has(serviceKey)) {
      logger.debug('Creating new WebhookNotificationService instance');
      // Implementação real seria criada aqui
      throw new Error('WebhookNotificationService not implemented yet');
    }

    return instances.get(serviceKey) as WebhookNotificationChannel;
  }

  /**
   * Limpa o cache de instâncias
   * Útil para testes e reinicialização
   */
  export function clearInstances(): void {
    instances.clear();
    logger.debug('Notification service instances cleared');
  }
}
