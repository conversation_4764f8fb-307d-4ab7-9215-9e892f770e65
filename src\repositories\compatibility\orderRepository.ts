/**
 * Camada de compatibilidade para o repositório de pedidos
 * 
 * Este arquivo mantém a compatibilidade com o código existente que
 * utiliza a estrutura antiga do repositório de pedidos.
 * 
 * @deprecated Use a nova estrutura de repositórios em src/repositories
 */

import type { QueryResult } from "pg";
import { pgHelper } from "../../repository/pgHelper";
import { repositories } from "../index";

/**
 * Cria um novo pedido
 * 
 * @param ulid_user ID do usuário
 * @param cod_status Código do status
 * @param total Valor total do pedido
 * @returns Resultado da consulta
 */
async function create(
  ulid_user: string,
  cod_status: number,
  total: number
): Promise<QueryResult> {
  try {
    // Usar a nova implementação
    const order = await repositories.orderRepository.create({
      userId: ulid_user,
      statusCode: cod_status,
      total
    });
    
    // Converter para o formato esperado pelo código antigo
    return {
      rows: [order],
      rowCount: 1,
      command: 'INSERT',
      oid: 0,
      fields: []
    };
  } catch (error) {
    console.error('Error in compatibility layer (orderRepository.create):', error);
    
    // Fallback para a implementação antiga em caso de erro
    return pgHelper.query(
      `INSERT INTO tab_order (
         ulid_order, 
         cod_status, 
         total) 
       VALUES ('$1', $2, $3) 
       RETURNING *`,
      [ulid_user, cod_status, total]
    );
  }
}

/**
 * Busca pedidos
 * 
 * @param ulid_order ID do pedido (opcional)
 * @param ulid_user ID do usuário (opcional)
 * @param cod_status Código do status (opcional)
 * @returns Resultado da consulta
 */
async function read(
  ulid_order?: string,
  ulid_user?: string,
  cod_status?: number
): Promise<QueryResult> {
  try {
    // Usar a nova implementação
    const orders = await repositories.orderRepository.findAll({
      id: ulid_order,
      userId: ulid_user,
      statusCode: cod_status
    });
    
    // Converter para o formato esperado pelo código antigo
    return {
      rows: orders,
      rowCount: orders.length,
      command: 'SELECT',
      oid: 0,
      fields: []
    };
  } catch (error) {
    console.error('Error in compatibility layer (orderRepository.read):', error);
    
    // Fallback para a implementação antiga em caso de erro
    return pgHelper.query(
      `SELECT * 
         FROM tab_order 
        WHERE TRUE 
          ${ulid_order ? 'AND ulid_order = $1' : ''}
          ${ulid_user ? 'AND ulid_user = $2' : ''}
          ${cod_status ? 'AND cod_status = $3' : ''}`,
      [ulid_order, ulid_user, cod_status]
    );
  }
}

/**
 * Atualiza um pedido
 * 
 * @param ulid_order ID do pedido
 * @param cod_status Código do status
 * @returns Resultado da consulta
 */
async function update(
  ulid_order: string,
  cod_status: number,
): Promise<QueryResult> {
  try {
    // Usar a nova implementação
    const order = await repositories.orderRepository.update(ulid_order, {
      statusCode: cod_status
    });
    
    // Converter para o formato esperado pelo código antigo
    return {
      rows: [order],
      rowCount: 1,
      command: 'UPDATE',
      oid: 0,
      fields: []
    };
  } catch (error) {
    console.error('Error in compatibility layer (orderRepository.update):', error);
    
    // Fallback para a implementação antiga em caso de erro
    // Corrigindo o SQL que tinha um erro na query original
    return pgHelper.query(`
      UPDATE tab_order 
         SET cod_status = $2,
             updated_at = NOW() 
       WHERE ulid_order = $1
       RETURNING *`, 
      [ulid_order, cod_status]);
  }
}

/**
 * Remove um pedido
 * 
 * @param ulid_order ID do pedido
 * @returns Resultado da consulta
 */
async function deleteByUlid(
  ulid_order: string
): Promise<QueryResult> {
  try {
    // Usar a nova implementação
    const success = await repositories.orderRepository.delete(ulid_order);
    
    // Converter para o formato esperado pelo código antigo
    return {
      rows: success ? [{ ulid_order }] : [],
      rowCount: success ? 1 : 0,
      command: 'DELETE',
      oid: 0,
      fields: []
    };
  } catch (error) {
    console.error('Error in compatibility layer (orderRepository.deleteByUlid):', error);
    
    // Fallback para a implementação antiga em caso de erro
    return pgHelper.query(
      `DELETE FROM tab_order 
        WHERE ulid_order = $1 
       RETURNING *`,
      [ulid_order],
    );
  }
}

export const orderRepository = {
  create,
  read,
  update,
  deleteByUlid
};
