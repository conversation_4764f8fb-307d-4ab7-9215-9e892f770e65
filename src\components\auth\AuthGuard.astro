---
/**
 * Componente de Proteção de Autenticação - Astro Nativo
 * 
 * Este componente protege conteúdo baseado no status de autenticação do usuário,
 * substituindo qualquer implementação React similar.
 */

import { authService } from '@services/authService';

interface Props {
  /** Mostrar conteúdo apenas para usuários autenticados */
  requireAuth?: boolean;
  /** Mostrar conteúdo apenas para usuários não autenticados */
  requireGuest?: boolean;
  /** Permissões necessárias para ver o conteúdo */
  permissions?: string[];
  /** Papéis necessários para ver o conteúdo */
  roles?: string[];
  /** Conteúdo alternativo para usuários não autorizados */
  fallback?: string;
}

const { 
  requireAuth = false,
  requireGuest = false,
  permissions = [],
  roles = [],
  fallback = ''
} = Astro.props;

// Verificar autenticação no servidor
const isAuthenticated = await authService.isAuthenticated(Astro);
const user = isAuthenticated ? await authService.getAuthenticatedUser(Astro) : null;

// Lógica de autorização
let isAuthorized = true;

// Verificar se requer autenticação
if (requireAuth && !isAuthenticated) {
  isAuthorized = false;
}

// Verificar se requer usuário não autenticado
if (requireGuest && isAuthenticated) {
  isAuthorized = false;
}

// Verificar permissões (se especificadas)
if (permissions.length > 0 && user) {
  const userPermissions = user.permissions || [];
  const hasAllPermissions = permissions.every(permission => 
    userPermissions.includes(permission)
  );
  if (!hasAllPermissions) {
    isAuthorized = false;
  }
}

// Verificar papéis (se especificados)
if (roles.length > 0 && user) {
  const userRoles = user.roles || [];
  const hasAnyRole = roles.some(role => 
    userRoles.includes(role)
  );
  if (!hasAnyRole) {
    isAuthorized = false;
  }
}
---

{isAuthorized ? (
  <slot />
) : (
  <div class="auth-guard-fallback">
    {fallback ? (
      <div set:html={fallback} />
    ) : (
      <div class="alert alert-warning">
        <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
        </svg>
        <span>Você não tem permissão para acessar este conteúdo.</span>
      </div>
    )}
  </div>
)}

<style>
  .auth-guard-fallback {
    padding: 1rem;
  }
  
  .alert {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
    border-radius: 0.5rem;
    background-color: #fef3c7;
    border: 1px solid #f59e0b;
    color: #92400e;
  }
  
  .alert svg {
    width: 1.5rem;
    height: 1.5rem;
    flex-shrink: 0;
  }
</style>
