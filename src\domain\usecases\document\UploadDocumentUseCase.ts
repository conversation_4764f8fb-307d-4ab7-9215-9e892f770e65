/**
 * Upload Document Use Case
 *
 * Caso de uso para upload de documentos PDF.
 * Parte da implementação da tarefa 8.3.1 - Armazenamento de PDFs
 */

import { calculateChecksum } from '../../../utils/fileUtils';
import { generateId } from '../../../utils/idGenerator';
import { Document, DocumentMetadata, DocumentVersion } from '../../entities/Document';
import { DocumentRepository } from '../../repositories/DocumentRepository';

export interface UploadDocumentRequest {
  title: string;
  description?: string;
  ownerId: string;
  isPublic: boolean;
  content: Uint8Array;
  contentType: string;
  author?: string;
  keywords?: string[];
  language?: string;
  category?: string;
  tags?: string[];
  pageCount?: number;
}

export interface UploadDocumentResponse {
  success: boolean;
  document?: Document;
  error?: string;
}

export class UploadDocumentUseCase {
  constructor(private documentRepository: DocumentRepository) {}

  async execute(request: UploadDocumentRequest): Promise<UploadDocumentResponse> {
    try {
      // Validar o tipo de conteúdo
      if (!this.isValidContentType(request.contentType)) {
        return {
          success: false,
          error: 'Tipo de arquivo inválido. Apenas PDFs são permitidos.',
        };
      }

      // Validar tamanho do arquivo (máximo 10MB)
      const maxSize = 10 * 1024 * 1024; // 10MB em bytes
      if (request.content.length > maxSize) {
        return {
          success: false,
          error: 'Tamanho do arquivo excede o limite máximo de 10MB.',
        };
      }

      // Calcular checksum para verificação de integridade
      const checksum = await calculateChecksum(request.content);

      // Criar metadados do documento
      const now = new Date();
      const metadata: DocumentMetadata = {
        title: request.title,
        description: request.description,
        author: request.author,
        keywords: request.keywords,
        createdAt: now,
        updatedAt: now,
        fileSize: request.content.length,
        pageCount: request.pageCount,
        language: request.language,
        category: request.category,
        tags: request.tags,
        version: 1, // Primeira versão
        contentType: request.contentType,
        checksum,
      };

      // Gerar IDs
      const documentId = generateId();
      const versionId = generateId();

      // Criar versão do documento
      const version: DocumentVersion = {
        id: versionId,
        documentId,
        version: 1,
        content: request.content,
        metadata,
        createdAt: now,
        createdBy: request.ownerId,
      };

      // Criar documento
      const document = new Document({
        id: documentId,
        title: request.title,
        description: request.description,
        ownerId: request.ownerId,
        isPublic: request.isPublic,
        currentVersion: 1,
        metadata,
        versions: [version],
        createdAt: now,
        updatedAt: now,
      });

      // Salvar documento no repositório
      const savedDocument = await this.documentRepository.create(document);

      return {
        success: true,
        document: savedDocument,
      };
    } catch (error) {
      console.error('Erro ao fazer upload do documento:', error);
      return {
        success: false,
        error: 'Ocorreu um erro ao processar o upload do documento.',
      };
    }
  }

  private isValidContentType(contentType: string): boolean {
    const allowedTypes = ['application/pdf', 'application/x-pdf'];
    return allowedTypes.includes(contentType);
  }
}
