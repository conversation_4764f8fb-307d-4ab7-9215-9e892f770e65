import { defineMiddleware } from 'astro:middleware';
import { AuditEventType, AuditSeverity, auditService } from '@services/auditService';
import { getCurrentUser } from '@utils/authUtils';
import { logger } from '@utils/logger';
import type { MiddlewareHandler } from 'astro';

// Define os tipos para a sessão
declare module 'astro' {
  interface AstroGlobal {
    user?: {
      id: string;
      name: string;
      email: string;
      isTeacher: boolean;
      userType: string;
      schoolType?: string;
    };
  }
}

/**
 * Middleware para verificar autenticação e registrar acesso a recursos sensíveis
 */
export const onRequest = defineMiddleware(async (context, next) => {
  const { cookies, url, request } = context;

  try {
    // Obter usuário atual
    const user = await getCurrentUser(cookies);

    // Rotas que não precisam de autenticação
    const publicRoutes = ['/signin', '/signup', '/api/auth', '/public', '/assets'];
    const isPublicRoute = publicRoutes.some((route) => url.pathname.startsWith(route));

    // Verificar se é uma rota de administração
    const isAdminRoute = url.pathname.startsWith('/admin');

    // Verificar se é uma rota de API sensível
    const isSensitiveApiRoute =
      url.pathname.startsWith('/api/admin') || url.pathname.startsWith('/api/rbac');

    // Obter informações da requisição para auditoria
    const ipAddress =
      request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown';

    const userAgent = request.headers.get('user-agent') || 'unknown';

    // Redirecionar para login se não estiver autenticado e não for rota pública
    if (!user && !isPublicRoute) {
      // Registrar tentativa de acesso não autorizado
      if (isAdminRoute || isSensitiveApiRoute) {
        await auditService.logEvent({
          eventType: AuditEventType.PERMISSION_DENIED,
          ipAddress,
          userAgent,
          resource: 'auth',
          action: 'access',
          result: 'failure',
          severity: AuditSeverity.WARNING,
          metadata: {
            path: url.pathname,
            reason: 'Não autenticado',
          },
        });
      }

      // Redirecionar para login com retorno para a página atual
      const redirectUrl = `/signin?redirect=${encodeURIComponent(url.pathname)}`;
      return context.redirect(redirectUrl);
    }

    // Registrar acesso a recursos sensíveis
    if (user && (isAdminRoute || isSensitiveApiRoute)) {
      await auditService.logEvent({
        eventType: AuditEventType.RESOURCE_ACCESSED,
        userId: user.ulid_user,
        userName: user.name,
        ipAddress,
        userAgent,
        resource: isAdminRoute ? 'admin' : 'api',
        resourceId: url.pathname,
        action: 'access',
        result: 'success',
        severity: AuditSeverity.INFO,
      });
    }

    // Disponibilizar o usuário para todos os componentes
    context.locals.user = user;

    return next();
  } catch (error) {
    logger.error('Erro no middleware de autenticação:', error);

    // Continuar para o próximo middleware ou rota
    return next();
  }
}) satisfies MiddlewareHandler;
