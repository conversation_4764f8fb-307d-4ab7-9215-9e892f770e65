---
/**
 * Componente OptimizedScript
 * 
 * Este componente implementa carregamento otimizado de scripts com:
 * - Carregamento assíncrono
 * - Carregamento diferido
 * - Preload para scripts críticos
 * - Suporte a módulos ES
 */

interface Props {
  /**
   * Caminho do script
   */
  src: string;
  
  /**
   * Se o script deve ser carregado como módulo ES
   * @default false
   */
  isModule?: boolean;
  
  /**
   * Se o script deve ser carregado de forma assíncrona
   * @default true
   */
  async?: boolean;
  
  /**
   * Se o script deve ser carregado de forma diferida
   * @default false
   */
  defer?: boolean;
  
  /**
   * Se o script é crítico para a página
   * @default false
   */
  critical?: boolean;
  
  /**
   * Conteúdo inline do script
   */
  content?: string;
  
  /**
   * Estratégia de carregamento
   * @default "default"
   */
  strategy?: 'default' | 'afterInteractive' | 'lazyOnload' | 'worker';
  
  /**
   * ID do script
   */
  id?: string;
  
  /**
   * Atributos adicionais
   */
  attributes?: Record<string, string>;
}

// Props com valores padrão
const {
  src,
  isModule = false,
  async = true,
  defer = false,
  critical = false,
  content,
  strategy = 'default',
  id,
  attributes = {},
} = Astro.props;

// Determinar se o script é externo ou inline
const isInline = !!content;

// Determinar se deve fazer preload
const shouldPreload = critical && !isInline;

// Determinar se deve usar Intersection Observer para carregamento lazy
const useLazyLoading = strategy === 'lazyOnload';

// Gerar ID único para o script
const scriptId = id || `script-${Math.random().toString(36).substring(2, 11)}`;

// Determinar atributos do script
const scriptAttributes = {
  ...attributes,
  ...(isModule ? { type: 'module' } : {}),
  ...(async && !defer && strategy !== 'afterInteractive' && strategy !== 'lazyOnload' ? { async: '' } : {}),
  ...(defer && !async && strategy !== 'afterInteractive' && strategy !== 'lazyOnload' ? { defer: '' } : {}),
  ...(id ? { id } : {}),
};

// Converter atributos para string
const attributesString = Object.entries(scriptAttributes)
  .map(([key, value]) => value === '' ? key : `${key}="${value}"`)
  .join(' ');
---

{/* Preload para scripts críticos */}
{shouldPreload && (
  <link 
    rel="preload" 
    href={src} 
    as="script" 
    {...(isModule ? { type: 'module' } : {})}
  />
)}

{/* Script inline */}
{isInline && (
  <script 
    id={scriptId}
    {...(isModule ? { type: 'module' } : {})}
    set:html={content}
  />
)}

{/* Script externo com carregamento padrão */}
{!isInline && strategy === 'default' && (
  <script 
    src={src}
    id={scriptId}
    {...scriptAttributes}
  />
)}

{/* Script externo com carregamento após interatividade */}
{!isInline && strategy === 'afterInteractive' && (
  <script data-strategy="afterInteractive" set:html={`
    document.addEventListener('DOMContentLoaded', function() {
      const script = document.createElement('script');
      script.src = "${src}";
      script.id = "${scriptId}";
      ${isModule ? 'script.type = "module";' : ''}
      ${attributesString ? `
        ${Object.entries(scriptAttributes)
          .map(([key, value]) => value === '' 
            ? `script.setAttribute("${key}", "");` 
            : `script.setAttribute("${key}", "${value}");`)
          .join('\n')}
      ` : ''}
      document.body.appendChild(script);
    });
  `} />
)}

{/* Script externo com carregamento lazy */}
{!isInline && strategy === 'lazyOnload' && (
  <script data-strategy="lazyOnload" set:html={`
    if ('IntersectionObserver' in window) {
      const loadScript = () => {
        const script = document.createElement('script');
        script.src = "${src}";
        script.id = "${scriptId}";
        ${isModule ? 'script.type = "module";' : ''}
        ${attributesString ? `
          ${Object.entries(scriptAttributes)
            .map(([key, value]) => value === '' 
              ? `script.setAttribute("${key}", "");` 
              : `script.setAttribute("${key}", "${value}");`)
            .join('\n')}
        ` : ''}
        document.body.appendChild(script);
      };

      const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            loadScript();
            observer.disconnect();
          }
        });
      }, { rootMargin: '200px' });

      // Observar o final da página
      const footer = document.querySelector('footer') || document.body;
      observer.observe(footer);

      // Carregar de qualquer forma após 5 segundos
      setTimeout(() => {
        if (!document.getElementById("${scriptId}")) {
          loadScript();
          observer.disconnect();
        }
      }, 5000);
    } else {
      // Fallback para navegadores sem suporte a Intersection Observer
      window.addEventListener('load', () => {
        setTimeout(() => {
          const script = document.createElement('script');
          script.src = "${src}";
          script.id = "${scriptId}";
          ${isModule ? 'script.type = "module";' : ''}
          ${attributesString ? `
            ${Object.entries(scriptAttributes)
              .map(([key, value]) => value === '' 
                ? `script.setAttribute("${key}", "");` 
                : `script.setAttribute("${key}", "${value}");`)
              .join('\n')}
          ` : ''}
          document.body.appendChild(script);
        }, 200);
      });
    }
  `} />
)}

{/* Script externo com carregamento em worker */}
{!isInline && strategy === 'worker' && (
  <script data-strategy="worker" set:html={`
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.ready.then(registration => {
        registration.active.postMessage({
          type: 'LOAD_SCRIPT',
          url: "${src}",
          id: "${scriptId}"
        });
      }).catch(err => {
        // Fallback se o service worker falhar
        const script = document.createElement('script');
        script.src = "${src}";
        script.id = "${scriptId}";
        ${isModule ? 'script.type = "module";' : ''}
        ${attributesString ? `
          ${Object.entries(scriptAttributes)
            .map(([key, value]) => value === '' 
              ? `script.setAttribute("${key}", "");` 
              : `script.setAttribute("${key}", "${value}");`)
            .join('\n')}
        ` : ''}
        document.body.appendChild(script);
      });
    } else {
      // Fallback para navegadores sem suporte a service worker
      const script = document.createElement('script');
      script.src = "${src}";
      script.id = "${scriptId}";
      ${isModule ? 'script.type = "module";' : ''}
      ${attributesString ? `
        ${Object.entries(scriptAttributes)
          .map(([key, value]) => value === '' 
            ? `script.setAttribute("${key}", "");` 
            : `script.setAttribute("${key}", "${value}");`)
          .join('\n')}
      ` : ''}
      document.body.appendChild(script);
    }
  `} />
)}
