---
import { PageTransition } from '../../components/transitions';
import DaisyCard from '../../components/ui/DaisyCard.astro';
import Breadcrumbs from '../../components/ui/navigation/Breadcrumbs.astro';
/**
 * Página de demonstração da tipografia
 * Esta página mostra todos os elementos tipográficos disponíveis no sistema
 */
import BaseLayout from '../../layouts/BaseLayout.astro';
import { Container, Grid, Section } from '../../layouts/grid';

// Título da página
const title = 'Tipografia';

// Breadcrumbs para a página atual
const breadcrumbItems = [
  { href: '/', label: 'Início' },
  { href: '/examples', label: 'Exemplos' },
  { label: 'Tipografia' },
];

// Exemplos de código
const fontFamiliesCode = `/* Famílias de fontes */
--font-primary: "<PERSON>uni<PERSON>", sans-serif;
--font-secondary: "Quicksand", sans-serif;
--font-mono: "Roboto Mono", monospace;`;

const fontSizesCode = `/* Tamanhos de fonte base */
--font-size-xs: 0.75rem;   /* 12px */
--font-size-sm: 0.875rem;  /* 14px */
--font-size-base: 1rem;    /* 16px */
--font-size-lg: 1.125rem;  /* 18px */
--font-size-xl: 1.25rem;   /* 20px */
--font-size-2xl: 1.5rem;   /* 24px */
--font-size-3xl: 1.875rem; /* 30px */
--font-size-4xl: 2.25rem;  /* 36px */
--font-size-5xl: 3rem;     /* 48px */
--font-size-6xl: 3.75rem;  /* 60px */`;

const fontWeightsCode = `/* Pesos de fonte */
--font-weight-light: 300;
--font-weight-regular: 400;
--font-weight-medium: 500;
--font-weight-semibold: 600;
--font-weight-bold: 700;
--font-weight-extrabold: 800;`;

const lineHeightsCode = `/* Alturas de linha */
--line-height-none: 1;
--line-height-tight: 1.25;
--line-height-snug: 1.375;
--line-height-normal: 1.5;
--line-height-relaxed: 1.625;
--line-height-loose: 2;`;

const letterSpacingCode = `/* Espaçamento entre letras */
--letter-spacing-tighter: -0.05em;
--letter-spacing-tight: -0.025em;
--letter-spacing-normal: 0em;
--letter-spacing-wide: 0.025em;
--letter-spacing-wider: 0.05em;
--letter-spacing-widest: 0.1em;`;

const educationalClassesCode = `/* Classes para texto educacional */
.educational-title { ... }
.educational-subtitle { ... }
.educational-content { ... }
.educational-note { ... }`;

const childrenClassesCode = `/* Classes para texto infantil */
.children-title { ... }
.children-subtitle { ... }
.children-content { ... }
.children-note { ... }`;

// Exemplos de texto para demonstração
const loremIpsum =
  'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam in dui mauris. Vivamus hendrerit arcu sed erat molestie vehicula. Sed auctor neque eu tellus rhoncus ut eleifend nibh porttitor.';
const alfabetizacaoText =
  'A Estação da Alfabetização é um projeto educacional focado no desenvolvimento de habilidades de leitura e escrita para crianças em fase de alfabetização.';
---

<BaseLayout title={title} enableViewTransitions={true} transitionType="fade">
  <Container>
    <PageTransition type="fade" duration="0.5s">
      <Section>
        <Breadcrumbs items={breadcrumbItems} class="mb-6" />
        
        <h1 class="text-3xl font-bold mb-6">{title}</h1>
        
        <p class="mb-8">
          Esta página demonstra o sistema tipográfico do projeto Estação da Alfabetização,
          com foco em legibilidade, acessibilidade e adequação ao público infantil e educadores.
        </p>
        
        <h2 class="text-2xl font-bold mb-4">Famílias de Fontes</h2>
        <Grid cols={{ sm: 1, md: 3 }} gap={4} class="mb-8">
          <DaisyCard title="Fonte Primária">
            <p class="font-primary mb-2">Nunito</p>
            <p class="font-primary text-lg mb-4">ABCDEFGHIJKLMNOPQRSTUVWXYZ<br>abcdefghijklmnopqrstuvwxyz<br>0123456789</p>
            <pre class="bg-base-200 p-2 rounded-box text-xs overflow-x-auto">font-family: var(--font-primary);</pre>
          </DaisyCard>
          
          <DaisyCard title="Fonte Secundária">
            <p class="font-secondary mb-2">Quicksand</p>
            <p class="font-secondary text-lg mb-4">ABCDEFGHIJKLMNOPQRSTUVWXYZ<br>abcdefghijklmnopqrstuvwxyz<br>0123456789</p>
            <pre class="bg-base-200 p-2 rounded-box text-xs overflow-x-auto">font-family: var(--font-secondary);</pre>
          </DaisyCard>
          
          <DaisyCard title="Fonte Mono">
            <p class="font-mono mb-2">Roboto Mono</p>
            <p class="font-mono text-lg mb-4">ABCDEFGHIJKLMNOPQRSTUVWXYZ<br>abcdefghijklmnopqrstuvwxyz<br>0123456789</p>
            <pre class="bg-base-200 p-2 rounded-box text-xs overflow-x-auto">font-family: var(--font-mono);</pre>
          </DaisyCard>
        </Grid>
        
        <h2 class="text-2xl font-bold mb-4">Tamanhos de Fonte</h2>
        <div class="mb-8">
          <div class="overflow-x-auto">
            <table class="table w-full">
              <thead>
                <tr>
                  <th>Classe</th>
                  <th>Variável</th>
                  <th>Exemplo</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td><code>.text-xs</code></td>
                  <td><code>--font-size-xs</code></td>
                  <td><span class="text-xs">Texto extra pequeno (0.75rem / 12px)</span></td>
                </tr>
                <tr>
                  <td><code>.text-sm</code></td>
                  <td><code>--font-size-sm</code></td>
                  <td><span class="text-sm">Texto pequeno (0.875rem / 14px)</span></td>
                </tr>
                <tr>
                  <td><code>.text-base</code></td>
                  <td><code>--font-size-base</code></td>
                  <td><span class="text-base">Texto base (1rem / 16px)</span></td>
                </tr>
                <tr>
                  <td><code>.text-lg</code></td>
                  <td><code>--font-size-lg</code></td>
                  <td><span class="text-lg">Texto grande (1.125rem / 18px)</span></td>
                </tr>
                <tr>
                  <td><code>.text-xl</code></td>
                  <td><code>--font-size-xl</code></td>
                  <td><span class="text-xl">Texto extra grande (1.25rem / 20px)</span></td>
                </tr>
                <tr>
                  <td><code>.text-2xl</code></td>
                  <td><code>--font-size-2xl</code></td>
                  <td><span class="text-2xl">Texto 2XL (1.5rem / 24px)</span></td>
                </tr>
                <tr>
                  <td><code>.text-3xl</code></td>
                  <td><code>--font-size-3xl</code></td>
                  <td><span class="text-3xl">Texto 3XL (1.875rem / 30px)</span></td>
                </tr>
                <tr>
                  <td><code>.text-4xl</code></td>
                  <td><code>--font-size-4xl</code></td>
                  <td><span class="text-4xl">Texto 4XL (2.25rem / 36px)</span></td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
        
        <h2 class="text-2xl font-bold mb-4">Pesos de Fonte</h2>
        <Grid cols={{ sm: 1, md: 2, lg: 3 }} gap={4} class="mb-8">
          <div class="p-4 border rounded-box">
            <p class="font-light mb-2">Light (300)</p>
            <pre class="bg-base-200 p-2 rounded-box text-xs">font-weight: var(--font-weight-light);</pre>
          </div>
          <div class="p-4 border rounded-box">
            <p class="font-normal mb-2">Regular (400)</p>
            <pre class="bg-base-200 p-2 rounded-box text-xs">font-weight: var(--font-weight-regular);</pre>
          </div>
          <div class="p-4 border rounded-box">
            <p class="font-medium mb-2">Medium (500)</p>
            <pre class="bg-base-200 p-2 rounded-box text-xs">font-weight: var(--font-weight-medium);</pre>
          </div>
          <div class="p-4 border rounded-box">
            <p class="font-semibold mb-2">Semibold (600)</p>
            <pre class="bg-base-200 p-2 rounded-box text-xs">font-weight: var(--font-weight-semibold);</pre>
          </div>
          <div class="p-4 border rounded-box">
            <p class="font-bold mb-2">Bold (700)</p>
            <pre class="bg-base-200 p-2 rounded-box text-xs">font-weight: var(--font-weight-bold);</pre>
          </div>
          <div class="p-4 border rounded-box">
            <p class="font-extrabold mb-2">Extrabold (800)</p>
            <pre class="bg-base-200 p-2 rounded-box text-xs">font-weight: var(--font-weight-extrabold);</pre>
          </div>
        </Grid>
        
        <h2 class="text-2xl font-bold mb-4">Elementos Tipográficos</h2>
        <Grid cols={{ sm: 1, md: 2 }} gap={6} class="mb-8">
          <div class="border rounded-box p-6">
            <h3 class="text-xl font-bold mb-4">Cabeçalhos</h3>
            <h1 class="mb-2">Cabeçalho h1</h1>
            <h2 class="mb-2">Cabeçalho h2</h2>
            <h3 class="mb-2">Cabeçalho h3</h3>
            <h4 class="mb-2">Cabeçalho h4</h4>
            <h5 class="mb-2">Cabeçalho h5</h5>
            <h6 class="mb-2">Cabeçalho h6</h6>
          </div>
          
          <div class="border rounded-box p-6">
            <h3 class="text-xl font-bold mb-4">Outros Elementos</h3>
            <p class="mb-4">Parágrafo: {loremIpsum}</p>
            <p class="mb-4"><strong>Texto em negrito</strong> e <em>texto em itálico</em></p>
            <p class="mb-4">Link: <a href="#">Exemplo de link</a></p>
            <p class="mb-4">Código: <code>console.log('Hello World');</code></p>
            <blockquote class="mb-4">
              Esta é uma citação de exemplo que demonstra o estilo de blockquote.
            </blockquote>
            <ul class="mb-4">
              <li>Item de lista não ordenada 1</li>
              <li>Item de lista não ordenada 2</li>
              <li>Item de lista não ordenada 3</li>
            </ul>
            <ol class="mb-4">
              <li>Item de lista ordenada 1</li>
              <li>Item de lista ordenada 2</li>
              <li>Item de lista ordenada 3</li>
            </ol>
          </div>
        </Grid>
        
        <h2 class="text-2xl font-bold mb-4">Estilos para Conteúdo Educacional</h2>
        <Grid cols={{ sm: 1, md: 2 }} gap={6} class="mb-8">
          <div class="border rounded-box p-6">
            <h3 class="text-xl font-bold mb-4">Estilo Educacional</h3>
            <div class="educational-title mb-2">Título Educacional</div>
            <div class="educational-subtitle mb-2">Subtítulo Educacional</div>
            <div class="educational-content mb-2">{alfabetizacaoText}</div>
            <div class="educational-note mb-2">Nota: Este é um exemplo de nota educacional.</div>
          </div>
          
          <div class="border rounded-box p-6">
            <h3 class="text-xl font-bold mb-4">Estilo Infantil</h3>
            <div class="children-title mb-2">Título Infantil</div>
            <div class="children-subtitle mb-2">Subtítulo Infantil</div>
            <div class="children-content mb-2">{alfabetizacaoText}</div>
            <div class="children-note mb-2">Nota: Este é um exemplo de nota para crianças.</div>
          </div>
        </Grid>
        
        <h2 class="text-2xl font-bold mb-4">Código CSS</h2>
        <Grid cols={{ sm: 1, md: 2 }} gap={4} class="mb-8">
          <DaisyCard title="Famílias de Fontes">
            <pre class="bg-base-200 p-4 rounded-box overflow-x-auto text-xs">{fontFamiliesCode}</pre>
          </DaisyCard>
          
          <DaisyCard title="Tamanhos de Fonte">
            <pre class="bg-base-200 p-4 rounded-box overflow-x-auto text-xs">{fontSizesCode}</pre>
          </DaisyCard>
          
          <DaisyCard title="Pesos de Fonte">
            <pre class="bg-base-200 p-4 rounded-box overflow-x-auto text-xs">{fontWeightsCode}</pre>
          </DaisyCard>
          
          <DaisyCard title="Alturas de Linha">
            <pre class="bg-base-200 p-4 rounded-box overflow-x-auto text-xs">{lineHeightsCode}</pre>
          </DaisyCard>
        </Grid>
      </Section>
    </PageTransition>
  </Container>
</BaseLayout>
