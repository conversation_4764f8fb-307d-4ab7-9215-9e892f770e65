---
import BaseLayout from '../../layouts/BaseLayout.astro';

const title = 'Variáveis CSS';

// Categorias de variáveis para demonstração
const categories = [
  {
    title: 'Cores',
    variables: [
      { name: '--blue-500', description: '<PERSON><PERSON> prim<PERSON> (azul)' },
      { name: '--red-500', description: '<PERSON><PERSON> (vermelho)' },
      { name: '--yellow-500', description: '<PERSON><PERSON> de acento (amarelo)' },
      { name: '--green-500', description: 'Cor de sucesso (verde)' },
      { name: '--orange-500', description: '<PERSON><PERSON> de alerta (laranja)' },
      { name: '--pink-500', description: '<PERSON><PERSON> de erro (rosa)' },
      { name: '--navy-500', description: 'Cor neutra (azul escuro)' },
      { name: '--gray-500', description: 'Cor neutra (cinza)' },
    ],
  },
  {
    title: 'Tipografia',
    variables: [
      { name: '--font-sans', description: '<PERSON>onte principal' },
      { name: '--font-display', description: 'Fonte para títulos' },
      { name: '--font-handwriting', description: 'Fonte manuscrita' },
      { name: '--font-fancy', description: 'Fonte decorativa' },
      { name: '--font-size-base', description: 'Tamanho de fonte base (1rem/16px)' },
      { name: '--font-size-xl', description: 'Tamanho de fonte grande (1.25rem/20px)' },
      { name: '--font-size-3xl', description: 'Tamanho de fonte título (1.875rem/30px)' },
      { name: '--font-weight-bold', description: 'Peso de fonte negrito (700)' },
    ],
  },
  {
    title: 'Espaçamento',
    variables: [
      { name: '--spacing-1', description: 'Espaçamento pequeno (0.25rem/4px)' },
      { name: '--spacing-2', description: 'Espaçamento pequeno (0.5rem/8px)' },
      { name: '--spacing-4', description: 'Espaçamento médio (1rem/16px)' },
      { name: '--spacing-8', description: 'Espaçamento grande (2rem/32px)' },
      { name: '--spacing-16', description: 'Espaçamento muito grande (4rem/64px)' },
      { name: '--component-padding-x', description: 'Padding horizontal de componentes' },
      { name: '--component-padding-y', description: 'Padding vertical de componentes' },
      { name: '--section-spacing', description: 'Espaçamento entre seções' },
    ],
  },
  {
    title: 'Bordas e Sombras',
    variables: [
      { name: '--border-radius-md', description: 'Raio de borda médio (0.25rem/4px)' },
      { name: '--border-radius-lg', description: 'Raio de borda grande (0.5rem/8px)' },
      { name: '--border-radius-full', description: 'Raio de borda circular (9999px)' },
      { name: '--border-width-1', description: 'Largura de borda padrão (1px)' },
      { name: '--border-width-2', description: 'Largura de borda média (2px)' },
      { name: '--shadow-sm', description: 'Sombra pequena' },
      { name: '--shadow-md', description: 'Sombra média' },
      { name: '--shadow-lg', description: 'Sombra grande' },
    ],
  },
  {
    title: 'Transições e Animações',
    variables: [
      { name: '--duration-150', description: 'Duração curta (150ms)' },
      { name: '--duration-300', description: 'Duração média (300ms)' },
      { name: '--duration-500', description: 'Duração longa (500ms)' },
      { name: '--ease-in-out', description: 'Função de temporização suave' },
      { name: '--ease-out', description: 'Função de temporização de saída' },
      { name: '--ease-in', description: 'Função de temporização de entrada' },
      { name: '--ease-linear', description: 'Função de temporização linear' },
    ],
  },
];

// Exemplos de uso
const examples = [
  {
    title: 'Botão com Variáveis CSS',
    code: `.button-primary {
  font-family: var(--font-sans);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-bold);
  color: white;
  background-color: var(--blue-500);
  padding: var(--spacing-2) var(--spacing-4);
  border-radius: var(--border-radius-md);
  border: none;
  box-shadow: var(--shadow-sm);
  transition: all var(--duration-150) var(--ease-in-out);
}

.button-primary:hover {
  background-color: var(--blue-600);
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}`,
  },
  {
    title: 'Card com Variáveis CSS',
    code: `.card {
  background-color: white;
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);
  padding: var(--component-padding-y) var(--component-padding-x);
  margin-bottom: var(--spacing-4);
}

.card-title {
  font-family: var(--font-display);
  font-size: var(--font-size-xl);
  color: var(--navy-500);
  margin-bottom: var(--spacing-2);
}

.card-content {
  font-family: var(--font-sans);
  font-size: var(--font-size-base);
  color: var(--gray-700);
  line-height: var(--line-height-relaxed);
}`,
  },
];
---

<BaseLayout title={title}>
  <div class="container mx-auto px-4 py-8">
    <h1 class="text-4xl font-bold mb-8 text-center">{title}</h1>
    
    <section class="mb-12">
      <h2 class="text-2xl font-bold mb-4">Visão Geral</h2>
      
      <div class="card p-6 mb-8">
        <p class="mb-4">
          O projeto Estação da Alfabetização utiliza um sistema abrangente de variáveis CSS para garantir consistência visual e facilitar a manutenção.
          Estas variáveis estão definidas no arquivo <code>src/styles/variables.css</code> e estão disponíveis globalmente em toda a aplicação.
        </p>
        
        <p>
          As variáveis CSS permitem definir valores uma única vez e reutilizá-los em todo o projeto, facilitando atualizações e garantindo consistência.
        </p>
      </div>
    </section>
    
    <section class="mb-12">
      <h2 class="text-2xl font-bold mb-4">Categorias de Variáveis</h2>
      
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        {categories.map(category => (
          <div class="card p-6">
            <h3 class="text-xl font-bold mb-4">{category.title}</h3>
            
            <div class="space-y-4">
              {category.variables.map(variable => (
                <div class="variable-demo">
                  <div class="flex justify-between items-center mb-1">
                    <code class="text-sm font-mono bg-base-200 px-2 py-1 rounded">{variable.name}</code>
                    <span class="text-sm text-base-content/70">{variable.description}</span>
                  </div>
                  
                  <div class="variable-preview h-8 w-full rounded" style={`var-name: ${variable.name};`}></div>
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>
    </section>
    
    <section class="mb-12">
      <h2 class="text-2xl font-bold mb-4">Exemplos de Uso</h2>
      
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        {examples.map(example => (
          <div class="card p-6">
            <h3 class="text-xl font-bold mb-4">{example.title}</h3>
            
            <pre class="bg-base-200 p-4 rounded-lg overflow-x-auto text-sm"><code>{example.code}</code></pre>
          </div>
        ))}
      </div>
    </section>
    
    <section class="mb-12">
      <h2 class="text-2xl font-bold mb-4">Demonstração Interativa</h2>
      
      <div class="card p-6">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h3 class="text-xl font-bold mb-4">Botão com Variáveis CSS</h3>
            
            <div class="space-y-4">
              <button class="demo-button">Botão Primário</button>
              <button class="demo-button demo-button-secondary">Botão Secundário</button>
              <button class="demo-button demo-button-accent">Botão de Acento</button>
            </div>
          </div>
          
          <div>
            <h3 class="text-xl font-bold mb-4">Card com Variáveis CSS</h3>
            
            <div class="demo-card">
              <h4 class="demo-card-title">Título do Card</h4>
              <p class="demo-card-content">
                Este é um exemplo de card estilizado usando variáveis CSS.
                O espaçamento, cores, tipografia e sombras são todos definidos usando variáveis.
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
    
    <section class="mb-12">
      <h2 class="text-2xl font-bold mb-4">Documentação Completa</h2>
      
      <div class="card p-6">
        <p class="mb-4">
          Para uma lista completa de todas as variáveis CSS disponíveis, consulte a documentação:
        </p>
        
        <a href="/docs/CSS_VARIABLES.md" class="link link-primary">CSS_VARIABLES.md</a>
      </div>
    </section>
  </div>
</BaseLayout>

<style>
  /* Estilos para demonstração de variáveis */
  .variable-preview {
    position: relative;
  }
  
  /* Cores */
  .variable-preview[style*="--blue"] {
    background-color: var(--blue-500);
  }
  
  .variable-preview[style*="--red"] {
    background-color: var(--red-500);
  }
  
  .variable-preview[style*="--yellow"] {
    background-color: var(--yellow-500);
  }
  
  .variable-preview[style*="--green"] {
    background-color: var(--green-500);
  }
  
  .variable-preview[style*="--orange"] {
    background-color: var(--orange-500);
  }
  
  .variable-preview[style*="--pink"] {
    background-color: var(--pink-500);
  }
  
  .variable-preview[style*="--navy"] {
    background-color: var(--navy-500);
  }
  
  .variable-preview[style*="--gray"] {
    background-color: var(--gray-500);
  }
  
  /* Tipografia */
  .variable-preview[style*="--font-sans"] {
    background-color: var(--gray-200);
  }
  
  .variable-preview[style*="--font-sans"]::after {
    content: "Proxima Nova";
    font-family: var(--font-sans);
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: var(--gray-700);
  }
  
  .variable-preview[style*="--font-display"] {
    background-color: var(--gray-200);
  }
  
  .variable-preview[style*="--font-display"]::after {
    content: "Coiny";
    font-family: var(--font-display);
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: var(--gray-700);
  }
  
  .variable-preview[style*="--font-handwriting"] {
    background-color: var(--gray-200);
  }
  
  .variable-preview[style*="--font-handwriting"]::after {
    content: "Delius Swash Caps";
    font-family: var(--font-handwriting);
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: var(--gray-700);
  }
  
  .variable-preview[style*="--font-fancy"] {
    background-color: var(--gray-200);
  }
  
  .variable-preview[style*="--font-fancy"]::after {
    content: "Gwendolyn";
    font-family: var(--font-fancy);
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: var(--gray-700);
  }
  
  .variable-preview[style*="--font-size"] {
    background-color: var(--gray-200);
  }
  
  .variable-preview[style*="--font-size-base"]::after {
    content: "Texto Base";
    font-size: var(--font-size-base);
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: var(--gray-700);
  }
  
  .variable-preview[style*="--font-size-xl"]::after {
    content: "Texto Grande";
    font-size: var(--font-size-xl);
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: var(--gray-700);
  }
  
  .variable-preview[style*="--font-size-3xl"]::after {
    content: "Título";
    font-size: var(--font-size-3xl);
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: var(--gray-700);
  }
  
  .variable-preview[style*="--font-weight-bold"] {
    background-color: var(--gray-200);
  }
  
  .variable-preview[style*="--font-weight-bold"]::after {
    content: "Texto Negrito";
    font-weight: var(--font-weight-bold);
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: var(--gray-700);
  }
  
  /* Espaçamento */
  .variable-preview[style*="--spacing"] {
    background-color: var(--blue-200);
  }
  
  .variable-preview[style*="--spacing"]::after {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: var(--blue-500);
  }
  
  .variable-preview[style*="--spacing-1"]::after {
    width: var(--spacing-1);
    height: var(--spacing-1);
  }
  
  .variable-preview[style*="--spacing-2"]::after {
    width: var(--spacing-2);
    height: var(--spacing-2);
  }
  
  .variable-preview[style*="--spacing-4"]::after {
    width: var(--spacing-4);
    height: var(--spacing-4);
  }
  
  .variable-preview[style*="--spacing-8"]::after {
    width: var(--spacing-8);
    height: var(--spacing-8);
  }
  
  .variable-preview[style*="--spacing-16"]::after {
    width: var(--spacing-16);
    height: 100%;
  }
  
  .variable-preview[style*="--component-padding"] {
    background-color: var(--blue-200);
  }
  
  .variable-preview[style*="--component-padding"]::after {
    content: "";
    position: absolute;
    background-color: var(--blue-500);
  }
  
  .variable-preview[style*="--component-padding-x"]::after {
    top: 0;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: var(--component-padding-x);
    height: 100%;
  }
  
  .variable-preview[style*="--component-padding-y"]::after {
    left: 0;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    height: var(--component-padding-y);
    width: 100%;
  }
  
  .variable-preview[style*="--section-spacing"]::after {
    width: 100%;
    height: 4px;
  }
  
  /* Bordas e Sombras */
  .variable-preview[style*="--border-radius"] {
    background-color: var(--blue-500);
  }
  
  .variable-preview[style*="--border-radius-md"] {
    border-radius: var(--border-radius-md);
  }
  
  .variable-preview[style*="--border-radius-lg"] {
    border-radius: var(--border-radius-lg);
  }
  
  .variable-preview[style*="--border-radius-full"] {
    border-radius: var(--border-radius-full);
  }
  
  .variable-preview[style*="--border-width"] {
    background-color: white;
    border-color: var(--blue-500);
    border-style: solid;
  }
  
  .variable-preview[style*="--border-width-1"] {
    border-width: var(--border-width-1);
  }
  
  .variable-preview[style*="--border-width-2"] {
    border-width: var(--border-width-2);
  }
  
  .variable-preview[style*="--shadow"] {
    background-color: white;
  }
  
  .variable-preview[style*="--shadow-sm"] {
    box-shadow: var(--shadow-sm);
  }
  
  .variable-preview[style*="--shadow-md"] {
    box-shadow: var(--shadow-md);
  }
  
  .variable-preview[style*="--shadow-lg"] {
    box-shadow: var(--shadow-lg);
  }
  
  /* Transições e Animações */
  .variable-preview[style*="--duration"], 
  .variable-preview[style*="--ease"] {
    background-color: var(--blue-200);
    overflow: hidden;
  }
  
  .variable-preview[style*="--duration"]::after,
  .variable-preview[style*="--ease"]::after {
    content: "";
    position: absolute;
    width: 20px;
    height: 20px;
    background-color: var(--blue-500);
    border-radius: 50%;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    animation: move-dot 2s infinite;
  }
  
  .variable-preview[style*="--duration-150"]::after {
    animation-duration: var(--duration-150);
  }
  
  .variable-preview[style*="--duration-300"]::after {
    animation-duration: var(--duration-300);
  }
  
  .variable-preview[style*="--duration-500"]::after {
    animation-duration: var(--duration-500);
  }
  
  .variable-preview[style*="--ease-in-out"]::after {
    animation-timing-function: var(--ease-in-out);
  }
  
  .variable-preview[style*="--ease-out"]::after {
    animation-timing-function: var(--ease-out);
  }
  
  .variable-preview[style*="--ease-in"]::after {
    animation-timing-function: var(--ease-in);
  }
  
  .variable-preview[style*="--ease-linear"]::after {
    animation-timing-function: var(--ease-linear);
  }
  
  @keyframes move-dot {
    0% { left: 0; }
    50% { left: calc(100% - 20px); }
    100% { left: 0; }
  }
  
  /* Exemplos interativos */
  .demo-button {
    font-family: var(--font-sans);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-bold);
    color: white;
    background-color: var(--blue-500);
    padding: var(--spacing-2) var(--spacing-4);
    border-radius: var(--border-radius-md);
    border: none;
    box-shadow: var(--shadow-sm);
    transition: all var(--duration-150) var(--ease-in-out);
    cursor: pointer;
  }
  
  .demo-button:hover {
    background-color: var(--blue-600);
    box-shadow: var(--shadow-md);
    transform: translateY(-1px);
  }
  
  .demo-button-secondary {
    background-color: var(--red-500);
  }
  
  .demo-button-secondary:hover {
    background-color: var(--red-600);
  }
  
  .demo-button-accent {
    background-color: var(--yellow-500);
    color: var(--gray-900);
  }
  
  .demo-button-accent:hover {
    background-color: var(--yellow-600);
  }
  
  .demo-card {
    background-color: white;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    padding: var(--component-padding-y) var(--component-padding-x);
    transition: all var(--duration-300) var(--ease-in-out);
  }
  
  .demo-card:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
  }
  
  .demo-card-title {
    font-family: var(--font-display);
    font-size: var(--font-size-xl);
    color: var(--navy-500);
    margin-bottom: var(--spacing-2);
  }
  
  .demo-card-content {
    font-family: var(--font-sans);
    font-size: var(--font-size-base);
    color: var(--gray-700);
    line-height: var(--line-height-relaxed);
  }
</style>
