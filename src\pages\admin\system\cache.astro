---
import CacheMonitor from '@components/admin/CacheMonitor.astro';
import CacheTypeManager from '@components/admin/CacheTypeManager.astro';
import { checkAdmin } from '@helpers/authGuard';
import AdminLayout from '@layouts/AdminLayout.astro';

// Protege a rota verificando se o usuário é admin
const authRedirect = await checkAdmin(Astro);
if (authRedirect) return authRedirect;
---

<AdminLayout title="Gerenciamento de Cache">
  <div class="container mx-auto p-4">
    <h1 class="text-2xl font-bold mb-6">Gerenciamento de Cache</h1>

    <div class="mb-6">
      <div class="alert alert-info">
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" class="stroke-current shrink-0 w-6 h-6"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>
        <div>
          <span class="font-bold">Sobre o Cache</span>
          <p>O sistema utiliza Valkey (fork do Redis) para armazenar dados em cache e melhorar a performance. Use esta página para monitorar e gerenciar o cache.</p>
        </div>
      </div>
    </div>

    <!-- Monitor de Cache -->
    <CacheMonitor />

    <!-- Gerenciador de Cache por Tipo -->
    <CacheTypeManager />

    <!-- Configurações de Cache -->
    <div class="card bg-base-100 shadow-xl">
      <div class="card-body">
        <h2 class="card-title">Configurações de Cache</h2>

        <div class="overflow-x-auto">
          <table class="table w-full">
            <thead>
              <tr>
                <th>Tipo de Cache</th>
                <th>TTL (Tempo de Vida)</th>
                <th>Compressão</th>
                <th>Prefixo</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>Usuários</td>
                <td>30 minutos</td>
                <td>Ativada (>1KB)</td>
                <td><code>user:</code></td>
              </tr>
              <tr>
                <td>Produtos</td>
                <td>2 horas</td>
                <td>Ativada (>1KB)</td>
                <td><code>product:</code></td>
              </tr>
              <tr>
                <td>Categorias</td>
                <td>2 horas</td>
                <td>Ativada (>1KB)</td>
                <td><code>category:</code></td>
              </tr>
              <tr>
                <td>Pedidos</td>
                <td>30 minutos</td>
                <td>Ativada (>1KB)</td>
                <td><code>order:</code></td>
              </tr>
              <tr>
                <td>Pagamentos</td>
                <td>30 minutos</td>
                <td>Ativada (>1KB)</td>
                <td><code>payment:</code></td>
              </tr>
              <tr>
                <td>Sessões</td>
                <td>24 horas</td>
                <td>Ativada (>1KB)</td>
                <td><code>session:</code></td>
              </tr>
              <tr>
                <td>Tokens</td>
                <td>24 horas</td>
                <td>Ativada (>1KB)</td>
                <td><code>token:</code></td>
              </tr>
              <tr>
                <td>Consultas</td>
                <td>5 minutos</td>
                <td>Ativada (>1KB)</td>
                <td><code>query:</code></td>
              </tr>
            </tbody>
          </table>
        </div>

        <div class="alert alert-warning mt-4">
          <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" /></svg>
          <span>Para alterar estas configurações, edite as variáveis de ambiente ou o arquivo de configuração do cache.</span>
        </div>
      </div>
    </div>
  </div>
</AdminLayout>


