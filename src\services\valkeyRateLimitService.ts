/**
 * Serviço de Rate Limiting com Valkey
 * 
 * Este serviço implementa controle de taxa de requisições (rate limiting)
 * utilizando algoritmos eficientes do Valkey para proteção contra abusos.
 * 
 * Implementa:
 * - Algoritmo de sliding window para contagem precisa
 * - Suporte para diferentes tipos de limites
 * - Bloqueio automático após exceder limites
 * - Monitoramento e métricas de uso
 */

import { cacheService } from '@services/cacheService';
import { logger } from '@utils/logger';
import { applicationMonitoringService } from '@services/applicationMonitoringService';

/**
 * Interface para configuração de limite de taxa
 */
export interface RateLimitConfig {
  /**
   * Número máximo de requisições permitidas no período
   */
  maxRequests: number;

  /**
   * Período de tempo em segundos
   */
  windowSizeInSeconds: number;

  /**
   * Tempo de bloqueio em segundos quando o limite é excedido
   * @default 0 (sem bloqueio)
   */
  blockDurationInSeconds?: number;
}

/**
 * Interface para resultado da verificação de limite
 */
export interface RateLimitResult {
  /**
   * Se a requisição deve ser limitada
   */
  limited: boolean;

  /**
   * Número de requisições restantes no período
   */
  remaining: number;

  /**
   * Tempo em segundos até o reset do contador
   */
  resetInSeconds: number;

  /**
   * Tempo em segundos até o fim do bloqueio (se estiver bloqueado)
   */
  blockExpiresInSeconds?: number;
}

/**
 * Tipos de recursos que podem ser limitados
 */
export enum RateLimitType {
  /**
   * Limite global por IP
   */
  GLOBAL = 'global',

  /**
   * Limite para tentativas de login
   */
  LOGIN = 'login',

  /**
   * Limite para criação de conta
   */
  SIGNUP = 'signup',

  /**
   * Limite para envio de formulário de contato
   */
  CONTACT = 'contact',

  /**
   * Limite para API pública
   */
  API = 'api',

  /**
   * Limite para upload de arquivos
   */
  UPLOAD = 'upload',

  /**
   * Limite para requisições de pagamento
   */
  PAYMENT = 'payment',
}

/**
 * Configurações padrão por tipo de limite
 */
const DEFAULT_CONFIGS: Record<RateLimitType, RateLimitConfig> = {
  [RateLimitType.GLOBAL]: {
    maxRequests: 1000,
    windowSizeInSeconds: 60 * 60, // 1 hora
    blockDurationInSeconds: 0, // Sem bloqueio
  },
  [RateLimitType.LOGIN]: {
    maxRequests: 5,
    windowSizeInSeconds: 60 * 5, // 5 minutos
    blockDurationInSeconds: 60 * 15, // 15 minutos de bloqueio
  },
  [RateLimitType.SIGNUP]: {
    maxRequests: 3,
    windowSizeInSeconds: 60 * 60, // 1 hora
    blockDurationInSeconds: 60 * 60 * 24, // 24 horas de bloqueio
  },
  [RateLimitType.CONTACT]: {
    maxRequests: 5,
    windowSizeInSeconds: 60 * 60, // 1 hora
    blockDurationInSeconds: 60 * 60 * 3, // 3 horas de bloqueio
  },
  [RateLimitType.API]: {
    maxRequests: 100,
    windowSizeInSeconds: 60, // 1 minuto
    blockDurationInSeconds: 60 * 5, // 5 minutos de bloqueio
  },
  [RateLimitType.UPLOAD]: {
    maxRequests: 10,
    windowSizeInSeconds: 60 * 10, // 10 minutos
    blockDurationInSeconds: 60 * 30, // 30 minutos de bloqueio
  },
  [RateLimitType.PAYMENT]: {
    maxRequests: 10,
    windowSizeInSeconds: 60 * 10, // 10 minutos
    blockDurationInSeconds: 0, // Sem bloqueio
  },
};

/**
 * Serviço de Rate Limiting com Valkey
 */
export const valkeyRateLimitService = {
  /**
   * Prefixo para chaves de cache
   */
  CACHE_PREFIX: 'ratelimit:',

  /**
   * Prefixo para chaves de bloqueio
   */
  BLOCK_PREFIX: 'ratelimit:block:',

  /**
   * Prefixo para chaves de métricas
   */
  METRICS_PREFIX: 'ratelimit:metrics:',

  /**
   * Inicializa o serviço de rate limiting
   */
  async initialize(): Promise<void> {
    try {
      logger.info('Inicializando serviço de rate limiting com Valkey');
      
      // Verificar conexão com o Valkey
      const client = await cacheService.getClient();
      await client.ping();
      
      // Carregar scripts Lua para o Valkey
      await this.loadScripts();
      
      logger.info('Serviço de rate limiting inicializado com sucesso');
    } catch (error) {
      logger.error('Erro ao inicializar serviço de rate limiting:', error);
      throw error;
    }
  },

  /**
   * Carrega scripts Lua para o Valkey
   * Estes scripts permitem operações atômicas para rate limiting
   */
  async loadScripts(): Promise<void> {
    try {
      const client = await cacheService.getClient();
      
      // Script para sliding window rate limiting
      await client.scriptLoad(`
        local key = KEYS[1]
        local now = tonumber(ARGV[1])
        local window = tonumber(ARGV[2])
        local limit = tonumber(ARGV[3])
        
        -- Remove timestamps older than the window
        redis.call('ZREMRANGEBYSCORE', key, 0, now - window)
        
        -- Count current requests in window
        local count = redis.call('ZCARD', key)
        
        -- Check if limit exceeded
        if count >= limit then
          return {1, count, limit}
        end
        
        -- Add current timestamp
        redis.call('ZADD', key, now, now .. ':' .. math.random())
        
        -- Set expiration
        redis.call('EXPIRE', key, window)
        
        -- Return result: not limited, current count, limit
        return {0, count + 1, limit}
      `);
      
      logger.info('Scripts Lua carregados com sucesso');
    } catch (error) {
      logger.error('Erro ao carregar scripts Lua:', error);
      throw error;
    }
  },

  /**
   * Verifica se uma requisição deve ser limitada
   * @param identifier - Identificador único (IP, usuário, etc)
   * @param type - Tipo de limite
   * @param config - Configuração personalizada (opcional)
   * @returns Resultado da verificação
   */
  async check(
    identifier: string,
    type: RateLimitType,
    config?: Partial<RateLimitConfig>
  ): Promise<RateLimitResult> {
    try {
      // Obter configuração completa
      const fullConfig = {
        ...DEFAULT_CONFIGS[type],
        ...config,
      };

      // Verificar se o identificador está bloqueado
      const isBlocked = await this.isBlocked(identifier, type);

      if (isBlocked.blocked) {
        // Registrar métrica de bloqueio
        this.recordMetric(type, 'blocked');
        
        return {
          limited: true,
          remaining: 0,
          resetInSeconds: 0,
          blockExpiresInSeconds: isBlocked.expiresInSeconds,
        };
      }

      // Gerar chave de cache
      const cacheKey = this.getCacheKey(identifier, type);
      
      // Executar verificação de limite usando Valkey
      const result = await this.checkRateLimit(
        cacheKey,
        fullConfig.windowSizeInSeconds,
        fullConfig.maxRequests
      );
      
      const limited = result.limited;
      const remaining = result.remaining;

      // Se excedeu o limite e tem duração de bloqueio, bloquear o identificador
      if (
        limited &&
        fullConfig.blockDurationInSeconds &&
        fullConfig.blockDurationInSeconds > 0
      ) {
        await this.blockIdentifier(
          identifier,
          type,
          fullConfig.blockDurationInSeconds
        );
        
        // Registrar métrica de bloqueio
        this.recordMetric(type, 'new_block');
      }
      
      // Registrar métrica de verificação
      this.recordMetric(type, limited ? 'limited' : 'allowed');

      return {
        limited,
        remaining,
        resetInSeconds: fullConfig.windowSizeInSeconds,
        blockExpiresInSeconds:
          limited && fullConfig.blockDurationInSeconds
            ? fullConfig.blockDurationInSeconds
            : undefined,
      };
    } catch (error) {
      logger.error('Erro ao verificar limite de taxa:', error);

      // Em caso de erro, permitir a requisição
      return {
        limited: false,
        remaining: 1,
        resetInSeconds: 60,
      };
    }
  },

  /**
   * Executa verificação de rate limit usando sliding window
   * @param key - Chave de cache
   * @param windowSize - Tamanho da janela em segundos
   * @param limit - Limite de requisições
   * @returns Resultado da verificação
   */
  async checkRateLimit(
    key: string,
    windowSize: number,
    limit: number
  ): Promise<{ limited: boolean; remaining: number }> {
    try {
      const client = await cacheService.getClient();
      const now = Math.floor(Date.now() / 1000);
      
      // Usar script Lua para operação atômica
      const result = await client.evalSha(
        await client.scriptLoad(`
          local key = KEYS[1]
          local now = tonumber(ARGV[1])
          local window = tonumber(ARGV[2])
          local limit = tonumber(ARGV[3])
          
          -- Remove timestamps older than the window
          redis.call('ZREMRANGEBYSCORE', key, 0, now - window)
          
          -- Count current requests in window
          local count = redis.call('ZCARD', key)
          
          -- Check if limit exceeded
          if count >= limit then
            return {1, count, limit}
          end
          
          -- Add current timestamp
          redis.call('ZADD', key, now, now .. ':' .. math.random())
          
          -- Set expiration
          redis.call('EXPIRE', key, window)
          
          -- Return result: not limited, current count, limit
          return {0, count + 1, limit}
        `),
        1,
        key,
        now.toString(),
        windowSize.toString(),
        limit.toString()
      );
      
      const limited = result[0] === 1;
      const count = parseInt(result[1], 10);
      const maxRequests = parseInt(result[2], 10);
      
      return {
        limited,
        remaining: limited ? 0 : maxRequests - count,
      };
    } catch (error) {
      logger.error('Erro ao executar verificação de rate limit:', error);
      
      // Em caso de erro, permitir a requisição
      return {
        limited: false,
        remaining: 1,
      };
    }
  },

  /**
   * Verifica se um identificador está bloqueado
   * @param identifier - Identificador único
   * @param type - Tipo de limite
   * @returns Resultado da verificação
   */
  async isBlocked(
    identifier: string,
    type: RateLimitType
  ): Promise<{ blocked: boolean; expiresInSeconds: number }> {
    try {
      // Gerar chave de bloqueio
      const blockKey = this.getBlockKey(identifier, type);
      
      // Verificar se existe no cache
      const client = await cacheService.getClient();
      const exists = await client.exists(blockKey);
      
      if (!exists) {
        return {
          blocked: false,
          expiresInSeconds: 0,
        };
      }
      
      // Obter TTL
      const ttl = await client.ttl(blockKey);
      
      return {
        blocked: true,
        expiresInSeconds: ttl > 0 ? ttl : 0,
      };
    } catch (error) {
      logger.error('Erro ao verificar bloqueio:', error);
      
      // Em caso de erro, considerar não bloqueado
      return {
        blocked: false,
        expiresInSeconds: 0,
      };
    }
  },

  /**
   * Bloqueia um identificador por um período de tempo
   * @param identifier - Identificador único
   * @param type - Tipo de limite
   * @param durationInSeconds - Duração do bloqueio em segundos
   * @returns Verdadeiro se o bloqueio foi aplicado com sucesso
   */
  async blockIdentifier(
    identifier: string,
    type: RateLimitType,
    durationInSeconds: number
  ): Promise<boolean> {
    try {
      // Gerar chave de bloqueio
      const blockKey = this.getBlockKey(identifier, type);
      
      // Armazenar no cache com TTL
      const client = await cacheService.getClient();
      await client.set(
        blockKey,
        JSON.stringify({
          identifier,
          type,
          blockedAt: Date.now(),
        }),
        {
          EX: durationInSeconds,
        }
      );
      
      // Registrar evento de bloqueio
      logger.warn(`Identificador bloqueado por rate limiting: ${identifier} (${type})`, {
        identifier,
        type,
        durationInSeconds,
      });
      
      return true;
    } catch (error) {
      logger.error('Erro ao bloquear identificador:', error);
      return false;
    }
  },

  /**
   * Desbloqueia um identificador
   * @param identifier - Identificador único
   * @param type - Tipo de limite
   * @returns Verdadeiro se o desbloqueio foi aplicado com sucesso
   */
  async unblockIdentifier(
    identifier: string,
    type: RateLimitType
  ): Promise<boolean> {
    try {
      // Gerar chave de bloqueio
      const blockKey = this.getBlockKey(identifier, type);
      
      // Remover do cache
      const client = await cacheService.getClient();
      await client.del(blockKey);
      
      // Registrar evento de desbloqueio
      logger.info(`Identificador desbloqueado: ${identifier} (${type})`, {
        identifier,
        type,
      });
      
      return true;
    } catch (error) {
      logger.error('Erro ao desbloquear identificador:', error);
      return false;
    }
  },

  /**
   * Obtém todos os bloqueios ativos
   * @returns Lista de chaves de bloqueio
   */
  async getActiveBlocks(): Promise<string[]> {
    try {
      const client = await cacheService.getClient();
      return await client.keys(`${this.BLOCK_PREFIX}*`);
    } catch (error) {
      logger.error('Erro ao obter bloqueios ativos:', error);
      return [];
    }
  },

  /**
   * Gera chave de cache para um identificador e tipo
   * @param identifier - Identificador único
   * @param type - Tipo de limite
   * @returns Chave de cache
   */
  getCacheKey(identifier: string, type: RateLimitType): string {
    return `${this.CACHE_PREFIX}${type}:${identifier}`;
  },

  /**
   * Gera chave de bloqueio para um identificador e tipo
   * @param identifier - Identificador único
   * @param type - Tipo de limite
   * @returns Chave de bloqueio
   */
  getBlockKey(identifier: string, type: RateLimitType): string {
    return `${this.BLOCK_PREFIX}${type}:${identifier}`;
  },

  /**
   * Registra métrica de rate limiting
   * @param type - Tipo de limite
   * @param action - Ação realizada
   */
  recordMetric(type: RateLimitType, action: 'allowed' | 'limited' | 'blocked' | 'new_block'): void {
    try {
      // Incrementar contador no serviço de monitoramento
      applicationMonitoringService.incrementCounter(`ratelimit_${type}_${action}`);
      
      // Incrementar contador no Valkey para estatísticas em tempo real
      this.incrementMetricCounter(`${type}:${action}`).catch(error => {
        logger.error('Erro ao incrementar contador de métrica:', error);
      });
    } catch (error) {
      logger.error('Erro ao registrar métrica:', error);
    }
  },

  /**
   * Incrementa contador de métrica no Valkey
   * @param key - Chave da métrica
   */
  async incrementMetricCounter(key: string): Promise<void> {
    try {
      const client = await cacheService.getClient();
      const metricKey = `${this.METRICS_PREFIX}${key}`;
      
      // Incrementar contador
      await client.incr(metricKey);
      
      // Definir TTL se não existir
      const ttl = await client.ttl(metricKey);
      if (ttl < 0) {
        await client.expire(metricKey, 60 * 60 * 24); // 24 horas
      }
    } catch (error) {
      logger.error('Erro ao incrementar contador de métrica:', error);
    }
  },

  /**
   * Obtém configuração para um tipo de limite
   * @param type - Tipo de limite
   * @returns Configuração
   */
  getConfig(type: RateLimitType): RateLimitConfig {
    return DEFAULT_CONFIGS[type];
  },
};
