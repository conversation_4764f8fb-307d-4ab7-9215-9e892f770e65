---
import { PageTransition } from '../../components/transitions';
import { PersistentElement } from '../../components/transitions';
import DaisyButton from '../../components/ui/DaisyButton.astro';
import DaisyCard from '../../components/ui/DaisyCard.astro';
import Breadcrumbs from '../../components/ui/navigation/Breadcrumbs.astro';
import Tabs from '../../components/ui/navigation/Tabs.astro';
/**
 * Página de exemplo para demonstrar as transições entre páginas
 * Esta página mostra diferentes tipos de transições disponíveis
 */
import BaseLayout from '../../layouts/BaseLayout.astro';
import { Container, Grid, Section } from '../../layouts/grid';

// Título da página
const title = 'Demonstração de Transições';

// Breadcrumbs para a página atual
const breadcrumbItems = [
  { href: '/', label: 'In<PERSON>cio' },
  { href: '/examples', label: 'Exemplos' },
  { label: 'Transições' },
];

// Tabs para os diferentes tipos de transições
const transitionTabs = [
  { id: 'fade', label: 'Fade', isActive: true },
  { id: 'slide', label: 'Slide' },
  { id: 'scale', label: 'Scale' },
  { id: 'flip', label: 'Flip' },
  { id: 'persistent', label: 'Elementos Persistentes' },
];

// Exemplos de código
const fadeCode = `import { PageTransition } from '../components/transitions';

<PageTransition type="fade" duration="0.3s">
  <div>Conteúdo com transição fade</div>
</PageTransition>`;

const slideCode = `import { PageTransition } from '../components/transitions';

<PageTransition type="slide" direction="left" duration="0.4s">
  <div>Conteúdo com transição slide</div>
</PageTransition>`;

const scaleCode = `import { PageTransition } from '../components/transitions';

<PageTransition type="scale" duration="0.3s">
  <div>Conteúdo com transição scale</div>
</PageTransition>`;

const flipCode = `import { PageTransition } from '../components/transitions';

<PageTransition type="flip" direction="up" duration="0.5s">
  <div>Conteúdo com transição flip</div>
</PageTransition>`;

const persistentCode = `import { PersistentElement } from '../components/transitions';

<PersistentElement id="header">
  <header>Este elemento permanecerá durante a transição</header>
</PersistentElement>`;

// Exemplos de páginas para demonstração
const demoPages = [
  { name: 'Página Inicial', path: '/' },
  { name: 'Sobre', path: '/sobre' },
  { name: 'Produtos', path: '/produtos' },
  { name: 'Blog', path: '/blog' },
  { name: 'Contato', path: '/contato' },
];
---

<BaseLayout title={title} enableViewTransitions={true} transitionType="fade">
  <Container>
    <Section>
      <Breadcrumbs items={breadcrumbItems} class="mb-6" />
      
      <PersistentElement id="transition-title" animationType="fade">
        <h1 class="text-3xl font-bold mb-6">{title}</h1>
      </PersistentElement>
      
      <p class="mb-8">
        Esta página demonstra as diferentes transições disponíveis no projeto Estação da Alfabetização.
        As transições são implementadas usando a View Transitions API e possuem fallbacks para navegadores sem suporte.
      </p>
      
      <Tabs tabs={transitionTabs}>
        <div slot="fade" class="py-6">
          <PageTransition type="fade" duration="0.5s">
            <Grid cols={{ sm: 1, md: 2 }} gap={6}>
              <DaisyCard title="Transição Fade">
                <p class="mb-4">
                  A transição fade é a mais suave e simples. Ela faz com que o conteúdo desvaneça ao sair e apareça gradualmente ao entrar.
                </p>
                <pre class="bg-base-200 p-4 rounded-box overflow-x-auto mb-4"><code>{fadeCode}</code></pre>
                <div class="flex justify-end">
                  <DaisyButton href="/examples/transitions?type=fade" color="primary">
                    Ver exemplo
                  </DaisyButton>
                </div>
              </DaisyCard>
              
              <div class="border rounded-box p-6 bg-base-200 flex items-center justify-center">
                <div class="demo-box fade-demo">
                  <h3 class="text-xl font-bold mb-2">Demonstração</h3>
                  <p>Este elemento tem uma transição fade</p>
                </div>
              </div>
            </Grid>
          </PageTransition>
        </div>
        
        <div slot="slide" class="py-6">
          <PageTransition type="slide" direction="left" duration="0.5s">
            <Grid cols={{ sm: 1, md: 2 }} gap={6}>
              <DaisyCard title="Transição Slide">
                <p class="mb-4">
                  A transição slide move o conteúdo na direção especificada. É útil para indicar navegação entre páginas sequenciais.
                </p>
                <pre class="bg-base-200 p-4 rounded-box overflow-x-auto mb-4"><code>{slideCode}</code></pre>
                <div class="flex justify-end">
                  <DaisyButton href="/examples/transitions?type=slide" color="primary">
                    Ver exemplo
                  </DaisyButton>
                </div>
              </DaisyCard>
              
              <div class="border rounded-box p-6 bg-base-200 flex items-center justify-center">
                <div class="demo-box slide-demo">
                  <h3 class="text-xl font-bold mb-2">Demonstração</h3>
                  <p>Este elemento tem uma transição slide</p>
                </div>
              </div>
            </Grid>
          </PageTransition>
        </div>
        
        <div slot="scale" class="py-6">
          <PageTransition type="scale" duration="0.5s">
            <Grid cols={{ sm: 1, md: 2 }} gap={6}>
              <DaisyCard title="Transição Scale">
                <p class="mb-4">
                  A transição scale aumenta ou diminui o tamanho do conteúdo. É útil para elementos que aparecem ou desaparecem.
                </p>
                <pre class="bg-base-200 p-4 rounded-box overflow-x-auto mb-4"><code>{scaleCode}</code></pre>
                <div class="flex justify-end">
                  <DaisyButton href="/examples/transitions?type=scale" color="primary">
                    Ver exemplo
                  </DaisyButton>
                </div>
              </DaisyCard>
              
              <div class="border rounded-box p-6 bg-base-200 flex items-center justify-center">
                <div class="demo-box scale-demo">
                  <h3 class="text-xl font-bold mb-2">Demonstração</h3>
                  <p>Este elemento tem uma transição scale</p>
                </div>
              </div>
            </Grid>
          </PageTransition>
        </div>
        
        <div slot="flip" class="py-6">
          <PageTransition type="flip" direction="up" duration="0.5s">
            <Grid cols={{ sm: 1, md: 2 }} gap={6}>
              <DaisyCard title="Transição Flip">
                <p class="mb-4">
                  A transição flip rotaciona o conteúdo em 3D. É uma transição mais dramática, ideal para mudanças significativas de conteúdo.
                </p>
                <pre class="bg-base-200 p-4 rounded-box overflow-x-auto mb-4"><code>{flipCode}</code></pre>
                <div class="flex justify-end">
                  <DaisyButton href="/examples/transitions?type=flip" color="primary">
                    Ver exemplo
                  </DaisyButton>
                </div>
              </DaisyCard>
              
              <div class="border rounded-box p-6 bg-base-200 flex items-center justify-center">
                <div class="demo-box flip-demo">
                  <h3 class="text-xl font-bold mb-2">Demonstração</h3>
                  <p>Este elemento tem uma transição flip</p>
                </div>
              </div>
            </Grid>
          </PageTransition>
        </div>
        
        <div slot="persistent" class="py-6">
          <PageTransition type="fade" duration="0.5s">
            <Grid cols={{ sm: 1, md: 2 }} gap={6}>
              <DaisyCard title="Elementos Persistentes">
                <p class="mb-4">
                  Elementos persistentes mantêm sua posição e estado durante as transições, criando uma experiência contínua.
                </p>
                <pre class="bg-base-200 p-4 rounded-box overflow-x-auto mb-4"><code>{persistentCode}</code></pre>
                <div class="flex justify-end">
                  <DaisyButton href="/examples/transitions?type=persistent" color="primary">
                    Ver exemplo
                  </DaisyButton>
                </div>
              </DaisyCard>
              
              <div class="border rounded-box p-6 bg-base-200 flex items-center justify-center">
                <div class="demo-box persistent-demo">
                  <h3 class="text-xl font-bold mb-2">Demonstração</h3>
                  <p>Observe o título da página durante a navegação</p>
                </div>
              </div>
            </Grid>
          </PageTransition>
        </div>
      </Tabs>
      
      <Section title="Navegação entre Páginas" class="mt-12">
        <p class="mb-6">
          Experimente navegar entre as páginas abaixo para ver as transições em ação.
          Cada link utiliza um tipo diferente de transição.
        </p>
        
        <div class="flex flex-wrap gap-4 justify-center">
          <DaisyButton href="/" color="primary" variant="outline">
            Página Inicial (Fade)
          </DaisyButton>
          <DaisyButton href="/examples" color="secondary" variant="outline">
            Exemplos (Slide)
          </DaisyButton>
          <DaisyButton href="/examples/components" color="accent" variant="outline">
            Componentes (Scale)
          </DaisyButton>
          <DaisyButton href="/examples/layout" color="info" variant="outline">
            Layout (Flip)
          </DaisyButton>
        </div>
      </Section>
    </Section>
  </Container>
</BaseLayout>

<style>
  .demo-box {
    background-color: white;
    border-radius: 0.5rem;
    padding: 1.5rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    text-align: center;
    width: 100%;
    max-width: 300px;
  }
  
  /* Animações para demonstração */
  .fade-demo {
    animation: demo-fade 3s infinite;
  }
  
  .slide-demo {
    animation: demo-slide 3s infinite;
  }
  
  .scale-demo {
    animation: demo-scale 3s infinite;
  }
  
  .flip-demo {
    animation: demo-flip 3s infinite;
    transform-style: preserve-3d;
    perspective: 1000px;
  }
  
  @keyframes demo-fade {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.3; }
  }
  
  @keyframes demo-slide {
    0%, 100% { transform: translateX(0); }
    50% { transform: translateX(30px); }
  }
  
  @keyframes demo-scale {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(0.9); }
  }
  
  @keyframes demo-flip {
    0%, 100% { transform: rotateY(0deg); }
    50% { transform: rotateY(180deg); }
  }
</style>

<script>
  // Obter tipo de transição da URL
  const urlParams = new URLSearchParams(window.location.search);
  const transitionType = urlParams.get('type');
  
  // Ativar a tab correspondente ao tipo de transição
  if (transitionType) {
    const tabs = document.querySelectorAll('[role="tab"]');
    tabs.forEach(tab => {
      if (tab.id === `tab-${transitionType}`) {
        tab.click();
      }
    });
  }
</script>
