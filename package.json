{"name": "estacao_alfabetizacao", "type": "module", "version": "0.0.1", "scripts": {"dev": "astro dev", "start": "astro dev", "build": "npm run optimize:assets && astro check && astro build", "build:analyze": "npm run build && npm run analyze:bundle", "preview": "astro preview", "astro": "astro", "optimize:assets": "npm run optimize:images && npm run optimize:fonts", "optimize:images": "node scripts/optimize-images-v2.js", "optimize:images:legacy": "node scripts/optimize-images.js", "optimize:fonts": "node scripts/optimize-fonts-v2.js", "optimize:fonts:legacy": "node scripts/optimize-fonts.js", "analyze:bundle": "node scripts/analyze-bundle-v2.js", "analyze:bundle:legacy": "node scripts/analyze-bundle.js", "optimize:bundle": "node scripts/optimize-bundle.js", "minify:assets": "node scripts/minify-assets.js", "lighthouse": "lhci autorun", "lighthouse:desktop": "lighthouse http://localhost:4173 --preset=desktop --output-path=./lighthouse-report.html --view", "lighthouse:mobile": "lighthouse http://localhost:4173 --preset=mobile --output-path=./lighthouse-report.html --view", "test:performance": "npm run build && npm run preview & timeout /t 5 && npm run lighthouse:desktop && taskkill /f /im node.exe /fi \"WINDOWTITLE eq astro preview\"", "test:performance:advanced": "node scripts/performance-tests.js", "test:performance:verify": "node scripts/verify-performance.js", "test:performance:report": "node scripts/verify-performance.js --output=performance-report.html", "test:responsive": "playwright test tests/responsive/responsive.spec.js", "test:accessibility": "playwright test tests/responsive/accessibility.spec.js", "test:responsive:report": "node tests/responsive/generate-report.js", "test:responsive:all": "npm run test:responsive && npm run test:accessibility && npm run test:responsive:report", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage", "test:ui": "vitest --ui", "test:integration": "playwright test --config=tests/integration/playwright.config.ts", "test:integration:ui": "playwright test --config=tests/integration/playwright.config.ts --ui", "test:integration:report": "playwright show-report tests/integration/reports", "test:performance:load": "k6 run tests/performance/scenarios/load-test.js", "test:performance:stress": "k6 run tests/performance/scenarios/stress-test.js", "test:performance:api": "k6 run tests/performance/scenarios/api-benchmark.js", "test:performance:spike": "k6 run tests/performance/scenarios/spike-test.js", "test:performance:soak": "k6 run tests/performance/scenarios/soak-test.js", "test:security": "node tests/security/scripts/security-test-runner.js", "test:security:static": "node tests/security/static-analysis/eslint-security.js", "test:security:pentest": "node tests/security/penetration/owasp-zap-runner.js", "test:security:deps": "node tests/security/dependency-check/dependency-scanner.js", "lint": "eslint . --ext .js,.ts,.astro", "lint:fix": "eslint . --ext .js,.ts,.astro --fix", "format": "prettier --write .", "format:check": "prettier --check .", "code:check": "npm run lint && npm run format:check", "code:fix": "npm run lint:fix && npm run format"}, "dependencies": {"@astrojs/check": "^0.9.4", "@astrojs/node": "^9.2.1", "@astrojs/tailwind": "^6.0.2", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@types/pdfkit": "^0.13.9", "argon2": "^0.41.1", "astro": "^5.7.10", "chart.js": "^4.4.9", "cli@latest": "link:/cli@latest", "kafkajs": "^2.2.4", "nanostores": "^0.11.4", "nodemailer": "^6.10.1", "pdfkit": "^0.17.1", "pg": "^8.15.6", "qrcode": "^1.5.3", "rate-limiter-flexible": "^6.2.1", "redis": "^4.6.13", "sdk-node-apis-efi": "^1.2.20", "typescript": "^5.8.3", "uuid": "^11.1.0", "vitest": "^3.1.2", "winston": "^3.17.0"}, "devDependencies": {"@axe-core/playwright": "^4.8.5", "@biomejs/biome": "1.9.4", "@lhci/cli": "^0.12.0", "@playwright/test": "^1.42.1", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/jest": "^29.5.14", "@types/nodemailer": "^6.4.17", "@types/pg": "^8.11.14", "@typescript-eslint/eslint-plugin": "^7.4.0", "@typescript-eslint/parser": "^7.4.0", "astro-compress": "^2.2.8", "astro-purgecss": "^4.1.0", "autoprefixer": "^10.4.16", "chalk": "^5.3.0", "cli-table3": "^0.6.3", "cssnano": "^6.0.3", "cssnano-preset-advanced": "^7.0.7", "daisyui": "^5.0.35", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-import-resolver-typescript": "^3.6.1", "eslint-plugin-astro": "^0.33.1", "eslint-plugin-deprecation": "^2.0.0", "eslint-plugin-import": "^2.29.1", "glob": "^10.3.10", "jsdom": "^26.1.0", "lighthouse": "^11.0.0", "playwright": "^1.42.1", "prettier": "^3.2.5", "prettier-plugin-astro": "^0.13.0", "pretty-bytes": "^6.1.1", "sharp": "^0.33.2", "tailwindcss": "3.3.5", "terser": "^5.26.0", "tsx": "^4.19.4", "vite": "^6.3.4", "web-vitals": "^3.4.0"}, "pnpm": {"overrides": {"send@<0.19.0": ">=0.19.0", "cookie@<0.7.0": ">=0.7.0"}, "onlyBuiltDependencies": ["@biomejs/biome", "@clerk/shared", "argon2", "esbuild", "sharp"]}}