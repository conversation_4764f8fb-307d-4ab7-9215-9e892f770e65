{"name": "estacao_alfabetizacao", "type": "module", "version": "0.0.1", "scripts": {"dev": "astro dev", "start": "astro dev", "build": "npm run optimize:assets && astro check && astro build", "build:analyze": "npm run build && npm run analyze:bundle", "preview": "astro preview", "astro": "astro", "optimize:assets": "npm run optimize:images && npm run optimize:fonts", "optimize:images": "node scripts/optimize-images-v2.js", "optimize:images:legacy": "node scripts/optimize-images.js", "optimize:fonts": "node scripts/optimize-fonts-v2.js", "optimize:fonts:legacy": "node scripts/optimize-fonts.js", "analyze:bundle": "node scripts/analyze-bundle-v2.js", "analyze:bundle:legacy": "node scripts/analyze-bundle.js", "optimize:bundle": "node scripts/optimize-bundle.js", "minify:assets": "node scripts/minify-assets.js", "lighthouse": "lhci autorun", "lighthouse:desktop": "lighthouse http://localhost:4173 --preset=desktop --output-path=./lighthouse-report.html --view", "lighthouse:mobile": "lighthouse http://localhost:4173 --preset=mobile --output-path=./lighthouse-report.html --view", "test:performance": "npm run build && npm run preview & timeout /t 5 && npm run lighthouse:desktop && taskkill /f /im node.exe /fi \"WINDOWTITLE eq astro preview\"", "test:performance:advanced": "node scripts/performance-tests.js", "test:performance:verify": "node scripts/verify-performance.js", "test:performance:report": "node scripts/verify-performance.js --output=performance-report.html", "test:responsive": "playwright test tests/responsive/responsive.spec.js", "test:accessibility": "playwright test tests/responsive/accessibility.spec.js", "test:responsive:report": "node tests/responsive/generate-report.js", "test:responsive:all": "npm run test:responsive && npm run test:accessibility && npm run test:responsive:report", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage", "test:ui": "vitest --ui", "test:integration": "playwright test --config=tests/integration/playwright.config.ts", "test:integration:ui": "playwright test --config=tests/integration/playwright.config.ts --ui", "test:integration:report": "playwright show-report tests/integration/reports", "test:performance:load": "k6 run tests/performance/scenarios/load-test.js", "test:performance:stress": "k6 run tests/performance/scenarios/stress-test.js", "test:performance:api": "k6 run tests/performance/scenarios/api-benchmark.js", "test:performance:spike": "k6 run tests/performance/scenarios/spike-test.js", "test:performance:soak": "k6 run tests/performance/scenarios/soak-test.js", "test:security": "node tests/security/scripts/security-test-runner.js", "test:security:static": "node tests/security/static-analysis/eslint-security.js", "test:security:pentest": "node tests/security/penetration/owasp-zap-runner.js", "test:security:deps": "node tests/security/dependency-check/dependency-scanner.js", "lint": "biome check --apply-unsafe .", "lint:fix": "biome check --apply-unsafe .", "format": "biome format --write .", "format:check": "biome format --check .", "organize": "biome organize-imports .", "organize:fix": "biome organize-imports --write .", "code:check": "npm run lint && npm run format:check && npm run organize", "code:fix": "npm run lint:fix && npm run format && npm run organize:fix"}, "dependencies": {"@astrojs/check": "^0.9.4", "@astrojs/node": "^9.2.2", "@astrojs/tailwind": "6.0.2", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@types/jsonwebtoken": "^9.0.9", "@types/pdfkit": "^0.13.9", "@valkey/client": "^1.0.0", "animejs": "^4.0.2", "argon2": "^0.41.1", "astro": "^5.8.0", "chart.js": "^4.4.9", "jsonwebtoken": "^9.0.2", "kafkajs": "^2.2.4", "nanoid": "^5.1.5", "nanostores": "^0.11.4", "nodemailer": "^6.10.1", "pdfjs-dist": "^5.2.133", "pdfkit": "^0.17.1", "pg": "^8.16.0", "pino": "^9.7.0", "qrcode": "^1.5.4", "rate-limiter-flexible": "^6.2.1", "sdk-node-apis-efi": "^1.2.20", "typescript": "^5.8.3", "vitest": "^3.1.4", "zod": "^3.25.28"}, "devDependencies": {"@axe-core/playwright": "^4.10.1", "@biomejs/biome": "1.9.4", "@lhci/cli": "^0.14.0", "@playwright/test": "^1.52.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/jest": "^29.5.14", "@types/node": "^22.15.21", "@types/nodemailer": "^6.4.17", "@types/pg": "^8.15.2", "astro-compress": "^2.3.8", "astro-purgecss": "^5.2.2", "autoprefixer": "^10.4.21", "chalk": "^5.4.1", "cli-table3": "^0.6.5", "cssnano": "^6.1.2", "cssnano-preset-advanced": "^7.0.7", "daisyui": "^5.0.37", "glob": "^10.4.5", "jsdom": "^26.1.0", "lighthouse": "^12.6.0", "playwright": "^1.52.0", "pretty-bytes": "^6.1.1", "sharp": "^0.33.5", "tailwindcss": "3.4.0", "terser": "^5.39.2", "tsx": "^4.19.4", "vite": "^6.3.5", "web-vitals": "^3.5.2"}, "pnpm": {"overrides": {"send@<0.19.0": ">=0.19.0", "cookie@<0.7.0": ">=0.7.0", "glob@<10.0.0": ">=10.4.5", "rimraf@<4.0.0": ">=4.4.1", "inflight@<2.0.0": ">=2.0.0", "intl-messageformat-parser@<7.0.0": ">=7.0.0"}, "onlyBuiltDependencies": ["@biomejs/biome", "@clerk/shared", "argon2", "esbuild", "sharp"]}}