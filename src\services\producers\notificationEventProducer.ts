/**
 * Produtor de eventos de notificações
 *
 * Este serviço é responsável por produzir eventos relacionados a notificações
 * para o Kafka.
 */

import { NotificationEvent, eventProducerService } from '@services/eventProducerService';
import { logger } from '@utils/logger';

/**
 * Interface para dados de email
 */
interface EmailData {
  notificationId: string;
  recipientId?: string;
  recipientEmail: string;
  subject: string;
  templateName?: string;
  templateData?: Record<string, unknown>;
  metadata?: Record<string, unknown>;
}

/**
 * Interface para dados de alerta
 */
interface AlertData {
  notificationId: string;
  alertType: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  source: string;
  metadata?: Record<string, unknown>;
}

/**
 * Produtor de eventos de notificação
 */
export const notificationEventProducer = {
  /**
   * Envia evento de email enfileirado
   * @param emailData - Dados do email
   */
  async emailQueued(emailData: EmailData): Promise<void> {
    try {
      const event: NotificationEvent = {
        ...eventProducerService.createBaseEvent(`email-queued-${emailData.notificationId}`, {
          ...(emailData.metadata || {}),
          templateName: emailData.templateName,
          templateData: emailData.templateData,
        }),
        notificationId: emailData.notificationId,
        type: 'email',
        recipientId: emailData.recipientId,
        recipientEmail: emailData.recipientEmail,
        subject: emailData.subject,
        status: 'queued',
      };

      await eventProducerService.sendNotificationEvent('email.queued', event);

      logger.info(`Evento de email enfileirado enviado: ${emailData.notificationId}`);
    } catch (error) {
      logger.error(
        `Erro ao enviar evento de email enfileirado ${emailData.notificationId}:`,
        error
      );
      // Não propagar erro para não interromper o fluxo principal
    }
  },

  /**
   * Envia evento de email enviado
   * @param emailData - Dados do email
   * @param providerResponse - Resposta do provedor de email
   */
  async emailSent(emailData: EmailData, providerResponse?: Record<string, unknown>): Promise<void> {
    try {
      const event: NotificationEvent = {
        ...eventProducerService.createBaseEvent(`email-sent-${emailData.notificationId}`, {
          ...(emailData.metadata || {}),
          providerResponse,
        }),
        notificationId: emailData.notificationId,
        type: 'email',
        recipientId: emailData.recipientId,
        recipientEmail: emailData.recipientEmail,
        subject: emailData.subject,
        status: 'sent',
      };

      await eventProducerService.sendNotificationEvent('email.sent', event);

      logger.info(`Evento de email enviado: ${emailData.notificationId}`);
    } catch (error) {
      logger.error(`Erro ao enviar evento de email enviado ${emailData.notificationId}:`, error);
      // Não propagar erro para não interromper o fluxo principal
    }
  },

  /**
   * Envia evento de falha no envio de email
   * @param emailData - Dados do email
   * @param errorDetails - Detalhes do erro
   */
  async emailFailed(
    emailData: EmailData,
    errorDetails: { code: string; message: string }
  ): Promise<void> {
    try {
      const event: NotificationEvent = {
        ...eventProducerService.createBaseEvent(`email-failed-${emailData.notificationId}`, {
          ...(emailData.metadata || {}),
          error: errorDetails,
        }),
        notificationId: emailData.notificationId,
        type: 'email',
        recipientId: emailData.recipientId,
        recipientEmail: emailData.recipientEmail,
        subject: emailData.subject,
        status: 'failed',
      };

      await eventProducerService.sendNotificationEvent('email.failed', event);

      logger.info(`Evento de falha no envio de email: ${emailData.notificationId}`);
    } catch (error) {
      logger.error(
        `Erro ao enviar evento de falha no envio de email ${emailData.notificationId}:`,
        error
      );
      // Não propagar erro para não interromper o fluxo principal
    }
  },

  /**
   * Envia evento de alerta criado
   * @param alertData - Dados do alerta
   */
  async alertCreated(alertData: AlertData): Promise<void> {
    try {
      const event: NotificationEvent = {
        ...eventProducerService.createBaseEvent(`alert-created-${alertData.notificationId}`, {
          ...(alertData.metadata || {}),
          alertType: alertData.alertType,
          severity: alertData.severity,
          message: alertData.message,
          source: alertData.source,
        }),
        notificationId: alertData.notificationId,
        type: 'alert',
        status: 'created',
      };

      await eventProducerService.sendNotificationEvent('alert.created', event);

      logger.info(`Evento de alerta criado enviado: ${alertData.notificationId}`);
    } catch (error) {
      logger.error(`Erro ao enviar evento de alerta criado ${alertData.notificationId}:`, error);
      // Não propagar erro para não interromper o fluxo principal
    }
  },

  /**
   * Envia evento de alerta resolvido
   * @param alertData - Dados do alerta
   * @param resolution - Detalhes da resolução
   */
  async alertResolved(
    alertData: AlertData,
    resolution: { resolvedBy?: string; comments?: string }
  ): Promise<void> {
    try {
      const event: NotificationEvent = {
        ...eventProducerService.createBaseEvent(`alert-resolved-${alertData.notificationId}`, {
          ...(alertData.metadata || {}),
          alertType: alertData.alertType,
          severity: alertData.severity,
          message: alertData.message,
          source: alertData.source,
          resolution,
        }),
        notificationId: alertData.notificationId,
        type: 'alert',
        status: 'resolved',
      };

      await eventProducerService.sendNotificationEvent('alert.resolved', event);

      logger.info(`Evento de alerta resolvido enviado: ${alertData.notificationId}`);
    } catch (error) {
      logger.error(`Erro ao enviar evento de alerta resolvido ${alertData.notificationId}:`, error);
      // Não propagar erro para não interromper o fluxo principal
    }
  },
};
