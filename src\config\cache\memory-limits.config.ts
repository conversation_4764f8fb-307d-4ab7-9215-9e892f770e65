/**
 * Configuração de limites de memória para cache
 *
 * Este arquivo define os limites de memória e políticas de evicção
 * para o cache Valkey.
 */

/**
 * Tipos de políticas de evicção
 */
export enum EvictionPolicy {
  /**
   * Least Recently Used - Remove os itens menos recentemente acessados
   */
  LRU = 'volatile-lru',

  /**
   * Least Frequently Used - Remove os itens menos frequentemente acessados
   */
  LFU = 'volatile-lfu',

  /**
   * Random - Remove itens aleatoriamente
   */
  RANDOM = 'volatile-random',

  /**
   * Time To Live - Remove os itens mais próximos de expirar
   */
  TTL = 'volatile-ttl',

  /**
   * Least Recently Used (todos os tipos) - Remove os itens menos recentemente acessados, incluindo os sem TTL
   */
  ALLKEYS_LRU = 'allkeys-lru',

  /**
   * Least Frequently Used (todos os tipos) - Remove os itens menos frequentemente acessados, incluindo os sem TTL
   */
  ALLKEYS_LFU = 'allkeys-lfu',

  /**
   * Random (todos os tipos) - Remove itens aleatoriamente, incluindo os sem TTL
   */
  ALLKEYS_RANDOM = 'allkeys-random',

  /**
   * No Eviction - Não remove itens, retorna erro quando a memória está cheia
   */
  NO_EVICTION = 'noeviction',
}

/**
 * Interface para configuração de limites de memória
 */
export interface MemoryLimitsConfig {
  /**
   * Limite máximo de memória em bytes
   */
  maxMemory: string;

  /**
   * Política de evicção
   */
  evictionPolicy: EvictionPolicy;

  /**
   * Limite de aviso de memória (porcentagem)
   */
  warningThreshold: number;

  /**
   * Limite crítico de memória (porcentagem)
   */
  criticalThreshold: number;

  /**
   * Descrição da configuração
   */
  description: string;
}

/**
 * Configuração de limites de memória para diferentes ambientes
 */
export const memoryLimits: Record<string, MemoryLimitsConfig> = {
  // Ambiente de desenvolvimento
  development: {
    maxMemory: '100mb',
    evictionPolicy: EvictionPolicy.ALLKEYS_LRU,
    warningThreshold: 80,
    criticalThreshold: 95,
    description: 'Configuração para ambiente de desenvolvimento',
  },

  // Ambiente de teste
  test: {
    maxMemory: '200mb',
    evictionPolicy: EvictionPolicy.ALLKEYS_LRU,
    warningThreshold: 80,
    criticalThreshold: 95,
    description: 'Configuração para ambiente de teste',
  },

  // Ambiente de produção
  production: {
    maxMemory: '1gb',
    evictionPolicy: EvictionPolicy.LFU,
    warningThreshold: 75,
    criticalThreshold: 90,
    description: 'Configuração para ambiente de produção',
  },

  // Ambiente de produção com alta carga
  'production-high-load': {
    maxMemory: '4gb',
    evictionPolicy: EvictionPolicy.LFU,
    warningThreshold: 70,
    criticalThreshold: 85,
    description: 'Configuração para ambiente de produção com alta carga',
  },
};

/**
 * Obtém a configuração de limites de memória para o ambiente atual
 * @returns Configuração de limites de memória
 */
export function getMemoryLimits(): MemoryLimitsConfig {
  const env = process.env.NODE_ENV || 'development';
  const configKey = process.env.CACHE_MEMORY_PROFILE || env;

  return memoryLimits[configKey] || memoryLimits['development'];
}

/**
 * Converte string de tamanho para bytes
 * @param sizeStr String de tamanho (ex: '100mb', '1gb')
 * @returns Tamanho em bytes
 */
export function parseMemorySize(sizeStr: string): number {
  const units: Record<string, number> = {
    b: 1,
    kb: 1024,
    mb: 1024 * 1024,
    gb: 1024 * 1024 * 1024,
  };

  const match = sizeStr.toLowerCase().match(/^(\d+)([a-z]+)$/);
  if (!match) {
    return Number.parseInt(sizeStr, 10);
  }

  const size = Number.parseInt(match[1], 10);
  const unit = match[2];

  return size * (units[unit] || 1);
}

/**
 * Gera a configuração para o arquivo valkey.conf
 * @returns Linhas de configuração para valkey.conf
 */
export function generateValkeyConfig(): string[] {
  const config = getMemoryLimits();
  const maxMemoryBytes = parseMemorySize(config.maxMemory);

  return [
    `# Configuração de limites de memória`,
    `# Gerado automaticamente em ${new Date().toISOString()}`,
    ``,
    `# Limite máximo de memória`,
    `maxmemory ${maxMemoryBytes}`,
    ``,
    `# Política de evicção`,
    `maxmemory-policy ${config.evictionPolicy}`,
    ``,
    `# Amostragem para LRU/LFU`,
    `maxmemory-samples 10`,
    ``,
    `# Configurações adicionais`,
    `# Desabilitar comandos perigosos em produção`,
    `${process.env.NODE_ENV === 'production' ? 'rename-command FLUSHALL ""' : '# rename-command FLUSHALL ""'}`,
    `${process.env.NODE_ENV === 'production' ? 'rename-command FLUSHDB ""' : '# rename-command FLUSHDB ""'}`,
    `${process.env.NODE_ENV === 'production' ? 'rename-command DEBUG ""' : '# rename-command DEBUG ""'}`,
  ];
}
