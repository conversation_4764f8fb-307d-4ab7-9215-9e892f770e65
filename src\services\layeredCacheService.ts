/**
 * Serviço de Cache em Camadas
 *
 * Este serviço implementa um sistema de cache em múltiplas camadas:
 * 1. Cache em memória (para itens frequentemente acessados)
 * 2. Cache distribuído (Valkey/Redis)
 *
 * Isso melhora a performance reduzindo a latência para itens frequentemente acessados
 * e diminui a carga no serviço de cache distribuído.
 */

import { promisify } from 'node:util';
import { gunzip, gzip } from 'node:zlib';
import { cacheService } from '@services/cacheService';
import { logger } from '@utils/logger';
import LRUCache from 'lru-cache';

// Promisify zlib functions
const gzipAsync = promisify(gzip);
const gunzipAsync = promisify(gunzip);

/**
 * Opções para operações de cache
 */
export interface CacheOptions {
  /**
   * TTL em segundos para cache em memória
   */
  memoryTTL?: number;

  /**
   * TTL em segundos para cache distribuído
   */
  distributedTTL?: number;

  /**
   * Se deve usar apenas cache em memória
   */
  memoryOnly?: boolean;

  /**
   * Se deve usar apenas cache distribuído
   */
  distributedOnly?: boolean;

  /**
   * Se deve comprimir dados
   */
  compress?: boolean;

  /**
   * Tamanho mínimo em bytes para compressão
   */
  compressionThreshold?: number;

  /**
   * Tags para categorizar o item em cache
   */
  tags?: string[];
}

/**
 * Configuração do cache em camadas
 */
export const layeredCacheConfig = {
  // Configuração do cache em memória
  memory: {
    maxSize: Number.parseInt(process.env.MEMORY_CACHE_MAX_SIZE || '1000', 10), // Número máximo de itens
    maxSizeInBytes: Number.parseInt(process.env.MEMORY_CACHE_MAX_SIZE_BYTES || '104857600', 10), // 100MB
    ttl: Number.parseInt(process.env.MEMORY_CACHE_TTL || '300', 10), // 5 minutos
    updateAgeOnGet: true, // Atualizar idade do item ao acessá-lo
  },

  // Configuração de compressão
  compression: {
    enabled: process.env.CACHE_COMPRESSION === 'true',
    threshold: Number.parseInt(process.env.CACHE_COMPRESSION_THRESHOLD || '1024', 10), // 1KB
    level: Number.parseInt(process.env.CACHE_COMPRESSION_LEVEL || '6', 10), // 0-9, onde 9 é máxima compressão
  },

  // Configuração de métricas
  metrics: {
    enabled: process.env.CACHE_METRICS_ENABLED === 'true',
    sampleRate: Number.parseFloat(process.env.CACHE_METRICS_SAMPLE_RATE || '0.1'), // Taxa de amostragem (10%)
  },
};

// Criar cache em memória usando LRU
const memoryCache = new LRUCache<string, any>({
  max: layeredCacheConfig.memory.maxSize,
  maxSize: layeredCacheConfig.memory.maxSizeInBytes,
  sizeCalculation: (value, key) => {
    // Calcular tamanho aproximado em bytes
    if (typeof value === 'string') {
      return value.length * 2; // Aproximação para strings
    }
    if (value && typeof value === 'object') {
      return JSON.stringify(value).length * 2; // Aproximação para objetos
    }
    return 100; // Tamanho padrão para outros tipos
  },
  ttl: layeredCacheConfig.memory.ttl * 1000, // Converter para milissegundos
  updateAgeOnGet: layeredCacheConfig.memory.updateAgeOnGet,
});

// Contador para métricas
const metrics = {
  hits: {
    memory: 0,
    distributed: 0,
  },
  misses: {
    memory: 0,
    distributed: 0,
  },
  sets: {
    memory: 0,
    distributed: 0,
  },
  errors: {
    memory: 0,
    distributed: 0,
  },
};

// Registrar métrica
function recordMetric(
  type: 'hits' | 'misses' | 'sets' | 'errors',
  layer: 'memory' | 'distributed'
): void {
  if (layeredCacheConfig.metrics.enabled) {
    // Incrementar contador apenas para uma amostra de requisições
    if (Math.random() < layeredCacheConfig.metrics.sampleRate) {
      metrics[type][layer]++;
    }
  }
}

/**
 * Serviço de cache em camadas
 */
export const layeredCacheService = {
  /**
   * Obtém um item do cache
   * @param key Chave do cache
   * @param options Opções de cache
   * @returns Valor armazenado ou null se não encontrado
   */
  async get<T>(key: string, options: CacheOptions = {}): Promise<T | null> {
    try {
      // Verificar cache em memória (a menos que distributedOnly seja true)
      if (!options.distributedOnly) {
        const memoryValue = memoryCache.get(key) as T;

        if (memoryValue !== undefined) {
          recordMetric('hits', 'memory');
          return memoryValue;
        }

        recordMetric('misses', 'memory');
      }

      // Verificar cache distribuído (a menos que memoryOnly seja true)
      if (!options.memoryOnly) {
        // Obter do cache distribuído
        const distributedValue = await cacheService.get<T | string>(key);

        if (distributedValue !== null) {
          recordMetric('hits', 'distributed');

          // Verificar se o valor está comprimido
          if (typeof distributedValue === 'string' && distributedValue.startsWith('COMPRESSED:')) {
            try {
              // Descomprimir valor
              const compressedData = Buffer.from(distributedValue.substring(11), 'base64');
              const decompressedData = await gunzipAsync(compressedData);
              const jsonData = decompressedData.toString('utf-8');
              const value = JSON.parse(jsonData) as T;

              // Armazenar em cache de memória para acesso futuro
              if (!options.distributedOnly) {
                memoryCache.set(
                  key,
                  value,
                  options.memoryTTL ? options.memoryTTL * 1000 : undefined
                );
                recordMetric('sets', 'memory');
              }

              return value;
            } catch (error) {
              logger.error(`Erro ao descomprimir valor para chave ${key}:`, error);
              recordMetric('errors', 'distributed');
              return null;
            }
          }

          // Armazenar em cache de memória para acesso futuro
          if (!options.distributedOnly) {
            memoryCache.set(
              key,
              distributedValue,
              options.memoryTTL ? options.memoryTTL * 1000 : undefined
            );
            recordMetric('sets', 'memory');
          }

          return distributedValue as T;
        }

        recordMetric('misses', 'distributed');
      }

      return null;
    } catch (error) {
      logger.error(`Erro ao obter valor do cache para chave ${key}:`, error);
      return null;
    }
  },

  /**
   * Define um item no cache
   * @param key Chave do cache
   * @param value Valor a ser armazenado
   * @param ttl Tempo de vida em segundos (opcional)
   * @param options Opções de cache
   * @returns Sucesso da operação
   */
  async set<T>(key: string, value: T, ttl?: number, options: CacheOptions = {}): Promise<boolean> {
    try {
      // Definir tempos de vida
      const memoryTTL = options.memoryTTL || ttl || layeredCacheConfig.memory.ttl;
      const distributedTTL = options.distributedTTL || ttl;

      // Armazenar no cache em memória (a menos que distributedOnly seja true)
      if (!options.distributedOnly) {
        memoryCache.set(key, value, memoryTTL * 1000); // Converter para milissegundos
        recordMetric('sets', 'memory');
      }

      // Armazenar no cache distribuído (a menos que memoryOnly seja true)
      if (!options.memoryOnly) {
        // Verificar se deve comprimir
        const shouldCompress =
          options.compress !== false &&
          (options.compress === true || layeredCacheConfig.compression.enabled);

        if (shouldCompress && typeof value === 'object') {
          const jsonData = JSON.stringify(value);

          // Comprimir apenas se o tamanho for maior que o limite
          const threshold =
            options.compressionThreshold || layeredCacheConfig.compression.threshold;

          if (jsonData.length > threshold) {
            try {
              // Comprimir dados
              const compressedData = await gzipAsync(Buffer.from(jsonData, 'utf-8'));
              const base64Data = compressedData.toString('base64');

              // Armazenar com prefixo para identificar dados comprimidos
              await cacheService.set(`${key}`, `COMPRESSED:${base64Data}`, distributedTTL);
              recordMetric('sets', 'distributed');

              return true;
            } catch (error) {
              logger.error(`Erro ao comprimir valor para chave ${key}:`, error);
              recordMetric('errors', 'distributed');

              // Tentar armazenar sem compressão
              await cacheService.set(key, value, distributedTTL);
              return true;
            }
          }
        }

        // Armazenar sem compressão
        await cacheService.set(key, value, distributedTTL);
        recordMetric('sets', 'distributed');
      }

      // Armazenar tags se fornecidas
      if (options.tags && options.tags.length > 0 && !options.memoryOnly) {
        await this.addTagsToKey(key, options.tags);
      }

      return true;
    } catch (error) {
      logger.error(`Erro ao definir valor no cache para chave ${key}:`, error);
      return false;
    }
  },

  /**
   * Remove um item do cache
   * @param key Chave do cache
   * @returns Sucesso da operação
   */
  async del(key: string): Promise<boolean> {
    try {
      // Remover do cache em memória
      memoryCache.delete(key);

      // Remover do cache distribuído
      await cacheService.del(key);

      // Remover referências de tags
      await this.removeKeyFromTags(key);

      return true;
    } catch (error) {
      logger.error(`Erro ao remover valor do cache para chave ${key}:`, error);
      return false;
    }
  },

  /**
   * Adiciona tags a uma chave
   * @param key Chave do cache
   * @param tags Tags a serem adicionadas
   */
  async addTagsToKey(key: string, tags: string[]): Promise<void> {
    try {
      // Para cada tag, adicionar a chave ao conjunto de chaves com essa tag
      for (const tag of tags) {
        const tagKey = `tag:${tag}`;
        await cacheService.sadd(tagKey, key);
      }

      // Armazenar tags associadas à chave
      const keyTagsKey = `key-tags:${key}`;
      await cacheService.sadd(keyTagsKey, ...tags);
    } catch (error) {
      logger.error(`Erro ao adicionar tags para chave ${key}:`, error);
    }
  },

  /**
   * Remove uma chave de todas as suas tags
   * @param key Chave do cache
   */
  async removeKeyFromTags(key: string): Promise<void> {
    try {
      // Obter tags associadas à chave
      const keyTagsKey = `key-tags:${key}`;
      const tags = await cacheService.smembers(keyTagsKey);

      // Para cada tag, remover a chave do conjunto
      for (const tag of tags) {
        const tagKey = `tag:${tag}`;
        await cacheService.srem(tagKey, key);
      }

      // Remover registro de tags da chave
      await cacheService.del(keyTagsKey);
    } catch (error) {
      logger.error(`Erro ao remover chave ${key} das tags:`, error);
    }
  },

  /**
   * Invalida todas as chaves com uma determinada tag
   * @param tag Tag para invalidação
   * @returns Número de chaves invalidadas
   */
  async invalidateByTag(tag: string): Promise<number> {
    try {
      const tagKey = `tag:${tag}`;

      // Obter todas as chaves com essa tag
      const keys = await cacheService.smembers(tagKey);

      if (keys.length === 0) {
        return 0;
      }

      // Remover cada chave do cache
      for (const key of keys) {
        // Remover do cache em memória
        memoryCache.delete(key);

        // Remover do cache distribuído
        await cacheService.del(key);

        // Remover referências de tags
        await this.removeKeyFromTags(key);
      }

      // Limpar a tag
      await cacheService.del(tagKey);

      return keys.length;
    } catch (error) {
      logger.error(`Erro ao invalidar cache por tag ${tag}:`, error);
      return 0;
    }
  },

  /**
   * Invalida todas as chaves que correspondem a um padrão
   * @param pattern Padrão de chave (ex: "user:*")
   * @returns Número de chaves invalidadas
   */
  async invalidateByPattern(pattern: string): Promise<number> {
    try {
      // Buscar chaves que correspondem ao padrão
      const keys = await cacheService.keys(pattern);

      if (keys.length === 0) {
        return 0;
      }

      // Remover cada chave do cache
      for (const key of keys) {
        // Remover do cache em memória
        memoryCache.delete(key);

        // Remover do cache distribuído
        await cacheService.del(key);

        // Remover referências de tags
        await this.removeKeyFromTags(key);
      }

      return keys.length;
    } catch (error) {
      logger.error(`Erro ao invalidar cache por padrão ${pattern}:`, error);
      return 0;
    }
  },

  /**
   * Verifica se uma chave existe no cache
   * @param key Chave do cache
   * @returns Se a chave existe
   */
  async exists(key: string): Promise<boolean> {
    try {
      // Verificar cache em memória
      if (memoryCache.has(key)) {
        return true;
      }

      // Verificar cache distribuído
      return (await cacheService.exists(key)) > 0;
    } catch (error) {
      logger.error(`Erro ao verificar existência de chave ${key}:`, error);
      return false;
    }
  },

  /**
   * Define o tempo de expiração de uma chave
   * @param key Chave do cache
   * @param ttl Tempo de vida em segundos
   * @returns Sucesso da operação
   */
  async expire(key: string, ttl: number): Promise<boolean> {
    try {
      // Definir expiração no cache em memória
      const value = memoryCache.get(key);
      if (value !== undefined) {
        memoryCache.set(key, value, ttl * 1000);
      }

      // Definir expiração no cache distribuído
      return await cacheService.expire(key, ttl);
    } catch (error) {
      logger.error(`Erro ao definir expiração para chave ${key}:`, error);
      return false;
    }
  },

  /**
   * Obtém estatísticas do cache
   * @returns Estatísticas do cache
   */
  getStats(): any {
    return {
      memory: {
        size: memoryCache.size,
        maxSize: layeredCacheConfig.memory.maxSize,
        itemCount: memoryCache.size,
        ...memoryCache.stats,
      },
      metrics: {
        ...metrics,
        hitRate: {
          memory: metrics.hits.memory / (metrics.hits.memory + metrics.misses.memory || 1),
          distributed:
            metrics.hits.distributed / (metrics.hits.distributed + metrics.misses.distributed || 1),
          overall:
            (metrics.hits.memory + metrics.hits.distributed) /
            (metrics.hits.memory +
              metrics.hits.distributed +
              metrics.misses.memory +
              metrics.misses.distributed || 1),
        },
      },
    };
  },

  /**
   * Limpa todo o cache em memória
   */
  clearMemoryCache(): void {
    memoryCache.clear();
    logger.info('Cache em memória limpo');
  },

  /**
   * Limpa todo o cache distribuído com um determinado prefixo
   * @param prefix Prefixo das chaves a serem limpas
   * @returns Número de chaves removidas
   */
  async clearDistributedCache(prefix = '*'): Promise<number> {
    try {
      const pattern = prefix === '*' ? '*' : `${prefix}*`;
      const keys = await cacheService.keys(pattern);

      if (keys.length === 0) {
        return 0;
      }

      // Remover chaves do cache distribuído
      const result = await cacheService.del(...keys);

      logger.info(`Cache distribuído limpo: ${result} chaves removidas`);

      return result;
    } catch (error) {
      logger.error('Erro ao limpar cache distribuído:', error);
      return 0;
    }
  },
};
