---
/**
 * Página de administração de permissões
 *
 * Esta página permite visualizar e gerenciar permissões no sistema.
 */

import PermissionGate from '@components/auth/PermissionGate.astro';
import RoleGate from '@components/auth/RoleGate.astro';
// Importações
import MainLayout from '@layouts/MainLayout.astro';
import { permissionRepository } from '@repository/permissionRepository';
import { resourceRepository } from '@repository/resourceRepository';
import { roleRepository } from '@repository/roleRepository';
import { policyEnforcementService } from '@services/policyEnforcementService';
import { getCurrentUser } from '@utils/authUtils';

// Verificar autenticação
const user = await getCurrentUser(Astro.cookies);

// Redirecionar se não estiver autenticado
if (!user) {
  return Astro.redirect('/signin?redirect=/admin/permissions');
}

// Buscar dados
const roles = await roleRepository.read();
const resources = await resourceRepository.read();
const resourcesWithPermissions = await resourceRepository.getResourcesWithPermissions();

// Agrupar permissões por recurso
const permissionsByResource = resourcesWithPermissions.rows.reduce(
  (acc, row) => {
    const resourceId = row.ulid_resource;
    const resourceName = row.name;

    if (!acc[resourceId]) {
      acc[resourceId] = {
        id: resourceId,
        name: resourceName,
        description: row.description,
        permissions: [],
      };
    }

    acc[resourceId].permissions.push({
      id: row.ulid_permission,
      resourcePermissionId: row.ulid_resource_permission,
      name: row.permission_name,
      action: row.action,
    });

    return acc;
  },
  {} as Record<string, any>
);

// Obter políticas de acesso
const policies = policyEnforcementService.getPolicies();

// Título da página
const title = 'Gerenciamento de Permissões';
---

<MainLayout title={title}>
  <main class="container mx-auto px-4 py-8">
    <PermissionGate resource="roles" action="read">
      <h1 class="text-3xl font-bold mb-6">{title}</h1>
      
      <div class="grid grid-cols-1 md:grid-cols-12 gap-6">
        <!-- Sidebar com papéis -->
        <div class="md:col-span-3">
          <div class="bg-white rounded-lg shadow p-4">
            <h2 class="text-xl font-semibold mb-4">Papéis</h2>
            
            <ul class="space-y-2">
              {roles.rows.map((role) => (
                <li>
                  <a 
                    href={`/admin/roles/${role.ulid_role}`}
                    class="block p-2 hover:bg-gray-100 rounded transition"
                  >
                    {role.name}
                    {role.is_system && (
                      <span class="ml-2 text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">Sistema</span>
                    )}
                  </a>
                </li>
              ))}
            </ul>
            
            <PermissionGate resource="roles" action="create">
              <div class="mt-4">
                <a 
                  href="/admin/roles/new" 
                  class="inline-block bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition"
                >
                  Novo Papel
                </a>
              </div>
            </PermissionGate>
          </div>
        </div>
        
        <!-- Conteúdo principal -->
        <div class="md:col-span-9">
          <div class="bg-white rounded-lg shadow p-6">
            <h2 class="text-xl font-semibold mb-4">Recursos e Permissões</h2>
            
            <div class="space-y-6">
              {Object.values(permissionsByResource).map((resource: any) => (
                <div class="border rounded-lg p-4">
                  <h3 class="text-lg font-medium mb-2">{resource.name}</h3>
                  <p class="text-gray-600 mb-4">{resource.description}</p>
                  
                  <h4 class="font-medium mb-2">Permissões:</h4>
                  <ul class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                    {resource.permissions.map((permission: any) => (
                      <li class="bg-gray-50 p-2 rounded">
                        <span class="font-medium">{permission.name}</span>
                        <span class="ml-2 text-xs bg-green-100 text-green-800 px-2 py-1 rounded">{permission.action}</span>
                      </li>
                    ))}
                  </ul>
                  
                  <PermissionGate resource="permissions" action="update">
                    <div class="mt-4">
                      <a 
                        href={`/admin/resources/${resource.id}`}
                        class="text-blue-600 hover:underline"
                      >
                        Gerenciar permissões
                      </a>
                    </div>
                  </PermissionGate>
                </div>
              ))}
            </div>
          </div>
          
          <div class="mt-6 bg-white rounded-lg shadow p-6">
            <h2 class="text-xl font-semibold mb-4">Políticas de Acesso</h2>
            
            <div class="space-y-6">
              {policies.map((policy) => (
                <div class="border rounded-lg p-4">
                  <h3 class="text-lg font-medium mb-2">{policy.resource}</h3>
                  <p class="text-gray-600 mb-4">{policy.description}</p>
                  
                  <h4 class="font-medium mb-2">Ações:</h4>
                  <ul class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                    {policy.actions.map((action) => (
                      <li class="bg-gray-50 p-2 rounded">
                        <span class="font-medium">{action.name}</span>
                        <p class="text-sm text-gray-600">{action.description}</p>
                        <div class="mt-1">
                          <span class="text-xs text-gray-500">Papéis padrão:</span>
                          <div class="flex flex-wrap gap-1 mt-1">
                            {action.defaultRoles.map((role) => (
                              <span class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">{role}</span>
                            ))}
                          </div>
                        </div>
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
      
      <slot name="fallback">
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mt-6">
          <p>Você não tem permissão para acessar esta página.</p>
          <p class="mt-2">
            <a href="/" class="text-red-700 font-medium hover:underline">Voltar para a página inicial</a>
          </p>
        </div>
      </slot>
    </PermissionGate>
  </main>
</MainLayout>

<script>
  // Script para interatividade no cliente
  document.addEventListener('DOMContentLoaded', () => {
    // Implementar funcionalidades interativas aqui
  });
</script>

<style>
  /* Estilos específicos da página */
</style>
