/**
 * PostgreSQL Document Repository
 *
 * Implementação do repositório de documentos usando PostgreSQL.
 * Parte da implementação da tarefa 8.3.1 - Armazenamento de PDFs
 */

import type { Pool } from 'pg';
import {
  Document,
  type DocumentMetadata,
  type DocumentVersion,
} from '../../../domain/entities/Document';
import type {
  DocumentRepository,
  DocumentFilter,
  DocumentSortOptions,
  DocumentPaginationOptions,
  PaginatedDocuments,
} from '../../../domain/repositories/DocumentRepository';

export class PostgresDocumentRepository implements DocumentRepository {
  constructor(private pool: Pool) {}

  async create(document: Document): Promise<Document> {
    const client = await this.pool.connect();

    try {
      await client.query('BEGIN');

      // Inserir documento
      const documentResult = await client.query(
        `INSERT INTO documents (
          id, title, description, owner_id, is_public,
          current_version, created_at, updated_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
        RETURNING *`,
        [
          document.id,
          document.title,
          document.description,
          document.ownerId,
          document.isPublic,
          document.currentVersion,
          document.createdAt,
          document.updatedAt,
        ]
      );

      // Inserir metadados
      await client.query(
        `INSERT INTO document_metadata (
          document_id, title, description, author, keywords,
          created_at, updated_at, file_size, page_count,
          language, category, tags, version, content_type, checksum
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15)`,
        [
          document.id,
          document.metadata.title,
          document.metadata.description,
          document.metadata.author,
          document.metadata.keywords
            ? JSON.stringify(document.metadata.keywords)
            : null,
          document.metadata.createdAt,
          document.metadata.updatedAt,
          document.metadata.fileSize,
          document.metadata.pageCount,
          document.metadata.language,
          document.metadata.category,
          document.metadata.tags
            ? JSON.stringify(document.metadata.tags)
            : null,
          document.metadata.version,
          document.metadata.contentType,
          document.metadata.checksum,
        ]
      );

      // Inserir versões
      for (const version of document.versions) {
        await client.query(
          `INSERT INTO document_versions (
            id, document_id, version, content, created_at,
            created_by, change_notes
          ) VALUES ($1, $2, $3, $4, $5, $6, $7)`,
          [
            version.id,
            version.documentId,
            version.version,
            version.content,
            version.createdAt,
            version.createdBy,
            version.changeNotes,
          ]
        );
      }

      await client.query('COMMIT');

      return document;
    } catch (error) {
      await client.query('ROLLBACK');
      console.error('Erro ao criar documento:', error);
      throw error;
    } finally {
      client.release();
    }
  }

  async update(document: Document): Promise<Document> {
    const client = await this.pool.connect();

    try {
      await client.query('BEGIN');

      // Atualizar documento
      await client.query(
        `UPDATE documents SET
          title = $1,
          description = $2,
          is_public = $3,
          current_version = $4,
          updated_at = $5,
          deleted_at = $6
        WHERE id = $7`,
        [
          document.title,
          document.description,
          document.isPublic,
          document.currentVersion,
          document.updatedAt,
          document.deletedAt,
          document.id,
        ]
      );

      // Atualizar metadados
      await client.query(
        `UPDATE document_metadata SET
          title = $1,
          description = $2,
          author = $3,
          keywords = $4,
          updated_at = $5,
          file_size = $6,
          page_count = $7,
          language = $8,
          category = $9,
          tags = $10,
          version = $11,
          content_type = $12,
          checksum = $13
        WHERE document_id = $14 AND version = $15`,
        [
          document.metadata.title,
          document.metadata.description,
          document.metadata.author,
          document.metadata.keywords
            ? JSON.stringify(document.metadata.keywords)
            : null,
          document.metadata.updatedAt,
          document.metadata.fileSize,
          document.metadata.pageCount,
          document.metadata.language,
          document.metadata.category,
          document.metadata.tags
            ? JSON.stringify(document.metadata.tags)
            : null,
          document.metadata.version,
          document.metadata.contentType,
          document.metadata.checksum,
          document.id,
          document.currentVersion,
        ]
      );

      await client.query('COMMIT');

      return document;
    } catch (error) {
      await client.query('ROLLBACK');
      console.error('Erro ao atualizar documento:', error);
      throw error;
    } finally {
      client.release();
    }
  }

  async addVersion(
    documentId: string,
    version: DocumentVersion
  ): Promise<Document> {
    const client = await this.pool.connect();

    try {
      await client.query('BEGIN');

      // Inserir nova versão
      await client.query(
        `INSERT INTO document_versions (
          id, document_id, version, content, created_at,
          created_by, change_notes
        ) VALUES ($1, $2, $3, $4, $5, $6, $7)`,
        [
          version.id,
          version.documentId,
          version.version,
          version.content,
          version.createdAt,
          version.createdBy,
          version.changeNotes,
        ]
      );

      // Inserir metadados da versão
      await client.query(
        `INSERT INTO document_metadata (
          document_id, title, description, author, keywords,
          created_at, updated_at, file_size, page_count,
          language, category, tags, version, content_type, checksum
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15)`,
        [
          version.documentId,
          version.metadata.title,
          version.metadata.description,
          version.metadata.author,
          version.metadata.keywords
            ? JSON.stringify(version.metadata.keywords)
            : null,
          version.metadata.createdAt,
          version.metadata.updatedAt,
          version.metadata.fileSize,
          version.metadata.pageCount,
          version.metadata.language,
          version.metadata.category,
          version.metadata.tags ? JSON.stringify(version.metadata.tags) : null,
          version.metadata.version,
          version.metadata.contentType,
          version.metadata.checksum,
        ]
      );

      // Atualizar versão atual do documento
      await client.query(
        `UPDATE documents SET
          current_version = $1,
          updated_at = $2
        WHERE id = $3`,
        [version.version, new Date(), documentId]
      );

      await client.query('COMMIT');

      // Buscar o documento atualizado
      return (await this.getById(documentId, true)) as Document;
    } catch (error) {
      await client.query('ROLLBACK');
      console.error('Erro ao adicionar versão ao documento:', error);
      throw error;
    } finally {
      client.release();
    }
  }

  async getById(id: string, includeVersions = false): Promise<Document | null> {
    const client = await this.pool.connect();

    try {
      // Buscar documento
      const documentResult = await client.query(
        `SELECT * FROM documents WHERE id = $1`,
        [id]
      );

      if (documentResult.rows.length === 0) {
        return null;
      }

      const documentData = documentResult.rows[0];

      // Buscar metadados da versão atual
      const metadataResult = await client.query(
        `SELECT * FROM document_metadata
        WHERE document_id = $1 AND version = $2`,
        [id, documentData.current_version]
      );

      if (metadataResult.rows.length === 0) {
        throw new Error(`Metadados não encontrados para o documento ${id}`);
      }

      const metadataData = metadataResult.rows[0];

      // Construir objeto de metadados
      const metadata: DocumentMetadata = {
        title: metadataData.title,
        description: metadataData.description,
        author: metadataData.author,
        keywords: metadataData.keywords
          ? JSON.parse(metadataData.keywords)
          : undefined,
        createdAt: new Date(metadataData.created_at),
        updatedAt: new Date(metadataData.updated_at),
        fileSize: metadataData.file_size,
        pageCount: metadataData.page_count,
        language: metadataData.language,
        category: metadataData.category,
        tags: metadataData.tags ? JSON.parse(metadataData.tags) : undefined,
        version: metadataData.version,
        contentType: metadataData.content_type,
        checksum: metadataData.checksum,
      };

      // Buscar versões se solicitado
      let versions: DocumentVersion[] = [];

      if (includeVersions) {
        const versionsResult = await client.query(
          `SELECT v.*, m.*
          FROM document_versions v
          JOIN document_metadata m ON v.document_id = m.document_id AND v.version = m.version
          WHERE v.document_id = $1
          ORDER BY v.version DESC`,
          [id]
        );

        versions = versionsResult.rows.map(row => ({
          id: row.id,
          documentId: row.document_id,
          version: row.version,
          content: row.content,
          metadata: {
            title: row.title,
            description: row.description,
            author: row.author,
            keywords: row.keywords ? JSON.parse(row.keywords) : undefined,
            createdAt: new Date(row.created_at),
            updatedAt: new Date(row.updated_at),
            fileSize: row.file_size,
            pageCount: row.page_count,
            language: row.language,
            category: row.category,
            tags: row.tags ? JSON.parse(row.tags) : undefined,
            version: row.version,
            contentType: row.content_type,
            checksum: row.checksum,
          },
          createdAt: new Date(row.created_at),
          createdBy: row.created_by,
          changeNotes: row.change_notes,
        }));
      }

      // Construir e retornar documento
      return new Document({
        id: documentData.id,
        title: documentData.title,
        description: documentData.description,
        ownerId: documentData.owner_id,
        isPublic: documentData.is_public,
        currentVersion: documentData.current_version,
        metadata,
        versions,
        createdAt: new Date(documentData.created_at),
        updatedAt: new Date(documentData.updated_at),
        deletedAt: documentData.deleted_at
          ? new Date(documentData.deleted_at)
          : undefined,
      });
    } catch (error) {
      console.error('Erro ao buscar documento por ID:', error);
      throw error;
    } finally {
      client.release();
    }
  }

  async getVersion(
    documentId: string,
    versionNumber: number
  ): Promise<DocumentVersion | null> {
    const client = await this.pool.connect();

    try {
      // Buscar versão específica
      const versionResult = await client.query(
        `SELECT v.*, m.*
        FROM document_versions v
        JOIN document_metadata m ON v.document_id = m.document_id AND v.version = m.version
        WHERE v.document_id = $1 AND v.version = $2`,
        [documentId, versionNumber]
      );

      if (versionResult.rows.length === 0) {
        return null;
      }

      const row = versionResult.rows[0];

      // Construir e retornar versão
      return {
        id: row.id,
        documentId: row.document_id,
        version: row.version,
        content: row.content,
        metadata: {
          title: row.title,
          description: row.description,
          author: row.author,
          keywords: row.keywords ? JSON.parse(row.keywords) : undefined,
          createdAt: new Date(row.created_at),
          updatedAt: new Date(row.updated_at),
          fileSize: row.file_size,
          pageCount: row.page_count,
          language: row.language,
          category: row.category,
          tags: row.tags ? JSON.parse(row.tags) : undefined,
          version: row.version,
          contentType: row.content_type,
          checksum: row.checksum,
        },
        createdAt: new Date(row.created_at),
        createdBy: row.created_by,
        changeNotes: row.change_notes,
      };
    } catch (error) {
      console.error('Erro ao buscar versão do documento:', error);
      throw error;
    } finally {
      client.release();
    }
  }

  async find(
    filter: DocumentFilter,
    sort?: DocumentSortOptions,
    pagination?: DocumentPaginationOptions
  ): Promise<PaginatedDocuments> {
    const client = await this.pool.connect();

    try {
      // Construir cláusula WHERE
      const conditions: string[] = [];
      const params: any[] = [];
      let paramIndex = 1;

      if (filter.ownerId) {
        conditions.push(`d.owner_id = $${paramIndex++}`);
        params.push(filter.ownerId);
      }

      if (filter.title) {
        conditions.push(`d.title ILIKE $${paramIndex++}`);
        params.push(`%${filter.title}%`);
      }

      if (filter.isPublic !== undefined) {
        conditions.push(`d.is_public = $${paramIndex++}`);
        params.push(filter.isPublic);
      }

      if (filter.category) {
        conditions.push(`m.category = $${paramIndex++}`);
        params.push(filter.category);
      }

      if (filter.tags && filter.tags.length > 0) {
        // Buscar documentos que contenham qualquer uma das tags
        const tagConditions = filter.tags.map(
          (_, i) => `m.tags @> $${paramIndex + i}::jsonb`
        );
        conditions.push(`(${tagConditions.join(' OR ')})`);
        filter.tags.forEach(tag => {
          params.push(JSON.stringify([tag]));
          paramIndex++;
        });
      }

      if (filter.createdAfter) {
        conditions.push(`d.created_at >= $${paramIndex++}`);
        params.push(filter.createdAfter);
      }

      if (filter.createdBefore) {
        conditions.push(`d.created_at <= $${paramIndex++}`);
        params.push(filter.createdBefore);
      }

      if (filter.updatedAfter) {
        conditions.push(`d.updated_at >= $${paramIndex++}`);
        params.push(filter.updatedAfter);
      }

      if (filter.updatedBefore) {
        conditions.push(`d.updated_at <= $${paramIndex++}`);
        params.push(filter.updatedBefore);
      }

      // Por padrão, não incluir documentos excluídos
      if (!filter.includeDeleted) {
        conditions.push(`d.deleted_at IS NULL`);
      }

      const whereClause =
        conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';

      // Construir cláusula ORDER BY
      let orderByClause = 'ORDER BY d.updated_at DESC';

      if (sort) {
        const field =
          sort.field === 'fileSize' ? 'm.file_size' : `d.${sort.field}`;
        orderByClause = `ORDER BY ${field} ${sort.direction.toUpperCase()}`;
      }

      // Configurar paginação
      const page = pagination?.page || 1;
      const limit = pagination?.limit || 10;
      const offset = (page - 1) * limit;

      // Contar total de documentos
      const countQuery = `
        SELECT COUNT(*) as total
        FROM documents d
        JOIN document_metadata m ON d.id = m.document_id AND d.current_version = m.version
        ${whereClause}
      `;

      const countResult = await client.query(countQuery, params);
      const total = Number.parseInt(countResult.rows[0].total);
      const totalPages = Math.ceil(total / limit);

      // Buscar documentos
      const query = `
        SELECT d.*, m.*
        FROM documents d
        JOIN document_metadata m ON d.id = m.document_id AND d.current_version = m.version
        ${whereClause}
        ${orderByClause}
        LIMIT $${paramIndex++} OFFSET $${paramIndex++}
      `;

      params.push(limit, offset);

      const result = await client.query(query, params);

      // Mapear resultados para objetos Document
      const documents = result.rows.map(
        row =>
          new Document({
            id: row.id,
            title: row.title,
            description: row.description,
            ownerId: row.owner_id,
            isPublic: row.is_public,
            currentVersion: row.current_version,
            metadata: {
              title: row.title,
              description: row.description,
              author: row.author,
              keywords: row.keywords ? JSON.parse(row.keywords) : undefined,
              createdAt: new Date(row.created_at),
              updatedAt: new Date(row.updated_at),
              fileSize: row.file_size,
              pageCount: row.page_count,
              language: row.language,
              category: row.category,
              tags: row.tags ? JSON.parse(row.tags) : undefined,
              version: row.version,
              contentType: row.content_type,
              checksum: row.checksum,
            },
            createdAt: new Date(row.created_at),
            updatedAt: new Date(row.updated_at),
            deletedAt: row.deleted_at ? new Date(row.deleted_at) : undefined,
          })
      );

      return {
        documents,
        total,
        page,
        limit,
        totalPages,
      };
    } catch (error) {
      console.error('Erro ao buscar documentos:', error);
      throw error;
    } finally {
      client.release();
    }
  }

  async delete(id: string): Promise<boolean> {
    const client = await this.pool.connect();

    try {
      // Marcar documento como excluído (soft delete)
      const result = await client.query(
        `UPDATE documents SET deleted_at = $1 WHERE id = $2 AND deleted_at IS NULL`,
        [new Date(), id]
      );

      return result.rowCount > 0;
    } catch (error) {
      console.error('Erro ao excluir documento:', error);
      throw error;
    } finally {
      client.release();
    }
  }

  async restore(id: string): Promise<boolean> {
    const client = await this.pool.connect();

    try {
      // Restaurar documento excluído
      const result = await client.query(
        `UPDATE documents SET deleted_at = NULL WHERE id = $1 AND deleted_at IS NOT NULL`,
        [id]
      );

      return result.rowCount > 0;
    } catch (error) {
      console.error('Erro ao restaurar documento:', error);
      throw error;
    } finally {
      client.release();
    }
  }

  async permanentDelete(id: string): Promise<boolean> {
    const client = await this.pool.connect();

    try {
      await client.query('BEGIN');

      // Excluir versões do documento
      await client.query(
        `DELETE FROM document_versions WHERE document_id = $1`,
        [id]
      );

      // Excluir metadados do documento
      await client.query(
        `DELETE FROM document_metadata WHERE document_id = $1`,
        [id]
      );

      // Excluir documento
      const result = await client.query(`DELETE FROM documents WHERE id = $1`, [
        id,
      ]);

      await client.query('COMMIT');

      return result.rowCount > 0;
    } catch (error) {
      await client.query('ROLLBACK');
      console.error('Erro ao excluir permanentemente o documento:', error);
      throw error;
    } finally {
      client.release();
    }
  }
}
