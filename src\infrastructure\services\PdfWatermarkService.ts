/**
 * PDF Watermark Service
 *
 * Implementação do serviço de marca d'água em documentos PDF.
 * Parte da implementação da tarefa 8.3.2 - Visualização segura
 *
 * Nota: Esta implementação usa a biblioteca pdf-lib para manipulação de PDFs.
 * Em um ambiente real, seria necessário instalar esta dependência:
 * npm install pdf-lib
 */

import { PDFDocument, StandardFonts, degrees, rgb } from 'pdf-lib';
import { WatermarkOptions, WatermarkService } from '../../domain/services/WatermarkService';

export class PdfWatermarkService implements WatermarkService {
  async applyWatermark(
    pdfContent: Uint8Array,
    textOrOptions: string | WatermarkOptions,
    options?: WatermarkOptions
  ): Promise<Uint8Array> {
    try {
      // Carregar o documento PDF
      const pdfDoc = await PDFDocument.load(pdfContent);

      // Determinar as opções de marca d'água
      const watermarkOptions: WatermarkOptions =
        typeof textOrOptions === 'string'
          ? { text: textOrOptions, ...(options || {}) }
          : textOrOptions;

      // Definir valores padrão para opções não especificadas
      const {
        text,
        fontSize = 50,
        color = '0.5,0.5,0.5', // Cinza (R,G,B)
        opacity = 0.3,
        rotation = -45,
        position = 'diagonal',
        margin = 50,
      } = watermarkOptions;

      // Converter string de cor para objeto rgb
      const [r, g, b] = color.split(',').map((c) => Number.parseFloat(c));
      const colorRgb = rgb(r, g, b);

      // Carregar fonte padrão
      const font = await pdfDoc.embedFont(StandardFonts.Helvetica);

      // Aplicar marca d'água em cada página
      const pages = pdfDoc.getPages();

      for (const page of pages) {
        const { width, height } = page.getSize();

        // Calcular dimensões do texto
        const textWidth = font.widthOfTextAtSize(text, fontSize);
        const textHeight = font.heightAtSize(fontSize);

        // Determinar posição com base na opção selecionada
        let x: number;
        let y: number;

        switch (position) {
          case 'center':
            x = (width - textWidth) / 2;
            y = (height - textHeight) / 2;
            break;

          case 'tiled':
            // Para marca d'água em mosaico, adicionar várias instâncias
            for (let tileX = margin; tileX < width - margin; tileX += textWidth * 2) {
              for (let tileY = margin; tileY < height - margin; tileY += textHeight * 2) {
                page.drawText(text, {
                  x: tileX,
                  y: tileY,
                  size: fontSize,
                  font,
                  color: colorRgb,
                  opacity,
                  rotate: degrees(rotation),
                });
              }
            }
            continue; // Pular o drawText abaixo para evitar duplicação
          default:
            // Posicionar diagonalmente no centro da página
            x = (width - textWidth) / 2;
            y = (height - textHeight) / 2;
            break;
        }

        // Desenhar o texto da marca d'água
        page.drawText(text, {
          x,
          y,
          size: fontSize,
          font,
          color: colorRgb,
          opacity,
          rotate: degrees(rotation),
        });
      }

      // Serializar o documento modificado
      return await pdfDoc.save();
    } catch (error) {
      console.error("Erro ao aplicar marca d'água ao PDF:", error);
      // Em caso de erro, retornar o PDF original
      return pdfContent;
    }
  }

  async hasWatermark(pdfContent: Uint8Array): Promise<boolean> {
    try {
      // Esta é uma implementação simplificada que verifica a presença de texto
      // que possa indicar uma marca d'água. Em um cenário real, seria necessário
      // uma análise mais sofisticada do conteúdo do PDF.

      // Carregar o documento PDF
      const pdfDoc = await PDFDocument.load(pdfContent);

      // Extrair texto de cada página (simplificado - na prática, precisaria de uma
      // biblioteca mais robusta para extração de texto de PDFs)
      const pages = pdfDoc.getPages();

      // Verificar se há padrões comuns de marca d'água no documento
      // Nota: Esta é uma verificação muito básica e não confiável
      // Em um cenário real, seria necessário uma análise mais profunda

      // Verificar metadados do documento
      const { author, creator, producer } = pdfDoc.getAuthor() || '';
      const watermarkIndicators = [
        'watermark',
        'marca',
        'confidential',
        'confidencial',
        'draft',
        'rascunho',
        'copy',
        'cópia',
        'restricted',
        'restrito',
      ];

      const metadata = [author, creator, producer].filter(Boolean).join(' ').toLowerCase();

      for (const indicator of watermarkIndicators) {
        if (metadata.includes(indicator)) {
          return true;
        }
      }

      // Se não encontrou indicadores nos metadados, retornar falso
      // Em uma implementação real, seria necessário analisar o conteúdo das páginas
      return false;
    } catch (error) {
      console.error("Erro ao verificar marca d'água no PDF:", error);
      // Em caso de erro, assumir que não há marca d'água
      return false;
    }
  }

  generateWatermarkText(userId: string, userName?: string, timestamp?: Date): string {
    const date = timestamp || new Date();
    const formattedDate = date.toLocaleDateString();
    const formattedTime = date.toLocaleTimeString();

    if (userName) {
      return `Visualizado por: ${userName} (${userId}) | ${formattedDate} ${formattedTime}`;
    }
    return `Visualizado por: ${userId} | ${formattedDate} ${formattedTime}`;
  }
}
