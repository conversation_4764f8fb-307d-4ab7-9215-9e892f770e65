/**
 * Serviço de gerenciamento de papéis
 *
 * Este serviço é responsável por gerenciar papéis (roles) e suas hierarquias,
 * incluindo atribuição de papéis a usuários e permissões a papéis.
 */

import type { FullRoleData } from '@repository/interfaces/rbacInterfaces';
import { permissionRepository } from '@repository/permissionRepository';
import { resourceRepository } from '@repository/resourceRepository';
import { roleRepository } from '@repository/roleRepository';
import { logger } from '@utils/logger';
import { permissionCacheService } from './permissionCacheService';

/**
 * Serviço de papéis
 */
export const roleService = {
  /**
   * Obtém todos os papéis de um usuário
   * @param userId - ID do usuário
   * @returns Lista de papéis
   */
  async getUserRoles(userId: string): Promise<any[]> {
    try {
      const result = await roleRepository.getUserRoles(userId);
      return result.rows;
    } catch (error) {
      logger.error('Erro ao obter papéis do usuário:', error);
      return [];
    }
  },

  /**
   * Atribui um papel a um usuário
   * @param userId - ID do usuário
   * @param roleId - ID do papel
   * @returns Verdadeiro se a operação foi bem-sucedida
   */
  async assignRoleToUser(userId: string, roleId: string): Promise<boolean> {
    try {
      const result = await roleRepository.assignRoleToUser(userId, roleId);

      // Invalidar cache de permissões do usuário
      await permissionCacheService.invalidateUserPermissionsCache(userId);

      return result.rowCount > 0;
    } catch (error) {
      logger.error('Erro ao atribuir papel ao usuário:', error);
      return false;
    }
  },

  /**
   * Remove um papel de um usuário
   * @param userId - ID do usuário
   * @param roleId - ID do papel
   * @returns Verdadeiro se a operação foi bem-sucedida
   */
  async removeRoleFromUser(userId: string, roleId: string): Promise<boolean> {
    try {
      const result = await roleRepository.removeRoleFromUser(userId, roleId);

      // Invalidar cache de permissões do usuário
      await permissionCacheService.invalidateUserPermissionsCache(userId);

      return result.rowCount > 0;
    } catch (error) {
      logger.error('Erro ao remover papel do usuário:', error);
      return false;
    }
  },

  /**
   * Obtém detalhes completos de um papel, incluindo permissões
   * @param roleId - ID do papel
   * @returns Dados completos do papel
   */
  async getRoleDetails(roleId: string): Promise<FullRoleData | null> {
    try {
      // Obter dados básicos do papel
      const roleResult = await roleRepository.read(roleId);

      if (roleResult.rowCount === 0) {
        return null;
      }

      const role = roleResult.rows[0];

      // Obter permissões do papel
      const permissionsResult = await permissionRepository.getRolePermissions(roleId);

      // Obter papéis pais e filhos
      const parentRolesResult = await roleRepository.getParentRoles(roleId);
      const childRolesResult = await roleRepository.getChildRoles(roleId);

      return {
        ...role,
        permissions: permissionsResult.rows,
        parent_roles: parentRolesResult.rows.map((r) => r.name),
        child_roles: childRolesResult.rows.map((r) => r.name),
      };
    } catch (error) {
      logger.error('Erro ao obter detalhes do papel:', error);
      return null;
    }
  },

  /**
   * Associa uma permissão de recurso a um papel
   * @param roleId - ID do papel
   * @param resourceId - ID do recurso
   * @param permissionId - ID da permissão
   * @returns Verdadeiro se a operação foi bem-sucedida
   */
  async assignPermissionToRole(
    roleId: string,
    resourceId: string,
    permissionId: string
  ): Promise<boolean> {
    try {
      // Primeiro, verificar se a associação recurso-permissão já existe
      const resourcePermissionsResult = await resourceRepository.getResourcePermissions(resourceId);

      let resourcePermissionId = null;

      // Procurar a associação existente
      for (const row of resourcePermissionsResult.rows) {
        if (row.ulid_permission === permissionId) {
          resourcePermissionId = row.ulid_resource_permission;
          break;
        }
      }

      // Se não existir, criar a associação
      if (!resourcePermissionId) {
        const associationResult = await permissionRepository.associateWithResource(
          resourceId,
          permissionId
        );

        if (associationResult.rowCount === 0) {
          return false;
        }

        resourcePermissionId = associationResult.rows[0].ulid_resource_permission;
      }

      // Associar ao papel
      const result = await resourceRepository.associateWithRole(roleId, resourcePermissionId);

      // Invalidar cache de permissões para todos os usuários com este papel
      await permissionCacheService.invalidateRolePermissionsCache(roleId);

      return result.rowCount > 0;
    } catch (error) {
      logger.error('Erro ao atribuir permissão ao papel:', error);
      return false;
    }
  },

  /**
   * Remove uma permissão de recurso de um papel
   * @param roleId - ID do papel
   * @param resourcePermissionId - ID da associação recurso-permissão
   * @returns Verdadeiro se a operação foi bem-sucedida
   */
  async removePermissionFromRole(roleId: string, resourcePermissionId: string): Promise<boolean> {
    try {
      const result = await resourceRepository.dissociateFromRole(roleId, resourcePermissionId);

      // Invalidar cache de permissões para todos os usuários com este papel
      await permissionCacheService.invalidateRolePermissionsCache(roleId);

      return result.rowCount > 0;
    } catch (error) {
      logger.error('Erro ao remover permissão do papel:', error);
      return false;
    }
  },

  /**
   * Adiciona uma relação de hierarquia entre papéis
   * @param parentRoleId - ID do papel pai
   * @param childRoleId - ID do papel filho
   * @returns Verdadeiro se a operação foi bem-sucedida
   */
  async addRoleHierarchy(parentRoleId: string, childRoleId: string): Promise<boolean> {
    try {
      // Verificar se criaria um ciclo na hierarquia
      if (await this.wouldCreateHierarchyCycle(parentRoleId, childRoleId)) {
        logger.warn('Tentativa de criar ciclo na hierarquia de papéis:', {
          parentRoleId,
          childRoleId,
        });
        return false;
      }

      // Adicionar relação de hierarquia
      const result = await roleRepository.addRoleHierarchy(parentRoleId, childRoleId);

      // Invalidar cache de permissões para o papel filho
      await permissionCacheService.invalidateRolePermissionsCache(childRoleId);

      return result.rowCount > 0;
    } catch (error) {
      logger.error('Erro ao adicionar hierarquia de papéis:', error);
      return false;
    }
  },

  /**
   * Remove uma relação de hierarquia entre papéis
   * @param parentRoleId - ID do papel pai
   * @param childRoleId - ID do papel filho
   * @returns Verdadeiro se a operação foi bem-sucedida
   */
  async removeRoleHierarchy(parentRoleId: string, childRoleId: string): Promise<boolean> {
    try {
      // Remover relação de hierarquia
      const result = await roleRepository.removeRoleHierarchy(parentRoleId, childRoleId);

      // Invalidar cache de permissões para o papel filho
      await permissionCacheService.invalidateRolePermissionsCache(childRoleId);

      return result.rowCount > 0;
    } catch (error) {
      logger.error('Erro ao remover hierarquia de papéis:', error);
      return false;
    }
  },

  /**
   * Verifica se adicionar uma relação de hierarquia criaria um ciclo
   * @param parentRoleId - ID do papel pai
   * @param childRoleId - ID do papel filho
   * @returns Verdadeiro se criaria um ciclo
   */
  async wouldCreateHierarchyCycle(parentRoleId: string, childRoleId: string): Promise<boolean> {
    // Se o pai e o filho são o mesmo, é um ciclo
    if (parentRoleId === childRoleId) {
      return true;
    }

    // Verificar se o filho já é um ancestral do pai (o que criaria um ciclo)
    return await this.isRoleAncestor(childRoleId, parentRoleId);
  },

  /**
   * Verifica se um papel é ancestral de outro na hierarquia
   * @param ancestorRoleId - ID do possível papel ancestral
   * @param descendantRoleId - ID do papel descendente
   * @returns Verdadeiro se o papel é ancestral
   */
  async isRoleAncestor(ancestorRoleId: string, descendantRoleId: string): Promise<boolean> {
    // Conjunto para rastrear papéis já visitados (evitar loops infinitos)
    const visited = new Set<string>();

    // Função recursiva para verificar ancestrais
    const checkAncestors = async (roleId: string): Promise<boolean> => {
      // Se já visitamos este papel, pular
      if (visited.has(roleId)) {
        return false;
      }

      // Marcar como visitado
      visited.add(roleId);

      // Obter papéis pais
      const parentRolesResult = await roleRepository.getParentRoles(roleId);

      // Verificar cada papel pai
      for (const parentRole of parentRolesResult.rows) {
        // Se o pai é o ancestral que estamos procurando, encontramos
        if (parentRole.ulid_role === ancestorRoleId) {
          return true;
        }

        // Verificar recursivamente os ancestrais deste pai
        if (await checkAncestors(parentRole.ulid_role)) {
          return true;
        }
      }

      // Não encontramos o ancestral
      return false;
    };

    // Iniciar verificação a partir do descendente
    return await checkAncestors(descendantRoleId);
  },

  /**
   * Obtém todas as permissões de um papel, incluindo as herdadas
   * @param roleId - ID do papel
   * @returns Lista de permissões com informações de origem
   */
  async getRolePermissionsWithInheritance(roleId: string): Promise<any[]> {
    try {
      // Estrutura para armazenar permissões por ID (para evitar duplicatas)
      const permissionsMap: Record<string, any> = {};

      // Função recursiva para obter permissões de um papel e seus ancestrais
      const getPermissionsRecursive = async (
        currentRoleId: string,
        isDirect: boolean,
        visited = new Set<string>()
      ): Promise<void> => {
        // Evitar ciclos
        if (visited.has(currentRoleId)) {
          return;
        }

        visited.add(currentRoleId);

        // Obter informações do papel
        const roleResult = await roleRepository.read(currentRoleId);
        if (roleResult.rowCount === 0) {
          return;
        }

        const role = roleResult.rows[0];

        // Obter permissões diretas do papel
        const permissionsResult = await permissionRepository.getRolePermissions(currentRoleId);

        // Adicionar permissões ao mapa
        for (const perm of permissionsResult.rows) {
          // Se a permissão já existe como direta, não sobrescrever
          if (
            permissionsMap[perm.ulid_permission] &&
            permissionsMap[perm.ulid_permission].isDirect
          ) {
            continue;
          }

          // Adicionar ou atualizar permissão no mapa
          permissionsMap[perm.ulid_permission] = {
            ...perm,
            sourceRoleId: currentRoleId,
            sourceRoleName: role.name,
            isDirect,
          };
        }

        // Obter papéis pais e suas permissões
        const parentRolesResult = await roleRepository.getParentRoles(currentRoleId);

        for (const parentRole of parentRolesResult.rows) {
          await getPermissionsRecursive(parentRole.ulid_role, false, new Set(visited));
        }
      };

      // Iniciar a busca recursiva
      await getPermissionsRecursive(roleId, true);

      // Converter mapa para array
      return Object.values(permissionsMap);
    } catch (error) {
      logger.error('Erro ao obter permissões com herança:', error);
      return [];
    }
  },
};
