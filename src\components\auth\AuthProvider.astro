---
/**
 * Componente de Provedor de Autenticação - Astro Nativo
 * 
 * Este componente fornece funcionalidades de autenticação usando apenas
 * tecnologias nativas do Astro.js, substituindo qualquer implementação React.
 */

import { authService } from '@services/authService';

interface Props {
  /** Redirecionar para login se não autenticado */
  requireAuth?: boolean;
  /** Página de redirecionamento para usuários não autenticados */
  redirectTo?: string;
}

const { 
  requireAuth = false, 
  redirectTo = '/signin' 
} = Astro.props;

// Verificar autenticação no servidor
const isAuthenticated = await authService.isAuthenticated(Astro);
const user = isAuthenticated ? await authService.getAuthenticatedUser(Astro) : null;

// Redirecionar se autenticação for obrigatória e usuário não estiver autenticado
if (requireAuth && !isAuthenticated) {
  return Astro.redirect(redirectTo);
}

// Dados para o cliente
const authData = {
  isAuthenticated,
  user,
  requireAuth
};
---

<div class="auth-provider" data-auth={JSON.stringify(authData)}>
  <slot />
</div>

<script define:vars={{ authData }}>
  // Disponibilizar dados de autenticação globalmente
  window.authData = authData;
  
  // Funções utilitárias para autenticação no cliente
  window.auth = {
    isAuthenticated: () => authData.isAuthenticated,
    getUser: () => authData.user,
    
    // Função para logout
    logout: async () => {
      try {
        const response = await fetch('/api/auth/logout', {
          method: 'POST',
          credentials: 'include'
        });
        
        if (response.ok) {
          window.location.href = '/signin';
        } else {
          console.error('Erro ao fazer logout');
        }
      } catch (error) {
        console.error('Erro ao fazer logout:', error);
      }
    },
    
    // Função para verificar se o usuário tem uma permissão específica
    hasPermission: (permission) => {
      if (!authData.user) return false;
      return authData.user.permissions?.includes(permission) || false;
    },
    
    // Função para verificar se o usuário tem um papel específico
    hasRole: (role) => {
      if (!authData.user) return false;
      return authData.user.roles?.includes(role) || false;
    }
  };
  
  // Emitir evento personalizado quando a autenticação estiver pronta
  document.dispatchEvent(new CustomEvent('auth:ready', {
    detail: authData
  }));
</script>

<style>
  .auth-provider {
    /* Container transparente - não afeta o layout */
    display: contents;
  }
</style>
