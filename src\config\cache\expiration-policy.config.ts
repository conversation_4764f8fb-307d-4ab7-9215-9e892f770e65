/**
 * Configuração de políticas de expiração para cache
 *
 * Este arquivo define as estratégias de expiração para diferentes tipos de dados
 * armazenados no cache Valkey.
 */

/**
 * Tipos de estratégias de expiração
 */
export enum ExpirationStrategy {
  /**
   * Expiração fixa - o item expira após um tempo fixo
   */
  FIXED = 'fixed',

  /**
   * Expiração deslizante - o tempo de expiração é renovado a cada acesso
   */
  SLIDING = 'sliding',

  /**
   * Expiração adaptativa - o tempo de expiração é ajustado com base na frequência de acesso
   */
  ADAPTIVE = 'adaptive',

  /**
   * Sem expiração - o item não expira automaticamente
   */
  NONE = 'none',
}

/**
 * Interface para configuração de expiração
 */
export interface ExpirationConfig {
  /**
   * Estratégia de expiração
   */
  strategy: ExpirationStrategy;

  /**
   * Tempo de vida em segundos (TTL)
   */
  ttl: number;

  /**
   * Tempo máximo de vida em segundos (para estratégias adaptativas)
   */
  maxTtl?: number;

  /**
   * Tempo mínimo de vida em segundos (para estratégias adaptativas)
   */
  minTtl?: number;

  /**
   * Fator de ajuste para estratégias adaptativas
   */
  adjustmentFactor?: number;

  /**
   * Descrição da política
   */
  description: string;
}

/**
 * Configurações de expiração por tipo de conteúdo
 */
export const expirationPolicies: Record<string, ExpirationConfig> = {
  // Dados de usuário
  user: {
    strategy: ExpirationStrategy.SLIDING,
    ttl: 1800, // 30 minutos
    maxTtl: 7200, // 2 horas
    description: 'Dados de perfil e preferências de usuário',
  },

  // Dados de sessão
  session: {
    strategy: ExpirationStrategy.SLIDING,
    ttl: 1800, // 30 minutos
    maxTtl: 86400, // 24 horas
    description: 'Dados de sessão de usuário',
  },

  // Tokens de autenticação
  token: {
    strategy: ExpirationStrategy.FIXED,
    ttl: 900, // 15 minutos
    description: 'Tokens de acesso',
  },

  // Tokens de refresh
  refresh_token: {
    strategy: ExpirationStrategy.FIXED,
    ttl: 604800, // 7 dias
    description: 'Tokens de refresh',
  },

  // Dados de produto
  product: {
    strategy: ExpirationStrategy.ADAPTIVE,
    ttl: 3600, // 1 hora
    minTtl: 1800, // 30 minutos
    maxTtl: 86400, // 24 horas
    adjustmentFactor: 1.5,
    description: 'Dados de produtos',
  },

  // Dados de categoria
  category: {
    strategy: ExpirationStrategy.FIXED,
    ttl: 7200, // 2 horas
    description: 'Dados de categorias',
  },

  // Resultados de busca
  search: {
    strategy: ExpirationStrategy.FIXED,
    ttl: 300, // 5 minutos
    description: 'Resultados de busca',
  },

  // Dados de pedido
  order: {
    strategy: ExpirationStrategy.SLIDING,
    ttl: 1800, // 30 minutos
    maxTtl: 3600, // 1 hora
    description: 'Dados de pedidos',
  },

  // Dados de pagamento
  payment: {
    strategy: ExpirationStrategy.FIXED,
    ttl: 1800, // 30 minutos
    description: 'Dados de pagamentos',
  },

  // Conteúdo estático
  static: {
    strategy: ExpirationStrategy.FIXED,
    ttl: 604800, // 7 dias
    description: 'Conteúdo estático (imagens, CSS, JS)',
  },

  // Conteúdo semi-estático
  semi_static: {
    strategy: ExpirationStrategy.FIXED,
    ttl: 86400, // 24 horas
    description: 'Conteúdo semi-estático (páginas institucionais)',
  },

  // Dados de configuração
  config: {
    strategy: ExpirationStrategy.FIXED,
    ttl: 3600, // 1 hora
    description: 'Dados de configuração do sistema',
  },

  // Dados de localização
  location: {
    strategy: ExpirationStrategy.FIXED,
    ttl: 86400, // 24 horas
    description: 'Dados de localização (cidades, estados, países)',
  },

  // Dados de métricas
  metrics: {
    strategy: ExpirationStrategy.FIXED,
    ttl: 60, // 1 minuto
    description: 'Dados de métricas em tempo real',
  },

  // Dados de relatórios
  reports: {
    strategy: ExpirationStrategy.FIXED,
    ttl: 3600, // 1 hora
    description: 'Dados de relatórios',
  },

  // Dados de auditoria
  audit: {
    strategy: ExpirationStrategy.NONE,
    ttl: 0,
    description: 'Dados de auditoria (sem expiração automática)',
  },

  // Dados de webhook
  webhook: {
    strategy: ExpirationStrategy.FIXED,
    ttl: 86400, // 24 horas
    description: 'Dados de webhook',
  },

  // Dados de notificação
  notification: {
    strategy: ExpirationStrategy.FIXED,
    ttl: 604800, // 7 dias
    description: 'Dados de notificações',
  },

  // Dados de cache de página
  page: {
    strategy: ExpirationStrategy.FIXED,
    ttl: 3600, // 1 hora
    description: 'Cache de páginas renderizadas',
  },

  // Dados de cache de API
  api: {
    strategy: ExpirationStrategy.FIXED,
    ttl: 300, // 5 minutos
    description: 'Cache de respostas de API',
  },

  // Dados de cache de consulta
  query: {
    strategy: ExpirationStrategy.FIXED,
    ttl: 300, // 5 minutos
    description: 'Cache de resultados de consultas',
  },

  // Dados temporários
  temp: {
    strategy: ExpirationStrategy.FIXED,
    ttl: 300, // 5 minutos
    description: 'Dados temporários',
  },
};

/**
 * Obtém a configuração de expiração para um tipo de conteúdo
 * @param type Tipo de conteúdo
 * @returns Configuração de expiração
 */
export function getExpirationPolicy(type: string): ExpirationConfig {
  return (
    expirationPolicies[type] || {
      strategy: ExpirationStrategy.FIXED,
      ttl: 3600, // 1 hora (padrão)
      description: 'Configuração padrão',
    }
  );
}

/**
 * Calcula o TTL para um item com base na política de expiração
 * @param type Tipo de conteúdo
 * @param accessCount Número de acessos (para estratégias adaptativas)
 * @returns TTL em segundos
 */
export function calculateTTL(type: string, accessCount = 0): number {
  const policy = getExpirationPolicy(type);

  switch (policy.strategy) {
    case ExpirationStrategy.FIXED:
      return policy.ttl;

    case ExpirationStrategy.SLIDING:
      return policy.ttl;

    case ExpirationStrategy.ADAPTIVE: {
      // Ajustar TTL com base na frequência de acesso
      const factor = policy.adjustmentFactor || 1.5;
      const adjustedTTL = policy.ttl * factor ** Math.min(accessCount, 5);

      // Limitar ao TTL máximo e mínimo
      return Math.min(
        Math.max(adjustedTTL, policy.minTtl || policy.ttl / 2),
        policy.maxTtl || policy.ttl * 10
      );
    }

    case ExpirationStrategy.NONE:
      return 0; // Sem expiração

    default:
      return policy.ttl;
  }
}
