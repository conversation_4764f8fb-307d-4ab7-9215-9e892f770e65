---
/**
 * Layout otimizado para performance
 *
 * Este layout implementa as melhores práticas de performance,
 * incluindo CSS crítico, preload de recursos e outras estratégias de carregamento.
 */

import {
  generatePreconnectTags,
  generatePrefetchTags,
  generatePreloadTags,
  getCriticalCSS,
  getCriticalResources,
} from '../utils/performance';

// Propriedades do componente
interface Props {
  /**
   * T<PERSON><PERSON>lo da página
   */
  title: string;

  /**
   * Descrição da página para SEO
   */
  description?: string;

  /**
   * Nome da página para carregamento de recursos específicos
   * @default "default"
   */
  pageName?: string;

  /**
   * Se deve incluir CSS crítico inline
   * @default true
   */
  includeCriticalCSS?: boolean;

  /**
   * Se deve incluir preload de recursos críticos
   * @default true
   */
  includePreload?: boolean;

  /**
   * Se deve incluir prefetch de recursos futuros
   * @default true
   */
  includePrefetch?: boolean;

  /**
   * Se deve incluir preconnect com domínios externos
   * @default true
   */
  includePreconnect?: boolean;
}

// Valores padrão
const {
  title,
  description = 'Estação da Alfabetização - Plataforma educacional para alfabetização',
  pageName = 'default',
  includeCriticalCSS = true,
  includePreload = true,
  includePrefetch = true,
  includePreconnect = true,
} = Astro.props;

// Obter CSS crítico
let criticalCSS = '';
if (includeCriticalCSS) {
  criticalCSS = await getCriticalCSS(pageName);
}

// Obter recursos críticos
const { preload, prefetch, preconnect } = getCriticalResources(pageName);

// Gerar tags HTML para recursos
const preloadTags = includePreload ? generatePreloadTags(preload) : '';
const prefetchTags = includePrefetch ? generatePrefetchTags(prefetch) : '';
const preconnectTags = includePreconnect ? generatePreconnectTags(preconnect) : '';

// Determinar se a página é AMP
const isAmp = false;
---

<!DOCTYPE html>
<html lang="pt-BR">
  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content={description}>
    <title>{title}</title>
    
    {/* Preconnect com domínios externos */}
    <Fragment set:html={preconnectTags} />
    
    {/* CSS crítico inline */}
    {includeCriticalCSS && criticalCSS && (
      <style set:html={criticalCSS}></style>
    )}
    
    {/* Preload de recursos críticos */}
    <Fragment set:html={preloadTags} />
    
    {/* Prefetch de recursos futuros */}
    <Fragment set:html={prefetchTags} />
    
    {/* CSS não crítico carregado de forma assíncrona */}
    <link 
      rel="preload" 
      href="/styles/main.css" 
      as="style" 
      onload="this.onload=null;this.rel='stylesheet'"
    >
    <noscript>
      <link rel="stylesheet" href="/styles/main.css">
    </noscript>
    
    {/* Fontes */}
    <link 
      rel="preload" 
      href="/fonts/primary-font.woff2" 
      as="font" 
      type="font/woff2" 
      crossorigin
    >
    
    {/* Favicon */}
    <link rel="icon" href="/favicon.ico" sizes="any">
    <link rel="icon" href="/icon.svg" type="image/svg+xml">
    <link rel="apple-touch-icon" href="/apple-touch-icon.png">
    <link rel="manifest" href="/manifest.json">
    
    {/* Meta tags para redes sociais */}
    <meta property="og:title" content={title}>
    <meta property="og:description" content={description}>
    <meta property="og:type" content="website">
    <meta property="og:url" content={Astro.url}>
    <meta property="og:image" content="/images/social-share.jpg">
    
    {/* Meta tags para Twitter */}
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content={title}>
    <meta name="twitter:description" content={description}>
    <meta name="twitter:image" content="/images/social-share.jpg">
    
    {/* Script para detecção de JavaScript */}
    <script>
      document.documentElement.classList.remove('no-js');
      document.documentElement.classList.add('js');
    </script>
    
    {/* Slot para head adicional */}
    <slot name="head" />
  </head>
  
  <body>
    {/* Skip link para acessibilidade */}
    <a href="#main-content" class="skip-to-content">
      Pular para o conteúdo principal
    </a>
    
    {/* Cabeçalho */}
    <header class="site-header">
      <slot name="header" />
    </header>
    
    {/* Conteúdo principal */}
    <main id="main-content">
      <slot />
    </main>
    
    {/* Rodapé */}
    <footer class="site-footer">
      <slot name="footer" />
    </footer>
    
    {/* Scripts não críticos */}
    <script src="/scripts/main.js" defer></script>
    
    {/* Slot para scripts adicionais */}
    <slot name="scripts" />
  </body>
</html>

<style is:global>
  /* Estilos para o link de pular para o conteúdo */
  .skip-to-content {
    position: absolute;
    top: -40px;
    left: 0;
    background: var(--primary-blue);
    color: white;
    padding: 8px;
    z-index: 100;
    transition: top 0.3s;
  }
  
  .skip-to-content:focus {
    top: 0;
  }
  
  /* Estilos para detecção de JavaScript */
  .no-js .js-only {
    display: none;
  }
  
  .js .no-js-only {
    display: none;
  }
</style>

<script>
  // Detectar suporte a recursos modernos
  document.documentElement.classList.toggle('webp', 
    document.createElement('canvas')
      .toDataURL('image/webp')
      .indexOf('data:image/webp') === 0
  );
  
  document.documentElement.classList.toggle('avif', 
    document.createElement('canvas')
      .toDataURL('image/avif')
      .indexOf('data:image/avif') === 0
  );
  
  // Detectar preferências do usuário
  document.documentElement.classList.toggle('prefers-reduced-motion', 
    window.matchMedia('(prefers-reduced-motion: reduce)').matches
  );
  
  document.documentElement.classList.toggle('prefers-color-scheme-dark', 
    window.matchMedia('(prefers-color-scheme: dark)').matches
  );
  
  // Monitorar Core Web Vitals
  if ('PerformanceObserver' in window) {
    // LCP
    new PerformanceObserver((entryList) => {
      for (const entry of entryList.getEntries()) {
        console.log('LCP:', entry.startTime, entry);
      }
    }).observe({ type: 'largest-contentful-paint', buffered: true });
    
    // FID
    new PerformanceObserver((entryList) => {
      for (const entry of entryList.getEntries()) {
        console.log('FID:', entry.processingStart - entry.startTime, entry);
      }
    }).observe({ type: 'first-input', buffered: true });
    
    // CLS
    let clsValue = 0;
    new PerformanceObserver((entryList) => {
      for (const entry of entryList.getEntries()) {
        if (!entry.hadRecentInput) {
          clsValue += entry.value;
          console.log('CLS update:', clsValue, entry);
        }
      }
    }).observe({ type: 'layout-shift', buffered: true });
  }
</script>
