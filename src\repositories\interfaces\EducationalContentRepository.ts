/**
 * Interface de Repositório de Conteúdo Educacional
 *
 * Esta interface define os métodos para acesso e manipulação de conteúdos educacionais
 * no sistema, seguindo o princípio de inversão de dependência.
 */

import {
  ContentType,
  DifficultyLevel,
  EducationalContent,
} from '../../domain/entities/EducationalContent';

/**
 * Opções de filtro para busca de conteúdos educacionais
 */
export interface ContentFilterOptions {
  type?: ContentType;
  difficulty?: DifficultyLevel;
  authorId?: string;
  tags?: string[];
  published?: boolean;
  minAge?: number;
  maxAge?: number;
  search?: string;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

/**
 * Resultado paginado de conteúdos educacionais
 */
export interface PaginatedContents {
  items: EducationalContent[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

/**
 * Interface do Repositório de Conteúdo Educacional
 */
export interface EducationalContentRepository {
  /**
   * Busca um conteúdo educacional pelo ID
   *
   * @param id ID do conteúdo
   * @returns Conteúdo encontrado ou null se não existir
   */
  findById(id: string): Promise<EducationalContent | null>;

  /**
   * Busca conteúdos educacionais com filtros
   *
   * @param options Opções de filtro
   * @returns Resultado paginado de conteúdos
   */
  findAll(options?: ContentFilterOptions): Promise<PaginatedContents>;

  /**
   * Busca conteúdos educacionais por tipo
   *
   * @param type Tipo para filtro
   * @param options Opções de filtro adicionais
   * @returns Resultado paginado de conteúdos
   */
  findByType(
    type: ContentType,
    options?: Omit<ContentFilterOptions, 'type'>
  ): Promise<PaginatedContents>;

  /**
   * Busca conteúdos educacionais por nível de dificuldade
   *
   * @param difficulty Nível de dificuldade para filtro
   * @param options Opções de filtro adicionais
   * @returns Resultado paginado de conteúdos
   */
  findByDifficulty(
    difficulty: DifficultyLevel,
    options?: Omit<ContentFilterOptions, 'difficulty'>
  ): Promise<PaginatedContents>;

  /**
   * Busca conteúdos educacionais por autor
   *
   * @param authorId ID do autor para filtro
   * @param options Opções de filtro adicionais
   * @returns Resultado paginado de conteúdos
   */
  findByAuthor(
    authorId: string,
    options?: Omit<ContentFilterOptions, 'authorId'>
  ): Promise<PaginatedContents>;

  /**
   * Busca conteúdos educacionais por tags
   *
   * @param tags Tags para filtro
   * @param options Opções de filtro adicionais
   * @returns Resultado paginado de conteúdos
   */
  findByTags(
    tags: string[],
    options?: Omit<ContentFilterOptions, 'tags'>
  ): Promise<PaginatedContents>;

  /**
   * Busca conteúdos educacionais por faixa etária
   *
   * @param age Idade para filtro
   * @param options Opções de filtro adicionais
   * @returns Resultado paginado de conteúdos
   */
  findByAge(
    age: number,
    options?: Omit<ContentFilterOptions, 'minAge' | 'maxAge'>
  ): Promise<PaginatedContents>;

  /**
   * Salva um conteúdo educacional (cria ou atualiza)
   *
   * @param content Conteúdo a ser salvo
   * @returns Conteúdo salvo
   */
  save(content: EducationalContent): Promise<EducationalContent>;

  /**
   * Remove um conteúdo educacional
   *
   * @param id ID do conteúdo a ser removido
   * @returns true se removido com sucesso, false caso contrário
   */
  remove(id: string): Promise<boolean>;

  /**
   * Atualiza o status de publicação de um conteúdo
   *
   * @param id ID do conteúdo
   * @param published Status de publicação
   * @returns Conteúdo atualizado
   */
  updatePublishStatus(id: string, published: boolean): Promise<EducationalContent>;
}
