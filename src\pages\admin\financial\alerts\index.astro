---
import FinancialNav from '@components/navigation/FinancialNav.astro';
import { queryHelper } from '@db/queryHelper';
import { checkAdmin } from '@helpers/authGuard';
import AdminLayout from '@layouts/AdminLayout.astro';

// Protege a rota verificando se o usuário é admin
const authRedirect = await checkAdmin(Astro);
if (authRedirect) return authRedirect;

// Buscar configurações de alertas
const alertConfigs = await queryHelper.query(
  'SELECT * FROM tab_alert_config WHERE active = true ORDER BY type'
);

// Mapear tipos de alertas para nomes amigáveis
const alertTypeNames = {
  high_value_transaction: 'Transação de Alto Valor',
  suspicious_activity: 'Atividade Suspeita',
  payment_failure: 'Falha no Pagamento',
  chargeback_risk: 'Risco de Chargeback',
  payment_success: 'Pagamento Aprovado',
  refund_processed: 'Reembolso Processado',
};

// Buscar histórico de alertas recentes
const recentAlerts = await queryHelper.query(
  `SELECT 
    al.ulid_alert_log,
    al.type,
    al.created_at,
    al.ulid_order,
    al.ulid_user,
    al.order_total,
    al.payment_method,
    al.payment_status,
    u.name as user_name,
    u.email as user_email
   FROM tab_alert_log al
   JOIN tab_user u ON al.ulid_user = u.ulid_user
   ORDER BY al.created_at DESC
   LIMIT 10`
);

// Formatar valores monetários
const formatCurrency = (value) => {
  return new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: 'BRL',
  }).format(value || 0);
};

// Formatar data
const formatDate = (dateString) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  return `${date.toLocaleDateString('pt-BR')} ${date.toLocaleTimeString('pt-BR')}`;
};
---

<AdminLayout title="Alertas Financeiros">
  <div class="container mx-auto p-4">
    <h1 class="text-2xl font-bold mb-4">Gestão Financeira</h1>
    
    <FinancialNav />
    
    <h2 class="text-xl font-bold mb-6">Configuração de Alertas</h2>
    
    <!-- Configurações de Alertas -->
    <div class="card bg-base-100 shadow-xl mb-6">
      <div class="card-body">
        <h2 class="card-title">Alertas Disponíveis</h2>
        <p class="text-sm mb-4">Configure os alertas para monitorar transações financeiras.</p>
        
        <div class="overflow-x-auto">
          <table class="table table-zebra w-full">
            <thead>
              <tr>
                <th>Tipo de Alerta</th>
                <th>Status</th>
                <th>Limite</th>
                <th>Notificar Cliente</th>
                <th>Destinatários</th>
                <th>Ações</th>
              </tr>
            </thead>
            <tbody>
              {alertConfigs.rows.length === 0 ? (
                <tr>
                  <td colspan="6" class="text-center py-4">Nenhuma configuração de alerta encontrada</td>
                </tr>
              ) : (
                alertConfigs.rows.map(config => (
                  <tr>
                    <td>
                      <div class="font-bold">{alertTypeNames[config.type] || config.type}</div>
                      <div class="text-xs opacity-60">{config.description}</div>
                    </td>
                    <td>
                      <div class={`badge ${config.enabled ? 'badge-success' : 'badge-error'}`}>
                        {config.enabled ? 'Ativo' : 'Inativo'}
                      </div>
                    </td>
                    <td>
                      {config.threshold ? formatCurrency(config.threshold) : 'N/A'}
                    </td>
                    <td>
                      <div class={`badge ${config.notify_customer ? 'badge-info' : 'badge-ghost'}`}>
                        {config.notify_customer ? 'Sim' : 'Não'}
                      </div>
                    </td>
                    <td>
                      <div class="text-xs">
                        {config.recipients ? config.recipients.split(',').join(', ') : 'Nenhum'}
                      </div>
                    </td>
                    <td>
                      <a href={`/admin/financial/alerts/edit/${config.ulid_alert_config}`} class="btn btn-xs btn-primary">
                        Editar
                      </a>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
    
    <!-- Histórico de Alertas -->
    <div class="card bg-base-100 shadow-xl mb-6">
      <div class="card-body">
        <h2 class="card-title">Alertas Recentes</h2>
        <p class="text-sm mb-4">Histórico dos últimos alertas disparados.</p>
        
        <div class="overflow-x-auto">
          <table class="table table-zebra w-full">
            <thead>
              <tr>
                <th>Data</th>
                <th>Tipo</th>
                <th>Cliente</th>
                <th>Pedido</th>
                <th>Valor</th>
                <th>Status</th>
              </tr>
            </thead>
            <tbody>
              {recentAlerts.rows.length === 0 ? (
                <tr>
                  <td colspan="6" class="text-center py-4">Nenhum alerta recente encontrado</td>
                </tr>
              ) : (
                recentAlerts.rows.map(alert => (
                  <tr>
                    <td>{formatDate(alert.created_at)}</td>
                    <td>
                      <div class="badge badge-outline">
                        {alertTypeNames[alert.type] || alert.type}
                      </div>
                    </td>
                    <td>
                      <div>{alert.user_name}</div>
                      <div class="text-xs opacity-60">{alert.user_email}</div>
                    </td>
                    <td>{alert.ulid_order.substring(0, 8)}...</td>
                    <td>{formatCurrency(alert.order_total)}</td>
                    <td>
                      <div class={`badge ${
                        alert.payment_status === 'Aprovado' ? 'badge-success' :
                        alert.payment_status === 'Pendente' ? 'badge-warning' :
                        alert.payment_status === 'Rejeitado' ? 'badge-error' :
                        alert.payment_status === 'Reembolsado' ? 'badge-info' :
                        'badge-ghost'
                      }`}>
                        {alert.payment_status || 'N/A'}
                      </div>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
        
        <div class="card-actions justify-end mt-4">
          <a href="/admin/financial/alerts/history" class="btn btn-outline btn-sm">
            Ver Histórico Completo
          </a>
        </div>
      </div>
    </div>
  </div>
</AdminLayout>

<script>
  // Inicializar tooltips ou outros componentes interativos
  document.addEventListener('DOMContentLoaded', () => {
    // Código de inicialização, se necessário
  });
</script>
