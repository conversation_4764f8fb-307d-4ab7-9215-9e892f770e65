/**
 * <PERSON><PERSON><PERSON><PERSON> de logging
 *
 * Este módulo exporta uma instância configurada da fábrica de loggers
 * e funções auxiliares para obter loggers.
 */

import { config } from '../../config';
import { LogLevel, Logger } from '../../domain/interfaces/Logger';
import { LoggerFactory } from './LoggerFactory';

// Obter configuração de logging do arquivo de configuração
const loggingConfig = config.logging || {};

// Criar instância da fábrica de loggers
const loggerFactory = new LoggerFactory({
  minLevel: loggingConfig.minLevel || LogLevel.INFO,
  includeTimestamp: loggingConfig.includeTimestamp !== false,
  includeContext: loggingConfig.includeContext !== false,
  format: loggingConfig.format || 'text',
  destinations: loggingConfig.destinations || [
    {
      type: 'console',
      minLevel: LogLevel.INFO,
    },
    {
      type: 'file',
      minLevel: LogLevel.DEBUG,
      config: {
        filePath: 'logs/app.log',
        maxSize: 10 * 1024 * 1024, // 10 MB
        maxFiles: 5,
      },
    },
  ],
});

// Exportar a fábrica de loggers
export { loggerFactory };

// Exportar função para obter um logger para um contexto específico
export function getLogger(context: string): Logger {
  return loggerFactory.createLogger(context);
}

// Exportar o logger global
export const logger = loggerFactory.getGlobalLogger();

// Re-exportar tipos e enums
export { Logger, LogLevel, LogMetadata } from '../../domain/interfaces/Logger';
