---
/**
 * Página de Métricas de On-Demand Rendering
 *
 * Esta página exibe métricas de performance do ODR para administradores.
 * Inclui estatísticas de cache, tempo de renderização e outras métricas.
 */

import { getCacheStats } from '../../infrastructure/cache/LayeredCacheService';
import AdminLayout from '../../layouts/AdminLayout.astro';
import { MetricType, getCacheHitRate, getODRMetrics } from '../../services/ODRMetricsService';

// Verificar autenticação
const isAuthenticated = true; // Em produção, verificar autenticação real

// Se não estiver autenticado, redirecionar para login
if (!isAuthenticated) {
  return Astro.redirect('/admin/login');
}

// Obter métricas
const renderTimeMetrics = await getODRMetrics(MetricType.RENDER_TIME, 'hourly', 24);
const cacheHitMetrics = await getODRMetrics(MetricType.CACHE_HIT, 'hourly', 24);
const cacheMissMetrics = await getODRMetrics(MetricType.CACHE_MISS, 'hourly', 24);
const revalidationMetrics = await getODRMetrics(MetricType.REVALIDATION, 'hourly', 24);
const errorMetrics = await getODRMetrics(MetricType.ERROR, 'hourly', 24);

// Calcular taxa de acerto de cache
const cacheHitRate = await getCacheHitRate('hourly');

// Obter estatísticas de cache
const cacheStats = getCacheStats();

// Formatar dados para gráficos
const formatMetricsForChart = (metrics) => {
  return {
    labels: metrics.map((m) => new Date(m.timestamp).toLocaleTimeString()),
    values: metrics.map((m) => m.avg),
    counts: metrics.map((m) => m.count),
  };
};

const renderTimeChart = formatMetricsForChart(renderTimeMetrics);
const cacheHitChart = formatMetricsForChart(cacheHitMetrics);
const cacheMissChart = formatMetricsForChart(cacheMissMetrics);
---

<AdminLayout title="Métricas de ODR - Administração">
  <div class="admin-container">
    <h1>Métricas de On-Demand Rendering</h1>
    
    <div class="metrics-summary">
      <div class="metric-card">
        <h3>Taxa de Acerto de Cache</h3>
        <div class="metric-value">{(cacheHitRate * 100).toFixed(2)}%</div>
        <div class="metric-description">Percentual de requisições servidas do cache</div>
      </div>
      
      <div class="metric-card">
        <h3>Tempo Médio de Renderização</h3>
        <div class="metric-value">
          {renderTimeMetrics.length > 0 
            ? `${renderTimeMetrics[0].avg.toFixed(2)}ms` 
            : 'N/A'}
        </div>
        <div class="metric-description">Tempo médio para renderizar páginas</div>
      </div>
      
      <div class="metric-card">
        <h3>Cache Hits (Memória)</h3>
        <div class="metric-value">{cacheStats.hits.memory}</div>
        <div class="metric-description">Acessos ao cache em memória</div>
      </div>
      
      <div class="metric-card">
        <h3>Cache Hits (Distribuído)</h3>
        <div class="metric-value">{cacheStats.hits.distributed}</div>
        <div class="metric-description">Acessos ao cache distribuído</div>
      </div>
    </div>
    
    <div class="charts-container">
      <div class="chart-wrapper">
        <h2>Tempo de Renderização (ms)</h2>
        <canvas id="renderTimeChart"></canvas>
      </div>
      
      <div class="chart-wrapper">
        <h2>Cache Hits vs Misses</h2>
        <canvas id="cacheHitMissChart"></canvas>
      </div>
    </div>
    
    <div class="metrics-details">
      <h2>Detalhes de Cache</h2>
      
      <table class="metrics-table">
        <thead>
          <tr>
            <th>Métrica</th>
            <th>Cache em Memória</th>
            <th>Cache Distribuído</th>
            <th>Total</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>Hits</td>
            <td>{cacheStats.hits.memory}</td>
            <td>{cacheStats.hits.distributed}</td>
            <td>{cacheStats.hits.memory + cacheStats.hits.distributed}</td>
          </tr>
          <tr>
            <td>Misses</td>
            <td>{cacheStats.misses.memory}</td>
            <td>{cacheStats.misses.distributed}</td>
            <td>{cacheStats.misses.memory + cacheStats.misses.distributed}</td>
          </tr>
          <tr>
            <td>Sets</td>
            <td>{cacheStats.sets.memory}</td>
            <td>{cacheStats.sets.distributed}</td>
            <td>{cacheStats.sets.memory + cacheStats.sets.distributed}</td>
          </tr>
          <tr>
            <td>Erros</td>
            <td>{cacheStats.errors.memory}</td>
            <td>{cacheStats.errors.distributed}</td>
            <td>{cacheStats.errors.memory + cacheStats.errors.distributed}</td>
          </tr>
        </tbody>
      </table>
    </div>
    
    <div class="admin-actions">
      <h2>Ações de Administração</h2>
      
      <div class="action-buttons">
        <button id="clearCacheStats" class="btn btn-secondary">Limpar Estatísticas</button>
        <button id="invalidateAllCache" class="btn btn-danger">Invalidar Todo o Cache</button>
        <button id="invalidateContentCache" class="btn btn-warning">Invalidar Cache de Conteúdo</button>
      </div>
      
      <div class="revalidation-form">
        <h3>Revalidar Conteúdo Específico</h3>
        <form id="revalidateForm">
          <div class="form-group">
            <label for="contentId">ID do Conteúdo</label>
            <input type="text" id="contentId" name="contentId" placeholder="ID do conteúdo">
          </div>
          
          <div class="form-group">
            <label for="contentType">Tipo de Conteúdo</label>
            <select id="contentType" name="contentType">
              <option value="">Selecione um tipo</option>
              <option value="atividade">Atividade</option>
              <option value="jogo">Jogo</option>
              <option value="material">Material</option>
              <option value="video">Vídeo</option>
              <option value="audio">Áudio</option>
            </select>
          </div>
          
          <button type="submit" class="btn btn-primary">Revalidar</button>
        </form>
      </div>
    </div>
  </div>
</AdminLayout>

<script>
  // Importar Chart.js
  import Chart from 'chart.js/auto';
  
  // Dados para gráficos
  const renderTimeData = {
    labels: JSON.parse('{JSON.stringify(renderTimeChart.labels)}'),
    values: JSON.parse('{JSON.stringify(renderTimeChart.values)}'),
  };
  
  const cacheHitData = {
    labels: JSON.parse('{JSON.stringify(cacheHitChart.labels)}'),
    hitValues: JSON.parse('{JSON.stringify(cacheHitChart.counts)}'),
    missValues: JSON.parse('{JSON.stringify(cacheMissChart.counts)}'),
  };
  
  // Criar gráfico de tempo de renderização
  const renderTimeCtx = document.getElementById('renderTimeChart');
  new Chart(renderTimeCtx, {
    type: 'line',
    data: {
      labels: renderTimeData.labels,
      datasets: [{
        label: 'Tempo de Renderização (ms)',
        data: renderTimeData.values,
        borderColor: '#4a6cf7',
        backgroundColor: 'rgba(74, 108, 247, 0.1)',
        tension: 0.3,
        fill: true,
      }]
    },
    options: {
      responsive: true,
      scales: {
        y: {
          beginAtZero: true,
          title: {
            display: true,
            text: 'Tempo (ms)'
          }
        },
        x: {
          title: {
            display: true,
            text: 'Hora'
          }
        }
      }
    }
  });
  
  // Criar gráfico de cache hits vs misses
  const cacheHitMissCtx = document.getElementById('cacheHitMissChart');
  new Chart(cacheHitMissCtx, {
    type: 'bar',
    data: {
      labels: cacheHitData.labels,
      datasets: [
        {
          label: 'Cache Hits',
          data: cacheHitData.hitValues,
          backgroundColor: 'rgba(75, 192, 192, 0.7)',
        },
        {
          label: 'Cache Misses',
          data: cacheHitData.missValues,
          backgroundColor: 'rgba(255, 99, 132, 0.7)',
        }
      ]
    },
    options: {
      responsive: true,
      scales: {
        y: {
          beginAtZero: true,
          title: {
            display: true,
            text: 'Contagem'
          }
        },
        x: {
          title: {
            display: true,
            text: 'Hora'
          }
        }
      }
    }
  });
  
  // Manipuladores de eventos para botões de ação
  document.getElementById('clearCacheStats').addEventListener('click', async () => {
    if (confirm('Tem certeza que deseja limpar as estatísticas de cache?')) {
      const response = await fetch('/api/admin/clear-cache-stats', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      
      if (response.ok) {
        alert('Estatísticas de cache limpas com sucesso!');
        window.location.reload();
      } else {
        alert('Erro ao limpar estatísticas de cache.');
      }
    }
  });
  
  document.getElementById('invalidateAllCache').addEventListener('click', async () => {
    if (confirm('Tem certeza que deseja invalidar todo o cache? Isso pode afetar a performance do sistema.')) {
      const response = await fetch('/api/revalidate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          secret: 'estacao-alfabetizacao-secret',
          contentType: 'all',
        }),
      });
      
      if (response.ok) {
        alert('Cache invalidado com sucesso!');
      } else {
        alert('Erro ao invalidar cache.');
      }
    }
  });
  
  document.getElementById('revalidateForm').addEventListener('submit', async (e) => {
    e.preventDefault();
    
    const contentId = document.getElementById('contentId').value;
    const contentType = document.getElementById('contentType').value;
    
    if (!contentId && !contentType) {
      alert('Por favor, forneça um ID de conteúdo ou selecione um tipo de conteúdo.');
      return;
    }
    
    const response = await fetch('/api/revalidate', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        secret: 'estacao-alfabetizacao-secret',
        contentId,
        contentType,
      }),
    });
    
    if (response.ok) {
      const data = await response.json();
      alert(`Cache revalidado com sucesso! ${data.message}`);
    } else {
      alert('Erro ao revalidar cache.');
    }
  });
</script>

<style>
  .admin-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
  }
  
  h1 {
    margin-bottom: 2rem;
    color: var(--color-text-dark, #111827);
  }
  
  .metrics-summary {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2.5rem;
  }
  
  .metric-card {
    background-color: white;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    padding: 1.5rem;
    text-align: center;
  }
  
  .metric-value {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--color-primary, #4a6cf7);
    margin: 1rem 0;
  }
  
  .metric-description {
    color: var(--color-text-light, #6b7280);
    font-size: 0.875rem;
  }
  
  .charts-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin-bottom: 2.5rem;
  }
  
  .chart-wrapper {
    background-color: white;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    padding: 1.5rem;
  }
  
  .chart-wrapper h2 {
    margin-bottom: 1rem;
    font-size: 1.25rem;
    color: var(--color-text-dark, #111827);
  }
  
  .metrics-details {
    background-color: white;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    padding: 1.5rem;
    margin-bottom: 2.5rem;
  }
  
  .metrics-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 1rem;
  }
  
  .metrics-table th,
  .metrics-table td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid var(--color-border, #e5e7eb);
  }
  
  .metrics-table th {
    background-color: var(--color-bg-light, #f3f4f6);
    font-weight: 600;
  }
  
  .admin-actions {
    background-color: white;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    padding: 1.5rem;
  }
  
  .action-buttons {
    display: flex;
    gap: 1rem;
    margin: 1.5rem 0;
  }
  
  .revalidation-form {
    margin-top: 2rem;
  }
  
  .form-group {
    margin-bottom: 1rem;
  }
  
  .form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
  }
  
  .form-group input,
  .form-group select {
    width: 100%;
    padding: 0.625rem;
    border: 1px solid var(--color-border, #e5e7eb);
    border-radius: 0.375rem;
    font-size: 1rem;
  }
  
  .btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.625rem 1.25rem;
    font-weight: 500;
    border-radius: 0.375rem;
    border: none;
    cursor: pointer;
    transition: all 0.2s;
    font-size: 1rem;
  }
  
  .btn-primary {
    background-color: var(--color-primary, #4a6cf7);
    color: white;
  }
  
  .btn-secondary {
    background-color: var(--color-secondary, #6b7280);
    color: white;
  }
  
  .btn-danger {
    background-color: var(--color-danger, #ef4444);
    color: white;
  }
  
  .btn-warning {
    background-color: var(--color-warning, #f59e0b);
    color: white;
  }
  
  @media (max-width: 768px) {
    .charts-container {
      grid-template-columns: 1fr;
    }
    
    .action-buttons {
      flex-direction: column;
    }
  }
</style>
