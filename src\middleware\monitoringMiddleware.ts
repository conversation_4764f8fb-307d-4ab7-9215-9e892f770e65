/**
 * Middleware de Monitoramento
 *
 * Este middleware monitora as requisições HTTP e coleta métricas
 * de performance e uso para o serviço de monitoramento.
 */

import { performance } from 'node:perf_hooks';
import { monitoringEvents } from '@services/applicationMonitoringService';
import { logger } from '@utils/logger';
import { NextFunction, Request, Response } from 'express';

/**
 * Interface para dados de requisição
 */
interface RequestData {
  id: string;
  path: string;
  method: string;
  startTime: number;
  userId?: string;
}

/**
 * Mapa de requisições em andamento
 */
const activeRequests = new Map<string, RequestData>();

/**
 * Gera um ID único para a requisição
 * @returns ID único
 */
function generateRequestId(): string {
  return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
}

/**
 * Middleware para monitoramento de requisições
 */
export function monitoringMiddleware(req: Request, res: Response, next: NextFunction): void {
  // Gerar ID único para a requisição
  const requestId = generateRequestId();
  req.id = requestId;

  // Registrar dados da requisição
  const requestData: RequestData = {
    id: requestId,
    path: req.path,
    method: req.method,
    startTime: performance.now(),
    userId: req.user?.id,
  };

  // Armazenar requisição ativa
  activeRequests.set(requestId, requestData);

  // Emitir evento de início de requisição
  monitoringEvents.emit('request_start', {
    id: requestId,
    path: req.path,
    method: req.method,
    userId: req.user?.id,
  });

  // Monitorar resposta
  res.on('finish', () => {
    // Obter dados da requisição
    const data = activeRequests.get(requestId);

    if (data) {
      // Calcular duração
      const duration = performance.now() - data.startTime;

      // Emitir evento de fim de requisição
      monitoringEvents.emit('request_end', {
        id: requestId,
        path: data.path,
        method: data.method,
        duration,
        statusCode: res.statusCode,
        userId: data.userId,
      });

      // Registrar métricas específicas para erros
      if (res.statusCode >= 400) {
        monitoringEvents.emit('request_error', {
          id: requestId,
          path: data.path,
          method: data.method,
          statusCode: res.statusCode,
          userId: data.userId,
        });

        // Registrar erro no log para códigos 5xx
        if (res.statusCode >= 500) {
          logger.error(`Erro ${res.statusCode} em ${data.method} ${data.path}`, {
            requestId,
            statusCode: res.statusCode,
            duration,
            userId: data.userId,
          });
        }
      }

      // Remover requisição do mapa de ativas
      activeRequests.delete(requestId);
    }
  });

  // Continuar para o próximo middleware
  next();
}

/**
 * Middleware para monitoramento de erros
 */
export function errorMonitoringMiddleware(
  err: Error,
  req: Request,
  res: Response,
  next: NextFunction
): void {
  // Obter ID da requisição
  const requestId = req.id || generateRequestId();

  // Emitir evento de erro
  monitoringEvents.emit('application_error', {
    id: requestId,
    path: req.path,
    method: req.method,
    error: err.message,
    stack: err.stack,
    userId: req.user?.id,
  });

  // Registrar erro no log
  logger.error(`Erro não tratado em ${req.method} ${req.path}: ${err.message}`, {
    requestId,
    error: err.message,
    stack: err.stack,
    userId: req.user?.id,
  });

  // Continuar para o próximo middleware de erro
  next(err);
}

/**
 * Obtém estatísticas de requisições ativas
 * @returns Estatísticas de requisições
 */
export function getRequestStats(): Record<string, any> {
  const stats = {
    activeRequests: activeRequests.size,
    requestsByPath: {} as Record<string, number>,
    requestsByMethod: {} as Record<string, number>,
  };

  // Agrupar requisições por caminho e método
  for (const [_, data] of activeRequests.entries()) {
    // Por caminho
    if (!stats.requestsByPath[data.path]) {
      stats.requestsByPath[data.path] = 0;
    }
    stats.requestsByPath[data.path]++;

    // Por método
    if (!stats.requestsByMethod[data.method]) {
      stats.requestsByMethod[data.method] = 0;
    }
    stats.requestsByMethod[data.method]++;
  }

  return stats;
}
