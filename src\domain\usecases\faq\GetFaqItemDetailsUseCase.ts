/**
 * Get FAQ Item Details Use Case
 *
 * Caso de uso para obter detalhes de um item de FAQ.
 * Parte da implementação da tarefa 8.7.1 - Implementação de FAQ interativo
 */

import { FaqItem } from '../../entities/FaqItem';
import { FaqRepository } from '../../repositories/FaqRepository';

export interface GetFaqItemDetailsRequest {
  id: string;
  incrementViewCount?: boolean;
}

export interface GetFaqItemDetailsResponse {
  success: boolean;
  data?: {
    item: FaqItem;
    relatedItems: FaqItem[];
  };
  error?: string;
}

export class GetFaqItemDetailsUseCase {
  constructor(private faqRepository: FaqRepository) {}

  async execute(request: GetFaqItemDetailsRequest): Promise<GetFaqItemDetailsResponse> {
    try {
      // Validar os dados de entrada
      if (!request.id) {
        return {
          success: false,
          error: 'ID do item de FAQ é obrigatório.',
        };
      }

      // Obter o item de FAQ
      const item = await this.faqRepository.getById(request.id);

      if (!item) {
        return {
          success: false,
          error: `Item de FAQ com ID ${request.id} não encontrado.`,
        };
      }

      // Incrementar o contador de visualizações, se solicitado
      if (request.incrementViewCount) {
        await this.faqRepository.incrementViewCount(request.id);
        item.incrementViewCount();
      }

      // Obter itens relacionados
      const relatedItems = await this.faqRepository.getRelatedItems(
        request.id,
        true, // Apenas itens publicados
        5 // Limite de itens relacionados
      );

      return {
        success: true,
        data: {
          item,
          relatedItems,
        },
      };
    } catch (error) {
      console.error('Erro ao obter detalhes do item de FAQ:', error);

      return {
        success: false,
        error:
          error instanceof Error
            ? error.message
            : 'Erro desconhecido ao obter detalhes do item de FAQ.',
      };
    }
  }
}
