---
import { PageTransition } from '../../components/transitions';
import DaisyCard from '../../components/ui/DaisyCard.astro';
import Breadcrumbs from '../../components/ui/navigation/Breadcrumbs.astro';
/**
 * Página de demonstração da paleta de cores
 * Esta página mostra todas as cores disponíveis no sistema
 */
import BaseLayout from '../../layouts/BaseLayout.astro';
import { Container, Grid, Section } from '../../layouts/grid';

// Título da página
const title = 'Paleta de Cores';

// Breadcrumbs para a página atual
const breadcrumbItems = [
  { href: '/', label: 'Início' },
  { href: '/examples', label: 'Exemplos' },
  { label: 'Paleta de Cores' },
];

// Cores primárias
const primaryColors = [
  { name: '<PERSON>er<PERSON><PERSON>', variable: '--primary-red', class: 'bg-red' },
  { name: '<PERSON>', variable: '--primary-green', class: 'bg-green' },
  { name: '<PERSON><PERSON>', variable: '--primary-brown', class: 'bg-brown' },
  { name: '<PERSON>elo', variable: '--primary-yellow', class: 'bg-yellow' },
  { name: 'Azul', variable: '--primary-blue', class: 'bg-blue' },
];

// Cores secundárias
const secondaryColors = [
  { name: 'Roxo', variable: '--secondary-purple', class: 'bg-purple' },
  { name: 'Verde-água', variable: '--secondary-teal', class: 'bg-teal' },
  { name: 'Laranja', variable: '--secondary-orange', class: 'bg-orange' },
  { name: 'Ciano', variable: '--secondary-cyan', class: 'bg-cyan' },
];

// Cores de estado
const stateColors = [
  { name: 'Sucesso', variable: '--success', class: 'bg-success' },
  { name: 'Erro', variable: '--error', class: 'bg-error' },
  { name: 'Alerta', variable: '--warning', class: 'bg-warning' },
  { name: 'Informação', variable: '--info', class: 'bg-info' },
];

// Cores neutras
const neutralColors = [
  { name: 'Neutro 50', variable: '--neutral-50', value: '#fafafa' },
  { name: 'Neutro 100', variable: '--neutral-100', value: '#f5f5f5' },
  { name: 'Neutro 200', variable: '--neutral-200', value: '#eeeeee' },
  { name: 'Neutro 300', variable: '--neutral-300', value: '#e0e0e0' },
  { name: 'Neutro 400', variable: '--neutral-400', value: '#bdbdbd' },
  { name: 'Neutro 500', variable: '--neutral-500', value: '#9e9e9e' },
  { name: 'Neutro 600', variable: '--neutral-600', value: '#757575' },
  { name: 'Neutro 700', variable: '--neutral-700', value: '#616161' },
  { name: 'Neutro 800', variable: '--neutral-800', value: '#424242' },
  { name: 'Neutro 900', variable: '--neutral-900', value: '#212121' },
];

// Cores temáticas
const thematicColors = [
  { name: 'Alfabetização', variable: '--theme-alfabetizacao', class: 'text-red' },
  { name: 'Matemática', variable: '--theme-matematica', class: 'text-green' },
  { name: 'Ciências', variable: '--theme-ciencias', class: 'text-purple' },
  { name: 'Artes', variable: '--theme-artes', class: 'text-yellow' },
  { name: 'Geografia', variable: '--theme-geografia', class: 'text-blue' },
  { name: 'História', variable: '--theme-historia', class: 'text-brown' },
];

// Função para obter o valor computado de uma variável CSS
const getComputedColor = (variable) => {
  if (typeof window !== 'undefined') {
    return getComputedStyle(document.documentElement).getPropertyValue(variable);
  }
  return '';
};
---

<BaseLayout title={title} enableViewTransitions={true} transitionType="fade">
  <Container>
    <PageTransition type="fade" duration="0.5s">
      <Section>
        <Breadcrumbs items={breadcrumbItems} class="mb-6" />
        
        <h1 class="text-3xl font-bold mb-6">{title}</h1>
        
        <p class="mb-8">
          Esta página demonstra a paleta de cores disponível no projeto Estação da Alfabetização.
          A paleta é inspirada nas cores da Turma da Mônica, adaptada para uso educacional e otimizada para acessibilidade.
        </p>
        
        <h2 class="text-2xl font-bold mb-4">Cores Primárias</h2>
        <Grid cols={{ sm: 1, md: 2, lg: 3 }} gap={4} class="mb-8">
          {primaryColors.map(color => (
            <div class="flex flex-col">
              <div class={`${color.class} h-24 rounded-t-box flex items-end p-2`}>
                <span class="text-sm font-mono">{color.variable}</span>
              </div>
              <div class="bg-base-200 p-4 rounded-b-box">
                <h3 class="font-bold">{color.name}</h3>
                <div class="flex flex-col mt-2">
                  <div class="flex gap-2">
                    <div class={`w-6 h-6 rounded ${color.class.replace('bg-', 'bg-')}`}></div>
                    <span>Normal</span>
                  </div>
                  <div class="flex gap-2 mt-1">
                    <div class={`w-6 h-6 rounded ${color.class.replace('bg-', 'bg-')}-light`}></div>
                    <span>Light</span>
                  </div>
                  <div class="flex gap-2 mt-1">
                    <div class={`w-6 h-6 rounded ${color.class.replace('bg-', 'bg-')}-dark`}></div>
                    <span>Dark</span>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </Grid>
        
        <h2 class="text-2xl font-bold mb-4">Cores Secundárias</h2>
        <Grid cols={{ sm: 1, md: 2, lg: 4 }} gap={4} class="mb-8">
          {secondaryColors.map(color => (
            <div class="flex flex-col">
              <div class={`${color.class} h-24 rounded-t-box flex items-end p-2`}>
                <span class="text-sm font-mono">{color.variable}</span>
              </div>
              <div class="bg-base-200 p-4 rounded-b-box">
                <h3 class="font-bold">{color.name}</h3>
              </div>
            </div>
          ))}
        </Grid>
        
        <h2 class="text-2xl font-bold mb-4">Cores de Estado</h2>
        <Grid cols={{ sm: 1, md: 2, lg: 4 }} gap={4} class="mb-8">
          {stateColors.map(color => (
            <div class="flex flex-col">
              <div class={`${color.class} h-24 rounded-t-box flex items-end p-2`}>
                <span class="text-sm font-mono">{color.variable}</span>
              </div>
              <div class="bg-base-200 p-4 rounded-b-box">
                <h3 class="font-bold">{color.name}</h3>
              </div>
            </div>
          ))}
        </Grid>
        
        <h2 class="text-2xl font-bold mb-4">Cores Neutras</h2>
        <div class="overflow-x-auto mb-8">
          <table class="table w-full">
            <thead>
              <tr>
                <th>Nome</th>
                <th>Variável</th>
                <th>Valor</th>
                <th>Amostra</th>
              </tr>
            </thead>
            <tbody>
              {neutralColors.map(color => (
                <tr>
                  <td>{color.name}</td>
                  <td><code>{color.variable}</code></td>
                  <td><code>{color.value}</code></td>
                  <td>
                    <div class="w-8 h-8 rounded" style={`background-color: ${color.value};`}></div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        
        <h2 class="text-2xl font-bold mb-4">Cores Temáticas</h2>
        <Grid cols={{ sm: 1, md: 2, lg: 3 }} gap={4} class="mb-8">
          {thematicColors.map(color => (
            <DaisyCard title={color.name}>
              <p class="mb-2">Variável: <code>{color.variable}</code></p>
              <p class={color.class}>Exemplo de texto nesta cor</p>
              <div class={`w-full h-8 mt-2 rounded ${color.class.replace('text-', 'bg-')}`}></div>
            </DaisyCard>
          ))}
        </Grid>
        
        <h2 class="text-2xl font-bold mb-4">Gradientes</h2>
        <Grid cols={{ sm: 1, md: 3 }} gap={4} class="mb-8">
          <div class="gradient-primary h-32 rounded-box flex items-center justify-center text-white font-bold">
            Gradiente Primário
          </div>
          <div class="gradient-secondary h-32 rounded-box flex items-center justify-center text-white font-bold">
            Gradiente Secundário
          </div>
          <div class="gradient-accent h-32 rounded-box flex items-center justify-center text-white font-bold">
            Gradiente Acento
          </div>
        </Grid>
      </Section>
    </PageTransition>
  </Container>
</BaseLayout>

<script>
  // Script para atualizar os valores computados das cores
  document.addEventListener('astro:page-load', () => {
    const updateComputedColors = () => {
      document.querySelectorAll('[data-color-variable]').forEach(el => {
        const variable = el.getAttribute('data-color-variable');
        if (variable) {
          const computedValue = getComputedStyle(document.documentElement).getPropertyValue(variable);
          el.textContent = computedValue.trim();
        }
      });
    };
    
    updateComputedColors();
  });
</script>
