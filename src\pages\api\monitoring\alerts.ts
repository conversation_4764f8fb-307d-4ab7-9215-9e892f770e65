/**
 * API de Alertas de Monitoramento
 *
 * Este endpoint fornece acesso aos alertas ativos
 * gerados pelo sistema de monitoramento.
 */

import { isAdmin } from '@helpers/authGuard';
import { applicationMonitoringService } from '@services/applicationMonitoringService';
import type { APIRoute } from 'astro';

/**
 * Endpoint GET para obter alertas ativos
 */
export const GET: APIRoute = async ({ request, cookies }) => {
  try {
    // Verificar se o usuário é administrador
    const adminResult = await isAdmin({ request, cookies } as any);

    if (!adminResult) {
      return new Response(JSON.stringify({ error: 'Não autorizado' }), {
        status: 403,
        headers: {
          'Content-Type': 'application/json',
        },
      });
    }

    // Obter alertas ativos
    const alerts = applicationMonitoringService.getActiveAlerts();

    return new Response(
      JSON.stringify({
        success: true,
        data: alerts,
      }),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  } catch (error) {
    return new Response(
      JSON.stringify({
        success: false,
        error: error.message,
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  }
};
