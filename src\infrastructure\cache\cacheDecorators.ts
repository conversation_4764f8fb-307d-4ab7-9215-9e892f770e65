/**
 * Cache decorators
 * Provides decorators for caching function results
 */
import { getCache, setCache } from './cacheClient';

/**
 * Cache function result
 * @param keyPrefix Prefix for the cache key
 * @param ttl Time to live in seconds
 * @returns Decorator function
 */
export function Cached(keyPrefix: string, ttl = 3600) {
  return (target: any, propertyKey: string, descriptor: PropertyDescriptor) => {
    const originalMethod = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      // Generate cache key
      const cacheKey = `${keyPrefix}:${propertyKey}:${JSON.stringify(args)}`;

      // Try to get from cache
      const cachedResult = await getCache(cacheKey);

      if (cachedResult !== null) {
        return cachedResult;
      }

      // Call original method
      const result = await originalMethod.apply(this, args);

      // Cache result
      await setCache(cacheKey, result, ttl);

      return result;
    };

    return descriptor;
  };
}

/**
 * Cache function result with dynamic TTL
 * @param keyPrefix Prefix for the cache key
 * @param ttlFn Function to calculate TTL based on result
 * @returns Decorator function
 */
export function CachedWithDynamicTTL(keyPrefix: string, ttlFn: (result: any) => number) {
  return (target: any, propertyKey: string, descriptor: PropertyDescriptor) => {
    const originalMethod = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      // Generate cache key
      const cacheKey = `${keyPrefix}:${propertyKey}:${JSON.stringify(args)}`;

      // Try to get from cache
      const cachedResult = await getCache(cacheKey);

      if (cachedResult !== null) {
        return cachedResult;
      }

      // Call original method
      const result = await originalMethod.apply(this, args);

      // Calculate TTL based on result
      const ttl = ttlFn(result);

      // Cache result
      await setCache(cacheKey, result, ttl);

      return result;
    };

    return descriptor;
  };
}
