/**
 * Helpers de Animação - Estação da Alfabetização
 *
 * Este arquivo fornece utilitários para trabalhar com animações
 * usando AnimeJS de forma otimizada para o projeto.
 */

import { anime, animeHelpers } from './anime-config.js';

/**
 * Verifica se o usuário prefere movimento reduzido
 * @returns {boolean} Verdadeiro se o usuário prefere movimento reduzido
 */
export function prefersReducedMotion() {
  return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
}

/**
 * Executa uma animação apenas se o usuário não preferir movimento reduzido
 * @param {Function} animationFn - Função de animação a ser executada
 * @param {Function} fallbackFn - Função alternativa para quando movimento é reduzido
 * @returns {Object|null} Instância da animação ou null
 */
export function animateIfAllowed(animationFn, fallbackFn = null) {
  if (prefersReducedMotion()) {
    return fallbackFn ? fallbackFn() : null;
  }
  return animationFn();
}

/**
 * Configura animações para elementos que entram na viewport
 * @param {string} selector - Seletor CSS para os elementos
 * @param {Function} animationFn - Função de animação a ser aplicada
 * @param {Object} options - Opções adicionais
 */
export function setupScrollAnimations(selector, animationFn, options = {}) {
  // Se o usuário prefere movimento reduzido, não configurar animações
  if (prefersReducedMotion()) {
    // Tornar elementos visíveis imediatamente
    document.querySelectorAll(selector).forEach((el) => {
      el.style.opacity = 1;
    });
    return;
  }

  // Configurar o observador de interseção
  const observer = new IntersectionObserver(
    (entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          // Executar a animação quando o elemento entrar na viewport
          animationFn(entry.target);

          // Parar de observar o elemento após a animação (opcional)
          if (options.once !== false) {
            observer.unobserve(entry.target);
          }
        }
      });
    },
    {
      root: options.root || null,
      rootMargin: options.rootMargin || '0px',
      threshold: options.threshold || 0.1,
    }
  );

  // Observar todos os elementos que correspondem ao seletor
  document.querySelectorAll(selector).forEach((el) => {
    // Definir opacidade inicial como 0 para evitar flash
    el.style.opacity = 0;
    observer.observe(el);
  });
}

/**
 * Configura animações para elementos que são adicionados dinamicamente
 * @param {string} containerSelector - Seletor do contêiner onde elementos são adicionados
 * @param {string} elementSelector - Seletor dos elementos a serem animados
 * @param {Function} animationFn - Função de animação a ser aplicada
 */
export function setupDynamicAnimations(containerSelector, elementSelector, animationFn) {
  // Se o usuário prefere movimento reduzido, não configurar animações
  if (prefersReducedMotion()) return;

  // Configurar o observador de mutação
  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
        mutation.addedNodes.forEach((node) => {
          // Verificar se o nó é um elemento e corresponde ao seletor
          if (node.nodeType === Node.ELEMENT_NODE) {
            if (node.matches(elementSelector)) {
              // Animar o elemento adicionado
              animationFn(node);
            } else {
              // Verificar elementos filhos
              node.querySelectorAll(elementSelector).forEach((el) => {
                animationFn(el);
              });
            }
          }
        });
      }
    });
  });

  // Observar o contêiner para adições de elementos
  const container = document.querySelector(containerSelector);
  if (container) {
    observer.observe(container, { childList: true, subtree: true });
  }
}

/**
 * Animações educacionais específicas para alfabetização
 */
export const educationalAnimations = {
  /**
   * Anima uma letra com efeito de pop
   * @param {string|Element} target - Elemento alvo
   * @param {Object} options - Opções adicionais
   */
  letterPop: (target, options = {}) => {
    return anime({
      targets: target,
      scale: [1, 1.3, 1],
      duration: options.duration || 600,
      easing: 'easeInOutQuad',
      ...options,
    });
  },

  /**
   * Anima um número com efeito de rotação
   * @param {string|Element} target - Elemento alvo
   * @param {Object} options - Opções adicionais
   */
  numberSpin: (target, options = {}) => {
    return anime({
      targets: target,
      rotateY: [0, 360],
      duration: options.duration || 800,
      easing: 'easeInOutSine',
      ...options,
    });
  },

  /**
   * Anima uma palavra letra por letra
   * @param {string|Element} target - Elemento alvo
   * @param {Object} options - Opções adicionais
   */
  wordAnimation: (target, options = {}) => {
    const element = typeof target === 'string' ? document.querySelector(target) : target;
    if (!element) return;

    const text = element.textContent;
    element.innerHTML = '';

    // Criar spans para cada letra
    for (let i = 0; i < text.length; i++) {
      const span = document.createElement('span');
      span.textContent = text[i] === ' ' ? '\u00A0' : text[i];
      span.style.display = 'inline-block';
      span.style.opacity = 0;
      element.appendChild(span);
    }

    // Animar cada letra
    return anime({
      targets: element.querySelectorAll('span'),
      opacity: [0, 1],
      translateY: [20, 0],
      delay: anime.stagger(60),
      duration: options.duration || 800,
      easing: options.easing || 'easeOutExpo',
      ...options,
    });
  },

  /**
   * Anima um elemento com efeito de escrita
   * @param {string|Element} target - Elemento alvo
   * @param {Object} options - Opções adicionais
   */
  writingEffect: (target, options = {}) => {
    const element = typeof target === 'string' ? document.querySelector(target) : target;
    if (!element) return;

    // Configurar elemento para animação
    const text = element.textContent;
    element.textContent = '';
    element.style.whiteSpace = 'pre';

    let i = 0;
    const speed = options.speed || 50;

    // Função para adicionar caracteres um por um
    function typeWriter() {
      if (i < text.length) {
        element.textContent += text.charAt(i);
        i++;
        setTimeout(typeWriter, speed);
      }
    }

    // Iniciar a animação
    typeWriter();
  },

  /**
   * Anima um trem se movendo
   * @param {string|Element} target - Elemento alvo
   * @param {Object} options - Opções adicionais
   */
  trainAnimation: (target, options = {}) => {
    return anime({
      targets: target,
      translateX: ['100%', '-100%'],
      duration: options.duration || 10000,
      easing: 'linear',
      loop: options.loop !== undefined ? options.loop : true,
      ...options,
    });
  },
};

// Exportar os helpers do anime-config para facilitar o uso
export { animeHelpers };
