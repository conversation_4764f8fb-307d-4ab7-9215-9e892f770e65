/**
 * PostgreSQL Admin User Repository
 *
 * Implementação do repositório de usuários administradores usando PostgreSQL.
 * Parte da implementação da tarefa 8.8.2 - Gestão de usuários
 */

import { Pool } from 'pg';
import { AdminPermission, AdminRole, AdminUser } from '../../../domain/entities/AdminUser';
import {
  AdminUserFilter,
  AdminUserPaginationOptions,
  AdminUserRepository,
  AdminUserSortOptions,
  PaginatedAdminUsers,
} from '../../../domain/repositories/AdminUserRepository';
import { getDbConnection } from '../connection';

export class PostgresAdminUserRepository implements AdminUserRepository {
  private pool: Pool;

  constructor() {
    this.pool = getDbConnection();
  }

  /**
   * Cria um novo usuário administrador
   */
  async create(user: AdminUser): Promise<AdminUser> {
    const client = await this.pool.connect();

    try {
      await client.query('BEGIN');

      // Inserir usuário
      const userQuery = `
        INSERT INTO admin_users (
          id, username, password_hash, role, is_active, last_login, created_at, updated_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
        RETURNING *
      `;

      const userValues = [
        user.id,
        user.username,
        user.passwordHash,
        user.role,
        user.isActive,
        user.lastLogin,
        user.createdAt,
        user.updatedAt,
      ];

      const userResult = await client.query(userQuery, userValues);

      // Inserir perfil
      const profileQuery = `
        INSERT INTO admin_user_profiles (
          user_id, full_name, email, phone, avatar, department, position, bio
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
        RETURNING *
      `;

      const profileValues = [
        user.id,
        user.profile.fullName,
        user.profile.email,
        user.profile.phone || null,
        user.profile.avatar || null,
        user.profile.department || null,
        user.profile.position || null,
        user.profile.bio || null,
      ];

      const profileResult = await client.query(profileQuery, profileValues);

      // Inserir permissões
      for (const permission of user.permissions) {
        const permissionQuery = `
          INSERT INTO admin_user_permissions (user_id, permission)
          VALUES ($1, $2)
        `;

        await client.query(permissionQuery, [user.id, permission]);
      }

      await client.query('COMMIT');

      // Mapear resultado
      return this.mapUserFromDb(userResult.rows[0], profileResult.rows[0], user.permissions);
    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }
  }

  /**
   * Atualiza um usuário administrador existente
   */
  async update(user: AdminUser): Promise<AdminUser> {
    const client = await this.pool.connect();

    try {
      await client.query('BEGIN');

      // Atualizar usuário
      const userQuery = `
        UPDATE admin_users
        SET username = $1, role = $2, is_active = $3, updated_at = $4
        WHERE id = $5
        RETURNING *
      `;

      const userValues = [user.username, user.role, user.isActive, new Date(), user.id];

      const userResult = await client.query(userQuery, userValues);

      // Atualizar perfil
      const profileQuery = `
        UPDATE admin_user_profiles
        SET full_name = $1, email = $2, phone = $3, avatar = $4, department = $5, position = $6, bio = $7
        WHERE user_id = $8
        RETURNING *
      `;

      const profileValues = [
        user.profile.fullName,
        user.profile.email,
        user.profile.phone || null,
        user.profile.avatar || null,
        user.profile.department || null,
        user.profile.position || null,
        user.profile.bio || null,
        user.id,
      ];

      const profileResult = await client.query(profileQuery, profileValues);

      // Atualizar permissões
      await client.query('DELETE FROM admin_user_permissions WHERE user_id = $1', [user.id]);

      for (const permission of user.permissions) {
        const permissionQuery = `
          INSERT INTO admin_user_permissions (user_id, permission)
          VALUES ($1, $2)
        `;

        await client.query(permissionQuery, [user.id, permission]);
      }

      await client.query('COMMIT');

      // Mapear resultado
      return this.mapUserFromDb(userResult.rows[0], profileResult.rows[0], user.permissions);
    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }
  }

  /**
   * Obtém um usuário administrador pelo ID
   */
  async getById(id: string): Promise<AdminUser | null> {
    try {
      // Obter usuário
      const userQuery = `
        SELECT * FROM admin_users
        WHERE id = $1
      `;

      const userResult = await this.pool.query(userQuery, [id]);

      if (userResult.rows.length === 0) {
        return null;
      }

      // Obter perfil
      const profileQuery = `
        SELECT * FROM admin_user_profiles
        WHERE user_id = $1
      `;

      const profileResult = await this.pool.query(profileQuery, [id]);

      // Obter permissões
      const permissionsQuery = `
        SELECT permission FROM admin_user_permissions
        WHERE user_id = $1
      `;

      const permissionsResult = await this.pool.query(permissionsQuery, [id]);

      const permissions = permissionsResult.rows.map((row) => row.permission as AdminPermission);

      // Mapear resultado
      return this.mapUserFromDb(userResult.rows[0], profileResult.rows[0], permissions);
    } catch (error) {
      console.error('Erro ao obter usuário administrador por ID:', error);
      return null;
    }
  }

  /**
   * Obtém um usuário administrador pelo nome de usuário
   */
  async getByUsername(username: string): Promise<AdminUser | null> {
    try {
      // Obter usuário
      const userQuery = `
        SELECT * FROM admin_users
        WHERE username = $1
      `;

      const userResult = await this.pool.query(userQuery, [username]);

      if (userResult.rows.length === 0) {
        return null;
      }

      const userId = userResult.rows[0].id;

      // Obter perfil
      const profileQuery = `
        SELECT * FROM admin_user_profiles
        WHERE user_id = $1
      `;

      const profileResult = await this.pool.query(profileQuery, [userId]);

      // Obter permissões
      const permissionsQuery = `
        SELECT permission FROM admin_user_permissions
        WHERE user_id = $1
      `;

      const permissionsResult = await this.pool.query(permissionsQuery, [userId]);

      const permissions = permissionsResult.rows.map((row) => row.permission as AdminPermission);

      // Mapear resultado
      return this.mapUserFromDb(userResult.rows[0], profileResult.rows[0], permissions);
    } catch (error) {
      console.error('Erro ao obter usuário administrador por nome de usuário:', error);
      return null;
    }
  }

  /**
   * Obtém um usuário administrador pelo email
   */
  async getByEmail(email: string): Promise<AdminUser | null> {
    try {
      // Obter perfil
      const profileQuery = `
        SELECT * FROM admin_user_profiles
        WHERE email = $1
      `;

      const profileResult = await this.pool.query(profileQuery, [email]);

      if (profileResult.rows.length === 0) {
        return null;
      }

      const userId = profileResult.rows[0].user_id;

      // Obter usuário
      const userQuery = `
        SELECT * FROM admin_users
        WHERE id = $1
      `;

      const userResult = await this.pool.query(userQuery, [userId]);

      // Obter permissões
      const permissionsQuery = `
        SELECT permission FROM admin_user_permissions
        WHERE user_id = $1
      `;

      const permissionsResult = await this.pool.query(permissionsQuery, [userId]);

      const permissions = permissionsResult.rows.map((row) => row.permission as AdminPermission);

      // Mapear resultado
      return this.mapUserFromDb(userResult.rows[0], profileResult.rows[0], permissions);
    } catch (error) {
      console.error('Erro ao obter usuário administrador por email:', error);
      return null;
    }
  }

  /**
   * Busca usuários administradores com filtros, ordenação e paginação
   */
  async find(
    filter: AdminUserFilter,
    sort?: AdminUserSortOptions,
    pagination?: AdminUserPaginationOptions
  ): Promise<PaginatedAdminUsers> {
    try {
      // Construir consulta base
      let query = `
        SELECT u.*, p.*
        FROM admin_users u
        JOIN admin_user_profiles p ON u.id = p.user_id
      `;

      // Adicionar condições de filtro
      const conditions: string[] = [];
      const values: any[] = [];
      let paramIndex = 1;

      if (filter.ids && filter.ids.length > 0) {
        conditions.push(`u.id = ANY($${paramIndex})`);
        values.push(filter.ids);
        paramIndex++;
      }

      if (filter.username) {
        conditions.push(`u.username ILIKE $${paramIndex}`);
        values.push(`%${filter.username}%`);
        paramIndex++;
      }

      if (filter.role) {
        if (Array.isArray(filter.role)) {
          conditions.push(`u.role = ANY($${paramIndex})`);
          values.push(filter.role);
        } else {
          conditions.push(`u.role = $${paramIndex}`);
          values.push(filter.role);
        }
        paramIndex++;
      }

      if (filter.isActive !== undefined) {
        conditions.push(`u.is_active = $${paramIndex}`);
        values.push(filter.isActive);
        paramIndex++;
      }

      if (filter.email) {
        conditions.push(`p.email ILIKE $${paramIndex}`);
        values.push(`%${filter.email}%`);
        paramIndex++;
      }

      if (filter.department) {
        conditions.push(`p.department ILIKE $${paramIndex}`);
        values.push(`%${filter.department}%`);
        paramIndex++;
      }

      if (conditions.length > 0) {
        query += ` WHERE ${conditions.join(' AND ')}`;
      }

      // Adicionar ordenação
      if (sort) {
        const sortField = this.getSortField(sort.field);
        const sortDirection = sort.direction.toUpperCase();

        query += ` ORDER BY ${sortField} ${sortDirection}`;
      } else {
        query += ' ORDER BY u.created_at DESC';
      }

      // Obter contagem total
      const countQuery = `
        SELECT COUNT(*)
        FROM admin_users u
        JOIN admin_user_profiles p ON u.id = p.user_id
        ${conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : ''}
      `;

      const countResult = await this.pool.query(countQuery, values);
      const total = Number.parseInt(countResult.rows[0].count);

      // Adicionar paginação
      if (pagination) {
        const offset = (pagination.page - 1) * pagination.limit;

        query += ` LIMIT $${paramIndex} OFFSET $${paramIndex + 1}`;
        values.push(pagination.limit, offset);
      }

      // Executar consulta
      const result = await this.pool.query(query, values);

      // Obter permissões para cada usuário
      const users: AdminUser[] = [];

      for (const row of result.rows) {
        const permissionsQuery = `
          SELECT permission FROM admin_user_permissions
          WHERE user_id = $1
        `;

        const permissionsResult = await this.pool.query(permissionsQuery, [row.id]);
        const permissions = permissionsResult.rows.map(
          (permRow) => permRow.permission as AdminPermission
        );

        users.push(this.mapUserFromDb(row, row, permissions));
      }

      // Calcular total de páginas
      const totalPages = pagination ? Math.ceil(total / pagination.limit) : 1;

      return {
        users,
        total,
        page: pagination?.page || 1,
        limit: pagination?.limit || users.length,
        totalPages,
      };
    } catch (error) {
      console.error('Erro ao buscar usuários administradores:', error);

      return {
        users: [],
        total: 0,
        page: 1,
        limit: 10,
        totalPages: 0,
      };
    }
  }

  /**
   * Obtém usuários administradores por função
   */
  async getByRole(
    role: AdminRole,
    pagination?: AdminUserPaginationOptions
  ): Promise<PaginatedAdminUsers> {
    return this.find({ role }, { field: 'createdAt', direction: 'desc' }, pagination);
  }

  /**
   * Obtém usuários administradores ativos
   */
  async getActive(pagination?: AdminUserPaginationOptions): Promise<PaginatedAdminUsers> {
    return this.find({ isActive: true }, { field: 'createdAt', direction: 'desc' }, pagination);
  }

  /**
   * Obtém usuários administradores inativos
   */
  async getInactive(pagination?: AdminUserPaginationOptions): Promise<PaginatedAdminUsers> {
    return this.find({ isActive: false }, { field: 'createdAt', direction: 'desc' }, pagination);
  }

  /**
   * Ativa um usuário administrador
   */
  async activate(id: string): Promise<boolean> {
    try {
      const query = `
        UPDATE admin_users
        SET is_active = true, updated_at = $1
        WHERE id = $2
      `;

      const result = await this.pool.query(query, [new Date(), id]);

      return result.rowCount > 0;
    } catch (error) {
      console.error('Erro ao ativar usuário administrador:', error);
      return false;
    }
  }

  /**
   * Desativa um usuário administrador
   */
  async deactivate(id: string): Promise<boolean> {
    try {
      const query = `
        UPDATE admin_users
        SET is_active = false, updated_at = $1
        WHERE id = $2
      `;

      const result = await this.pool.query(query, [new Date(), id]);

      return result.rowCount > 0;
    } catch (error) {
      console.error('Erro ao desativar usuário administrador:', error);
      return false;
    }
  }

  /**
   * Atualiza a senha de um usuário administrador
   */
  async updatePassword(id: string, passwordHash: string): Promise<boolean> {
    try {
      const query = `
        UPDATE admin_users
        SET password_hash = $1, updated_at = $2
        WHERE id = $3
      `;

      const result = await this.pool.query(query, [passwordHash, new Date(), id]);

      return result.rowCount > 0;
    } catch (error) {
      console.error('Erro ao atualizar senha de usuário administrador:', error);
      return false;
    }
  }

  /**
   * Registra um login de usuário administrador
   */
  async registerLogin(id: string): Promise<boolean> {
    try {
      const query = `
        UPDATE admin_users
        SET last_login = $1, updated_at = $2
        WHERE id = $3
      `;

      const result = await this.pool.query(query, [new Date(), new Date(), id]);

      return result.rowCount > 0;
    } catch (error) {
      console.error('Erro ao registrar login de usuário administrador:', error);
      return false;
    }
  }

  /**
   * Exclui um usuário administrador
   */
  async delete(id: string): Promise<boolean> {
    const client = await this.pool.connect();

    try {
      await client.query('BEGIN');

      // Excluir permissões
      await client.query('DELETE FROM admin_user_permissions WHERE user_id = $1', [id]);

      // Excluir perfil
      await client.query('DELETE FROM admin_user_profiles WHERE user_id = $1', [id]);

      // Excluir usuário
      const result = await client.query('DELETE FROM admin_users WHERE id = $1', [id]);

      await client.query('COMMIT');

      return result.rowCount > 0;
    } catch (error) {
      await client.query('ROLLBACK');
      console.error('Erro ao excluir usuário administrador:', error);
      return false;
    } finally {
      client.release();
    }
  }

  /**
   * Verifica se um nome de usuário já existe
   */
  async usernameExists(username: string, excludeId?: string): Promise<boolean> {
    try {
      let query = `
        SELECT COUNT(*) FROM admin_users
        WHERE username = $1
      `;

      const values: any[] = [username];

      if (excludeId) {
        query += ' AND id != $2';
        values.push(excludeId);
      }

      const result = await this.pool.query(query, values);

      return Number.parseInt(result.rows[0].count) > 0;
    } catch (error) {
      console.error('Erro ao verificar existência de nome de usuário:', error);
      return false;
    }
  }

  /**
   * Verifica se um email já existe
   */
  async emailExists(email: string, excludeId?: string): Promise<boolean> {
    try {
      let query = `
        SELECT COUNT(*) FROM admin_user_profiles
        WHERE email = $1
      `;

      const values: any[] = [email];

      if (excludeId) {
        query += ' AND user_id != $2';
        values.push(excludeId);
      }

      const result = await this.pool.query(query, values);

      return Number.parseInt(result.rows[0].count) > 0;
    } catch (error) {
      console.error('Erro ao verificar existência de email:', error);
      return false;
    }
  }

  /**
   * Mapeia um usuário do banco de dados para a entidade
   */
  private mapUserFromDb(userRow: any, profileRow: any, permissions: AdminPermission[]): AdminUser {
    return new AdminUser({
      id: userRow.id,
      username: userRow.username,
      passwordHash: userRow.password_hash,
      role: userRow.role as AdminRole,
      permissions,
      profile: {
        fullName: profileRow.full_name,
        email: profileRow.email,
        phone: profileRow.phone,
        avatar: profileRow.avatar,
        department: profileRow.department,
        position: profileRow.position,
        bio: profileRow.bio,
      },
      isActive: userRow.is_active,
      lastLogin: userRow.last_login,
      createdAt: userRow.created_at,
      updatedAt: userRow.updated_at,
    });
  }

  /**
   * Obtém o campo de ordenação para a consulta SQL
   */
  private getSortField(field: string): string {
    switch (field) {
      case 'username':
        return 'u.username';
      case 'fullName':
        return 'p.full_name';
      case 'email':
        return 'p.email';
      case 'role':
        return 'u.role';
      case 'lastLogin':
        return 'u.last_login';
      case 'createdAt':
        return 'u.created_at';
      default:
        return 'u.created_at';
    }
  }
}
