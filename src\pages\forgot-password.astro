---
import { actions } from '@actions';
import SignContainer from '@components/auth/SignContainer.astro';
import MainHeaderSign from '@components/header/MainHeaderSign.astro';
import { csrfHelper } from '@helpers/csrfHelper';
import BaseLayout from '@layouts/BaseLayout.astro';

// Obter resultado da ação (se houver)
const result = Astro.getActionResult(actions.authAction.forgotPassword);

// Gerar token CSRF
const csrfToken = await csrfHelper.generateToken(Astro);

// Verificar se o usuário já está autenticado
const isAuthenticated = await Astro.session.get('isAuthenticated');
if (isAuthenticated) {
  return Astro.redirect('/dashboard');
}
---

<BaseLayout title="Recuperação de Senha">
  <MainHeaderSign />
  
  <SignContainer title="Recuperação de Senha">
    <div class="text-sm mb-4">
      Digite seu email para receber instruções de recuperação de senha.
    </div>
    
    {result?.success && (
      <div class="alert alert-success mb-4">
        <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
        <span>Email enviado com sucesso! Verifique sua caixa de entrada.</span>
      </div>
    )}
    
    {result?.error && (
      <div class="alert alert-error mb-4">
        <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
        <span>{result.error}</span>
      </div>
    )}
    
    <form id="forgot-password-form" method="POST" action={actions.authAction.forgotPassword} class="space-y-4">
      <!-- Token CSRF -->
      <input type="hidden" name="csrf_token" value={csrfToken} />
      
      <!-- Email -->
      <div class="form-control">
        <label class="label">
          <span class="label-text">Email</span>
        </label>
        <input 
          type="email" 
          name="email" 
          id="email"
          class="input input-bordered input-sm" 
          placeholder="Digite seu email"
          required
        />
        <div class="label">
          <span class="label-text-alt text-error hidden" id="email-error"></span>
        </div>
      </div>
      
      <!-- Botões de Ação -->
      <div class="flex justify-between mt-6">
        <a href="/signin" class="btn btn-outline btn-sm">Voltar ao Login</a>
        <button type="submit" class="btn btn-primary btn-sm">Enviar</button>
      </div>
    </form>
  </SignContainer>
</BaseLayout>

<script>
  // Função para validar email
  function validateEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }
  
  // Função para mostrar erro
  function showError(elementId, message) {
    const errorElement = document.getElementById(`${elementId}-error`);
    if (errorElement) {
      errorElement.textContent = message;
      errorElement.classList.remove('hidden');
      document.getElementById(elementId).classList.add('input-error');
    }
  }
  
  // Função para limpar erro
  function clearError(elementId) {
    const errorElement = document.getElementById(`${elementId}-error`);
    if (errorElement) {
      errorElement.textContent = '';
      errorElement.classList.add('hidden');
      document.getElementById(elementId).classList.remove('input-error');
    }
  }
  
  // Inicializar quando o documento estiver pronto
  document.addEventListener('DOMContentLoaded', () => {
    const form = document.getElementById('forgot-password-form');
    const emailInput = document.getElementById('email');
    
    // Validar formulário antes de enviar
    form.addEventListener('submit', (event) => {
      let isValid = true;
      
      // Validar email
      if (!validateEmail(emailInput.value)) {
        showError('email', 'Email inválido');
        isValid = false;
      } else {
        clearError('email');
      }
      
      if (!isValid) {
        event.preventDefault();
      }
    });
    
    // Limpar erros ao digitar
    emailInput.addEventListener('input', () => {
      clearError('email');
    });
  });
</script>
