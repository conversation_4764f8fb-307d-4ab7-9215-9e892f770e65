---
/**
 * Página de detalhes de conteúdo educacional
 *
 * Esta página implementa a visualização de detalhes de um conteúdo educacional,
 * seguindo a arquitetura Zero-JS e utilizando On-Demand Rendering (ODR).
 *
 * O ODR permite que a página seja renderizada sob demanda e armazenada em cache,
 * melhorando a performance e reduzindo a carga no servidor.
 */

import MainNavigation from '../../components/navigation/MainNavigation.astro';
import StaticFragment from '../../components/performance/StaticFragment.astro';
import StaticTabs from '../../components/ui/StaticTabs.astro';
import { ContentType } from '../../domain/value-objects/ContentType';
import BaseLayout from '../../layouts/BaseLayout.astro';
import { getContentById, getContentByType } from '../../services/DataService';

// Configuração de ODR para esta página
export const prerender = false; // Desabilitar pré-renderização estática

// Configuração de cache para esta página
export const cacheControl = {
  maxAge: 3600, // 1 hora
  staleWhileRevalidate: 300, // 5 minutos
  public: true,
};

// Obter ID do conteúdo da URL
const { id } = Astro.params;

// Obter conteúdo pelo ID
const content = await getContentById(id || '');

// Se o conteúdo não for encontrado, redirecionar para página 404
if (!content) {
  return Astro.redirect('/404');
}

// Obter conteúdos relacionados
const relatedContents = await getContentByType(content.type, 3);

// Filtrar o conteúdo atual dos relacionados
const filteredRelatedContents = relatedContents.filter((item) => item.id !== content.id);

// Tipos de conteúdo para exibição
const contentTypeLabels = {
  [ContentType.ATIVIDADE]: 'Atividade',
  [ContentType.JOGO]: 'Jogo',
  [ContentType.MATERIAL]: 'Material',
  [ContentType.VIDEO]: 'Vídeo',
  [ContentType.AUDIO]: 'Áudio',
};

// Configuração de fragmentos estáticos
const staticFragmentConfig = {
  // Versão global dos fragmentos estáticos (incrementar para invalidar todos)
  version: '1.0',

  // TTLs específicos por fragmento
  ttl: {
    header: 86400, // 24 horas
    navigation: 86400, // 24 horas
    footer: 86400, // 24 horas
    contentHeader: 3600, // 1 hora
    relatedContent: 1800, // 30 minutos
  },

  // Tags para categorização e invalidação seletiva
  tags: {
    header: ['layout', 'global'],
    navigation: ['layout', 'global', 'navigation'],
    footer: ['layout', 'global'],
    contentHeader: ['content', `type-${content.type}`],
    relatedContent: ['content', `type-${content.type}`],
  },
};

// Adicionar headers de cache específicos para esta página
Astro.response.headers.set('X-Content-Type', content.type);
Astro.response.headers.set('X-Content-ID', content.id);
Astro.response.headers.set(
  'X-Last-Modified',
  content.updatedAt?.toISOString() || content.createdAt.toISOString()
);
Astro.response.headers.set('X-Static-Fragments', 'enabled');

// Formatar data de publicação
const formattedDate = new Intl.DateTimeFormat('pt-BR', {
  day: '2-digit',
  month: '2-digit',
  year: 'numeric',
}).format(content.publishedAt || content.createdAt);

// Configurar abas de conteúdo
const tabs = [
  {
    id: 'descricao',
    label: 'Descrição',
    content: content.description,
    isOpen: true,
  },
  {
    id: 'conteudo',
    label: 'Conteúdo',
    content: content.content,
    isOpen: false,
  },
  {
    id: 'como-usar',
    label: 'Como Usar',
    content: `
      <h3>Instruções de Uso</h3>
      <p>Este conteúdo é destinado para crianças de ${content.ageRange[0]} a ${content.ageRange[1]} anos.</p>
      <p>Recomendamos que um adulto acompanhe a criança durante a atividade para melhor aproveitamento.</p>

      <h4>Materiais Necessários</h4>
      <ul>
        <li>Impressora (para materiais impressos)</li>
        <li>Lápis de cor ou canetinhas</li>
        <li>Tesoura sem ponta</li>
        <li>Cola</li>
      </ul>

      <h4>Tempo Estimado</h4>
      <p>30 a 45 minutos</p>
    `,
    isOpen: false,
  },
];
---

<BaseLayout title={`${content.title} - Estação Alfabetização`} description={content.description}>
  <StaticFragment
    id="navigation"
    ttl={staticFragmentConfig.ttl.navigation}
    version={staticFragmentConfig.version}
    tags={staticFragmentConfig.tags.navigation}
  >
    <MainNavigation currentPath="/conteudos" />
  </StaticFragment>

  <main class="content-detail-page">
    <div class="container">
      <div class="content-breadcrumb">
        <a href="/">Início</a> &gt;
        <a href="/conteudos">Conteúdos</a> &gt;
        <a href={`/conteudos?tipo=${content.type}`}>{contentTypeLabels[content.type]}</a> &gt;
        <span>{content.title}</span>
      </div>

      <div class="content-layout">
        <div class="content-main">
          <StaticFragment
            id={`content-header-${content.id}`}
            ttl={staticFragmentConfig.ttl.contentHeader}
            version={staticFragmentConfig.version}
            tags={staticFragmentConfig.tags.contentHeader}
          >
            <div class="content-header">
              <span class="content-type">{contentTypeLabels[content.type]}</span>
              <h1>{content.title}</h1>
              <div class="content-meta">
                <span class="content-date">Publicado em: {formattedDate}</span>
                <span class="content-age">Idade: {content.ageRange[0]}-{content.ageRange[1]} anos</span>
              </div>
            </div>
          </StaticFragment>

          <div class="content-featured-image">
            <img
              src={content.featuredImageUrl || '/images/placeholder.jpg'}
              alt={content.title}
              width="800"
              height="450"
            >
          </div>

          <div class="content-tabs">
            <StaticTabs tabs={tabs} />
          </div>

          <div class="content-tags">
            <h3>Tags</h3>
            <div class="tags-list">
              {content.tags.map((tag: string) => (
                <a href={`/conteudos?tag=${tag}`} class="tag">{tag}</a>
              ))}
            </div>
          </div>

          <div class="content-actions">
            <a href="#" class="btn btn-primary">
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon">
                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                <polyline points="7 10 12 15 17 10"></polyline>
                <line x1="12" y1="15" x2="12" y2="3"></line>
              </svg>
              Baixar Material
            </a>

            <a href="#" class="btn btn-outline">
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon">
                <path d="M17 3a2.828 2.828 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5L17 3z"></path>
              </svg>
              Imprimir
            </a>

            <a href="#" class="btn btn-outline">
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon">
                <path d="M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8"></path>
                <polyline points="16 6 12 2 8 6"></polyline>
                <line x1="12" y1="2" x2="12" y2="15"></line>
              </svg>
              Compartilhar
            </a>
          </div>
        </div>

        <aside class="content-sidebar">
          <StaticFragment
            id={`related-content-${content.type}-${content.id}`}
            ttl={staticFragmentConfig.ttl.relatedContent}
            version={staticFragmentConfig.version}
            tags={staticFragmentConfig.tags.relatedContent}
          >
            <div class="sidebar-section">
              <h2>Conteúdos Relacionados</h2>

              <div class="related-contents">
                {filteredRelatedContents.map(relatedContent => (
                  <a href={`/conteudos/${relatedContent.id}`} class="related-content-card">
                    <div class="related-content-image">
                      <img
                        src={relatedContent.featuredImageUrl || '/images/placeholder.jpg'}
                        alt={relatedContent.title}
                        width="100"
                        height="70"
                        loading="lazy"
                      >
                    </div>
                    <div class="related-content-info">
                      <h3>{relatedContent.title}</h3>
                      <span class="related-content-type">{contentTypeLabels[relatedContent.type]}</span>
                    </div>
                  </a>
                ))}
              </div>

              <a href={`/conteudos?tipo=${content.type}`} class="btn btn-outline btn-sm view-more">
                Ver mais {contentTypeLabels[content.type].toLowerCase()}s
              </a>
            </div>
          </StaticFragment>

          <div class="sidebar-section">
            <h2>Faixa Etária</h2>
            <p>Este conteúdo é recomendado para crianças de {content.ageRange[0]} a {content.ageRange[1]} anos.</p>
            <a href={`/conteudos?idade_min=${content.ageRange[0]}&idade_max=${content.ageRange[1]}`} class="btn btn-outline btn-sm">
              Ver mais para esta idade
            </a>
          </div>
        </aside>
      </div>
    </div>
  </main>
</BaseLayout>

<style>
  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
  }

  .content-detail-page {
    padding: 2rem 0;
  }

  .content-breadcrumb {
    margin-bottom: 2rem;
    font-size: 0.875rem;
    color: var(--color-text-light, #6b7280);
  }

  .content-breadcrumb a {
    color: var(--color-text-light, #6b7280);
    text-decoration: none;
    margin: 0 0.5rem;
  }

  .content-breadcrumb a:first-child {
    margin-left: 0;
  }

  .content-breadcrumb a:hover {
    color: var(--color-primary, #4a6cf7);
  }

  .content-layout {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 2rem;
    margin-bottom: 3rem;
  }

  .content-main {
    background-color: white;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    padding: 2rem;
  }

  .content-header {
    margin-bottom: 1.5rem;
  }

  .content-type {
    display: inline-block;
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--color-primary, #4a6cf7);
    background-color: var(--color-primary-light, #f0f4ff);
    padding: 0.25rem 0.75rem;
    border-radius: 0.25rem;
    margin-bottom: 1rem;
  }

  .content-header h1 {
    font-size: 2rem;
    color: var(--color-text-dark, #111827);
    margin-bottom: 1rem;
  }

  .content-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    font-size: 0.875rem;
    color: var(--color-text-light, #6b7280);
  }

  .content-featured-image {
    margin-bottom: 2rem;
    border-radius: 0.5rem;
    overflow: hidden;
  }

  .content-featured-image img {
    width: 100%;
    height: auto;
    display: block;
  }

  .content-tabs {
    margin-bottom: 2rem;
  }

  .content-tags {
    margin-bottom: 2rem;
  }

  .content-tags h3 {
    font-size: 1.125rem;
    color: var(--color-text-dark, #111827);
    margin-bottom: 1rem;
  }

  .tags-list {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  .tag {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    background-color: var(--color-bg-light, #f3f4f6);
    color: var(--color-text, #374151);
    border-radius: 0.25rem;
    font-size: 0.75rem;
    text-decoration: none;
    transition: background-color 0.2s;
  }

  .tag:hover {
    background-color: var(--color-bg, #e5e7eb);
  }

  .content-actions {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid var(--color-border, #e5e7eb);
  }

  .content-sidebar {
    align-self: start;
  }

  .sidebar-section {
    background-color: white;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
  }

  .sidebar-section h2 {
    font-size: 1.25rem;
    color: var(--color-text-dark, #111827);
    margin-bottom: 1.25rem;
  }

  .related-contents {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 1.25rem;
  }

  .related-content-card {
    display: flex;
    gap: 1rem;
    text-decoration: none;
    color: inherit;
    transition: transform 0.2s;
  }

  .related-content-card:hover {
    transform: translateX(4px);
  }

  .related-content-image {
    width: 80px;
    height: 60px;
    border-radius: 0.25rem;
    overflow: hidden;
    flex-shrink: 0;
  }

  .related-content-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .related-content-info h3 {
    font-size: 0.875rem;
    color: var(--color-text-dark, #111827);
    margin-bottom: 0.25rem;
  }

  .related-content-type {
    font-size: 0.75rem;
    color: var(--color-text-light, #6b7280);
  }

  .view-more {
    width: 100%;
    text-align: center;
  }

  .btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.625rem 1.25rem;
    font-weight: 500;
    border-radius: 0.375rem;
    border: none;
    cursor: pointer;
    transition: all 0.2s;
    font-size: 1rem;
    line-height: 1.5;
    text-decoration: none;
  }

  .btn-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
  }

  .btn-primary {
    background-color: var(--color-primary, #4a6cf7);
    color: white;
  }

  .btn-primary:hover {
    background-color: var(--color-primary-dark, #3b5bd9);
  }

  .btn-outline {
    background-color: transparent;
    border: 1px solid var(--color-border, #e5e7eb);
    color: var(--color-text, #374151);
  }

  .btn-outline:hover {
    background-color: var(--color-bg-light, #f3f4f6);
  }

  .icon {
    flex-shrink: 0;
  }

  @media (max-width: 768px) {
    .content-layout {
      grid-template-columns: 1fr;
    }

    .content-header h1 {
      font-size: 1.75rem;
    }

    .content-actions {
      flex-direction: column;
    }

    .btn {
      width: 100%;
    }
  }

  @media (prefers-reduced-motion: reduce) {
    .btn,
    .tag,
    .related-content-card {
      transition: none;
    }
  }
</style>
