import type { QueryResult } from 'pg';
import { pgH<PERSON>per } from './pgHelper';

async function create(type: string): Promise<QueryResult> {
  const result = await read(type);

  if (result.rows.length > 0) {
    return result;
  }

  return await pgHelper.query(
    `INSERT INTO tab_school_type (
      ulid_school_type, 
      type) 
     VALUES ($1, $2) 
     RETURNING *`,
    [pgHelper.generateULID(), type]
  );
}

async function read(ulid_school_type?: string, type?: string, active = true): Promise<QueryResult> {
  return await pgHelper.query(
    `SELECT * 
       FROM tab_school_type 
      WHERE active = $1 
     ${ulid_school_type ? 'AND ulid_school_type = $2' : ''}
     ${type ? 'AND type ILIKE "%$2%" ' : ''}
     ORDER BY type ASC`,
    [active, ulid_school_type, type]
  );
}

async function update(
  ulid_school_type: string,
  active: boolean,
  type: string
): Promise<QueryResult> {
  return await pgHelper.query(
    `UPDATE tab_school_type 
        SET active     = $2,
            type       = $3, 
            updated_at = CURRENT_TIMESTAMP 
      WHERE ulid_school_type = $1`,
    [ulid_school_type, active, type]
  );
}

async function deleteByUlid(ulid_school_type: string): Promise<QueryResult> {
  return await pgHelper.query(
    `DELETE FROM tab_school_type 
      WHERE ulid_school_type = $1 
     RETURNING *`,
    [ulid_school_type]
  );
}

async function inactivate(ulid_school_type: string): Promise<QueryResult> {
  return await pgHelper.query(
    `UPDATE tab_school_type 
        SET active = false, 
            updated_at = CURRENT_TIMESTAMP 
      WHERE ulid_school_type = $1 
     RETURNING *`,
    [ulid_school_type]
  );
}

export const schoolTypeRepository = {
  create,
  read,
  update,
  deleteByUlid,
  inactivate,
};
