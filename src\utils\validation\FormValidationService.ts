/**
 * Serviço de validação de formulário
 *
 * Este serviço fornece funcionalidades para validar dados de formulário
 * usando validadores registrados.
 */

import { formValidatorRegistry } from './FormValidator';
import './validators'; // Importar para garantir que os validadores padrão sejam registrados

/**
 * Tipo para regras de validação de campo
 */
export type FieldValidationRule = {
  /**
   * Nome do validador
   */
  validator: string;

  /**
   * Opções para o validador
   */
  options?: Record<string, unknown>;

  /**
   * Mensagem de erro personalizada
   */
  message?: string;
};

/**
 * Tipo para esquema de validação de formulário
 */
export type FormValidationSchema = Record<string, FieldValidationRule[]>;

/**
 * Tipo para erros de validação de formulário
 */
export type FormValidationErrors = Record<string, string>;

/**
 * Serviço de validação de formulário
 */
export const formValidationService = {
  /**
   * Valida dados de formulário contra um esquema
   * @param data - Dados a serem validados
   * @param schema - Esquema de validação
   * @returns Objeto com erros de validação ou null se válido
   */
  validate(
    data: Record<string, unknown>,
    schema: FormValidationSchema
  ): FormValidationErrors | null {
    const errors: FormValidationErrors = {};

    // Validar cada campo no esquema
    for (const [field, rules] of Object.entries(schema)) {
      const value = data[field];

      // Aplicar cada regra de validação
      for (const rule of rules) {
        const validator = formValidatorRegistry.get(rule.validator);

        if (!validator) {
          console.warn(`Validador "${rule.validator}" não encontrado`);
          continue;
        }

        // Executar validação
        const error = validator.validate(value, rule.options);

        // Se houver erro, adicionar ao objeto de erros e parar a validação deste campo
        if (error) {
          errors[field] = rule.message || error;
          break;
        }
      }
    }

    // Retornar null se não houver erros
    return Object.keys(errors).length > 0 ? errors : null;
  },

  /**
   * Registra um novo validador
   * @param validator - Validador a ser registrado
   */
  registerValidator(validator: FormValidator): void {
    formValidatorRegistry.register(validator);
  },
};
