/**
 * Social Auth Callback API
 *
 * Endpoint para processar o retorno da autenticação social.
 * Parte da implementação da tarefa 8.9.2 - Integração com redes sociais
 */

import type { APIRoute } from 'astro';
import { UserRepository } from '../../../../../domain/repositories/UserRepository';
import { SocialAuthService } from '../../../../../domain/services/SocialAuthService';
import { TokenService } from '../../../../../domain/services/TokenService';
import { PostgresUserRepository } from '../../../../../infrastructure/database/repositories/PostgresUserRepository';
import { JwtTokenService } from '../../../../../infrastructure/services/JwtTokenService';
import { OAuthSocialAuthService } from '../../../../../infrastructure/services/OAuthSocialAuthService';

// Inicializar serviços
const socialAuthService: SocialAuthService = new OAuthSocialAuthService();
const userRepository: UserRepository = new PostgresUserRepository();
const tokenService: TokenService = new JwtTokenService(
  process.env.JWT_SECRET || 'default-secret-key',
  process.env.PASSWORD_RESET_SECRET || 'password-reset-secret-key',
  process.env.EMAIL_VERIFICATION_SECRET || 'email-verification-secret-key'
);

// Configurar serviço de autenticação social
await socialAuthService.initialize({
  facebook: {
    appId: process.env.FACEBOOK_APP_ID || '',
    appSecret: process.env.FACEBOOK_APP_SECRET || '',
    redirectUri: `${process.env.BASE_URL || 'https://example.com'}/api/auth/social/callback/facebook`,
  },
  google: {
    clientId: process.env.GOOGLE_CLIENT_ID || '',
    clientSecret: process.env.GOOGLE_CLIENT_SECRET || '',
    redirectUri: `${process.env.BASE_URL || 'https://example.com'}/api/auth/social/callback/google`,
  },
  twitter: {
    apiKey: process.env.TWITTER_API_KEY || '',
    apiSecret: process.env.TWITTER_API_SECRET || '',
    redirectUri: `${process.env.BASE_URL || 'https://example.com'}/api/auth/social/callback/twitter`,
  },
  linkedin: {
    clientId: process.env.LINKEDIN_CLIENT_ID || '',
    clientSecret: process.env.LINKEDIN_CLIENT_SECRET || '',
    redirectUri: `${process.env.BASE_URL || 'https://example.com'}/api/auth/social/callback/linkedin`,
  },
  github: {
    clientId: process.env.GITHUB_CLIENT_ID || '',
    clientSecret: process.env.GITHUB_CLIENT_SECRET || '',
    redirectUri: `${process.env.BASE_URL || 'https://example.com'}/api/auth/social/callback/github`,
  },
});

export const GET: APIRoute = async ({ params, request }) => {
  try {
    const provider = params.provider as 'facebook' | 'google' | 'twitter' | 'linkedin' | 'github';

    if (!provider || !['facebook', 'google', 'twitter', 'linkedin', 'github'].includes(provider)) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Provedor de autenticação inválido.',
        }),
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    // Obter código de autorização da URL
    const url = new URL(request.url);
    const code = url.searchParams.get('code');
    const state = url.searchParams.get('state');

    if (!code) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Código de autorização não encontrado.',
        }),
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    // Processar callback
    const profile = await socialAuthService.handleCallback(provider, code, state || undefined);

    // Verificar se o usuário já existe
    let user = await userRepository.findBySocialId(provider, profile.id);

    if (!user) {
      // Verificar se existe um usuário com o mesmo email
      if (profile.email) {
        user = await userRepository.findByEmail(profile.email);
      }

      if (user) {
        // Atualizar usuário existente com informações sociais
        user = await userRepository.updateSocialInfo(user.id, {
          provider,
          socialId: profile.id,
          socialToken: profile.accessToken,
          socialRefreshToken: profile.refreshToken,
          socialTokenExpiresAt: profile.expiresAt,
        });
      } else {
        // Criar novo usuário
        user = await userRepository.createSocialUser({
          email: profile.email,
          name: profile.name || `${profile.firstName || ''} ${profile.lastName || ''}`.trim(),
          provider,
          socialId: profile.id,
          socialToken: profile.accessToken,
          socialRefreshToken: profile.refreshToken,
          socialTokenExpiresAt: profile.expiresAt,
          profilePicture: profile.profilePicture,
        });
      }
    } else {
      // Atualizar token de acesso
      user = await userRepository.updateSocialInfo(user.id, {
        socialToken: profile.accessToken,
        socialRefreshToken: profile.refreshToken,
        socialTokenExpiresAt: profile.expiresAt,
      });
    }

    // Gerar token JWT
    const token = tokenService.generateToken({
      id: user.id,
      username: user.username || user.email,
      role: 'user',
      permissions: [],
    });

    // Redirecionar para a página de sucesso com o token
    const redirectUrl = new URL('/auth/success', process.env.BASE_URL || 'https://example.com');
    redirectUrl.searchParams.set('token', token);

    return new Response(null, {
      status: 302,
      headers: {
        Location: redirectUrl.toString(),
      },
    });
  } catch (error) {
    console.error('Erro ao processar callback de autenticação social:', error);

    // Redirecionar para a página de erro
    const redirectUrl = new URL('/auth/error', process.env.BASE_URL || 'https://example.com');
    redirectUrl.searchParams.set('error', 'Erro ao processar autenticação social.');

    return new Response(null, {
      status: 302,
      headers: {
        Location: redirectUrl.toString(),
      },
    });
  }
};
