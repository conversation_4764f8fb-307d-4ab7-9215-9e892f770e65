/**
 * Modelo de dados para atividades educacionais
 *
 * Este arquivo define as interfaces e tipos para atividades
 * educacionais utilizadas na plataforma.
 */

/**
 * Tipos de atividades suportadas
 */
export enum ActivityType {
  /**
   * Atividade de múltipla escolha
   */
  MULTIPLE_CHOICE = 'multiple_choice',

  /**
   * Atividade de arrastar e soltar
   */
  DRAG_AND_DROP = 'drag_and_drop',

  /**
   * Atividade de completar lacunas
   */
  FILL_BLANKS = 'fill_blanks',

  /**
   * Atividade de associação
   */
  MATCHING = 'matching',

  /**
   * Atividade de ordenação
   */
  ORDERING = 'ordering',

  /**
   * Atividade de verdadeiro ou falso
   */
  TRUE_FALSE = 'true_false',

  /**
   * Atividade de resposta curta
   */
  SHORT_ANSWER = 'short_answer',

  /**
   * Atividade de desenho
   */
  DRAWING = 'drawing',

  /**
   * Atividade de reconhecimento de letras
   */
  LETTER_RECOGNITION = 'letter_recognition',

  /**
   * Atividade de reconhecimento de sons
   */
  SOUND_RECOGNITION = 'sound_recognition',

  /**
   * Atividade de formação de palavras
   */
  WORD_FORMATION = 'word_formation',

  /**
   * Atividade de leitura
   */
  READING = 'reading',

  /**
   * Atividade de escrita
   */
  WRITING = 'writing',

  /**
   * Atividade de contagem
   */
  COUNTING = 'counting',

  /**
   * Atividade de operações matemáticas
   */
  MATH_OPERATIONS = 'math_operations',
}

/**
 * Níveis de dificuldade
 */
export enum DifficultyLevel {
  /**
   * Nível fácil
   */
  EASY = 'easy',

  /**
   * Nível médio
   */
  MEDIUM = 'medium',

  /**
   * Nível difícil
   */
  HARD = 'hard',
}

/**
 * Categorias de atividades
 */
export enum ActivityCategory {
  /**
   * Alfabetização
   */
  LITERACY = 'literacy',

  /**
   * Matemática
   */
  MATH = 'math',

  /**
   * Ciências
   */
  SCIENCE = 'science',

  /**
   * Artes
   */
  ARTS = 'arts',

  /**
   * Geografia
   */
  GEOGRAPHY = 'geography',

  /**
   * História
   */
  HISTORY = 'history',
}

/**
 * Status de uma atividade
 */
export enum ActivityStatus {
  /**
   * Rascunho (não publicada)
   */
  DRAFT = 'draft',

  /**
   * Publicada (disponível para uso)
   */
  PUBLISHED = 'published',

  /**
   * Arquivada (não disponível, mas mantida)
   */
  ARCHIVED = 'archived',
}

/**
 * Interface base para qualquer tipo de atividade
 */
export interface BaseActivity {
  /**
   * ID único da atividade
   */
  id: string;

  /**
   * Título da atividade
   */
  title: string;

  /**
   * Descrição da atividade
   */
  description?: string;

  /**
   * Tipo de atividade
   */
  type: ActivityType;

  /**
   * Categoria da atividade
   */
  category: ActivityCategory;

  /**
   * Nível de dificuldade
   */
  difficulty: DifficultyLevel;

  /**
   * Pontuação máxima possível
   */
  maxScore: number;

  /**
   * Tempo estimado para conclusão (em segundos)
   */
  estimatedTime?: number;

  /**
   * Tags para classificação
   */
  tags?: string[];

  /**
   * Idade mínima recomendada
   */
  minAge?: number;

  /**
   * Idade máxima recomendada
   */
  maxAge?: number;

  /**
   * Série escolar recomendada
   */
  grade?: string;

  /**
   * Instruções para o aluno
   */
  instructions?: string;

  /**
   * Instruções para o professor
   */
  teacherNotes?: string;

  /**
   * Status da atividade
   */
  status: ActivityStatus;

  /**
   * ID do criador da atividade
   */
  createdBy: string;

  /**
   * Data de criação
   */
  createdAt: Date;

  /**
   * ID do último usuário que atualizou
   */
  updatedBy?: string;

  /**
   * Data da última atualização
   */
  updatedAt?: Date;

  /**
   * Metadados adicionais
   */
  metadata?: Record<string, any>;
}

/**
 * Interface para atividade de múltipla escolha
 */
export interface MultipleChoiceActivity extends BaseActivity {
  type: ActivityType.MULTIPLE_CHOICE;

  /**
   * Conteúdo específico da atividade
   */
  content: {
    /**
     * Pergunta principal
     */
    question: string;

    /**
     * Opções de resposta
     */
    options: {
      /**
       * ID da opção
       */
      id: string;

      /**
       * Texto da opção
       */
      text: string;

      /**
       * URL da imagem (opcional)
       */
      imageUrl?: string;
    }[];

    /**
     * IDs das opções corretas
     */
    correctOptionIds: string[];

    /**
     * Se permite múltiplas respostas
     */
    allowMultiple: boolean;

    /**
     * Feedback para resposta correta
     */
    correctFeedback?: string;

    /**
     * Feedback para resposta incorreta
     */
    incorrectFeedback?: string;
  };
}

/**
 * Interface para atividade de arrastar e soltar
 */
export interface DragAndDropActivity extends BaseActivity {
  type: ActivityType.DRAG_AND_DROP;

  /**
   * Conteúdo específico da atividade
   */
  content: {
    /**
     * Instruções específicas
     */
    instructions: string;

    /**
     * Itens para arrastar
     */
    draggableItems: {
      /**
       * ID do item
       */
      id: string;

      /**
       * Texto do item
       */
      text?: string;

      /**
       * URL da imagem
       */
      imageUrl?: string;
    }[];

    /**
     * Áreas para soltar
     */
    dropZones: {
      /**
       * ID da área
       */
      id: string;

      /**
       * Texto da área
       */
      text?: string;

      /**
       * URL da imagem
       */
      imageUrl?: string;

      /**
       * IDs dos itens corretos para esta área
       */
      correctItemIds: string[];
    }[];

    /**
     * Se permite múltiplos itens por área
     */
    allowMultipleItemsPerZone: boolean;
  };
}

/**
 * Interface para atividade de completar lacunas
 */
export interface FillBlanksActivity extends BaseActivity {
  type: ActivityType.FILL_BLANKS;

  /**
   * Conteúdo específico da atividade
   */
  content: {
    /**
     * Texto com lacunas (usando marcadores como {1}, {2}, etc.)
     */
    text: string;

    /**
     * Respostas corretas para cada lacuna
     */
    blanks: {
      /**
       * ID da lacuna (corresponde ao número no marcador)
       */
      id: string;

      /**
       * Respostas corretas (aceita múltiplas possibilidades)
       */
      correctAnswers: string[];

      /**
       * Dica para a lacuna
       */
      hint?: string;
    }[];

    /**
     * Se a verificação deve ignorar maiúsculas/minúsculas
     */
    caseSensitive: boolean;

    /**
     * Se a verificação deve ignorar acentos
     */
    ignoreAccents: boolean;
  };
}

/**
 * Interface para resultado de atividade
 */
export interface ActivityResult {
  /**
   * ID único do resultado
   */
  id: string;

  /**
   * ID da atividade
   */
  activityId: string;

  /**
   * ID do usuário
   */
  userId: string;

  /**
   * Pontuação obtida
   */
  score: number;

  /**
   * Pontuação máxima possível
   */
  maxScore: number;

  /**
   * Percentual de acerto
   */
  percentage: number;

  /**
   * Tempo gasto (em segundos)
   */
  timeSpent: number;

  /**
   * Se foi concluída
   */
  completed: boolean;

  /**
   * Respostas fornecidas
   */
  answers: Record<string, any>;

  /**
   * Feedback específico
   */
  feedback?: string;

  /**
   * Data de início
   */
  startedAt: Date;

  /**
   * Data de conclusão
   */
  completedAt?: Date;

  /**
   * Metadados adicionais
   */
  metadata?: Record<string, any>;
}
