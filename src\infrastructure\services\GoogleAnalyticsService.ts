/**
 * Google Analytics Service
 *
 * Implementação do serviço de analytics usando Google Analytics.
 * Parte da implementação da tarefa 8.9.3 - Analytics
 */

import { nanoid } from 'nanoid';
import {
  AnalyticsConfig,
  AnalyticsService,
  ConversionData,
  EcommerceData,
  EventData,
  ExceptionData,
  PageViewData,
  UserTimingData,
} from '../../domain/services/AnalyticsService';

export class GoogleAnalyticsService implements AnalyticsService {
  private config: AnalyticsConfig | null = null;
  private userId: string | null = null;
  private clientId: string | null = null;
  private sessionId: string | null = null;
  private customDimensions: Record<string, string> = {};
  private customMetrics: Record<string, number> = {};
  private isInitialized = false;

  /**
   * Inicializa o serviço de analytics com a configuração fornecida
   */
  async initialize(config: AnalyticsConfig): Promise<boolean> {
    try {
      this.config = config;
      this.clientId = this.getStoredClientId() || this.generateClientId();
      this.sessionId = this.getStoredSessionId() || this.generateSessionId();
      this.storeClientId(this.clientId);
      this.storeSessionId(this.sessionId);

      if (config.customDimensions) {
        this.customDimensions = { ...config.customDimensions };
      }

      if (config.customMetrics) {
        this.customMetrics = { ...config.customMetrics };
      }

      this.isInitialized = true;
      return true;
    } catch (error) {
      console.error('Erro ao inicializar serviço de analytics:', error);
      return false;
    }
  }

  /**
   * Rastreia uma visualização de página
   */
  async trackPageView(data: PageViewData): Promise<boolean> {
    if (!this.isInitialized || !this.config) {
      console.error('Serviço de analytics não inicializado');
      return false;
    }

    try {
      // Preparar dados para envio
      const payload = {
        v: 1, // Versão da API
        tid: this.config.trackingId, // ID de rastreamento
        cid: data.clientId || this.clientId, // ID do cliente
        t: 'pageview', // Tipo de hit
        dp: data.path, // Caminho da página
        dt: data.title, // Título da página
        dr: data.referrer, // Referenciador
        ua: data.userAgent, // User Agent
        uid: data.userId || this.userId, // ID do usuário
        z: Date.now(), // Cache buster
      };

      // Adicionar dimensões personalizadas
      const dimensions = { ...this.customDimensions, ...data.customDimensions };
      Object.entries(dimensions).forEach(([key, value]) => {
        payload[`cd${key}`] = value;
      });

      // Enviar dados para o Google Analytics
      await this.sendToGA(payload);

      return true;
    } catch (error) {
      console.error('Erro ao rastrear visualização de página:', error);
      return false;
    }
  }

  /**
   * Rastreia um evento
   */
  async trackEvent(data: EventData): Promise<boolean> {
    if (!this.isInitialized || !this.config) {
      console.error('Serviço de analytics não inicializado');
      return false;
    }

    try {
      // Preparar dados para envio
      const payload = {
        v: 1, // Versão da API
        tid: this.config.trackingId, // ID de rastreamento
        cid: data.clientId || this.clientId, // ID do cliente
        t: 'event', // Tipo de hit
        ec: data.category, // Categoria do evento
        ea: data.action, // Ação do evento
        el: data.label, // Rótulo do evento
        ev: data.value, // Valor do evento
        ni: data.nonInteraction ? 1 : 0, // Indicador de não-interação
        uid: data.userId || this.userId, // ID do usuário
        z: Date.now(), // Cache buster
      };

      // Adicionar dimensões personalizadas
      const dimensions = { ...this.customDimensions, ...data.customDimensions };
      Object.entries(dimensions).forEach(([key, value]) => {
        payload[`cd${key}`] = value;
      });

      // Enviar dados para o Google Analytics
      await this.sendToGA(payload);

      return true;
    } catch (error) {
      console.error('Erro ao rastrear evento:', error);
      return false;
    }
  }

  /**
   * Rastreia uma conversão
   */
  async trackConversion(data: ConversionData): Promise<boolean> {
    if (!this.isInitialized || !this.config) {
      console.error('Serviço de analytics não inicializado');
      return false;
    }

    try {
      // Preparar dados para envio
      const payload = {
        v: 1, // Versão da API
        tid: this.config.trackingId, // ID de rastreamento
        cid: data.clientId || this.clientId, // ID do cliente
        t: 'event', // Tipo de hit
        ec: 'Conversion', // Categoria do evento
        ea: data.goalName, // Ação do evento
        el: data.goalId, // Rótulo do evento
        ev: data.value, // Valor do evento
        uid: data.userId || this.userId, // ID do usuário
        z: Date.now(), // Cache buster
      };

      // Adicionar dimensões personalizadas
      const dimensions = { ...this.customDimensions, ...data.customDimensions };
      Object.entries(dimensions).forEach(([key, value]) => {
        payload[`cd${key}`] = value;
      });

      // Enviar dados para o Google Analytics
      await this.sendToGA(payload);

      return true;
    } catch (error) {
      console.error('Erro ao rastrear conversão:', error);
      return false;
    }
  }

  /**
   * Rastreia uma transação de e-commerce
   */
  async trackEcommerce(data: EcommerceData): Promise<boolean> {
    if (!this.isInitialized || !this.config) {
      console.error('Serviço de analytics não inicializado');
      return false;
    }

    try {
      // Preparar dados da transação
      const transactionPayload = {
        v: 1, // Versão da API
        tid: this.config.trackingId, // ID de rastreamento
        cid: data.clientId || this.clientId, // ID do cliente
        t: 'transaction', // Tipo de hit
        ti: data.transactionId, // ID da transação
        ta: data.affiliation, // Afiliação
        tr: data.revenue, // Receita
        tt: data.tax, // Imposto
        ts: data.shipping, // Frete
        tcc: data.coupon, // Cupom
        cu: data.currency, // Moeda
        uid: data.userId || this.userId, // ID do usuário
        z: Date.now(), // Cache buster
      };

      // Enviar dados da transação
      await this.sendToGA(transactionPayload);

      // Enviar dados dos itens
      if (data.items && data.items.length > 0) {
        for (const item of data.items) {
          const itemPayload = {
            v: 1, // Versão da API
            tid: this.config.trackingId, // ID de rastreamento
            cid: data.clientId || this.clientId, // ID do cliente
            t: 'item', // Tipo de hit
            ti: data.transactionId, // ID da transação
            in: item.name, // Nome do item
            ip: item.price, // Preço do item
            iq: item.quantity, // Quantidade do item
            ic: item.id, // SKU do item
            iv: item.category, // Categoria do item
            cu: data.currency, // Moeda
            uid: data.userId || this.userId, // ID do usuário
            z: Date.now(), // Cache buster
          };

          await this.sendToGA(itemPayload);
        }
      }

      return true;
    } catch (error) {
      console.error('Erro ao rastrear transação de e-commerce:', error);
      return false;
    }
  }

  /**
   * Rastreia métricas de tempo de usuário
   */
  async trackUserTiming(data: UserTimingData): Promise<boolean> {
    if (!this.isInitialized || !this.config) {
      console.error('Serviço de analytics não inicializado');
      return false;
    }

    try {
      // Preparar dados para envio
      const payload = {
        v: 1, // Versão da API
        tid: this.config.trackingId, // ID de rastreamento
        cid: data.clientId || this.clientId, // ID do cliente
        t: 'timing', // Tipo de hit
        utc: data.category, // Categoria do timing
        utv: data.variable, // Nome da variável
        utt: data.time, // Tempo em milissegundos
        utl: data.label, // Rótulo
        uid: data.userId || this.userId, // ID do usuário
        z: Date.now(), // Cache buster
      };

      // Enviar dados para o Google Analytics
      await this.sendToGA(payload);

      return true;
    } catch (error) {
      console.error('Erro ao rastrear timing de usuário:', error);
      return false;
    }
  }

  /**
   * Rastreia uma exceção
   */
  async trackException(data: ExceptionData): Promise<boolean> {
    if (!this.isInitialized || !this.config) {
      console.error('Serviço de analytics não inicializado');
      return false;
    }

    try {
      // Preparar dados para envio
      const payload = {
        v: 1, // Versão da API
        tid: this.config.trackingId, // ID de rastreamento
        cid: data.clientId || this.clientId, // ID do cliente
        t: 'exception', // Tipo de hit
        exd: data.description, // Descrição da exceção
        exf: data.fatal ? 1 : 0, // Indicador de fatalidade
        uid: data.userId || this.userId, // ID do usuário
        z: Date.now(), // Cache buster
      };

      // Enviar dados para o Google Analytics
      await this.sendToGA(payload);

      return true;
    } catch (error) {
      console.error('Erro ao rastrear exceção:', error);
      return false;
    }
  }

  /**
   * Define o ID do usuário para o rastreamento
   */
  setUserId(userId: string): void {
    this.userId = userId;
  }

  /**
   * Define uma dimensão personalizada
   */
  setCustomDimension(index: number, value: string): void {
    this.customDimensions[index.toString()] = value;
  }

  /**
   * Define uma métrica personalizada
   */
  setCustomMetric(index: number, value: number): void {
    this.customMetrics[index.toString()] = value;
  }

  /**
   * Gera o código de inicialização do analytics para o cliente
   */
  generateTrackingCode(): string {
    if (!this.config) {
      return '';
    }

    return `
      <!-- Google Analytics -->
      <script>
      (function(i,s,o,g,r,a,m){i['GoogleAnalyticsObject']=r;i[r]=i[r]||function(){
      (i[r].q=i[r].q||[]).push(arguments)},i[r].l=1*new Date();a=s.createElement(o),
      m=s.getElementsByTagName(o)[0];a.async=1;a.src=g;m.parentNode.insertBefore(a,m)
      })(window,document,'script','https://www.google-analytics.com/analytics.js','ga');

      ga('create', '${this.config.trackingId}', {
        cookieDomain: '${this.config.cookieDomain || 'auto'}',
        cookieExpires: ${this.config.cookieExpires || 63072000},
        cookieUpdate: ${this.config.cookieUpdate !== false},
        useSecure: ${this.config.useSecure !== false}
      });
      ${this.config.anonymizeIp ? "ga('set', 'anonymizeIp', true);" : ''}
      ${this.userId && this.config.sendUserId ? `ga('set', 'userId', '${this.userId}');` : ''}

      // Dimensões personalizadas
      ${Object.entries(this.customDimensions)
        .map(([key, value]) => `ga('set', 'dimension${key}', '${value}');`)
        .join('\n')}

      // Métricas personalizadas
      ${Object.entries(this.customMetrics)
        .map(([key, value]) => `ga('set', 'metric${key}', ${value});`)
        .join('\n')}

      ga('send', 'pageview');
      </script>
      <!-- End Google Analytics -->
    `;
  }

  /**
   * Obtém o ID do cliente atual
   */
  getClientId(): string | null {
    return this.clientId;
  }

  /**
   * Cria um novo ID de sessão
   */
  createSession(): string {
    this.sessionId = this.generateSessionId();
    this.storeSessionId(this.sessionId);
    return this.sessionId;
  }

  /**
   * Obtém o ID da sessão atual
   */
  getSessionId(): string | null {
    return this.sessionId;
  }

  /**
   * Limpa os dados de rastreamento (útil para GDPR)
   */
  async clearData(): Promise<boolean> {
    try {
      this.clientId = null;
      this.sessionId = null;
      this.userId = null;
      this.customDimensions = {};
      this.customMetrics = {};

      // Limpar dados armazenados
      if (typeof localStorage !== 'undefined') {
        localStorage.removeItem('ga_client_id');
        localStorage.removeItem('ga_session_id');
      }

      return true;
    } catch (error) {
      console.error('Erro ao limpar dados de rastreamento:', error);
      return false;
    }
  }

  /**
   * Envia dados para o Google Analytics
   */
  private async sendToGA(payload: Record<string, any>): Promise<void> {
    // No lado do servidor, enviar via API de Measurement Protocol
    try {
      const params = new URLSearchParams();

      // Adicionar todos os parâmetros válidos
      Object.entries(payload).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          params.append(key, value.toString());
        }
      });

      // Enviar requisição
      const response = await fetch('https://www.google-analytics.com/collect', {
        method: 'POST',
        body: params,
      });

      if (!response.ok) {
        throw new Error(`Erro ao enviar dados para o Google Analytics: ${response.status}`);
      }
    } catch (error) {
      console.error('Erro ao enviar dados para o Google Analytics:', error);
      throw error;
    }
  }

  /**
   * Gera um novo ID de cliente
   */
  private generateClientId(): string {
    return nanoid();
  }

  /**
   * Gera um novo ID de sessão
   */
  private generateSessionId(): string {
    return nanoid();
  }

  /**
   * Obtém o ID do cliente armazenado
   */
  private getStoredClientId(): string | null {
    if (typeof localStorage !== 'undefined') {
      return localStorage.getItem('ga_client_id');
    }
    return null;
  }

  /**
   * Obtém o ID da sessão armazenado
   */
  private getStoredSessionId(): string | null {
    if (typeof localStorage !== 'undefined') {
      return localStorage.getItem('ga_session_id');
    }
    return null;
  }

  /**
   * Armazena o ID do cliente
   */
  private storeClientId(clientId: string): void {
    if (typeof localStorage !== 'undefined') {
      localStorage.setItem('ga_client_id', clientId);
    }
  }

  /**
   * Armazena o ID da sessão
   */
  private storeSessionId(sessionId: string): void {
    if (typeof localStorage !== 'undefined') {
      localStorage.setItem('ga_session_id', sessionId);
    }
  }
}
