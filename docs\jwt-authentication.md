# Sistema de Autenticação JWT

## Visão Geral

O sistema de autenticação JWT (JSON Web Tokens) implementa um mecanismo seguro e escalável para autenticação e autorização de usuários na aplicação Estação da Alfabetização. Este documento descreve a arquitetura, componentes e fluxos de autenticação implementados.

## Componentes Principais

### 1. Serviço JWT (`jwtService`)

Responsável pela geração, validação e gerenciamento de tokens JWT.

**Funcionalidades:**
- Geração de tokens com diferentes finalidades (acesso, refresh, reset, verificação)
- Validação de tokens com verificações de segurança
- Revogação de tokens (blacklisting)
- Gerenciamento de chaves secretas

### 2. Serviço de Autenticação JWT (`authJwtService`)

Implementa a lógica de negócio para autenticação de usuários.

**Funcionalidades:**
- Autenticação de usuários com email/senha
- Verificação de tokens
- Renovação de tokens de acesso
- Rotação de refresh tokens
- Gerenciamento de sessões em múltiplos dispositivos

### 3. Repositório de Tokens (`tokenRepository`)

Gerencia o armazenamento e consulta de tokens no banco de dados.

**Funcionalidades:**
- Armazenamento de tokens emitidos
- Consulta de tokens por ID, valor ou JTI
- Revogação de tokens
- Limpeza de tokens expirados
- Rastreamento de uso de tokens

### 4. Middleware de Autenticação JWT (`jwtAuthMiddleware`)

Intercepta requisições HTTP para verificar a autenticação.

**Funcionalidades:**
- Verificação de tokens em requisições
- Renovação automática de tokens expirados
- Proteção contra roubo de tokens
- Redirecionamento para login quando necessário

## Tipos de Tokens

O sistema utiliza diferentes tipos de tokens para propósitos específicos:

1. **Access Token**
   - Curta duração (15 minutos)
   - Usado para autenticar requisições à API
   - Contém informações do usuário e permissões

2. **Refresh Token**
   - Longa duração (7 dias)
   - Usado para obter novos access tokens
   - Armazenado no banco de dados para revogação

3. **Reset Token**
   - Duração média (1 hora)
   - Usado para redefinição de senha

4. **Verification Token**
   - Duração média (24 horas)
   - Usado para verificação de email

5. **API Token**
   - Longa duração (30 dias)
   - Usado para integrações com sistemas externos

## Fluxos de Autenticação

### Login

1. Usuário submete credenciais (email/senha)
2. Sistema valida credenciais
3. Sistema gera access token e refresh token
4. Tokens são enviados ao cliente (cookies HttpOnly)
5. Refresh token é armazenado no banco de dados

### Verificação de Autenticação

1. Cliente envia requisição com access token
2. Middleware verifica a validade do token
3. Se válido, a requisição prossegue
4. Se inválido, o cliente é redirecionado para login

### Renovação de Token

1. Access token expira
2. Cliente envia refresh token
3. Sistema valida o refresh token
4. Sistema gera novo access token
5. Se o refresh token estiver próximo da expiração, um novo refresh token é gerado (rotação)

### Logout

1. Cliente solicita logout
2. Sistema revoga todos os tokens do usuário
3. Cookies são removidos do cliente

## Segurança

### Proteção Contra Ataques

1. **JWT ID (JTI)**
   - Cada token possui um identificador único
   - Permite revogação específica de tokens

2. **Blacklisting**
   - Tokens revogados são adicionados a uma blacklist
   - Implementado com Valkey (Redis) para alta performance

3. **Rotação de Refresh Tokens**
   - Refresh tokens são rotacionados periodicamente
   - Reduz o impacto de tokens comprometidos

4. **Fingerprinting de Dispositivos**
   - Tokens são vinculados a dispositivos específicos
   - Detecta possíveis roubos de tokens

5. **Múltiplas Chaves Secretas**
   - Diferentes chaves para diferentes tipos de tokens
   - Limita o impacto de uma chave comprometida

### Boas Práticas Implementadas

1. **Tokens de Curta Duração**
   - Access tokens expiram em 15 minutos
   - Reduz a janela de oportunidade para ataques

2. **Cookies HttpOnly**
   - Tokens armazenados em cookies HttpOnly
   - Proteção contra ataques XSS

3. **Validação Completa**
   - Verificação de emissor (issuer)
   - Verificação de audiência (audience)
   - Verificação de expiração
   - Verificação de tipo de token

4. **Auditoria**
   - Logging de eventos de autenticação
   - Rastreamento de uso de tokens
   - Detecção de anomalias

## Configuração

### Variáveis de Ambiente

```
JWT_SECRET=chave_secreta_principal
JWT_ACCESS_SECRET=chave_para_access_tokens
JWT_REFRESH_SECRET=chave_para_refresh_tokens
JWT_RESET_SECRET=chave_para_reset_tokens
JWT_VERIFICATION_SECRET=chave_para_verification_tokens
JWT_API_SECRET=chave_para_api_tokens
JWT_ISSUER=estacao-alfabetizacao
JWT_AUDIENCE=estacao-alfabetizacao-app
```

## Exemplos de Uso

### Autenticação de Usuário

```typescript
const authResult = await authJwtService.authenticate(email, password, deviceInfo);
if (authResult) {
  // Definir cookies
  cookies.set('access_token', authResult.accessToken, {
    httpOnly: true,
    secure: true,
    maxAge: 60 * 15 // 15 minutos
  });
  
  cookies.set('refresh_token', authResult.refreshToken, {
    httpOnly: true,
    secure: true,
    maxAge: 60 * 60 * 24 * 7 // 7 dias
  });
}
```

### Verificação de Token

```typescript
const user = await authJwtService.verifyToken(token);
if (user) {
  // Usuário autenticado
  // Prosseguir com a requisição
} else {
  // Token inválido
  // Redirecionar para login
}
```

### Renovação de Token

```typescript
const result = await authJwtService.refreshTokenWithRotation(refreshToken);
if (result) {
  // Definir novos tokens
  cookies.set('access_token', result.accessToken, { ... });
  
  if (result.refreshToken) {
    cookies.set('refresh_token', result.refreshToken, { ... });
  }
}
```

## Considerações Futuras

1. **WebAuthn/FIDO2**
   - Integração com autenticação sem senha
   - Suporte a chaves de segurança físicas

2. **OAuth/OIDC**
   - Suporte a provedores de identidade externos
   - Implementação de servidor de autorização

3. **Monitoramento Avançado**
   - Detecção de anomalias em tempo real
   - Alertas de segurança automatizados
