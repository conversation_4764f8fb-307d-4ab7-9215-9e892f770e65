/**
 * Generate Coupon Use Case
 *
 * Caso de uso para gerar um novo cupom de desconto.
 * Parte da implementação da tarefa 8.4.1 - Sistema de cupons
 */

import { generateRandomCode } from '../../../utils/couponUtils';
import { Coupon, CouponRestriction, CouponType } from '../../entities/Coupon';
import { CouponRepository } from '../../repositories/CouponRepository';

export interface GenerateCouponRequest {
  type: CouponType;
  value: number;
  description?: string;
  code?: string;
  usageLimit?: number;
  restrictions?: CouponRestriction;
  startDate?: Date;
  endDate?: Date;
  createdBy?: string;
  isActive?: boolean;
}

export interface GenerateCouponResponse {
  success: boolean;
  coupon?: Coupon;
  error?: string;
}

export class GenerateCouponUseCase {
  constructor(private couponRepository: CouponRepository) {}

  async execute(request: GenerateCouponRequest): Promise<GenerateCouponResponse> {
    try {
      // Validar os dados de entrada
      if (!this.validateRequest(request)) {
        return {
          success: false,
          error: 'Dados inválidos para geração de cupom.',
        };
      }

      // Gerar código de cupom se não fornecido
      let code = request.code?.trim().toUpperCase();

      if (!code) {
        code = await this.generateUniqueCode();
      } else {
        // Verificar se o código já existe
        const codeExists = await this.couponRepository.codeExists(code);

        if (codeExists) {
          return {
            success: false,
            error: 'Este código de cupom já está em uso.',
          };
        }
      }

      // Criar o cupom
      const coupon = new Coupon({
        id: crypto.randomUUID(),
        code,
        type: request.type,
        value: request.value,
        description: request.description,
        usageLimit: request.usageLimit,
        restrictions: request.restrictions,
        startDate: request.startDate,
        endDate: request.endDate,
        createdBy: request.createdBy,
        isActive: request.isActive !== undefined ? request.isActive : true,
      });

      // Salvar o cupom
      const savedCoupon = await this.couponRepository.create(coupon);

      return {
        success: true,
        coupon: savedCoupon,
      };
    } catch (error) {
      console.error('Erro ao gerar cupom:', error);
      return {
        success: false,
        error: 'Ocorreu um erro ao gerar o cupom.',
      };
    }
  }

  private validateRequest(request: GenerateCouponRequest): boolean {
    // Validar tipo de cupom
    if (!['percentage', 'fixed_amount', 'free_shipping'].includes(request.type)) {
      return false;
    }

    // Validar valor do cupom
    if (request.value < 0) {
      return false;
    }

    // Validar valor percentual (máximo 100%)
    if (request.type === 'percentage' && request.value > 100) {
      return false;
    }

    // Validar datas
    if (request.startDate && request.endDate && request.startDate > request.endDate) {
      return false;
    }

    // Validar limite de uso
    if (request.usageLimit !== undefined && request.usageLimit <= 0) {
      return false;
    }

    return true;
  }

  private async generateUniqueCode(): Promise<string> {
    const maxAttempts = 10;
    let attempts = 0;

    while (attempts < maxAttempts) {
      // Gerar código aleatório
      const code = generateRandomCode();

      // Verificar se o código já existe
      const codeExists = await this.couponRepository.codeExists(code);

      if (!codeExists) {
        return code;
      }

      attempts++;
    }

    throw new Error('Não foi possível gerar um código único após várias tentativas.');
  }
}
