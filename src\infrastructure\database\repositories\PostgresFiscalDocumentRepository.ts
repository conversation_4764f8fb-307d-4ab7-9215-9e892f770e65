/**
 * PostgreSQL Fiscal Document Repository
 *
 * Implementação do repositório de documentos fiscais usando PostgreSQL.
 * Parte da implementação da tarefa 8.7.1 - Integração fiscal
 */

import { Pool } from 'pg';
import {
  FiscalAddress,
  FiscalCustomer,
  FiscalDocument,
  FiscalDocumentStatus,
  FiscalDocumentType,
  FiscalItem,
} from '../../../domain/entities/FiscalDocument';
import {
  FiscalDocumentFilter,
  FiscalDocumentPaginationOptions,
  FiscalDocumentRepository,
  FiscalDocumentSortOptions,
  PaginatedFiscalDocuments,
} from '../../../domain/repositories/FiscalDocumentRepository';
import { getDbConnection } from '../connection';

export class PostgresFiscalDocumentRepository implements FiscalDocumentRepository {
  private pool: Pool;

  constructor() {
    this.pool = getDbConnection();
  }

  async create(document: FiscalDocument): Promise<FiscalDocument> {
    const client = await this.pool.connect();

    try {
      await client.query('BEGIN');

      // Inserir documento fiscal
      const documentQuery = `
        INSERT INTO fiscal_documents (
          id, type, number, series, status, issue_date,
          customer_id, customer_name, customer_document_type, customer_document_number,
          customer_email, customer_phone, customer_address,
          total_value, tax_value, discount_value, shipping_value, final_value,
          notes, xml_content, pdf_url, error_message,
          created_at, updated_at
        )
        VALUES (
          $1, $2, $3, $4, $5, $6,
          $7, $8, $9, $10,
          $11, $12, $13,
          $14, $15, $16, $17, $18,
          $19, $20, $21, $22,
          $23, $24
        )
        RETURNING *
      `;

      const documentValues = [
        document.id,
        document.type,
        document.number,
        document.series,
        document.status,
        document.issueDate,
        document.customer.id,
        document.customer.name,
        document.customer.documentType,
        document.customer.documentNumber,
        document.customer.email,
        document.customer.phone,
        JSON.stringify(document.customer.address),
        document.totalValue,
        document.taxValue,
        document.discountValue,
        document.shippingValue,
        document.finalValue,
        document.notes,
        document.xmlContent,
        document.pdfUrl,
        document.errorMessage,
        document.createdAt,
        document.updatedAt,
      ];

      const documentResult = await client.query(documentQuery, documentValues);

      // Inserir itens do documento
      for (const item of document.items) {
        const itemQuery = `
          INSERT INTO fiscal_document_items (
            id, document_id, product_code, description,
            quantity, unit_value, total_value,
            tax_code, tax_rate, tax_value
          )
          VALUES (
            $1, $2, $3, $4,
            $5, $6, $7,
            $8, $9, $10
          )
        `;

        const itemValues = [
          item.id,
          document.id,
          item.productCode,
          item.description,
          item.quantity,
          item.unitValue,
          item.totalValue,
          item.taxCode,
          item.taxRate,
          item.taxValue,
        ];

        await client.query(itemQuery, itemValues);
      }

      await client.query('COMMIT');

      return this.mapDbDocumentToEntity(documentResult.rows[0], document.items);
    } catch (error) {
      await client.query('ROLLBACK');
      console.error('Erro ao criar documento fiscal:', error);
      throw error;
    } finally {
      client.release();
    }
  }

  async update(document: FiscalDocument): Promise<FiscalDocument> {
    const client = await this.pool.connect();

    try {
      await client.query('BEGIN');

      // Atualizar documento fiscal
      const documentQuery = `
        UPDATE fiscal_documents
        SET
          type = $2,
          number = $3,
          series = $4,
          status = $5,
          issue_date = $6,
          customer_id = $7,
          customer_name = $8,
          customer_document_type = $9,
          customer_document_number = $10,
          customer_email = $11,
          customer_phone = $12,
          customer_address = $13,
          total_value = $14,
          tax_value = $15,
          discount_value = $16,
          shipping_value = $17,
          final_value = $18,
          notes = $19,
          xml_content = $20,
          pdf_url = $21,
          error_message = $22,
          updated_at = $23
        WHERE id = $1
        RETURNING *
      `;

      const documentValues = [
        document.id,
        document.type,
        document.number,
        document.series,
        document.status,
        document.issueDate,
        document.customer.id,
        document.customer.name,
        document.customer.documentType,
        document.customer.documentNumber,
        document.customer.email,
        document.customer.phone,
        JSON.stringify(document.customer.address),
        document.totalValue,
        document.taxValue,
        document.discountValue,
        document.shippingValue,
        document.finalValue,
        document.notes,
        document.xmlContent,
        document.pdfUrl,
        document.errorMessage,
        document.updatedAt,
      ];

      const documentResult = await client.query(documentQuery, documentValues);

      if (documentResult.rows.length === 0) {
        throw new Error(`Documento fiscal com ID ${document.id} não encontrado`);
      }

      // Remover itens existentes
      await client.query('DELETE FROM fiscal_document_items WHERE document_id = $1', [document.id]);

      // Inserir novos itens
      for (const item of document.items) {
        const itemQuery = `
          INSERT INTO fiscal_document_items (
            id, document_id, product_code, description,
            quantity, unit_value, total_value,
            tax_code, tax_rate, tax_value
          )
          VALUES (
            $1, $2, $3, $4,
            $5, $6, $7,
            $8, $9, $10
          )
        `;

        const itemValues = [
          item.id,
          document.id,
          item.productCode,
          item.description,
          item.quantity,
          item.unitValue,
          item.totalValue,
          item.taxCode,
          item.taxRate,
          item.taxValue,
        ];

        await client.query(itemQuery, itemValues);
      }

      await client.query('COMMIT');

      return this.mapDbDocumentToEntity(documentResult.rows[0], document.items);
    } catch (error) {
      await client.query('ROLLBACK');
      console.error('Erro ao atualizar documento fiscal:', error);
      throw error;
    } finally {
      client.release();
    }
  }

  async getById(id: string): Promise<FiscalDocument | null> {
    try {
      // Obter documento
      const documentQuery = `
        SELECT * FROM fiscal_documents
        WHERE id = $1
      `;

      const documentResult = await this.pool.query(documentQuery, [id]);

      if (documentResult.rows.length === 0) {
        return null;
      }

      // Obter itens do documento
      const itemsQuery = `
        SELECT * FROM fiscal_document_items
        WHERE document_id = $1
        ORDER BY id
      `;

      const itemsResult = await this.pool.query(itemsQuery, [id]);

      const items = itemsResult.rows.map((row) => this.mapDbItemToEntity(row));

      return this.mapDbDocumentToEntity(documentResult.rows[0], items);
    } catch (error) {
      console.error('Erro ao obter documento fiscal:', error);
      throw error;
    }
  }

  async find(
    filter: FiscalDocumentFilter,
    sort?: FiscalDocumentSortOptions,
    pagination?: FiscalDocumentPaginationOptions
  ): Promise<PaginatedFiscalDocuments> {
    try {
      // Construir a consulta base
      let query = 'SELECT * FROM fiscal_documents WHERE 1=1';
      let countQuery = 'SELECT COUNT(*) FROM fiscal_documents WHERE 1=1';
      const values: any[] = [];
      let paramIndex = 1;

      // Adicionar filtros
      if (filter.ids && filter.ids.length > 0) {
        query += ` AND id = ANY($${paramIndex})`;
        countQuery += ` AND id = ANY($${paramIndex})`;
        values.push(filter.ids);
        paramIndex++;
      }

      if (filter.type) {
        if (Array.isArray(filter.type)) {
          query += ` AND type = ANY($${paramIndex})`;
          countQuery += ` AND type = ANY($${paramIndex})`;
          values.push(filter.type);
        } else {
          query += ` AND type = $${paramIndex}`;
          countQuery += ` AND type = $${paramIndex}`;
          values.push(filter.type);
        }
        paramIndex++;
      }

      if (filter.status) {
        if (Array.isArray(filter.status)) {
          query += ` AND status = ANY($${paramIndex})`;
          countQuery += ` AND status = ANY($${paramIndex})`;
          values.push(filter.status);
        } else {
          query += ` AND status = $${paramIndex}`;
          countQuery += ` AND status = $${paramIndex}`;
          values.push(filter.status);
        }
        paramIndex++;
      }

      if (filter.number) {
        query += ` AND number = $${paramIndex}`;
        countQuery += ` AND number = $${paramIndex}`;
        values.push(filter.number);
        paramIndex++;
      }

      if (filter.customerDocumentNumber) {
        query += ` AND customer_document_number = $${paramIndex}`;
        countQuery += ` AND customer_document_number = $${paramIndex}`;
        values.push(filter.customerDocumentNumber);
        paramIndex++;
      }

      if (filter.customerName) {
        query += ` AND customer_name ILIKE $${paramIndex}`;
        countQuery += ` AND customer_name ILIKE $${paramIndex}`;
        values.push(`%${filter.customerName}%`);
        paramIndex++;
      }

      if (filter.startDate) {
        query += ` AND issue_date >= $${paramIndex}`;
        countQuery += ` AND issue_date >= $${paramIndex}`;
        values.push(filter.startDate);
        paramIndex++;
      }

      if (filter.endDate) {
        query += ` AND issue_date <= $${paramIndex}`;
        countQuery += ` AND issue_date <= $${paramIndex}`;
        values.push(filter.endDate);
        paramIndex++;
      }

      if (filter.minValue) {
        query += ` AND final_value >= $${paramIndex}`;
        countQuery += ` AND final_value >= $${paramIndex}`;
        values.push(filter.minValue);
        paramIndex++;
      }

      if (filter.maxValue) {
        query += ` AND final_value <= $${paramIndex}`;
        countQuery += ` AND final_value <= $${paramIndex}`;
        values.push(filter.maxValue);
        paramIndex++;
      }

      // Adicionar ordenação
      if (sort) {
        const fieldMap: Record<string, string> = {
          issueDate: 'issue_date',
          number: 'number',
          customerName: 'customer_name',
          finalValue: 'final_value',
          createdAt: 'created_at',
          updatedAt: 'updated_at',
        };

        const field = fieldMap[sort.field] || 'created_at';
        const direction = sort.direction.toUpperCase() === 'DESC' ? 'DESC' : 'ASC';

        query += ` ORDER BY ${field} ${direction}`;
      } else {
        query += ' ORDER BY created_at DESC';
      }

      // Adicionar paginação
      if (pagination) {
        const limit = pagination.limit || 10;
        const offset = ((pagination.page || 1) - 1) * limit;

        query += ` LIMIT $${paramIndex} OFFSET $${paramIndex + 1}`;
        values.push(limit, offset);
        paramIndex += 2;
      }

      // Executar consultas
      const [documentsResult, countResult] = await Promise.all([
        this.pool.query(query, values),
        this.pool.query(countQuery, values.slice(0, values.length - (pagination ? 2 : 0))),
      ]);

      // Obter itens para cada documento
      const documents: FiscalDocument[] = [];

      for (const documentRow of documentsResult.rows) {
        const itemsQuery = `
          SELECT * FROM fiscal_document_items
          WHERE document_id = $1
          ORDER BY id
        `;

        const itemsResult = await this.pool.query(itemsQuery, [documentRow.id]);
        const items = itemsResult.rows.map((row) => this.mapDbItemToEntity(row));

        documents.push(this.mapDbDocumentToEntity(documentRow, items));
      }

      const total = Number.parseInt(countResult.rows[0].count, 10);

      return {
        documents,
        total,
        page: pagination?.page || 1,
        limit: pagination?.limit || documents.length,
        totalPages: pagination ? Math.ceil(total / pagination.limit) : 1,
      };
    } catch (error) {
      console.error('Erro ao buscar documentos fiscais:', error);
      throw error;
    }
  }

  async getByType(
    type: FiscalDocumentType,
    pagination?: FiscalDocumentPaginationOptions
  ): Promise<PaginatedFiscalDocuments> {
    return this.find({ type }, { field: 'createdAt', direction: 'desc' }, pagination);
  }

  async getByStatus(
    status: FiscalDocumentStatus,
    pagination?: FiscalDocumentPaginationOptions
  ): Promise<PaginatedFiscalDocuments> {
    return this.find({ status }, { field: 'createdAt', direction: 'desc' }, pagination);
  }

  async getByCustomer(
    customerId: string,
    pagination?: FiscalDocumentPaginationOptions
  ): Promise<PaginatedFiscalDocuments> {
    try {
      return this.find(
        { ids: [customerId] },
        { field: 'createdAt', direction: 'desc' },
        pagination
      );
    } catch (error) {
      console.error('Erro ao obter documentos fiscais por cliente:', error);
      throw error;
    }
  }

  async getByPeriod(
    startDate: Date,
    endDate: Date,
    pagination?: FiscalDocumentPaginationOptions
  ): Promise<PaginatedFiscalDocuments> {
    return this.find({ startDate, endDate }, { field: 'issueDate', direction: 'desc' }, pagination);
  }

  async updateStatus(
    id: string,
    status: FiscalDocumentStatus,
    errorMessage?: string
  ): Promise<boolean> {
    try {
      const query = `
        UPDATE fiscal_documents
        SET
          status = $2,
          error_message = $3,
          updated_at = NOW()
        WHERE id = $1
        RETURNING id
      `;

      const result = await this.pool.query(query, [id, status, errorMessage]);

      return result.rows.length > 0;
    } catch (error) {
      console.error('Erro ao atualizar status do documento fiscal:', error);
      throw error;
    }
  }

  async setIssuedInfo(
    id: string,
    number: string,
    series: string,
    issueDate: Date,
    xmlContent: string,
    pdfUrl: string
  ): Promise<boolean> {
    try {
      const query = `
        UPDATE fiscal_documents
        SET
          number = $2,
          series = $3,
          issue_date = $4,
          xml_content = $5,
          pdf_url = $6,
          status = 'ISSUED',
          updated_at = NOW()
        WHERE id = $1
        RETURNING id
      `;

      const result = await this.pool.query(query, [
        id,
        number,
        series,
        issueDate,
        xmlContent,
        pdfUrl,
      ]);

      return result.rows.length > 0;
    } catch (error) {
      console.error('Erro ao atualizar informações de emissão do documento fiscal:', error);
      throw error;
    }
  }

  async cancel(id: string, reason: string): Promise<boolean> {
    try {
      const query = `
        UPDATE fiscal_documents
        SET
          status = 'CANCELLED',
          notes = CASE
            WHEN notes IS NULL THEN $2
            ELSE notes || E'\\n' || $2
          END,
          updated_at = NOW()
        WHERE id = $1
        RETURNING id
      `;

      const result = await this.pool.query(query, [id, `Cancelamento: ${reason}`]);

      return result.rows.length > 0;
    } catch (error) {
      console.error('Erro ao cancelar documento fiscal:', error);
      throw error;
    }
  }

  async delete(id: string): Promise<boolean> {
    const client = await this.pool.connect();

    try {
      await client.query('BEGIN');

      // Verificar se o documento está em status DRAFT
      const checkQuery = `
        SELECT status FROM fiscal_documents
        WHERE id = $1
      `;

      const checkResult = await client.query(checkQuery, [id]);

      if (checkResult.rows.length === 0) {
        return false;
      }

      if (checkResult.rows[0].status !== 'DRAFT') {
        throw new Error('Não é possível excluir um documento fiscal que não está em rascunho');
      }

      // Excluir itens
      await client.query('DELETE FROM fiscal_document_items WHERE document_id = $1', [id]);

      // Excluir documento
      const deleteQuery = `
        DELETE FROM fiscal_documents
        WHERE id = $1
        RETURNING id
      `;

      const deleteResult = await client.query(deleteQuery, [id]);

      await client.query('COMMIT');

      return deleteResult.rows.length > 0;
    } catch (error) {
      await client.query('ROLLBACK');
      console.error('Erro ao excluir documento fiscal:', error);
      throw error;
    } finally {
      client.release();
    }
  }

  async getStats(): Promise<{
    totalDocuments: number;
    issuedDocuments: number;
    cancelledDocuments: number;
    errorDocuments: number;
    totalValue: number;
    typeDistribution: Record<FiscalDocumentType, number>;
    statusDistribution: Record<FiscalDocumentStatus, number>;
  }> {
    try {
      const query = `
        SELECT * FROM fiscal_stats
        ORDER BY updated_at DESC
        LIMIT 1
      `;

      const result = await this.pool.query(query);

      if (result.rows.length === 0) {
        return {
          totalDocuments: 0,
          issuedDocuments: 0,
          cancelledDocuments: 0,
          errorDocuments: 0,
          totalValue: 0,
          typeDistribution: {} as Record<FiscalDocumentType, number>,
          statusDistribution: {} as Record<FiscalDocumentStatus, number>,
        };
      }

      const stats = result.rows[0];

      return {
        totalDocuments: stats.total_documents,
        issuedDocuments: stats.issued_documents,
        cancelledDocuments: stats.cancelled_documents,
        errorDocuments: stats.error_documents,
        totalValue: Number.parseFloat(stats.total_value),
        typeDistribution: stats.type_distribution,
        statusDistribution: stats.status_distribution,
      };
    } catch (error) {
      console.error('Erro ao obter estatísticas fiscais:', error);
      throw error;
    }
  }

  /**
   * Mapeia um documento fiscal do banco de dados para a entidade
   */
  private mapDbDocumentToEntity(dbDocument: any, items: FiscalItem[]): FiscalDocument {
    const address: FiscalAddress = dbDocument.customer_address;

    const customer: FiscalCustomer = {
      id: dbDocument.customer_id,
      name: dbDocument.customer_name,
      documentType: dbDocument.customer_document_type,
      documentNumber: dbDocument.customer_document_number,
      email: dbDocument.customer_email,
      phone: dbDocument.customer_phone,
      address,
    };

    return new FiscalDocument({
      id: dbDocument.id,
      type: dbDocument.type as FiscalDocumentType,
      number: dbDocument.number,
      series: dbDocument.series,
      status: dbDocument.status as FiscalDocumentStatus,
      issueDate: dbDocument.issue_date,
      customer,
      items,
      totalValue: Number.parseFloat(dbDocument.total_value),
      taxValue: Number.parseFloat(dbDocument.tax_value),
      discountValue: Number.parseFloat(dbDocument.discount_value),
      shippingValue: Number.parseFloat(dbDocument.shipping_value),
      finalValue: Number.parseFloat(dbDocument.final_value),
      notes: dbDocument.notes,
      xmlContent: dbDocument.xml_content,
      pdfUrl: dbDocument.pdf_url,
      errorMessage: dbDocument.error_message,
      createdAt: dbDocument.created_at,
      updatedAt: dbDocument.updated_at,
    });
  }

  /**
   * Mapeia um item fiscal do banco de dados para a entidade
   */
  private mapDbItemToEntity(dbItem: any): FiscalItem {
    return {
      id: dbItem.id,
      productCode: dbItem.product_code,
      description: dbItem.description,
      quantity: Number.parseFloat(dbItem.quantity),
      unitValue: Number.parseFloat(dbItem.unit_value),
      totalValue: Number.parseFloat(dbItem.total_value),
      taxCode: dbItem.tax_code,
      taxRate: Number.parseFloat(dbItem.tax_rate),
      taxValue: Number.parseFloat(dbItem.tax_value),
    };
  }
}
