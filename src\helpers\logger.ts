import { createWriteStream } from 'node:fs';
import { mkdir } from 'node:fs/promises';
import { dirname } from 'node:path';
// src/helpers/logger.ts
import pino from 'pino';

// Criar diretório de logs se não existir
const logsDir = 'logs';
mkdir(logsDir, { recursive: true }).catch(() => {
  // Ignorar erro se o diretório já existir
});

// Configurar streams para arquivos
const securityStream = createWriteStream('logs/security.log', { flags: 'a' });
const combinedStream = createWriteStream('logs/combined.log', { flags: 'a' });

// Configurar logger principal
const logger = pino(
  {
    level: 'info',
    timestamp: pino.stdTimeFunctions.isoTime,
    formatters: {
      level: (label) => {
        return { level: label };
      },
    },
  },
  process.env.NODE_ENV !== 'production' ? process.stdout : combinedStream
);

// Logger específico para segurança
const securityLogger = pino(
  {
    level: 'warn',
    timestamp: pino.stdTimeFunctions.isoTime,
  },
  securityStream
);

export { logger };

export const securityLoggerHelpers = {
  suspicious: (data: Record<string, unknown>) => {
    securityLogger.warn(data, 'Atividade suspeita detectada');
  },

  blocked: (data: Record<string, unknown>) => {
    securityLogger.error(data, 'Tentativa bloqueada');
  },

  auth: (data: Record<string, unknown>) => {
    logger.info(data, 'Evento de autenticação');
  },
};
