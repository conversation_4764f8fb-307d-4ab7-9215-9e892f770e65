import type {
  ActionResponse,
  CategoryData,
} from "@helpers/database/interfacesHelper";
import { handleError } from "@helpers/errorUtils";
import { helper } from "@helpers/index";
import { defineAction } from "astro:actions";
import { z } from "zod";
import { DatabaseError, type QueryResult } from "pg";

export const categoryAction = {
  create: defineAction({
    accept: "form",
    input: z.object({
      name: z.string().min(3),
      ulid_parent: z.string().optional(),
    }),
    handler: async (input): Promise<ActionResponse<CategoryData>> => {
      try {
        const result = await helper.database.categoryHelper.create(
          input.name,
          input.ulid_parent,
        );

        return {
          success: true,
          result: result.rows[0],
        };
      } catch (error) {
        return handleError("create", error);
      }
    },
  }),

  read: defineAction({
    input: z.object({
      ulid_category: z.string().optional(),
      name: z.string().optional(),
      active: z.boolean().optional(),
    }),
    handler: async (input): Promise<ActionResponse<CategoryData[]>> => {
      try {
        let result = {} as QueryResult;

        result = await helper.database.categoryHelper.read(
          input.ulid_category || undefined,
          input.name || undefined,
          input.active || true,
        );

        if (result.rowCount === 0) {
          return {
            success: false,
            error: "Categoria não encontrada",
          };
        }

        return {
          success: true,
          result: result.rows,
        };
      } catch (error) {
        return handleError("read", error);
      }
    },
  }),

  update: defineAction({
    accept: "form",
    input: z.object({
      ulid_category: z.string(),
      name: z.string().min(3),
      active: z.boolean(),
      ulid_parent: z.string().optional(),
    }),
    handler: async (input): Promise<ActionResponse<CategoryData>> => {
      try {
        const result = await helper.database.categoryHelper.update(
          input.ulid_category,
          input.active,
          input.name,
          input.ulid_parent,
        );

        if (result.rowCount === 0) {
          return {
            success: false,
            error: "Categoria não encontrada",
          };
        }

        return {
          success: true,
          result: result.rows[0],
        };
      } catch (error) {
        return handleError("update", error);
      }
    },
  }),

  delete: defineAction({
    input: z.object({
      method: z.literal("delete"),
      ulid_category: z.string(),
    }),
    handler: async (input): Promise<ActionResponse<CategoryData>> => {
      try {
        const result = await helper.database.categoryHelper.deleteByUlid(
          input.ulid_category,
        );

        if (result.rowCount === 0) {
          return {
            success: false,
            error: "Categoria não encontrada",
          };
        }

        return {
          success: true,
          result: result.rows[0],
        };
      } catch (error) {
        if (error instanceof DatabaseError && error.code === "23503") {
          const result = await helper.database.categoryHelper.inactivate(
            input.ulid_category,
          );

          return {
            success: true,
            result: result.rows[0],
          };
        }

        return handleError("delete", error);
      }
    },
  }),
};
