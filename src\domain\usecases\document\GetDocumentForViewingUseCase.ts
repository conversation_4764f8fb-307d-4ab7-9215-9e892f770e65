/**
 * Get Document For Viewing Use Case
 *
 * Caso de uso para obter um documento para visualização segura.
 * Parte da implementação da tarefa 8.3.2 - Visualização segura
 */

import { Document, DocumentVersion } from '../../entities/Document';
import { AccessLogRepository } from '../../repositories/AccessLogRepository';
import { DocumentRepository } from '../../repositories/DocumentRepository';
import { PermissionRepository } from '../../repositories/PermissionRepository';
import { WatermarkService } from '../../services/WatermarkService';

export interface GetDocumentForViewingRequest {
  documentId: string;
  userId: string;
  version?: number;
  ipAddress?: string;
  userAgent?: string;
  applyWatermark?: boolean;
  watermarkText?: string;
}

export interface GetDocumentForViewingResponse {
  success: boolean;
  document?: Document;
  version?: DocumentVersion;
  content?: Uint8Array;
  watermarkedContent?: Uint8Array;
  error?: string;
  isOwner: boolean;
  hasEditPermission: boolean;
}

export class GetDocumentForViewingUseCase {
  constructor(
    private documentRepository: DocumentRepository,
    private accessLogRepository: AccessLogRepository,
    private permissionRepository: PermissionRepository,
    private watermarkService: WatermarkService
  ) {}

  async execute(request: GetDocumentForViewingRequest): Promise<GetDocumentForViewingResponse> {
    try {
      // Buscar o documento
      const document = await this.documentRepository.getById(request.documentId, true);

      if (!document) {
        return {
          success: false,
          error: 'Documento não encontrado.',
          isOwner: false,
          hasEditPermission: false,
        };
      }

      // Verificar se o usuário é o proprietário do documento
      const isOwner = document.ownerId === request.userId;

      // Verificar se o documento é público
      const isPublic = document.isPublic;

      // Verificar permissões do usuário
      let hasViewPermission = isOwner || isPublic;
      let hasEditPermission = isOwner;

      if (!hasViewPermission) {
        // Verificar se o usuário tem permissão explícita para visualizar
        const permission = await this.permissionRepository.getPermission(
          request.documentId,
          request.userId
        );

        if (permission) {
          hasViewPermission = ['read', 'edit', 'admin'].includes(permission.permissionType);
          hasEditPermission = ['edit', 'admin'].includes(permission.permissionType);

          // Verificar se a permissão expirou
          if (permission.expiresAt && permission.expiresAt < new Date()) {
            hasViewPermission = false;
            hasEditPermission = false;
          }
        }
      }

      // Se o usuário não tem permissão para visualizar, retornar erro
      if (!hasViewPermission) {
        return {
          success: false,
          error: 'Você não tem permissão para visualizar este documento.',
          isOwner,
          hasEditPermission,
        };
      }

      // Determinar qual versão buscar
      const versionNumber = request.version || document.currentVersion;

      // Buscar a versão específica
      const version = await this.documentRepository.getVersion(request.documentId, versionNumber);

      if (!version) {
        return {
          success: false,
          error: `Versão ${versionNumber} não encontrada.`,
          isOwner,
          hasEditPermission,
        };
      }

      // Registrar o acesso no log
      await this.accessLogRepository.logAccess({
        documentId: request.documentId,
        userId: request.userId,
        accessType: 'view',
        version: versionNumber,
        ipAddress: request.ipAddress,
        userAgent: request.userAgent,
        accessedAt: new Date(),
      });

      // Aplicar marca d'água se solicitado
      let watermarkedContent: Uint8Array | undefined;

      if (request.applyWatermark) {
        const watermarkText =
          request.watermarkText ||
          `Visualizado por: ${request.userId} | Data: ${new Date().toLocaleString()}`;

        watermarkedContent = await this.watermarkService.applyWatermark(
          version.content,
          watermarkText
        );
      }

      return {
        success: true,
        document,
        version,
        content: version.content,
        watermarkedContent,
        isOwner,
        hasEditPermission,
      };
    } catch (error) {
      console.error('Erro ao obter documento para visualização:', error);
      return {
        success: false,
        error: 'Ocorreu um erro ao processar a solicitação de visualização do documento.',
        isOwner: false,
        hasEditPermission: false,
      };
    }
  }
}
