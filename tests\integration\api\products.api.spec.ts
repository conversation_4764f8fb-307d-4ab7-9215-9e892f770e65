/**
 * Testes de integração para API de produtos
 *
 * Este arquivo contém testes que verificam a integração entre a API de produtos
 * e os serviços relacionados.
 * Parte da implementação da tarefa 9.1.2 - Testes de integração
 */

import { expect, test } from '@playwright/test';

// Dados de teste
const testAdmin = {
  email: '<EMAIL>',
  password: 'Admin@123456',
  name: 'Admin Products User',
  role: 'admin',
};

const testProduct = {
  name: 'Produto de Teste Integração',
  description: 'Descrição do produto de teste para integração',
  price: 99.99,
  categoryId: '1', // Será atualizado com uma categoria real
  stock: 100,
  isActive: true,
  images: ['https://example.com/image1.jpg'],
};

// Configuração para testes de API
test.describe('API de Produtos', () => {
  // Variáveis para armazenar tokens e IDs
  let adminToken: string;
  let productId: string;
  let categoryId: string;

  // Antes de todos os testes, criar usuário admin e obter categoria
  test.beforeAll(async ({ request }) => {
    // Criar usuário admin
    try {
      const adminRegisterResponse = await request.post('/api/auth/register', {
        data: {
          ...testAdmin,
          role: 'user', // Inicialmente registrar como usuário normal
        },
      });

      if (adminRegisterResponse.ok()) {
        // Fazer login como admin
        const adminLoginResponse = await request.post('/api/auth/login', {
          data: {
            email: testAdmin.email,
            password: testAdmin.password,
          },
        });

        if (adminLoginResponse.ok()) {
          const adminData = await adminLoginResponse.json();
          adminToken = adminData.accessToken;

          // Promover a admin usando uma rota especial de teste
          await request.post('/api/test/promote-to-admin', {
            headers: {
              Authorization: `Bearer ${adminToken}`,
              'X-Test-Key': process.env.TEST_SECRET_KEY || 'test-integration-key',
            },
            data: {
              userId: adminData.user.id,
            },
          });

          // Fazer login novamente para obter token com permissões de admin
          const newAdminLoginResponse = await request.post('/api/auth/login', {
            data: {
              email: testAdmin.email,
              password: testAdmin.password,
            },
          });

          if (newAdminLoginResponse.ok()) {
            const newAdminData = await newAdminLoginResponse.json();
            adminToken = newAdminData.accessToken;
          }
        }
      }

      // Obter categorias existentes ou criar uma nova
      const categoriesResponse = await request.get('/api/categories', {
        headers: {
          Authorization: `Bearer ${adminToken}`,
        },
      });

      if (categoriesResponse.ok()) {
        const categoriesData = await categoriesResponse.json();

        if (categoriesData.categories && categoriesData.categories.length > 0) {
          // Usar a primeira categoria existente
          categoryId = categoriesData.categories[0].id;
        } else {
          // Criar uma nova categoria
          const newCategoryResponse = await request.post('/api/categories', {
            headers: {
              Authorization: `Bearer ${adminToken}`,
            },
            data: {
              name: 'Categoria de Teste',
              description: 'Categoria para testes de integração',
            },
          });

          if (newCategoryResponse.ok()) {
            const newCategoryData = await newCategoryResponse.json();
            categoryId = newCategoryData.id;
          }
        }

        // Atualizar o produto de teste com a categoria real
        testProduct.categoryId = categoryId;
      }
    } catch (error) {
      console.error('Erro ao configurar testes de produtos:', error);
    }
  });

  // Após todos os testes, excluir o produto de teste e o usuário admin
  test.afterAll(async ({ request }) => {
    // Excluir produto de teste
    if (productId && adminToken) {
      await request.delete(`/api/products/${productId}`, {
        headers: {
          Authorization: `Bearer ${adminToken}`,
        },
      });
    }

    // Excluir usuário admin
    if (adminToken) {
      await request.delete('/api/users/me', {
        headers: {
          Authorization: `Bearer ${adminToken}`,
        },
      });
    }
  });

  test('admin deve criar um novo produto', async ({ request }) => {
    // Garantir que temos um token de admin
    expect(adminToken).toBeDefined();
    expect(categoryId).toBeDefined();

    const response = await request.post('/api/products', {
      headers: {
        Authorization: `Bearer ${adminToken}`,
      },
      data: testProduct,
    });

    expect(response.status()).toBe(201);

    const productData = await response.json();
    expect(productData).toHaveProperty('id');
    expect(productData).toHaveProperty('name', testProduct.name);
    expect(productData).toHaveProperty('price', testProduct.price);
    expect(productData).toHaveProperty('categoryId', testProduct.categoryId);

    // Armazenar ID do produto para testes subsequentes
    productId = productData.id;
  });

  test('deve listar todos os produtos', async ({ request }) => {
    const response = await request.get('/api/products');

    expect(response.status()).toBe(200);

    const productsData = await response.json();
    expect(Array.isArray(productsData.products)).toBeTruthy();
    expect(productsData.products.length).toBeGreaterThan(0);

    // Verificar se nosso produto de teste está na lista
    const testProductInList = productsData.products.find((p: any) => p.id === productId);
    expect(testProductInList).toBeDefined();
    expect(testProductInList.name).toBe(testProduct.name);
  });

  test('deve obter detalhes de um produto específico', async ({ request }) => {
    // Garantir que temos um ID de produto
    expect(productId).toBeDefined();

    const response = await request.get(`/api/products/${productId}`);

    expect(response.status()).toBe(200);

    const productData = await response.json();
    expect(productData).toHaveProperty('id', productId);
    expect(productData).toHaveProperty('name', testProduct.name);
    expect(productData).toHaveProperty('description', testProduct.description);
    expect(productData).toHaveProperty('price', testProduct.price);
    expect(productData).toHaveProperty('categoryId', testProduct.categoryId);
    expect(productData).toHaveProperty('stock', testProduct.stock);
  });

  test('deve filtrar produtos por categoria', async ({ request }) => {
    // Garantir que temos um ID de categoria
    expect(categoryId).toBeDefined();

    const response = await request.get(`/api/products?categoryId=${categoryId}`);

    expect(response.status()).toBe(200);

    const productsData = await response.json();
    expect(Array.isArray(productsData.products)).toBeTruthy();

    // Verificar se todos os produtos retornados pertencem à categoria
    for (const product of productsData.products) {
      expect(product.categoryId).toBe(categoryId);
    }

    // Verificar se nosso produto de teste está na lista
    const testProductInList = productsData.products.find((p: any) => p.id === productId);
    expect(testProductInList).toBeDefined();
  });

  test('deve pesquisar produtos por nome', async ({ request }) => {
    // Usar parte do nome do produto de teste
    const searchTerm = testProduct.name.substring(0, 10);

    const response = await request.get(`/api/products?search=${encodeURIComponent(searchTerm)}`);

    expect(response.status()).toBe(200);

    const productsData = await response.json();
    expect(Array.isArray(productsData.products)).toBeTruthy();

    // Verificar se nosso produto de teste está na lista
    const testProductInList = productsData.products.find((p: any) => p.id === productId);
    expect(testProductInList).toBeDefined();
  });

  test('admin deve atualizar um produto existente', async ({ request }) => {
    // Garantir que temos um token de admin e ID de produto
    expect(adminToken).toBeDefined();
    expect(productId).toBeDefined();

    const updatedData = {
      name: 'Produto Atualizado',
      description: 'Descrição atualizada do produto',
      price: 129.99,
    };

    const response = await request.put(`/api/products/${productId}`, {
      headers: {
        Authorization: `Bearer ${adminToken}`,
      },
      data: updatedData,
    });

    expect(response.status()).toBe(200);

    const productData = await response.json();
    expect(productData).toHaveProperty('id', productId);
    expect(productData).toHaveProperty('name', updatedData.name);
    expect(productData).toHaveProperty('description', updatedData.description);
    expect(productData).toHaveProperty('price', updatedData.price);

    // Verificar se a atualização foi persistida
    const getResponse = await request.get(`/api/products/${productId}`);
    const getProductData = await getResponse.json();
    expect(getProductData).toHaveProperty('name', updatedData.name);
  });

  test('admin deve atualizar o estoque de um produto', async ({ request }) => {
    // Garantir que temos um token de admin e ID de produto
    expect(adminToken).toBeDefined();
    expect(productId).toBeDefined();

    const newStock = 50;

    const response = await request.patch(`/api/products/${productId}/stock`, {
      headers: {
        Authorization: `Bearer ${adminToken}`,
      },
      data: {
        stock: newStock,
      },
    });

    expect(response.status()).toBe(200);

    const productData = await response.json();
    expect(productData).toHaveProperty('stock', newStock);

    // Verificar se a atualização foi persistida
    const getResponse = await request.get(`/api/products/${productId}`);
    const getProductData = await getResponse.json();
    expect(getProductData).toHaveProperty('stock', newStock);
  });

  test('admin deve desativar e reativar um produto', async ({ request }) => {
    // Garantir que temos um token de admin e ID de produto
    expect(adminToken).toBeDefined();
    expect(productId).toBeDefined();

    // Desativar produto
    const deactivateResponse = await request.patch(`/api/products/${productId}/status`, {
      headers: {
        Authorization: `Bearer ${adminToken}`,
      },
      data: {
        isActive: false,
      },
    });

    expect(deactivateResponse.status()).toBe(200);

    const deactivatedData = await deactivateResponse.json();
    expect(deactivatedData).toHaveProperty('isActive', false);

    // Verificar que o produto não aparece na listagem pública
    const listResponse = await request.get('/api/products');
    const productsData = await listResponse.json();
    const deactivatedProduct = productsData.products.find((p: any) => p.id === productId);
    expect(deactivatedProduct).toBeUndefined();

    // Reativar produto
    const activateResponse = await request.patch(`/api/products/${productId}/status`, {
      headers: {
        Authorization: `Bearer ${adminToken}`,
      },
      data: {
        isActive: true,
      },
    });

    expect(activateResponse.status()).toBe(200);

    const activatedData = await activateResponse.json();
    expect(activatedData).toHaveProperty('isActive', true);

    // Verificar que o produto aparece na listagem pública novamente
    const newListResponse = await request.get('/api/products');
    const newProductsData = await newListResponse.json();
    const reactivatedProduct = newProductsData.products.find((p: any) => p.id === productId);
    expect(reactivatedProduct).toBeDefined();
  });
});
