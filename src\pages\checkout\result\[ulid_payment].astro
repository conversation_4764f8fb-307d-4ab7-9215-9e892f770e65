---
import { queryHelper } from '@db/queryHelper';
import Layout from '@layouts/Layout.astro';
import { PaymentStatus, PaymentType } from '@services/paymentService';

// Obter ID do pagamento da URL
const { ulid_payment } = Astro.params;

// Verificar se o ID do pagamento foi fornecido
if (!ulid_payment) {
  return Astro.redirect('/404');
}

// Buscar dados do pagamento
const paymentResult = await queryHelper.queryOne(
  `SELECT p.*, pt.type as payment_type, o.total as order_total, u.name as user_name, u.email as user_email
   FROM tab_payment p
   JOIN tab_payment_type pt ON p.ulid_payment_type = pt.ulid_payment_type
   JOIN tab_order o ON p.ulid_order = o.ulid_order
   JOIN tab_user u ON o.ulid_user = u.ulid_user
   WHERE p.ulid_payment = $1`,
  [ulid_payment]
);

// Verificar se o pagamento existe
if (!paymentResult) {
  return Astro.redirect('/404');
}

// Mapear status para texto
const statusText = {
  [PaymentStatus.PENDING]: 'Pendente',
  [PaymentStatus.APPROVED]: 'Aprovado',
  [PaymentStatus.REJECTED]: 'Rejeitado',
  [PaymentStatus.REFUNDED]: 'Reembolsado',
  [PaymentStatus.CANCELLED]: 'Cancelado',
};

// Mapear tipo de pagamento para texto
const paymentTypeText = {
  [PaymentType.PIX]: 'PIX',
  [PaymentType.CREDIT_CARD]: 'Cartão de Crédito',
  [PaymentType.BILLET]: 'Boleto',
};

// Determinar classe CSS para o status
const statusClass = {
  [PaymentStatus.PENDING]: 'alert-warning',
  [PaymentStatus.APPROVED]: 'alert-success',
  [PaymentStatus.REJECTED]: 'alert-error',
  [PaymentStatus.REFUNDED]: 'alert-info',
  [PaymentStatus.CANCELLED]: 'alert-error',
};
---

<Layout title="Resultado do Pagamento">
  <div class="container mx-auto p-4">
    <h1 class="text-2xl font-bold mb-4">Resultado do Pagamento</h1>
    
    <div class="bg-white rounded-lg shadow-md p-6 max-w-2xl mx-auto">
      <div class={`alert ${statusClass[paymentResult.cod_status]} mb-6`}>
        <span class="font-bold">Status: {statusText[paymentResult.cod_status]}</span>
      </div>
      
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <h2 class="text-xl font-semibold mb-4">Detalhes do Pagamento</h2>
          
          <div class="mb-4">
            <p class="text-sm text-gray-500">ID do Pagamento</p>
            <p class="font-medium">{ulid_payment}</p>
          </div>
          
          <div class="mb-4">
            <p class="text-sm text-gray-500">Método de Pagamento</p>
            <p class="font-medium">{paymentTypeText[paymentResult.payment_type.toLowerCase()] || paymentResult.payment_type}</p>
          </div>
          
          <div class="mb-4">
            <p class="text-sm text-gray-500">Valor</p>
            <p class="font-medium">R$ {paymentResult.value.toFixed(2)}</p>
          </div>
          
          <div class="mb-4">
            <p class="text-sm text-gray-500">Data</p>
            <p class="font-medium">{new Date(paymentResult.created_at).toLocaleString()}</p>
          </div>
          
          {paymentResult.external_id && (
            <div class="mb-4">
              <p class="text-sm text-gray-500">ID Externo</p>
              <p class="font-medium">{paymentResult.external_id}</p>
            </div>
          )}
        </div>
        
        <div>
          <h2 class="text-xl font-semibold mb-4">Dados do Cliente</h2>
          
          <div class="mb-4">
            <p class="text-sm text-gray-500">Nome</p>
            <p class="font-medium">{paymentResult.user_name}</p>
          </div>
          
          <div class="mb-4">
            <p class="text-sm text-gray-500">Email</p>
            <p class="font-medium">{paymentResult.user_email}</p>
          </div>
        </div>
      </div>
      
      {paymentResult.payment_type.toLowerCase() === PaymentType.PIX && paymentResult.cod_status === PaymentStatus.PENDING && (
        <div class="mt-6 p-4 border border-gray-200 rounded-lg">
          <h3 class="text-lg font-semibold mb-2">Instruções para Pagamento PIX</h3>
          <p class="mb-4">Escaneie o QR Code abaixo com o aplicativo do seu banco para realizar o pagamento:</p>
          
          <div class="flex justify-center mb-4">
            <img src="/placeholder-qrcode.png" alt="QR Code PIX" class="w-48 h-48" />
          </div>
          
          <p class="text-sm text-gray-500 text-center">O pagamento será confirmado automaticamente após a transferência.</p>
        </div>
      )}
      
      {paymentResult.payment_type.toLowerCase() === PaymentType.BILLET && paymentResult.cod_status === PaymentStatus.PENDING && (
        <div class="mt-6 p-4 border border-gray-200 rounded-lg">
          <h3 class="text-lg font-semibold mb-2">Instruções para Pagamento do Boleto</h3>
          <p class="mb-4">O boleto foi enviado para o seu email. Você também pode acessá-lo pelo link abaixo:</p>
          
          <div class="flex justify-center mb-4">
            <a href="#" class="btn btn-primary">Visualizar Boleto</a>
          </div>
          
          <p class="text-sm text-gray-500 text-center">O pagamento será confirmado em até 3 dias úteis após o pagamento.</p>
        </div>
      )}
      
      <div class="mt-6 flex justify-center">
        <a href="/" class="btn btn-outline">Voltar para a Página Inicial</a>
      </div>
    </div>
  </div>
</Layout>
