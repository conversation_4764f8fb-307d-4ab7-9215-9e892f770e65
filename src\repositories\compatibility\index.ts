/**
 * Camada de Compatibilidade para Repositórios
 *
 * Este arquivo exporta implementações de compatibilidade para manter a retrocompatibilidade
 * com o código existente que utiliza a estrutura antiga de repositórios.
 *
 * @deprecated Use a nova estrutura de repositórios em src/repositories
 */

// Exportar repositórios de compatibilidade
export * from './productRepository';
export * from './userRepository';
export * from './categoryRepository';
export * from './orderRepository';
export * from './orderItemRepository';
export * from './invoiceRepository';
export * from './invoiceItemRepository';
export * from './paymentRepository';

// Aviso de depreciação
console.warn(
  'DEPRECATED: A camada de compatibilidade em src/repositories/compatibility está sendo usada. ' +
    'Esta camada será removida em versões futuras. ' +
    'Use a nova estrutura de repositórios em src/repositories.'
);
