import { DATABASE_URL } from 'astro:env/server';
import { logger } from '@utils/logger';
import { Pool, type QueryResult } from 'pg';

/**
 * Interface para configuração de consulta
 */
export interface QueryConfig {
  /**
   * Se deve usar cache para esta consulta
   * @default false
   */
  useCache?: boolean;

  /**
   * Tempo de vida em cache em segundos
   * @default 300 (5 minutos)
   */
  cacheTTL?: number;

  /**
   * Tags para categorizar a consulta em cache
   */
  cacheTags?: string[];

  /**
   * Se é uma consulta de leitura (SELECT)
   * @default auto-detected
   */
  isReadQuery?: boolean;
}

let instance: Pool;

/**
 * Configuração do pool de conexões
 */
const POOL_CONFIG = {
  // Número máximo de clientes no pool
  max: 20,
  // Tempo máximo (em ms) que um cliente pode ficar ocioso no pool antes de ser encerrado
  idleTimeoutMillis: 30000,
  // Tempo máximo (em ms) para tentar conectar antes de falhar
  connectionTimeoutMillis: 5000,
};

/**
 * Obtém a instância do pool de conexões
 * @returns Pool de conexões
 */
function getInstance(): Pool {
  if (!instance) {
    // Criar pool com configurações otimizadas
    instance = new Pool({
      connectionString: DATABASE_URL,
      ...POOL_CONFIG,
    });

    // Configurar eventos de log
    instance.on('error', (err) => {
      logger.error('Erro inesperado no pool de conexões PostgreSQL:', err);
    });

    instance.on('connect', () => {
      logger.debug('Nova conexão estabelecida com o PostgreSQL');
    });

    // Monitorar clientes ociosos
    instance.on('remove', () => {
      logger.debug('Conexão removida do pool por inatividade');
    });

    // Configurar verificação periódica do pool
    setInterval(async () => {
      try {
        const poolStatus = await getPoolStatus();

        // Registrar status do pool a cada minuto
        logger.debug('Status do pool de conexões:', poolStatus);

        // Verificar se o pool está sobrecarregado
        if (poolStatus.waiting > 5) {
          logger.warn('Pool de conexões sobrecarregado:', poolStatus);
        }
      } catch (error) {
        logger.error('Erro ao verificar status do pool:', error);
      }
    }, 60000); // A cada minuto
  }

  return instance;
}

/**
 * Obtém o status atual do pool de conexões
 * @returns Status do pool
 */
async function getPoolStatus(): Promise<{
  total: number;
  idle: number;
  active: number;
  waiting: number;
}> {
  try {
    const pool = getInstance();

    // Executar consulta para obter estatísticas do pool
    const result = await pool.query(
      'SELECT * FROM pg_stat_activity WHERE application_name = current_application_name'
    );

    // Contar conexões ativas
    const active = result.rows.length;

    // Obter estatísticas do pool
    const idleCount = pool.idleCount;
    const totalCount = pool.totalCount;
    const waitingCount = pool.waitingCount || 0;

    return {
      total: totalCount,
      idle: idleCount,
      active,
      waiting: waitingCount,
    };
  } catch (error) {
    logger.error('Erro ao obter status do pool:', error);

    // Retornar valores padrão em caso de erro
    return {
      total: 0,
      idle: 0,
      active: 0,
      waiting: 0,
    };
  }
}

/**
 * Executa uma consulta SQL diretamente no banco de dados
 * @param text - Consulta SQL
 * @param params - Parâmetros da consulta
 * @returns Resultado da consulta
 */
async function executeQuery(text: string, params?: unknown[]): Promise<QueryResult> {
  const pool: Pool = getInstance();
  const client = await pool.connect();

  try {
    const startTime = Date.now();
    const res = await client.query(text, params);
    const duration = Date.now() - startTime;

    // Log de performance para consultas lentas (> 100ms)
    if (duration > 100) {
      logger.warn(`Consulta lenta (${duration}ms): ${text.substring(0, 100)}...`);
    }

    return res;
  } catch (error) {
    logger.error('Erro ao executar consulta SQL:', error, { query: text });
    throw error; // Re-throw the error for further handling
  } finally {
    client.release();
  }
}

/**
 * Executa uma consulta SQL com suporte a cache
 * @param text - Consulta SQL
 * @param params - Parâmetros da consulta
 * @param config - Configuração da consulta
 * @returns Resultado da consulta
 */
async function query(
  text: string,
  params?: unknown[],
  config: QueryConfig = {}
): Promise<QueryResult> {
  // Verificar se é uma consulta de leitura (se não especificado)
  if (config.isReadQuery === undefined) {
    config.isReadQuery = isReadQuery(text);
  }

  // Se o cache está habilitado e é uma consulta de leitura, usar queryCacheService
  if (config.useCache && config.isReadQuery) {
    try {
      // Importar dinamicamente para evitar dependência circular
      const { queryCacheService } = await import('@services/queryCacheService');

      return await queryCacheService.queryRead(text, params as any[], config.cacheTTL);
    } catch (error) {
      logger.error('Erro ao usar cache de consulta:', error);
      // Fallback para execução direta
      return await executeQuery(text, params);
    }
  } else if (!config.isReadQuery) {
    try {
      // Para consultas de escrita, invalidar caches relacionados
      const { queryCacheService } = await import('@services/queryCacheService');

      return await queryCacheService.queryWrite(text, params as any[]);
    } catch (error) {
      logger.error('Erro ao invalidar cache após escrita:', error);
      // Fallback para execução direta
      return await executeQuery(text, params);
    }
  }

  // Execução direta sem cache
  return await executeQuery(text, params);
}

/**
 * Verifica se uma consulta SQL é de leitura (SELECT)
 * @param query - Consulta SQL
 * @returns Verdadeiro se for uma consulta de leitura
 */
function isReadQuery(query: string): boolean {
  const normalizedQuery = query.trim().toLowerCase();
  return normalizedQuery.startsWith('select') || normalizedQuery.startsWith('with');
}

function base32Encode(num: number): string {
  const base32Chars = '0123456789ABCDEFGHJKMNPQRSTVWXYZ';
  let encoded = '';
  let numAux = num;

  while (numAux > 0) {
    encoded = base32Chars[numAux % 32] + encoded;
    numAux = Math.floor(numAux / 32);
  }

  return encoded.padStart(10, '0'); // Preenche com zeros à esquerda para garantir 10 caracteres
}

function generateULID(): string {
  const timestamp = Date.now();
  const timestampPart = base32Encode(Math.floor(timestamp / 1000)); // Converte para segundos

  let randomPart = '';
  for (let i = 0; i < 16; i++) {
    const randomValue = Math.floor(Math.random() * 32); // Gera um valor aleatório entre 0 e 31
    randomPart += '0123456789ABCDEFGHJKMNPQRSTVWXYZ'[randomValue];
  }

  return timestampPart + randomPart;
}

export const pgHelper = {
  generateULID,
  query,
  executeQuery,
  isReadQuery,
  getPoolStatus,

  /**
   * Executa uma consulta SQL com cache
   * @param text - Consulta SQL
   * @param params - Parâmetros da consulta
   * @param ttl - Tempo de vida em cache em segundos
   * @returns Resultado da consulta
   */
  queryCached: (text: string, params?: unknown[], ttl?: number): Promise<QueryResult> => {
    return query(text, params, { useCache: true, cacheTTL: ttl });
  },

  /**
   * Executa uma consulta SQL de leitura com cache
   * @param text - Consulta SQL
   * @param params - Parâmetros da consulta
   * @param ttl - Tempo de vida em cache em segundos
   * @returns Resultado da consulta
   */
  queryRead: (text: string, params?: unknown[], ttl?: number): Promise<QueryResult> => {
    return query(text, params, {
      useCache: true,
      isReadQuery: true,
      cacheTTL: ttl,
    });
  },

  /**
   * Executa uma consulta SQL de escrita e invalida caches relacionados
   * @param text - Consulta SQL
   * @param params - Parâmetros da consulta
   * @returns Resultado da consulta
   */
  queryWrite: (text: string, params?: unknown[]): Promise<QueryResult> => {
    return query(text, params, { isReadQuery: false });
  },
};
