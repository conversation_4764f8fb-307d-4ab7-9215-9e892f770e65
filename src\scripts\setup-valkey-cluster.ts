/**
 * Script para configuração de cluster Valkey
 * 
 * Este script configura um cluster Valkey para alta disponibilidade,
 * incluindo a criação de nós master e replica, configuração de slots
 * e verificação do estado do cluster.
 */

import { createClient } from 'valkey';
import { getClusterConfig, ClusterNodeConfig } from '@config/cache/cluster.config';
import { getPersistenceConfig, createPersistenceDirectories } from '@config/cache/persistence.config';
import { logger } from '@utils/logger';
import { exec } from 'child_process';
import { promisify } from 'util';
import * as fs from 'fs';
import * as path from 'path';

// Promisificar funções
const execAsync = promisify(exec);
const writeFileAsync = promisify(fs.writeFile);
const mkdirAsync = promisify(fs.mkdir);

/**
 * Verifica se o Valkey está instalado
 */
async function checkValkeyInstallation(): Promise<boolean> {
  try {
    const { stdout } = await execAsync('valkey-cli --version');
    logger.info(`Valkey instalado: ${stdout.trim()}`);
    return true;
  } catch (error) {
    logger.error('Valkey não está instalado ou não está no PATH');
    return false;
  }
}

/**
 * Gera arquivo de configuração para um nó
 * @param node Configuração do nó
 * @param index Índice do nó
 */
async function generateNodeConfig(node: ClusterNodeConfig, index: number): Promise<string> {
  const clusterConfig = getClusterConfig();
  const persistenceConfig = getPersistenceConfig();
  
  // Criar diretório para o nó
  const nodeDir = path.join(process.cwd(), 'cluster', `node-${index}`);
  await mkdirAsync(nodeDir, { recursive: true });
  
  // Criar diretório de dados
  const dataDir = path.join(nodeDir, 'data');
  await mkdirAsync(dataDir, { recursive: true });
  
  // Gerar configuração
  const config = [
    '# Configuração gerada automaticamente',
    `# Nó ${index} - ${node.type}`,
    '',
    '# Configurações de rede',
    `bind ${node.host}`,
    'protected-mode yes',
    `port ${node.port}`,
    'tcp-backlog 511',
    'timeout 0',
    'tcp-keepalive 300',
    '',
    '# Configurações gerais',
    'daemonize yes',
    'supervised auto',
    `pidfile ${path.join(nodeDir, 'valkey.pid')}`,
    'loglevel notice',
    `logfile ${path.join(nodeDir, 'valkey.log')}`,
    'databases 16',
    '',
    '# Configurações de cluster',
    'cluster-enabled yes',
    `cluster-config-file ${path.join(nodeDir, 'nodes.conf')}`,
    `cluster-node-timeout ${clusterConfig.nodeTimeout}`,
    'cluster-replica-validity-factor 10',
    `cluster-replica-no-failover ${clusterConfig.failover.enabled ? 'no' : 'yes'}`,
    'cluster-migration-barrier 1',
    'cluster-require-full-coverage no',
    '',
    '# Configurações de persistência',
    ...persistenceConfig.type === 'none' ? ['save ""'] : [
      ...persistenceConfig.rdb.saveSettings.map(([seconds, changes]) => `save ${seconds} ${changes}`),
      `stop-writes-on-bgsave-error ${persistenceConfig.rdb.stopWritesOnError ? 'yes' : 'no'}`,
      `rdbcompression ${persistenceConfig.rdb.compression ? 'yes' : 'no'}`,
      `rdbchecksum ${persistenceConfig.rdb.checksum ? 'yes' : 'no'}`,
      `dbfilename ${persistenceConfig.rdb.filename}`,
      `dir ${dataDir}`
    ],
    '',
    '# Configurações de AOF',
    `appendonly ${persistenceConfig.aof.enabled ? 'yes' : 'no'}`,
    `appendfilename "${persistenceConfig.aof.filename}"`,
    `appendfsync ${persistenceConfig.aof.syncStrategy}`,
    `no-appendfsync-on-rewrite ${persistenceConfig.aof.autoRewrite ? 'yes' : 'no'}`,
    `auto-aof-rewrite-percentage ${persistenceConfig.aof.rewritePercentage}`,
    `auto-aof-rewrite-min-size ${persistenceConfig.aof.rewriteMinSize}`,
    '',
    '# Configurações de segurança',
    clusterConfig.password ? `requirepass "${clusterConfig.password}"` : '# Sem senha configurada',
    clusterConfig.password ? `masterauth "${clusterConfig.password}"` : '# Sem senha configurada',
  ];
  
  // Salvar configuração
  const configPath = path.join(nodeDir, 'valkey.conf');
  await writeFileAsync(configPath, config.join('\n'));
  
  logger.info(`Configuração gerada para nó ${index} em ${configPath}`);
  
  return configPath;
}

/**
 * Inicia um nó do cluster
 * @param configPath Caminho do arquivo de configuração
 */
async function startNode(configPath: string): Promise<void> {
  try {
    await execAsync(`valkey-server ${configPath}`);
    logger.info(`Nó iniciado com configuração ${configPath}`);
  } catch (error) {
    logger.error(`Erro ao iniciar nó com configuração ${configPath}:`, error);
    throw error;
  }
}

/**
 * Cria o cluster
 * @param nodes Lista de nós
 */
async function createCluster(nodes: ClusterNodeConfig[]): Promise<void> {
  try {
    // Construir comando de criação de cluster
    const nodeAddresses = nodes.map((node, index) => {
      const role = node.type === 'master' ? 'master' : `slave ${node.masterId}`;
      return `${node.host}:${node.port}@${16379 + index} ${role}`;
    });
    
    const command = `valkey-cli --cluster create ${nodeAddresses.join(' ')} --cluster-replicas ${nodes.filter(n => n.type === 'replica').length / nodes.filter(n => n.type === 'master').length} --cluster-yes`;
    
    logger.info(`Executando comando de criação de cluster: ${command}`);
    
    const { stdout, stderr } = await execAsync(command);
    
    logger.info('Cluster criado com sucesso:', stdout);
    
    if (stderr) {
      logger.warn('Avisos durante criação do cluster:', stderr);
    }
  } catch (error) {
    logger.error('Erro ao criar cluster:', error);
    throw error;
  }
}

/**
 * Verifica o estado do cluster
 */
async function checkClusterStatus(): Promise<void> {
  try {
    const { stdout } = await execAsync('valkey-cli cluster info');
    logger.info('Estado do cluster:', stdout);
    
    const { stdout: nodesOutput } = await execAsync('valkey-cli cluster nodes');
    logger.info('Nós do cluster:', nodesOutput);
  } catch (error) {
    logger.error('Erro ao verificar estado do cluster:', error);
    throw error;
  }
}

/**
 * Configura o cluster Valkey
 */
async function setupValkeyCluster(): Promise<void> {
  try {
    logger.info('Iniciando configuração do cluster Valkey');
    
    // Verificar instalação do Valkey
    const isInstalled = await checkValkeyInstallation();
    
    if (!isInstalled) {
      logger.error('Valkey não está instalado. Instale o Valkey antes de continuar.');
      process.exit(1);
    }
    
    // Obter configuração do cluster
    const clusterConfig = getClusterConfig();
    
    if (!clusterConfig.enabled) {
      logger.info('Cluster não está habilitado na configuração. Pulando configuração.');
      return;
    }
    
    // Criar diretórios para persistência
    await createPersistenceDirectories();
    
    // Gerar configurações para cada nó
    const configPaths: string[] = [];
    
    for (let i = 0; i < clusterConfig.nodes.length; i++) {
      const configPath = await generateNodeConfig(clusterConfig.nodes[i], i);
      configPaths.push(configPath);
    }
    
    // Iniciar nós
    for (const configPath of configPaths) {
      await startNode(configPath);
    }
    
    // Aguardar inicialização dos nós
    logger.info('Aguardando inicialização dos nós...');
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    // Criar cluster
    await createCluster(clusterConfig.nodes);
    
    // Verificar estado do cluster
    await checkClusterStatus();
    
    logger.info('Configuração do cluster Valkey concluída com sucesso');
  } catch (error) {
    logger.error('Erro durante configuração do cluster Valkey:', error);
    throw error;
  }
}

/**
 * Função principal
 */
async function main(): Promise<void> {
  try {
    await setupValkeyCluster();
    process.exit(0);
  } catch (error) {
    logger.error('Erro ao executar script:', error);
    process.exit(1);
  }
}

// Executar script
if (require.main === module) {
  main();
}

export { setupValkeyCluster };
