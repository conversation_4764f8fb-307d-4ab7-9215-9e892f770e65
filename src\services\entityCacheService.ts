/**
 * Serviço de Cache de Entidades
 * 
 * Este serviço gerencia o cache de entidades do sistema,
 * como usuários, produtos, categorias, etc.
 */

import { layeredCacheService, CacheOptions } from '@services/layeredCacheService';
import { strategicCacheService } from '@services/strategicCacheService';
import { logger } from '@utils/logger';
import { EventEmitter } from 'events';

/**
 * Tipos de entidades que podem ser armazenadas em cache
 */
export enum EntityType {
  USER = 'user',
  PRODUCT = 'product',
  CATEGORY = 'category',
  ORDER = 'order',
  PAYMENT = 'payment',
  PERMISSION = 'permission',
  ROLE = 'role',
  RESOURCE = 'resource',
  CONTENT = 'content',
  SETTING = 'setting'
}

/**
 * Configuração de cache por tipo de entidade
 */
export interface EntityCacheConfig {
  /**
   * Tempo de vida em segundos
   */
  ttl: number;
  
  /**
   * Se deve atualizar TTL ao acessar
   */
  refreshOnAccess: boolean;
  
  /**
   * Tags para categorização
   */
  tags: string[];
  
  /**
   * Prioridade de cache (1-10)
   */
  priority: number;
  
  /**
   * Se deve usar cache em memória
   */
  useMemoryCache: boolean;
  
  /**
   * Se deve comprimir dados
   */
  compress: boolean;
  
  /**
   * Eventos que invalidam o cache
   */
  invalidationEvents: string[];
}

/**
 * Configurações padrão por tipo de entidade
 */
export const entityCacheConfigs: Record<EntityType, EntityCacheConfig> = {
  [EntityType.USER]: {
    ttl: 30 * 60, // 30 minutos
    refreshOnAccess: true,
    tags: ['user', 'auth'],
    priority: 8,
    useMemoryCache: true,
    compress: false,
    invalidationEvents: ['user:update', 'user:delete', 'user:logout']
  },
  [EntityType.PRODUCT]: {
    ttl: 60 * 60, // 1 hora
    refreshOnAccess: false,
    tags: ['product', 'catalog'],
    priority: 7,
    useMemoryCache: true,
    compress: true,
    invalidationEvents: ['product:update', 'product:delete', 'product:price']
  },
  [EntityType.CATEGORY]: {
    ttl: 2 * 60 * 60, // 2 horas
    refreshOnAccess: false,
    tags: ['category', 'catalog'],
    priority: 9,
    useMemoryCache: true,
    compress: false,
    invalidationEvents: ['category:update', 'category:delete']
  },
  [EntityType.ORDER]: {
    ttl: 15 * 60, // 15 minutos
    refreshOnAccess: true,
    tags: ['order', 'transaction'],
    priority: 6,
    useMemoryCache: false,
    compress: true,
    invalidationEvents: ['order:update', 'order:delete', 'order:status']
  },
  [EntityType.PAYMENT]: {
    ttl: 15 * 60, // 15 minutos
    refreshOnAccess: true,
    tags: ['payment', 'transaction'],
    priority: 6,
    useMemoryCache: false,
    compress: true,
    invalidationEvents: ['payment:update', 'payment:status']
  },
  [EntityType.PERMISSION]: {
    ttl: 30 * 60, // 30 minutos
    refreshOnAccess: true,
    tags: ['permission', 'auth'],
    priority: 9,
    useMemoryCache: true,
    compress: false,
    invalidationEvents: ['permission:update', 'role:update']
  },
  [EntityType.ROLE]: {
    ttl: 30 * 60, // 30 minutos
    refreshOnAccess: true,
    tags: ['role', 'auth'],
    priority: 9,
    useMemoryCache: true,
    compress: false,
    invalidationEvents: ['role:update', 'role:delete']
  },
  [EntityType.RESOURCE]: {
    ttl: 60 * 60, // 1 hora
    refreshOnAccess: true,
    tags: ['resource', 'auth'],
    priority: 8,
    useMemoryCache: true,
    compress: false,
    invalidationEvents: ['resource:update', 'resource:delete']
  },
  [EntityType.CONTENT]: {
    ttl: 2 * 60 * 60, // 2 horas
    refreshOnAccess: false,
    tags: ['content'],
    priority: 7,
    useMemoryCache: true,
    compress: true,
    invalidationEvents: ['content:update', 'content:delete', 'content:publish']
  },
  [EntityType.SETTING]: {
    ttl: 24 * 60 * 60, // 24 horas
    refreshOnAccess: false,
    tags: ['setting', 'config'],
    priority: 10,
    useMemoryCache: true,
    compress: false,
    invalidationEvents: ['setting:update']
  }
};

/**
 * Emissor de eventos para invalidação de cache
 */
export const entityCacheEvents = new EventEmitter();

// Configurar limite de listeners para evitar vazamentos de memória
entityCacheEvents.setMaxListeners(100);

/**
 * Serviço de cache de entidades
 */
export const entityCacheService = {
  /**
   * Prefixo para chaves de cache
   */
  CACHE_PREFIX: 'entity:',
  
  /**
   * Inicializa o serviço de cache de entidades
   */
  initialize(): void {
    // Registrar listeners para eventos de invalidação
    this.setupInvalidationListeners();
    
    logger.info('Serviço de cache de entidades inicializado');
  },
  
  /**
   * Configura listeners para eventos de invalidação
   */
  setupInvalidationListeners(): void {
    // Para cada tipo de entidade
    Object.values(EntityType).forEach(type => {
      const config = entityCacheConfigs[type];
      
      // Para cada evento de invalidação
      config.invalidationEvents.forEach(event => {
        // Registrar listener para o evento
        entityCacheEvents.on(event, (data: any) => {
          // Extrair ID da entidade dos dados do evento
          const entityId = data?.id || data?.entityId || '*';
          
          // Invalidar cache para a entidade
          this.invalidate(type, entityId).catch(error => {
            logger.error(`Erro ao invalidar cache para ${type}:${entityId} após evento ${event}:`, error);
          });
          
          logger.debug(`Cache invalidado para ${type}:${entityId} após evento ${event}`);
        });
      });
    });
  },
  
  /**
   * Gera chave de cache para uma entidade
   * @param type Tipo de entidade
   * @param id ID da entidade
   * @returns Chave de cache
   */
  getCacheKey(type: EntityType, id: string): string {
    return `${this.CACHE_PREFIX}${type}:${id}`;
  },
  
  /**
   * Obtém uma entidade do cache
   * @param type Tipo de entidade
   * @param id ID da entidade
   * @returns Entidade ou null se não encontrada
   */
  async get<T>(type: EntityType, id: string): Promise<T | null> {
    try {
      // Obter configuração para o tipo
      const config = entityCacheConfigs[type];
      
      // Gerar chave de cache
      const cacheKey = this.getCacheKey(type, id);
      
      // Configurar opções de cache
      const options: CacheOptions = {
        memoryOnly: !config.useMemoryCache,
        compress: config.compress,
        tags: config.tags
      };
      
      // Buscar do cache
      const cachedEntity = await layeredCacheService.get<T>(cacheKey, options);
      
      // Se encontrado e deve atualizar TTL ao acessar
      if (cachedEntity && config.refreshOnAccess) {
        await layeredCacheService.expire(cacheKey, config.ttl);
      }
      
      return cachedEntity;
    } catch (error) {
      logger.error(`Erro ao obter entidade ${type}:${id} do cache:`, error);
      return null;
    }
  },
  
  /**
   * Armazena uma entidade no cache
   * @param type Tipo de entidade
   * @param id ID da entidade
   * @param data Dados da entidade
   * @returns Sucesso da operação
   */
  async set<T>(type: EntityType, id: string, data: T): Promise<boolean> {
    try {
      // Obter configuração para o tipo
      const config = entityCacheConfigs[type];
      
      // Gerar chave de cache
      const cacheKey = this.getCacheKey(type, id);
      
      // Configurar opções de cache
      const options: CacheOptions = {
        memoryOnly: !config.useMemoryCache,
        compress: config.compress,
        tags: config.tags
      };
      
      // Armazenar no cache
      return await layeredCacheService.set<T>(cacheKey, data, config.ttl, options);
    } catch (error) {
      logger.error(`Erro ao armazenar entidade ${type}:${id} no cache:`, error);
      return false;
    }
  },
  
  /**
   * Invalida o cache de uma entidade
   * @param type Tipo de entidade
   * @param id ID da entidade ou * para todas
   * @returns Número de chaves invalidadas
   */
  async invalidate(type: EntityType, id: string = '*'): Promise<number> {
    try {
      // Se ID for *, invalidar todas as entidades do tipo
      if (id === '*') {
        const pattern = `${this.CACHE_PREFIX}${type}:*`;
        return await layeredCacheService.invalidateByPattern(pattern);
      }
      
      // Gerar chave de cache
      const cacheKey = this.getCacheKey(type, id);
      
      // Remover do cache
      await layeredCacheService.del(cacheKey);
      
      return 1;
    } catch (error) {
      logger.error(`Erro ao invalidar cache para ${type}:${id}:`, error);
      return 0;
    }
  },
  
  /**
   * Invalida o cache por tag
   * @param tag Tag para invalidação
   * @returns Número de chaves invalidadas
   */
  async invalidateByTag(tag: string): Promise<number> {
    try {
      return await layeredCacheService.invalidateByTag(tag);
    } catch (error) {
      logger.error(`Erro ao invalidar cache por tag ${tag}:`, error);
      return 0;
    }
  },
  
  /**
   * Emite um evento para invalidar cache
   * @param event Nome do evento
   * @param data Dados do evento
   */
  emitInvalidationEvent(event: string, data: any): void {
    entityCacheEvents.emit(event, data);
    logger.debug(`Evento de invalidação emitido: ${event}`, data);
  },
  
  /**
   * Pré-carrega entidades frequentemente acessadas
   * @param type Tipo de entidade
   * @param fetchFn Função para buscar entidades
   * @param limit Limite de entidades a pré-carregar
   * @returns Número de entidades pré-carregadas
   */
  async preload<T>(
    type: EntityType,
    fetchFn: () => Promise<T[]>,
    limit: number = 100
  ): Promise<number> {
    try {
      logger.info(`Pré-carregando entidades do tipo ${type}`);
      
      // Buscar entidades
      const entities = await fetchFn();
      
      // Limitar número de entidades
      const limitedEntities = entities.slice(0, limit);
      
      // Armazenar cada entidade no cache
      let count = 0;
      
      for (const entity of limitedEntities) {
        // Extrair ID da entidade (assumindo que tem uma propriedade id)
        const entityId = (entity as any).id;
        
        if (!entityId) {
          logger.warn(`Entidade sem ID encontrada durante pré-carregamento de ${type}`);
          continue;
        }
        
        // Armazenar no cache
        const success = await this.set(type, entityId, entity);
        
        if (success) {
          count++;
        }
      }
      
      logger.info(`${count} entidades do tipo ${type} pré-carregadas com sucesso`);
      
      return count;
    } catch (error) {
      logger.error(`Erro ao pré-carregar entidades do tipo ${type}:`, error);
      return 0;
    }
  },
  
  /**
   * Obtém estatísticas do cache de entidades
   * @returns Estatísticas por tipo de entidade
   */
  async getStats(): Promise<Record<string, any>> {
    try {
      const stats: Record<string, any> = {};
      
      // Para cada tipo de entidade
      for (const type of Object.values(EntityType)) {
        // Contar entidades em cache
        const pattern = `${this.CACHE_PREFIX}${type}:*`;
        const keys = await layeredCacheService.invalidateByPattern(pattern);
        
        stats[type] = {
          count: keys,
          config: entityCacheConfigs[type]
        };
      }
      
      return stats;
    } catch (error) {
      logger.error('Erro ao obter estatísticas do cache de entidades:', error);
      return {};
    }
  }
};
