/**
 * Configuração de cluster para o Valkey
 *
 * Este arquivo contém as configurações para o cluster Valkey,
 * incluindo definições de nós master e replica, failover automático
 * e monitoramento de estado do cluster.
 */

import { logger } from '@utils/logger';

/**
 * Interface para configuração de nó do cluster
 */
export interface ClusterNodeConfig {
  /**
   * Host do nó
   */
  host: string;

  /**
   * Porta do nó
   */
  port: number;

  /**
   * Tipo do nó (master ou replica)
   */
  type: 'master' | 'replica';

  /**
   * ID do nó master (apenas para replicas)
   */
  masterId?: string;

  /**
   * Peso do nó (para distribuição de slots)
   */
  weight?: number;

  /**
   * Tags do nó (para agrupamento)
   */
  tags?: string[];
}

/**
 * Interface para configuração de failover
 */
export interface FailoverConfig {
  /**
   * Se o failover automático está habilitado
   */
  enabled: boolean;

  /**
   * Timeout para detecção de falha (ms)
   */
  timeout: number;

  /**
   * Número de tentativas antes de considerar um nó como falho
   */
  maxAttempts: number;

  /**
   * Período de quarentena após failover (ms)
   */
  quarantinePeriod: number;

  /**
   * Estratégia de promoção de replica
   */
  promotionStrategy: 'latency' | 'replication-offset' | 'random';
}

/**
 * Interface para configuração de monitoramento
 */
export interface MonitoringConfig {
  /**
   * Se o monitoramento está habilitado
   */
  enabled: boolean;

  /**
   * Intervalo de verificação de estado (ms)
   */
  interval: number;

  /**
   * Métricas a serem coletadas
   */
  metrics: string[];

  /**
   * Alertas a serem enviados
   */
  alerts: {
    /**
     * Se alertas estão habilitados
     */
    enabled: boolean;

    /**
     * Canais para envio de alertas
     */
    channels: ('email' | 'slack' | 'webhook')[];

    /**
     * Destinatários dos alertas
     */
    recipients?: string[];

    /**
     * URL do webhook
     */
    webhookUrl?: string;
  };
}

/**
 * Interface para configuração de slots
 */
export interface SlotsConfig {
  /**
   * Estratégia de distribuição de slots
   */
  distributionStrategy: 'balanced' | 'weighted' | 'manual';

  /**
   * Mapeamento manual de slots (apenas para estratégia manual)
   */
  manualMapping?: Record<string, number[]>;

  /**
   * Número de slots a migrar por vez durante rebalanceamento
   */
  migrationBatchSize: number;

  /**
   * Timeout para migração de slots (ms)
   */
  migrationTimeout: number;
}

/**
 * Interface para configuração completa de cluster
 */
export interface ClusterConfig {
  /**
   * Se o cluster está habilitado
   */
  enabled: boolean;

  /**
   * Nome do cluster
   */
  name: string;

  /**
   * Configuração de nós
   */
  nodes: ClusterNodeConfig[];

  /**
   * Configuração de failover
   */
  failover: FailoverConfig;

  /**
   * Configuração de monitoramento
   */
  monitoring: MonitoringConfig;

  /**
   * Configuração de slots
   */
  slots: SlotsConfig;

  /**
   * Senha de autenticação do cluster
   */
  password?: string;

  /**
   * Timeout de comunicação entre nós (ms)
   */
  nodeTimeout: number;

  /**
   * Intervalo de heartbeat entre nós (ms)
   */
  nodeHeartbeat: number;
}

/**
 * Configurações de cluster para diferentes ambientes
 */
export const clusterConfigs: Record<string, ClusterConfig> = {
  // Ambiente de desenvolvimento
  development: {
    enabled: process.env.VALKEY_CLUSTER === 'true',
    name: 'estacao-dev-cluster',
    nodes: process.env.VALKEY_CLUSTER_NODES
      ? process.env.VALKEY_CLUSTER_NODES.split(',').map((nodeStr, index) => {
          const [host, port] = nodeStr.split(':');
          return {
            host,
            port: Number.parseInt(port, 10),
            type: index === 0 ? 'master' : 'replica',
            masterId: index === 0 ? undefined : '0',
            weight: 1,
            tags: ['dev'],
          };
        })
      : [
          { host: 'localhost', port: 6379, type: 'master', weight: 1, tags: ['dev'] },
          {
            host: 'localhost',
            port: 6380,
            type: 'replica',
            masterId: '0',
            weight: 1,
            tags: ['dev'],
          },
        ],
    failover: {
      enabled: true,
      timeout: 5000,
      maxAttempts: 3,
      quarantinePeriod: 30000,
      promotionStrategy: 'latency',
    },
    monitoring: {
      enabled: true,
      interval: 10000,
      metrics: ['memory', 'clients', 'cpu', 'cluster', 'keyspace'],
      alerts: {
        enabled: false,
        channels: ['email'],
        recipients: ['<EMAIL>'],
      },
    },
    slots: {
      distributionStrategy: 'balanced',
      migrationBatchSize: 100,
      migrationTimeout: 30000,
    },
    password: process.env.VALKEY_PASSWORD,
    nodeTimeout: 5000,
    nodeHeartbeat: 1000,
  },

  // Ambiente de teste
  test: {
    enabled: process.env.VALKEY_CLUSTER === 'true',
    name: 'estacao-test-cluster',
    nodes: [
      { host: 'valkey-test-1', port: 6379, type: 'master', weight: 1, tags: ['test'] },
      {
        host: 'valkey-test-2',
        port: 6379,
        type: 'replica',
        masterId: '0',
        weight: 1,
        tags: ['test'],
      },
    ],
    failover: {
      enabled: true,
      timeout: 5000,
      maxAttempts: 3,
      quarantinePeriod: 30000,
      promotionStrategy: 'latency',
    },
    monitoring: {
      enabled: true,
      interval: 10000,
      metrics: ['memory', 'clients', 'cpu', 'cluster', 'keyspace'],
      alerts: {
        enabled: false,
        channels: ['email'],
        recipients: ['<EMAIL>'],
      },
    },
    slots: {
      distributionStrategy: 'balanced',
      migrationBatchSize: 100,
      migrationTimeout: 30000,
    },
    password: process.env.VALKEY_PASSWORD,
    nodeTimeout: 5000,
    nodeHeartbeat: 1000,
  },

  // Ambiente de produção
  production: {
    enabled: process.env.VALKEY_CLUSTER === 'true',
    name: 'estacao-prod-cluster',
    nodes: process.env.VALKEY_CLUSTER_NODES
      ? process.env.VALKEY_CLUSTER_NODES.split(',').map((nodeStr, index) => {
          const [host, port] = nodeStr.split(':');
          // Em produção, configuramos 3 masters e 3 replicas
          const nodeCount = process.env.VALKEY_CLUSTER_NODES.split(',').length;
          const masterCount = Math.ceil(nodeCount / 2);
          const isMaster = index < masterCount;
          const masterId = isMaster ? undefined : String(index % masterCount);

          return {
            host,
            port: Number.parseInt(port, 10),
            type: isMaster ? 'master' : 'replica',
            masterId,
            weight: 1,
            tags: ['prod'],
          };
        })
      : [
          {
            host: 'valkey-prod-1',
            port: 6379,
            type: 'master',
            weight: 1,
            tags: ['prod', 'zone-a'],
          },
          {
            host: 'valkey-prod-2',
            port: 6379,
            type: 'master',
            weight: 1,
            tags: ['prod', 'zone-b'],
          },
          {
            host: 'valkey-prod-3',
            port: 6379,
            type: 'master',
            weight: 1,
            tags: ['prod', 'zone-c'],
          },
          {
            host: 'valkey-prod-4',
            port: 6379,
            type: 'replica',
            masterId: '0',
            weight: 1,
            tags: ['prod', 'zone-b'],
          },
          {
            host: 'valkey-prod-5',
            port: 6379,
            type: 'replica',
            masterId: '1',
            weight: 1,
            tags: ['prod', 'zone-c'],
          },
          {
            host: 'valkey-prod-6',
            port: 6379,
            type: 'replica',
            masterId: '2',
            weight: 1,
            tags: ['prod', 'zone-a'],
          },
        ],
    failover: {
      enabled: true,
      timeout: 3000,
      maxAttempts: 5,
      quarantinePeriod: 60000,
      promotionStrategy: 'replication-offset',
    },
    monitoring: {
      enabled: true,
      interval: 5000,
      metrics: ['memory', 'clients', 'cpu', 'cluster', 'keyspace', 'replication', 'persistence'],
      alerts: {
        enabled: true,
        channels: ['email', 'slack', 'webhook'],
        recipients: ['<EMAIL>', '<EMAIL>'],
        webhookUrl: process.env.MONITORING_WEBHOOK_URL,
      },
    },
    slots: {
      distributionStrategy: 'weighted',
      migrationBatchSize: 50,
      migrationTimeout: 60000,
    },
    password: process.env.VALKEY_PASSWORD,
    nodeTimeout: 3000,
    nodeHeartbeat: 1000,
  },
};

/**
 * Obtém a configuração de cluster para o ambiente atual
 * @returns Configuração de cluster
 */
export function getClusterConfig(): ClusterConfig {
  const env = process.env.NODE_ENV || 'development';
  return clusterConfigs[env] || clusterConfigs.development;
}

/**
 * Gera comandos de configuração de cluster para o Valkey
 * @returns Array de linhas de configuração
 */
export function generateClusterConfig(): string[] {
  const config = getClusterConfig();
  const lines: string[] = [];

  if (!config.enabled) {
    lines.push('# Cluster desabilitado');
    lines.push('cluster-enabled no');
    return lines;
  }

  lines.push('# Configuração de cluster');
  lines.push('cluster-enabled yes');
  lines.push(`cluster-config-file nodes-${config.name}.conf`);
  lines.push(`cluster-node-timeout ${config.nodeTimeout}`);

  if (config.failover.enabled) {
    lines.push('cluster-replica-validity-factor 10');
    lines.push('cluster-replica-no-failover no');
    lines.push('cluster-migration-barrier 1');
    lines.push('cluster-require-full-coverage no');
  } else {
    lines.push('cluster-replica-no-failover yes');
  }

  return lines;
}

/**
 * Verifica o estado do cluster
 * @returns Informações sobre o estado do cluster
 */
export async function checkClusterStatus(client: any): Promise<any> {
  try {
    const info = await client.clusterInfo();
    const nodes = await client.clusterNodes();
    const slots = await client.clusterSlots();

    logger.info('Estado do cluster Valkey:', {
      info,
      nodesCount: nodes.length,
      slotsAssigned: Object.keys(slots).length,
    });

    return { info, nodes, slots };
  } catch (error) {
    logger.error('Erro ao verificar estado do cluster Valkey:', error);
    throw error;
  }
}
