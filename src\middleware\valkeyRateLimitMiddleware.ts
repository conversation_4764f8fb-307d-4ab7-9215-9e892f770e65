/**
 * Middleware de Rate Limiting com Valkey
 *
 * Este middleware implementa controle de taxa de requisições (rate limiting)
 * utilizando o Valkey para proteção contra abusos, ataques de força bruta e sobrecarga.
 *
 * Características:
 * - Algoritmo de sliding window para contagem precisa
 * - Diferentes limites por tipo de rota
 * - Suporte para whitelist de IPs
 * - Cabeçalhos padrão de rate limiting
 * - Integração com sistema de monitoramento
 */

import { defineMiddleware } from 'astro:middleware';
import { applicationMonitoringService } from '@services/applicationMonitoringService';
import { RateLimitType, valkeyRateLimitService } from '@services/valkeyRateLimitService';
import { logger } from '@utils/logger';

/**
 * Configuração do middleware
 */
const CONFIG = {
  /**
   * Se o middleware está habilitado
   */
  enabled: process.env.ENABLE_RATE_LIMITING !== 'false',

  /**
   * Caminhos que devem ser ignorados pelo rate limiting
   */
  excludePaths: [
    '/assets/',
    '/static/',
    '/favicon.ico',
    '/robots.txt',
    '/sitemap.xml',
    '/service-worker.js',
    '/manifest.json',
    '/offline.html',
  ],

  /**
   * IPs que devem ser ignorados pelo rate limiting (ex: localhost, servidores de monitoramento)
   */
  whitelistedIPs: [
    '127.0.0.1',
    '::1',
    'localhost',
    ...(process.env.RATE_LIMIT_WHITELIST?.split(',') || []),
  ],

  /**
   * Cabeçalhos personalizados para respostas
   */
  customHeaders: {
    limit: 'X-RateLimit-Limit',
    remaining: 'X-RateLimit-Remaining',
    reset: 'X-RateLimit-Reset',
    retryAfter: 'Retry-After',
  },
};

/**
 * Obtém o tipo de limite com base no caminho da requisição
 * @param path - Caminho da requisição
 * @returns Tipo de limite
 */
function getLimitTypeForPath(path: string): RateLimitType {
  // Rotas de autenticação
  if (path.startsWith('/api/auth/signin') || path.includes('/login') || path.includes('/signin')) {
    return RateLimitType.LOGIN;
  }

  // Rotas de registro
  if (
    path.startsWith('/api/auth/signup') ||
    path.includes('/register') ||
    path.includes('/signup')
  ) {
    return RateLimitType.SIGNUP;
  }

  // Rotas de contato
  if (path.startsWith('/api/contact') || path.includes('/contact') || path.includes('/contato')) {
    return RateLimitType.CONTACT;
  }

  // Rotas de upload
  if (path.includes('/upload') || path.includes('/files') || path.includes('/media')) {
    return RateLimitType.UPLOAD;
  }

  // Rotas de pagamento
  if (path.includes('/payment') || path.includes('/checkout') || path.includes('/pix')) {
    return RateLimitType.PAYMENT;
  }

  // Rotas de API
  if (path.startsWith('/api/')) {
    return RateLimitType.API;
  }

  // Limite global para outras rotas
  return RateLimitType.GLOBAL;
}

/**
 * Obtém identificador único para a requisição
 * @param request - Objeto de requisição
 * @returns Identificador único (geralmente IP)
 */
function getIdentifier(request: Request): string {
  // Tentar obter IP de cabeçalhos de proxy
  const forwardedFor = request.headers.get('x-forwarded-for');
  if (forwardedFor) {
    // Pegar o primeiro IP da lista (cliente original)
    return forwardedFor.split(',')[0].trim();
  }

  // Tentar obter IP real
  const realIp = request.headers.get('x-real-ip');
  if (realIp) {
    return realIp.trim();
  }

  // Fallback para IP desconhecido
  return 'unknown';
}

/**
 * Verifica se um caminho deve ser ignorado
 * @param path - Caminho da requisição
 * @returns Verdadeiro se o caminho deve ser ignorado
 */
function shouldExcludePath(path: string): boolean {
  return CONFIG.excludePaths.some(
    (excludePath) => path.startsWith(excludePath) || path === excludePath
  );
}

/**
 * Verifica se um IP está na lista de permitidos
 * @param ip - Endereço IP
 * @returns Verdadeiro se o IP está na whitelist
 */
function isWhitelistedIP(ip: string): boolean {
  return CONFIG.whitelistedIPs.includes(ip);
}

/**
 * Middleware de rate limiting
 */
export const onRequest = defineMiddleware(async (context, next) => {
  // Verificar se o middleware está habilitado
  if (!CONFIG.enabled) {
    return next();
  }

  try {
    // Iniciar timer para métricas de performance
    const startTime = performance.now();

    // Obter caminho da requisição
    const path = new URL(context.request.url).pathname;

    // Verificar se o caminho deve ser ignorado
    if (shouldExcludePath(path)) {
      return next();
    }

    // Obter identificador (IP)
    const identifier = getIdentifier(context.request);

    // Verificar se o IP está na whitelist
    if (isWhitelistedIP(identifier)) {
      return next();
    }

    // Obter tipo de limite com base no caminho
    const limitType = getLimitTypeForPath(path);

    // Verificar limite
    const result = await valkeyRateLimitService.check(identifier, limitType);

    // Registrar métrica de tempo de verificação
    const checkDuration = performance.now() - startTime;
    applicationMonitoringService.recordMetric('rate_limit_check_duration', checkDuration);

    // Se o limite foi excedido, retornar erro 429
    if (result.limited) {
      logger.warn('Rate limit excedido', {
        identifier,
        path,
        limitType,
        resetInSeconds: result.resetInSeconds,
      });

      // Criar resposta de erro
      return new Response(
        JSON.stringify({
          error: 'Too Many Requests',
          message: 'Você excedeu o limite de requisições. Tente novamente mais tarde.',
          retryAfter: result.blockExpiresInSeconds || result.resetInSeconds,
        }),
        {
          status: 429,
          headers: {
            'Content-Type': 'application/json',
            [CONFIG.customHeaders.retryAfter]: String(
              result.blockExpiresInSeconds || result.resetInSeconds
            ),
            [CONFIG.customHeaders.limit]: String(result.remaining + (result.limited ? 0 : 1)),
            [CONFIG.customHeaders.remaining]: String(result.remaining),
            [CONFIG.customHeaders.reset]: String(result.resetInSeconds),
          },
        }
      );
    }

    // Executar próximo middleware
    const response = await next();

    // Adicionar cabeçalhos de rate limit à resposta
    const headers = new Headers(response.headers);
    headers.set(CONFIG.customHeaders.limit, String(result.remaining + 1));
    headers.set(CONFIG.customHeaders.remaining, String(result.remaining));
    headers.set(CONFIG.customHeaders.reset, String(result.resetInSeconds));

    // Criar nova resposta com os cabeçalhos adicionados
    return new Response(response.body, {
      status: response.status,
      statusText: response.statusText,
      headers,
    });
  } catch (error) {
    logger.error('Erro no middleware de rate limiting:', error);

    // Em caso de erro, permitir a requisição
    return next();
  }
});
