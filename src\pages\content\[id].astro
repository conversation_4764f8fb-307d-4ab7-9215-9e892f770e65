---
/**
 * Página de detalhes de conteúdo educacional
 *
 * Esta página exibe os detalhes de um conteúdo educacional específico.
 * Ela utiliza o controlador para obter os dados do conteúdo e os exibe de forma formatada.
 */

import { EducationalContentController } from '../../adapters/controllers/EducationalContentController';
import { PostgresEducationalContentRepository } from '../../adapters/repositories/PostgresEducationalContentRepository';
import { LogLevel } from '../../application/interfaces/services/Logger';
import { GetEducationalContentUseCase } from '../../application/usecases/content/GetEducationalContentUseCase';
import ContentCard from '../../components/content/ContentCard.astro';
import { getDatabaseConfig } from '../../infrastructure/config/database.config';
import { PostgresConnection } from '../../infrastructure/database/PostgresConnection';
import { ConsoleLogger } from '../../infrastructure/logging/ConsoleLogger';
// Importações
import BaseLayout from '../../layouts/BaseLayout.astro';

// Obter parâmetros da URL
const { id } = Astro.params;

// Inicializar dependências
const logger = new ConsoleLogger(LogLevel.INFO);
const dbConfig = getDatabaseConfig();
const dbConnection = new PostgresConnection(dbConfig, logger);
const contentRepository = new PostgresEducationalContentRepository(dbConnection);
const getContentUseCase = new GetEducationalContentUseCase(contentRepository);
const contentController = new EducationalContentController(getContentUseCase, logger);

// Obter dados do conteúdo
const contentResponse = await contentController.getContent(id || '');
const content = contentResponse.success ? contentResponse.data : null;

// Obter conteúdos relacionados
let relatedContents = [];
if (content) {
  const relatedResponse = await contentController.getContentsByAge(content.ageRange.min, 4);
  if (relatedResponse.success) {
    relatedContents = relatedResponse.data.filter((item) => item.id !== content.id).slice(0, 3);
  }
}

// Definir título da página
const title = content
  ? `${content.title} | Estação Alfabetização`
  : 'Conteúdo não encontrado | Estação Alfabetização';
const description = content
  ? content.description.substring(0, 160)
  : 'O conteúdo que você está procurando não foi encontrado.';
---

<BaseLayout title={title} description={description}>
  {content ? (
    <div class="content-detail">
      <div class="content-detail__header">
        <div class="container">
          <div class="content-detail__meta">
            <div class="content-detail__type">{content.type}</div>
            <div class="content-detail__age">Idade: {content.ageRange.min}-{content.ageRange.max} anos</div>
            <div class="content-detail__duration">Duração: {content.formattedDuration || `${content.duration} min`}</div>
          </div>
          
          <h1 class="content-detail__title">{content.title}</h1>
          
          <p class="content-detail__description">{content.description}</p>
          
          {content.tags.length > 0 && (
            <div class="content-detail__tags">
              {content.tags.map((tag: string) => (
                <a href={`/tags/${tag}`} class="content-detail__tag">{tag}</a>
              ))}
            </div>
          )}
        </div>
      </div>
      
      <div class="container">
        <div class="content-detail__main">
          <div class="content-detail__content">
            <div class="content-detail__content-inner">
              <!-- Conteúdo principal -->
              <div class="content-html" set:html={content.content} />
            </div>
          </div>
          
          <aside class="content-detail__sidebar">
            <div class="content-detail__sidebar-inner">
              <div class="content-detail__author">
                <h3>Autor</h3>
                <div class="content-detail__author-info">
                  <img src="/images/avatar-placeholder.jpg" alt="Avatar do autor" class="content-detail__author-avatar" />
                  <div>
                    <div class="content-detail__author-name">Nome do Autor</div>
                    <a href={`/authors/${content.authorId}`} class="content-detail__author-link">Ver perfil</a>
                  </div>
                </div>
              </div>
              
              <div class="content-detail__difficulty">
                <h3>Nível de Dificuldade</h3>
                <div class="difficulty-meter">
                  <div class={`difficulty-meter__level difficulty-meter__level--${content.difficulty}`}>
                    <span class="difficulty-meter__label">{content.difficulty}</span>
                  </div>
                </div>
              </div>
              
              <div class="content-detail__actions">
                <button class="btn btn-primary btn-block">
                  <i class="icon-download"></i> Baixar Material
                </button>
                <button class="btn btn-outline btn-block">
                  <i class="icon-bookmark"></i> Salvar para Depois
                </button>
              </div>
            </div>
          </aside>
        </div>
        
        {relatedContents.length > 0 && (
          <div class="content-detail__related">
            <h2 class="section-title">Conteúdos Relacionados</h2>
            <div class="content-grid">
              {relatedContents.map((item: any) => (
                <div class="content-grid__item">
                  <ContentCard
                    id={item.id}
                    title={item.title}
                    description={item.description}
                    type={item.type}
                    difficulty={item.difficulty}
                    ageRange={item.ageRange}
                    duration={item.duration}
                    formattedDuration={item.formattedDuration}
                    tags={item.tags}
                  />
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  ) : (
    <div class="container">
      <div class="error-container">
        <h1 class="error-title">Conteúdo não encontrado</h1>
        <p class="error-message">O conteúdo que você está procurando não está disponível ou foi removido.</p>
        <div class="error-actions">
          <a href="/" class="btn btn-primary">Voltar para a Página Inicial</a>
          <a href="/content" class="btn btn-outline">Ver Todos os Conteúdos</a>
        </div>
      </div>
    </div>
  )}
</BaseLayout>

<style>
  .content-detail__header {
    background-color: var(--color-primary-light, #f0f4ff);
    padding: 2rem 0;
    margin-bottom: 2rem;
  }
  
  .content-detail__meta {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    margin-bottom: 1rem;
    font-size: 0.875rem;
  }
  
  .content-detail__type {
    background-color: var(--color-primary, #4a6cf7);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 0.25rem;
    text-transform: uppercase;
    font-weight: 600;
  }
  
  .content-detail__age,
  .content-detail__duration {
    color: var(--color-text-light, #666);
  }
  
  .content-detail__title {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    color: var(--color-text-dark, #111);
  }
  
  .content-detail__description {
    font-size: 1.125rem;
    line-height: 1.6;
    color: var(--color-text, #333);
    margin-bottom: 1.5rem;
  }
  
  .content-detail__tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
  }
  
  .content-detail__tag {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    background-color: var(--color-tag-bg, #f3f4f6);
    color: var(--color-tag-text, #4b5563);
    border-radius: 0.25rem;
    font-size: 0.875rem;
    text-decoration: none;
    transition: background-color 0.2s;
  }
  
  .content-detail__tag:hover {
    background-color: var(--color-tag-bg-hover, #e5e7eb);
  }
  
  .content-detail__main {
    display: grid;
    grid-template-columns: 1fr 300px;
    gap: 2rem;
    margin-bottom: 3rem;
  }
  
  @media (max-width: 768px) {
    .content-detail__main {
      grid-template-columns: 1fr;
    }
  }
  
  .content-detail__content {
    background-color: white;
    border-radius: 0.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    overflow: hidden;
  }
  
  .content-detail__content-inner {
    padding: 2rem;
  }
  
  .content-detail__sidebar {
    align-self: start;
  }
  
  .content-detail__sidebar-inner {
    background-color: white;
    border-radius: 0.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    padding: 1.5rem;
  }
  
  .content-detail__author {
    margin-bottom: 1.5rem;
  }
  
  .content-detail__author h3 {
    font-size: 1.125rem;
    margin-bottom: 1rem;
    color: var(--color-text-dark, #111);
  }
  
  .content-detail__author-info {
    display: flex;
    align-items: center;
    gap: 1rem;
  }
  
  .content-detail__author-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    object-fit: cover;
  }
  
  .content-detail__author-name {
    font-weight: 600;
    margin-bottom: 0.25rem;
  }
  
  .content-detail__author-link {
    color: var(--color-primary, #4a6cf7);
    font-size: 0.875rem;
    text-decoration: none;
  }
  
  .content-detail__author-link:hover {
    text-decoration: underline;
  }
  
  .content-detail__difficulty {
    margin-bottom: 1.5rem;
  }
  
  .content-detail__difficulty h3 {
    font-size: 1.125rem;
    margin-bottom: 1rem;
    color: var(--color-text-dark, #111);
  }
  
  .difficulty-meter {
    background-color: var(--color-bg-light, #f9fafb);
    border-radius: 0.25rem;
    overflow: hidden;
  }
  
  .difficulty-meter__level {
    padding: 0.75rem;
    text-align: center;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.875rem;
  }
  
  .difficulty-meter__level--beginner {
    background-color: var(--color-success-light, #d1fae5);
    color: var(--color-success, #10b981);
  }
  
  .difficulty-meter__level--intermediate {
    background-color: var(--color-warning-light, #fef3c7);
    color: var(--color-warning, #f59e0b);
  }
  
  .difficulty-meter__level--advanced {
    background-color: var(--color-danger-light, #fee2e2);
    color: var(--color-danger, #ef4444);
  }
  
  .content-detail__actions {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
  }
  
  .btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border-radius: 0.375rem;
    font-weight: 600;
    transition: all 0.2s;
    cursor: pointer;
    text-decoration: none;
    border: none;
  }
  
  .btn-primary {
    background-color: var(--color-primary, #4a6cf7);
    color: white;
  }
  
  .btn-primary:hover {
    background-color: var(--color-primary-dark, #3b5bd9);
  }
  
  .btn-outline {
    background-color: transparent;
    border: 1px solid var(--color-border, #e5e7eb);
    color: var(--color-text, #333);
  }
  
  .btn-outline:hover {
    background-color: var(--color-bg-light, #f9fafb);
  }
  
  .btn-block {
    width: 100%;
  }
  
  .content-detail__related {
    margin-top: 3rem;
    padding-top: 2rem;
    border-top: 1px solid var(--color-border, #e5e7eb);
  }
  
  .section-title {
    font-size: 1.5rem;
    margin-bottom: 1.5rem;
    color: var(--color-text-dark, #111);
  }
  
  .content-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
  }
  
  .error-container {
    text-align: center;
    padding: 4rem 0;
  }
  
  .error-title {
    font-size: 2rem;
    margin-bottom: 1rem;
    color: var(--color-text-dark, #111);
  }
  
  .error-message {
    font-size: 1.125rem;
    color: var(--color-text-light, #666);
    margin-bottom: 2rem;
  }
  
  .error-actions {
    display: flex;
    justify-content: center;
    gap: 1rem;
  }
  
  .content-html {
    line-height: 1.7;
  }
  
  .content-html h2 {
    font-size: 1.5rem;
    margin: 1.5rem 0 1rem;
    color: var(--color-text-dark, #111);
  }
  
  .content-html h3 {
    font-size: 1.25rem;
    margin: 1.25rem 0 0.75rem;
    color: var(--color-text-dark, #111);
  }
  
  .content-html p {
    margin-bottom: 1rem;
  }
  
  .content-html ul, .content-html ol {
    margin-bottom: 1rem;
    padding-left: 1.5rem;
  }
  
  .content-html li {
    margin-bottom: 0.5rem;
  }
  
  .content-html img {
    max-width: 100%;
    height: auto;
    border-radius: 0.375rem;
    margin: 1.5rem 0;
  }
  
  .content-html blockquote {
    border-left: 4px solid var(--color-primary, #4a6cf7);
    padding-left: 1rem;
    margin: 1.5rem 0;
    color: var(--color-text-light, #666);
    font-style: italic;
  }
  
  .content-html pre {
    background-color: var(--color-code-bg, #f3f4f6);
    padding: 1rem;
    border-radius: 0.375rem;
    overflow-x: auto;
    margin: 1.5rem 0;
  }
  
  .content-html code {
    font-family: monospace;
    font-size: 0.875rem;
  }
  
  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
  }
</style>
