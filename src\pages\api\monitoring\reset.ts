/**
 * API para Reset de Contadores de Monitoramento
 * 
 * Este endpoint permite resetar contadores de métricas
 * do sistema de monitoramento.
 */

import type { APIRoute } from 'astro';
import { applicationMonitoringService, MetricType } from '@services/applicationMonitoringService';
import { isAdmin } from '@helpers/authGuard';

/**
 * Endpoint POST para resetar contadores de métricas
 */
export const POST: APIRoute = async ({ request, cookies }) => {
  try {
    // Verificar se o usuário é administrador
    const adminResult = await isAdmin({ request, cookies } as any);
    
    if (!adminResult) {
      return new Response(JSON.stringify({ error: 'Não autorizado' }), {
        status: 403,
        headers: {
          'Content-Type': 'application/json'
        }
      });
    }
    
    // Obter tipos de contadores a resetar
    const body = await request.json();
    const { types } = body;
    
    if (!types || !Array.isArray(types)) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Tipos de contadores inválidos'
      }), {
        status: 400,
        headers: {
          'Content-Type': 'application/json'
        }
      });
    }
    
    // Resetar contadores
    for (const type of types) {
      if (Object.values(MetricType).includes(type as MetricType)) {
        applicationMonitoringService.resetCounter(type as MetricType);
      }
    }
    
    return new Response(JSON.stringify({
      success: true,
      message: 'Contadores resetados com sucesso'
    }), {
      status: 200,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  } catch (error) {
    return new Response(JSON.stringify({
      success: false,
      error: error.message
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }
};
