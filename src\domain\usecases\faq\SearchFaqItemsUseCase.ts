/**
 * Search FAQ Items Use Case
 *
 * Caso de uso para buscar itens de FAQ por texto.
 * Parte da implementação da tarefa 8.7.1 - Implementação de FAQ interativo
 */

import { FaqRepository, PaginatedFaqItems } from '../../repositories/FaqRepository';

export interface SearchFaqItemsRequest {
  query: string;
  onlyPublished?: boolean;
  pagination?: {
    page: number;
    limit: number;
  };
}

export interface SearchFaqItemsResponse {
  success: boolean;
  data?: PaginatedFaqItems;
  error?: string;
}

export class SearchFaqItemsUseCase {
  constructor(private faqRepository: FaqRepository) {}

  async execute(request: SearchFaqItemsRequest): Promise<SearchFaqItemsResponse> {
    try {
      // Validar os dados de entrada
      if (!request.query || request.query.trim().length < 2) {
        return {
          success: false,
          error: 'Termo de busca deve ter pelo menos 2 caracteres.',
        };
      }

      if (request.pagination && (request.pagination.page < 1 || request.pagination.limit < 1)) {
        return {
          success: false,
          error: 'Parâmetros de paginação inválidos.',
        };
      }

      // Realizar a busca
      const result = await this.faqRepository.search(
        request.query,
        request.onlyPublished ?? true,
        request.pagination
      );

      return {
        success: true,
        data: result,
      };
    } catch (error) {
      console.error('Erro ao buscar itens de FAQ:', error);

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Erro desconhecido ao buscar itens de FAQ.',
      };
    }
  }
}
