---
import AdminLayout from '@layouts/AdminLayout.astro';
import { kafka } from '@config/kafka';
import { formatNumber, formatDate } from '@utils/formatters';

// Obter lista de grupos de consumidores
const admin = kafka.admin();
await admin.connect();

const groups = await admin.listGroups();
const consumerGroups = groups.groups;

// Obter detalhes de cada grupo
const groupDetails = await Promise.all(
  consumerGroups.map(async (group) => {
    try {
      // Obter descrição do grupo
      const description = await admin.describeGroups([group.groupId]);
      const groupDescription = description.groups[0];
      
      // Obter offsets do consumidor
      const consumerOffsets = await admin.fetchOffsets({
        groupId: group.groupId,
      });
      
      // Calcular lag total
      let totalLag = 0;
      const topicLags = [];
      
      for (const { topic, partitions } of consumerOffsets) {
        // Obter offsets mais recentes para o tópico
        const topicOffsets = await admin.fetchTopicOffsets(topic);
        
        let topicLag = 0;
        
        for (const { partition, offset } of partitions) {
          const latestOffset = topicOffsets.find(
            t => t.partition === partition
          )?.offset || '0';
          
          const consumerOffset = offset;
          const lag = Number(BigInt(latestOffset) - BigInt(consumerOffset));
          
          topicLag += lag;
          totalLag += lag;
        }
        
        topicLags.push({
          topic,
          lag: topicLag,
          partitions: partitions.length,
        });
      }
      
      return {
        groupId: group.groupId,
        protocolType: group.protocolType,
        state: groupDescription.state,
        members: groupDescription.members?.length || 0,
        topics: topicLags,
        totalLag,
      };
    } catch (error) {
      console.error(`Erro ao obter detalhes do grupo ${group.groupId}:`, error);
      return {
        groupId: group.groupId,
        protocolType: group.protocolType,
        state: 'Error',
        members: 0,
        topics: [],
        totalLag: 0,
        error: error.message,
      };
    }
  })
);

// Ordenar grupos por lag total (decrescente)
groupDetails.sort((a, b) => b.totalLag - a.totalLag);

await admin.disconnect();

// Função para obter classe CSS com base no estado
function getStateClass(state: string): string {
  switch (state) {
    case 'Stable':
      return 'bg-green-100 text-green-800';
    case 'PreparingRebalance':
      return 'bg-yellow-100 text-yellow-800';
    case 'CompletingRebalance':
      return 'bg-blue-100 text-blue-800';
    case 'Dead':
      return 'bg-red-100 text-red-800';
    case 'Empty':
      return 'bg-gray-100 text-gray-800';
    case 'Error':
      return 'bg-red-100 text-red-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
}

// Função para obter classe CSS com base no lag
function getLagClass(lag: number): string {
  if (lag === 0) {
    return 'text-green-600';
  } else if (lag < 1000) {
    return 'text-yellow-600';
  } else if (lag < 10000) {
    return 'text-orange-600';
  } else {
    return 'text-red-600 font-bold';
  }
}
---

<AdminLayout title="Consumidores Kafka">
  <div class="container mx-auto px-4 py-8">
    <h1 class="text-3xl font-bold mb-8">Consumidores Kafka</h1>
    
    <div class="bg-white rounded-lg shadow overflow-hidden mb-8">
      <div class="p-6 border-b">
        <div class="flex items-center justify-between">
          <h2 class="text-xl font-semibold">Visão Geral</h2>
          <span class="text-lg font-medium">{groupDetails.length} grupos</span>
        </div>
      </div>
      
      <div class="p-6">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <!-- Total de grupos -->
          <div class="bg-blue-50 rounded-lg p-4">
            <div class="text-blue-800 text-sm font-medium mb-2">Total de Grupos</div>
            <div class="text-2xl font-bold text-blue-900">{groupDetails.length}</div>
          </div>
          
          <!-- Grupos ativos -->
          <div class="bg-green-50 rounded-lg p-4">
            <div class="text-green-800 text-sm font-medium mb-2">Grupos Ativos</div>
            <div class="text-2xl font-bold text-green-900">
              {groupDetails.filter(g => g.state === 'Stable').length}
            </div>
          </div>
          
          <!-- Grupos com lag -->
          <div class="bg-yellow-50 rounded-lg p-4">
            <div class="text-yellow-800 text-sm font-medium mb-2">Grupos com Lag</div>
            <div class="text-2xl font-bold text-yellow-900">
              {groupDetails.filter(g => g.totalLag > 0).length}
            </div>
          </div>
          
          <!-- Lag total -->
          <div class="bg-red-50 rounded-lg p-4">
            <div class="text-red-800 text-sm font-medium mb-2">Lag Total</div>
            <div class="text-2xl font-bold text-red-900">
              {formatNumber(groupDetails.reduce((sum, g) => sum + g.totalLag, 0))}
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Lista de grupos de consumidores -->
    <div class="bg-white rounded-lg shadow overflow-hidden">
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Grupo</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Estado</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Membros</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tópicos</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Lag Total</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ações</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            {groupDetails.map((group) => (
              <tr>
                <td class="px-6 py-4 whitespace-nowrap">
                  <a href={`/admin/kafka/consumer/${group.groupId}`} class="text-blue-600 hover:text-blue-900">
                    {group.groupId}
                  </a>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span class={`px-2 py-1 rounded-full text-xs font-medium ${getStateClass(group.state)}`}>
                    {group.state}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">{group.members}</td>
                <td class="px-6 py-4 whitespace-nowrap">{group.topics.length}</td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span class={getLagClass(group.totalLag)}>
                    {formatNumber(group.totalLag)}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm">
                  <a href={`/admin/kafka/consumer/${group.groupId}`} class="text-blue-600 hover:text-blue-900">
                    Detalhes
                  </a>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
    
    <div class="mt-8 flex justify-between items-center">
      <a href="/admin/kafka/dashboard" class="text-blue-600 hover:text-blue-800">
        &larr; Voltar para Dashboard
      </a>
      
      <a href="/admin/kafka/consumers" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded">
        Atualizar Lista
      </a>
    </div>
  </div>
</AdminLayout>
---
