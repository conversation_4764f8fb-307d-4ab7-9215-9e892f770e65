/**
 * Serviço de cache de permissões
 *
 * Este serviço é responsável por gerenciar o cache de permissões,
 * incluindo armazenamento, recuperação e invalidação.
 */

import { roleRepository } from '@repository/roleRepository';
import { cacheService } from '@services/cacheService';
import { logger } from '@utils/logger';

/**
 * Tempo de vida padrão do cache em segundos (5 minutos)
 */
const DEFAULT_CACHE_TTL = 300;

/**
 * Serviço de cache de permissões
 */
export const permissionCacheService = {
  /**
   * Obtém uma permissão do cache
   * @param userId - ID do usuário
   * @param resource - Nome do recurso
   * @param action - Ação a ser verificada
   * @returns Resultado da permissão ou null se não estiver em cache
   */
  async getPermissionFromCache(
    userId: string,
    resource: string,
    action: string
  ): Promise<boolean | null> {
    try {
      const cacheKey = this.buildPermissionCacheKey(userId, resource, action);
      const cachedResult = await cacheService.get(cacheKey);

      if (cachedResult !== null) {
        return cachedResult === 'true';
      }

      return null;
    } catch (error) {
      logger.error('Erro ao obter permissão do cache:', error);
      return null;
    }
  },

  /**
   * Armazena um resultado de permissão no cache
   * @param userId - ID do usuário
   * @param resource - Nome do recurso
   * @param action - Ação a ser verificada
   * @param result - Resultado da verificação
   */
  async cachePermissionResult(
    userId: string,
    resource: string,
    action: string,
    result: boolean
  ): Promise<void> {
    try {
      const cacheKey = this.buildPermissionCacheKey(userId, resource, action);
      await cacheService.set(cacheKey, result ? 'true' : 'false', DEFAULT_CACHE_TTL);
    } catch (error) {
      logger.error('Erro ao armazenar permissão no cache:', error);
    }
  },

  /**
   * Constrói a chave de cache para uma permissão
   * @param userId - ID do usuário
   * @param resource - Nome do recurso
   * @param action - Ação a ser verificada
   * @returns Chave de cache
   */
  buildPermissionCacheKey(userId: string, resource: string, action: string): string {
    return `perm:${userId}:${resource}:${action}`;
  },

  /**
   * Invalida o cache de permissões de um usuário
   * @param userId - ID do usuário
   */
  async invalidateUserPermissionsCache(userId: string): Promise<void> {
    try {
      // Obter padrão de chaves para permissões do usuário
      const pattern = `perm:${userId}:*`;

      // Remover todas as chaves que correspondem ao padrão
      await cacheService.deletePattern(pattern);
    } catch (error) {
      logger.error('Erro ao invalidar cache de permissões do usuário:', error);
    }
  },

  /**
   * Invalida o cache de permissões para todos os usuários com um papel específico
   * @param roleId - ID do papel
   */
  async invalidateRolePermissionsCache(roleId: string): Promise<void> {
    try {
      // Obter todos os usuários com este papel
      const result = await roleRepository.getUsersWithRole(roleId);

      // Invalidar cache para cada usuário
      for (const row of result.rows) {
        await this.invalidateUserPermissionsCache(row.ulid_user);
      }

      // Invalidar cache para usuários com papéis filhos (que herdam deste papel)
      await this.invalidateChildRolesPermissionsCache(roleId);
    } catch (error) {
      logger.error('Erro ao invalidar cache de permissões do papel:', error);
    }
  },

  /**
   * Invalida o cache de permissões para todos os usuários com papéis filhos
   * @param parentRoleId - ID do papel pai
   */
  async invalidateChildRolesPermissionsCache(parentRoleId: string): Promise<void> {
    try {
      // Obter papéis filhos
      const childRolesResult = await roleRepository.getChildRoles(parentRoleId);

      // Para cada papel filho
      for (const childRole of childRolesResult.rows) {
        // Invalidar cache para usuários com este papel filho
        await this.invalidateRolePermissionsCache(childRole.ulid_role);
      }
    } catch (error) {
      logger.error('Erro ao invalidar cache de permissões dos papéis filhos:', error);
    }
  },
};
