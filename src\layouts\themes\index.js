/**
 * Sistema de Temas
 *
 * Este módulo exporta componentes e utilitários para o sistema de temas da aplicação.
 */

// Exportar componentes
export { default as ThemeProvider } from './ThemeProvider.astro';

// Exportar utilitários
export const themes = {
  // Lista de temas disponíveis
  available: ['light', 'dark', 'estacao', 'estacao-dark'],

  // Te<PERSON> padr<PERSON>
  default: 'light',

  // Mapeamento de nomes de temas para rótulos legíveis
  labels: {
    light: 'Claro',
    dark: 'Escuro',
    estacao: 'Estação',
    'estacao-dark': 'Estação (Escuro)',
  },

  // Mapeamento de temas para ícones
  icons: {
    light: 'sun',
    dark: 'moon',
    estacao: 'book',
    'estacao-dark': 'book-dark',
  },

  // Cores principais por tema
  colors: {
    light: {
      primary: '#FF8A00',
      secondary: '#8B31F3',
      accent: '#1DB9AA',
      background: '#FFFFFF',
      text: '#1F1F1F',
    },
    dark: {
      primary: '#FF8A00',
      secondary: '#8B31F3',
      accent: '#1DB9AA',
      background: '#1F1F1F',
      text: '#F2F2F2',
    },
    estacao: {
      primary: '#1E88E5',
      secondary: '#E53935',
      accent: '#FFD600',
      background: '#FFFFFF',
      text: '#1A2E4A',
    },
    'estacao-dark': {
      primary: '#1E88E5',
      secondary: '#E53935',
      accent: '#FFD600',
      background: '#1A2E4A',
      text: '#F0F4F8',
    },
  },
};

/**
 * Obtém o tema atual do cliente
 * @returns {string} O tema atual
 */
export function getTheme() {
  if (typeof window === 'undefined') {
    return themes.default;
  }

  if (window.themeProvider) {
    return window.themeProvider.getTheme();
  }

  const storedTheme = localStorage.getItem('theme');
  if (storedTheme && themes.available.includes(storedTheme)) {
    return storedTheme;
  }

  const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
  return systemTheme;
}

/**
 * Define o tema atual
 * @param {string} theme - O tema a ser definido
 * @returns {boolean} Se o tema foi definido com sucesso
 */
export function setTheme(theme) {
  if (typeof window === 'undefined') {
    return false;
  }

  if (!themes.available.includes(theme)) {
    console.error(
      `Tema "${theme}" não disponível. Temas disponíveis: ${themes.available.join(', ')}`
    );
    return false;
  }

  if (window.themeProvider) {
    window.themeProvider.setTheme(theme);
    return true;
  }

  const root = document.documentElement;

  // Remover classes de tema anteriores
  themes.available.forEach((t) => {
    root.classList.remove(`theme-${t}`);
    root.removeAttribute('data-theme');
  });

  // Adicionar nova classe de tema
  root.classList.add(`theme-${theme}`);
  root.setAttribute('data-theme', theme);

  // Persistir tema
  localStorage.setItem('theme', theme);

  // Disparar evento de mudança de tema
  window.dispatchEvent(new CustomEvent('themechange', { detail: { theme } }));

  return true;
}

/**
 * Alterna para o próximo tema na lista
 * @returns {string} O novo tema
 */
export function toggleTheme() {
  if (typeof window === 'undefined') {
    return themes.default;
  }

  if (window.themeProvider) {
    return window.themeProvider.toggleTheme();
  }

  const currentTheme = getTheme();
  const currentIndex = themes.available.indexOf(currentTheme);
  const nextIndex = (currentIndex + 1) % themes.available.length;
  const nextTheme = themes.available[nextIndex];

  setTheme(nextTheme);
  return nextTheme;
}

/**
 * Verifica se o tema atual é escuro
 * @returns {boolean} Se o tema atual é escuro
 */
export function isDarkTheme() {
  const currentTheme = getTheme();
  return currentTheme === 'dark' || currentTheme === 'estacao-dark';
}

/**
 * Obtém a cor principal do tema atual
 * @param {string} colorName - Nome da cor (primary, secondary, accent, background, text)
 * @returns {string} Código hexadecimal da cor
 */
export function getThemeColor(colorName = 'primary') {
  const currentTheme = getTheme();
  return themes.colors[currentTheme]?.[colorName] || themes.colors[themes.default][colorName];
}

/**
 * Adiciona um ouvinte para mudanças de tema
 * @param {Function} callback - Função a ser chamada quando o tema mudar
 * @returns {Function} Função para remover o ouvinte
 */
export function onThemeChange(callback) {
  if (typeof window === 'undefined') {
    return () => {};
  }

  const handler = (e) => callback(e.detail.theme);
  window.addEventListener('themechange', handler);

  return () => window.removeEventListener('themechange', handler);
}
