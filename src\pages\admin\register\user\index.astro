---
import { actions } from 'astro:actions';
import AdminLayout from '@layouts/AdminLayout.astro';
import type { UserData } from 'src/database/interfacesHelper';

const name = Astro.url.searchParams.get('query') || undefined;
const result = await actions.userAction.read({
  filter: 'search',
  name,
});
---

<AdminLayout title="Usuários">
  <div class="container mx-auto p-4">
    <div class="flex justify-between items-center mb-6">
      <h1 class="text-2xl font-bold">Usuários</h1>
      <a href="./new" class="btn btn-primary">Novo Usuário</a>
    </div>

    <form method="get" class="mb-6">
      <div class="flex gap-4">
        <input 
          type="text"
          name="query"
          value={name}
          class="input input-bordered flex-1"
          placeholder="Pesquisar por nome..."
        />
        <button type="submit" class="btn btn-primary">Buscar</button>
      </div>
    </form>

    <div class="overflow-x-auto">
      <table class="table w-full">
        <thead>
          <tr>
            <th>Nome</th>
            <th>Email</th>
            <th>Tipo</th>
            <th>Status</th>
            <th>Ações</th>
          </tr>
        </thead>
        <tbody>
          {Array.isArray(result.data) && result.data.map((user: UserData) => (
            <tr>
              <td>{user.name}</td>
              <td>{user.email}</td>
              <td>{user.user_type}</td>
              <td>
                <span class={`badge ${user.active ? 'badge-success' : 'badge-error'}`}>
                  {user.active ? 'Ativo' : 'Inativo'}
                </span>
              </td>
              <td class="flex gap-2">
                <a href={`./${user.ulid_user}`} class="btn btn-sm btn-info">
                  Editar
                </a>
                <button 
                  data-id={user.ulid_user}
                  class="btn btn-sm btn-error delete-btn">
                  Excluir
                </button>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  </div>
</AdminLayout>

<script>
  import { actions } from "astro:actions";
  
  document.querySelectorAll('.delete-btn').forEach(btn => {
    btn.addEventListener('click', async (e) => {
      const id = (e.target as HTMLButtonElement).dataset.id;
      if (!id || !confirm('Confirma exclusão?')) return;
      
      try {
        const response = await actions.userAction.delete({
          method: "delete",
          ulid_user: id
        });

        if (response.data?.success) {
          window.location.reload();
        } else {
          throw new Error(String(response.data?.error || 'Erro desconhecido'));
        }
      } catch (error) {
        console.error('Erro:', error);
        alert(error instanceof Error ? error.message : 'Erro ao excluir');
      }
    });
  });
</script>