---
import InputText from '@components/form/InputText.astro';
import AdminLayout from '@layouts/AdminLayout.astro';
---
<AdminLayout title="Cadastro de Status de Pagamento">
  <template>
    <div class="search-container flex justify-between mb-4">
      <InputText id="search-input" label="Pesquisar" name="search" placeholder="Pesquisar status de pagamento..." />
      <div>
        <button class="btn btn-primary" id="handleSearch">Pesquisar</button>
        <button class="btn btn-secondary ml-2" id="navigateToNewPaymentStatus">Novo</button>
      </div>
    </div>

    <ul class="payment-status-list list-disc pl-5">
    </ul>
  </template>

  <style>
    .search-container {
      display: flex;
      justify-content: space-between;
      margin-bottom: 20px;
    }
    .payment-status-list {
      list-style: none;
      padding: 0;
    }
    .payment-status-list li {
      display: flex;
      justify-content: space-between;
      margin-bottom: 10px;
    }
  </style>
</AdminLayout>

<script>
  import { actions } from "astro:actions";
  
  interface PaymentStatus {
    ulid_payment_status: string;
    status: string;
  }

  let paymentStatuses: PaymentStatus[] = []; // Declare and initialize the 'paymentStatuses' variable

  async function handleSearch() {
    const searchInputElement = document.getElementById('search-input') as HTMLInputElement;
    if (searchInputElement) {
      const searchInput = searchInputElement.value;
      if (searchInput !== null && searchInput !== undefined) {
        // Lógica para buscar status de pagamento com base no input
        const param = { filter: "status", status: searchInput };
        loadPaymentStatuses(param);
      } else {
        console.error('O valor do input de pesquisa é nulo ou indefinido.');
      }
    } else {
      console.error('Elemento de input de pesquisa não encontrado.');
    }
  }

  function navigateToNewPaymentStatus() {
    // Lógica para redirecionar para o novo status de pagamento
    window.location.href = '/admin/register/payment/status/new';
  }

  async function handleEdit(ulid_payment_status: string) {
    // Lógica para editar o status de pagamento
    window.location.href = `/admin/register/payment/status/${ulid_payment_status}`;
  }

  async function handleDelete(ulid_payment_status: string) {
    // Lógica para excluir o status de pagamento
    if (confirm('Tem certeza que deseja excluir este status de pagamento?')) {
      await actions.paymentStatusAction.delete({ ulid_payment_status });
      loadPaymentStatuses({ filter: "all" }); // Recarregar status de pagamento após exclusão
    }
  }

  async function loadPaymentStatuses(param: any) {
    const result = await actions.paymentStatusAction.read(param);
    paymentStatuses = result.data || [];

    // Apagar a lista existente
    const paymentStatusList = document.querySelector('.payment-status-list');

    if (paymentStatusList) {
      paymentStatusList.innerHTML = '';

      // Inserir o cabeçalho
      const titleRow = document.createElement('li');
      titleRow.innerHTML = `
          <strong>Nome</strong>
          <strong>Ações</strong>
      `;
      paymentStatusList.appendChild(titleRow);

      if (paymentStatuses.length === 0) {
        const noDataMessage = document.createElement('li');
        noDataMessage.textContent = 'Nenhum status de pagamento encontrado.';
        paymentStatusList.appendChild(noDataMessage);
        return;
      }
      
      // Inserir novos status de pagamento
      paymentStatuses.forEach((paymentStatus: PaymentStatus) => {
        const listItem = document.createElement('li');
        listItem.innerHTML = `
            <span>${paymentStatus.status}</span>
            <div>
                <button type="button" class="btn btn-warning mr-2" onclick="handleEdit('${paymentStatus.ulid_payment_status}')">Alterar</button>
                <button type="button" class="btn btn-error" onclick="handleDelete('${paymentStatus.ulid_payment_status}')">Excluir</button>
            </div>
        `;
        paymentStatusList.appendChild(listItem);
      });
    } 
  }

  loadPaymentStatuses({ filter: "all" });
</script>