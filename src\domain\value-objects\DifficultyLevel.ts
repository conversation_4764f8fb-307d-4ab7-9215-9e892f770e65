/**
 * Níveis de dificuldade para conteúdos educacionais
 *
 * Este objeto de valor representa os níveis de dificuldade possíveis para conteúdos educacionais.
 */

export enum DifficultyLevel {
  BEGINNER = 'beginner',
  INTERMEDIATE = 'intermediate',
  ADVANCED = 'advanced',
}

/**
 * Verifica se um valor é um nível de dificuldade válido
 *
 * @param value Valor a ser verificado
 * @returns Verdadeiro se o valor for um nível de dificuldade válido
 */
export function isValidDifficultyLevel(value: string): value is DifficultyLevel {
  return Object.values(DifficultyLevel).includes(value as DifficultyLevel);
}

/**
 * Obtém a descrição de um nível de dificuldade
 *
 * @param level Nível de dificuldade
 * @returns Descrição do nível de dificuldade
 */
export function getDifficultyLevelDescription(level: DifficultyLevel): string {
  const descriptions: Record<DifficultyLevel, string> = {
    [DifficultyLevel.BEGINNER]: 'Iniciante',
    [DifficultyLevel.INTERMEDIATE]: 'Intermediário',
    [DifficultyLevel.ADVANCED]: 'Avançado',
  };

  return descriptions[level] || 'Desconhecido';
}

/**
 * Obtém o valor numérico de um nível de dificuldade
 *
 * @param level Nível de dificuldade
 * @returns Valor numérico (1 para iniciante, 2 para intermediário, 3 para avançado)
 */
export function getDifficultyLevelValue(level: DifficultyLevel): number {
  const values: Record<DifficultyLevel, number> = {
    [DifficultyLevel.BEGINNER]: 1,
    [DifficultyLevel.INTERMEDIATE]: 2,
    [DifficultyLevel.ADVANCED]: 3,
  };

  return values[level] || 0;
}

/**
 * Obtém o nível de dificuldade a partir de um valor numérico
 *
 * @param value Valor numérico (1 para iniciante, 2 para intermediário, 3 para avançado)
 * @returns Nível de dificuldade correspondente
 */
export function getDifficultyLevelFromValue(value: number): DifficultyLevel {
  switch (value) {
    case 1:
      return DifficultyLevel.BEGINNER;
    case 2:
      return DifficultyLevel.INTERMEDIATE;
    case 3:
      return DifficultyLevel.ADVANCED;
    default:
      return DifficultyLevel.BEGINNER;
  }
}

/**
 * Obtém a cor associada a um nível de dificuldade
 *
 * @param level Nível de dificuldade
 * @returns Código de cor para o nível de dificuldade
 */
export function getDifficultyLevelColor(level: DifficultyLevel): string {
  const colors: Record<DifficultyLevel, string> = {
    [DifficultyLevel.BEGINNER]: 'green',
    [DifficultyLevel.INTERMEDIATE]: 'yellow',
    [DifficultyLevel.ADVANCED]: 'red',
  };

  return colors[level] || 'gray';
}
