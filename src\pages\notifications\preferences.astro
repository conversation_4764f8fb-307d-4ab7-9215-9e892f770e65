---
import { PageTransition } from '../../components/transitions';
import DaisyCard from '../../components/ui/DaisyCard.astro';
import Breadcrumbs from '../../components/ui/navigation/Breadcrumbs.astro';
/**
 * Página de Preferências de Notificação
 *
 * Interface para configurar preferências de notificação.
 * Parte da implementação da tarefa 8.5.1 - Notificações internas
 */
import BaseLayout from '../../layouts/BaseLayout.astro';
import { Container, Section } from '../../layouts/grid';

// Título da página
const title = 'Preferências de Notificação';

// Breadcrumbs para a página atual
const breadcrumbItems = [
  { href: '/', label: 'Início' },
  { href: '/notifications', label: 'Notificações' },
  { label: 'Preferências' },
];

// Em um cenário real, buscaríamos as preferências do usuário do repositório
// Por enquanto, usaremos dados de exemplo
const preferences = {
  userId: 'user-123',
  preferences: {
    info: {
      in_app: { enabled: true, frequency: 'immediate' },
      email: { enabled: true, frequency: 'daily' },
      push: { enabled: true, frequency: 'immediate' },
      sms: { enabled: false, frequency: 'never' },
    },
    success: {
      in_app: { enabled: true, frequency: 'immediate' },
      email: { enabled: true, frequency: 'immediate' },
      push: { enabled: true, frequency: 'immediate' },
      sms: { enabled: false, frequency: 'never' },
    },
    warning: {
      in_app: { enabled: true, frequency: 'immediate' },
      email: { enabled: true, frequency: 'immediate' },
      push: { enabled: true, frequency: 'immediate' },
      sms: { enabled: false, frequency: 'never' },
    },
    error: {
      in_app: { enabled: true, frequency: 'immediate' },
      email: { enabled: true, frequency: 'immediate' },
      push: { enabled: true, frequency: 'immediate' },
      sms: { enabled: false, frequency: 'never' },
    },
    system: {
      in_app: { enabled: true, frequency: 'immediate' },
      email: { enabled: true, frequency: 'immediate' },
      push: { enabled: true, frequency: 'immediate' },
      sms: { enabled: false, frequency: 'never' },
    },
    payment: {
      in_app: { enabled: true, frequency: 'immediate' },
      email: { enabled: true, frequency: 'immediate' },
      push: { enabled: true, frequency: 'immediate' },
      sms: { enabled: false, frequency: 'never' },
    },
    order: {
      in_app: { enabled: true, frequency: 'immediate' },
      email: { enabled: true, frequency: 'immediate' },
      push: { enabled: true, frequency: 'immediate' },
      sms: { enabled: false, frequency: 'never' },
    },
    product: {
      in_app: { enabled: true, frequency: 'immediate' },
      email: { enabled: true, frequency: 'daily' },
      push: { enabled: false, frequency: 'never' },
      sms: { enabled: false, frequency: 'never' },
    },
    content: {
      in_app: { enabled: true, frequency: 'immediate' },
      email: { enabled: true, frequency: 'daily' },
      push: { enabled: false, frequency: 'never' },
      sms: { enabled: false, frequency: 'never' },
    },
    message: {
      in_app: { enabled: true, frequency: 'immediate' },
      email: { enabled: true, frequency: 'immediate' },
      push: { enabled: true, frequency: 'immediate' },
      sms: { enabled: false, frequency: 'never' },
    },
  },
  doNotDisturb: {
    enabled: true,
    startTime: '22:00',
    endTime: '08:00',
    timezone: 'America/Sao_Paulo',
  },
};

// Tipos de notificação
const notificationTypes = [
  {
    id: 'info',
    label: 'Informações',
    icon: 'info-circle',
    description: 'Notificações informativas gerais',
  },
  {
    id: 'success',
    label: 'Sucesso',
    icon: 'check-circle',
    description: 'Confirmações de ações bem-sucedidas',
  },
  {
    id: 'warning',
    label: 'Avisos',
    icon: 'alert-triangle',
    description: 'Avisos importantes que requerem atenção',
  },
  { id: 'error', label: 'Erros', icon: 'x-circle', description: 'Erros e falhas no sistema' },
  {
    id: 'system',
    label: 'Sistema',
    icon: 'settings',
    description: 'Atualizações e manutenções do sistema',
  },
  {
    id: 'payment',
    label: 'Pagamentos',
    icon: 'credit-card',
    description: 'Notificações relacionadas a pagamentos',
  },
  {
    id: 'order',
    label: 'Pedidos',
    icon: 'shopping-bag',
    description: 'Atualizações de status de pedidos',
  },
  { id: 'product', label: 'Produtos', icon: 'package', description: 'Novos produtos e promoções' },
  {
    id: 'content',
    label: 'Conteúdo',
    icon: 'file-text',
    description: 'Novos conteúdos e materiais educacionais',
  },
  {
    id: 'message',
    label: 'Mensagens',
    icon: 'message-circle',
    description: 'Mensagens de outros usuários',
  },
];

// Canais de notificação
const notificationChannels = [
  { id: 'in_app', label: 'No aplicativo', icon: 'bell' },
  { id: 'email', label: 'E-mail', icon: 'mail' },
  { id: 'push', label: 'Push', icon: 'smartphone' },
  { id: 'sms', label: 'SMS', icon: 'message-square' },
];

// Frequências de notificação
const frequencies = [
  { id: 'immediate', label: 'Imediatamente' },
  { id: 'daily', label: 'Resumo diário' },
  { id: 'weekly', label: 'Resumo semanal' },
  { id: 'never', label: 'Nunca' },
];

// Processar o formulário
let formSubmitted = false;
let formSuccess = false;
let formError = '';

if (Astro.request.method === 'POST') {
  try {
    // Em um cenário real, aqui seria implementada a lógica para:
    // 1. Receber os dados do formulário
    // 2. Validar os dados
    // 3. Atualizar as preferências no repositório
    // 4. Redirecionar ou exibir mensagem de sucesso

    formSubmitted = true;
    formSuccess = true;

    // Por enquanto, apenas simulamos o sucesso
  } catch (error) {
    console.error('Erro ao processar preferências de notificação:', error);
    formSubmitted = true;
    formError = 'Ocorreu um erro ao salvar suas preferências. Por favor, tente novamente.';
  }
}
---

<BaseLayout title={title} enableViewTransitions={true} transitionType="fade">
  <Container>
    <PageTransition type="fade" duration="0.5s">
      <Section>
        <Breadcrumbs items={breadcrumbItems} class="mb-6" />
        
        <h1 class="text-3xl font-bold mb-6">{title}</h1>
        
        {formSubmitted && formSuccess && (
          <div class="alert alert-success mb-6">
            <i class="icon icon-check-circle"></i>
            <span>Suas preferências foram salvas com sucesso!</span>
          </div>
        )}
        
        {formError && (
          <div class="alert alert-error mb-6">
            <i class="icon icon-alert-circle"></i>
            <span>{formError}</span>
          </div>
        )}
        
        <form action="/notifications/preferences" method="POST" class="space-y-8">
          <DaisyCard>
            <div class="p-6">
              <h2 class="text-xl font-bold mb-4">Configurações Gerais</h2>
              
              <div class="space-y-4">
                <div class="form-control">
                  <label class="label cursor-pointer justify-start gap-4">
                    <input 
                      type="checkbox" 
                      name="enable_all" 
                      class="toggle toggle-primary" 
                      checked={Object.values(preferences.preferences).some(type => 
                        Object.values(type).some(channel => channel.enabled)
                      )}
                    />
                    <span class="label-text font-medium">Habilitar todas as notificações</span>
                  </label>
                  <label class="label">
                    <span class="label-text-alt">Ative ou desative todas as notificações de uma vez.</span>
                  </label>
                </div>
                
                <div class="form-control">
                  <label class="label cursor-pointer justify-start gap-4">
                    <input 
                      type="checkbox" 
                      name="do_not_disturb" 
                      class="toggle toggle-primary" 
                      checked={preferences.doNotDisturb.enabled}
                    />
                    <span class="label-text font-medium">Modo "Não Perturbe"</span>
                  </label>
                  <label class="label">
                    <span class="label-text-alt">Quando ativado, você não receberá notificações durante o período especificado.</span>
                  </label>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-2">
                  <div class="form-control">
                    <label class="label">
                      <span class="label-text">Início</span>
                    </label>
                    <input 
                      type="time" 
                      name="dnd_start_time" 
                      class="input input-bordered" 
                      value={preferences.doNotDisturb.startTime}
                    />
                  </div>
                  
                  <div class="form-control">
                    <label class="label">
                      <span class="label-text">Fim</span>
                    </label>
                    <input 
                      type="time" 
                      name="dnd_end_time" 
                      class="input input-bordered" 
                      value={preferences.doNotDisturb.endTime}
                    />
                  </div>
                </div>
              </div>
            </div>
          </DaisyCard>
          
          <DaisyCard>
            <div class="p-6">
              <h2 class="text-xl font-bold mb-4">Preferências por Canal</h2>
              
              <div class="overflow-x-auto">
                <table class="table table-zebra w-full">
                  <thead>
                    <tr>
                      <th>Canal</th>
                      <th>Ativo</th>
                      <th>Frequência</th>
                    </tr>
                  </thead>
                  <tbody>
                    {notificationChannels.map(channel => (
                      <tr>
                        <td class="flex items-center gap-2">
                          <i class={`icon icon-${channel.icon}`}></i>
                          <span>{channel.label}</span>
                        </td>
                        <td>
                          <input 
                            type="checkbox" 
                            name={`channel_${channel.id}_enabled`} 
                            class="toggle toggle-primary toggle-sm" 
                            checked={Object.values(preferences.preferences).some(type => 
                              type[channel.id]?.enabled
                            )}
                          />
                        </td>
                        <td>
                          <select 
                            name={`channel_${channel.id}_frequency`} 
                            class="select select-bordered select-sm w-full max-w-xs"
                          >
                            {frequencies.map(freq => (
                              <option 
                                value={freq.id}
                                selected={Object.values(preferences.preferences).some(type => 
                                  type[channel.id]?.frequency === freq.id
                                )}
                              >
                                {freq.label}
                              </option>
                            ))}
                          </select>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </DaisyCard>
          
          <DaisyCard>
            <div class="p-6">
              <h2 class="text-xl font-bold mb-4">Preferências por Tipo de Notificação</h2>
              
              <div class="space-y-6">
                {notificationTypes.map(type => (
                  <div class="border-b border-base-300 pb-4 last:border-0 last:pb-0">
                    <div class="flex items-center gap-2 mb-2">
                      <i class={`icon icon-${type.icon}`}></i>
                      <h3 class="text-lg font-medium">{type.label}</h3>
                    </div>
                    
                    <p class="text-sm text-gray-500 mb-4">{type.description}</p>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                      {notificationChannels.map(channel => (
                        <div class="form-control">
                          <label class="label cursor-pointer justify-start gap-2">
                            <input 
                              type="checkbox" 
                              name={`type_${type.id}_${channel.id}_enabled`} 
                              class="checkbox checkbox-sm checkbox-primary" 
                              checked={preferences.preferences[type.id]?.[channel.id]?.enabled}
                            />
                            <span class="label-text">{channel.label}</span>
                          </label>
                          
                          <select 
                            name={`type_${type.id}_${channel.id}_frequency`} 
                            class="select select-bordered select-sm mt-2"
                          >
                            {frequencies.map(freq => (
                              <option 
                                value={freq.id}
                                selected={preferences.preferences[type.id]?.[channel.id]?.frequency === freq.id}
                              >
                                {freq.label}
                              </option>
                            ))}
                          </select>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </DaisyCard>
          
          <div class="flex justify-between">
            <button 
              type="button" 
              class="btn btn-outline"
              id="reset-defaults"
            >
              Restaurar Padrões
            </button>
            
            <div class="flex gap-2">
              <a href="/notifications" class="btn btn-ghost">Cancelar</a>
              <button type="submit" class="btn btn-primary">Salvar Preferências</button>
            </div>
          </div>
        </form>
      </Section>
    </PageTransition>
  </Container>
</BaseLayout>

<script>
  // Script para funcionalidade da página de preferências
  document.addEventListener('DOMContentLoaded', () => {
    // Botão "Habilitar todas as notificações"
    const enableAllCheckbox = document.querySelector('input[name="enable_all"]') as HTMLInputElement;
    
    if (enableAllCheckbox) {
      enableAllCheckbox.addEventListener('change', () => {
        const isChecked = enableAllCheckbox.checked;
        
        // Atualizar todos os checkboxes de canal
        document.querySelectorAll('input[name^="channel_"][name$="_enabled"]').forEach(checkbox => {
          (checkbox as HTMLInputElement).checked = isChecked;
        });
        
        // Atualizar todos os checkboxes de tipo
        document.querySelectorAll('input[name^="type_"][name$="_enabled"]').forEach(checkbox => {
          (checkbox as HTMLInputElement).checked = isChecked;
        });
      });
    }
    
    // Botão "Restaurar Padrões"
    const resetDefaultsButton = document.getElementById('reset-defaults');
    
    if (resetDefaultsButton) {
      resetDefaultsButton.addEventListener('click', () => {
        if (confirm('Tem certeza que deseja restaurar todas as preferências para os valores padrão?')) {
          // Em um cenário real, aqui seria feita uma chamada para a API
          // Por enquanto, apenas recarregamos a página
          window.location.reload();
        }
      });
    }
    
    // Atualizar checkboxes de canal quando um checkbox de tipo é alterado
    document.querySelectorAll('input[name^="type_"][name$="_enabled"]').forEach(checkbox => {
      checkbox.addEventListener('change', () => {
        updateEnableAllCheckbox();
      });
    });
    
    // Atualizar checkboxes de tipo quando um checkbox de canal é alterado
    document.querySelectorAll('input[name^="channel_"][name$="_enabled"]').forEach(checkbox => {
      checkbox.addEventListener('change', () => {
        const channelId = (checkbox as HTMLInputElement).name.split('_')[1];
        const isChecked = (checkbox as HTMLInputElement).checked;
        
        // Atualizar todos os checkboxes de tipo para este canal
        document.querySelectorAll(`input[name$="_${channelId}_enabled"]`).forEach(typeCheckbox => {
          if (typeCheckbox.getAttribute('name')?.startsWith('type_')) {
            (typeCheckbox as HTMLInputElement).checked = isChecked;
          }
        });
        
        updateEnableAllCheckbox();
      });
    });
    
    // Função para atualizar o checkbox "Habilitar todas as notificações"
    function updateEnableAllCheckbox() {
      if (!enableAllCheckbox) return;
      
      const allTypeCheckboxes = document.querySelectorAll('input[name^="type_"][name$="_enabled"]');
      const allChecked = Array.from(allTypeCheckboxes).every(checkbox => (checkbox as HTMLInputElement).checked);
      const allUnchecked = Array.from(allTypeCheckboxes).every(checkbox => !(checkbox as HTMLInputElement).checked);
      
      enableAllCheckbox.checked = !allUnchecked;
      enableAllCheckbox.indeterminate = !allChecked && !allUnchecked;
    }
  });
</script>
