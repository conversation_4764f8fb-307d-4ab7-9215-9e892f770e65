/**
 * Inicialização de fluxos de eventos
 *
 * Este arquivo é responsável por inicializar todos os fluxos de eventos
 * e registrar os handlers necessários.
 */

import { logger } from '@utils/logger';
import { eventConsumerService } from '@services/eventConsumerService';
import { 
  PaymentCreatedHandler, 
  PaymentUpdatedHandler,
  PaymentFailedHandler,
  RefundRequestedHandler,
  RefundProcessedHandler,
  PaymentWebhookHandler
} from '@services/handlers/paymentEventHandler';
import { kafkaLoggingService } from '@services/kafka-logging.service';

// Importar transformadores para garantir que sejam registrados
import '@services/transformers/paymentTransformers';
import '@services/transformers/orderTransformers';

/**
 * Inicializa os fluxos de eventos
 */
export async function initEventFlows(): Promise<void> {
  try {
    logger.info('Inicializando fluxos de eventos');

    // Registrar handlers de pagamento
    eventConsumerService.registerHandler(
      'payment.transaction.created',
      new PaymentCreatedHandler()
    );

    eventConsumerService.registerHandler(
      'payment.transaction.updated',
      new PaymentUpdatedHandler()
    );

    eventConsumerService.registerHandler(
      'payment.transaction.failed',
      new PaymentFailedHandler()
    );

    eventConsumerService.registerHandler(
      'payment.refund.requested',
      new RefundRequestedHandler()
    );

    eventConsumerService.registerHandler(
      'payment.refund.processed',
      new RefundProcessedHandler()
    );

    eventConsumerService.registerHandler(
      'payment.webhook.received',
      new PaymentWebhookHandler()
    );

    // Iniciar consumidor para tópicos de pagamento
    await eventConsumerService.init({
      topics: [
        'payment.transaction.created',
        'payment.transaction.updated',
        'payment.transaction.failed',
        'payment.refund.requested',
        'payment.refund.processed',
        'payment.webhook.received',
      ],
      groupId: 'payment-flow-consumer-group',
      offsetConfig: {
        commitStrategy: 'auto',
        autoCommitInterval: 5000,
      },
      maxParallelMessages: 10,
      retryConfig: {
        maxRetries: 3,
        initialRetryTimeMs: 1000,
        maxRetryTimeMs: 30000,
      },
    });

    logger.info('Fluxos de eventos inicializados com sucesso');
    kafkaLoggingService.info(
      'event.flows',
      'Fluxos de eventos inicializados com sucesso'
    );
  } catch (error) {
    logger.error('Erro ao inicializar fluxos de eventos:', error);
    kafkaLoggingService.error(
      'event.flows',
      'Erro ao inicializar fluxos de eventos',
      error
    );
    throw error;
  }
}
