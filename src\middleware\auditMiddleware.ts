/**
 * Middleware de auditoria
 *
 * Este middleware registra ações sensíveis realizadas pelos usuários,
 * como acesso a recursos protegidos e operações de administração.
 */

import { AuditEventType, AuditSeverity, auditService } from '@services/auditService';
import { authJwtService } from '@services/authJwtService';
import { logger } from '@utils/logger';
import type { APIContext, MiddlewareHandler } from 'astro';

/**
 * Opções para o middleware de auditoria
 */
export interface AuditOptions {
  /**
   * Tipo do evento de auditoria
   */
  eventType: AuditEventType;

  /**
   * Severidade do evento
   */
  severity?: AuditSeverity;

  /**
   * Recurso afetado pela ação
   */
  resource?: string;

  /**
   * Função para extrair ID do recurso da requisição
   */
  getResourceId?: (context: APIContext) => string | undefined;

  /**
   * Ação realizada
   */
  action?: string;

  /**
   * Função para extrair metadados adicionais da requisição
   */
  getMetadata?: (context: APIContext) => Record<string, any> | undefined;

  /**
   * Se deve registrar apenas ações bem-sucedidas
   * @default false
   */
  onlySuccess?: boolean;

  /**
   * Se deve registrar apenas ações com usuário autenticado
   * @default false
   */
  requireAuth?: boolean;
}

/**
 * Middleware para registrar ações de auditoria
 * @param options - Opções de auditoria
 * @returns Middleware handler
 */
export function auditAction(options: AuditOptions): MiddlewareHandler {
  return async (context: APIContext, next: () => Promise<Response>) => {
    const { request, cookies, locals } = context;

    try {
      // Obter informações da requisição
      const ipAddress =
        request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown';

      const userAgent = request.headers.get('user-agent') || 'unknown';

      // Obter token do cabeçalho Authorization ou do cookie
      let token = request.headers.get('Authorization')?.replace('Bearer ', '');

      if (!token) {
        token = cookies.get('access_token')?.value;
      }

      // Verificar token e obter dados do usuário
      let user = null;

      if (token) {
        user = await authJwtService.verifyToken(token, true);
      }

      // Se requer autenticação e não há usuário, pular auditoria
      if (options.requireAuth && !user) {
        return await next();
      }

      // Obter ID do recurso (se aplicável)
      const resourceId = options.getResourceId ? options.getResourceId(context) : undefined;

      // Obter metadados adicionais (se aplicável)
      const metadata = options.getMetadata ? options.getMetadata(context) : undefined;

      // Armazenar dados iniciais do evento
      const eventData = {
        eventType: options.eventType,
        userId: user?.ulid_user,
        userName: user?.name,
        ipAddress,
        userAgent,
        resource: options.resource,
        resourceId,
        action: options.action,
        severity: options.severity || AuditSeverity.INFO,
        metadata,
      };

      // Executar o próximo middleware ou rota
      const response = await next();

      // Se deve registrar apenas ações bem-sucedidas, verificar status da resposta
      if (options.onlySuccess && !response.ok) {
        return response;
      }

      // Adicionar resultado ao evento
      const result = response.ok ? 'success' : 'failure';

      // Registrar evento de auditoria
      await auditService.logEvent({
        ...eventData,
        result,
      });

      return response;
    } catch (error) {
      logger.error('Erro no middleware de auditoria:', error);

      // Continuar para o próximo middleware ou rota
      return await next();
    }
  };
}

/**
 * Middleware para registrar tentativas de login
 * @returns Middleware handler
 */
export function auditLogin(): MiddlewareHandler {
  return async (context: APIContext, next: () => Promise<Response>) => {
    const { request } = context;

    try {
      // Obter informações da requisição
      const ipAddress =
        request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown';

      const userAgent = request.headers.get('user-agent') || 'unknown';

      // Obter dados do corpo da requisição
      let email = '';

      if (request.method === 'POST') {
        try {
          const body = await request.clone().json();
          email = body.email || '';
        } catch (e) {
          // Ignorar erro ao ler corpo
        }
      }

      // Executar o próximo middleware ou rota
      const response = await next();

      // Determinar tipo de evento com base no resultado
      const eventType = response.ok ? AuditEventType.LOGIN_SUCCESS : AuditEventType.LOGIN_FAILURE;

      // Determinar severidade com base no resultado
      const severity = response.ok ? AuditSeverity.INFO : AuditSeverity.WARNING;

      // Obter dados do usuário da resposta (se login bem-sucedido)
      let userId = undefined;
      let userName = undefined;

      if (response.ok) {
        try {
          const responseClone = response.clone();
          const data = await responseClone.json();

          if (data.success && data.user) {
            userId = data.user.ulid_user;
            userName = data.user.name;
          }
        } catch (e) {
          // Ignorar erro ao ler resposta
        }
      }

      // Registrar evento de auditoria
      await auditService.logEvent({
        eventType,
        userId,
        userName,
        ipAddress,
        userAgent,
        resource: 'auth',
        action: 'login',
        result: response.ok ? 'success' : 'failure',
        severity,
        metadata: {
          email,
          statusCode: response.status,
        },
      });

      return response;
    } catch (error) {
      logger.error('Erro no middleware de auditoria de login:', error);

      // Continuar para o próximo middleware ou rota
      return await next();
    }
  };
}

/**
 * Middleware para registrar acesso a recursos sensíveis
 * @param resource - Nome do recurso
 * @param getResourceId - Função para extrair ID do recurso
 * @returns Middleware handler
 */
export function auditResourceAccess(
  resource: string,
  getResourceId?: (context: APIContext) => string | undefined
): MiddlewareHandler {
  return auditAction({
    eventType: AuditEventType.RESOURCE_ACCESSED,
    severity: AuditSeverity.INFO,
    resource,
    getResourceId,
    action: 'access',
    onlySuccess: true,
  });
}

/**
 * Middleware para registrar alterações em recursos
 * @param resource - Nome do recurso
 * @param action - Ação realizada (create, update, delete)
 * @param getResourceId - Função para extrair ID do recurso
 * @param getMetadata - Função para extrair metadados adicionais
 * @returns Middleware handler
 */
export function auditResourceChange(
  resource: string,
  action: 'create' | 'update' | 'delete',
  getResourceId?: (context: APIContext) => string | undefined,
  getMetadata?: (context: APIContext) => Record<string, any> | undefined
): MiddlewareHandler {
  // Mapear ação para tipo de evento
  const eventTypeMap = {
    create: AuditEventType.RESOURCE_CREATED,
    update: AuditEventType.RESOURCE_UPDATED,
    delete: AuditEventType.RESOURCE_DELETED,
  };

  // Mapear ação para severidade
  const severityMap = {
    create: AuditSeverity.INFO,
    update: AuditSeverity.INFO,
    delete: AuditSeverity.WARNING,
  };

  return auditAction({
    eventType: eventTypeMap[action],
    severity: severityMap[action],
    resource,
    getResourceId,
    action,
    getMetadata,
    requireAuth: true,
  });
}
