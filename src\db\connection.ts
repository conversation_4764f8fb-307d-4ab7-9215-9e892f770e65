// src/db/connection.ts
import { Pool } from 'pg';
import { DATABASE_URL } from 'astro:env/server';
import { logger } from '@utils/logger';

/**
 * Pool de conexões PostgreSQL
 * Configurado para uso com o queryHelper
 */
export const pool = new Pool({
  connectionString: DATABASE_URL,
  max: 20, // Máximo de 20 conexões no pool
  idleTimeoutMillis: 30000, // 30 segundos
  connectionTimeoutMillis: 5000, // 5 segundos
});

// Configurar eventos de log
pool.on('error', (err) => {
  logger.error('Erro inesperado no pool de conexões PostgreSQL:', err);
});

pool.on('connect', () => {
  logger.debug('Nova conexão estabelecida com o PostgreSQL');
});

pool.on('remove', () => {
  logger.debug('Conexão removida do pool por inatividade');
});

// Verificação periódica do pool
setInterval(async () => {
  try {
    const totalCount = pool.totalCount;
    const idleCount = pool.idleCount;
    const waitingCount = pool.waitingCount || 0;

    logger.debug('Status do pool de conexões:', {
      total: totalCount,
      idle: idleCount,
      active: totalCount - idleCount,
      waiting: waitingCount,
    });

    // Verificar se o pool está sobrecarregado
    if (waitingCount > 5) {
      logger.warn('Pool de conexões sobrecarregado:', {
        total: totalCount,
        idle: idleCount,
        waiting: waitingCount,
      });
    }
  } catch (error) {
    logger.error('Erro ao verificar status do pool:', error);
  }
}, 60000); // A cada minuto

// Graceful shutdown
process.on('SIGINT', async () => {
  logger.info('Fechando pool de conexões...');
  await pool.end();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  logger.info('Fechando pool de conexões...');
  await pool.end();
  process.exit(0);
});
