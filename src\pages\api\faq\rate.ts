/**
 * API de Avaliação de FAQ
 *
 * Endpoint para processar avaliações de itens de FAQ.
 * Parte da implementação da tarefa 8.7.1 - Implementação de FAQ interativo
 */

import type { APIRoute } from 'astro';
import { FaqRepository } from '../../../domain/repositories/FaqRepository';
import { FaqRating, RateFaqItemUseCase } from '../../../domain/usecases/faq/RateFaqItemUseCase';
import { PostgresFaqRepository } from '../../../infrastructure/database/repositories/PostgresFaqRepository';

// Inicializar repositório
const faqRepository: FaqRepository = new PostgresFaqRepository();

// Inicializar caso de uso
const rateFaqItemUseCase = new RateFaqItemUseCase(faqRepository);

export const POST: APIRoute = async ({ request }) => {
  try {
    // Obter dados da requisição
    const body = await request.json();
    const { id, rating } = body;

    // Validar dados básicos
    if (!id || !rating) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'ID e avaliação são obrigatórios.',
        }),
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    // Validar tipo de avaliação
    if (rating !== 'helpful' && rating !== 'not_helpful') {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Avaliação inválida. Deve ser "helpful" ou "not_helpful".',
        }),
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    // Avaliar o item de FAQ
    const result = await rateFaqItemUseCase.execute({
      id,
      rating: rating as FaqRating,
    });

    if (result.success) {
      return new Response(
        JSON.stringify({
          success: true,
        }),
        {
          status: 200,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }
    return new Response(
      JSON.stringify({
        success: false,
        error: result.error || 'Erro desconhecido ao avaliar item de FAQ.',
      }),
      {
        status: 400,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  } catch (error) {
    console.error('Erro ao processar avaliação de FAQ:', error);

    return new Response(
      JSON.stringify({
        success: false,
        error: 'Ocorreu um erro ao processar a avaliação. Por favor, tente novamente.',
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  }
};
