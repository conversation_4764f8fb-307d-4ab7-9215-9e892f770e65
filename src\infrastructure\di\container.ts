/**
 * Container de injeção de dependências
 *
 * Este arquivo configura o container de injeção de dependências para a aplicação.
 * Ele registra todas as dependências e suas implementações, permitindo a inversão de controle.
 */

import { Container } from 'inversify';
import { interfaces } from 'inversify';
import 'reflect-metadata';

import { DatabaseConnection } from '../../adapters/interfaces/DatabaseConnection';
import { EducationalContentRepository } from '../../application/interfaces/repositories/EducationalContentRepository';
// Interfaces
import { Logger } from '../../application/interfaces/services/Logger';

import { PostgresEducationalContentRepository } from '../../adapters/repositories/PostgresEducationalContentRepository';
import { PostgresConnection } from '../database/PostgresConnection';
// Implementações
import { ConsoleLogger } from '../logging/ConsoleLogger';

// Casos de uso
import { GetEducationalContentUseCase } from '../../application/usecases/content/GetEducationalContentUseCase';

// Controladores
import { EducationalContentController } from '../../adapters/controllers/EducationalContentController';

// Configurações
import { getDatabaseConfig } from '../config/database.config';

// Símbolos para injeção de dependências
export const TYPES = {
  // Interfaces
  Logger: Symbol.for('Logger'),
  DatabaseConnection: Symbol.for('DatabaseConnection'),
  EducationalContentRepository: Symbol.for('EducationalContentRepository'),

  // Casos de uso
  GetEducationalContentUseCase: Symbol.for('GetEducationalContentUseCase'),

  // Controladores
  EducationalContentController: Symbol.for('EducationalContentController'),

  // Configurações
  DatabaseConfig: Symbol.for('DatabaseConfig'),
};

// Criar container
const container = new Container();

// Registrar configurações
container.bind(TYPES.DatabaseConfig).toConstantValue(getDatabaseConfig());

// Registrar serviços de infraestrutura
container.bind<Logger>(TYPES.Logger).to(ConsoleLogger).inSingletonScope();
container
  .bind<DatabaseConnection>(TYPES.DatabaseConnection)
  .to(PostgresConnection)
  .inSingletonScope();

// Registrar repositórios
container
  .bind<EducationalContentRepository>(TYPES.EducationalContentRepository)
  .to(PostgresEducationalContentRepository)
  .inSingletonScope();

// Registrar casos de uso
container
  .bind<GetEducationalContentUseCase>(TYPES.GetEducationalContentUseCase)
  .toDynamicValue((context: interfaces.Context) => {
    const repository = context.container.get<EducationalContentRepository>(
      TYPES.EducationalContentRepository
    );
    return new GetEducationalContentUseCase(repository);
  })
  .inRequestScope();

// Registrar controladores
container
  .bind<EducationalContentController>(TYPES.EducationalContentController)
  .toDynamicValue((context: interfaces.Context) => {
    const useCase = context.container.get<GetEducationalContentUseCase>(
      TYPES.GetEducationalContentUseCase
    );
    const logger = context.container.get<Logger>(TYPES.Logger);
    return new EducationalContentController(useCase, logger);
  })
  .inRequestScope();

export { container };
