---
import { PageTransition } from '../../../components/transitions';
import DaisyButton from '../../../components/ui/DaisyButton.astro';
import DaisyCard from '../../../components/ui/DaisyCard.astro';
import Breadcrumbs from '../../../components/ui/navigation/Breadcrumbs.astro';
import { FiscalDocumentRepository } from '../../../domain/repositories/FiscalDocumentRepository';
import { PostgresFiscalDocumentRepository } from '../../../infrastructure/database/repositories/PostgresFiscalDocumentRepository';
/**
 * Página de Relatórios Fiscais
 *
 * Interface para exportação de relatórios fiscais.
 * Parte da implementação da tarefa 8.7.2 - Gestão de documentos fiscais
 */
import BaseLayout from '../../../layouts/BaseLayout.astro';
import { Container, Section } from '../../../layouts/grid';

// Título da página
const title = 'Relatórios Fiscais';

// Breadcrumbs para a página atual
const breadcrumbItems = [
  { href: '/', label: 'Início' },
  { href: '/admin', label: 'Administração' },
  { href: '/admin/fiscal', label: 'Gestão Fiscal' },
  { label: 'Relatórios' },
];

// Inicializar repositório
const fiscalDocumentRepository: FiscalDocumentRepository = new PostgresFiscalDocumentRepository();

// Obter estatísticas
const stats = await fiscalDocumentRepository.getStats();

// Tipos de documentos disponíveis
const documentTypes = [
  { value: '', label: 'Todos os tipos' },
  { value: 'NFE', label: 'NF-e' },
  { value: 'NFSE', label: 'NFS-e' },
  { value: 'NFCE', label: 'NFC-e' },
];

// Status disponíveis
const documentStatuses = [
  { value: '', label: 'Todos os status' },
  { value: 'ISSUED', label: 'Emitido' },
  { value: 'CANCELLED', label: 'Cancelado' },
];

// Formatos de exportação
const exportFormats = [
  { value: 'csv', label: 'CSV', icon: 'file-text' },
  { value: 'xlsx', label: 'Excel', icon: 'file-text' },
  { value: 'pdf', label: 'PDF', icon: 'file' },
];

// Função para formatar valor monetário
const formatCurrency = (value: number): string => {
  return value.toLocaleString('pt-BR', {
    style: 'currency',
    currency: 'BRL',
  });
};

// Obter data atual e data de 30 dias atrás
const today = new Date();
const thirtyDaysAgo = new Date();
thirtyDaysAgo.setDate(today.getDate() - 30);

// Formatar datas para o formato YYYY-MM-DD
const formatDateForInput = (date: Date): string => {
  return date.toISOString().split('T')[0];
};
---

<BaseLayout title={title} enableViewTransitions={true} transitionType="fade">
  <Container>
    <PageTransition type="fade" duration="0.5s">
      <Section>
        <Breadcrumbs items={breadcrumbItems} class="mb-6" />
        
        <div class="flex justify-between items-center mb-6">
          <h1 class="text-3xl font-bold">{title}</h1>
          
          <DaisyButton 
            href="/admin/fiscal" 
            variant="outline" 
            icon="arrow-left"
            text="Voltar"
          />
        </div>
        
        <div class="stats shadow mb-6 w-full">
          <div class="stat">
            <div class="stat-figure text-primary">
              <i class="icon icon-file-text text-3xl"></i>
            </div>
            <div class="stat-title">Total de Documentos</div>
            <div class="stat-value">{stats.totalDocuments}</div>
          </div>
          
          <div class="stat">
            <div class="stat-figure text-success">
              <i class="icon icon-check-circle text-3xl"></i>
            </div>
            <div class="stat-title">Emitidos</div>
            <div class="stat-value text-success">{stats.issuedDocuments}</div>
          </div>
          
          <div class="stat">
            <div class="stat-figure text-error">
              <i class="icon icon-x-circle text-3xl"></i>
            </div>
            <div class="stat-title">Cancelados</div>
            <div class="stat-value text-error">{stats.cancelledDocuments}</div>
          </div>
          
          <div class="stat">
            <div class="stat-figure text-info">
              <i class="icon icon-dollar-sign text-3xl"></i>
            </div>
            <div class="stat-title">Valor Total</div>
            <div class="stat-value">{formatCurrency(stats.totalValue)}</div>
          </div>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          <DaisyCard>
            <div class="p-6">
              <h2 class="text-xl font-bold mb-4">
                <i class="icon icon-download mr-2"></i>
                Exportar Relatório
              </h2>
              
              <form id="export-form" class="space-y-4">
                <div class="form-control">
                  <label class="label">
                    <span class="label-text">Período</span>
                  </label>
                  <div class="flex gap-2">
                    <div class="flex-1">
                      <label class="label">
                        <span class="label-text">De</span>
                      </label>
                      <input 
                        type="date" 
                        name="startDate" 
                        class="input input-bordered w-full" 
                        value={formatDateForInput(thirtyDaysAgo)}
                      />
                    </div>
                    <div class="flex-1">
                      <label class="label">
                        <span class="label-text">Até</span>
                      </label>
                      <input 
                        type="date" 
                        name="endDate" 
                        class="input input-bordered w-full" 
                        value={formatDateForInput(today)}
                      />
                    </div>
                  </div>
                </div>
                
                <div class="form-control">
                  <label class="label">
                    <span class="label-text">Tipo de Documento</span>
                  </label>
                  <select name="documentType" class="select select-bordered w-full">
                    {documentTypes.map(type => (
                      <option value={type.value}>{type.label}</option>
                    ))}
                  </select>
                </div>
                
                <div class="form-control">
                  <label class="label">
                    <span class="label-text">Status</span>
                  </label>
                  <select name="documentStatus" class="select select-bordered w-full">
                    {documentStatuses.map(status => (
                      <option value={status.value}>{status.label}</option>
                    ))}
                  </select>
                </div>
                
                <div class="form-control">
                  <label class="label">
                    <span class="label-text">CPF/CNPJ do Cliente (opcional)</span>
                  </label>
                  <input 
                    type="text" 
                    name="customerDocument" 
                    class="input input-bordered w-full" 
                    placeholder="Digite o CPF ou CNPJ"
                  />
                </div>
                
                <div class="form-control">
                  <label class="label flex items-center">
                    <span class="label-text">Incluir Itens</span>
                    <input type="checkbox" name="includeItems" class="checkbox ml-2" />
                  </label>
                </div>
                
                <div class="form-control">
                  <label class="label">
                    <span class="label-text">Formato</span>
                  </label>
                  <div class="flex gap-2">
                    {exportFormats.map(format => (
                      <label class="flex-1">
                        <input 
                          type="radio" 
                          name="format" 
                          value={format.value} 
                          class="hidden peer" 
                          checked={format.value === 'csv'}
                        />
                        <div class="btn btn-outline w-full peer-checked:btn-primary">
                          <i class={`icon icon-${format.icon} mr-2`}></i>
                          {format.label}
                        </div>
                      </label>
                    ))}
                  </div>
                </div>
                
                <div class="form-control mt-6">
                  <button type="submit" class="btn btn-primary">
                    <i class="icon icon-download mr-2"></i>
                    Exportar Relatório
                  </button>
                </div>
              </form>
            </div>
          </DaisyCard>
          
          <DaisyCard>
            <div class="p-6">
              <h2 class="text-xl font-bold mb-4">
                <i class="icon icon-bar-chart-2 mr-2"></i>
                Estatísticas Fiscais
              </h2>
              
              <div class="space-y-4">
                <div>
                  <h3 class="font-bold mb-2">Distribuição por Tipo</h3>
                  <div class="overflow-x-auto">
                    <table class="table w-full">
                      <thead>
                        <tr>
                          <th>Tipo</th>
                          <th>Quantidade</th>
                          <th>Percentual</th>
                        </tr>
                      </thead>
                      <tbody>
                        {Object.entries(stats.typeDistribution).map(([type, count]) => (
                          <tr>
                            <td>{type}</td>
                            <td>{count}</td>
                            <td>{stats.totalDocuments > 0 ? ((count as number / stats.totalDocuments) * 100).toFixed(2) + '%' : '0%'}</td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
                
                <div>
                  <h3 class="font-bold mb-2">Distribuição por Status</h3>
                  <div class="overflow-x-auto">
                    <table class="table w-full">
                      <thead>
                        <tr>
                          <th>Status</th>
                          <th>Quantidade</th>
                          <th>Percentual</th>
                        </tr>
                      </thead>
                      <tbody>
                        {Object.entries(stats.statusDistribution).map(([status, count]) => (
                          <tr>
                            <td>{status}</td>
                            <td>{count}</td>
                            <td>{stats.totalDocuments > 0 ? ((count as number / stats.totalDocuments) * 100).toFixed(2) + '%' : '0%'}</td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>
          </DaisyCard>
        </div>
      </Section>
    </PageTransition>
  </Container>
</BaseLayout>

<script>
  // Script para funcionalidade da página de relatórios fiscais
  document.addEventListener('DOMContentLoaded', () => {
    const exportForm = document.getElementById('export-form') as HTMLFormElement;
    
    if (exportForm) {
      exportForm.addEventListener('submit', async (e) => {
        e.preventDefault();
        
        const formData = new FormData(exportForm);
        const startDate = formData.get('startDate') as string;
        const endDate = formData.get('endDate') as string;
        const documentType = formData.get('documentType') as string;
        const documentStatus = formData.get('documentStatus') as string;
        const customerDocument = formData.get('customerDocument') as string;
        const includeItems = formData.has('includeItems');
        const format = formData.get('format') as string;
        
        // Construir filtro
        const filter: any = {};
        
        if (startDate) {
          filter.startDate = new Date(startDate);
        }
        
        if (endDate) {
          // Adicionar um dia para incluir todo o dia final
          const endDateObj = new Date(endDate);
          endDateObj.setDate(endDateObj.getDate() + 1);
          filter.endDate = endDateObj;
        }
        
        if (documentType) {
          filter.type = documentType;
        }
        
        if (documentStatus) {
          filter.status = documentStatus;
        }
        
        if (customerDocument) {
          filter.customerDocumentNumber = customerDocument.replace(/\D/g, '');
        }
        
        try {
          // Iniciar download do relatório
          const response = await fetch('/api/fiscal/export', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              filter,
              format,
              includeItems
            })
          });
          
          if (response.ok) {
            // Obter o nome do arquivo do cabeçalho Content-Disposition
            const contentDisposition = response.headers.get('Content-Disposition');
            let filename = 'relatorio-fiscal.' + format;
            
            if (contentDisposition) {
              const filenameMatch = contentDisposition.match(/filename="(.+)"/);
              if (filenameMatch && filenameMatch[1]) {
                filename = filenameMatch[1];
              }
            }
            
            // Criar blob e link para download
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);
          } else {
            const data = await response.json();
            alert(`Erro ao exportar relatório: ${data.error || 'Erro desconhecido'}`);
          }
        } catch (error) {
          console.error('Erro ao exportar relatório:', error);
          alert('Erro ao exportar relatório. Por favor, tente novamente.');
        }
      });
    }
  });
</script>
