/**
 * API de Usuários Administradores
 *
 * Endpoint para gerenciamento de usuários administradores.
 * Parte da implementação da tarefa 8.8.2 - Gestão de usuários
 */

import type { APIRoute } from 'astro';
import { AdminPermission, AdminRole } from '../../../../domain/entities/AdminUser';
import { AdminUserRepository } from '../../../../domain/repositories/AdminUserRepository';
import { PasswordService } from '../../../../domain/services/PasswordService';
import { TokenService } from '../../../../domain/services/TokenService';
import { CreateAdminUserUseCase } from '../../../../domain/usecases/admin/CreateAdminUserUseCase';
import { ListAdminUsersUseCase } from '../../../../domain/usecases/admin/ListAdminUsersUseCase';
import { PostgresAdminUserRepository } from '../../../../infrastructure/database/repositories/PostgresAdminUserRepository';
import { BcryptPasswordService } from '../../../../infrastructure/services/BcryptPasswordService';
import { JwtTokenService } from '../../../../infrastructure/services/JwtTokenService';

// Inicializar repositório
const adminUserRepository: AdminUserRepository = new PostgresAdminUserRepository();

// Inicializar serviços
const passwordService: PasswordService = new BcryptPasswordService();
const tokenService: TokenService = new JwtTokenService(
  process.env.JWT_SECRET || 'default-secret-key',
  process.env.PASSWORD_RESET_SECRET || 'password-reset-secret-key',
  process.env.EMAIL_VERIFICATION_SECRET || 'email-verification-secret-key'
);

// Inicializar casos de uso
const listAdminUsersUseCase = new ListAdminUsersUseCase(adminUserRepository);
const createAdminUserUseCase = new CreateAdminUserUseCase(adminUserRepository, passwordService);

// Verificar autenticação e permissões
const checkAuth = (request: Request): { userId: string; isAuthorized: boolean } | null => {
  const authHeader = request.headers.get('Authorization');

  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null;
  }

  const token = authHeader.substring(7);
  const payload = tokenService.verifyToken(token);

  if (!payload) {
    return null;
  }

  // Verificar permissões
  const isAuthorized = payload.role === 'admin' || payload.permissions?.includes('users:read');

  return {
    userId: payload.id,
    isAuthorized,
  };
};

export const GET: APIRoute = async ({ request }) => {
  try {
    // Verificar autenticação
    const auth = checkAuth(request);

    if (!auth) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Não autorizado. Faça login para continuar.',
        }),
        {
          status: 401,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    if (!auth.isAuthorized) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Você não tem permissão para acessar este recurso.',
        }),
        {
          status: 403,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    // Obter parâmetros de consulta
    const url = new URL(request.url);
    const username = url.searchParams.get('username');
    const role = url.searchParams.get('role');
    const isActive = url.searchParams.get('isActive');
    const email = url.searchParams.get('email');
    const department = url.searchParams.get('department');
    const page = url.searchParams.get('page');
    const limit = url.searchParams.get('limit');
    const sortField = url.searchParams.get('sortField');
    const sortDirection = url.searchParams.get('sortDirection');

    // Construir filtro
    const filter: any = {};

    if (username) {
      filter.username = username;
    }

    if (role) {
      filter.role = role as AdminRole;
    }

    if (isActive !== null) {
      filter.isActive = isActive === 'true';
    }

    if (email) {
      filter.email = email;
    }

    if (department) {
      filter.department = department;
    }

    // Construir ordenação
    const sort =
      sortField && sortDirection
        ? {
            field: sortField as
              | 'username'
              | 'fullName'
              | 'email'
              | 'role'
              | 'lastLogin'
              | 'createdAt',
            direction: sortDirection as 'asc' | 'desc',
          }
        : undefined;

    // Construir paginação
    const pagination =
      page && limit
        ? {
            page: Number.parseInt(page),
            limit: Number.parseInt(limit),
          }
        : undefined;

    // Executar caso de uso
    const result = await listAdminUsersUseCase.execute({
      filter: Object.keys(filter).length > 0 ? filter : undefined,
      sort,
      pagination,
    });

    if (result.success && result.data) {
      // Remover senha hash da resposta
      const usersWithoutPassword = result.data.users.map((user) => {
        const { passwordHash, ...userWithoutPassword } = user;
        return userWithoutPassword;
      });

      return new Response(
        JSON.stringify({
          success: true,
          data: {
            ...result.data,
            users: usersWithoutPassword,
          },
        }),
        {
          status: 200,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }
    return new Response(
      JSON.stringify({
        success: false,
        error: result.error || 'Erro desconhecido ao listar usuários.',
      }),
      {
        status: 400,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  } catch (error) {
    console.error('Erro ao processar listagem de usuários administrativos:', error);

    return new Response(
      JSON.stringify({
        success: false,
        error: 'Ocorreu um erro ao processar a listagem de usuários. Por favor, tente novamente.',
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  }
};

export const POST: APIRoute = async ({ request }) => {
  try {
    // Verificar autenticação
    const auth = checkAuth(request);

    if (!auth) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Não autorizado. Faça login para continuar.',
        }),
        {
          status: 401,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    // Verificar permissão específica para criar usuários
    const currentUser = await adminUserRepository.getById(auth.userId);

    if (!currentUser || !currentUser.hasPermission('users:write')) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Você não tem permissão para criar usuários.',
        }),
        {
          status: 403,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    // Obter dados do corpo da requisição
    const body = await request.json();

    // Validar dados
    if (
      !body.username ||
      !body.password ||
      !body.role ||
      !body.profile?.fullName ||
      !body.profile?.email
    ) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Dados incompletos. Verifique os campos obrigatórios.',
        }),
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    // Verificar se o usuário atual pode criar usuários com a função especificada
    if (body.role === 'admin' && currentUser.role !== 'admin') {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Apenas administradores podem criar outros administradores.',
        }),
        {
          status: 403,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    // Executar caso de uso
    const result = await createAdminUserUseCase.execute({
      username: body.username,
      password: body.password,
      role: body.role as AdminRole,
      permissions: body.permissions as AdminPermission[],
      profile: body.profile,
      isActive: body.isActive,
    });

    if (result.success && result.data) {
      // Remover senha hash da resposta
      const { passwordHash, ...userWithoutPassword } = result.data;

      return new Response(
        JSON.stringify({
          success: true,
          data: userWithoutPassword,
        }),
        {
          status: 201,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }
    return new Response(
      JSON.stringify({
        success: false,
        error: result.error || 'Erro desconhecido ao criar usuário.',
      }),
      {
        status: 400,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  } catch (error) {
    console.error('Erro ao processar criação de usuário administrativo:', error);

    return new Response(
      JSON.stringify({
        success: false,
        error: 'Ocorreu um erro ao processar a criação do usuário. Por favor, tente novamente.',
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  }
};
