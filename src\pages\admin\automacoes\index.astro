---
import { PageTransition } from '../../../components/transitions';
import DaisyButton from '../../../components/ui/DaisyButton.astro';
import DaisyCard from '../../../components/ui/DaisyCard.astro';
import DaisyTabs from '../../../components/ui/DaisyTabs.astro';
import Breadcrumbs from '../../../components/ui/navigation/Breadcrumbs.astro';
/**
 * Página de Automações de Contato
 *
 * Interface para gerenciar automações de contato.
 * Parte da implementação da tarefa 8.6.3 - Automações de contato
 */
import BaseLayout from '../../../layouts/BaseLayout.astro';
import { Container, Section } from '../../../layouts/grid';

// Título da página
const title = 'Automações de Contato';

// Breadcrumbs para a página atual
const breadcrumbItems = [
  { href: '/', label: 'In<PERSON>cio' },
  { href: '/admin', label: 'Administração' },
  { label: 'Automações de Contato' },
];

// Obter parâmetros de consulta
const tab = Astro.url.searchParams.get('tab') || 'auto-replies';

// Em um cenário real, buscaríamos os dados do repositório
// Por enquanto, usaremos dados de exemplo

// Modelos de resposta automática
const autoReplyTemplates = [
  {
    id: 'template1',
    name: 'Confirmação de Recebimento',
    subject: 'Recebemos sua mensagem: {{subject}}',
    content:
      'Olá {{name}},\n\nRecebemos sua mensagem e responderemos o mais breve possível.\n\nAtenciosamente,\nEquipe de Suporte',
    isActive: true,
    trigger: 'new_message',
    priority: 10,
    usageCount: 156,
  },
  {
    id: 'template2',
    name: 'Fora do Horário Comercial',
    subject: 'Recebemos sua mensagem: {{subject}}',
    content:
      'Olá {{name}},\n\nRecebemos sua mensagem, mas estamos fora do horário comercial no momento. Retornaremos no próximo dia útil.\n\nAtenciosamente,\nEquipe de Suporte',
    isActive: true,
    trigger: 'outside_business_hours',
    priority: 5,
    usageCount: 78,
  },
  {
    id: 'template3',
    name: 'Suporte Técnico',
    subject: 'Suporte Técnico: {{subject}}',
    content:
      'Olá {{name}},\n\nRecebemos sua solicitação de suporte técnico. Nossa equipe está analisando seu caso e entrará em contato em breve.\n\nAtenciosamente,\nEquipe de Suporte Técnico',
    isActive: true,
    trigger: 'specific_category',
    triggerValue: 'technical',
    priority: 20,
    usageCount: 42,
  },
];

// Regras de encaminhamento
const routingRules = [
  {
    id: 'rule1',
    name: 'Encaminhar Suporte Técnico',
    description: 'Encaminha mensagens de suporte técnico para a equipe técnica',
    isActive: true,
    conditions: [{ type: 'category', operator: 'equals', value: 'technical' }],
    actions: [
      { type: 'assign_to_team', value: 'tech_support' },
      { type: 'add_tag', value: 'technical_issue' },
    ],
    priority: 10,
    applicationCount: 87,
  },
  {
    id: 'rule2',
    name: 'Encaminhar Vendas',
    description: 'Encaminha mensagens de vendas para a equipe comercial',
    isActive: true,
    conditions: [{ type: 'category', operator: 'equals', value: 'sales' }],
    actions: [
      { type: 'assign_to_team', value: 'sales_team' },
      { type: 'add_tag', value: 'sales_inquiry' },
    ],
    priority: 20,
    applicationCount: 65,
  },
  {
    id: 'rule3',
    name: 'Encaminhar Mensagens Urgentes',
    description: 'Encaminha mensagens com palavras-chave urgentes',
    isActive: true,
    conditions: [{ type: 'keyword', operator: 'contains', value: 'urgente' }],
    actions: [
      { type: 'set_priority', value: 'urgent' },
      { type: 'add_tag', value: 'urgent_request' },
      { type: 'send_notification', value: 'Nova mensagem urgente recebida' },
    ],
    priority: 5,
    applicationCount: 23,
  },
];

// Regras de priorização
const prioritizationRules = [
  {
    id: 'prule1',
    name: 'Priorizar Mensagens de Faturamento',
    description: 'Define prioridade alta para mensagens relacionadas a faturamento',
    isActive: true,
    conditions: [{ type: 'category', operator: 'equals', value: 'billing' }],
    resultingPriority: 'high',
    executionOrder: 10,
    applicationCount: 45,
  },
  {
    id: 'prule2',
    name: 'Priorizar Clientes Recorrentes',
    description: 'Define prioridade alta para mensagens de clientes recorrentes',
    isActive: true,
    conditions: [{ type: 'repeat_customer', operator: 'is_true', value: true }],
    resultingPriority: 'high',
    executionOrder: 20,
    applicationCount: 78,
  },
  {
    id: 'prule3',
    name: 'Priorizar Mensagens Longas',
    description: 'Define prioridade média para mensagens muito longas',
    isActive: true,
    conditions: [{ type: 'message_length', operator: 'greater_than', value: 500 }],
    resultingPriority: 'medium',
    executionOrder: 30,
    applicationCount: 34,
  },
];

// Função para formatar número
const formatNumber = (num: number): string => {
  return num.toLocaleString('pt-BR');
};

// Abas
const tabs = [
  { id: 'auto-replies', label: 'Respostas Automáticas', count: autoReplyTemplates.length },
  { id: 'routing', label: 'Regras de Encaminhamento', count: routingRules.length },
  { id: 'prioritization', label: 'Regras de Priorização', count: prioritizationRules.length },
];

// Função para obter ícone do gatilho
const getTriggerIcon = (trigger: string): string => {
  switch (trigger) {
    case 'new_message':
      return 'mail';
    case 'specific_category':
      return 'tag';
    case 'specific_keyword':
      return 'search';
    case 'outside_business_hours':
      return 'clock';
    case 'high_volume':
      return 'bar-chart-2';
    default:
      return 'settings';
  }
};

// Função para obter descrição do gatilho
const getTriggerDescription = (trigger: string, value?: string): string => {
  switch (trigger) {
    case 'new_message':
      return 'Qualquer nova mensagem';
    case 'specific_category':
      return `Categoria: ${value}`;
    case 'specific_keyword':
      return `Palavra-chave: ${value}`;
    case 'outside_business_hours':
      return 'Fora do horário comercial';
    case 'high_volume':
      return 'Alto volume de mensagens';
    default:
      return 'Gatilho personalizado';
  }
};

// Função para obter ícone da condição
const getConditionIcon = (type: string): string => {
  switch (type) {
    case 'category':
      return 'tag';
    case 'keyword':
      return 'search';
    case 'email_domain':
      return 'at-sign';
    case 'priority':
      return 'flag';
    case 'time_of_day':
      return 'clock';
    case 'day_of_week':
      return 'calendar';
    case 'has_attachment':
      return 'paperclip';
    case 'message_length':
      return 'align-left';
    case 'repeat_customer':
      return 'repeat';
    case 'time_sensitive':
      return 'alert-circle';
    default:
      return 'filter';
  }
};

// Função para obter ícone da ação
const getActionIcon = (type: string): string => {
  switch (type) {
    case 'assign_to_user':
      return 'user';
    case 'assign_to_team':
      return 'users';
    case 'set_priority':
      return 'flag';
    case 'add_tag':
      return 'tag';
    case 'send_notification':
      return 'bell';
    default:
      return 'settings';
  }
};

// Função para obter descrição da ação
const getActionDescription = (action: {
  type: string;
  value: string | string[] | number;
}): string => {
  switch (action.type) {
    case 'assign_to_user':
      return `Atribuir ao usuário: ${action.value}`;
    case 'assign_to_team':
      return `Atribuir à equipe: ${action.value}`;
    case 'set_priority':
      return `Definir prioridade: ${action.value}`;
    case 'add_tag':
      if (Array.isArray(action.value)) {
        return `Adicionar tags: ${action.value.join(', ')}`;
      }
      return `Adicionar tag: ${action.value}`;
    case 'send_notification':
      return `Enviar notificação: ${action.value}`;
    default:
      return 'Ação personalizada';
  }
};

// Função para obter cor da prioridade
const getPriorityColor = (priority: string): string => {
  switch (priority) {
    case 'low':
      return 'text-success';
    case 'medium':
      return 'text-info';
    case 'high':
      return 'text-warning';
    case 'urgent':
      return 'text-error';
    default:
      return '';
  }
};

// Função para obter label da prioridade
const getPriorityLabel = (priority: string): string => {
  switch (priority) {
    case 'low':
      return 'Baixa';
    case 'medium':
      return 'Média';
    case 'high':
      return 'Alta';
    case 'urgent':
      return 'Urgente';
    default:
      return priority;
  }
};
---

<BaseLayout title={title} enableViewTransitions={true} transitionType="fade">
  <Container>
    <PageTransition type="fade" duration="0.5s">
      <Section>
        <Breadcrumbs items={breadcrumbItems} class="mb-6" />
        
        <div class="flex justify-between items-center mb-6">
          <h1 class="text-3xl font-bold">{title}</h1>
          
          <div class="flex gap-2">
            <DaisyButton 
              href={`/admin/automacoes/nova?tipo=${tab}`}
              variant="primary" 
              icon="plus"
              text="Nova Automação"
            />
            
            <DaisyButton 
              href="/admin/automacoes/estatisticas" 
              variant="outline" 
              icon="bar-chart-2"
              text="Estatísticas"
            />
          </div>
        </div>
        
        <div class="tabs-container">
          <div class="tabs tabs-boxed mb-6">
            {tabs.map(t => (
              <a 
                href={`/admin/automacoes?tab=${t.id}`} 
                class={`tab ${t.id === tab ? 'tab-active' : ''}`}
              >
                {t.label} {t.count > 0 && <span class="badge badge-sm ml-1">{t.count}</span>}
              </a>
            ))}
          </div>
          
          <div class="tab-content">
            {tab === 'auto-replies' && (
              <div class="space-y-6">
                {autoReplyTemplates.length === 0 ? (
                  <div class="text-center py-12">
                    <div class="text-4xl text-gray-400 mb-4">
                      <i class="icon icon-mail"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-2">Nenhum modelo de resposta automática</h3>
                    <p class="text-gray-500 mb-4">Crie modelos para responder automaticamente às mensagens de contato.</p>
                    <DaisyButton 
                      href="/admin/automacoes/nova?tipo=auto-replies"
                      variant="primary" 
                      icon="plus"
                      text="Criar Modelo"
                    />
                  </div>
                ) : (
                  autoReplyTemplates.map(template => (
                    <DaisyCard>
                      <div class="p-6">
                        <div class="flex justify-between items-start mb-4">
                          <div class="flex items-center">
                            <div class={`text-2xl mr-3 ${template.isActive ? 'text-primary' : 'text-gray-400'}`}>
                              <i class={`icon icon-${getTriggerIcon(template.trigger)}`}></i>
                            </div>
                            <div>
                              <h3 class="text-xl font-bold">{template.name}</h3>
                              <p class="text-sm text-gray-500">{getTriggerDescription(template.trigger, template.triggerValue)}</p>
                            </div>
                          </div>
                          
                          <div class="flex items-center">
                            <span class="badge mr-2">{formatNumber(template.usageCount)} usos</span>
                            <div class="form-control">
                              <label class="cursor-pointer label">
                                <input 
                                  type="checkbox" 
                                  class="toggle toggle-primary" 
                                  checked={template.isActive}
                                  data-id={template.id}
                                  data-type="auto-reply"
                                />
                              </label>
                            </div>
                          </div>
                        </div>
                        
                        <div class="mb-4">
                          <div class="font-bold mb-1">Assunto:</div>
                          <div class="bg-base-200 p-2 rounded">{template.subject}</div>
                        </div>
                        
                        <div class="mb-4">
                          <div class="font-bold mb-1">Conteúdo:</div>
                          <div class="bg-base-200 p-2 rounded whitespace-pre-wrap">{template.content}</div>
                        </div>
                        
                        <div class="flex justify-between items-center">
                          <div class="text-sm text-gray-500">
                            Prioridade: {template.priority}
                          </div>
                          
                          <div class="flex gap-1">
                            <a 
                              href={`/admin/automacoes/editar?id=${template.id}&tipo=auto-replies`} 
                              class="btn btn-sm btn-ghost"
                            >
                              <i class="icon icon-edit-2"></i>
                            </a>
                            
                            <button 
                              class="btn btn-sm btn-ghost text-error"
                              data-action="delete"
                              data-id={template.id}
                              data-type="auto-reply"
                            >
                              <i class="icon icon-trash"></i>
                            </button>
                          </div>
                        </div>
                      </div>
                    </DaisyCard>
                  ))
                )}
              </div>
            )}
            
            {tab === 'routing' && (
              <div class="space-y-6">
                {routingRules.length === 0 ? (
                  <div class="text-center py-12">
                    <div class="text-4xl text-gray-400 mb-4">
                      <i class="icon icon-git-branch"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-2">Nenhuma regra de encaminhamento</h3>
                    <p class="text-gray-500 mb-4">Crie regras para encaminhar automaticamente as mensagens de contato.</p>
                    <DaisyButton 
                      href="/admin/automacoes/nova?tipo=routing"
                      variant="primary" 
                      icon="plus"
                      text="Criar Regra"
                    />
                  </div>
                ) : (
                  routingRules.map(rule => (
                    <DaisyCard>
                      <div class="p-6">
                        <div class="flex justify-between items-start mb-4">
                          <div>
                            <h3 class="text-xl font-bold">{rule.name}</h3>
                            <p class="text-sm text-gray-500">{rule.description}</p>
                          </div>
                          
                          <div class="flex items-center">
                            <span class="badge mr-2">{formatNumber(rule.applicationCount)} aplicações</span>
                            <div class="form-control">
                              <label class="cursor-pointer label">
                                <input 
                                  type="checkbox" 
                                  class="toggle toggle-primary" 
                                  checked={rule.isActive}
                                  data-id={rule.id}
                                  data-type="routing"
                                />
                              </label>
                            </div>
                          </div>
                        </div>
                        
                        <div class="mb-4">
                          <div class="font-bold mb-2">Condições:</div>
                          <div class="space-y-2">
                            {rule.conditions.map(condition => (
                              <div class="flex items-center bg-base-200 p-2 rounded">
                                <i class={`icon icon-${getConditionIcon(condition.type)} mr-2`}></i>
                                <span>{condition.type}: {condition.operator} {JSON.stringify(condition.value)}</span>
                              </div>
                            ))}
                          </div>
                        </div>
                        
                        <div class="mb-4">
                          <div class="font-bold mb-2">Ações:</div>
                          <div class="space-y-2">
                            {rule.actions.map(action => (
                              <div class="flex items-center bg-base-200 p-2 rounded">
                                <i class={`icon icon-${getActionIcon(action.type)} mr-2`}></i>
                                <span>{getActionDescription(action)}</span>
                              </div>
                            ))}
                          </div>
                        </div>
                        
                        <div class="flex justify-between items-center">
                          <div class="text-sm text-gray-500">
                            Prioridade: {rule.priority}
                          </div>
                          
                          <div class="flex gap-1">
                            <a 
                              href={`/admin/automacoes/editar?id=${rule.id}&tipo=routing`} 
                              class="btn btn-sm btn-ghost"
                            >
                              <i class="icon icon-edit-2"></i>
                            </a>
                            
                            <button 
                              class="btn btn-sm btn-ghost text-error"
                              data-action="delete"
                              data-id={rule.id}
                              data-type="routing"
                            >
                              <i class="icon icon-trash"></i>
                            </button>
                          </div>
                        </div>
                      </div>
                    </DaisyCard>
                  ))
                )}
              </div>
            )}
            
            {tab === 'prioritization' && (
              <div class="space-y-6">
                {prioritizationRules.length === 0 ? (
                  <div class="text-center py-12">
                    <div class="text-4xl text-gray-400 mb-4">
                      <i class="icon icon-flag"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-2">Nenhuma regra de priorização</h3>
                    <p class="text-gray-500 mb-4">Crie regras para priorizar automaticamente as mensagens de contato.</p>
                    <DaisyButton 
                      href="/admin/automacoes/nova?tipo=prioritization"
                      variant="primary" 
                      icon="plus"
                      text="Criar Regra"
                    />
                  </div>
                ) : (
                  prioritizationRules.map(rule => (
                    <DaisyCard>
                      <div class="p-6">
                        <div class="flex justify-between items-start mb-4">
                          <div>
                            <h3 class="text-xl font-bold">{rule.name}</h3>
                            <p class="text-sm text-gray-500">{rule.description}</p>
                          </div>
                          
                          <div class="flex items-center">
                            <span class="badge mr-2">{formatNumber(rule.applicationCount)} aplicações</span>
                            <div class="form-control">
                              <label class="cursor-pointer label">
                                <input 
                                  type="checkbox" 
                                  class="toggle toggle-primary" 
                                  checked={rule.isActive}
                                  data-id={rule.id}
                                  data-type="prioritization"
                                />
                              </label>
                            </div>
                          </div>
                        </div>
                        
                        <div class="mb-4">
                          <div class="font-bold mb-2">Condições:</div>
                          <div class="space-y-2">
                            {rule.conditions.map(condition => (
                              <div class="flex items-center bg-base-200 p-2 rounded">
                                <i class={`icon icon-${getConditionIcon(condition.type)} mr-2`}></i>
                                <span>{condition.type}: {condition.operator} {JSON.stringify(condition.value)}</span>
                              </div>
                            ))}
                          </div>
                        </div>
                        
                        <div class="mb-4">
                          <div class="font-bold mb-2">Prioridade resultante:</div>
                          <div class="flex items-center bg-base-200 p-2 rounded">
                            <i class="icon icon-flag mr-2"></i>
                            <span class={getPriorityColor(rule.resultingPriority)}>
                              {getPriorityLabel(rule.resultingPriority)}
                            </span>
                          </div>
                        </div>
                        
                        <div class="flex justify-between items-center">
                          <div class="text-sm text-gray-500">
                            Ordem de execução: {rule.executionOrder}
                          </div>
                          
                          <div class="flex gap-1">
                            <a 
                              href={`/admin/automacoes/editar?id=${rule.id}&tipo=prioritization`} 
                              class="btn btn-sm btn-ghost"
                            >
                              <i class="icon icon-edit-2"></i>
                            </a>
                            
                            <button 
                              class="btn btn-sm btn-ghost text-error"
                              data-action="delete"
                              data-id={rule.id}
                              data-type="prioritization"
                            >
                              <i class="icon icon-trash"></i>
                            </button>
                          </div>
                        </div>
                      </div>
                    </DaisyCard>
                  ))
                )}
              </div>
            )}
          </div>
        </div>
      </Section>
    </PageTransition>
  </Container>
</BaseLayout>

<script>
  // Script para funcionalidade da página de automações
  document.addEventListener('DOMContentLoaded', () => {
    // Alternar estado ativo/inativo
    const toggles = document.querySelectorAll('.toggle') as NodeListOf<HTMLInputElement>;
    
    toggles.forEach(toggle => {
      toggle.addEventListener('change', async () => {
        const id = toggle.getAttribute('data-id');
        const type = toggle.getAttribute('data-type');
        const isActive = toggle.checked;
        
        if (!id || !type) {
          return;
        }
        
        // Em um cenário real, aqui seria feita uma chamada para a API
        console.log(`${isActive ? 'Ativando' : 'Desativando'} ${type} ${id}`);
        
        // Simular atualização bem-sucedida
        alert(`${isActive ? 'Ativado' : 'Desativado'} com sucesso!`);
      });
    });
    
    // Excluir automação
    const deleteButtons = document.querySelectorAll('[data-action="delete"]');
    
    deleteButtons.forEach(button => {
      button.addEventListener('click', async () => {
        const id = button.getAttribute('data-id');
        const type = button.getAttribute('data-type');
        
        if (!id || !type) {
          return;
        }
        
        let typeName = '';
        switch (type) {
          case 'auto-reply':
            typeName = 'modelo de resposta automática';
            break;
          case 'routing':
            typeName = 'regra de encaminhamento';
            break;
          case 'prioritization':
            typeName = 'regra de priorização';
            break;
        }
        
        if (confirm(`Tem certeza que deseja excluir este ${typeName}?`)) {
          // Em um cenário real, aqui seria feita uma chamada para a API
          console.log(`Excluindo ${type} ${id}`);
          
          // Simular exclusão bem-sucedida
          alert('Excluído com sucesso!');
          window.location.reload();
        }
      });
    });
  });
</script>
