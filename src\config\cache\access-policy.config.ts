/**
 * Configuração de políticas de acesso para o Valkey
 * 
 * Este arquivo define as políticas de acesso para diferentes tipos de usuários
 * e serviços que acessam o Valkey.
 */

/**
 * Interface para política de acesso
 */
export interface AccessPolicy {
  /**
   * Nome da política
   */
  name: string;
  
  /**
   * Descrição da política
   */
  description: string;
  
  /**
   * Comandos permitidos
   */
  allowedCommands: string[];
  
  /**
   * Categorias de comandos permitidas
   */
  allowedCategories: string[];
  
  /**
   * Padrões de chaves permitidos
   */
  allowedKeyPatterns: string[];
  
  /**
   * Limite de memória (em bytes, 0 = sem limite)
   */
  memoryLimit: number;
}

/**
 * Categorias de comandos do Valkey
 */
export const CommandCategories = {
  // Comandos de leitura
  READ: '@read',
  
  // Comandos de escrita
  WRITE: '@write',
  
  // Comandos administrativos
  ADMIN: '@admin',
  
  // Comandos perigosos
  DANGEROUS: '@dangerous',
  
  // Comandos de conexão
  CONNECTION: '@connection',
  
  // Comandos de bloqueio
  BLOCKING: '@blocking',
  
  // Comandos de transação
  TRANSACTION: '@transaction',
  
  // Comandos de script
  SCRIPTING: '@scripting',
  
  // Comandos de geolocalização
  GEO: '@geo',
  
  // Comandos de stream
  STREAM: '@stream',
  
  // Comandos de bitmap
  BITMAP: '@bitmap',
  
  // Comandos de hyperloglog
  HYPERLOGLOG: '@hyperloglog',
  
  // Comandos de cluster
  CLUSTER: '@cluster',
  
  // Comandos de pub/sub
  PUBSUB: '@pubsub',
  
  // Comandos de ordenação
  SORTED_SET: '@sortedset',
  
  // Comandos de conjunto
  SET: '@set',
  
  // Comandos de string
  STRING: '@string',
  
  // Comandos de hash
  HASH: '@hash',
  
  // Comandos de lista
  LIST: '@list',
  
  // Todos os comandos
  ALL: '@all',
};

/**
 * Políticas de acesso predefinidas
 */
export const accessPolicies: Record<string, AccessPolicy> = {
  // Política de acesso para administradores
  'admin': {
    name: 'admin',
    description: 'Acesso completo para administradores',
    allowedCommands: [],
    allowedCategories: [CommandCategories.ALL],
    allowedKeyPatterns: ['*'],
    memoryLimit: 0, // Sem limite
  },
  
  // Política de acesso para aplicação web
  'web-app': {
    name: 'web-app',
    description: 'Acesso para aplicação web',
    allowedCommands: [],
    allowedCategories: [
      CommandCategories.READ,
      CommandCategories.WRITE,
      CommandCategories.CONNECTION,
      CommandCategories.TRANSACTION,
      CommandCategories.GEO,
      CommandCategories.STREAM,
      CommandCategories.BITMAP,
      CommandCategories.HYPERLOGLOG,
      CommandCategories.PUBSUB,
      CommandCategories.SORTED_SET,
      CommandCategories.SET,
      CommandCategories.STRING,
      CommandCategories.HASH,
      CommandCategories.LIST,
    ],
    allowedKeyPatterns: ['web:*', 'session:*', 'user:*', 'cache:*'],
    memoryLimit: 1073741824, // 1GB
  },
  
  // Política de acesso para serviços de background
  'background-service': {
    name: 'background-service',
    description: 'Acesso para serviços de background',
    allowedCommands: [],
    allowedCategories: [
      CommandCategories.READ,
      CommandCategories.WRITE,
      CommandCategories.CONNECTION,
      CommandCategories.TRANSACTION,
      CommandCategories.STREAM,
      CommandCategories.PUBSUB,
    ],
    allowedKeyPatterns: ['job:*', 'queue:*', 'task:*', 'schedule:*'],
    memoryLimit: 536870912, // 512MB
  },
  
  // Política de acesso para cache de sessão
  'session-cache': {
    name: 'session-cache',
    description: 'Acesso para cache de sessão',
    allowedCommands: ['GET', 'SET', 'DEL', 'EXPIRE', 'TTL', 'SCAN', 'EXISTS'],
    allowedCategories: [],
    allowedKeyPatterns: ['session:*'],
    memoryLimit: 268435456, // 256MB
  },
  
  // Política de acesso para cache de dados
  'data-cache': {
    name: 'data-cache',
    description: 'Acesso para cache de dados',
    allowedCommands: ['GET', 'SET', 'DEL', 'EXPIRE', 'TTL', 'SCAN', 'EXISTS', 'MGET', 'MSET'],
    allowedCategories: [
      CommandCategories.STRING,
      CommandCategories.HASH,
      CommandCategories.LIST,
      CommandCategories.SET,
      CommandCategories.SORTED_SET,
    ],
    allowedKeyPatterns: ['cache:*', 'data:*'],
    memoryLimit: 536870912, // 512MB
  },
  
  // Política de acesso para monitoramento
  'monitoring': {
    name: 'monitoring',
    description: 'Acesso para monitoramento',
    allowedCommands: ['PING', 'INFO', 'CLIENT', 'SLOWLOG', 'MEMORY', 'MONITOR'],
    allowedCategories: [],
    allowedKeyPatterns: [],
    memoryLimit: 0, // Sem limite
  },
  
  // Política de acesso para leitura
  'read-only': {
    name: 'read-only',
    description: 'Acesso somente leitura',
    allowedCommands: [],
    allowedCategories: [CommandCategories.READ],
    allowedKeyPatterns: ['*'],
    memoryLimit: 0, // Sem limite
  },
  
  // Política de acesso para pub/sub
  'pubsub': {
    name: 'pubsub',
    description: 'Acesso para pub/sub',
    allowedCommands: ['PUBLISH', 'SUBSCRIBE', 'PSUBSCRIBE', 'UNSUBSCRIBE', 'PUNSUBSCRIBE', 'PUBSUB'],
    allowedCategories: [],
    allowedKeyPatterns: [],
    memoryLimit: 104857600, // 100MB
  },
};

/**
 * Obtém uma política de acesso pelo nome
 * @param name Nome da política
 * @returns Política de acesso ou undefined se não encontrada
 */
export function getAccessPolicy(name: string): AccessPolicy | undefined {
  return accessPolicies[name];
}

/**
 * Gera um comando ACL para uma política de acesso
 * @param policy Política de acesso
 * @param username Nome do usuário
 * @param password Senha do usuário
 * @returns Comando ACL
 */
export function generateACLCommand(
  policy: AccessPolicy,
  username: string,
  password: string
): string {
  let command = `ACL SETUSER ${username} on >${password}`;
  
  // Adicionar padrões de chaves
  for (const pattern of policy.allowedKeyPatterns) {
    command += ` ~${pattern}`;
  }
  
  // Negar todos os comandos primeiro
  command += ' -@all';
  
  // Adicionar categorias permitidas
  for (const category of policy.allowedCategories) {
    command += ` +${category}`;
  }
  
  // Adicionar comandos permitidos
  for (const cmd of policy.allowedCommands) {
    command += ` +${cmd}`;
  }
  
  return command;
}

/**
 * Gera um arquivo ACL para todas as políticas
 * @returns Conteúdo do arquivo ACL
 */
export function generateACLFile(): string {
  const lines: string[] = [];
  
  // Adicionar cabeçalho
  lines.push(`# Arquivo ACL do Valkey`);
  lines.push(`# Gerado automaticamente em ${new Date().toISOString()}`);
  lines.push(``);
  
  // Adicionar usuário padrão
  lines.push(`user default off`);
  lines.push(``);
  
  // Adicionar políticas
  for (const [name, policy] of Object.entries(accessPolicies)) {
    lines.push(`# Política: ${policy.name} - ${policy.description}`);
    
    // Criar usuário com nome da política
    let userLine = `user ${name} on #${policy.description}`;
    
    // Adicionar padrões de chaves
    for (const pattern of policy.allowedKeyPatterns) {
      userLine += ` ~${pattern}`;
    }
    
    // Negar todos os comandos primeiro
    userLine += ' -@all';
    
    // Adicionar categorias permitidas
    for (const category of policy.allowedCategories) {
      userLine += ` +${category}`;
    }
    
    // Adicionar comandos permitidos
    for (const cmd of policy.allowedCommands) {
      userLine += ` +${cmd}`;
    }
    
    // Adicionar limite de memória se definido
    if (policy.memoryLimit > 0) {
      userLine += ` resetchannels allchannels &${policy.memoryLimit}`;
    }
    
    lines.push(userLine);
    lines.push(``);
  }
  
  return lines.join('\n');
}
