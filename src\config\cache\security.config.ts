/**
 * Configuração de segurança para o Valkey
 *
 * Este arquivo define as configurações de segurança para o Valkey,
 * incluindo autenticação, TLS/SSL e políticas de acesso.
 */

import * as crypto from 'node:crypto';
import * as fs from 'node:fs';
import * as path from 'node:path';
import { logger } from '@utils/logger';

/**
 * Tipos de autenticação suportados
 */
export enum AuthenticationType {
  /**
   * Autenticação básica com usuário e senha
   */
  BASIC = 'basic',

  /**
   * Autenticação com ACL (Access Control List)
   */
  ACL = 'acl',

  /**
   * Sem autenticação (não recomendado para produção)
   */
  NONE = 'none',
}

/**
 * Interface para configuração de autenticação
 */
export interface AuthenticationConfig {
  /**
   * Tipo de autenticação
   */
  type: AuthenticationType;

  /**
   * Nome de usuário
   */
  username?: string;

  /**
   * <PERSON>ha
   */
  password?: string;

  /**
   * Caminho para arquivo de ACL
   */
  aclPath?: string;

  /**
   * Descrição da configuração
   */
  description: string;
}

/**
 * Interface para configuração de TLS/SSL
 */
export interface TLSConfig {
  /**
   * Se TLS/SSL está habilitado
   */
  enabled: boolean;

  /**
   * Caminho para o certificado
   */
  certPath?: string;

  /**
   * Caminho para a chave privada
   */
  keyPath?: string;

  /**
   * Caminho para o CA (Certificate Authority)
   */
  caPath?: string;

  /**
   * Se deve verificar certificados de cliente
   */
  verifyClient?: boolean;

  /**
   * Descrição da configuração
   */
  description: string;
}

/**
 * Interface para configuração de políticas de acesso
 */
export interface AccessPolicyConfig {
  /**
   * Se as políticas de acesso estão habilitadas
   */
  enabled: boolean;

  /**
   * Comandos permitidos
   */
  allowedCommands?: string[];

  /**
   * Comandos bloqueados
   */
  blockedCommands?: string[];

  /**
   * Padrões de chaves permitidas
   */
  allowedKeyPatterns?: string[];

  /**
   * Padrões de chaves bloqueadas
   */
  blockedKeyPatterns?: string[];

  /**
   * Descrição da configuração
   */
  description: string;
}

/**
 * Interface para configuração de segurança
 */
export interface SecurityConfig {
  /**
   * Configuração de autenticação
   */
  authentication: AuthenticationConfig;

  /**
   * Configuração de TLS/SSL
   */
  tls: TLSConfig;

  /**
   * Configuração de políticas de acesso
   */
  accessPolicy: AccessPolicyConfig;

  /**
   * Comandos perigosos a serem renomeados
   */
  renameCommands: Record<string, string>;

  /**
   * Configurações adicionais de segurança
   */
  additionalSettings: Record<string, string>;
}

/**
 * Configuração de segurança para diferentes ambientes
 */
export const securityConfigs: Record<string, SecurityConfig> = {
  // Ambiente de desenvolvimento
  development: {
    authentication: {
      type: AuthenticationType.BASIC,
      username: process.env.VALKEY_USERNAME || 'dev',
      password: process.env.VALKEY_PASSWORD || 'dev_password',
      description: 'Autenticação básica para ambiente de desenvolvimento',
    },
    tls: {
      enabled: false,
      description: 'TLS desabilitado para ambiente de desenvolvimento',
    },
    accessPolicy: {
      enabled: false,
      description: 'Políticas de acesso desabilitadas para ambiente de desenvolvimento',
    },
    renameCommands: {},
    additionalSettings: {
      'protected-mode': 'yes',
    },
  },

  // Ambiente de teste
  test: {
    authentication: {
      type: AuthenticationType.BASIC,
      username: process.env.VALKEY_USERNAME || 'test',
      password: process.env.VALKEY_PASSWORD || 'test_password',
      description: 'Autenticação básica para ambiente de teste',
    },
    tls: {
      enabled: false,
      description: 'TLS desabilitado para ambiente de teste',
    },
    accessPolicy: {
      enabled: true,
      blockedCommands: [
        'FLUSHALL',
        'FLUSHDB',
        'CONFIG',
        'SHUTDOWN',
        'BGREWRITEAOF',
        'BGSAVE',
        'SAVE',
        'DEBUG',
      ],
      description: 'Políticas de acesso básicas para ambiente de teste',
    },
    renameCommands: {
      FLUSHALL: '',
      FLUSHDB: '',
      CONFIG: 'CONFIG_SECURE',
    },
    additionalSettings: {
      'protected-mode': 'yes',
    },
  },

  // Ambiente de produção
  production: {
    authentication: {
      type: AuthenticationType.BASIC,
      username: process.env.VALKEY_USERNAME,
      password: process.env.VALKEY_PASSWORD,
      description: 'Autenticação básica para ambiente de produção',
    },
    tls: {
      enabled: process.env.VALKEY_TLS === 'true',
      certPath: process.env.VALKEY_CERT_PATH,
      keyPath: process.env.VALKEY_KEY_PATH,
      caPath: process.env.VALKEY_CA_PATH,
      verifyClient: true,
      description: 'TLS habilitado para ambiente de produção',
    },
    accessPolicy: {
      enabled: true,
      blockedCommands: [
        'FLUSHALL',
        'FLUSHDB',
        'CONFIG',
        'SHUTDOWN',
        'BGREWRITEAOF',
        'BGSAVE',
        'SAVE',
        'DEBUG',
        'KEYS',
        'MONITOR',
      ],
      description: 'Políticas de acesso restritas para ambiente de produção',
    },
    renameCommands: {
      FLUSHALL: '',
      FLUSHDB: '',
      CONFIG: 'CONFIG_SECURE',
      SHUTDOWN: '',
      DEBUG: '',
    },
    additionalSettings: {
      'protected-mode': 'yes',
      'tcp-backlog': '511',
      timeout: '0',
      'tcp-keepalive': '300',
    },
  },
};

/**
 * Obtém a configuração de segurança para o ambiente atual
 * @returns Configuração de segurança
 */
export function getSecurityConfig(): SecurityConfig {
  const env = process.env.NODE_ENV || 'development';
  return securityConfigs[env] || securityConfigs.development;
}

/**
 * Gera uma senha forte para o Valkey
 * @param length Comprimento da senha
 * @returns Senha gerada
 */
export function generateStrongPassword(length = 32): string {
  const chars =
    'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()-_=+[]{}|;:,.<>?';
  let password = '';

  // Gerar bytes aleatórios
  const randomBytes = crypto.randomBytes(length);

  // Converter bytes para caracteres
  for (let i = 0; i < length; i++) {
    const index = randomBytes[i] % chars.length;
    password += chars.charAt(index);
  }

  return password;
}

/**
 * Gera a configuração para o arquivo valkey.conf
 * @returns Linhas de configuração para valkey.conf
 */
export function generateSecurityConfig(): string[] {
  const config = getSecurityConfig();
  const lines: string[] = [];

  // Adicionar cabeçalho
  lines.push('# Configuração de segurança do Valkey');
  lines.push(`# Gerado automaticamente em ${new Date().toISOString()}`);
  lines.push('');

  // Configuração de autenticação
  if (config.authentication.type === AuthenticationType.BASIC) {
    if (config.authentication.username && config.authentication.password) {
      lines.push('# Autenticação básica');
      lines.push(`requirepass ${config.authentication.password}`);

      if (config.authentication.username !== 'default') {
        lines.push(
          `user ${config.authentication.username} on >${config.authentication.password} ~* +@all`
        );
      }

      lines.push('');
    }
  } else if (config.authentication.type === AuthenticationType.ACL) {
    lines.push('# Autenticação com ACL');
    lines.push(`aclfile ${config.authentication.aclPath || '/etc/valkey/users.acl'}`);
    lines.push('');
  }

  // Configuração de TLS/SSL
  if (config.tls.enabled) {
    lines.push('# Configuração de TLS/SSL');

    if (config.tls.certPath) {
      lines.push(`tls-cert-file ${config.tls.certPath}`);
    }

    if (config.tls.keyPath) {
      lines.push(`tls-key-file ${config.tls.keyPath}`);
    }

    if (config.tls.caPath) {
      lines.push(`tls-ca-cert-file ${config.tls.caPath}`);
    }

    if (config.tls.verifyClient) {
      lines.push('tls-auth-clients yes');
    } else {
      lines.push('tls-auth-clients optional');
    }

    lines.push(`tls-protocols "TLSv1.2 TLSv1.3"`);
    lines.push('tls-prefer-server-ciphers yes');
    lines.push('');
  }

  // Configuração de políticas de acesso
  if (config.accessPolicy.enabled) {
    lines.push('# Políticas de acesso');

    if (config.accessPolicy.blockedCommands && config.accessPolicy.blockedCommands.length > 0) {
      for (const command of config.accessPolicy.blockedCommands) {
        if (config.renameCommands[command] === '') {
          lines.push(`rename-command ${command} ""`);
        } else if (config.renameCommands[command]) {
          lines.push(`rename-command ${command} "${config.renameCommands[command]}"`);
        }
      }
    }

    lines.push('');
  }

  // Configurações adicionais
  if (Object.keys(config.additionalSettings).length > 0) {
    lines.push('# Configurações adicionais de segurança');

    for (const [key, value] of Object.entries(config.additionalSettings)) {
      lines.push(`${key} ${value}`);
    }

    lines.push('');
  }

  return lines;
}

/**
 * Salva a configuração de segurança em um arquivo
 * @param filePath Caminho do arquivo
 * @returns Verdadeiro se o arquivo foi salvo com sucesso
 */
export function saveSecurityConfig(filePath: string): boolean {
  try {
    const config = generateSecurityConfig();
    fs.writeFileSync(filePath, config.join('\n'), 'utf8');
    logger.info(`Configuração de segurança salva em ${filePath}`);
    return true;
  } catch (error) {
    logger.error('Erro ao salvar configuração de segurança:', error);
    return false;
  }
}
