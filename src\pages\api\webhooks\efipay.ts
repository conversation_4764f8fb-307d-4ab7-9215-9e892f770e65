import { webhookService } from '@services/webhookService';
// src/pages/api/webhooks/efipay.ts
import type { APIRoute } from 'astro';

/**
 * Endpoint para receber webhooks da Efí Pay
 *
 * Este endpoint recebe notificações de pagamentos PIX, devoluções e outras
 * atualizações de status enviadas pela Efí Pay.
 *
 * A URL deste endpoint deve ser configurada na conta da Efí Pay.
 */
export const POST: APIRoute = async ({ request }) => {
  try {
    // Obter assinatura do webhook
    const signature = request.headers.get('X-Efipay-Signature');

    // Obter corpo da requisição
    const body = await request.text();

    // Processar webhook
    const result = await webhookService.processWebhook(body, signature || undefined);

    // Retornar resposta de sucesso
    return new Response(JSON.stringify({ success: true, result }), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  } catch (error) {
    console.error('Erro ao processar webhook:', error);

    // Determinar código de status com base no erro
    let statusCode = 500;
    let errorMessage = 'Erro interno';

    if (error instanceof Error) {
      if (error.message.includes('Assinatura')) {
        statusCode = 401;
        errorMessage = error.message;
      } else if (error.message.includes('formato') || error.message.includes('inválido')) {
        statusCode = 400;
        errorMessage = error.message;
      } else {
        errorMessage = error.message;
      }
    }

    // Retornar resposta de erro
    return new Response(JSON.stringify({ error: errorMessage }), {
      status: statusCode,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  }
};
