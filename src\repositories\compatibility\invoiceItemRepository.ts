/**
 * Camada de compatibilidade para o repositório de itens de fatura
 * 
 * Este arquivo mantém a compatibilidade com o código existente que
 * utiliza a estrutura antiga do repositório de itens de fatura.
 * 
 * @deprecated Use a nova estrutura de repositórios em src/repositories
 */

import type { QueryResult } from "pg";
import { pgHelper } from "../../repository/pgHelper";
import { repositories } from "../index";

/**
 * Cria um novo item de fatura
 * 
 * @param ulid_invoice ID da fatura
 * @param ulid_product ID do produto
 * @param qty Quantidade
 * @param price Preço
 * @returns Resultado da consulta
 */
async function create(
  ulid_invoice: string,
  ulid_product: string,
  qty: number,
  price: number
): Promise<QueryResult> {
  try {
    // Usar a nova implementação
    const invoiceItem = await repositories.invoiceItemRepository.create({
      invoiceId: ulid_invoice,
      productId: ulid_product,
      quantity: qty,
      price
    });
    
    // Converter para o formato esperado pelo código antigo
    return {
      rows: [invoiceItem],
      rowCount: 1,
      command: 'INSERT',
      oid: 0,
      fields: []
    };
  } catch (error) {
    console.error('Error in compatibility layer (invoiceItemRepository.create):', error);
    
    // Fallback para a implementação antiga em caso de erro
    // Corrigindo o SQL que tinha um erro na query original (aspas simples nos placeholders)
    return pgHelper.query(
      `INSERT INTO tab_invoice_item (
         ulid_invoice, 
         ulid_product, 
         qty, 
         price) 
       VALUES ($1, $2, $3, $4)
       RETURNING *`,
      [ulid_invoice, ulid_product, qty, price]
    );
  }
}

/**
 * Busca itens de fatura
 * 
 * @param ulid_invoice_item ID do item de fatura (opcional)
 * @param ulid_invoice ID da fatura (opcional)
 * @param ulid_product ID do produto (opcional)
 * @returns Resultado da consulta
 */
async function read(
  ulid_invoice_item?: string,
  ulid_invoice?: string,
  ulid_product?: string
): Promise<QueryResult> {
  try {
    // Usar a nova implementação
    const invoiceItems = await repositories.invoiceItemRepository.findAll({
      id: ulid_invoice_item,
      invoiceId: ulid_invoice,
      productId: ulid_product
    });
    
    // Converter para o formato esperado pelo código antigo
    return {
      rows: invoiceItems,
      rowCount: invoiceItems.length,
      command: 'SELECT',
      oid: 0,
      fields: []
    };
  } catch (error) {
    console.error('Error in compatibility layer (invoiceItemRepository.read):', error);
    
    // Fallback para a implementação antiga em caso de erro
    return pgHelper.query(
      `SELECT * 
         FROM tab_invoice_item 
        WHERE TRUE 
          ${ulid_invoice_item ? 'AND ulid_invoice_item = $1' : ''}
          ${ulid_invoice ? 'AND ulid_invoice = $2' : ''}
          ${ulid_product ? 'AND ulid_product = $3' : ''}`,
      [ulid_invoice_item, ulid_invoice, ulid_product]
    );
  }
}

/**
 * Atualiza um item de fatura
 * 
 * @param ulid_invoice_item ID do item de fatura
 * @param qty Quantidade
 * @returns Resultado da consulta
 */
async function update(
  ulid_invoice_item: string,
  qty: number
): Promise<QueryResult> {
  try {
    // Usar a nova implementação
    const invoiceItem = await repositories.invoiceItemRepository.update(ulid_invoice_item, {
      quantity: qty
    });
    
    // Converter para o formato esperado pelo código antigo
    return {
      rows: [invoiceItem],
      rowCount: 1,
      command: 'UPDATE',
      oid: 0,
      fields: []
    };
  } catch (error) {
    console.error('Error in compatibility layer (invoiceItemRepository.update):', error);
    
    // Fallback para a implementação antiga em caso de erro
    return pgHelper.query(
      `UPDATE tab_invoice_item 
          SET qty               = $2,
              updated_at        = NOW() 
        WHERE ulid_invoice_item = $1 
       RETURNING *`,
      [ulid_invoice_item, qty]
    );
  }
}

/**
 * Remove um item de fatura
 * 
 * @param ulid_invoice_item ID do item de fatura
 * @returns Resultado da consulta
 */
async function deleteByUlid(
  ulid_invoice_item: string
): Promise<QueryResult> {
  try {
    // Usar a nova implementação
    const success = await repositories.invoiceItemRepository.delete(ulid_invoice_item);
    
    // Converter para o formato esperado pelo código antigo
    return {
      rows: success ? [{ ulid_invoice_item }] : [],
      rowCount: success ? 1 : 0,
      command: 'DELETE',
      oid: 0,
      fields: []
    };
  } catch (error) {
    console.error('Error in compatibility layer (invoiceItemRepository.deleteByUlid):', error);
    
    // Fallback para a implementação antiga em caso de erro
    return pgHelper.query(
      `DELETE FROM tab_invoice_item 
        WHERE ulid_invoice_item = $1 
       RETURNING *`,
      [ulid_invoice_item]
    );
  }
}

export const invoiceItemRepository = {
  create,
  read,
  update,
  deleteByUlid
};
