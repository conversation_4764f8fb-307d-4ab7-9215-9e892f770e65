/**
 * Tipos comuns para substituir uso de 'any' no projeto
 * Criado para eliminar warnings noExplicitAny
 */

// Tipos genéricos para dados não estruturados
export type UnknownRecord = Record<string, unknown>;
export type StringRecord = Record<string, string>;
export type NumberRecord = Record<string, number>;
export type AnyRecord = Record<string, unknown>; // Para casos específicos - migrado de any para unknown

// Tipos para APIs e respostas HTTP
export interface ApiResponse<T = unknown> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  statusCode?: number;
}

export interface ApiError {
  message: string;
  code?: string;
  statusCode?: number;
  details?: UnknownRecord;
}

// Tipos para formulários e validação
export interface FormData {
  [key: string]: string | number | boolean | File | null | undefined;
}

export interface ValidationError {
  field: string;
  message: string;
  code?: string;
}

export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
}

// Tipos para configuração
export interface ConfigValue {
  [key: string]: string | number | boolean | ConfigValue | ConfigValue[];
}

// Tipos para eventos e handlers
export type EventHandler<T = Event> = (event: T) => void | Promise<void>;
export type AsyncEventHandler<T = Event> = (event: T) => Promise<void>;

// Tipos para dados de teste
export interface TestData {
  [key: string]: unknown;
}

export interface MockFunction<T extends (...args: any[]) => any = (...args: any[]) => any> {
  (...args: Parameters<T>): ReturnType<T>;
  mockReturnValue(value: ReturnType<T>): void;
  mockResolvedValue(value: Awaited<ReturnType<T>>): void;
  mockRejectedValue(error: any): void;
  mockImplementation(fn: T): void;
  mockClear(): void;
  mockReset(): void;
}

// Tipos para dados de banco
export interface DatabaseRow {
  [column: string]: string | number | boolean | Date | null;
}

export interface QueryResult<T = DatabaseRow> {
  rows: T[];
  rowCount: number;
  command?: string;
}

// Tipos para logs e debugging
export interface LogData {
  level: 'debug' | 'info' | 'warn' | 'error';
  message: string;
  timestamp: Date;
  context?: UnknownRecord;
  error?: Error;
}

// Tipos para métricas e analytics
export interface MetricData {
  name: string;
  value: number;
  unit?: string;
  tags?: StringRecord;
  timestamp?: Date;
}

export interface AnalyticsEvent {
  name: string;
  properties?: UnknownRecord;
  userId?: string;
  sessionId?: string;
  timestamp?: Date;
}

// Tipos para cache
export interface CacheEntry<T = unknown> {
  key: string;
  value: T;
  expiresAt?: Date;
  createdAt: Date;
}

// Tipos para paginação
export interface PaginationParams {
  page: number;
  limit: number;
  offset?: number;
}

export interface PaginatedResult<T = unknown> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// Tipos para filtros e busca
export interface FilterParams {
  [key: string]: string | number | boolean | string[] | number[] | null | undefined;
}

export interface SearchParams extends FilterParams {
  query?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// Tipos para upload de arquivos
export interface FileUpload {
  file: File;
  filename: string;
  mimetype: string;
  size: number;
  buffer?: Buffer;
}

export interface UploadResult {
  success: boolean;
  filename?: string;
  url?: string;
  error?: string;
}

// Tipos para notificações
export interface NotificationData {
  title: string;
  message: string;
  type: 'info' | 'success' | 'warning' | 'error';
  userId?: string;
  metadata?: UnknownRecord;
}

// Tipos para webhooks
export interface WebhookPayload {
  event: string;
  data: UnknownRecord;
  timestamp: Date;
  signature?: string;
}

// Tipos para jobs e tarefas
export interface JobData {
  id: string;
  type: string;
  payload: UnknownRecord;
  priority?: number;
  delay?: number;
  attempts?: number;
  maxAttempts?: number;
}

export interface JobResult {
  success: boolean;
  result?: unknown;
  error?: string;
  duration?: number;
}
