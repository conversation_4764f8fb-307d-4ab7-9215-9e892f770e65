import { isAuthenticated } from '@middleware/authMiddleware';
import { webhookRepository } from '@repositories/webhookRepository';
import { outgoingWebhookService } from '@services/outgoingWebhookService';
// src/pages/api/webhooks/subscriptions/[id]/deliveries.ts
import type { APIRoute } from 'astro';

/**
 * Endpoint para listar entregas de webhook para uma assinatura
 */
export const GET: APIRoute = async ({ request, cookies, params, url }) => {
  try {
    // Verificar autenticação
    const authResult = await isAuthenticated({ request, cookies } as any);

    if (!authResult.isAuthenticated) {
      return new Response(JSON.stringify({ error: 'Não autorizado' }), {
        status: 401,
        headers: {
          'Content-Type': 'application/json',
        },
      });
    }

    // Obter ID da assinatura
    const { id } = params;

    if (!id) {
      return new Response(JSON.stringify({ error: 'ID da assinatura não fornecido' }), {
        status: 400,
        headers: {
          'Content-Type': 'application/json',
        },
      });
    }

    // Verificar se assinatura existe
    const exists = await outgoingWebhookService.subscriptionExists(id);

    if (!exists) {
      return new Response(JSON.stringify({ error: 'Assinatura de webhook não encontrada' }), {
        status: 404,
        headers: {
          'Content-Type': 'application/json',
        },
      });
    }

    // Extrair parâmetros de paginação
    const limit = Number.parseInt(url.searchParams.get('limit') || '50', 10);
    const offset = Number.parseInt(url.searchParams.get('offset') || '0', 10);

    // Validar parâmetros
    if (Number.isNaN(limit) || limit < 1 || limit > 100) {
      return new Response(JSON.stringify({ error: 'Parâmetro limit inválido' }), {
        status: 400,
        headers: {
          'Content-Type': 'application/json',
        },
      });
    }

    if (Number.isNaN(offset) || offset < 0) {
      return new Response(JSON.stringify({ error: 'Parâmetro offset inválido' }), {
        status: 400,
        headers: {
          'Content-Type': 'application/json',
        },
      });
    }

    // Obter entregas
    const deliveries = await webhookRepository.listDeliveriesForSubscription(id, limit, offset);

    // Retornar resposta
    return new Response(
      JSON.stringify({
        deliveries,
        pagination: {
          limit,
          offset,
          total: deliveries.length, // Isso é apenas uma aproximação, idealmente teríamos um count total
        },
      }),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  } catch (error) {
    console.error('Erro ao listar entregas de webhook:', error);

    return new Response(
      JSON.stringify({
        error: error instanceof Error ? error.message : 'Erro interno',
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  }
};
