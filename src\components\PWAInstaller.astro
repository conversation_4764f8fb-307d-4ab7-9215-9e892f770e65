---
/**
 * Componente PWAInstaller
 * 
 * Este componente registra o service worker e fornece uma interface para
 * instalar a aplicação como PWA.
 * 
 * Características:
 * - Registro do service worker
 * - Detecção de atualizações
 * - Prompt de instalação
 * - Feedback visual para o usuário
 */

interface Props {
  /**
   * Caminho para o service worker
   * @default "/service-worker.js"
   */
  swPath?: string;
  
  /**
   * Se deve mostrar o botão de instalação
   * @default true
   */
  showInstallButton?: boolean;
  
  /**
   * Se deve mostrar notificação de atualização
   * @default true
   */
  showUpdateNotification?: boolean;
  
  /**
   * Texto do botão de instalação
   * @default "Instalar aplicativo"
   */
  installButtonText?: string;
  
  /**
   * Texto da notificação de atualização
   * @default "Nova versão disponível"
   */
  updateNotificationText?: string;
  
  /**
   * Texto do botão de atualização
   * @default "Atualizar agora"
   */
  updateButtonText?: string;
}

// Props com valores padrão
const {
  swPath = "/service-worker.js",
  showInstallButton = true,
  showUpdateNotification = true,
  installButtonText = "Instalar aplicativo",
  updateNotificationText = "Nova versão disponível",
  updateButtonText = "Atualizar agora"
} = Astro.props;
---

{/* Botão de instalação (inicialmente oculto) */}
{showInstallButton && (
  <button 
    id="pwa-install-button" 
    class="pwa-install-button" 
    aria-label={installButtonText}
    style="display: none;"
  >
    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
      <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path>
      <polyline points="7.5 4.21 12 6.81 16.5 4.21"></polyline>
      <polyline points="7.5 19.79 7.5 14.6 3 12"></polyline>
      <polyline points="21 12 16.5 14.6 16.5 19.79"></polyline>
      <polyline points="3.27 6.96 12 12.01 20.73 6.96"></polyline>
      <line x1="12" y1="22.08" x2="12" y2="12"></line>
    </svg>
    <span>{installButtonText}</span>
  </button>
)}

{/* Notificação de atualização (inicialmente oculta) */}
{showUpdateNotification && (
  <div id="pwa-update-toast" class="pwa-update-toast" style="display: none;">
    <div class="pwa-update-toast-content">
      <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <path d="M23 12a11 11 0 1 1-22 0 11 11 0 0 1 22 0z"></path>
        <path d="M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0z"></path>
        <path d="M15.8 8.2l-3.8 3.8-3.8 3.8"></path>
        <path d="M8.2 8.2l3.8 3.8 3.8 3.8"></path>
      </svg>
      <span>{updateNotificationText}</span>
      <button id="pwa-update-button">{updateButtonText}</button>
      <button id="pwa-update-close" aria-label="Fechar">
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <line x1="18" y1="6" x2="6" y2="18"></line>
          <line x1="6" y1="6" x2="18" y2="18"></line>
        </svg>
      </button>
    </div>
  </div>
)}

<style>
  .pwa-install-button {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background-color: #0078d7;
    color: white;
    border: none;
    border-radius: 50px;
    padding: 10px 20px;
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    transition: transform 0.2s ease, background-color 0.2s ease;
  }
  
  .pwa-install-button:hover {
    background-color: #005a9e;
    transform: translateY(-2px);
  }
  
  .pwa-install-button:active {
    transform: translateY(0);
  }
  
  .pwa-update-toast {
    position: fixed;
    top: 20px;
    right: 20px;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    overflow: hidden;
    max-width: 400px;
    width: calc(100% - 40px);
  }
  
  .pwa-update-toast-content {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    gap: 12px;
  }
  
  .pwa-update-toast-content svg {
    flex-shrink: 0;
    color: #0078d7;
  }
  
  .pwa-update-toast-content span {
    flex-grow: 1;
    font-size: 14px;
  }
  
  #pwa-update-button {
    background-color: #0078d7;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 6px 12px;
    font-size: 14px;
    cursor: pointer;
    transition: background-color 0.2s ease;
  }
  
  #pwa-update-button:hover {
    background-color: #005a9e;
  }
  
  #pwa-update-close {
    background: none;
    border: none;
    padding: 4px;
    cursor: pointer;
    color: #666;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.2s ease;
  }
  
  #pwa-update-close:hover {
    background-color: #f0f0f0;
  }
  
  @media (max-width: 600px) {
    .pwa-install-button {
      bottom: 16px;
      right: 16px;
      padding: 8px 16px;
      font-size: 14px;
    }
    
    .pwa-update-toast {
      top: 16px;
      right: 16px;
      width: calc(100% - 32px);
    }
  }
</style>

<script define:vars={{ swPath }}>
  // Variáveis para armazenar referências
  let deferredPrompt;
  let registration;
  let refreshing = false;
  
  // Registrar o service worker
  async function registerServiceWorker() {
    if ('serviceWorker' in navigator) {
      try {
        // Registrar o service worker
        registration = await navigator.serviceWorker.register(swPath);
        console.log('Service Worker registrado com sucesso:', registration.scope);
        
        // Verificar atualizações
        checkForUpdates();
        
        // Adicionar listener para atualizações
        navigator.serviceWorker.addEventListener('controllerchange', () => {
          if (!refreshing) {
            refreshing = true;
            window.location.reload();
          }
        });
      } catch (error) {
        console.error('Erro ao registrar Service Worker:', error);
      }
    }
  }
  
  // Verificar atualizações do service worker
  function checkForUpdates() {
    if (!registration) return;
    
    // Verificar atualizações a cada hora
    setInterval(async () => {
      try {
        await registration.update();
        
        if (registration.waiting) {
          showUpdateNotification();
        }
      } catch (error) {
        console.error('Erro ao verificar atualizações:', error);
      }
    }, 60 * 60 * 1000); // 1 hora
    
    // Verificar imediatamente se há uma atualização pendente
    if (registration.waiting) {
      showUpdateNotification();
    }
    
    // Adicionar listener para novas atualizações
    registration.addEventListener('updatefound', () => {
      const newWorker = registration.installing;
      
      newWorker.addEventListener('statechange', () => {
        if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
          showUpdateNotification();
        }
      });
    });
  }
  
  // Mostrar notificação de atualização
  function showUpdateNotification() {
    const updateToast = document.getElementById('pwa-update-toast');
    if (updateToast) {
      updateToast.style.display = 'block';
    }
  }
  
  // Aplicar atualização
  function applyUpdate() {
    if (registration && registration.waiting) {
      // Enviar mensagem para o service worker ativar a nova versão
      registration.waiting.postMessage({ type: 'SKIP_WAITING' });
    }
  }
  
  // Configurar evento de instalação
  function setupInstallPrompt() {
    window.addEventListener('beforeinstallprompt', (e) => {
      // Prevenir o prompt automático
      e.preventDefault();
      
      // Armazenar o evento para uso posterior
      deferredPrompt = e;
      
      // Mostrar o botão de instalação
      const installButton = document.getElementById('pwa-install-button');
      if (installButton) {
        installButton.style.display = 'flex';
      }
    });
    
    // Esconder o botão se o app já estiver instalado
    window.addEventListener('appinstalled', () => {
      const installButton = document.getElementById('pwa-install-button');
      if (installButton) {
        installButton.style.display = 'none';
      }
      
      deferredPrompt = null;
      console.log('PWA instalado com sucesso');
    });
  }
  
  // Inicializar quando o DOM estiver pronto
  document.addEventListener('DOMContentLoaded', () => {
    // Registrar service worker
    registerServiceWorker();
    
    // Configurar prompt de instalação
    setupInstallPrompt();
    
    // Adicionar listener para o botão de instalação
    const installButton = document.getElementById('pwa-install-button');
    if (installButton) {
      installButton.addEventListener('click', async () => {
        if (!deferredPrompt) return;
        
        // Mostrar o prompt de instalação
        deferredPrompt.prompt();
        
        // Aguardar a escolha do usuário
        const { outcome } = await deferredPrompt.userChoice;
        console.log(`Resultado do prompt de instalação: ${outcome}`);
        
        // Limpar a referência
        deferredPrompt = null;
        
        // Esconder o botão
        installButton.style.display = 'none';
      });
    }
    
    // Adicionar listener para o botão de atualização
    const updateButton = document.getElementById('pwa-update-button');
    if (updateButton) {
      updateButton.addEventListener('click', () => {
        applyUpdate();
      });
    }
    
    // Adicionar listener para o botão de fechar notificação
    const closeButton = document.getElementById('pwa-update-close');
    if (closeButton) {
      closeButton.addEventListener('click', () => {
        const updateToast = document.getElementById('pwa-update-toast');
        if (updateToast) {
          updateToast.style.display = 'none';
        }
      });
    }
  });
</script>
