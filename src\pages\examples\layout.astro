---
import DaisyButton from '../../components/ui/DaisyButton.astro';
import DaisyCard from '../../components/ui/DaisyCard.astro';
import Breadcrumbs from '../../components/ui/navigation/Breadcrumbs.astro';
import Tabs from '../../components/ui/navigation/Tabs.astro';
import BaseLayout from '../../layouts/BaseLayout.astro';

// Importar componentes de layout
import { Container, Flex, GridItem, ResponsiveGrid, Section, Spacer } from '../../layouts/grid';

const title = 'Sistema de Layout';

// Breadcrumbs para a página atual
const breadcrumbItems = [
  { href: '/', label: 'Início' },
  { href: '/examples', label: 'Exemplos' },
  { label: 'Layout' },
];

// Tabs para as categorias de layout
const layoutTabs = [
  { id: 'container', label: 'Container', isActive: true },
  { id: 'grid', label: 'Grid' },
  { id: 'flex', label: 'Flex' },
  { id: 'section', label: 'Section' },
  { id: 'responsive', label: 'Responsividade' },
];

// Exemplos de código
const containerCode = `import { Container } from '../../layouts/grid';

<Container size="lg" px={4}>
  Conteúdo centralizado com largura máxima
</Container>`;

const gridCode = `import { ResponsiveGrid, GridItem } from '../../layouts/grid';

<ResponsiveGrid 
  cols={{ 
    base: 1, 
    sm: 2, 
    md: 3, 
    lg: 4 
  }} 
  gap={4}
>
  <GridItem>Item 1</GridItem>
  <GridItem>Item 2</GridItem>
  <GridItem>Item 3</GridItem>
  <GridItem>Item 4</GridItem>
</ResponsiveGrid>`;

const gridItemCode = `import { ResponsiveGrid, GridItem } from '../../layouts/grid';

<ResponsiveGrid cols={12} gap={4}>
  <GridItem colSpan={6}>Ocupa 6 colunas</GridItem>
  <GridItem colSpan={3}>Ocupa 3 colunas</GridItem>
  <GridItem colSpan={3}>Ocupa 3 colunas</GridItem>
  <GridItem colSpan={12}>Ocupa todas as colunas</GridItem>
</ResponsiveGrid>`;

const flexCode = `import { Flex } from '../../layouts/grid';

<Flex 
  direction="row" 
  wrap="wrap" 
  justify="between" 
  items="center" 
  gap={4}
>
  <div>Item 1</div>
  <div>Item 2</div>
  <div>Item 3</div>
</Flex>`;

const sectionCode = `import { Section } from '../../layouts/grid';

<Section 
  title="Título da Seção" 
  subtitle="Subtítulo da seção" 
  background="base-200" 
  py={12}
>
  Conteúdo da seção
</Section>`;

const responsiveCode = `import { ResponsiveGrid, GridItem } from '../../layouts/grid';

<ResponsiveGrid 
  cols={{ 
    base: 1,    // 1 coluna em dispositivos pequenos
    md: 2,      // 2 colunas em tablets
    lg: 3,      // 3 colunas em desktops
    xl: 4       // 4 colunas em telas grandes
  }} 
  gap={{ 
    base: 2,    // Gap pequeno em dispositivos pequenos
    md: 4,      // Gap médio em tablets
    lg: 6       // Gap grande em desktops
  }}
>
  <GridItem>Item 1</GridItem>
  <GridItem>Item 2</GridItem>
  <GridItem>Item 3</GridItem>
  <GridItem>Item 4</GridItem>
</ResponsiveGrid>`;
---

<BaseLayout title={title}>
  <Container>
    <Breadcrumbs items={breadcrumbItems} class="my-4" />
    
    <Section title={title} subtitle="Sistema de layout responsivo para o projeto Estação da Alfabetização">
      <div class="mb-8">
        <p>
          O sistema de layout foi desenvolvido para facilitar a criação de interfaces responsivas e consistentes.
          Ele é baseado em CSS Grid e Flexbox, com suporte a diferentes breakpoints e configurações.
        </p>
        
        <p class="mt-4">
          Os componentes de layout são altamente customizáveis e podem ser combinados para criar layouts complexos.
        </p>
      </div>
      
      <Tabs tabs={layoutTabs}>
        <!-- Container -->
        <div slot="container" class="py-4">
          <h2 class="text-2xl font-bold mb-4">Container</h2>
          
          <p class="mb-4">
            O componente Container limita a largura do conteúdo e o centraliza na página.
            Ele é útil para criar layouts consistentes em diferentes tamanhos de tela.
          </p>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            <div>
              <h3 class="text-xl font-bold mb-2">Exemplo</h3>
              <div class="border rounded-box p-4 bg-base-200">
                <Container size="sm" class="border border-primary p-4 text-center">
                  Container Small
                </Container>
                
                <Spacer size={4} />
                
                <Container size="md" class="border border-secondary p-4 text-center">
                  Container Medium
                </Container>
                
                <Spacer size={4} />
                
                <Container size="lg" class="border border-accent p-4 text-center">
                  Container Large
                </Container>
              </div>
            </div>
            
            <div>
              <h3 class="text-xl font-bold mb-2">Código</h3>
              <pre class="bg-base-300 p-4 rounded-box overflow-x-auto text-sm"><code>{containerCode}</code></pre>
            </div>
          </div>
          
          <h3 class="text-xl font-bold mb-2">Props</h3>
          <div class="overflow-x-auto mb-6">
            <table class="table table-zebra w-full">
              <thead>
                <tr>
                  <th>Prop</th>
                  <th>Tipo</th>
                  <th>Padrão</th>
                  <th>Descrição</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td><code>size</code></td>
                  <td>String</td>
                  <td><code>'lg'</code></td>
                  <td>Tamanho do container: <code>'sm'</code>, <code>'md'</code>, <code>'lg'</code>, <code>'xl'</code>, <code>'2xl'</code>, <code>'full'</code>, <code>'none'</code></td>
                </tr>
                <tr>
                  <td><code>centered</code></td>
                  <td>Boolean</td>
                  <td><code>true</code></td>
                  <td>Centraliza o container horizontalmente</td>
                </tr>
                <tr>
                  <td><code>fluid</code></td>
                  <td>Boolean</td>
                  <td><code>false</code></td>
                  <td>Define se o container ocupa toda a largura disponível</td>
                </tr>
                <tr>
                  <td><code>px</code></td>
                  <td>Number</td>
                  <td><code>undefined</code></td>
                  <td>Padding horizontal</td>
                </tr>
                <tr>
                  <td><code>py</code></td>
                  <td>Number</td>
                  <td><code>undefined</code></td>
                  <td>Padding vertical</td>
                </tr>
                <tr>
                  <td><code>p</code></td>
                  <td>Number</td>
                  <td><code>undefined</code></td>
                  <td>Padding em todos os lados</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
        
        <!-- Grid -->
        <div slot="grid" class="py-4">
          <h2 class="text-2xl font-bold mb-4">Grid</h2>
          
          <p class="mb-4">
            O componente ResponsiveGrid cria um layout de grid responsivo baseado em CSS Grid.
            Ele permite definir o número de colunas, gaps e alinhamento para diferentes tamanhos de tela.
          </p>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            <div>
              <h3 class="text-xl font-bold mb-2">Exemplo</h3>
              <div class="border rounded-box p-4 bg-base-200">
                <ResponsiveGrid cols={{ base: 1, sm: 2, md: 3, lg: 4 }} gap={4}>
                  {Array.from({ length: 8 }).map((_, i) => (
                    <div class="bg-primary text-primary-content p-4 rounded text-center">
                      Item {i + 1}
                    </div>
                  ))}
                </ResponsiveGrid>
              </div>
            </div>
            
            <div>
              <h3 class="text-xl font-bold mb-2">Código</h3>
              <pre class="bg-base-300 p-4 rounded-box overflow-x-auto text-sm"><code>{gridCode}</code></pre>
            </div>
          </div>
          
          <h3 class="text-xl font-bold mb-4">GridItem</h3>
          
          <p class="mb-4">
            O componente GridItem permite controlar o span e posicionamento de itens individuais dentro do grid.
          </p>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            <div>
              <h3 class="text-xl font-bold mb-2">Exemplo</h3>
              <div class="border rounded-box p-4 bg-base-200">
                <ResponsiveGrid cols={12} gap={4}>
                  <GridItem colSpan={6}>
                    <div class="bg-primary text-primary-content p-4 rounded text-center">
                      Ocupa 6 colunas
                    </div>
                  </GridItem>
                  <GridItem colSpan={3}>
                    <div class="bg-secondary text-secondary-content p-4 rounded text-center">
                      Ocupa 3 colunas
                    </div>
                  </GridItem>
                  <GridItem colSpan={3}>
                    <div class="bg-accent text-accent-content p-4 rounded text-center">
                      Ocupa 3 colunas
                    </div>
                  </GridItem>
                  <GridItem colSpan={12}>
                    <div class="bg-neutral text-neutral-content p-4 rounded text-center">
                      Ocupa todas as colunas
                    </div>
                  </GridItem>
                </ResponsiveGrid>
              </div>
            </div>
            
            <div>
              <h3 class="text-xl font-bold mb-2">Código</h3>
              <pre class="bg-base-300 p-4 rounded-box overflow-x-auto text-sm"><code>{gridItemCode}</code></pre>
            </div>
          </div>
          
          <h3 class="text-xl font-bold mb-2">Props do ResponsiveGrid</h3>
          <div class="overflow-x-auto mb-6">
            <table class="table table-zebra w-full">
              <thead>
                <tr>
                  <th>Prop</th>
                  <th>Tipo</th>
                  <th>Padrão</th>
                  <th>Descrição</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td><code>cols</code></td>
                  <td>Number | Object</td>
                  <td><code>1</code></td>
                  <td>Número de colunas ou objeto com configurações responsivas</td>
                </tr>
                <tr>
                  <td><code>gap</code></td>
                  <td>Number | Object</td>
                  <td><code>4</code></td>
                  <td>Espaçamento entre itens ou objeto com configurações responsivas</td>
                </tr>
                <tr>
                  <td><code>justify</code></td>
                  <td>String</td>
                  <td><code>undefined</code></td>
                  <td>Alinhamento horizontal: <code>'start'</code>, <code>'center'</code>, <code>'end'</code>, <code>'between'</code>, <code>'around'</code>, <code>'evenly'</code></td>
                </tr>
                <tr>
                  <td><code>items</code></td>
                  <td>String</td>
                  <td><code>undefined</code></td>
                  <td>Alinhamento vertical: <code>'start'</code>, <code>'center'</code>, <code>'end'</code>, <code>'stretch'</code>, <code>'baseline'</code></td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
        
        <!-- Flex -->
        <div slot="flex" class="py-4">
          <h2 class="text-2xl font-bold mb-4">Flex</h2>
          
          <p class="mb-4">
            O componente Flex cria um layout flexbox responsivo.
            Ele permite definir a direção, wrap, alinhamento e espaçamento entre itens.
          </p>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            <div>
              <h3 class="text-xl font-bold mb-2">Exemplo</h3>
              <div class="border rounded-box p-4 bg-base-200">
                <Flex direction="row" wrap="wrap" justify="between" items="center" gap={4}>
                  <div class="bg-primary text-primary-content p-4 rounded">Item 1</div>
                  <div class="bg-secondary text-secondary-content p-4 rounded">Item 2</div>
                  <div class="bg-accent text-accent-content p-4 rounded">Item 3</div>
                </Flex>
                
                <Spacer size={8} />
                
                <Flex direction="col" gap={2}>
                  <div class="bg-primary text-primary-content p-4 rounded">Item 1</div>
                  <div class="bg-secondary text-secondary-content p-4 rounded">Item 2</div>
                  <div class="bg-accent text-accent-content p-4 rounded">Item 3</div>
                </Flex>
              </div>
            </div>
            
            <div>
              <h3 class="text-xl font-bold mb-2">Código</h3>
              <pre class="bg-base-300 p-4 rounded-box overflow-x-auto text-sm"><code>{flexCode}</code></pre>
            </div>
          </div>
          
          <h3 class="text-xl font-bold mb-2">Props</h3>
          <div class="overflow-x-auto mb-6">
            <table class="table table-zebra w-full">
              <thead>
                <tr>
                  <th>Prop</th>
                  <th>Tipo</th>
                  <th>Padrão</th>
                  <th>Descrição</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td><code>direction</code></td>
                  <td>String</td>
                  <td><code>'row'</code></td>
                  <td>Direção do flex: <code>'row'</code>, <code>'row-reverse'</code>, <code>'col'</code>, <code>'col-reverse'</code></td>
                </tr>
                <tr>
                  <td><code>wrap</code></td>
                  <td>String</td>
                  <td><code>'wrap'</code></td>
                  <td>Wrap do flex: <code>'wrap'</code>, <code>'wrap-reverse'</code>, <code>'nowrap'</code></td>
                </tr>
                <tr>
                  <td><code>justify</code></td>
                  <td>String</td>
                  <td><code>'start'</code></td>
                  <td>Alinhamento horizontal: <code>'start'</code>, <code>'center'</code>, <code>'end'</code>, <code>'between'</code>, <code>'around'</code>, <code>'evenly'</code></td>
                </tr>
                <tr>
                  <td><code>items</code></td>
                  <td>String</td>
                  <td><code>'start'</code></td>
                  <td>Alinhamento vertical: <code>'start'</code>, <code>'center'</code>, <code>'end'</code>, <code>'stretch'</code>, <code>'baseline'</code></td>
                </tr>
                <tr>
                  <td><code>gap</code></td>
                  <td>Number</td>
                  <td><code>undefined</code></td>
                  <td>Espaçamento entre itens</td>
                </tr>
                <tr>
                  <td><code>inline</code></td>
                  <td>Boolean</td>
                  <td><code>false</code></td>
                  <td>Define se o flex é inline</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
        
        <!-- Section -->
        <div slot="section" class="py-4">
          <h2 class="text-2xl font-bold mb-4">Section</h2>
          
          <p class="mb-4">
            O componente Section cria uma seção de conteúdo com título, subtítulo e container opcional.
            Ele é útil para organizar o conteúdo em seções distintas.
          </p>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            <div>
              <h3 class="text-xl font-bold mb-2">Exemplo</h3>
              <div class="border rounded-box p-4 bg-base-200">
                <Section 
                  title="Título da Seção" 
                  subtitle="Subtítulo da seção" 
                  background="base-200" 
                  py={8}
                  container
                  containerSize="sm"
                >
                  <p class="text-center">Conteúdo da seção</p>
                </Section>
              </div>
            </div>
            
            <div>
              <h3 class="text-xl font-bold mb-2">Código</h3>
              <pre class="bg-base-300 p-4 rounded-box overflow-x-auto text-sm"><code>{sectionCode}</code></pre>
            </div>
          </div>
          
          <h3 class="text-xl font-bold mb-2">Props</h3>
          <div class="overflow-x-auto mb-6">
            <table class="table table-zebra w-full">
              <thead>
                <tr>
                  <th>Prop</th>
                  <th>Tipo</th>
                  <th>Padrão</th>
                  <th>Descrição</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td><code>title</code></td>
                  <td>String</td>
                  <td><code>undefined</code></td>
                  <td>Título da seção</td>
                </tr>
                <tr>
                  <td><code>subtitle</code></td>
                  <td>String</td>
                  <td><code>undefined</code></td>
                  <td>Subtítulo da seção</td>
                </tr>
                <tr>
                  <td><code>container</code></td>
                  <td>Boolean</td>
                  <td><code>true</code></td>
                  <td>Define se o conteúdo deve estar em um container</td>
                </tr>
                <tr>
                  <td><code>containerSize</code></td>
                  <td>String</td>
                  <td><code>'lg'</code></td>
                  <td>Tamanho do container: <code>'sm'</code>, <code>'md'</code>, <code>'lg'</code>, <code>'xl'</code>, <code>'2xl'</code>, <code>'full'</code>, <code>'none'</code></td>
                </tr>
                <tr>
                  <td><code>background</code></td>
                  <td>String</td>
                  <td><code>'none'</code></td>
                  <td>Cor de fundo da seção</td>
                </tr>
                <tr>
                  <td><code>py</code></td>
                  <td>Number</td>
                  <td><code>12</code></td>
                  <td>Padding vertical</td>
                </tr>
                <tr>
                  <td><code>centered</code></td>
                  <td>Boolean</td>
                  <td><code>false</code></td>
                  <td>Centraliza o conteúdo horizontalmente</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
        
        <!-- Responsividade -->
        <div slot="responsive" class="py-4">
          <h2 class="text-2xl font-bold mb-4">Responsividade</h2>
          
          <p class="mb-4">
            O sistema de layout é totalmente responsivo, permitindo definir configurações diferentes para cada breakpoint.
            Os breakpoints são baseados no Tailwind CSS: <code>sm</code> (640px), <code>md</code> (768px), <code>lg</code> (1024px), <code>xl</code> (1280px), <code>2xl</code> (1536px).
          </p>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            <div>
              <h3 class="text-xl font-bold mb-2">Exemplo</h3>
              <div class="border rounded-box p-4 bg-base-200">
                <ResponsiveGrid 
                  cols={{ 
                    base: 1,
                    md: 2,
                    lg: 3,
                    xl: 4
                  }} 
                  gap={{ 
                    base: 2,
                    md: 4,
                    lg: 6
                  }}
                >
                  {Array.from({ length: 8 }).map((_, i) => (
                    <div class="bg-primary text-primary-content p-4 rounded text-center">
                      Item {i + 1}
                    </div>
                  ))}
                </ResponsiveGrid>
              </div>
            </div>
            
            <div>
              <h3 class="text-xl font-bold mb-2">Código</h3>
              <pre class="bg-base-300 p-4 rounded-box overflow-x-auto text-sm"><code>{responsiveCode}</code></pre>
            </div>
          </div>
          
          <h3 class="text-xl font-bold mb-4">Breakpoints</h3>
          <div class="overflow-x-auto mb-6">
            <table class="table table-zebra w-full">
              <thead>
                <tr>
                  <th>Nome</th>
                  <th>Valor</th>
                  <th>Descrição</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td><code>base</code></td>
                  <td>0px+</td>
                  <td>Dispositivos móveis pequenos</td>
                </tr>
                <tr>
                  <td><code>sm</code></td>
                  <td>640px+</td>
                  <td>Dispositivos móveis grandes</td>
                </tr>
                <tr>
                  <td><code>md</code></td>
                  <td>768px+</td>
                  <td>Tablets</td>
                </tr>
                <tr>
                  <td><code>lg</code></td>
                  <td>1024px+</td>
                  <td>Desktops</td>
                </tr>
                <tr>
                  <td><code>xl</code></td>
                  <td>1280px+</td>
                  <td>Desktops grandes</td>
                </tr>
                <tr>
                  <td><code>2xl</code></td>
                  <td>1536px+</td>
                  <td>Telas muito grandes</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </Tabs>
      
      <div class="mt-12 p-6 bg-base-200 rounded-box">
        <h2 class="text-2xl font-bold mb-4">Importação</h2>
        
        <p class="mb-4">
          Para usar os componentes de layout, importe-os do módulo <code>layouts/grid</code>:
        </p>
        
        <pre class="bg-base-300 p-4 rounded-box overflow-x-auto text-sm"><code>import { 
  Container, 
  ResponsiveGrid, 
  GridItem, 
  Flex, 
  Section, 
  Spacer 
} from '../../layouts/grid';</code></pre>
      </div>
    </Section>
  </Container>
</BaseLayout>
