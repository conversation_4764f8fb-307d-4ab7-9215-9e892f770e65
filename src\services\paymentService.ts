import { producer } from '@config/kafka';
import { queryHelper } from '@db/queryHelper';
import type { PaymentData } from '@repositories/userRepository';
import { type AlertType, alertService } from './alertService';
// src/services/paymentService.ts
import { efiPayService } from './efiPayService';
import { paymentEventProducer } from './producers/paymentEventProducer';
import { nanoid } from 'nanoid';
import {
  type PaymentResponse,
  type PaymentError,
  PaymentStatus as SecurePaymentStatus,
  PaymentMethod,
  PaymentProvider,
  validatePaymentAmount
} from '@types/payment';

/**
 * Enum para tipos de pagamento
 */
export enum PaymentType {
  PIX = 'pix',
  CREDIT_CARD = 'credit_card',
  BILLET = 'billet',
}

/**
 * Enum para status de pagamento
 */
export enum PaymentStatus {
  PENDING = 1,
  APPROVED = 2,
  REJECTED = 3,
  REFUNDED = 4,
  CANCELLED = 5,
}

/**
 * Interface para dados de pagamento
 */
export interface PaymentRequest {
  orderId: string;
  userId: string;
  value: number;
  paymentType: PaymentType;
  description?: string;
  customerData?: {
    name: string;
    cpf: string;
    email: string;
    birthDate?: string;
    phone?: string;
  };
  paymentToken?: string;
  installments?: number;
  expirationDate?: string;
}

/**
 * Serviço para processamento de pagamentos
 */
export const paymentService = {
  /**
   * Processa um pagamento
   * @param paymentData - Dados do pagamento
   * @returns Resultado do processamento
   */
  async processPayment(paymentData: PaymentRequest): Promise<PaymentResponse | PaymentError> {
    try {
      // Obter o tipo de pagamento do banco de dados
      const paymentTypeResult = await queryHelper.queryOne(
        'SELECT ulid_payment_type FROM tab_payment_type WHERE type = $1 AND active = true',
        [paymentData.paymentType]
      );

      if (!paymentTypeResult) {
        throw new Error(`Tipo de pagamento ${paymentData.paymentType} não encontrado ou inativo`);
      }

      const paymentTypeId = paymentTypeResult.ulid_payment_type;

      // Criar registro de pagamento com status pendente
      const paymentId = await this.createPaymentRecord(
        paymentData.orderId,
        paymentTypeId,
        PaymentStatus.PENDING,
        paymentData.value
      );

      // Processar pagamento de acordo com o tipo
      let paymentResult;
      switch (paymentData.paymentType) {
        case PaymentType.PIX:
          paymentResult = await this.processPixPayment(paymentData, paymentId);
          break;
        case PaymentType.CREDIT_CARD:
          paymentResult = await this.processCreditCardPayment(paymentData, paymentId);
          break;
        case PaymentType.BILLET:
          paymentResult = await this.processBilletPayment(paymentData, paymentId);
          break;
        default:
          throw new Error(`Tipo de pagamento ${paymentData.paymentType} não suportado`);
      }

      // Enviar evento para o Kafka
      await this.sendPaymentEvent(paymentId, paymentData, paymentResult);

      return {
        paymentId,
        ...paymentResult,
      };
    } catch (error) {
      console.error('Erro ao processar pagamento:', error);
      throw error;
    }
  },

  /**
   * Cria um registro de pagamento no banco de dados
   * @param orderId - ID do pedido
   * @param paymentTypeId - ID do tipo de pagamento
   * @param status - Status do pagamento
   * @param value - Valor do pagamento
   * @returns ID do pagamento criado
   */
  async createPaymentRecord(
    orderId: string,
    paymentTypeId: string,
    status: PaymentStatus,
    value: number
  ): Promise<string> {
    const result = await queryHelper.query(
      `INSERT INTO tab_payment (
        ulid_payment,
        ulid_order,
        ulid_payment_type,
        cod_status,
        value,
        created_at,
        updated_at
      ) VALUES (
        $1, $2, $3, $4, $5, NOW(), NOW()
      ) RETURNING ulid_payment`,
      [nanoid(), orderId, paymentTypeId, status, value]
    );

    return result.rows[0].ulid_payment;
  },

  /**
   * Processa um pagamento PIX
   * @param paymentData - Dados do pagamento
   * @param paymentId - ID do pagamento
   * @returns Resultado do processamento
   */
  async processPixPayment(paymentData: PaymentRequest, paymentId: string): Promise<PaymentResponse> {
    // Converter valor de centavos para reais
    const valueInCents = Math.round(paymentData.value * 100);

    // Descrição padrão se não for fornecida
    const description = paymentData.description || `Pagamento #${paymentId}`;

    // Criar cobrança PIX
    const pixResult = await efiPayService.createPixCharge(valueInCents, description);

    // Atualizar registro com txid
    if (pixResult.txid) {
      await queryHelper.query(
        `UPDATE tab_payment
         SET external_id = $1,
             updated_at = NOW()
         WHERE ulid_payment = $2`,
        [pixResult.txid, paymentId]
      );
    }

    return pixResult;
  },

  /**
   * Processa um pagamento com cartão de crédito
   * @param paymentData - Dados do pagamento
   * @param paymentId - ID do pagamento
   * @returns Resultado do processamento
   */
  async processCreditCardPayment(paymentData: PaymentRequest, paymentId: string): Promise<PaymentResponse> {
    if (!paymentData.customerData) {
      throw new Error('Dados do cliente são obrigatórios para pagamento com cartão de crédito');
    }

    if (!paymentData.paymentToken) {
      throw new Error('Token de pagamento é obrigatório para pagamento com cartão de crédito');
    }

    // Converter valor de centavos para reais
    const valueInCents = Math.round(paymentData.value * 100);

    // Criar cobrança com cartão de crédito
    const ccResult = await efiPayService.createCreditCardCharge(
      valueInCents,
      paymentData.customerData,
      paymentData.paymentToken,
      paymentData.installments || 1
    );

    // Atualizar registro com charge_id
    if (ccResult.charge_id) {
      await queryHelper.query(
        `UPDATE tab_payment
         SET external_id = $1,
             updated_at = NOW()
         WHERE ulid_payment = $2`,
        [ccResult.charge_id.toString(), paymentId]
      );
    }

    return ccResult;
  },

  /**
   * Processa um pagamento com boleto
   * @param paymentData - Dados do pagamento
   * @param paymentId - ID do pagamento
   * @returns Resultado do processamento
   */
  async processBilletPayment(paymentData: PaymentRequest, paymentId: string): Promise<PaymentResponse> {
    if (!paymentData.customerData) {
      throw new Error('Dados do cliente são obrigatórios para pagamento com boleto');
    }

    if (!paymentData.expirationDate) {
      throw new Error('Data de vencimento é obrigatória para pagamento com boleto');
    }

    // Converter valor de centavos para reais
    const valueInCents = Math.round(paymentData.value * 100);

    // Criar cobrança com boleto
    const billetResult = await efiPayService.createBilletCharge(
      valueInCents,
      paymentData.customerData,
      paymentData.expirationDate
    );

    // Atualizar registro com charge_id
    if (billetResult.charge_id) {
      await queryHelper.query(
        `UPDATE tab_payment
         SET external_id = $1,
             updated_at = NOW()
         WHERE ulid_payment = $2`,
        [billetResult.charge_id.toString(), paymentId]
      );
    }

    return billetResult;
  },

  /**
   * Envia um evento de pagamento para o Kafka
   * @param paymentId - ID do pagamento
   * @param paymentData - Dados do pagamento
   * @param paymentResult - Resultado do processamento
   */
  async sendPaymentEvent(
    paymentId: string,
    paymentData: PaymentRequest,
    paymentResult: Record<string, unknown>
  ): Promise<void> {
    try {
      // Obter tipo de pagamento como string
      const paymentTypeResult = await queryHelper.queryOne(
        'SELECT type FROM tab_payment_type WHERE ulid_payment_type = (SELECT ulid_payment_type FROM tab_payment WHERE ulid_payment = $1)',
        [paymentId]
      );

      const paymentType = paymentTypeResult?.type || paymentData.paymentType;

      // Enviar evento usando o produtor de eventos de pagamento
      await paymentEventProducer.paymentCreated({
        paymentId,
        orderId: paymentData.orderId,
        userId: paymentData.userId,
        value: paymentData.value,
        paymentType: paymentType,
        status: PaymentStatus.PENDING,
        externalId:
          paymentResult.txid?.toString() || paymentResult.charge_id?.toString() || undefined,
        metadata: {
          result: paymentResult,
        },
      });
    } catch (error) {
      console.error('Erro ao enviar evento de pagamento:', error);
      // Não propagar erro para não interromper o fluxo principal
    }
  },

  /**
   * Verifica e dispara alertas relacionados a pagamentos
   * @param paymentId - ID do pagamento
   * @param status - Status do pagamento
   */
  async checkAndTriggerAlerts(paymentId: string, status: PaymentStatus): Promise<void> {
    try {
      // Obter dados do pagamento
      const paymentResult = await queryHelper.queryOne(
        `
        SELECT
          p.ulid_payment,
          p.ulid_order,
          p.value,
          pt.type as payment_type,
          s.status as payment_status,
          o.ulid_user,
          u.name as user_name,
          u.email as user_email,
          o.total as order_total,
          p.created_at as payment_date
        FROM tab_payment p
        JOIN tab_payment_type pt ON p.ulid_payment_type = pt.ulid_payment_type
        JOIN tab_status s ON p.cod_status = s.cod_status
        JOIN tab_order o ON p.ulid_order = o.ulid_order
        JOIN tab_user u ON o.ulid_user = u.ulid_user
        WHERE p.ulid_payment = $1
      `,
        [paymentId]
      );

      if (!paymentResult) {
        console.error(`Pagamento não encontrado: ${paymentId}`);
        return;
      }

      // Preparar dados para o alerta
      const alertData = {
        orderId: paymentResult.ulid_order,
        customerId: paymentResult.ulid_user,
        customerName: paymentResult.user_name,
        customerEmail: paymentResult.user_email,
        orderTotal: Number.parseFloat(paymentResult.order_total),
        paymentMethod: paymentResult.payment_type,
        paymentStatus: paymentResult.payment_status,
        transactionDate: new Date(paymentResult.payment_date),
        additionalInfo: {
          paymentId: paymentResult.ulid_payment,
          paymentValue: Number.parseFloat(paymentResult.value),
        },
      };

      // Determinar o tipo de alerta com base no status
      let alertType: AlertType | null = null;

      switch (status) {
        case PaymentStatus.APPROVED:
          alertType = 'payment_success';

          // Verificar também se é uma transação de alto valor
          if (alertData.orderTotal >= 1000) {
            await alertService.triggerAlert('high_value_transaction', alertData);
          }

          // Verificar risco de chargeback
          await alertService.triggerAlert('chargeback_risk', alertData);
          break;

        case PaymentStatus.REJECTED:
          alertType = 'payment_failure';

          // Verificar atividade suspeita
          await alertService.triggerAlert('suspicious_activity', alertData);
          break;

        case PaymentStatus.REFUNDED:
          alertType = 'refund_processed';
          break;

        default:
          // Não disparar alertas para outros status
          return;
      }

      if (alertType) {
        await alertService.triggerAlert(alertType, alertData);
      }
    } catch (error) {
      console.error('Erro ao verificar e disparar alertas:', error);
    }
  },
};
