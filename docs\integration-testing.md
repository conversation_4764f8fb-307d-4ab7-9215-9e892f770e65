# Documentação de Testes de Integração

## Visão Geral

Este documento descreve a implementação de testes de integração no projeto Estação Alfabetização, incluindo testes de API, banco de dados e fluxos de usuário. Os testes de integração verificam a interação entre diferentes componentes do sistema, garantindo que eles funcionem corretamente em conjunto.

## Ferramentas Utilizadas

- **Playwright**: Framework para testes de integração e end-to-end
- **Playwright Test**: Framework de testes baseado no Playwright
- **Node-Postgres (pg)**: Biblioteca para interação com banco de dados PostgreSQL
- **UUID**: Biblioteca para geração de identificadores únicos

## Estrutura de Diretórios

```
tests/
├── integration/
│   ├── api/                     # Testes de integração de API
│   │   ├── auth.api.spec.ts     # Testes de API de autenticação
│   │   ├── users.api.spec.ts    # Testes de API de usuários
│   │   ├── products.api.spec.ts # Testes de API de produtos
│   │   └── orders.api.spec.ts   # Testes de API de pedidos
│   ├── database/                # Testes de integração com banco de dados
│   │   └── database.integration.spec.ts # Testes de operações de banco de dados
│   ├── services/                # Testes de integração de serviços
│   │   └── payment.service.spec.ts # Testes de serviço de pagamento
│   ├── user-flows/              # Testes de fluxos de usuário
│   │   ├── auth.user-flows.spec.ts    # Fluxos de autenticação
│   │   ├── checkout.user-flows.spec.ts # Fluxos de checkout
│   │   └── payment.user-flows.spec.ts  # Fluxos de pagamento
│   └── playwright.config.ts     # Configuração do Playwright
└── setup.ts                     # Configuração global para testes
```

## Configuração do Playwright

O arquivo `tests/integration/playwright.config.ts` contém a configuração do Playwright para testes de integração, incluindo:

- Configuração de ambiente de teste
- Configuração de relatórios
- Configuração de projetos de teste
- Configuração de servidor web para testes

### Projetos de Teste

Os testes são organizados em diferentes projetos:

- **integration-chrome**: Testes de integração padrão executados no Chrome
- **api**: Testes específicos de API
- **database**: Testes específicos de banco de dados
- **services**: Testes específicos de serviços
- **user-flows**: Testes de fluxos de usuário

## Tipos de Testes de Integração

### Testes de API

Os testes de API verificam a integração entre as APIs e os serviços relacionados. Eles testam:

- Autenticação e autorização
- Operações CRUD em recursos
- Validação de dados
- Tratamento de erros

#### Exemplos de Testes de API

- **auth.api.spec.ts**: Testa a API de autenticação, incluindo login, registro, verificação de token e renovação de token.
- **users.api.spec.ts**: Testa a API de usuários, incluindo operações CRUD e permissões.
- **products.api.spec.ts**: Testa a API de produtos, incluindo operações CRUD, filtragem e pesquisa.
- **orders.api.spec.ts**: Testa a API de pedidos, incluindo criação, atualização de status e cancelamento.

### Testes de Banco de Dados

Os testes de banco de dados verificam a integração entre o código e o banco de dados. Eles testam:

- Operações CRUD em tabelas
- Transações e rollbacks
- Integridade referencial
- Consultas complexas

#### Exemplos de Testes de Banco de Dados

- **database.integration.spec.ts**: Testa operações CRUD em tabelas, transações e rollbacks.

### Testes de Fluxos de Usuário

Os testes de fluxos de usuário verificam fluxos completos de interação do usuário com o sistema. Eles testam:

- Fluxos de autenticação
- Fluxos de compra
- Fluxos de pagamento
- Validação de formulários

#### Exemplos de Testes de Fluxos de Usuário

- **auth.user-flows.spec.ts**: Testa fluxos de autenticação, incluindo registro, login e recuperação de senha.
- **checkout.user-flows.spec.ts**: Testa fluxos de checkout, incluindo adição ao carrinho, preenchimento de endereço e finalização de pedido.
- **payment.user-flows.spec.ts**: Testa fluxos de pagamento, incluindo pagamento com cartão de crédito e boleto.

## Estratégias de Teste

### Preparação de Dados

Os testes de integração frequentemente precisam de dados de teste. Utilizamos as seguintes estratégias:

1. **Criação de dados no início do teste**: Criamos dados necessários no início de cada teste.
2. **Limpeza de dados após o teste**: Limpamos os dados criados após cada teste para evitar interferência entre testes.
3. **Uso de prefixos para tabelas de teste**: Usamos prefixos para identificar tabelas de teste e evitar conflitos com dados reais.

### Autenticação

Para testes que requerem autenticação, utilizamos as seguintes estratégias:

1. **Criação de usuários de teste**: Criamos usuários específicos para testes.
2. **Obtenção de tokens de acesso**: Obtemos tokens de acesso para autenticação em APIs.
3. **Login em fluxos de usuário**: Realizamos login antes de executar fluxos que requerem autenticação.

### Isolamento de Testes

Para garantir que os testes sejam independentes e não interfiram uns nos outros, utilizamos:

1. **Dados únicos**: Usamos identificadores únicos (UUID) para evitar conflitos.
2. **Limpeza antes e depois dos testes**: Limpamos dados antes e depois de cada teste.
3. **Transações com rollback**: Usamos transações com rollback para testes de banco de dados.

## Execução de Testes

### Execução Local

Para executar os testes de integração localmente:

```bash
# Executar todos os testes de integração
npm run test:integration

# Executar testes de API
npm run test:integration:api

# Executar testes de banco de dados
npm run test:integration:database

# Executar testes de fluxos de usuário
npm run test:integration:user-flows
```

### Execução em CI/CD

Os testes de integração são executados automaticamente no pipeline de CI/CD:

1. **Pull Requests**: Todos os testes são executados em pull requests.
2. **Merge para Main**: Todos os testes são executados antes de merge para main.
3. **Deploy para Produção**: Todos os testes são executados antes de deploy para produção.

## Relatórios de Testes

Os relatórios de testes são gerados automaticamente após a execução dos testes:

- **Relatório HTML**: Disponível em `tests/integration/reports/index.html`
- **Relatório JSON**: Disponível em `tests/integration/reports/results.json`
- **Capturas de Tela**: Disponíveis em `tests/integration/reports/screenshots` (apenas para falhas)
- **Vídeos**: Disponíveis em `tests/integration/reports/videos` (apenas para falhas)

## Melhores Práticas

### Nomenclatura de Testes

- Use nomes descritivos para testes
- Siga o padrão "deve [comportamento esperado] quando [condição]"
- Agrupe testes relacionados em blocos `test.describe`

### Organização de Testes

- Um arquivo de teste por recurso ou fluxo
- Organize testes em blocos `test.describe` para agrupar testes relacionados
- Use `test.beforeAll`, `test.afterAll`, `test.beforeEach` e `test.afterEach` para configuração e limpeza

### Asserções

- Use asserções específicas e descritivas
- Evite asserções múltiplas em um único teste
- Use matchers apropriados para o tipo de asserção

## Conclusão

A implementação de testes de integração no projeto Estação Alfabetização segue as melhores práticas da indústria e fornece uma base sólida para garantir a qualidade do código. A combinação de testes de API, banco de dados e fluxos de usuário fornece uma cobertura abrangente e permite a detecção precoce de problemas de integração entre diferentes componentes do sistema.
