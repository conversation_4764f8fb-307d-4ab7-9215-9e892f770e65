# Segurança do Valkey

## Visão Geral

Este documento descreve as medidas de segurança implementadas para o Valkey (fork do Redis) no projeto Estação da Alfabetização. O Valkey é utilizado como sistema de cache e armazenamento de dados temporários, e sua segurança é crítica para a proteção dos dados do sistema.

## Componentes de Segurança

### 1. Autenticação Básica

A autenticação básica do Valkey é implementada através de usuário e senha, garantindo que apenas clientes autorizados possam se conectar ao servidor.

#### Configuração

```bash
# Configuração no arquivo valkey.conf
requirepass "senha_forte_e_complexa"
```

#### Implementação

A autenticação é configurada através do arquivo `src/config/cache/security.config.ts`, que define as credenciais para diferentes ambientes:

```typescript
// Exemplo de configuração para ambiente de produção
'production': {
  authentication: {
    type: AuthenticationType.BASIC,
    username: process.env.VALKEY_USERNAME,
    password: process.env.VALKEY_PASSWORD,
    description: 'Autenticação básica para ambiente de produção',
  },
  // ...
}
```

#### Boas Práticas

1. **Senhas Fortes**: Utilize senhas com pelo menos 32 caracteres, incluindo letras maiúsculas, minúsculas, números e símbolos.
2. **Rotação de Senhas**: Altere as senhas periodicamente (a cada 90 dias).
3. **Armazenamento Seguro**: Armazene as senhas em variáveis de ambiente ou em um gerenciador de segredos.
4. **Acesso Restrito**: Limite o acesso às credenciais apenas a administradores autorizados.

### 2. TLS/SSL

A comunicação entre clientes e o servidor Valkey é protegida por TLS/SSL, garantindo a confidencialidade e integridade dos dados em trânsito.

#### Configuração

```bash
# Configuração no arquivo valkey.conf
tls-cert-file /path/to/valkey.crt
tls-key-file /path/to/valkey.key
tls-ca-cert-file /path/to/ca.crt
tls-auth-clients yes
tls-protocols "TLSv1.2 TLSv1.3"
tls-prefer-server-ciphers yes
```

#### Implementação

A configuração TLS/SSL é definida no arquivo `src/config/cache/tls-config.ts`:

```typescript
// Exemplo de configuração para ambiente de produção
'production': {
  enabled: process.env.VALKEY_TLS === 'true',
  certPath: process.env.VALKEY_CERT_PATH || '/etc/valkey/certs/valkey.crt',
  keyPath: process.env.VALKEY_KEY_PATH || '/etc/valkey/certs/valkey.key',
  caPath: process.env.VALKEY_CA_PATH || '/etc/valkey/certs/ca.crt',
  verifyClient: true,
  protocols: ['TLSv1.2', 'TLSv1.3'],
  // ...
}
```

#### Boas Práticas

1. **Certificados Válidos**: Utilize certificados emitidos por autoridades certificadoras confiáveis.
2. **Protocolos Seguros**: Habilite apenas TLSv1.2 e TLSv1.3, desabilitando versões mais antigas.
3. **Cifras Fortes**: Configure apenas cifras seguras e atualizadas.
4. **Verificação de Cliente**: Habilite a verificação de certificados de cliente em ambientes de produção.
5. **Renovação de Certificados**: Implemente um processo para renovação automática de certificados antes da expiração.

### 3. Políticas de Acesso

As políticas de acesso controlam quais comandos e chaves cada usuário pode acessar, implementando o princípio do menor privilégio.

#### Configuração

```bash
# Configuração no arquivo valkey.conf ou users.acl
user app on >password ~app:* -@all +@read +@write +@string +@list +@set +@hash
user monitor on >password ~* -@all +@read +INFO
```

#### Implementação

As políticas de acesso são definidas no arquivo `src/config/cache/access-policy.config.ts`:

```typescript
// Exemplo de política de acesso para aplicação web
'web-app': {
  name: 'web-app',
  description: 'Acesso para aplicação web',
  allowedCommands: [],
  allowedCategories: [
    CommandCategories.READ,
    CommandCategories.WRITE,
    CommandCategories.STRING,
    CommandCategories.HASH,
    // ...
  ],
  allowedKeyPatterns: ['web:*', 'session:*', 'user:*', 'cache:*'],
  memoryLimit: 1073741824, // 1GB
}
```

#### Boas Práticas

1. **Menor Privilégio**: Conceda apenas os privilégios mínimos necessários para cada usuário ou serviço.
2. **Separação por Prefixo**: Utilize prefixos de chave para separar dados de diferentes componentes.
3. **Limites de Memória**: Configure limites de memória para cada usuário para evitar ataques de negação de serviço.
4. **Auditoria de Acesso**: Implemente logging de acesso para monitorar e auditar operações.

### 4. Proteção de Comandos Perigosos

Comandos perigosos que podem afetar a disponibilidade ou segurança do sistema são renomeados ou desabilitados.

#### Configuração

```bash
# Configuração no arquivo valkey.conf
rename-command FLUSHALL ""
rename-command FLUSHDB ""
rename-command CONFIG "CONFIG_SECURE"
rename-command SHUTDOWN ""
rename-command DEBUG ""
```

#### Implementação

A proteção de comandos perigosos é configurada no arquivo `src/config/cache/security.config.ts`:

```typescript
// Exemplo de configuração para ambiente de produção
'production': {
  // ...
  accessPolicy: {
    enabled: true,
    blockedCommands: ['FLUSHALL', 'FLUSHDB', 'CONFIG', 'SHUTDOWN', 'DEBUG', 'KEYS', 'MONITOR'],
    // ...
  },
  renameCommands: {
    'FLUSHALL': '',
    'FLUSHDB': '',
    'CONFIG': 'CONFIG_SECURE',
    'SHUTDOWN': '',
    'DEBUG': '',
  },
  // ...
}
```

#### Boas Práticas

1. **Desabilitar Comandos Críticos**: Desabilite completamente comandos como FLUSHALL e SHUTDOWN em produção.
2. **Renomear Comandos Administrativos**: Renomeie comandos como CONFIG para nomes não triviais.
3. **Limitar Comandos de Alto Impacto**: Restrinja comandos como KEYS que podem afetar a performance.
4. **Documentar Renomeações**: Mantenha documentação clara sobre comandos renomeados para administradores.

## Implementação no Projeto

### Arquivos de Configuração

- `src/config/cache/security.config.ts`: Configurações gerais de segurança
- `src/config/cache/tls-config.ts`: Configurações de TLS/SSL
- `src/config/cache/access-policy.config.ts`: Políticas de acesso

### Serviços

- `src/services/valkeySecurityService.ts`: Serviço para gerenciar a segurança do Valkey

### Inicialização

A segurança do Valkey é inicializada durante o startup da aplicação:

```typescript
// Exemplo de inicialização
import { valkeySecurityService } from '@services/valkeySecurityService';

// Inicializar segurança do Valkey
await valkeySecurityService.initialize();
```

## Monitoramento e Auditoria

### Monitoramento de Segurança

O sistema monitora continuamente a segurança do Valkey, verificando:

1. **Tentativas de Acesso Não Autorizado**: Alertas para tentativas de login com credenciais inválidas
2. **Comandos Bloqueados**: Registro de tentativas de executar comandos bloqueados
3. **Uso de Memória**: Monitoramento de uso de memória por usuário e total
4. **Conexões**: Monitoramento de número e origem das conexões

### Relatórios de Segurança

O serviço `valkeySecurityService` pode gerar relatórios de segurança com:

1. **Status de Autenticação**: Verificação de configurações de autenticação
2. **Status de TLS/SSL**: Verificação de configurações de TLS/SSL
3. **Comandos Perigosos**: Verificação de proteção contra comandos perigosos
4. **Recomendações**: Sugestões para melhorar a segurança

## Recuperação de Desastres

### Backup e Restauração

O sistema implementa backup automático dos dados do Valkey:

1. **Backup Periódico**: Configuração de RDB (Redis Database Backup) a cada hora
2. **Backup Incremental**: Configuração de AOF (Append-Only File) para recuperação point-in-time
3. **Armazenamento Externo**: Cópias de backup armazenadas em local seguro

### Procedimentos de Recuperação

Em caso de comprometimento de segurança:

1. **Isolamento**: Isolar o servidor comprometido da rede
2. **Análise**: Analisar logs para determinar o escopo da violação
3. **Restauração**: Restaurar a partir do último backup seguro
4. **Rotação de Credenciais**: Alterar todas as senhas e chaves
5. **Verificação**: Verificar a integridade dos dados restaurados

## Referências

- [Documentação de Segurança do Valkey](https://valkey.io/docs/security)
- [Guia de Segurança do Redis](https://redis.io/docs/management/security/)
- [OWASP Cheat Sheet para Redis](https://cheatsheetseries.owasp.org/cheatsheets/Redis_Security_Cheat_Sheet.html)
- [Melhores Práticas de TLS](https://github.com/ssllabs/research/wiki/SSL-and-TLS-Deployment-Best-Practices)
