---
import { PageTransition } from '../../../components/transitions';
import DaisyButton from '../../../components/ui/DaisyButton.astro';
import DaisyCard from '../../../components/ui/DaisyCard.astro';
import Breadcrumbs from '../../../components/ui/navigation/Breadcrumbs.astro';
import { GetContactMessageDetailsUseCase } from '../../../domain/usecases/contact/GetContactMessageDetailsUseCase';
import { PostgresContactMessageRepository } from '../../../infrastructure/database/repositories/PostgresContactMessageRepository';
/**
 * Página de Detalhes da Mensagem
 *
 * Interface para visualizar e responder a uma mensagem de contato.
 * Parte da implementação da tarefa 8.6.2 - Gestão de mensagens
 */
import BaseLayout from '../../../layouts/BaseLayout.astro';
import { Container, Section } from '../../../layouts/grid';

// Obter ID da mensagem da URL
const { id } = Astro.params;

// Verificar se o ID foi fornecido
if (!id) {
  return Astro.redirect('/admin/mensagens');
}

// Obter parâmetros de consulta
const reply = Astro.url.searchParams.get('reply') === 'true';

// Inicializar repositório e caso de uso
const contactMessageRepository = new PostgresContactMessageRepository();
const getContactMessageDetailsUseCase = new GetContactMessageDetailsUseCase(
  contactMessageRepository
);

// Obter detalhes da mensagem
const result = await getContactMessageDetailsUseCase.execute({
  id,
  markAsRead: true,
});

// Verificar se a mensagem foi encontrada
if (!result.success || !result.data) {
  return Astro.redirect('/admin/mensagens?error=Mensagem não encontrada');
}

const { message, replies } = result.data;

// Título da página
const title = `Mensagem: ${message.subject}`;

// Breadcrumbs para a página atual
const breadcrumbItems = [
  { href: '/', label: 'Início' },
  { href: '/admin', label: 'Administração' },
  { href: '/admin/mensagens', label: 'Mensagens' },
  { label: truncateText(message.subject, 30) },
];

// Categorias disponíveis
const categories = [
  { value: 'general', label: 'Geral' },
  { value: 'support', label: 'Suporte' },
  { value: 'sales', label: 'Vendas' },
  { value: 'billing', label: 'Faturamento' },
  { value: 'technical', label: 'Técnico' },
  { value: 'partnership', label: 'Parcerias' },
  { value: 'feedback', label: 'Feedback' },
  { value: 'other', label: 'Outro' },
];

// Status disponíveis
const statuses = [
  { value: 'pending', label: 'Pendente', color: 'badge-warning' },
  { value: 'read', label: 'Lida', color: 'badge-info' },
  { value: 'replied', label: 'Respondida', color: 'badge-success' },
  { value: 'archived', label: 'Arquivada', color: 'badge-neutral' },
  { value: 'spam', label: 'Spam', color: 'badge-error' },
];

// Prioridades disponíveis
const priorities = [
  { value: 'low', label: 'Baixa', color: 'text-success' },
  { value: 'medium', label: 'Média', color: 'text-info' },
  { value: 'high', label: 'Alta', color: 'text-warning' },
  { value: 'urgent', label: 'Urgente', color: 'text-error' },
];

// Função para truncar texto
function truncateText(text: string, maxLength: number): string {
  if (text.length <= maxLength) {
    return text;
  }

  return `${text.substring(0, maxLength)}...`;
}
---

<BaseLayout title={title} enableViewTransitions={true} transitionType="fade">
  <Container>
    <PageTransition type="fade" duration="0.5s">
      <Section>
        <Breadcrumbs items={breadcrumbItems} class="mb-6" />
        
        <div class="flex justify-between items-center mb-6">
          <h1 class="text-3xl font-bold">{title}</h1>
          
          <div class="flex gap-2">
            <DaisyButton 
              href="/admin/mensagens" 
              variant="outline" 
              icon="arrow-left"
              text="Voltar"
            />
            
            <div class="dropdown dropdown-end">
              <DaisyButton 
                variant="primary" 
                icon="more-horizontal"
                text="Ações"
                class="dropdown-toggle"
              />
              <ul class="dropdown-content z-[1] menu p-2 shadow bg-base-100 rounded-box w-52">
                <li>
                  <button data-action="markAsRead" data-id={message.id}>
                    <i class="icon icon-check"></i> Marcar como lida
                  </button>
                </li>
                <li>
                  <button data-action="markAsReplied" data-id={message.id}>
                    <i class="icon icon-check-circle"></i> Marcar como respondida
                  </button>
                </li>
                <li>
                  <button data-action="archive" data-id={message.id}>
                    <i class="icon icon-archive"></i> Arquivar
                  </button>
                </li>
                <li>
                  <button data-action="markAsSpam" data-id={message.id}>
                    <i class="icon icon-alert-triangle"></i> Marcar como spam
                  </button>
                </li>
                <li>
                  <button data-action="delete" data-id={message.id} class="text-error">
                    <i class="icon icon-trash"></i> Excluir
                  </button>
                </li>
              </ul>
            </div>
          </div>
        </div>
        
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div class="lg:col-span-2">
            <DaisyCard>
              <div class="p-6">
                <div class="flex justify-between items-start mb-6">
                  <div>
                    <h2 class="text-2xl font-bold">{message.subject}</h2>
                    <p class="text-gray-500">
                      De: <span class="font-medium">{message.name}</span> &lt;{message.email}&gt;
                      {message.phone && ` | Telefone: ${message.phone}`}
                    </p>
                  </div>
                  
                  <span class={`badge ${statuses.find(s => s.value === message.status)?.color || 'badge-ghost'}`}>
                    {statuses.find(s => s.value === message.status)?.label || message.status}
                  </span>
                </div>
                
                <div class="divider"></div>
                
                <div class="mb-6 whitespace-pre-wrap">
                  {message.message}
                </div>
                
                {message.attachments && message.attachments.length > 0 && (
                  <div class="mb-6">
                    <h3 class="font-bold mb-2">Anexos</h3>
                    <div class="flex flex-wrap gap-2">
                      {message.attachments.map((attachment: string) => (
                        <a 
                          href={attachment} 
                          target="_blank" 
                          rel="noopener noreferrer" 
                          class="btn btn-sm btn-outline"
                        >
                          <i class="icon icon-paperclip mr-1"></i>
                          {attachment.split('/').pop()}
                        </a>
                      ))}
                    </div>
                  </div>
                )}
                
                <div class="divider"></div>
                
                {replies && replies.length > 0 ? (
                  <div class="mb-6">
                    <h3 class="font-bold mb-4">Respostas anteriores</h3>
                    
                    <div class="space-y-4">
                      {replies.map((reply) => (
                        <div class="bg-base-200 p-4 rounded-lg">
                          <div class="flex justify-between items-start mb-2">
                            <div>
                              <span class="font-bold">{reply.userName}</span>
                              <span class="text-sm text-gray-500 ml-2">
                                {reply.createdAt.toLocaleString('pt-BR')}
                              </span>
                            </div>
                          </div>
                          
                          <div class="whitespace-pre-wrap">
                            {reply.content}
                          </div>
                          
                          {reply.attachments && reply.attachments.length > 0 && (
                            <div class="mt-2">
                              <div class="flex flex-wrap gap-2">
                                {reply.attachments.map((attachment: string) => (
                                  <a 
                                    href={attachment} 
                                    target="_blank" 
                                    rel="noopener noreferrer" 
                                    class="btn btn-xs btn-outline"
                                  >
                                    <i class="icon icon-paperclip mr-1"></i>
                                    {attachment.split('/').pop()}
                                  </a>
                                ))}
                              </div>
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                ) : (
                  <div class="mb-6">
                    <p class="text-gray-500 text-center">Nenhuma resposta anterior</p>
                  </div>
                )}
                
                <div id="reply-form" class={reply ? '' : 'hidden'}>
                  <h3 class="font-bold mb-4">Responder</h3>
                  
                  <form action="/api/contact/reply" method="POST" class="space-y-4">
                    <input type="hidden" name="messageId" value={message.id} />
                    
                    <div class="form-control">
                      <textarea 
                        name="content" 
                        class="textarea textarea-bordered h-32" 
                        placeholder="Digite sua resposta aqui..."
                        required
                      ></textarea>
                    </div>
                    
                    <div class="form-control">
                      <label class="label">
                        <span class="label-text">Anexos</span>
                      </label>
                      <input 
                        type="file" 
                        name="attachments" 
                        class="file-input file-input-bordered w-full" 
                        multiple
                      />
                    </div>
                    
                    <div class="form-control">
                      <label class="flex items-center gap-2 cursor-pointer">
                        <input 
                          type="checkbox" 
                          name="markAsReplied" 
                          class="checkbox checkbox-primary" 
                          checked
                        />
                        <span>Marcar mensagem como respondida</span>
                      </label>
                    </div>
                    
                    <div class="flex justify-end gap-2">
                      <button 
                        type="button" 
                        class="btn btn-outline" 
                        id="cancel-reply"
                      >
                        Cancelar
                      </button>
                      
                      <button type="submit" class="btn btn-primary">
                        <i class="icon icon-send mr-2"></i>
                        Enviar Resposta
                      </button>
                    </div>
                  </form>
                </div>
                
                <div id="reply-button" class={`flex justify-center ${reply ? 'hidden' : ''}`}>
                  <button class="btn btn-primary" id="show-reply-form">
                    <i class="icon icon-message-circle mr-2"></i>
                    Responder
                  </button>
                </div>
              </div>
            </DaisyCard>
          </div>
          
          <div>
            <DaisyCard>
              <div class="p-6">
                <h2 class="text-xl font-bold mb-4">Informações</h2>
                
                <div class="space-y-4">
                  <div>
                    <h3 class="font-bold">Status</h3>
                    <div class="flex items-center mt-1">
                      <span class={`badge ${statuses.find(s => s.value === message.status)?.color || 'badge-ghost'}`}>
                        {statuses.find(s => s.value === message.status)?.label || message.status}
                      </span>
                    </div>
                  </div>
                  
                  <div>
                    <h3 class="font-bold">Categoria</h3>
                    <p>{categories.find(c => c.value === message.category)?.label || message.category}</p>
                  </div>
                  
                  <div>
                    <h3 class="font-bold">Prioridade</h3>
                    <div class="flex items-center mt-1">
                      <select 
                        class="select select-bordered w-full" 
                        id="priority-select" 
                        data-id={message.id}
                      >
                        {priorities.map(p => (
                          <option 
                            value={p.value} 
                            selected={message.priority === p.value}
                            class={p.color}
                          >
                            {p.label}
                          </option>
                        ))}
                      </select>
                    </div>
                  </div>
                  
                  <div>
                    <h3 class="font-bold">Atribuído a</h3>
                    <div class="flex items-center mt-1">
                      <select 
                        class="select select-bordered w-full" 
                        id="assigned-to-select" 
                        data-id={message.id}
                      >
                        <option value="" selected={!message.assignedTo}>Não atribuído</option>
                        <option value="current-user-id" selected={message.assignedTo === 'current-user-id'}>
                          Eu
                        </option>
                        <option value="user-1" selected={message.assignedTo === 'user-1'}>
                          João Silva
                        </option>
                        <option value="user-2" selected={message.assignedTo === 'user-2'}>
                          Maria Oliveira
                        </option>
                      </select>
                    </div>
                  </div>
                  
                  <div>
                    <h3 class="font-bold">Tags</h3>
                    <div class="flex flex-wrap gap-2 mt-2">
                      {message.tags && message.tags.length > 0 ? (
                        message.tags.map((tag: string) => (
                          <div class="badge badge-outline gap-1">
                            {tag}
                            <button 
                              class="text-xs" 
                              data-action="removeTag" 
                              data-id={message.id} 
                              data-tag={tag}
                            >
                              <i class="icon icon-x"></i>
                            </button>
                          </div>
                        ))
                      ) : (
                        <p class="text-gray-500">Nenhuma tag</p>
                      )}
                    </div>
                    
                    <div class="flex items-center mt-2">
                      <input 
                        type="text" 
                        class="input input-bordered input-sm flex-1" 
                        placeholder="Nova tag" 
                        id="new-tag-input"
                      />
                      <button 
                        class="btn btn-sm btn-square ml-1" 
                        id="add-tag-button" 
                        data-id={message.id}
                      >
                        <i class="icon icon-plus"></i>
                      </button>
                    </div>
                  </div>
                  
                  <div>
                    <h3 class="font-bold">Datas</h3>
                    <div class="text-sm">
                      <p><span class="font-medium">Criada em:</span> {message.createdAt.toLocaleString('pt-BR')}</p>
                      {message.readAt && (
                        <p><span class="font-medium">Lida em:</span> {message.readAt.toLocaleString('pt-BR')}</p>
                      )}
                      {message.repliedAt && (
                        <p><span class="font-medium">Respondida em:</span> {message.repliedAt.toLocaleString('pt-BR')}</p>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </DaisyCard>
          </div>
        </div>
      </Section>
    </PageTransition>
  </Container>
</BaseLayout>

<script>
  // Script para funcionalidade da página de detalhes da mensagem
  document.addEventListener('DOMContentLoaded', () => {
    // Formulário de resposta
    const replyForm = document.getElementById('reply-form');
    const replyButton = document.getElementById('reply-button');
    const showReplyFormButton = document.getElementById('show-reply-form');
    const cancelReplyButton = document.getElementById('cancel-reply');
    
    if (replyForm && replyButton && showReplyFormButton && cancelReplyButton) {
      showReplyFormButton.addEventListener('click', () => {
        replyForm.classList.remove('hidden');
        replyButton.classList.add('hidden');
      });
      
      cancelReplyButton.addEventListener('click', () => {
        replyForm.classList.add('hidden');
        replyButton.classList.remove('hidden');
      });
    }
    
    // Ações de mensagem
    const actionButtons = document.querySelectorAll('[data-action]');
    
    actionButtons.forEach(button => {
      button.addEventListener('click', async () => {
        const action = button.getAttribute('data-action');
        const messageId = button.getAttribute('data-id');
        
        if (!action || !messageId) {
          return;
        }
        
        switch (action) {
          case 'markAsRead':
            if (await confirmAction('Marcar como lida', 'Tem certeza que deseja marcar esta mensagem como lida?')) {
              // Em um cenário real, aqui seria feita uma chamada para a API
              console.log(`Marcando mensagem ${messageId} como lida`);
              window.location.reload();
            }
            break;
            
          case 'markAsReplied':
            if (await confirmAction('Marcar como respondida', 'Tem certeza que deseja marcar esta mensagem como respondida?')) {
              // Em um cenário real, aqui seria feita uma chamada para a API
              console.log(`Marcando mensagem ${messageId} como respondida`);
              window.location.reload();
            }
            break;
            
          case 'archive':
            if (await confirmAction('Arquivar', 'Tem certeza que deseja arquivar esta mensagem?')) {
              // Em um cenário real, aqui seria feita uma chamada para a API
              console.log(`Arquivando mensagem ${messageId}`);
              window.location.href = '/admin/mensagens';
            }
            break;
            
          case 'markAsSpam':
            if (await confirmAction('Marcar como spam', 'Tem certeza que deseja marcar esta mensagem como spam?')) {
              // Em um cenário real, aqui seria feita uma chamada para a API
              console.log(`Marcando mensagem ${messageId} como spam`);
              window.location.href = '/admin/mensagens';
            }
            break;
            
          case 'delete':
            if (await confirmAction('Excluir', 'Tem certeza que deseja excluir esta mensagem? Esta ação não pode ser desfeita.')) {
              // Em um cenário real, aqui seria feita uma chamada para a API
              console.log(`Excluindo mensagem ${messageId}`);
              window.location.href = '/admin/mensagens';
            }
            break;
            
          case 'removeTag':
            const tag = button.getAttribute('data-tag');
            
            if (tag && await confirmAction('Remover tag', `Tem certeza que deseja remover a tag "${tag}"?`)) {
              // Em um cenário real, aqui seria feita uma chamada para a API
              console.log(`Removendo tag ${tag} da mensagem ${messageId}`);
              window.location.reload();
            }
            break;
        }
      });
    });
    
    // Seletor de prioridade
    const prioritySelect = document.getElementById('priority-select') as HTMLSelectElement;
    
    if (prioritySelect) {
      prioritySelect.addEventListener('change', async () => {
        const messageId = prioritySelect.getAttribute('data-id');
        const priority = prioritySelect.value;
        
        if (!messageId || !priority) {
          return;
        }
        
        // Em um cenário real, aqui seria feita uma chamada para a API
        console.log(`Definindo prioridade da mensagem ${messageId} como ${priority}`);
        
        // Simular atualização bem-sucedida
        alert(`Prioridade atualizada para ${priority}`);
      });
    }
    
    // Seletor de atribuição
    const assignedToSelect = document.getElementById('assigned-to-select') as HTMLSelectElement;
    
    if (assignedToSelect) {
      assignedToSelect.addEventListener('change', async () => {
        const messageId = assignedToSelect.getAttribute('data-id');
        const userId = assignedToSelect.value;
        
        if (!messageId) {
          return;
        }
        
        // Em um cenário real, aqui seria feita uma chamada para a API
        console.log(`Atribuindo mensagem ${messageId} para ${userId || 'ninguém'}`);
        
        // Simular atualização bem-sucedida
        alert(`Mensagem atribuída para ${userId ? 'usuário ' + userId : 'ninguém'}`);
      });
    }
    
    // Adicionar tag
    const newTagInput = document.getElementById('new-tag-input') as HTMLInputElement;
    const addTagButton = document.getElementById('add-tag-button');
    
    if (newTagInput && addTagButton) {
      addTagButton.addEventListener('click', async () => {
        const messageId = addTagButton.getAttribute('data-id');
        const tag = newTagInput.value.trim();
        
        if (!messageId || !tag) {
          return;
        }
        
        // Em um cenário real, aqui seria feita uma chamada para a API
        console.log(`Adicionando tag ${tag} à mensagem ${messageId}`);
        
        // Simular atualização bem-sucedida
        alert(`Tag "${tag}" adicionada`);
        newTagInput.value = '';
        window.location.reload();
      });
      
      newTagInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
          addTagButton.click();
          e.preventDefault();
        }
      });
    }
    
    // Função para confirmar ação
    async function confirmAction(title: string, message: string): Promise<boolean> {
      return confirm(message);
    }
  });
</script>
