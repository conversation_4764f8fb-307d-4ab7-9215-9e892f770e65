---
import DaisyButton from '../../components/ui/DaisyButton.astro';
import DaisyCard from '../../components/ui/DaisyCard.astro';
import Alert from '../../components/ui/feedback/Alert.astro';
import Breadcrumbs from '../../components/ui/navigation/Breadcrumbs.astro';
import Tabs from '../../components/ui/navigation/Tabs.astro';
import BaseLayout from '../../layouts/BaseLayout.astro';

// Importar componentes de layout
import { Container, Section } from '../../layouts/grid';

import ResponsiveTable from '../../components/ui/data/ResponsiveTable.astro';
import ResponsiveImage from '../../components/ui/media/ResponsiveImage.astro';
// Importar componentes responsivos
import MobileMenu from '../../components/ui/navigation/MobileMenu.astro';

const title = 'Estratégias para Dispositivos Móveis';

// Breadcrumbs para a página atual
const breadcrumbItems = [
  { href: '/', label: 'Início' },
  { href: '/examples', label: 'Exemplos' },
  { label: 'Mobile' },
];

// Tabs para as categorias
const mobileTabs = [
  { id: 'navigation', label: 'Navegação', isActive: true },
  { id: 'images', label: 'Imagens' },
  { id: 'tables', label: 'Tabelas' },
  { id: 'patterns', label: 'Padrões' },
];

// Itens do menu mobile
const menuItems = [
  { href: '/', label: 'Início', isActive: true },
  { href: '/examples', label: 'Exemplos' },
  { href: '/docs', label: 'Documentação' },
  {
    href: '#',
    label: 'Componentes',
    children: [
      { href: '/examples/layout', label: 'Layout' },
      { href: '/examples/forms', label: 'Formulários' },
      { href: '/examples/mobile', label: 'Mobile' },
    ],
  },
  { href: '/about', label: 'Sobre' },
];

// Dados para a tabela
const tableColumns = [
  { key: 'id', header: 'ID', width: '80px', align: 'center', priority: 1 },
  { key: 'name', header: 'Nome', priority: 1 },
  { key: 'email', header: 'Email', priority: 2 },
  { key: 'role', header: 'Função', priority: 3 },
  { key: 'status', header: 'Status', align: 'center', priority: 2 },
];

const tableData = [
  { id: 1, name: 'João Silva', email: '<EMAIL>', role: 'Administrador', status: 'Ativo' },
  { id: 2, name: 'Maria Souza', email: '<EMAIL>', role: 'Editor', status: 'Ativo' },
  { id: 3, name: 'Pedro Santos', email: '<EMAIL>', role: 'Usuário', status: 'Inativo' },
  { id: 4, name: 'Ana Oliveira', email: '<EMAIL>', role: 'Editor', status: 'Ativo' },
  { id: 5, name: 'Carlos Pereira', email: '<EMAIL>', role: 'Usuário', status: 'Ativo' },
];
---

<BaseLayout title={title}>
  <Container>
    <Breadcrumbs items={breadcrumbItems} class="my-4" />
    
    <Section title={title} subtitle="Estratégias e componentes otimizados para dispositivos móveis">
      <div class="mb-8">
        <p>
          Esta página demonstra estratégias e componentes otimizados para dispositivos móveis,
          incluindo navegação responsiva, imagens otimizadas e tabelas adaptáveis.
        </p>
        
        <Alert type="info" class="mt-4">
          <p>
            Redimensione a janela do navegador ou use as ferramentas de desenvolvedor para simular diferentes dispositivos
            e ver como os componentes se adaptam.
          </p>
        </Alert>
      </div>
      
      <Tabs tabs={mobileTabs}>
        <!-- Navegação -->
        <div slot="navigation" class="py-4">
          <h2 class="text-2xl font-bold mb-4">Navegação Mobile</h2>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            <DaisyCard title="Menu Dropdown">
              <div class="border rounded-box p-4 bg-base-200">
                <div class="navbar bg-base-100 rounded-box">
                  <div class="flex-1">
                    <a class="btn btn-ghost normal-case text-xl">Logo</a>
                  </div>
                  <div class="flex-none hidden lg:block">
                    <ul class="menu menu-horizontal px-1">
                      {menuItems.map(item => (
                        <li>
                          <a href={item.href} class={item.isActive ? 'active' : ''}>
                            {item.label}
                          </a>
                        </li>
                      ))}
                    </ul>
                  </div>
                  <MobileMenu
                    items={menuItems}
                    position="right"
                    variant="default"
                  />
                </div>
              </div>
              <p class="mt-4 text-sm">
                Menu dropdown simples que aparece quando o botão de hambúrguer é clicado.
                Ideal para menus com poucos itens.
              </p>
            </DaisyCard>
            
            <DaisyCard title="Menu Drawer">
              <div class="border rounded-box p-4 bg-base-200">
                <MobileMenu
                  items={menuItems}
                  position="left"
                  variant="drawer"
                  logo={{ text: "Estação da Alfabetização" }}
                >
                  <div class="p-4">
                    <p>Conteúdo da página vai aqui.</p>
                    <p class="mt-2">O menu drawer é acessado pelo botão no canto superior.</p>
                  </div>
                </MobileMenu>
              </div>
              <p class="mt-4 text-sm">
                Menu drawer que desliza da lateral da tela. Ideal para menus com muitos itens
                ou estruturas de navegação complexas.
              </p>
            </DaisyCard>
          </div>
          
          <h3 class="text-xl font-bold mb-4">Boas Práticas</h3>
          
          <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <DaisyCard title="Áreas de Toque">
              <div class="p-4 bg-base-200 rounded-box">
                <p class="mb-2">Tamanho mínimo recomendado:</p>
                <div class="flex items-center justify-center w-10 h-10 bg-primary text-primary-content rounded-full">
                  44px
                </div>
              </div>
              <p class="mt-4 text-sm">
                Elementos interativos devem ter pelo menos 44x44 pixels para facilitar o toque em dispositivos móveis.
              </p>
            </DaisyCard>
            
            <DaisyCard title="Priorização de Conteúdo">
              <div class="p-4 bg-base-200 rounded-box">
                <div class="space-y-2">
                  <div class="p-2 bg-primary text-primary-content rounded">Conteúdo essencial</div>
                  <div class="p-2 bg-secondary text-secondary-content rounded">Conteúdo importante</div>
                  <div class="p-2 bg-accent text-accent-content rounded">Conteúdo secundário</div>
                </div>
              </div>
              <p class="mt-4 text-sm">
                Priorize o conteúdo mais importante em dispositivos móveis e oculte ou reorganize o conteúdo secundário.
              </p>
            </DaisyCard>
            
            <DaisyCard title="Gestos de Toque">
              <div class="p-4 bg-base-200 rounded-box">
                <div class="space-y-2">
                  <div class="p-2 bg-base-300 rounded">Toque: Selecionar</div>
                  <div class="p-2 bg-base-300 rounded">Deslizar: Navegar</div>
                  <div class="p-2 bg-base-300 rounded">Pinçar: Zoom</div>
                </div>
              </div>
              <p class="mt-4 text-sm">
                Considere gestos de toque comuns ao projetar interações para dispositivos móveis.
              </p>
            </DaisyCard>
          </div>
        </div>
        
        <!-- Imagens -->
        <div slot="images" class="py-4">
          <h2 class="text-2xl font-bold mb-4">Imagens Responsivas</h2>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            <DaisyCard title="Imagem Responsiva Básica">
              <ResponsiveImage
                src="https://via.placeholder.com/1200x600/0066cc/ffffff?text=Imagem+Responsiva"
                alt="Imagem responsiva"
                aspectRatio="16/9"
                sizes="(max-width: 768px) 100vw, 50vw"
                sources={[
                  { src: "https://via.placeholder.com/480x240/0066cc/ffffff?text=Mobile", width: 480 },
                  { src: "https://via.placeholder.com/768x384/0066cc/ffffff?text=Tablet", width: 768 },
                  { src: "https://via.placeholder.com/1200x600/0066cc/ffffff?text=Desktop", width: 1200 }
                ]}
              />
              <p class="mt-4 text-sm">
                Imagem responsiva que carrega diferentes tamanhos de acordo com o dispositivo.
                Isso economiza largura de banda em dispositivos móveis.
              </p>
            </DaisyCard>
            
            <DaisyCard title="Imagem com Lazy Loading">
              <ResponsiveImage
                src="https://via.placeholder.com/1200x600/ff6b6b/ffffff?text=Lazy+Loading"
                alt="Imagem com lazy loading"
                aspectRatio="16/9"
                loading="lazy"
                placeholder="blur"
                placeholderColor="#ff6b6b"
              />
              <p class="mt-4 text-sm">
                Imagem com lazy loading que só é carregada quando entra no viewport.
                Isso melhora o desempenho em conexões lentas.
              </p>
            </DaisyCard>
          </div>
          
          <h3 class="text-xl font-bold mb-4">Boas Práticas</h3>
          
          <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <DaisyCard title="Formatos Modernos">
              <div class="p-4 bg-base-200 rounded-box">
                <div class="space-y-2">
                  <div class="p-2 bg-base-300 rounded">WebP: ~30% menor que JPEG</div>
                  <div class="p-2 bg-base-300 rounded">AVIF: ~50% menor que JPEG</div>
                </div>
              </div>
              <p class="mt-4 text-sm">
                Use formatos de imagem modernos como WebP e AVIF para reduzir o tamanho dos arquivos.
              </p>
            </DaisyCard>
            
            <DaisyCard title="Dimensionamento Correto">
              <div class="p-4 bg-base-200 rounded-box">
                <div class="space-y-2">
                  <div class="p-2 bg-base-300 rounded">Mobile: 480px</div>
                  <div class="p-2 bg-base-300 rounded">Tablet: 768px</div>
                  <div class="p-2 bg-base-300 rounded">Desktop: 1200px</div>
                </div>
              </div>
              <p class="mt-4 text-sm">
                Forneça imagens no tamanho correto para cada dispositivo para evitar desperdício de largura de banda.
              </p>
            </DaisyCard>
            
            <DaisyCard title="Art Direction">
              <div class="p-4 bg-base-200 rounded-box">
                <div class="space-y-2">
                  <div class="p-2 bg-base-300 rounded">Mobile: Recorte focado</div>
                  <div class="p-2 bg-base-300 rounded">Desktop: Imagem completa</div>
                </div>
              </div>
              <p class="mt-4 text-sm">
                Use art direction para mostrar diferentes recortes da imagem em diferentes dispositivos.
              </p>
            </DaisyCard>
          </div>
        </div>
        
        <!-- Tabelas -->
        <div slot="tables" class="py-4">
          <h2 class="text-2xl font-bold mb-4">Tabelas Responsivas</h2>
          
          <div class="grid grid-cols-1 gap-6 mb-8">
            <DaisyCard title="Tabela com Scroll Horizontal">
              <ResponsiveTable
                columns={tableColumns}
                data={tableData}
                responsive="scroll"
                striped
                hoverable
              />
              <p class="mt-4 text-sm">
                Tabela com scroll horizontal em dispositivos móveis. Mantém a estrutura da tabela,
                mas permite rolar horizontalmente para ver todas as colunas.
              </p>
            </DaisyCard>
            
            <DaisyCard title="Tabela Reflow">
              <ResponsiveTable
                columns={tableColumns}
                data={tableData}
                responsive="reflow"
                striped
                hoverable
              />
              <p class="mt-4 text-sm">
                Tabela que se transforma em cards em dispositivos móveis. Cada linha se torna um card
                com os rótulos das colunas exibidos antes de cada valor.
              </p>
            </DaisyCard>
            
            <DaisyCard title="Tabela Stack">
              <ResponsiveTable
                columns={tableColumns}
                data={tableData}
                responsive="stack"
                striped
                hoverable
              />
              <p class="mt-4 text-sm">
                Tabela que empilha as células em dispositivos móveis. Cada célula é exibida em uma linha
                separada com o rótulo da coluna.
              </p>
            </DaisyCard>
            
            <DaisyCard title="Tabela com Colunas Prioritárias">
              <ResponsiveTable
                columns={tableColumns}
                data={tableData}
                responsive="collapse"
                striped
                hoverable
              />
              <p class="mt-4 text-sm">
                Tabela que oculta colunas menos importantes em dispositivos móveis. Apenas as colunas
                com alta prioridade são exibidas.
              </p>
            </DaisyCard>
          </div>
        </div>
        
        <!-- Padrões -->
        <div slot="patterns" class="py-4">
          <h2 class="text-2xl font-bold mb-4">Padrões de Design Mobile</h2>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            <DaisyCard title="Bottom Navigation">
              <div class="border rounded-box p-4 bg-base-200 relative h-64">
                <div class="absolute bottom-0 left-0 right-0 btm-nav">
                  <button class="active">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" /></svg>
                    <span class="btm-nav-label">Home</span>
                  </button>
                  <button>
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
                    <span class="btm-nav-label">Sobre</span>
                  </button>
                  <button>
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" /></svg>
                    <span class="btm-nav-label">Estatísticas</span>
                  </button>
                </div>
              </div>
              <p class="mt-4 text-sm">
                Navegação inferior que facilita o acesso a seções principais em dispositivos móveis.
                Ideal para aplicativos com 3-5 seções principais.
              </p>
            </DaisyCard>
            
            <DaisyCard title="Pull to Refresh">
              <div class="border rounded-box p-4 bg-base-200 h-64 overflow-hidden relative">
                <div class="absolute top-0 left-0 right-0 p-2 text-center text-sm bg-base-300">
                  Puxe para baixo para atualizar
                </div>
                <div class="mt-8 space-y-2">
                  <div class="p-2 bg-base-300 rounded">Item 1</div>
                  <div class="p-2 bg-base-300 rounded">Item 2</div>
                  <div class="p-2 bg-base-300 rounded">Item 3</div>
                </div>
              </div>
              <p class="mt-4 text-sm">
                Padrão de pull-to-refresh que permite atualizar o conteúdo puxando a tela para baixo.
                Comum em aplicativos de feed e listas.
              </p>
            </DaisyCard>
            
            <DaisyCard title="Floating Action Button">
              <div class="border rounded-box p-4 bg-base-200 h-64 relative">
                <div class="space-y-2">
                  <div class="p-2 bg-base-300 rounded">Item 1</div>
                  <div class="p-2 bg-base-300 rounded">Item 2</div>
                  <div class="p-2 bg-base-300 rounded">Item 3</div>
                </div>
                <button class="btn btn-circle btn-primary absolute bottom-4 right-4">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" /></svg>
                </button>
              </div>
              <p class="mt-4 text-sm">
                Botão de ação flutuante que destaca a ação principal em uma tela.
                Geralmente usado para ações de criação ou adição.
              </p>
            </DaisyCard>
            
            <DaisyCard title="Swipe Actions">
              <div class="border rounded-box p-4 bg-base-200 h-64">
                <div class="space-y-2">
                  <div class="p-4 bg-base-300 rounded flex items-center justify-between">
                    <span>Deslize para ações</span>
                    <span class="text-sm opacity-70">←→</span>
                  </div>
                  <div class="p-4 bg-base-300 rounded flex items-center justify-between">
                    <span>Deslize para excluir</span>
                    <span class="text-sm opacity-70">←</span>
                  </div>
                  <div class="p-4 bg-base-300 rounded flex items-center justify-between">
                    <span>Deslize para arquivar</span>
                    <span class="text-sm opacity-70">→</span>
                  </div>
                </div>
              </div>
              <p class="mt-4 text-sm">
                Ações de deslize que permitem acessar funcionalidades adicionais deslizando um item para a esquerda ou direita.
                Comum em listas de emails, mensagens e tarefas.
              </p>
            </DaisyCard>
          </div>
          
          <h3 class="text-xl font-bold mb-4">Otimizações de Performance</h3>
          
          <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <DaisyCard title="Lazy Loading">
              <div class="p-4 bg-base-200 rounded-box">
                <div class="space-y-2">
                  <div class="p-2 bg-base-300 rounded">Imagens</div>
                  <div class="p-2 bg-base-300 rounded">Componentes</div>
                  <div class="p-2 bg-base-300 rounded">Dados</div>
                </div>
              </div>
              <p class="mt-4 text-sm">
                Carregue recursos apenas quando necessário para melhorar o tempo de carregamento inicial.
              </p>
            </DaisyCard>
            
            <DaisyCard title="Paginação Infinita">
              <div class="p-4 bg-base-200 rounded-box">
                <div class="space-y-2">
                  <div class="p-2 bg-base-300 rounded">Carrega mais itens ao rolar</div>
                  <div class="p-2 bg-base-300 rounded">Evita sobrecarga inicial</div>
                </div>
              </div>
              <p class="mt-4 text-sm">
                Use paginação infinita para carregar dados gradualmente à medida que o usuário rola a página.
              </p>
            </DaisyCard>
            
            <DaisyCard title="Otimização de Recursos">
              <div class="p-4 bg-base-200 rounded-box">
                <div class="space-y-2">
                  <div class="p-2 bg-base-300 rounded">Minificação de CSS/JS</div>
                  <div class="p-2 bg-base-300 rounded">Compressão de imagens</div>
                  <div class="p-2 bg-base-300 rounded">Cache de recursos</div>
                </div>
              </div>
              <p class="mt-4 text-sm">
                Otimize recursos para reduzir o tamanho dos arquivos e melhorar o tempo de carregamento.
              </p>
            </DaisyCard>
          </div>
        </div>
      </Tabs>
      
      <div class="mt-12 p-6 bg-base-200 rounded-box">
        <h2 class="text-2xl font-bold mb-4">Importação</h2>
        
        <p class="mb-4">
          Para usar os componentes responsivos, importe-os dos módulos correspondentes:
        </p>
        
        <pre class="bg-base-300 p-4 rounded-box overflow-x-auto text-sm"><code>// Navegação mobile
import MobileMenu from '../components/ui/navigation/MobileMenu.astro';

// Imagens responsivas
import ResponsiveImage from '../components/ui/media/ResponsiveImage.astro';

// Tabelas responsivas
import ResponsiveTable from '../components/ui/data/ResponsiveTable.astro';</code></pre>
      </div>
    </Section>
  </Container>
</BaseLayout>
