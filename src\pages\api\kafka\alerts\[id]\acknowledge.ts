import { kafkaAlertService } from '@services/kafka-alerts.service';
import type { APIRoute } from 'astro';

export const POST: APIRoute = async ({ params, request }) => {
  try {
    const alertId = params.id;

    if (!alertId) {
      return new Response(
        JSON.stringify({
          success: false,
          message: 'ID do alerta não fornecido',
        }),
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    const body = await request.json();
    const { acknowledgedBy } = body;

    if (!acknowledgedBy) {
      return new Response(
        JSON.stringify({
          success: false,
          message: 'Identificação de quem reconheceu o alerta não fornecida',
        }),
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    // Reconhecer alerta
    await kafkaAlertService.acknowledgeAlert(alertId, acknowledgedBy);

    return new Response(
      JSON.stringify({
        success: true,
        message: 'Alerta reconhecido com sucesso',
      }),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  } catch (error) {
    console.error('Erro ao reconhecer alerta:', error);

    return new Response(
      JSON.stringify({
        success: false,
        message: error.message || 'Erro ao reconhecer alerta',
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  }
};
