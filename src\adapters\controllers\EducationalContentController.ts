/**
 * Controlador para conteúdo educacional
 *
 * Este controlador é responsável por lidar com as requisições relacionadas a conteúdos educacionais.
 * Ele converte os dados de entrada, chama os casos de uso apropriados e formata a resposta.
 */

import { ContentNotFoundError } from '../../application/errors/ContentNotFoundError';
import { Logger } from '../../application/interfaces/services/Logger';
import { GetEducationalContentUseCase } from '../../application/usecases/content/GetEducationalContentUseCase';
import { type UnknownRecord } from '../../types/common';

/**
 * Resposta de conteúdo educacional
 */
export interface ContentResponse {
  success: boolean;
  data?: UnknownRecord | UnknownRecord[];
  error?: string;
}

/**
 * Controlador para conteúdo educacional
 */
export class EducationalContentController {
  /**
   * Cria uma nova instância do controlador
   *
   * @param getContentUseCase Caso de uso para obter conteúdo educacional
   * @param logger Serviço de logging
   */
  constructor(
    private readonly getContentUseCase: GetEducationalContentUseCase,
    private readonly logger: Logger
  ) {}

  /**
   * Obtém um conteúdo educacional pelo ID
   *
   * @param id ID do conteúdo educacional
   * @returns Resposta com o conteúdo educacional
   */
  async getContent(id: string): Promise<ContentResponse> {
    try {
      this.logger.info(`Controlador: Obtendo conteúdo educacional com ID ${id}`);

      // Validar ID
      if (!id || id.trim() === '') {
        this.logger.warn('Controlador: ID de conteúdo inválido');
        return {
          success: false,
          error: 'ID de conteúdo inválido',
        };
      }

      // Chamar caso de uso
      const content = await this.getContentUseCase.execute(id);

      // Formatar resposta
      return {
        success: true,
        data: {
          id: content.id,
          title: content.title,
          description: content.description,
          type: content.type,
          content: content.content,
          authorId: content.authorId,
          tags: content.tags,
          difficulty: content.difficulty,
          ageRange: content.ageRange,
          duration: content.duration,
          createdAt: content.createdAt,
          updatedAt: content.updatedAt,
          published: content.published,
          formattedDuration: content.formatDuration(),
        },
      };
    } catch (error) {
      // Tratar erros específicos
      if (error instanceof ContentNotFoundError) {
        this.logger.warn(`Controlador: Conteúdo não encontrado com ID ${id}`);
        return {
          success: false,
          error: `Conteúdo não encontrado com ID ${id}`,
        };
      }

      // Tratar erros genéricos
      this.logger.error('Controlador: Erro ao obter conteúdo educacional', error as Error);
      return {
        success: false,
        error: 'Erro ao obter conteúdo educacional',
      };
    }
  }

  /**
   * Obtém conteúdos educacionais por faixa etária
   *
   * @param age Idade para filtro
   * @param limit Limite de conteúdos a serem retornados
   * @returns Resposta com os conteúdos educacionais
   */
  async getContentsByAge(age: number, limit = 10): Promise<ContentResponse> {
    try {
      this.logger.info(`Controlador: Obtendo conteúdos educacionais para idade ${age}`);

      // Validar idade
      if (age < 0 || age > 18) {
        this.logger.warn(`Controlador: Idade inválida: ${age}`);
        return {
          success: false,
          error: 'Idade inválida. Deve estar entre 0 e 18 anos.',
        };
      }

      // Chamar caso de uso
      const contents = await this.getContentUseCase.getContentsByAge(age, limit);

      // Formatar resposta
      return {
        success: true,
        data: contents.map((content) => ({
          id: content.id,
          title: content.title,
          description: content.description,
          type: content.type,
          authorId: content.authorId,
          tags: content.tags,
          difficulty: content.difficulty,
          ageRange: content.ageRange,
          duration: content.duration,
          published: content.published,
          formattedDuration: content.formatDuration(),
        })),
      };
    } catch (error) {
      // Tratar erros
      this.logger.error('Controlador: Erro ao obter conteúdos por idade', error as Error);
      return {
        success: false,
        error: 'Erro ao obter conteúdos educacionais por idade',
      };
    }
  }

  /**
   * Obtém conteúdos educacionais populares
   *
   * @param limit Limite de conteúdos a serem retornados
   * @returns Resposta com os conteúdos educacionais
   */
  async getPopularContents(limit = 6): Promise<ContentResponse> {
    try {
      this.logger.info(`Controlador: Obtendo conteúdos educacionais populares (limite: ${limit})`);

      // Chamar caso de uso
      const contents = await this.getContentUseCase.getPopularContents(limit);

      // Formatar resposta
      return {
        success: true,
        data: contents.map((content) => ({
          id: content.id,
          title: content.title,
          description: content.description,
          type: content.type,
          authorId: content.authorId,
          tags: content.tags,
          difficulty: content.difficulty,
          ageRange: content.ageRange,
          duration: content.duration,
          published: content.published,
          formattedDuration: content.formatDuration(),
        })),
      };
    } catch (error) {
      // Tratar erros
      this.logger.error('Controlador: Erro ao obter conteúdos populares', error as Error);
      return {
        success: false,
        error: 'Erro ao obter conteúdos educacionais populares',
      };
    }
  }
}
