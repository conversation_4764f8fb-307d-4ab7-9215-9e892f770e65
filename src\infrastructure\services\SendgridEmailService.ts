import { EmailService } from '../../application/interfaces/services/EmailService';
import { Logger } from '../../domain/interfaces/Logger';

/**
 * Implementation of EmailService using Sendgrid
 */
export class SendgridEmailService implements EmailService {
  private readonly apiKey: string;
  private readonly fromEmail: string;
  private readonly logger: Logger;

  /**
   * Creates a new SendgridEmailService
   * @param apiKey Sendgrid API key
   * @param fromEmail Default sender email address
   * @param logger Logger instance
   */
  constructor(apiKey: string, fromEmail: string, logger: Logger) {
    this.apiKey = apiKey;
    this.fromEmail = fromEmail;
    this.logger = logger;
  }

  /**
   * Sends an email using Sendgrid
   * @param to Recipient email address
   * @param subject Email subject
   * @param body Email body content
   * @param isHtml Whether the body is HTML (default: false)
   * @returns Promise that resolves when email is sent
   */
  async sendEmail(to: string, subject: string, body: string, isHtml = false): Promise<void> {
    try {
      this.logger.info(`Sending email to ${to} with subject "${subject}"`);

      // In a real implementation, this would use the Sendgrid SDK
      // For example:
      // const sgMail = require('@sendgrid/mail');
      // sgMail.setApiKey(this.apiKey);
      // await sgMail.send({
      //   to,
      //   from: this.fromEmail,
      //   subject,
      //   [isHtml ? 'html' : 'text']: body
      // });

      // Mock implementation for demonstration
      console.log(`Email sent to ${to} with subject "${subject}"`);

      this.logger.info(`Email sent successfully to ${to}`);
    } catch (error) {
      this.logger.error(`Failed to send email to ${to}`, error);
      throw new Error(`Failed to send email: ${error.message}`);
    }
  }

  /**
   * Sends a welcome email to a new user
   * @param to Recipient email address
   * @param name User's name
   * @returns Promise that resolves when email is sent
   */
  async sendWelcomeEmail(to: string, name: string): Promise<void> {
    const subject = 'Welcome to Estação da Alfabetização';
    const body = `
      <h1>Welcome to Estação da Alfabetização, ${name}!</h1>
      <p>Thank you for joining our platform. We're excited to have you on board.</p>
      <p>Get started by exploring our educational content and resources.</p>
      <p>If you have any questions, feel free to contact our support team.</p>
    `;

    await this.sendEmail(to, subject, body, true);
  }

  /**
   * Sends a password reset email
   * @param to Recipient email address
   * @param resetToken Password reset token
   * @returns Promise that resolves when email is sent
   */
  async sendPasswordResetEmail(to: string, resetToken: string): Promise<void> {
    const subject = 'Password Reset Request';
    const resetUrl = `https://estacao-alfabetizacao.com/reset-password?token=${resetToken}`;
    const body = `
      <h1>Password Reset Request</h1>
      <p>You requested a password reset. Click the link below to reset your password:</p>
      <p><a href="${resetUrl}">Reset Password</a></p>
      <p>If you didn't request this, please ignore this email.</p>
      <p>This link will expire in 1 hour.</p>
    `;

    await this.sendEmail(to, subject, body, true);
  }
}
