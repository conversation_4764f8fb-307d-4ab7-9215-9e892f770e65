/**
 * Type guards para validação de tipos em runtime
 * Fornece validação segura de tipos para prevenir erros em produção
 */

import {
  type JWTPayload,
  Permission,
  type SafeUserData,
  type UserData,
  UserRole,
  type UserSession,
} from '@types/auth';
import {
  PaymentMethod,
  PaymentProvider,
  type PaymentResponse,
  PaymentStatus,
  type PaymentTransaction,
  type PaymentWebhook,
} from '@types/payment';

// Type guards para autenticação
export function isUserData(obj: unknown): obj is UserData {
  return (
    typeof obj === 'object' &&
    obj !== null &&
    'ulid_user' in obj &&
    'email' in obj &&
    'name' in obj &&
    typeof (obj as UserData).ulid_user === 'string' &&
    typeof (obj as UserData).email === 'string' &&
    typeof (obj as UserData).name === 'string'
  );
}

export function isSafeUserData(obj: unknown): obj is SafeUserData {
  return isUserData(obj) && !('password' in obj);
}

export function isUserSession(obj: unknown): obj is UserSession {
  return (
    typeof obj === 'object' &&
    obj !== null &&
    'userId' in obj &&
    'sessionId' in obj &&
    'email' in obj &&
    'role' in obj &&
    typeof (obj as UserSession).userId === 'string' &&
    typeof (obj as UserSession).sessionId === 'string' &&
    typeof (obj as UserSession).email === 'string' &&
    Object.values(UserRole).includes((obj as UserSession).role)
  );
}

export function isJWTPayload(obj: unknown): obj is JWTPayload {
  return (
    typeof obj === 'object' &&
    obj !== null &&
    'sub' in obj &&
    'email' in obj &&
    'role' in obj &&
    'permissions' in obj &&
    typeof (obj as JWTPayload).sub === 'string' &&
    typeof (obj as JWTPayload).email === 'string' &&
    Object.values(UserRole).includes((obj as JWTPayload).role) &&
    Array.isArray((obj as JWTPayload).permissions)
  );
}

export function isValidUserRole(role: unknown): role is UserRole {
  return typeof role === 'string' && Object.values(UserRole).includes(role as UserRole);
}

export function isValidPermission(permission: unknown): permission is Permission {
  return (
    typeof permission === 'string' && Object.values(Permission).includes(permission as Permission)
  );
}

// Type guards para pagamentos
export function isPaymentTransaction(obj: unknown): obj is PaymentTransaction {
  return (
    typeof obj === 'object' &&
    obj !== null &&
    'id' in obj &&
    'orderId' in obj &&
    'userId' in obj &&
    'method' in obj &&
    'status' in obj &&
    'amount' in obj &&
    typeof (obj as PaymentTransaction).id === 'string' &&
    typeof (obj as PaymentTransaction).orderId === 'string' &&
    typeof (obj as PaymentTransaction).userId === 'string' &&
    Object.values(PaymentMethod).includes((obj as PaymentTransaction).method) &&
    Object.values(PaymentStatus).includes((obj as PaymentTransaction).status)
  );
}

export function isPaymentResponse(obj: unknown): obj is PaymentResponse {
  return (
    typeof obj === 'object' &&
    obj !== null &&
    'success' in obj &&
    'transactionId' in obj &&
    'status' in obj &&
    typeof (obj as PaymentResponse).success === 'boolean' &&
    typeof (obj as PaymentResponse).transactionId === 'string' &&
    Object.values(PaymentStatus).includes((obj as PaymentResponse).status)
  );
}

export function isPaymentWebhook(obj: unknown): obj is PaymentWebhook {
  return (
    typeof obj === 'object' &&
    obj !== null &&
    'event' in obj &&
    'transactionId' in obj &&
    'signature' in obj &&
    typeof (obj as PaymentWebhook).transactionId === 'string' &&
    typeof (obj as PaymentWebhook).signature === 'string'
  );
}

export function isValidPaymentMethod(method: unknown): method is PaymentMethod {
  return (
    typeof method === 'string' && Object.values(PaymentMethod).includes(method as PaymentMethod)
  );
}

export function isValidPaymentStatus(status: unknown): status is PaymentStatus {
  return (
    typeof status === 'string' && Object.values(PaymentStatus).includes(status as PaymentStatus)
  );
}

export function isValidPaymentProvider(provider: unknown): provider is PaymentProvider {
  return (
    typeof provider === 'string' &&
    Object.values(PaymentProvider).includes(provider as PaymentProvider)
  );
}

// Type guards para dados gerais
export function isString(value: unknown): value is string {
  return typeof value === 'string';
}

export function isNumber(value: unknown): value is number {
  return typeof value === 'number' && !Number.isNaN(value);
}

export function isPositiveNumber(value: unknown): value is number {
  return isNumber(value) && value > 0;
}

export function isBoolean(value: unknown): value is boolean {
  return typeof value === 'boolean';
}

export function isDate(value: unknown): value is Date {
  return value instanceof Date && !Number.isNaN(value.getTime());
}

export function isValidEmail(email: unknown): email is string {
  if (!isString(email)) return false;
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

export function isValidULID(ulid: unknown): ulid is string {
  if (!isString(ulid)) return false;
  // ULID tem 26 caracteres e usa caracteres específicos
  const ulidRegex = /^[0123456789ABCDEFGHJKMNPQRSTVWXYZ]{26}$/;
  return ulidRegex.test(ulid);
}

export function isValidNanoId(nanoid: unknown): nanoid is string {
  if (!isString(nanoid)) return false;
  // NanoID padrão tem 21 caracteres
  return nanoid.length === 21 && /^[A-Za-z0-9_-]+$/.test(nanoid);
}

// Type guards para objetos de request/response
export function hasRequiredFields<T extends Record<string, unknown>>(
  obj: unknown,
  fields: (keyof T)[]
): obj is T {
  if (typeof obj !== 'object' || obj === null) return false;

  return fields.every((field) => field in obj);
}

export function isValidRequestBody(body: unknown): body is Record<string, unknown> {
  return typeof body === 'object' && body !== null && !Array.isArray(body);
}

// Utilitários de validação
export function assertIsUserData(obj: unknown): asserts obj is UserData {
  if (!isUserData(obj)) {
    throw new Error('Invalid UserData object');
  }
}

export function assertIsPaymentTransaction(obj: unknown): asserts obj is PaymentTransaction {
  if (!isPaymentTransaction(obj)) {
    throw new Error('Invalid PaymentTransaction object');
  }
}

export function assertIsValidEmail(email: unknown): asserts email is string {
  if (!isValidEmail(email)) {
    throw new Error('Invalid email format');
  }
}

export function assertIsPositiveNumber(value: unknown): asserts value is number {
  if (!isPositiveNumber(value)) {
    throw new Error('Value must be a positive number');
  }
}

// Validadores compostos
export function validateUserInput(input: unknown): {
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = [];

  if (!isValidRequestBody(input)) {
    errors.push('Request body must be a valid object');
    return { isValid: false, errors };
  }

  if ('email' in input && !isValidEmail(input.email)) {
    errors.push('Invalid email format');
  }

  if ('ulid_user' in input && !isValidULID(input.ulid_user)) {
    errors.push('Invalid ULID format');
  }

  if ('role' in input && !isValidUserRole(input.role)) {
    errors.push('Invalid user role');
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}

export function validatePaymentInput(input: unknown): {
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = [];

  if (!isValidRequestBody(input)) {
    errors.push('Request body must be a valid object');
    return { isValid: false, errors };
  }

  if ('amount' in input && !isPositiveNumber(input.amount)) {
    errors.push('Amount must be a positive number');
  }

  if ('method' in input && !isValidPaymentMethod(input.method)) {
    errors.push('Invalid payment method');
  }

  if ('status' in input && !isValidPaymentStatus(input.status)) {
    errors.push('Invalid payment status');
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}

// Middleware helper para validação de tipos
export function createTypeValidationMiddleware<T>(
  validator: (obj: unknown) => obj is T,
  errorMessage = 'Invalid request data'
) {
  return (data: unknown): T => {
    if (!validator(data)) {
      throw new Error(errorMessage);
    }
    return data;
  };
}

// Validadores específicos para endpoints
export const validateLoginRequest = createTypeValidationMiddleware(
  (obj: unknown): obj is { email: string; password: string } => {
    return (
      isValidRequestBody(obj) &&
      'email' in obj &&
      'password' in obj &&
      isValidEmail(obj.email) &&
      isString(obj.password)
    );
  },
  'Invalid login request data'
);

export const validatePaymentRequest = createTypeValidationMiddleware(
  (obj: unknown): obj is { amount: number; method: PaymentMethod } => {
    return (
      isValidRequestBody(obj) &&
      'amount' in obj &&
      'method' in obj &&
      isPositiveNumber(obj.amount) &&
      isValidPaymentMethod(obj.method)
    );
  },
  'Invalid payment request data'
);
