# Sistema de Monitoramento

## Visão Geral

Este documento descreve a arquitetura e implementação do sistema de monitoramento da plataforma Estação da Alfabetização. O sistema coleta, armazena e analisa métricas de performance, saúde e uso de recursos, permitindo a detecção proativa de problemas e a otimização contínua da plataforma.

## Arquitetura do Sistema de Monitoramento

O sistema de monitoramento é composto por vários componentes que trabalham em conjunto para fornecer uma visão abrangente do estado da aplicação:

### Componentes Principais

1. **Serviço de Monitoramento da Aplicação**: Componente central que coleta e gerencia métricas de todos os subsistemas.
2. **Middleware de Monitoramento**: Intercepta requisições HTTP para coletar métricas de performance e uso.
3. **Monitoramento de Banco de Dados**: Intercepta consultas SQL para coletar métricas de performance e uso.
4. **Monitoramento de Cache**: Intercepta operações de cache para coletar métricas de performance e uso.
5. **Dashboard de Monitoramento**: Interface web para visualização de métricas e alertas.
6. **Sistema de Alertas**: Detecta condições anômalas e notifica administradores.

### Fluxo de Dados

```
┌─────────────┐     ┌─────────────┐     ┌─────────────┐
│  Requisição │     │   Banco de  │     │    Cache    │
│     HTTP    │     │    Dados    │     │             │
└──────┬──────┘     └──────┬──────┘     └──────┬──────┘
       │                   │                   │
       ▼                   ▼                   ▼
┌─────────────┐     ┌─────────────┐     ┌─────────────┐
│ Middleware  │     │ DB Monitoring│     │ Cache Monitoring│
│ Monitoramento│     │   Helper    │     │   Helper    │
└──────┬──────┘     └──────┬──────┘     └──────┬──────┘
       │                   │                   │
       └───────────┬───────┴───────────┬──────┘
                   │                   │
                   ▼                   ▼
          ┌─────────────────────────────────┐
          │                                 │
          │  Serviço de Monitoramento       │
          │                                 │
          └─────────────┬───────────────────┘
                        │
                        ▼
          ┌─────────────────────────────────┐
          │                                 │
          │  Dashboard de Monitoramento     │
          │                                 │
          └─────────────────────────────────┘
```

## Métricas Coletadas

O sistema coleta diversos tipos de métricas para fornecer uma visão abrangente do estado da aplicação:

### Métricas de Sistema

- **CPU_USAGE**: Uso de CPU em porcentagem
- **MEMORY_USAGE**: Uso de memória em porcentagem
- **HEAP_USAGE**: Uso de heap do Node.js em porcentagem
- **ACTIVE_HANDLES**: Número de handles ativos no Node.js
- **ACTIVE_REQUESTS**: Número de requisições ativas no Node.js
- **EVENT_LOOP_LAG**: Latência do event loop em milissegundos

### Métricas de Aplicação

- **REQUEST_COUNT**: Número total de requisições
- **REQUEST_DURATION**: Duração média das requisições em milissegundos
- **REQUEST_ERROR_COUNT**: Número de requisições com erro
- **REQUEST_SUCCESS_RATE**: Taxa de sucesso das requisições em porcentagem

### Métricas de Banco de Dados

- **DB_QUERY_COUNT**: Número total de consultas
- **DB_QUERY_DURATION**: Duração média das consultas em milissegundos
- **DB_ERROR_COUNT**: Número de consultas com erro
- **DB_CONNECTION_COUNT**: Número de conexões ativas

### Métricas de Cache

- **CACHE_HIT_COUNT**: Número de acertos no cache
- **CACHE_MISS_COUNT**: Número de falhas no cache
- **CACHE_HIT_RATE**: Taxa de acertos no cache em porcentagem
- **CACHE_SIZE**: Tamanho do cache em número de chaves

### Métricas de Kafka

- **KAFKA_PRODUCER_COUNT**: Número de mensagens produzidas
- **KAFKA_CONSUMER_COUNT**: Número de mensagens consumidas
- **KAFKA_LAG**: Atraso de consumo em número de mensagens
- **KAFKA_ERROR_COUNT**: Número de erros no Kafka

### Métricas de Negócio

- **ACTIVE_USERS**: Número de usuários ativos
- **TRANSACTION_COUNT**: Número de transações
- **TRANSACTION_VALUE**: Valor total das transações
- **ERROR_RATE**: Taxa de erro geral da aplicação

## Sistema de Alertas

O sistema de alertas detecta condições anômalas e notifica administradores para ação imediata:

### Níveis de Severidade

- **INFO**: Informações gerais, não requerem ação imediata
- **WARNING**: Avisos que podem indicar problemas futuros
- **ERROR**: Erros que requerem atenção
- **CRITICAL**: Problemas críticos que requerem ação imediata

### Configuração de Alertas

```typescript
// Exemplo de configuração de alertas
const alertConfigs = [
  {
    metricType: MetricType.CPU_USAGE,
    threshold: 80,
    operator: 'gt',
    severity: AlertSeverity.WARNING,
    description: 'CPU usage is high',
    enabled: true
  },
  {
    metricType: MetricType.MEMORY_USAGE,
    threshold: 85,
    operator: 'gt',
    severity: AlertSeverity.WARNING,
    description: 'Memory usage is high',
    enabled: true
  }
];
```

### Canais de Notificação

O sistema suporta múltiplos canais de notificação:

- **Email**: Envio de alertas por email para administradores
- **Slack**: Integração com canais do Slack
- **Webhook**: Envio de alertas para endpoints personalizados

## Dashboard de Monitoramento

O dashboard de monitoramento fornece uma interface visual para análise de métricas e alertas:

### Endpoints da API

- **GET /api/monitoring/system**: Estatísticas gerais do sistema
- **GET /api/monitoring/metrics**: Métricas por tipo
- **GET /api/monitoring/alerts**: Alertas ativos
- **GET /api/monitoring/requests**: Estatísticas de requisições
- **GET /api/monitoring/database**: Estatísticas de banco de dados
- **GET /api/monitoring/cache**: Estatísticas de cache
- **GET /api/monitoring/kafka**: Estatísticas do Kafka
- **GET /api/monitoring/business**: Estatísticas de negócio
- **POST /api/monitoring/reset**: Reseta contadores de métricas

### Visualizações

O dashboard inclui diversas visualizações para análise de métricas:

- **Visão Geral**: Resumo do estado geral do sistema
- **Recursos do Sistema**: Gráficos de uso de CPU, memória e disco
- **Performance da Aplicação**: Gráficos de requisições, erros e latência
- **Banco de Dados**: Gráficos de consultas, conexões e performance
- **Cache**: Gráficos de hit rate, tamanho e performance
- **Kafka**: Gráficos de mensagens, lag e erros
- **Negócio**: Gráficos de usuários ativos, transações e métricas de negócio
- **Alertas**: Lista de alertas ativos e histórico

## Implementação

### Serviço de Monitoramento da Aplicação

O serviço central de monitoramento é implementado em `applicationMonitoringService.ts`:

```typescript
// Exemplo de uso do serviço de monitoramento
applicationMonitoringService.recordMetric(
  MetricType.REQUEST_DURATION,
  duration,
  { path: '/api/users', method: 'GET' }
);
```

### Middleware de Monitoramento

O middleware de monitoramento intercepta requisições HTTP:

```typescript
// Exemplo de uso do middleware de monitoramento
app.use(monitoringMiddleware);
app.use(errorMonitoringMiddleware);
```

### Monitoramento de Banco de Dados

O helper de monitoramento de banco de dados intercepta consultas SQL:

```typescript
// Exemplo de uso do helper de monitoramento de banco de dados
const monitoredPool = monitorPool(pool);
```

### Monitoramento de Cache

O helper de monitoramento de cache intercepta operações de cache:

```typescript
// Exemplo de uso do helper de monitoramento de cache
const monitoredCacheService = monitorCacheService(cacheService);
```

## Integração com Ferramentas Externas

O sistema de monitoramento pode ser integrado com ferramentas externas para análise mais avançada:

### Prometheus

Exportação de métricas no formato Prometheus:

```
# HELP cpu_usage CPU usage in percentage
# TYPE cpu_usage gauge
cpu_usage 45.2
```

### Grafana

Visualização de métricas em dashboards Grafana:

```
- name: Estação da Alfabetização
  type: prometheus
  url: http://localhost:9090
  access: proxy
```

### ELK Stack

Integração com Elasticsearch, Logstash e Kibana para análise de logs:

```
output {
  elasticsearch {
    hosts => ["elasticsearch:9200"]
    index => "estacao-logs-%{+YYYY.MM.dd}"
  }
}
```

## Boas Práticas

### Coleta de Métricas

- **Seletividade**: Coletar apenas métricas relevantes
- **Amostragem**: Usar amostragem para métricas de alto volume
- **Agregação**: Agregar métricas para reduzir volume de dados
- **Rotulagem**: Usar tags/labels para segmentar métricas

### Alertas

- **Thresholds Adequados**: Definir limites que evitem falsos positivos
- **Priorização**: Priorizar alertas por severidade e impacto
- **Contexto**: Fornecer contexto suficiente para diagnóstico
- **Acionabilidade**: Garantir que alertas sejam acionáveis

### Performance

- **Impacto Mínimo**: Minimizar o impacto do monitoramento na aplicação
- **Armazenamento Eficiente**: Usar técnicas de compressão e retenção
- **Escalabilidade**: Projetar para escalar com o crescimento da aplicação

## Exemplos de Uso

### Monitoramento de Requisições

```typescript
// Registrar início de requisição
monitoringEvents.emit('request_start', {
  path: req.path,
  method: req.method
});

// Registrar fim de requisição
monitoringEvents.emit('request_end', {
  path: req.path,
  method: req.method,
  duration: performance.now() - startTime,
  statusCode: res.statusCode
});
```

### Monitoramento de Consultas

```typescript
// Registrar início de consulta
monitoringEvents.emit('db_query_start', {
  query: 'SELECT * FROM users'
});

// Registrar fim de consulta
monitoringEvents.emit('db_query_end', {
  query: 'SELECT * FROM users',
  duration: performance.now() - startTime,
  rowCount: result.rowCount
});
```

### Monitoramento de Cache

```typescript
// Registrar acerto no cache
monitoringEvents.emit('cache_hit', {
  key: 'user:123'
});

// Registrar falha no cache
monitoringEvents.emit('cache_miss', {
  key: 'user:123'
});
```

## Referências

- [Node.js Monitoring](https://nodejs.org/en/docs/guides/diagnostics-flamegraph/)
- [Express.js Performance Monitoring](https://expressjs.com/en/advanced/best-practice-performance.html)
- [PostgreSQL Monitoring](https://www.postgresql.org/docs/current/monitoring.html)
- [Redis Monitoring](https://redis.io/topics/monitoring)
- [Kafka Monitoring](https://kafka.apache.org/documentation/#monitoring)
