/**
 * Serviço de dashboard para o Kafka
 * 
 * Este serviço implementa funcionalidades para o dashboard do Kafka,
 * incluindo métricas, estatísticas e visualizações.
 */

import { v4 as uuidv4 } from 'uuid';
import { kafka } from '../config/kafka';
import { kafkaLoggingService } from './kafka-logging.service';
import { kafkaAlertService } from './kafka-alerts.service';
import { queryHelper } from '../helpers/queryHelper';
import { cacheService } from '../infrastructure/cache/CacheService';

// Interface para métricas do Kafka
export interface KafkaMetrics {
  brokers: {
    count: number;
    status: Array<{
      id: number;
      host: string;
      port: number;
      status: 'online' | 'offline';
    }>;
  };
  topics: {
    count: number;
    partitions: number;
    underReplicatedPartitions: number;
    offlinePartitions: number;
  };
  consumers: {
    count: number;
    groups: Array<{
      groupId: string;
      members: number;
      state: string;
      lag: number;
    }>;
  };
  messages: {
    inRate: number;
    outRate: number;
    totalIn: number;
    totalOut: number;
  };
  alerts: {
    active: number;
    byType: Record<string, number>;
    bySeverity: Record<string, number>;
  };
  system: {
    cpu: number;
    memory: number;
    disk: number;
    network: {
      in: number;
      out: number;
    };
  };
  timestamp: string;
}

// Interface para estatísticas de tópico
export interface TopicStats {
  name: string;
  partitions: number;
  replicationFactor: number;
  messageCount: number;
  messageRate: number;
  sizeBytes: number;
  retentionMs: number;
  cleanupPolicy: string;
  consumers: Array<{
    groupId: string;
    lag: number;
    messageRate: number;
  }>;
}

// Interface para estatísticas de consumidor
export interface ConsumerGroupStats {
  groupId: string;
  state: string;
  members: number;
  topics: Array<{
    topic: string;
    partitions: number;
    lag: number;
    messageRate: number;
  }>;
  totalLag: number;
  avgMessageRate: number;
}

// Classe para o serviço de dashboard do Kafka
class KafkaDashboardService {
  private static instance: KafkaDashboardService;
  private admin = kafka.admin();
  private isConnected = false;
  private metricsCache: Map<string, any> = new Map();
  private refreshTimer: NodeJS.Timeout | null = null;
  private messageRateCounters: Map<string, { count: number, timestamp: number }> = new Map();
  
  /**
   * Construtor privado para implementar Singleton
   */
  private constructor() {
    this.initialize();
  }
  
  /**
   * Obtém a instância única do serviço
   */
  public static getInstance(): KafkaDashboardService {
    if (!KafkaDashboardService.instance) {
      KafkaDashboardService.instance = new KafkaDashboardService();
    }
    return KafkaDashboardService.instance;
  }
  
  /**
   * Inicializa o serviço de dashboard
   */
  private async initialize(): Promise<void> {
    try {
      // Conectar ao admin do Kafka
      await this.admin.connect();
      this.isConnected = true;
      
      // Configurar timer para atualização periódica de métricas
      this.setupRefreshTimer();
      
      // Coletar métricas iniciais
      await this.refreshMetrics();
      
      kafkaLoggingService.info('kafka.dashboard', 'Serviço de dashboard do Kafka inicializado');
    } catch (error) {
      kafkaLoggingService.error('kafka.dashboard', 'Erro ao inicializar serviço de dashboard do Kafka', error);
    }
  }
  
  /**
   * Configura timer para atualização periódica de métricas
   */
  private setupRefreshTimer(): void {
    // Limpar timer existente
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer);
    }
    
    // Configurar novo timer (a cada 30 segundos)
    this.refreshTimer = setInterval(async () => {
      try {
        await this.refreshMetrics();
      } catch (error) {
        kafkaLoggingService.error('kafka.dashboard', 'Erro ao atualizar métricas', error);
      }
    }, 30000);
  }
  
  /**
   * Atualiza métricas do Kafka
   */
  private async refreshMetrics(): Promise<void> {
    if (!this.isConnected) {
      return;
    }
    
    try {
      // Coletar métricas básicas
      const metrics = await this.collectMetrics();
      
      // Armazenar em cache
      await cacheService.set('kafka:metrics', metrics, 60); // 1 minuto
      
      // Armazenar histórico de métricas (a cada 5 minutos)
      const now = new Date();
      if (now.getMinutes() % 5 === 0 && now.getSeconds() < 30) {
        await this.storeMetricsHistory(metrics);
      }
    } catch (error) {
      kafkaLoggingService.error('kafka.dashboard', 'Erro ao atualizar métricas', error);
    }
  }
  
  /**
   * Coleta métricas do Kafka
   * @returns Métricas do Kafka
   */
  private async collectMetrics(): Promise<KafkaMetrics> {
    try {
      // Obter metadados do cluster
      const metadata = await this.admin.fetchTopicMetadata();
      
      // Obter grupos de consumidores
      const groups = await this.admin.listGroups();
      
      // Obter descrições de grupos
      const groupDescriptions = await Promise.all(
        groups.groups.map(async group => {
          try {
            return await this.admin.describeGroups([group.groupId]);
          } catch (error) {
            kafkaLoggingService.error('kafka.dashboard', `Erro ao descrever grupo ${group.groupId}`, error);
            return null;
          }
        })
      );
      
      // Filtrar descrições nulas
      const validGroupDescriptions = groupDescriptions.filter(Boolean);
      
      // Calcular métricas de tópicos
      let totalPartitions = 0;
      let underReplicatedPartitions = 0;
      let offlinePartitions = 0;
      
      for (const topic of metadata.topics) {
        totalPartitions += topic.partitions.length;
        
        for (const partition of topic.partitions) {
          if (partition.replicas.length > partition.isr.length) {
            underReplicatedPartitions++;
          }
          
          if (partition.isr.length === 0 || partition.leader < 0) {
            offlinePartitions++;
          }
        }
      }
      
      // Calcular métricas de consumidores
      const consumerGroups = [];
      let totalLag = 0;
      
      for (const group of groups.groups) {
        try {
          // Obter offsets do consumidor
          const consumerOffsets = await this.admin.fetchOffsets({
            groupId: group.groupId,
          });
          
          let groupLag = 0;
          
          for (const { topic, partitions } of consumerOffsets) {
            // Obter offsets mais recentes para o tópico
            const topicOffsets = await this.admin.fetchTopicOffsets(topic);
            
            for (const { partition, offset } of partitions) {
              const latestOffset = topicOffsets.find(
                t => t.partition === partition
              )?.offset || '0';
              
              const consumerOffset = offset;
              const lag = Number(BigInt(latestOffset) - BigInt(consumerOffset));
              
              groupLag += lag;
              totalLag += lag;
            }
          }
          
          // Obter descrição do grupo
          const groupDescription = validGroupDescriptions
            .find(desc => desc?.groups[0]?.groupId === group.groupId);
          
          const members = groupDescription?.groups[0]?.members?.length || 0;
          const state = groupDescription?.groups[0]?.state || 'Unknown';
          
          consumerGroups.push({
            groupId: group.groupId,
            members,
            state,
            lag: groupLag,
          });
        } catch (error) {
          kafkaLoggingService.error('kafka.dashboard', `Erro ao obter métricas para grupo ${group.groupId}`, error);
        }
      }
      
      // Obter alertas ativos
      const activeAlerts = kafkaAlertService.getActiveAlerts();
      
      // Contar alertas por tipo e severidade
      const alertsByType = {};
      const alertsBySeverity = {};
      
      for (const alert of activeAlerts) {
        alertsByType[alert.type] = (alertsByType[alert.type] || 0) + 1;
        alertsBySeverity[alert.severity] = (alertsBySeverity[alert.severity] || 0) + 1;
      }
      
      // Calcular taxas de mensagens (simulado - em um sistema real, usaria métricas JMX)
      const messageRates = this.calculateMessageRates(metadata.topics);
      
      // Construir objeto de métricas
      const metrics: KafkaMetrics = {
        brokers: {
          count: metadata.brokers.length,
          status: metadata.brokers.map(broker => ({
            id: broker.nodeId,
            host: broker.host,
            port: broker.port,
            status: 'online', // Assumimos que estão online se aparecem nos metadados
          })),
        },
        topics: {
          count: metadata.topics.length,
          partitions: totalPartitions,
          underReplicatedPartitions,
          offlinePartitions,
        },
        consumers: {
          count: groups.groups.length,
          groups: consumerGroups,
        },
        messages: {
          inRate: messageRates.inRate,
          outRate: messageRates.outRate,
          totalIn: messageRates.totalIn,
          totalOut: messageRates.totalOut,
        },
        alerts: {
          active: activeAlerts.length,
          byType: alertsByType,
          bySeverity: alertsBySeverity,
        },
        system: {
          cpu: Math.random() * 30 + 10, // Simulado - em um sistema real, usaria métricas do sistema
          memory: Math.random() * 40 + 30,
          disk: Math.random() * 20 + 40,
          network: {
            in: Math.random() * 100 + 50,
            out: Math.random() * 100 + 50,
          },
        },
        timestamp: new Date().toISOString(),
      };
      
      return metrics;
    } catch (error) {
      kafkaLoggingService.error('kafka.dashboard', 'Erro ao coletar métricas', error);
      throw error;
    }
  }
  
  /**
   * Calcula taxas de mensagens (simulado)
   * @param topics - Lista de tópicos
   * @returns Taxas de mensagens
   */
  private calculateMessageRates(topics: any[]): { inRate: number, outRate: number, totalIn: number, totalOut: number } {
    // Em um sistema real, usaria métricas JMX ou contadores reais
    // Esta é uma implementação simulada para fins de demonstração
    
    const now = Date.now();
    let totalInRate = 0;
    let totalOutRate = 0;
    let totalIn = 0;
    let totalOut = 0;
    
    for (const topic of topics) {
      const topicKey = `topic:${topic.name}`;
      
      // Gerar valores simulados
      const inRate = Math.random() * 100 * (topic.partitions.length / 2);
      const outRate = inRate * 0.9; // Ligeiramente menor que a taxa de entrada
      
      // Obter contador anterior
      const prevCounter = this.messageRateCounters.get(topicKey);
      
      if (prevCounter) {
        const timeDiff = (now - prevCounter.timestamp) / 1000; // em segundos
        
        // Acumular contadores
        totalIn += prevCounter.count + (inRate * timeDiff);
        totalOut += (prevCounter.count * 0.9) + (outRate * timeDiff);
      } else {
        // Valores iniciais
        totalIn += inRate * 60; // Simular 1 minuto de mensagens
        totalOut += outRate * 60;
      }
      
      // Atualizar contador
      this.messageRateCounters.set(topicKey, {
        count: totalIn,
        timestamp: now,
      });
      
      // Acumular taxas
      totalInRate += inRate;
      totalOutRate += outRate;
    }
    
    return {
      inRate: Math.round(totalInRate),
      outRate: Math.round(totalOutRate),
      totalIn: Math.round(totalIn),
      totalOut: Math.round(totalOut),
    };
  }
  
  /**
   * Armazena histórico de métricas
   * @param metrics - Métricas a serem armazenadas
   */
  private async storeMetricsHistory(metrics: KafkaMetrics): Promise<void> {
    try {
      // Inserir métricas no banco de dados
      await queryHelper.query(
        `INSERT INTO tab_kafka_metrics_history (
          metric_id,
          broker_count,
          topic_count,
          partition_count,
          under_replicated_partitions,
          offline_partitions,
          consumer_group_count,
          total_lag,
          message_in_rate,
          message_out_rate,
          active_alerts,
          cpu_usage,
          memory_usage,
          disk_usage,
          network_in,
          network_out,
          timestamp,
          created_at
        ) VALUES (
          $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, NOW()
        )`,
        [
          uuidv4(),
          metrics.brokers.count,
          metrics.topics.count,
          metrics.topics.partitions,
          metrics.topics.underReplicatedPartitions,
          metrics.topics.offlinePartitions,
          metrics.consumers.count,
          metrics.consumers.groups.reduce((sum, group) => sum + group.lag, 0),
          metrics.messages.inRate,
          metrics.messages.outRate,
          metrics.alerts.active,
          metrics.system.cpu,
          metrics.system.memory,
          metrics.system.disk,
          metrics.system.network.in,
          metrics.system.network.out,
          metrics.timestamp,
        ]
      );
    } catch (error) {
      kafkaLoggingService.error('kafka.dashboard', 'Erro ao armazenar histórico de métricas', error);
    }
  }
  
  /**
   * Obtém métricas atuais do Kafka
   * @returns Métricas do Kafka
   */
  public async getMetrics(): Promise<KafkaMetrics> {
    try {
      // Tentar obter do cache
      const cachedMetrics = await cacheService.get<KafkaMetrics>('kafka:metrics');
      
      if (cachedMetrics) {
        return cachedMetrics;
      }
      
      // Se não estiver em cache, coletar novamente
      return await this.collectMetrics();
    } catch (error) {
      kafkaLoggingService.error('kafka.dashboard', 'Erro ao obter métricas', error);
      throw error;
    }
  }
  
  /**
   * Obtém estatísticas de um tópico específico
   * @param topicName - Nome do tópico
   * @returns Estatísticas do tópico
   */
  public async getTopicStats(topicName: string): Promise<TopicStats> {
    try {
      // Obter metadados do tópico
      const metadata = await this.admin.fetchTopicMetadata({
        topics: [topicName],
      });
      
      if (!metadata.topics.length) {
        throw new Error(`Tópico ${topicName} não encontrado`);
      }
      
      const topic = metadata.topics[0];
      
      // Obter offsets do tópico
      const topicOffsets = await this.admin.fetchTopicOffsets(topicName);
      
      // Calcular contagem total de mensagens
      let messageCount = 0;
      
      for (const offset of topicOffsets) {
        messageCount += Number(offset.offset);
      }
      
      // Obter grupos de consumidores que consomem este tópico
      const groups = await this.admin.listGroups();
      const topicConsumers = [];
      
      for (const group of groups.groups) {
        try {
          // Obter offsets do consumidor
          const consumerOffsets = await this.admin.fetchOffsets({
            groupId: group.groupId,
          });
          
          // Verificar se o grupo consome este tópico
          const topicOffset = consumerOffsets.find(t => t.topic === topicName);
          
          if (topicOffset) {
            let groupLag = 0;
            
            for (const { partition, offset } of topicOffset.partitions) {
              const latestOffset = topicOffsets.find(
                t => t.partition === partition
              )?.offset || '0';
              
              const consumerOffset = offset;
              const lag = Number(BigInt(latestOffset) - BigInt(consumerOffset));
              
              groupLag += lag;
            }
            
            // Calcular taxa de mensagens (simulado)
            const messageRate = Math.random() * 50 + 10;
            
            topicConsumers.push({
              groupId: group.groupId,
              lag: groupLag,
              messageRate,
            });
          }
        } catch (error) {
          kafkaLoggingService.error('kafka.dashboard', `Erro ao obter métricas para grupo ${group.groupId}`, error);
        }
      }
      
      // Construir estatísticas do tópico
      const stats: TopicStats = {
        name: topicName,
        partitions: topic.partitions.length,
        replicationFactor: topic.partitions[0]?.replicas.length || 0,
        messageCount,
        messageRate: Math.random() * 100 + 50, // Simulado
        sizeBytes: messageCount * 1024, // Simulado (1KB por mensagem)
        retentionMs: *********, // Simulado (7 dias)
        cleanupPolicy: 'delete', // Simulado
        consumers: topicConsumers,
      };
      
      return stats;
    } catch (error) {
      kafkaLoggingService.error('kafka.dashboard', `Erro ao obter estatísticas do tópico ${topicName}`, error);
      throw error;
    }
  }
  
  /**
   * Obtém estatísticas de um grupo de consumidores
   * @param groupId - ID do grupo
   * @returns Estatísticas do grupo
   */
  public async getConsumerGroupStats(groupId: string): Promise<ConsumerGroupStats> {
    try {
      // Obter descrição do grupo
      const groupDescription = await this.admin.describeGroups([groupId]);
      
      if (!groupDescription.groups.length) {
        throw new Error(`Grupo ${groupId} não encontrado`);
      }
      
      const group = groupDescription.groups[0];
      
      // Obter offsets do consumidor
      const consumerOffsets = await this.admin.fetchOffsets({
        groupId,
      });
      
      const topicStats = [];
      let totalLag = 0;
      let totalMessageRate = 0;
      
      for (const { topic, partitions } of consumerOffsets) {
        // Obter offsets mais recentes para o tópico
        const topicOffsets = await this.admin.fetchTopicOffsets(topic);
        
        let topicLag = 0;
        
        for (const { partition, offset } of partitions) {
          const latestOffset = topicOffsets.find(
            t => t.partition === partition
          )?.offset || '0';
          
          const consumerOffset = offset;
          const lag = Number(BigInt(latestOffset) - BigInt(consumerOffset));
          
          topicLag += lag;
        }
        
        // Calcular taxa de mensagens (simulado)
        const messageRate = Math.random() * 50 + 10;
        
        topicStats.push({
          topic,
          partitions: partitions.length,
          lag: topicLag,
          messageRate,
        });
        
        totalLag += topicLag;
        totalMessageRate += messageRate;
      }
      
      // Construir estatísticas do grupo
      const stats: ConsumerGroupStats = {
        groupId,
        state: group.state,
        members: group.members?.length || 0,
        topics: topicStats,
        totalLag,
        avgMessageRate: totalMessageRate / (topicStats.length || 1),
      };
      
      return stats;
    } catch (error) {
      kafkaLoggingService.error('kafka.dashboard', `Erro ao obter estatísticas do grupo ${groupId}`, error);
      throw error;
    }
  }
  
  /**
   * Obtém histórico de métricas
   * @param startTime - Timestamp de início
   * @param endTime - Timestamp de fim
   * @returns Histórico de métricas
   */
  public async getMetricsHistory(
    startTime: string,
    endTime: string
  ): Promise<any[]> {
    try {
      const result = await queryHelper.query(
        `SELECT
          metric_id,
          broker_count,
          topic_count,
          partition_count,
          under_replicated_partitions,
          offline_partitions,
          consumer_group_count,
          total_lag,
          message_in_rate,
          message_out_rate,
          active_alerts,
          cpu_usage,
          memory_usage,
          disk_usage,
          network_in,
          network_out,
          timestamp
        FROM
          tab_kafka_metrics_history
        WHERE
          timestamp BETWEEN $1 AND $2
        ORDER BY
          timestamp ASC`,
        [startTime, endTime]
      );
      
      return result.rows;
    } catch (error) {
      kafkaLoggingService.error('kafka.dashboard', 'Erro ao obter histórico de métricas', error);
      throw error;
    }
  }
}

// Exportar instância única do serviço
export const kafkaDashboardService = KafkaDashboardService.getInstance();
