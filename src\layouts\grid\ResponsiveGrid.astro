---
/**
 * Componente de Grid Responsivo
 *
 * Este componente cria um layout de grid responsivo baseado em CSS Grid.
 */

import GridSystem from './GridSystem';

interface ColumnsConfig {
  base?: number;
  sm?: number;
  md?: number;
  lg?: number;
  xl?: number;
  '2xl'?: number;
}

interface GapConfig {
  base?: number;
  sm?: number;
  md?: number;
  lg?: number;
  xl?: number;
  '2xl'?: number;
}

interface Props {
  cols?: number | ColumnsConfig;
  gap?: number | GapConfig;
  rowGap?: number;
  colGap?: number;
  justify?: 'start' | 'center' | 'end' | 'between' | 'around' | 'evenly';
  items?: 'start' | 'center' | 'end' | 'stretch' | 'baseline';
  flow?: 'row' | 'col' | 'dense' | 'row-dense' | 'col-dense';
  autoRows?: 'auto' | 'min' | 'max' | 'fr';
  autoCols?: 'auto' | 'min' | 'max' | 'fr';
  class?: string;
  id?: string;
}

const {
  cols = 1,
  gap = 4,
  rowGap,
  colGap,
  justify,
  items,
  flow,
  autoRows,
  autoCols,
  class: className = '',
  id,
} = Astro.props;

// Função para gerar classes de colunas
function getColumnsClasses(cols: number | ColumnsConfig): string {
  if (typeof cols === 'number') {
    return GridSystem.columns[cols] || GridSystem.columns[1];
  }

  const classes = [];

  if (cols.base) {
    classes.push(GridSystem.columns[cols.base] || GridSystem.columns[1]);
  }

  Object.entries(cols).forEach(([breakpoint, value]) => {
    if (breakpoint !== 'base' && value && GridSystem.responsiveColumns[breakpoint]) {
      classes.push(GridSystem.responsiveColumns[breakpoint][value] || '');
    }
  });

  return classes.filter(Boolean).join(' ');
}

// Função para gerar classes de gap
function getGapClasses(gap: number | GapConfig, rowGap?: number, colGap?: number): string {
  const classes = [];

  // Se temos rowGap ou colGap específicos
  if (rowGap) {
    classes.push(`row-gap-${rowGap}`);
  }

  if (colGap) {
    classes.push(`col-gap-${colGap}`);
  }

  // Se não temos gaps específicos, usamos o gap geral
  if (!rowGap && !colGap) {
    if (typeof gap === 'number') {
      classes.push(GridSystem.gaps[gap] || GridSystem.gaps[4]);
    } else {
      if (gap.base) {
        classes.push(GridSystem.gaps[gap.base] || GridSystem.gaps[4]);
      }

      Object.entries(gap).forEach(([breakpoint, value]) => {
        if (breakpoint !== 'base' && value && GridSystem.responsiveGaps[breakpoint]) {
          classes.push(GridSystem.responsiveGaps[breakpoint][value] || '');
        }
      });
    }
  }

  return classes.filter(Boolean).join(' ');
}

// Gerar classes para o grid
const columnsClasses = getColumnsClasses(cols);
const gapClasses = getGapClasses(gap, rowGap, colGap);
const justifyClass = justify ? GridSystem.alignment[justify] : '';
const itemsClass = items ? GridSystem.verticalAlignment[items] : '';
const flowClass = flow ? `grid-flow-${flow}` : '';
const autoRowsClass = autoRows ? `auto-rows-${autoRows}` : '';
const autoColsClass = autoCols ? `auto-cols-${autoCols}` : '';

// Combinar todas as classes
const gridClasses = [
  'grid',
  columnsClasses,
  gapClasses,
  justifyClass,
  itemsClass,
  flowClass,
  autoRowsClass,
  autoColsClass,
  className,
]
  .filter(Boolean)
  .join(' ');
---

<div class={gridClasses} id={id}>
  <slot />
</div>
