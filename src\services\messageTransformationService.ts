/**
 * Serviço de transformação de mensagens
 *
 * Este serviço fornece funcionalidades para transformação de mensagens
 * entre diferentes formatos e versões.
 */

import { logger } from '@utils/logger';
import { cacheService } from './cacheService';
import { MessageTransformer } from './messageProcessingService';

/**
 * Tipo para função de transformação
 */
export type TransformFunction<T = Record<string, unknown>, R = Record<string, unknown>> = (
  message: T
) => R;

/**
 * Implementação de transformador de mensagem
 */
export class SimpleMessageTransformer<T = Record<string, unknown>, R = Record<string, unknown>>
  implements MessageTransformer<T, R>
{
  /**
   * Cria um novo transformador de mensagem
   * @param transformFn - Função de transformação
   */
  constructor(private transformFn: TransformFunction<T, R>) {}

  /**
   * Transforma uma mensagem
   * @param message - Mensagem a ser transformada
   * @returns Mensagem transformada
   */
  transform(message: T): R {
    return this.transformFn(message);
  }
}

/**
 * Transformador de mensagem com mapeamento de campos
 */
export class FieldMappingTransformer implements MessageTransformer {
  /**
   * Cria um novo transformador com mapeamento de campos
   * @param fieldMappings - Mapeamento de campos
   */
  constructor(private fieldMappings: Record<string, string>) {}

  /**
   * Transforma uma mensagem
   * @param message - Mensagem a ser transformada
   * @returns Mensagem transformada
   */
  transform(message: Record<string, unknown>): Record<string, unknown> {
    const result: Record<string, unknown> = {};

    // Mapear campos de acordo com o mapeamento
    for (const [sourceField, targetField] of Object.entries(this.fieldMappings)) {
      // Suporte para campos aninhados com notação de ponto
      const sourceValue = this.getNestedValue(message, sourceField);

      if (sourceValue !== undefined) {
        this.setNestedValue(result, targetField, sourceValue);
      }
    }

    // Incluir campos não mapeados
    for (const [key, value] of Object.entries(message)) {
      if (!Object.keys(this.fieldMappings).includes(key) && result[key] === undefined) {
        result[key] = value;
      }
    }

    return result;
  }

  /**
   * Obtém valor aninhado de um objeto
   * @param obj - Objeto
   * @param path - Caminho do valor
   * @returns Valor aninhado
   */
  private getNestedValue(obj: Record<string, unknown>, path: string): unknown {
    const parts = path.split('.');
    let current: any = obj;

    for (const part of parts) {
      if (current === null || current === undefined || typeof current !== 'object') {
        return undefined;
      }

      current = current[part];
    }

    return current;
  }

  /**
   * Define valor aninhado em um objeto
   * @param obj - Objeto
   * @param path - Caminho do valor
   * @param value - Valor a ser definido
   */
  private setNestedValue(obj: Record<string, unknown>, path: string, value: unknown): void {
    const parts = path.split('.');
    let current: any = obj;

    for (let i = 0; i < parts.length - 1; i++) {
      const part = parts[i];

      if (!(part in current) || current[part] === null || typeof current[part] !== 'object') {
        current[part] = {};
      }

      current = current[part];
    }

    current[parts[parts.length - 1]] = value;
  }
}

/**
 * Serviço de transformação de mensagens
 */
export const messageTransformationService = {
  /**
   * Cache de transformadores
   */
  transformers: new Map<string, MessageTransformer>(),

  /**
   * Obtém um transformador para um tipo de mensagem
   * @param transformerName - Nome do transformador
   * @returns Transformador de mensagem
   */
  async getTransformer(transformerName: string): Promise<MessageTransformer | null> {
    try {
      // Verificar cache em memória
      if (this.transformers.has(transformerName)) {
        return this.transformers.get(transformerName)!;
      }

      // Verificar cache distribuído
      const cacheKey = `transformer:${transformerName}`;
      const cachedTransformer = await cacheService.get(cacheKey);

      if (cachedTransformer) {
        const config = JSON.parse(cachedTransformer);
        const transformer = this.createTransformerFromConfig(config);

        if (transformer) {
          this.transformers.set(transformerName, transformer);
          return transformer;
        }
      }

      // Buscar transformador do banco de dados
      const transformerConfig = await this.getTransformerFromDatabase(transformerName);

      if (!transformerConfig) {
        // Verificar transformadores pré-definidos
        const predefinedTransformer = this.getPredefinedTransformer(transformerName);

        if (predefinedTransformer) {
          this.transformers.set(transformerName, predefinedTransformer);
          return predefinedTransformer;
        }

        logger.warn(`Transformador não encontrado: ${transformerName}`);
        return null;
      }

      // Criar transformador
      const transformer = this.createTransformerFromConfig(transformerConfig);

      if (!transformer) {
        logger.warn(`Não foi possível criar transformador: ${transformerName}`);
        return null;
      }

      // Armazenar em cache
      this.transformers.set(transformerName, transformer);
      await cacheService.set(cacheKey, JSON.stringify(transformerConfig), 3600); // 1 hora

      return transformer;
    } catch (error) {
      logger.error(`Erro ao obter transformador ${transformerName}:`, error);
      return null;
    }
  },

  /**
   * Cria um transformador a partir de uma configuração
   * @param config - Configuração do transformador
   * @returns Transformador de mensagem
   */
  createTransformerFromConfig(config: any): MessageTransformer | null {
    try {
      if (config.type === 'field_mapping') {
        return new FieldMappingTransformer(config.mappings);
      }

      return null;
    } catch (error) {
      logger.error('Erro ao criar transformador a partir de configuração:', error);
      return null;
    }
  },

  /**
   * Obtém um transformador do banco de dados
   * @param transformerName - Nome do transformador
   * @returns Configuração do transformador
   */
  async getTransformerFromDatabase(transformerName: string): Promise<any | null> {
    try {
      // Implementação fictícia - substituir pela consulta real ao banco de dados
      // Na implementação real, buscar da tabela tab_message_transformer

      return null;
    } catch (error) {
      logger.error(`Erro ao obter transformador ${transformerName} do banco de dados:`, error);
      return null;
    }
  },

  /**
   * Obtém um transformador pré-definido
   * @param transformerName - Nome do transformador
   * @returns Transformador de mensagem
   */
  getPredefinedTransformer(transformerName: string): MessageTransformer | null {
    // Transformadores pré-definidos para desenvolvimento
    const predefinedTransformers: Record<string, MessageTransformer> = {
      'payment.legacy_to_new': new FieldMappingTransformer({
        id: 'paymentId',
        order_id: 'orderId',
        user_id: 'userId',
        amount: 'value',
        payment_method: 'paymentType',
        payment_status: 'status',
        external_reference: 'externalId',
        additional_data: 'metadata',
      }),
      'order.legacy_to_new': new FieldMappingTransformer({
        id: 'orderId',
        customer_id: 'userId',
        order_total: 'total',
        order_status: 'status',
        products: 'items',
        additional_data: 'metadata',
      }),
      'notification.legacy_to_new': new FieldMappingTransformer({
        id: 'notificationId',
        user_id: 'userId',
        notification_type: 'type',
        title: 'subject',
        recipient: 'recipientEmail',
        body: 'content',
        additional_data: 'metadata',
      }),
    };

    return predefinedTransformers[transformerName] || null;
  },

  /**
   * Registra um novo transformador
   * @param transformerName - Nome do transformador
   * @param transformerConfig - Configuração do transformador
   * @returns ID do transformador registrado
   */
  async registerTransformer(
    transformerName: string,
    transformerConfig: Record<string, unknown>
  ): Promise<string | null> {
    try {
      // Validar configuração
      const transformer = this.createTransformerFromConfig(transformerConfig);

      if (!transformer) {
        logger.error(`Configuração de transformador inválida: ${transformerName}`);
        return null;
      }

      // Implementação fictícia - substituir pela inserção real no banco de dados
      // Na implementação real, inserir na tabela tab_message_transformer

      const transformerId = `transformer-${transformerName}-${Date.now()}`;

      // Limpar caches
      this.transformers.delete(transformerName);
      await cacheService.delete(`transformer:${transformerName}`);

      // Armazenar novo transformador em cache
      const cacheKey = `transformer:${transformerName}`;
      await cacheService.set(cacheKey, JSON.stringify(transformerConfig), 3600); // 1 hora

      return transformerId;
    } catch (error) {
      logger.error(`Erro ao registrar transformador ${transformerName}:`, error);
      return null;
    }
  },

  /**
   * Transforma uma mensagem
   * @param message - Mensagem a ser transformada
   * @param transformerName - Nome do transformador
   * @returns Mensagem transformada
   */
  async transformMessage(
    message: Record<string, unknown>,
    transformerName: string
  ): Promise<Record<string, unknown> | null> {
    try {
      const transformer = await this.getTransformer(transformerName);

      if (!transformer) {
        logger.warn(`Transformador não encontrado: ${transformerName}`);
        return null;
      }

      return transformer.transform(message);
    } catch (error) {
      logger.error(`Erro ao transformar mensagem com transformador ${transformerName}:`, error);
      return null;
    }
  },
};
