# Estratégias de Particionamento Kafka

Este documento descreve as estratégias de particionamento implementadas para os tópicos Kafka na plataforma Estação da Alfabetização.

## Visão Geral

O particionamento é um conceito fundamental no Kafka que determina como as mensagens são distribuídas entre as partições de um tópico. Uma estratégia de particionamento eficiente é crucial para:

1. **Balanceamento de carga**: Distribuir mensagens uniformemente entre partições
2. **Ordenação de mensagens**: Garantir que mensagens relacionadas sejam processadas na ordem correta
3. **Paralelismo**: Permitir processamento paralelo eficiente
4. **Escalabilidade**: Facilitar a escalabilidade horizontal do sistema

## Estratégias Implementadas

Implementamos quatro estratégias principais de particionamento:

### 1. Baseada em Chave (Key-Based)

Esta é nossa estratégia principal, onde a partição é determinada por um hash consistente de um campo específico da mensagem.

**Características**:
- Garante que mensagens com a mesma chave sempre vão para a mesma partição
- Mantém a ordenação de mensagens relacionadas
- Distribui carga de forma relativamente uniforme (dependendo da distribuição das chaves)

**Uso recomendado**:
- Quando a ordenação de mensagens relacionadas é importante
- Para eventos que precisam ser processados sequencialmente por entidade

### 2. Aleatória (Random)

Distribui mensagens aleatoriamente entre as partições disponíveis.

**Características**:
- Distribuição estatisticamente uniforme
- Não garante ordenação para mensagens relacionadas
- Simples de implementar

**Uso recomendado**:
- Quando a ordenação não é importante
- Para distribuição uniforme de carga

### 3. Round-Robin

Distribui mensagens sequencialmente entre as partições em um padrão circular.

**Características**:
- Distribuição perfeitamente uniforme
- Não garante ordenação para mensagens relacionadas
- Previsível e determinística

**Uso recomendado**:
- Quando a ordenação não é importante
- Para distribuição perfeitamente uniforme de carga

### 4. Personalizada (Custom)

Permite implementar lógicas de particionamento específicas para casos de uso particulares.

**Características**:
- Flexibilidade máxima
- Pode ser otimizada para padrões de acesso específicos
- Requer implementação e manutenção adicionais

**Uso recomendado**:
- Para casos de uso específicos não atendidos pelas estratégias padrão

## Campos de Chave para Particionamento

Para a estratégia baseada em chave, definimos campos específicos para cada domínio:

| Domínio | Campo de Chave | Justificativa |
|---------|---------------|---------------|
| Pagamentos | `transactionId` | Manter ordenação de eventos relacionados à mesma transação |
| Pedidos | `orderId` | Manter ordenação de eventos relacionados ao mesmo pedido |
| Usuários | `userId` | Manter ordenação de eventos relacionados ao mesmo usuário |
| Notificações | `userId` | Agrupar notificações do mesmo usuário |
| Analytics | `sessionId` | Agrupar eventos da mesma sessão |

## Configurações por Domínio

### Domínio: Pagamentos

| Tópico | Estratégia | Campo de Chave | Justificativa |
|--------|------------|---------------|---------------|
| `payment.transaction.created` | Key-Based | `transactionId` | Ordenação de eventos de transação |
| `payment.transaction.updated` | Key-Based | `transactionId` | Ordenação de eventos de transação |
| `payment.transaction.failed` | Key-Based | `transactionId` | Ordenação de eventos de transação |
| `payment.refund.requested` | Key-Based | `transactionId` | Ordenação de eventos de transação |
| `payment.refund.processed` | Key-Based | `transactionId` | Ordenação de eventos de transação |
| `payment.webhook.received` | Key-Based | `entityId` | Agrupamento por entidade relacionada |

### Domínio: Pedidos

| Tópico | Estratégia | Campo de Chave | Justificativa |
|--------|------------|---------------|---------------|
| `order.created` | Key-Based | `orderId` | Ordenação de eventos de pedido |
| `order.updated` | Key-Based | `orderId` | Ordenação de eventos de pedido |
| `order.status.changed` | Key-Based | `orderId` | Ordenação de eventos de pedido |
| `order.cancelled` | Key-Based | `orderId` | Ordenação de eventos de pedido |
| `order.fulfilled` | Key-Based | `orderId` | Ordenação de eventos de pedido |

### Domínio: Usuários

| Tópico | Estratégia | Campo de Chave | Justificativa |
|--------|------------|---------------|---------------|
| `user.registered` | Key-Based | `userId` | Ordenação de eventos de usuário |
| `user.profile.updated` | Key-Based | `userId` | Ordenação de eventos de usuário |
| `user.subscription.changed` | Key-Based | `userId` | Ordenação de eventos de usuário |

### Domínio: Notificações

| Tópico | Estratégia | Campo de Chave | Justificativa |
|--------|------------|---------------|---------------|
| `notification.email.queued` | Key-Based | `userId` | Agrupamento por usuário |
| `notification.email.sent` | Key-Based | `userId` | Agrupamento por usuário |
| `notification.email.failed` | Key-Based | `userId` | Agrupamento por usuário |

### Domínio: Analytics

| Tópico | Estratégia | Campo de Chave | Justificativa |
|--------|------------|---------------|---------------|
| `analytics.page.viewed` | Key-Based | `sessionId` | Agrupamento por sessão |
| `analytics.product.viewed` | Key-Based | `sessionId` | Agrupamento por sessão |
| `analytics.search.performed` | Key-Based | `sessionId` | Agrupamento por sessão |
| `analytics.cart.abandoned` | Key-Based | `userId` | Agrupamento por usuário |

## Considerações de Performance

### Número de Partições

O número de partições por tópico foi definido considerando:

1. **Volume de mensagens**: Tópicos com maior volume têm mais partições
2. **Criticidade**: Tópicos críticos têm mais partições para maior paralelismo
3. **Consumo**: Número de consumidores paralelos esperados

### Balanceamento de Carga

Para garantir um balanceamento adequado:

1. **Monitoramento**: Implementamos monitoramento de distribuição de mensagens por partição
2. **Ajuste**: Revisamos periodicamente as estratégias de particionamento
3. **Rebalanceamento**: Configuramos rebalanceamento automático de consumidores

### Hot Partitions

Para evitar "hot partitions" (partições sobrecarregadas):

1. **Análise de distribuição de chaves**: Verificamos a distribuição das chaves usadas para particionamento
2. **Ajuste de hashing**: Implementamos funções de hash que distribuem uniformemente as chaves
3. **Monitoramento**: Alertas para partições com carga desproporcional

## Implementação Técnica

A implementação das estratégias de particionamento é feita através dos seguintes componentes:

1. **kafka-partitioning.config.ts**: Define as configurações de particionamento para cada tópico
2. **kafka.ts**: Implementa as funções de particionamento e integração com o Kafka

Para mais detalhes sobre a implementação, consulte o código-fonte desses arquivos.
