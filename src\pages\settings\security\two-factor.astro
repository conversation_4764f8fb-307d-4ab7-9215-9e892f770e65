---
/**
 * Página de configuração de autenticação de dois fatores
 *
 * Esta página permite ao usuário configurar, ativar, desativar e gerenciar
 * a autenticação de dois fatores (2FA) para sua conta.
 *
 * Parte da implementação da tarefa 6.1.2 - Implementação de 2FA
 */

import {
  disableTwoFactor,
  regenerateBackupCodes,
  setupTwoFactor,
  verifyAndEnableTwoFactor,
} from '../../../actions/twoFactorAuthAction';
import Layout from '../../../layouts/DashboardLayout.astro';
import { twoFactorAuthService } from '../../../services/twoFactorAuthService';
import { csrfHelper } from '../../../utils/csrfHelper';

// Verificar autenticação
if (!Astro.locals.isAuthenticated) {
  return Astro.redirect('/auth/login?redirect=/settings/security/two-factor');
}

const user = Astro.locals.user;

// Verificar se o usuário tem 2FA ativado
const has2FA = await twoFactorAuthService.isTwoFactorEnabled(user.id);

// Gerar token CSRF
const csrfToken = await csrfHelper.generateToken(Astro);

// Estado da página
let error = '';
let success = '';
let setupData = null;
let backupCodes = null;
let showSetupForm = false;
let showVerifyForm = false;
let showDisableForm = false;
let showBackupCodesForm = false;
let showBackupCodes = false;

// Processar ações
if (Astro.request.method === 'POST') {
  const formData = await Astro.request.formData();
  const action = formData.get('action')?.toString();

  switch (action) {
    case 'setup': {
      // Iniciar configuração de 2FA
      const setupResult = await setupTwoFactor.submit(
        {
          _csrf: formData.get('_csrf')?.toString() || '',
        },
        Astro
      );

      if (setupResult.success) {
        setupData = setupResult.data;
        showSetupForm = false;
        showVerifyForm = true;
        backupCodes = setupData.backupCodes;
      } else {
        error = setupResult.error || 'Erro ao configurar 2FA';
      }
      break;
    }

    case 'verify': {
      // Verificar e ativar 2FA
      const verifyResult = await verifyAndEnableTwoFactor.submit(
        {
          token: formData.get('token')?.toString() || '',
          _csrf: formData.get('_csrf')?.toString() || '',
        },
        Astro
      );

      if (verifyResult.success) {
        success = 'Autenticação de dois fatores ativada com sucesso!';
        showVerifyForm = false;
        showBackupCodes = true;
        // Recarregar a página após 3 segundos
        setTimeout(() => {
          window.location.reload();
        }, 3000);
      } else {
        error = verifyResult.error || 'Código inválido';
      }
      break;
    }

    case 'disable': {
      // Desativar 2FA
      const disableResult = await disableTwoFactor.submit(
        {
          token: formData.get('token')?.toString() || '',
          _csrf: formData.get('_csrf')?.toString() || '',
        },
        Astro
      );

      if (disableResult.success) {
        success = 'Autenticação de dois fatores desativada com sucesso!';
        showDisableForm = false;
        // Recarregar a página após 3 segundos
        setTimeout(() => {
          window.location.reload();
        }, 3000);
      } else {
        error = disableResult.error || 'Código inválido';
      }
      break;
    }

    case 'regenerate': {
      // Regenerar códigos de backup
      const regenerateResult = await regenerateBackupCodes.submit(
        {
          token: formData.get('token')?.toString() || '',
          _csrf: formData.get('_csrf')?.toString() || '',
        },
        Astro
      );

      if (regenerateResult.success) {
        success = 'Códigos de backup regenerados com sucesso!';
        backupCodes = regenerateResult.data;
        showBackupCodesForm = false;
        showBackupCodes = true;
      } else {
        error = regenerateResult.error || 'Código inválido';
      }
      break;
    }

    default:
      error = 'Ação inválida';
  }
}
---

<Layout title="Autenticação de Dois Fatores">
  <div class="container">
    <div class="page-header">
      <h1>Autenticação de Dois Fatores</h1>
      <p class="subtitle">Aumente a segurança da sua conta com uma camada adicional de proteção</p>
    </div>
    
    {error && (
      <div class="alert alert-danger">
        {error}
      </div>
    )}
    
    {success && (
      <div class="alert alert-success">
        {success}
      </div>
    )}
    
    <div class="card">
      <div class="card-header">
        <h2>Status da Autenticação de Dois Fatores</h2>
      </div>
      <div class="card-body">
        <div class="status-indicator">
          <div class={`status-badge ${has2FA ? 'enabled' : 'disabled'}`}>
            {has2FA ? 'Ativada' : 'Desativada'}
          </div>
          <p class="status-description">
            {has2FA 
              ? 'A autenticação de dois fatores está ativada para sua conta. Isso adiciona uma camada extra de segurança ao exigir um código de verificação além da sua senha.' 
              : 'A autenticação de dois fatores não está ativada. Recomendamos ativar para aumentar a segurança da sua conta.'}
          </p>
        </div>
        
        {!has2FA && !showSetupForm && !showVerifyForm && (
          <button class="btn btn-primary" onclick="document.getElementById('setup-form').style.display = 'block'; this.style.display = 'none';">
            Configurar Autenticação de Dois Fatores
          </button>
        )}
        
        {!has2FA && (
          <form id="setup-form" method="POST" style={showSetupForm ? '' : 'display: none;'}>
            <input type="hidden" name="action" value="setup" />
            <input type="hidden" name="_csrf" value={csrfToken} />
            <p>Ao ativar a autenticação de dois fatores, você precisará fornecer um código de verificação além da sua senha ao fazer login.</p>
            <button type="submit" class="btn btn-primary">Iniciar Configuração</button>
          </form>
        )}
        
        {has2FA && !showDisableForm && (
          <div class="action-buttons">
            <button class="btn btn-danger" onclick="document.getElementById('disable-form').style.display = 'block'; this.style.display = 'none';">
              Desativar Autenticação de Dois Fatores
            </button>
            <button class="btn btn-secondary" onclick="document.getElementById('backup-codes-form').style.display = 'block'; this.style.display = 'none';">
              Regenerar Códigos de Backup
            </button>
          </div>
        )}
        
        {has2FA && (
          <form id="disable-form" method="POST" style={showDisableForm ? '' : 'display: none;'}>
            <input type="hidden" name="action" value="disable" />
            <input type="hidden" name="_csrf" value={csrfToken} />
            <div class="form-group">
              <label for="disable-token">Código de Verificação</label>
              <input 
                type="text" 
                id="disable-token" 
                name="token" 
                placeholder="Digite o código de 6 dígitos" 
                autocomplete="one-time-code"
                inputmode="numeric"
                pattern="[0-9]*"
                maxlength="8"
                required
              />
              <small>Digite o código do seu aplicativo autenticador para confirmar a desativação.</small>
            </div>
            <div class="form-actions">
              <button type="submit" class="btn btn-danger">Desativar</button>
              <button type="button" class="btn btn-secondary" onclick="document.getElementById('disable-form').style.display = 'none'; document.querySelector('.action-buttons').style.display = 'flex';">
                Cancelar
              </button>
            </div>
          </form>
        )}
        
        {has2FA && (
          <form id="backup-codes-form" method="POST" style={showBackupCodesForm ? '' : 'display: none;'}>
            <input type="hidden" name="action" value="regenerate" />
            <input type="hidden" name="_csrf" value={csrfToken} />
            <div class="form-group">
              <label for="regenerate-token">Código de Verificação</label>
              <input 
                type="text" 
                id="regenerate-token" 
                name="token" 
                placeholder="Digite o código de 6 dígitos" 
                autocomplete="one-time-code"
                inputmode="numeric"
                pattern="[0-9]*"
                maxlength="8"
                required
              />
              <small>Digite o código do seu aplicativo autenticador para confirmar a regeneração dos códigos de backup.</small>
            </div>
            <div class="form-actions">
              <button type="submit" class="btn btn-primary">Regenerar Códigos</button>
              <button type="button" class="btn btn-secondary" onclick="document.getElementById('backup-codes-form').style.display = 'none'; document.querySelector('.action-buttons').style.display = 'flex';">
                Cancelar
              </button>
            </div>
          </form>
        )}
      </div>
    </div>
    
    {setupData && showVerifyForm && (
      <div class="card">
        <div class="card-header">
          <h2>Configurar Autenticação de Dois Fatores</h2>
        </div>
        <div class="card-body">
          <div class="setup-instructions">
            <h3>1. Escaneie o código QR</h3>
            <p>Use um aplicativo autenticador como Google Authenticator, Authy ou Microsoft Authenticator para escanear o código QR abaixo:</p>
            
            <div class="qr-container">
              <img 
                src={`https://chart.googleapis.com/chart?chs=200x200&chld=M|0&cht=qr&chl=${encodeURIComponent(setupData.uri)}`} 
                alt="Código QR para configuração de 2FA" 
                class="qr-code"
              />
            </div>
            
            <div class="secret-key">
              <p>Se não conseguir escanear o código QR, insira esta chave secreta manualmente no seu aplicativo:</p>
              <div class="secret-value">
                {setupData.secret}
              </div>
              <button class="btn btn-sm btn-secondary" onclick="copyToClipboard('{setupData.secret}')">Copiar</button>
            </div>
            
            <h3>2. Verifique a configuração</h3>
            <p>Digite o código de 6 dígitos exibido no seu aplicativo autenticador:</p>
            
            <form method="POST">
              <input type="hidden" name="action" value="verify" />
              <input type="hidden" name="_csrf" value={csrfToken} />
              <div class="form-group">
                <input 
                  type="text" 
                  name="token" 
                  placeholder="Digite o código de 6 dígitos" 
                  autocomplete="one-time-code"
                  inputmode="numeric"
                  pattern="[0-9]*"
                  maxlength="6"
                  required
                />
              </div>
              <button type="submit" class="btn btn-primary">Verificar e Ativar</button>
            </form>
          </div>
        </div>
      </div>
    )}
    
    {(showBackupCodes || backupCodes) && (
      <div class="card">
        <div class="card-header">
          <h2>Códigos de Backup</h2>
        </div>
        <div class="card-body">
          <div class="backup-codes">
            <p class="warning">
              <strong>IMPORTANTE:</strong> Guarde estes códigos em um local seguro. Cada código pode ser usado apenas uma vez para acessar sua conta caso você perca acesso ao seu aplicativo autenticador.
            </p>
            
            <div class="codes-container">
              {backupCodes && backupCodes.map((code: string) => (
                <div class="code">{code}</div>
              ))}
            </div>
            
            <div class="backup-actions">
              <button class="btn btn-secondary" onclick="printBackupCodes()">Imprimir</button>
              <button class="btn btn-secondary" onclick="downloadBackupCodes()">Baixar</button>
              <button class="btn btn-primary" onclick="window.location.reload()">Concluído</button>
            </div>
          </div>
        </div>
      </div>
    )}
  </div>
</Layout>

<script>
  // Função para copiar texto para a área de transferência
  function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(() => {
      alert('Chave secreta copiada para a área de transferência!');
    }).catch(err => {
      console.error('Erro ao copiar texto: ', err);
    });
  }
  
  // Função para imprimir códigos de backup
  function printBackupCodes() {
    const printWindow = window.open('', '_blank');
    const codesHTML = document.querySelector('.codes-container').innerHTML;
    
    printWindow.document.write(`
      <html>
        <head>
          <title>Códigos de Backup - Efi Educacional</title>
          <style>
            body { font-family: Arial, sans-serif; padding: 20px; }
            h1 { color: #333; }
            .codes { display: grid; grid-template-columns: repeat(2, 1fr); gap: 10px; margin: 20px 0; }
            .code { padding: 10px; border: 1px solid #ddd; border-radius: 4px; font-family: monospace; font-size: 16px; text-align: center; }
            .warning { background-color: #fff3cd; padding: 10px; border-radius: 4px; margin-bottom: 20px; }
          </style>
        </head>
        <body>
          <h1>Códigos de Backup - Efi Educacional</h1>
          <p class="warning"><strong>IMPORTANTE:</strong> Guarde estes códigos em um local seguro. Cada código pode ser usado apenas uma vez.</p>
          <div class="codes">${codesHTML}</div>
          <p>Gerado em: ${new Date().toLocaleString()}</p>
        </body>
      </html>
    `);
    
    printWindow.document.close();
    printWindow.focus();
    printWindow.print();
    printWindow.close();
  }
  
  // Função para baixar códigos de backup
  function downloadBackupCodes() {
    const codes = Array.from(document.querySelectorAll('.code')).map(el => el.textContent).join('\n');
    const content = `CÓDIGOS DE BACKUP - EFI EDUCACIONAL\n\nIMPORTANTE: Guarde estes códigos em um local seguro. Cada código pode ser usado apenas uma vez.\n\n${codes}\n\nGerado em: ${new Date().toLocaleString()}`;
    
    const blob = new Blob([content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    
    const a = document.createElement('a');
    a.href = url;
    a.download = 'codigos-backup-efi.txt';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }
</script>

<style>
  .container {
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem;
  }
  
  .page-header {
    margin-bottom: 2rem;
  }
  
  .page-header h1 {
    font-size: 2rem;
    color: var(--color-primary);
    margin-bottom: 0.5rem;
  }
  
  .subtitle {
    color: var(--color-text-muted);
    font-size: 1.1rem;
  }
  
  .card {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
    overflow: hidden;
  }
  
  .card-header {
    background-color: var(--color-bg-light);
    padding: 1.25rem 1.5rem;
    border-bottom: 1px solid var(--color-border);
  }
  
  .card-header h2 {
    font-size: 1.5rem;
    margin: 0;
    color: var(--color-text);
  }
  
  .card-body {
    padding: 1.5rem;
  }
  
  .status-indicator {
    display: flex;
    align-items: center;
    margin-bottom: 1.5rem;
  }
  
  .status-badge {
    padding: 0.5rem 1rem;
    border-radius: 50px;
    font-weight: 600;
    font-size: 0.9rem;
    margin-right: 1rem;
  }
  
  .status-badge.enabled {
    background-color: #d1e7dd;
    color: #0f5132;
  }
  
  .status-badge.disabled {
    background-color: #f8d7da;
    color: #842029;
  }
  
  .status-description {
    flex: 1;
    margin: 0;
    color: var(--color-text);
  }
  
  .btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 4px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s, transform 0.1s;
  }
  
  .btn:active {
    transform: translateY(1px);
  }
  
  .btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
  }
  
  .btn-primary {
    background-color: var(--color-primary);
    color: white;
  }
  
  .btn-primary:hover {
    background-color: var(--color-primary-dark);
  }
  
  .btn-secondary {
    background-color: var(--color-secondary);
    color: white;
  }
  
  .btn-secondary:hover {
    background-color: var(--color-secondary-dark);
  }
  
  .btn-danger {
    background-color: #dc3545;
    color: white;
  }
  
  .btn-danger:hover {
    background-color: #bb2d3b;
  }
  
  .form-group {
    margin-bottom: 1.5rem;
  }
  
  .form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
  }
  
  .form-group input {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 1px solid var(--color-border);
    border-radius: 4px;
    font-size: 1rem;
  }
  
  .form-group small {
    display: block;
    margin-top: 0.5rem;
    color: var(--color-text-muted);
    font-size: 0.875rem;
  }
  
  .form-actions {
    display: flex;
    gap: 1rem;
  }
  
  .action-buttons {
    display: flex;
    gap: 1rem;
  }
  
  .alert {
    padding: 1rem;
    border-radius: 4px;
    margin-bottom: 1.5rem;
  }
  
  .alert-danger {
    background-color: #f8d7da;
    color: #842029;
    border: 1px solid #f5c2c7;
  }
  
  .alert-success {
    background-color: #d1e7dd;
    color: #0f5132;
    border: 1px solid #badbcc;
  }
  
  .setup-instructions {
    max-width: 600px;
    margin: 0 auto;
  }
  
  .setup-instructions h3 {
    font-size: 1.25rem;
    margin: 1.5rem 0 1rem;
    color: var(--color-primary);
  }
  
  .qr-container {
    display: flex;
    justify-content: center;
    margin: 1.5rem 0;
  }
  
  .qr-code {
    border: 1px solid var(--color-border);
    padding: 1rem;
    background-color: white;
  }
  
  .secret-key {
    background-color: var(--color-bg-light);
    padding: 1rem;
    border-radius: 4px;
    margin-bottom: 1.5rem;
  }
  
  .secret-value {
    font-family: monospace;
    font-size: 1.2rem;
    background-color: white;
    padding: 0.75rem;
    border: 1px solid var(--color-border);
    border-radius: 4px;
    margin: 0.5rem 0;
    word-break: break-all;
  }
  
  .backup-codes {
    max-width: 600px;
    margin: 0 auto;
  }
  
  .warning {
    background-color: #fff3cd;
    color: #664d03;
    padding: 1rem;
    border-radius: 4px;
    margin-bottom: 1.5rem;
  }
  
  .codes-container {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
    margin-bottom: 1.5rem;
  }
  
  .code {
    font-family: monospace;
    font-size: 1.2rem;
    background-color: var(--color-bg-light);
    padding: 0.75rem;
    border: 1px solid var(--color-border);
    border-radius: 4px;
    text-align: center;
  }
  
  .backup-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
  }
  
  @media (max-width: 768px) {
    .container {
      padding: 1rem;
    }
    
    .status-indicator {
      flex-direction: column;
      align-items: flex-start;
    }
    
    .status-badge {
      margin-bottom: 1rem;
    }
    
    .action-buttons {
      flex-direction: column;
    }
    
    .codes-container {
      grid-template-columns: 1fr;
    }
    
    .backup-actions {
      flex-direction: column;
    }
  }
</style>
