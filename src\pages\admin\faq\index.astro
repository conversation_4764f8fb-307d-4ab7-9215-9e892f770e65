---
import { PageTransition } from '../../../components/transitions';
import DaisyButton from '../../../components/ui/DaisyButton.astro';
import DaisyCard from '../../../components/ui/DaisyCard.astro';
import DaisyTabs from '../../../components/ui/DaisyTabs.astro';
import Breadcrumbs from '../../../components/ui/navigation/Breadcrumbs.astro';
import { GetFaqItemsUseCase } from '../../../domain/usecases/faq/GetFaqItemsUseCase';
import { PostgresFaqRepository } from '../../../infrastructure/database/repositories/PostgresFaqRepository';
/**
 * Página de Administração de FAQ
 *
 * Interface para gerenciar itens de FAQ.
 * Parte da implementação da tarefa 8.7.1 - Implementação de FAQ interativo
 */
import BaseLayout from '../../../layouts/BaseLayout.astro';
import { Container, Section } from '../../../layouts/grid';

// Título da página
const title = 'Gerenciar FAQ';

// Breadcrumbs para a página atual
const breadcrumbItems = [
  { href: '/', label: 'Início' },
  { href: '/admin', label: 'Administração' },
  { label: 'Gerenciar FAQ' },
];

// Obter parâmetros de consulta
const category = Astro.url.searchParams.get('category') || '';
const query = Astro.url.searchParams.get('q') || '';
const page = Number.parseInt(Astro.url.searchParams.get('page') || '1');
const limit = Number.parseInt(Astro.url.searchParams.get('limit') || '10');
const status = Astro.url.searchParams.get('status') || 'all';

// Inicializar repositório e caso de uso
const faqRepository = new PostgresFaqRepository();
const getFaqItemsUseCase = new GetFaqItemsUseCase(faqRepository);

// Construir filtro
const filter: any = {};

if (category) {
  filter.category = category;
}

if (query) {
  filter.search = query;
}

if (status === 'published') {
  filter.isPublished = true;
} else if (status === 'draft') {
  filter.isPublished = false;
}

// Obter itens de FAQ
const result = await getFaqItemsUseCase.execute({
  filter,
  sort: { field: 'updatedAt', direction: 'desc' },
  pagination: { page, limit },
});

const faqItems = result.success ? result.data?.items || [] : [];
const totalItems = result.success ? result.data?.total || 0 : 0;
const totalPages = result.success ? result.data?.totalPages || 1 : 1;

// Obter estatísticas
const stats = await faqRepository.getStats();

// Categorias disponíveis
const categories = [
  { value: '', label: 'Todas as categorias' },
  { value: 'general', label: 'Geral' },
  { value: 'account', label: 'Conta' },
  { value: 'products', label: 'Produtos' },
  { value: 'payment', label: 'Pagamento' },
  { value: 'shipping', label: 'Envio' },
  { value: 'technical', label: 'Técnico' },
  { value: 'educational', label: 'Educacional' },
  { value: 'other', label: 'Outro' },
];

// Status disponíveis
const statuses = [
  { value: 'all', label: 'Todos os status' },
  { value: 'published', label: 'Publicados' },
  { value: 'draft', label: 'Rascunhos' },
];

// Função para formatar data
const formatDate = (dateString: string): string => {
  const date = new Date(dateString);
  return date.toLocaleDateString('pt-BR', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  });
};

// Função para truncar texto
const truncateText = (text: string, maxLength: number): string => {
  if (text.length <= maxLength) {
    return text;
  }
  return `${text.substring(0, maxLength)}...`;
};

// Função para obter cor do status
const getStatusColor = (isPublished: boolean): string => {
  return isPublished ? 'success' : 'warning';
};

// Função para obter label do status
const getStatusLabel = (isPublished: boolean): string => {
  return isPublished ? 'Publicado' : 'Rascunho';
};
---

<BaseLayout title={title} enableViewTransitions={true} transitionType="fade">
  <Container>
    <PageTransition type="fade" duration="0.5s">
      <Section>
        <Breadcrumbs items={breadcrumbItems} class="mb-6" />
        
        <div class="flex justify-between items-center mb-6">
          <h1 class="text-3xl font-bold">{title}</h1>
          
          <DaisyButton 
            href="/admin/faq/novo" 
            variant="primary" 
            icon="plus"
            text="Novo Item"
          />
        </div>
        
        <div class="stats shadow mb-6 w-full">
          <div class="stat">
            <div class="stat-figure text-primary">
              <i class="icon icon-help-circle text-3xl"></i>
            </div>
            <div class="stat-title">Total de Itens</div>
            <div class="stat-value">{stats.totalItems}</div>
          </div>
          
          <div class="stat">
            <div class="stat-figure text-success">
              <i class="icon icon-check-circle text-3xl"></i>
            </div>
            <div class="stat-title">Publicados</div>
            <div class="stat-value text-success">{stats.publishedItems}</div>
          </div>
          
          <div class="stat">
            <div class="stat-figure text-info">
              <i class="icon icon-eye text-3xl"></i>
            </div>
            <div class="stat-title">Visualizações</div>
            <div class="stat-value">{stats.totalViews}</div>
          </div>
        </div>
        
        <div class="bg-base-200 p-4 rounded-lg mb-6">
          <form action="/admin/faq" method="GET" class="flex flex-col md:flex-row gap-4">
            <div class="form-control flex-1">
              <div class="input-group">
                <input 
                  type="text" 
                  name="q" 
                  placeholder="Buscar perguntas..." 
                  class="input input-bordered w-full" 
                  value={query}
                />
                <button type="submit" class="btn btn-square">
                  <i class="icon icon-search"></i>
                </button>
              </div>
            </div>
            
            <div class="form-control">
              <select name="category" class="select select-bordered">
                {categories.map(cat => (
                  <option 
                    value={cat.value} 
                    selected={cat.value === category}
                  >
                    {cat.label}
                  </option>
                ))}
              </select>
            </div>
            
            <div class="form-control">
              <select name="status" class="select select-bordered">
                {statuses.map(s => (
                  <option 
                    value={s.value} 
                    selected={s.value === status}
                  >
                    {s.label}
                  </option>
                ))}
              </select>
            </div>
            
            <button type="submit" class="btn btn-primary">
              Filtrar
            </button>
            
            {(query || category || status !== 'all') && (
              <a href="/admin/faq" class="btn btn-outline">
                Limpar Filtros
              </a>
            )}
          </form>
        </div>
        
        <div class="overflow-x-auto">
          {faqItems.length === 0 ? (
            <div class="text-center py-12">
              <div class="text-4xl text-gray-400 mb-4">
                <i class="icon icon-help-circle"></i>
              </div>
              <h3 class="text-xl font-bold mb-2">Nenhum item de FAQ encontrado</h3>
              <p class="text-gray-500 mb-4">
                {query 
                  ? `Não encontramos resultados para "${query}".` 
                  : 'Comece criando seu primeiro item de FAQ.'}
              </p>
              <DaisyButton 
                href="/admin/faq/novo" 
                variant="primary" 
                icon="plus"
                text="Criar Item de FAQ"
              />
            </div>
          ) : (
            <table class="table w-full">
              <thead>
                <tr>
                  <th>Pergunta</th>
                  <th>Categoria</th>
                  <th>Status</th>
                  <th>Visualizações</th>
                  <th>Útil</th>
                  <th>Atualizado</th>
                  <th>Ações</th>
                </tr>
              </thead>
              <tbody>
                {faqItems.map(item => (
                  <tr>
                    <td>
                      <div class="font-medium">{truncateText(item.question, 50)}</div>
                    </td>
                    <td>
                      <div class="badge badge-outline">
                        {categories.find(c => c.value === item.category)?.label || item.category}
                      </div>
                    </td>
                    <td>
                      <div class={`badge badge-${getStatusColor(item.isPublished)}`}>
                        {getStatusLabel(item.isPublished)}
                      </div>
                    </td>
                    <td class="text-center">{item.viewCount}</td>
                    <td class="text-center">
                      {item.helpfulCount + item.notHelpfulCount > 0 
                        ? `${Math.round(item.getHelpfulnessRate())}%` 
                        : '-'}
                    </td>
                    <td>{formatDate(item.updatedAt.toString())}</td>
                    <td>
                      <div class="flex gap-1">
                        <a 
                          href={`/admin/faq/editar/${item.id}`} 
                          class="btn btn-sm btn-ghost"
                          aria-label="Editar"
                        >
                          <i class="icon icon-edit-2"></i>
                        </a>
                        
                        <button 
                          class={`btn btn-sm btn-ghost ${item.isPublished ? 'text-warning' : 'text-success'}`}
                          data-action={item.isPublished ? 'unpublish' : 'publish'}
                          data-id={item.id}
                          aria-label={item.isPublished ? 'Despublicar' : 'Publicar'}
                        >
                          <i class={`icon icon-${item.isPublished ? 'eye-off' : 'eye'}`}></i>
                        </button>
                        
                        <button 
                          class="btn btn-sm btn-ghost text-error"
                          data-action="delete"
                          data-id={item.id}
                          aria-label="Excluir"
                        >
                          <i class="icon icon-trash"></i>
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          )}
        </div>
        
        {totalPages > 1 && (
          <div class="flex justify-center mt-8">
            <div class="join">
              {Array.from({ length: totalPages }, (_, i) => i + 1).map(p => (
                <a 
                  href={`/admin/faq?page=${p}&limit=${limit}${category ? `&category=${category}` : ''}${query ? `&q=${query}` : ''}${status !== 'all' ? `&status=${status}` : ''}`} 
                  class={`join-item btn ${p === page ? 'btn-active' : ''}`}
                >
                  {p}
                </a>
              ))}
            </div>
          </div>
        )}
      </Section>
    </PageTransition>
  </Container>
</BaseLayout>

<script>
  // Script para funcionalidade da página de administração de FAQ
  document.addEventListener('DOMContentLoaded', () => {
    // Botões de ação
    const actionButtons = document.querySelectorAll('[data-action]');
    
    actionButtons.forEach(button => {
      button.addEventListener('click', async () => {
        const action = button.getAttribute('data-action');
        const id = button.getAttribute('data-id');
        
        if (!action || !id) {
          return;
        }
        
        if (action === 'delete') {
          if (confirm('Tem certeza que deseja excluir este item de FAQ?')) {
            try {
              // Em um cenário real, aqui seria feita uma chamada para a API
              const response = await fetch(`/api/faq/${id}`, {
                method: 'DELETE'
              });
              
              if (response.ok) {
                window.location.reload();
              } else {
                alert('Erro ao excluir item de FAQ.');
              }
            } catch (error) {
              console.error('Erro ao excluir item de FAQ:', error);
              alert('Erro ao excluir item de FAQ.');
            }
          }
        } else if (action === 'publish' || action === 'unpublish') {
          try {
            // Em um cenário real, aqui seria feita uma chamada para a API
            const response = await fetch(`/api/faq/${action}/${id}`, {
              method: 'POST'
            });
            
            if (response.ok) {
              window.location.reload();
            } else {
              alert(`Erro ao ${action === 'publish' ? 'publicar' : 'despublicar'} item de FAQ.`);
            }
          } catch (error) {
            console.error(`Erro ao ${action === 'publish' ? 'publicar' : 'despublicar'} item de FAQ:`, error);
            alert(`Erro ao ${action === 'publish' ? 'publicar' : 'despublicar'} item de FAQ.`);
          }
        }
      });
    });
  });
</script>
