/**
 * Consumidor de eventos de pedidos
 *
 * Este serviço é responsável por consumir eventos relacionados a pedidos
 * do Kafka.
 */

import { queryHelper } from '@db/queryHelper';
import { alertService } from '@services/alertService';
import { emailService } from '@services/emailService';
import { type EventHandler, eventConsumerService } from '@services/eventConsumerService';
import type { OrderEvent } from '@services/eventProducerService';
import type { ProcessingContext } from '@services/messageProcessingService';
import { logger } from '@utils/logger';

/**
 * Handler para eventos de criação de pedido
 */
class OrderCreatedHandler implements EventHandler<OrderEvent> {
  async handle(event: OrderEvent, context: ProcessingContext): Promise<void> {
    try {
      logger.info(`Processando evento de criação de pedido: ${event.orderId}`);

      // Verificar se o pedido existe
      const order = await queryHelper.queryOne('SELECT * FROM tab_order WHERE ulid_order = $1', [
        event.orderId,
      ]);

      if (!order) {
        logger.warn(`Pedido não encontrado: ${event.orderId}`);
        return;
      }

      // Registrar evento no log
      await this.logOrderEvent(event.orderId, event);

      // Enviar email de confirmação de pedido
      await this.sendOrderConfirmationEmail(event);

      // Verificar se há alertas a serem disparados
      if (event.total > 1000) {
        await alertService.checkAndCreateAlert(
          'high_value_order',
          {
            orderId: event.orderId,
            userId: event.userId,
            total: event.total,
          },
          'Pedido de alto valor criado'
        );
      }

      logger.info(`Evento de criação de pedido processado: ${event.orderId}`);
    } catch (error) {
      logger.error(`Erro ao processar evento de criação de pedido ${event.orderId}:`, error);
      throw error;
    }
  }

  /**
   * Registra evento de pedido no log
   * @param orderId - ID do pedido
   * @param event - Evento de pedido
   */
  private async logOrderEvent(orderId: string, event: OrderEvent): Promise<void> {
    try {
      await queryHelper.query(
        `INSERT INTO tab_order_log (
          ulid_order_log, ulid_order,
          event_type, event_data, created_at
        ) VALUES (
          gen_ulid(), $1, $2, $3, NOW()
        )`,
        [orderId, 'created', JSON.stringify(event)]
      );
    } catch (error) {
      logger.error(`Erro ao registrar evento de pedido ${orderId}:`, error);
    }
  }

  /**
   * Envia email de confirmação de pedido
   * @param event - Evento de pedido
   */
  private async sendOrderConfirmationEmail(event: OrderEvent): Promise<void> {
    try {
      // Obter dados do usuário
      const user = await queryHelper.queryOne(
        'SELECT name, email FROM tab_user WHERE ulid_user = $1',
        [event.userId]
      );

      if (!user) {
        logger.warn(`Usuário não encontrado: ${event.userId}`);
        return;
      }

      // Obter itens do pedido
      const orderItems = event.items || [];

      // Enviar email
      await emailService.sendEmail({
        to: user.email,
        subject: 'Pedido Confirmado',
        template: 'order-confirmation',
        data: {
          userName: user.name,
          orderId: event.orderId,
          total: event.total,
          status: event.status,
          items: orderItems,
        },
      });
    } catch (error) {
      logger.error(`Erro ao enviar email de confirmação de pedido ${event.orderId}:`, error);
    }
  }
}

/**
 * Handler para eventos de mudança de status de pedido
 */
class OrderStatusChangedHandler implements EventHandler<OrderEvent> {
  async handle(event: OrderEvent, context: ProcessingContext): Promise<void> {
    try {
      logger.info(`Processando evento de mudança de status de pedido: ${event.orderId}`);

      // Verificar se o pedido existe
      const order = await queryHelper.queryOne('SELECT * FROM tab_order WHERE ulid_order = $1', [
        event.orderId,
      ]);

      if (!order) {
        logger.warn(`Pedido não encontrado: ${event.orderId}`);
        return;
      }

      // Registrar evento no log
      await this.logOrderEvent(event.orderId, event);

      // Processar de acordo com o status
      switch (event.status) {
        case 'paid':
          await this.handlePaidOrder(event);
          break;
        case 'shipped':
          await this.handleShippedOrder(event);
          break;
        case 'delivered':
          await this.handleDeliveredOrder(event);
          break;
        case 'cancelled':
          await this.handleCancelledOrder(event);
          break;
        default:
          logger.info(`Status de pedido não tratado: ${event.status}`);
      }

      logger.info(`Evento de mudança de status de pedido processado: ${event.orderId}`);
    } catch (error) {
      logger.error(
        `Erro ao processar evento de mudança de status de pedido ${event.orderId}:`,
        error
      );
      throw error;
    }
  }

  /**
   * Registra evento de pedido no log
   * @param orderId - ID do pedido
   * @param event - Evento de pedido
   */
  private async logOrderEvent(orderId: string, event: OrderEvent): Promise<void> {
    try {
      await queryHelper.query(
        `INSERT INTO tab_order_log (
          ulid_order_log, ulid_order,
          event_type, event_data, created_at
        ) VALUES (
          gen_ulid(), $1, $2, $3, NOW()
        )`,
        [orderId, 'status_changed', JSON.stringify(event)]
      );
    } catch (error) {
      logger.error(`Erro ao registrar evento de pedido ${orderId}:`, error);
    }
  }

  /**
   * Processa pedido pago
   * @param event - Evento de pedido
   */
  private async handlePaidOrder(event: OrderEvent): Promise<void> {
    try {
      // Obter dados do usuário
      const user = await queryHelper.queryOne(
        'SELECT name, email FROM tab_user WHERE ulid_user = $1',
        [event.userId]
      );

      if (!user) {
        logger.warn(`Usuário não encontrado: ${event.userId}`);
        return;
      }

      // Enviar email
      await emailService.sendEmail({
        to: user.email,
        subject: 'Pagamento do Pedido Confirmado',
        template: 'order-paid',
        data: {
          userName: user.name,
          orderId: event.orderId,
          total: event.total,
        },
      });
    } catch (error) {
      logger.error(`Erro ao processar pedido pago ${event.orderId}:`, error);
    }
  }

  /**
   * Processa pedido enviado
   * @param event - Evento de pedido
   */
  private async handleShippedOrder(event: OrderEvent): Promise<void> {
    try {
      // Obter dados do usuário
      const user = await queryHelper.queryOne(
        'SELECT name, email FROM tab_user WHERE ulid_user = $1',
        [event.userId]
      );

      if (!user) {
        logger.warn(`Usuário não encontrado: ${event.userId}`);
        return;
      }

      // Obter dados de envio
      const shipping = await queryHelper.queryOne(
        'SELECT * FROM tab_shipping WHERE ulid_order = $1',
        [event.orderId]
      );

      // Enviar email
      await emailService.sendEmail({
        to: user.email,
        subject: 'Pedido Enviado',
        template: 'order-shipped',
        data: {
          userName: user.name,
          orderId: event.orderId,
          trackingCode: shipping?.tracking_code || 'Não disponível',
          shippingCompany: shipping?.shipping_company || 'Não disponível',
          estimatedDelivery: shipping?.estimated_delivery || 'Não disponível',
        },
      });
    } catch (error) {
      logger.error(`Erro ao processar pedido enviado ${event.orderId}:`, error);
    }
  }

  /**
   * Processa pedido entregue
   * @param event - Evento de pedido
   */
  private async handleDeliveredOrder(event: OrderEvent): Promise<void> {
    try {
      // Obter dados do usuário
      const user = await queryHelper.queryOne(
        'SELECT name, email FROM tab_user WHERE ulid_user = $1',
        [event.userId]
      );

      if (!user) {
        logger.warn(`Usuário não encontrado: ${event.userId}`);
        return;
      }

      // Enviar email
      await emailService.sendEmail({
        to: user.email,
        subject: 'Pedido Entregue',
        template: 'order-delivered',
        data: {
          userName: user.name,
          orderId: event.orderId,
        },
      });
    } catch (error) {
      logger.error(`Erro ao processar pedido entregue ${event.orderId}:`, error);
    }
  }

  /**
   * Processa pedido cancelado
   * @param event - Evento de pedido
   */
  private async handleCancelledOrder(event: OrderEvent): Promise<void> {
    try {
      // Obter dados do usuário
      const user = await queryHelper.queryOne(
        'SELECT name, email FROM tab_user WHERE ulid_user = $1',
        [event.userId]
      );

      if (!user) {
        logger.warn(`Usuário não encontrado: ${event.userId}`);
        return;
      }

      // Enviar email
      await emailService.sendEmail({
        to: user.email,
        subject: 'Pedido Cancelado',
        template: 'order-cancelled',
        data: {
          userName: user.name,
          orderId: event.orderId,
          cancellationReason:
            (event as OrderEvent & { cancellationReason?: string }).cancellationReason ||
            'Não especificado',
        },
      });

      // Verificar se há alertas a serem disparados
      await alertService.checkAndCreateAlert(
        'order_cancelled',
        {
          orderId: event.orderId,
          userId: event.userId,
          total: event.total,
        },
        'Pedido cancelado'
      );
    } catch (error) {
      logger.error(`Erro ao processar pedido cancelado ${event.orderId}:`, error);
    }
  }
}

/**
 * Inicializa o consumidor de eventos de pedido
 */
export async function initOrderEventConsumer(): Promise<void> {
  try {
    // Registrar handlers
    eventConsumerService.registerHandler('order.created', new OrderCreatedHandler());

    eventConsumerService.registerHandler('order.status.changed', new OrderStatusChangedHandler());

    // Iniciar consumidor
    await eventConsumerService.init({
      topics: [
        'order.created',
        'order.updated',
        'order.status.changed',
        'order.cancelled',
        'order.fulfilled',
      ],
      groupId: 'order-consumer-group',
    });

    logger.info('Consumidor de eventos de pedido inicializado');
  } catch (error) {
    logger.error('Erro ao inicializar consumidor de eventos de pedido:', error);
    throw error;
  }
}
