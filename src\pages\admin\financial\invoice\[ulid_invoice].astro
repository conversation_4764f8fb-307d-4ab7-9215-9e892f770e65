---
import { actions } from 'astro:actions';
import ControlButtons from '@components/form/ControlButtons.astro';
import FormBase from '@components/form/FormBase.astro';
import InputHidden from '@components/form/InputHidden.astro';
import InputText from '@components/form/InputText.astro';
import BaseLayout from '@layouts/BaseLayout.astro';
import type { InvoiceData } from 'src/database/interfacesHelper';

// Define interfaces for type safety
interface ActionResult {
  data?: InvoiceData | InvoiceData[];
  error?: string;
}

// Obter parâmetro da URL
const { ulid_invoice = '' } = Astro.params;

// Busca dados da fatura
let data: InvoiceData = {};
try {
  const result = (await Astro.callAction(actions.invoiceAction.read, {
    filter: 'ulid_invoice',
    ulid_invoice,
  })) as ActionResult;
  data = (result.data as InvoiceData) || {};
} catch (error) {
  console.error('Erro ao carregar fatura:', error);
  return Astro.redirect('/admin/financial/invoice?error=load');
}

const formValidation = `
  const nameInput = form.querySelector('input[name="name"]');
  if (!nameInput || !nameInput.value.trim()) {
    alert('O nome da fatura é obrigatório');
    return false;
  }
  return true;
`;
---
<BaseLayout title="Formulário de Faturamento">
  <div class="container mx-auto p-4">
    <h1 class="text-2xl font-bold mb-4">
      {ulid_invoice ? "Editar" : "Novo"} Fatura
    </h1>

    <FormBase
      action={actions.invoiceAction.update}
      formType="invoice"
      onSubmitValidation={formValidation}
    >
      <!-- Campos ocultos -->
      <InputHidden ulid={data.ulid_invoice ?? ""} field="ulid_invoice" />
      <InputHidden ulid={data.ulid_order ?? ""} field="ulid_order" />
      <InputHidden ulid={data.ulid_user ?? ""} field="ulid_user" />

      <!-- Valor -->
      <InputText 
        label="Valor" 
        name="value" 
        value={data.value?.toString() ?? ""} 
        required={true}
      />

      <!-- Taxa -->
      <InputText 
        label="Taxa" 
        name="tax" 
        value={data.tax?.toString() ?? ""} 
        required={true}
      />

      <!-- File -->
      <InputText 
        label="File" 
        name="file" 
        value={data.file ?? ""} 
        required={false}
      />

      <!-- Botões de controle -->
      <ControlButtons 
        saveLabel="Salvar"
        cancelHref="/admin/financial/invoice"
      />
    </FormBase>
  </div>
</BaseLayout>