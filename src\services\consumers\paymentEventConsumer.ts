/**
 * Consumidor de eventos de pagamento
 *
 * Este serviço é responsável por consumir eventos relacionados a pagamentos
 * e reembolsos do Kafka.
 */

import { queryHelper } from '@db/queryHelper';
import { alertService } from '@services/alertService';
import { emailService } from '@services/emailService';
import { type EventHandler, eventConsumerService } from '@services/eventConsumerService';
import type { PaymentEvent } from '@services/eventProducerService';
import type { ProcessingContext } from '@services/messageProcessingService';
import { PaymentStatus } from '@services/paymentService';
import { RefundStatus } from '@services/refundService';
import { logger } from '@utils/logger';

/**
 * Handler para eventos de criação de pagamento
 */
class PaymentCreatedHandler implements EventHandler<PaymentEvent> {
  async handle(event: PaymentEvent, context: ProcessingContext): Promise<void> {
    try {
      logger.info(`Processando evento de criação de pagamento: ${event.paymentId}`);

      // Verificar se o pagamento existe
      const payment = await queryHelper.queryOne(
        'SELECT * FROM tab_payment WHERE ulid_payment = $1',
        [event.paymentId]
      );

      if (!payment) {
        logger.warn(`Pagamento não encontrado: ${event.paymentId}`);
        return;
      }

      // Registrar evento no log
      await this.logPaymentEvent(event.paymentId, event);

      // Enviar email de confirmação de pagamento pendente
      await this.sendPaymentPendingEmail(event);

      logger.info(`Evento de criação de pagamento processado: ${event.paymentId}`);
    } catch (error) {
      logger.error(`Erro ao processar evento de criação de pagamento ${event.paymentId}:`, error);
      throw error;
    }
  }

  /**
   * Registra evento de pagamento no log
   * @param paymentId - ID do pagamento
   * @param event - Evento de pagamento
   */
  private async logPaymentEvent(paymentId: string, event: PaymentEvent): Promise<void> {
    try {
      await queryHelper.query(
        `INSERT INTO tab_payment_log (
          ulid_payment_log, ulid_payment,
          event_type, event_data, created_at
        ) VALUES (
          gen_ulid(), $1, $2, $3, NOW()
        )`,
        [paymentId, 'created', JSON.stringify(event)]
      );
    } catch (error) {
      logger.error(`Erro ao registrar evento de pagamento ${paymentId}:`, error);
    }
  }

  /**
   * Envia email de confirmação de pagamento pendente
   * @param event - Evento de pagamento
   */
  private async sendPaymentPendingEmail(event: PaymentEvent): Promise<void> {
    try {
      // Obter dados do usuário
      if (!event.userId) {
        return;
      }

      const user = await queryHelper.queryOne(
        'SELECT name, email FROM tab_user WHERE ulid_user = $1',
        [event.userId]
      );

      if (!user) {
        logger.warn(`Usuário não encontrado: ${event.userId}`);
        return;
      }

      // Obter dados do pedido
      const order = event.orderId
        ? await queryHelper.queryOne('SELECT * FROM tab_order WHERE ulid_order = $1', [
            event.orderId,
          ])
        : null;

      // Enviar email
      await emailService.sendEmail({
        to: user.email,
        subject: 'Pagamento em Processamento',
        template: 'payment-pending',
        data: {
          userName: user.name,
          paymentId: event.paymentId,
          orderId: event.orderId,
          value: event.value,
          paymentType: event.paymentType,
          orderItems: order?.items || [],
        },
      });
    } catch (error) {
      logger.error(`Erro ao enviar email de pagamento pendente ${event.paymentId}:`, error);
    }
  }
}

/**
 * Handler para eventos de atualização de pagamento
 */
class PaymentUpdatedHandler implements EventHandler<PaymentEvent> {
  async handle(event: PaymentEvent, context: ProcessingContext): Promise<void> {
    try {
      logger.info(`Processando evento de atualização de pagamento: ${event.paymentId}`);

      // Verificar se o pagamento existe
      const payment = await queryHelper.queryOne(
        'SELECT * FROM tab_payment WHERE ulid_payment = $1',
        [event.paymentId]
      );

      if (!payment) {
        logger.warn(`Pagamento não encontrado: ${event.paymentId}`);
        return;
      }

      // Registrar evento no log
      await this.logPaymentEvent(event.paymentId, event);

      // Processar de acordo com o status
      if (event.status === PaymentStatus.APPROVED) {
        await this.handleApprovedPayment(event);
      } else if (
        event.status === PaymentStatus.REJECTED ||
        event.status === PaymentStatus.CANCELLED
      ) {
        await this.handleRejectedPayment(event);
      }

      logger.info(`Evento de atualização de pagamento processado: ${event.paymentId}`);
    } catch (error) {
      logger.error(
        `Erro ao processar evento de atualização de pagamento ${event.paymentId}:`,
        error
      );
      throw error;
    }
  }

  /**
   * Registra evento de pagamento no log
   * @param paymentId - ID do pagamento
   * @param event - Evento de pagamento
   */
  private async logPaymentEvent(paymentId: string, event: PaymentEvent): Promise<void> {
    try {
      await queryHelper.query(
        `INSERT INTO tab_payment_log (
          ulid_payment_log, ulid_payment,
          event_type, event_data, created_at
        ) VALUES (
          gen_ulid(), $1, $2, $3, NOW()
        )`,
        [paymentId, 'updated', JSON.stringify(event)]
      );
    } catch (error) {
      logger.error(`Erro ao registrar evento de pagamento ${paymentId}:`, error);
    }
  }

  /**
   * Processa pagamento aprovado
   * @param event - Evento de pagamento
   */
  private async handleApprovedPayment(event: PaymentEvent): Promise<void> {
    try {
      // Atualizar status do pedido, se existir
      if (event.orderId) {
        await queryHelper.query(
          `UPDATE tab_order
           SET cod_status = 2, -- Assumindo que 2 é o código para "Pago"
               updated_at = NOW()
           WHERE ulid_order = $1`,
          [event.orderId]
        );
      }

      // Enviar email de confirmação de pagamento
      await this.sendPaymentConfirmationEmail(event);

      // Verificar se há alertas a serem disparados
      await alertService.checkAndCreateAlert(
        'payment_approved',
        {
          paymentId: event.paymentId,
          orderId: event.orderId,
          value: event.value,
        },
        'Pagamento aprovado'
      );
    } catch (error) {
      logger.error(`Erro ao processar pagamento aprovado ${event.paymentId}:`, error);
    }
  }

  /**
   * Processa pagamento rejeitado ou cancelado
   * @param event - Evento de pagamento
   */
  private async handleRejectedPayment(event: PaymentEvent): Promise<void> {
    try {
      // Atualizar status do pedido, se existir
      if (event.orderId) {
        await queryHelper.query(
          `UPDATE tab_order
           SET cod_status = 3, -- Assumindo que 3 é o código para "Cancelado"
               updated_at = NOW()
           WHERE ulid_order = $1`,
          [event.orderId]
        );
      }

      // Enviar email de pagamento rejeitado
      await this.sendPaymentRejectedEmail(event);

      // Verificar se há alertas a serem disparados
      await alertService.checkAndCreateAlert(
        'payment_rejected',
        {
          paymentId: event.paymentId,
          orderId: event.orderId,
          value: event.value,
        },
        'Pagamento rejeitado'
      );
    } catch (error) {
      logger.error(`Erro ao processar pagamento rejeitado ${event.paymentId}:`, error);
    }
  }

  /**
   * Envia email de confirmação de pagamento
   * @param event - Evento de pagamento
   */
  private async sendPaymentConfirmationEmail(event: PaymentEvent): Promise<void> {
    try {
      // Obter dados do usuário
      if (!event.userId) {
        return;
      }

      const user = await queryHelper.queryOne(
        'SELECT name, email FROM tab_user WHERE ulid_user = $1',
        [event.userId]
      );

      if (!user) {
        logger.warn(`Usuário não encontrado: ${event.userId}`);
        return;
      }

      // Enviar email
      await emailService.sendEmail({
        to: user.email,
        subject: 'Pagamento Confirmado',
        template: 'payment-confirmed',
        data: {
          userName: user.name,
          paymentId: event.paymentId,
          orderId: event.orderId,
          value: event.value,
          paymentType: event.paymentType,
        },
      });
    } catch (error) {
      logger.error(`Erro ao enviar email de confirmação de pagamento ${event.paymentId}:`, error);
    }
  }

  /**
   * Envia email de pagamento rejeitado
   * @param event - Evento de pagamento
   */
  private async sendPaymentRejectedEmail(event: PaymentEvent): Promise<void> {
    try {
      // Obter dados do usuário
      if (!event.userId) {
        return;
      }

      const user = await queryHelper.queryOne(
        'SELECT name, email FROM tab_user WHERE ulid_user = $1',
        [event.userId]
      );

      if (!user) {
        logger.warn(`Usuário não encontrado: ${event.userId}`);
        return;
      }

      // Enviar email
      await emailService.sendEmail({
        to: user.email,
        subject: 'Pagamento Não Aprovado',
        template: 'payment-rejected',
        data: {
          userName: user.name,
          paymentId: event.paymentId,
          orderId: event.orderId,
          value: event.value,
          paymentType: event.paymentType,
          reason: event.status === PaymentStatus.REJECTED ? 'rejeitado' : 'cancelado',
        },
      });
    } catch (error) {
      logger.error(`Erro ao enviar email de pagamento rejeitado ${event.paymentId}:`, error);
    }
  }
}

/**
 * Inicializa o consumidor de eventos de pagamento
 */
export async function initPaymentEventConsumer(): Promise<void> {
  try {
    // Registrar handlers
    eventConsumerService.registerHandler(
      'payment.transaction.created',
      new PaymentCreatedHandler()
    );

    eventConsumerService.registerHandler(
      'payment.transaction.updated',
      new PaymentUpdatedHandler()
    );

    // Iniciar consumidor
    await eventConsumerService.init({
      topics: [
        'payment.transaction.created',
        'payment.transaction.updated',
        'payment.transaction.failed',
        'payment.refund.requested',
        'payment.refund.processed',
        'payment.webhook.received',
      ],
      groupId: 'payment-consumer-group',
    });

    logger.info('Consumidor de eventos de pagamento inicializado');
  } catch (error) {
    logger.error('Erro ao inicializar consumidor de eventos de pagamento:', error);
    throw error;
  }
}
