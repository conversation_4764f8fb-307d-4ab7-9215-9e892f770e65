/**
 * Script para execução da reconciliação diária de transações
 *
 * Este script pode ser executado manualmente ou agendado para execução automática
 * através de um cron job ou serviço de agendamento.
 *
 * Exemplo de uso com cron:
 * 0 1 * * * /usr/bin/node /path/to/project/dist/scripts/dailyReconciliation.js
 */

import { queryHelper } from '../db/queryHelper';
import { email } from '../helpers/emailHelper';
import { reconciliationService } from '../services/reconciliationService';
import { logger } from '../utils/logger';

/**
 * Função principal para execução da reconciliação
 */
async function runReconciliation() {
  try {
    logger.info('Iniciando script de reconciliação diária');

    // Executar reconciliação
    const result = await reconciliationService.runDailyReconciliation();

    // Registrar resultado
    logger.info(
      `Reconciliação concluída: ${result.matchedTransactions}/${result.totalTransactions} transações correspondentes`
    );
    logger.info(`Discrepâncias encontradas: ${result.discrepancies.length}`);

    // Enviar relatório por e-mail para administradores
    if (result.success) {
      await sendReconciliationReport(result);
    }

    // Encerrar conexões
    await queryHelper.disconnect();

    // Encerrar script com sucesso
    process.exit(0);
  } catch (error) {
    logger.error('Erro durante execução da reconciliação diária:', error);

    // Enviar alerta de falha
    await sendFailureAlert(error);

    // Encerrar conexões
    await queryHelper.disconnect();

    // Encerrar script com erro
    process.exit(1);
  }
}

/**
 * Envia relatório de reconciliação por e-mail
 * @param result - Resultado da reconciliação
 */
async function sendReconciliationReport(result: any) {
  try {
    // Obter lista de administradores
    const admins = await queryHelper.query(
      `SELECT email FROM tab_user WHERE role = 'admin' AND active = true`
    );

    if (admins.rows.length === 0) {
      logger.warn('Nenhum administrador encontrado para envio do relatório');
      return;
    }

    const adminEmails = admins.rows.map((admin: any) => admin.email);

    // Preparar dados para o template
    const templateData = {
      reconciliationDate: result.reconciliationDate.toLocaleString('pt-BR'),
      totalTransactions: result.totalTransactions,
      matchedTransactions: result.matchedTransactions,
      discrepancyCount: result.discrepancies.length,
      discrepancies: result.discrepancies.slice(0, 10), // Limitar a 10 discrepâncias no e-mail
      hasMoreDiscrepancies: result.discrepancies.length > 10,
      remainingDiscrepancies: result.discrepancies.length - 10,
      adminUrl: process.env.ADMIN_URL || 'https://admin.estacaoalfabetizacao.com.br',
    };

    // Enviar e-mail
    await email.sendEmail({
      to: adminEmails,
      subject: `Relatório de Reconciliação Financeira - ${new Date().toLocaleDateString('pt-BR')}`,
      template: 'reconciliation-report',
      data: templateData,
    });

    logger.info(`Relatório de reconciliação enviado para ${adminEmails.join(', ')}`);
  } catch (error) {
    logger.error('Erro ao enviar relatório de reconciliação:', error);
  }
}

/**
 * Envia alerta de falha na reconciliação
 * @param error - Erro ocorrido
 */
async function sendFailureAlert(error: any) {
  try {
    // Obter lista de administradores
    const admins = await queryHelper.query(
      `SELECT email FROM tab_user WHERE role = 'admin' AND active = true`
    );

    if (admins.rows.length === 0) {
      logger.warn('Nenhum administrador encontrado para envio do alerta');
      return;
    }

    const adminEmails = admins.rows.map((admin: any) => admin.email);

    // Enviar e-mail de alerta
    await email.sendEmail({
      to: adminEmails,
      subject: `ALERTA: Falha na Reconciliação Financeira - ${new Date().toLocaleDateString('pt-BR')}`,
      template: 'reconciliation-failure',
      data: {
        errorMessage: error.message || 'Erro desconhecido',
        errorStack: error.stack || '',
        timestamp: new Date().toLocaleString('pt-BR'),
      },
    });

    logger.info(`Alerta de falha enviado para ${adminEmails.join(', ')}`);
  } catch (alertError) {
    logger.error('Erro ao enviar alerta de falha:', alertError);
  }
}

// Executar reconciliação
runReconciliation();
