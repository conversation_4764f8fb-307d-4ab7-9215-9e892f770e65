---
import AdminSidebar from '@components/admin/AdminSidebar.astro';
import WebhookManager from '@components/admin/WebhookManager.astro';
import Layout from '@layouts/Layout.astro';
import { isAuthenticated } from '@middleware/authMiddleware';

// Verificar autenticação
const authResult = await isAuthenticated(Astro);

// Redirecionar para login se não estiver autenticado
if (!authResult.isAuthenticated) {
  return Astro.redirect('/login?redirect=/admin/webhooks');
}

// Verificar se o usuário é administrador
if (authResult.user?.role !== 'admin') {
  return Astro.redirect('/dashboard?error=Acesso+negado');
}

// Título da página
const title = 'Gerenciamento de Webhooks';
---

<Layout title={title}>
  <div class="flex flex-col md:flex-row min-h-screen bg-gray-100">
    <AdminSidebar activeItem="webhooks" />
    
    <main class="flex-1 p-6">
      <div class="mb-6">
        <h1 class="text-2xl font-bold">{title}</h1>
        <p class="text-gray-600">Configure e gerencie webhooks para receber notificações de pagamentos.</p>
      </div>
      
      <div class="grid grid-cols-1 gap-6">
        <WebhookManager />
        
        <div class="card bg-base-100 shadow-xl">
          <div class="card-body">
            <h2 class="card-title">Documentação de Webhooks</h2>
            <p>Os webhooks são usados para receber notificações em tempo real sobre eventos de pagamento.</p>
            
            <div class="mt-4">
              <h3 class="text-lg font-semibold mb-2">Tipos de Notificações</h3>
              <ul class="list-disc pl-5 space-y-2">
                <li>
                  <strong>PIX Recebido</strong> - Notificação quando um PIX é recebido na sua conta.
                </li>
                <li>
                  <strong>PIX Enviado</strong> - Notificação quando um PIX é enviado da sua conta.
                </li>
                <li>
                  <strong>Devolução Recebida</strong> - Notificação quando uma devolução de PIX é recebida.
                </li>
                <li>
                  <strong>Devolução Enviada</strong> - Notificação quando uma devolução de PIX é enviada.
                </li>
              </ul>
            </div>
            
            <div class="mt-4">
              <h3 class="text-lg font-semibold mb-2">Configuração</h3>
              <p>Para configurar o webhook, siga os passos abaixo:</p>
              <ol class="list-decimal pl-5 space-y-2 mt-2">
                <li>Certifique-se de que a URL do webhook está acessível publicamente.</li>
                <li>Clique no botão "Configurar Webhook" acima.</li>
                <li>Verifique se o status do webhook está "Configurado".</li>
                <li>Teste o webhook para garantir que está funcionando corretamente.</li>
              </ol>
            </div>
            
            <div class="mt-4">
              <h3 class="text-lg font-semibold mb-2">Segurança</h3>
              <p>
                Os webhooks são protegidos por assinatura. Cada notificação inclui um cabeçalho
                <code>X-Efipay-Signature</code> que contém uma assinatura HMAC SHA-256 do corpo da requisição.
                Essa assinatura é verificada automaticamente pelo sistema.
              </p>
            </div>
          </div>
        </div>
      </div>
    </main>
  </div>
</Layout>
