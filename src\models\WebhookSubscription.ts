// src/models/WebhookSubscription.ts
/**
 * Modelo para assinaturas de webhooks
 * 
 * Este modelo representa uma assinatura de webhook que permite
 * enviar notificações HTTP para sistemas externos quando
 * eventos específicos ocorrem na aplicação.
 */

/**
 * Enum para status de webhook
 */
export enum WebhookStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  FAILED = 'failed'
}

/**
 * Enum para eventos de webhook
 */
export enum WebhookEvent {
  // Eventos de pagamento
  PAYMENT_CREATED = 'payment.created',
  PAYMENT_UPDATED = 'payment.updated',
  PAYMENT_COMPLETED = 'payment.completed',
  PAYMENT_FAILED = 'payment.failed',
  PAYMENT_REFUNDED = 'payment.refunded',
  
  // Eventos de pedido
  ORDER_CREATED = 'order.created',
  ORDER_UPDATED = 'order.updated',
  ORDER_COMPLETED = 'order.completed',
  ORDER_CANCELLED = 'order.cancelled',
  
  // Eventos de usuário
  USER_CREATED = 'user.created',
  USER_UPDATED = 'user.updated',
  
  // Eventos de produto
  PRODUCT_CREATED = 'product.created',
  PRODUCT_UPDATED = 'product.updated',
  PRODUCT_DELETED = 'product.deleted'
}

/**
 * Interface para cabeçalhos HTTP personalizados
 */
export interface WebhookHeaders {
  [key: string]: string;
}

/**
 * Interface para modelo de assinatura de webhook
 */
export interface WebhookSubscription {
  id: string;
  name: string;
  url: string;
  events: WebhookEvent[];
  status: WebhookStatus;
  secretKey: string;
  description?: string;
  headers?: WebhookHeaders;
  createdBy?: string;
  createdAt: Date;
  updatedAt: Date;
  lastUsedAt?: Date;
}

/**
 * Interface para criação de assinatura de webhook
 */
export interface CreateWebhookSubscriptionDTO {
  name: string;
  url: string;
  events: WebhookEvent[];
  secretKey?: string; // Opcional, pode ser gerado automaticamente
  description?: string;
  headers?: WebhookHeaders;
  createdBy?: string;
}

/**
 * Interface para atualização de assinatura de webhook
 */
export interface UpdateWebhookSubscriptionDTO {
  name?: string;
  url?: string;
  events?: WebhookEvent[];
  status?: WebhookStatus;
  secretKey?: string;
  description?: string;
  headers?: WebhookHeaders;
}

/**
 * Interface para filtros de consulta de webhooks
 */
export interface WebhookSubscriptionFilters {
  status?: WebhookStatus;
  event?: WebhookEvent;
  createdBy?: string;
}

/**
 * Interface para modelo de entrega de webhook
 */
export interface WebhookDelivery {
  id: string;
  subscriptionId: string;
  event: WebhookEvent;
  payload: Record<string, any>;
  responseCode?: number;
  responseBody?: string;
  status: WebhookDeliveryStatus;
  errorMessage?: string;
  attemptCount: number;
  createdAt: Date;
  completedAt?: Date;
}

/**
 * Enum para status de entrega de webhook
 */
export enum WebhookDeliveryStatus {
  PENDING = 'pending',
  SUCCESS = 'success',
  FAILED = 'failed',
  RETRYING = 'retrying'
}

/**
 * Interface para criação de entrega de webhook
 */
export interface CreateWebhookDeliveryDTO {
  subscriptionId: string;
  event: WebhookEvent;
  payload: Record<string, any>;
}

/**
 * Interface para atualização de entrega de webhook
 */
export interface UpdateWebhookDeliveryDTO {
  responseCode?: number;
  responseBody?: string;
  status: WebhookDeliveryStatus;
  errorMessage?: string;
  attemptCount?: number;
  completedAt?: Date;
}
