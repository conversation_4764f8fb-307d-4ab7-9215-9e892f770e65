---
/**
 * Componente de Provedor de <PERSON>
 *
 * Gerencia o sistema de temas da aplicação, permitindo a alternância entre temas claros e escuros,
 * bem como temas personalizados.
 */

interface Props {
  defaultTheme?: string;
  storageKey?: string;
  enableSystem?: boolean;
  enablePersistence?: boolean;
  forcedTheme?: string;
  disableTransitionOnChange?: boolean;
  themes?: string[];
}

const {
  defaultTheme = 'light',
  storageKey = 'theme',
  enableSystem = true,
  enablePersistence = true,
  forcedTheme,
  disableTransitionOnChange = false,
  themes = ['light', 'dark', 'estacao'],
} = Astro.props;

// Determinar o tema inicial
const initialTheme = defaultTheme;

// Verificar se há um tema forçado
const hasForcedTheme = !!forcedTheme;

// Gerar ID único para o script
const scriptId = `theme-provider-${Math.random().toString(36).substring(2, 9)}`;
---

<div id="theme-provider">
  <slot />
</div>

<script define:vars={{ 
  defaultTheme, 
  storageKey, 
  enableSystem, 
  enablePersistence, 
  forcedTheme, 
  disableTransitionOnChange, 
  themes,
  hasForcedTheme,
  scriptId
}}>
  // Função para obter o tema atual
  function getTheme() {
    if (hasForcedTheme) {
      return forcedTheme;
    }
    
    if (enablePersistence) {
      const storedTheme = localStorage.getItem(storageKey);
      if (storedTheme && themes.includes(storedTheme)) {
        return storedTheme;
      }
    }
    
    if (enableSystem) {
      const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
      return systemTheme;
    }
    
    return defaultTheme;
  }
  
  // Função para definir o tema
  function setTheme(theme) {
    const root = document.documentElement;
    
    // Remover classes de tema anteriores
    themes.forEach(t => {
      root.classList.remove(`theme-${t}`);
      root.removeAttribute('data-theme');
    });
    
    // Adicionar nova classe de tema
    root.classList.add(`theme-${theme}`);
    root.setAttribute('data-theme', theme);
    
    // Persistir tema se habilitado
    if (enablePersistence && !hasForcedTheme) {
      localStorage.setItem(storageKey, theme);
    }
    
    // Disparar evento de mudança de tema
    window.dispatchEvent(new CustomEvent('themechange', { detail: { theme } }));
  }
  
  // Função para alternar entre temas
  function toggleTheme() {
    const currentTheme = getTheme();
    const currentIndex = themes.indexOf(currentTheme);
    const nextIndex = (currentIndex + 1) % themes.length;
    const nextTheme = themes[nextIndex];
    
    setTheme(nextTheme);
    return nextTheme;
  }
  
  // Inicializar tema
  function initTheme() {
    // Desativar transições durante a mudança de tema
    if (disableTransitionOnChange) {
      document.documentElement.classList.add('disable-transitions');
    }
    
    // Definir tema inicial
    const initialTheme = getTheme();
    setTheme(initialTheme);
    
    // Reativar transições
    if (disableTransitionOnChange) {
      setTimeout(() => {
        document.documentElement.classList.remove('disable-transitions');
      }, 0);
    }
    
    // Observar mudanças no sistema
    if (enableSystem && !hasForcedTheme) {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
      
      const handleChange = () => {
        const storedTheme = localStorage.getItem(storageKey);
        if (!storedTheme) {
          setTheme(mediaQuery.matches ? 'dark' : 'light');
        }
      };
      
      mediaQuery.addEventListener('change', handleChange);
    }
  }
  
  // Expor API para outros scripts
  window.themeProvider = {
    getTheme,
    setTheme,
    toggleTheme,
    themes
  };
  
  // Inicializar quando o DOM estiver pronto
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initTheme);
  } else {
    initTheme();
  }
</script>

<style>
  /* Desativar transições durante mudança de tema */
  :global(.disable-transitions) * {
    transition: none !important;
  }
  
  /* Estilos para acessibilidade */
  @media (prefers-reduced-motion) {
    :global(html) {
      transition: none !important;
    }
  }
</style>
