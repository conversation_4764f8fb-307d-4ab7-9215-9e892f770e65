/**
 * Implementação base para loggers
 *
 * Esta classe abstrata implementa funcionalidades comuns a todos os loggers
 * e serve como base para implementações concretas.
 */

import { LogLevel, LogMetadata, Logger, LoggerConfig } from '../../domain/interfaces/Logger';

/**
 * Classe base abstrata para loggers
 */
export abstract class BaseLogger implements Logger {
  /**
   * Contexto do logger
   */
  protected readonly context: string;

  /**
   * Configuração do logger
   */
  protected readonly config: LoggerConfig;

  /**
   * Cria uma nova instância de BaseLogger
   * @param context - Contexto do logger
   * @param config - Configuração do logger
   */
  constructor(context: string, config: LoggerConfig) {
    this.context = context;
    this.config = config;
  }

  /**
   * Registra uma mensagem de debug
   * @param message - Mensagem a ser registrada
   * @param metadata - Metadados adicionais
   */
  public debug(message: string, metadata?: LogMetadata): void {
    this.log(LogLevel.DEBUG, message, metadata);
  }

  /**
   * Registra uma mensagem informativa
   * @param message - Mensagem a ser registrada
   * @param metadata - Metadados adicionais
   */
  public info(message: string, metadata?: LogMetadata): void {
    this.log(LogLevel.INFO, message, metadata);
  }

  /**
   * Registra uma mensagem de aviso
   * @param message - Mensagem a ser registrada
   * @param metadata - Metadados adicionais
   */
  public warn(message: string, metadata?: LogMetadata): void {
    this.log(LogLevel.WARN, message, metadata);
  }

  /**
   * Registra uma mensagem de erro
   * @param message - Mensagem a ser registrada
   * @param metadata - Metadados adicionais
   */
  public error(message: string, metadata?: LogMetadata): void {
    this.log(LogLevel.ERROR, message, metadata);
  }

  /**
   * Registra uma mensagem de erro fatal
   * @param message - Mensagem a ser registrada
   * @param metadata - Metadados adicionais
   */
  public fatal(message: string, metadata?: LogMetadata): void {
    this.log(LogLevel.FATAL, message, metadata);
  }

  /**
   * Registra uma mensagem com nível personalizado
   * @param level - Nível de log
   * @param message - Mensagem a ser registrada
   * @param metadata - Metadados adicionais
   */
  public log(level: LogLevel, message: string, metadata?: LogMetadata): void {
    // Verificar se o nível de log é suficiente
    if (!this.shouldLog(level)) {
      return;
    }

    // Preparar entrada de log
    const logEntry = this.formatLogEntry(level, message, metadata);

    // Registrar a entrada
    this.writeLog(level, logEntry);
  }

  /**
   * Verifica se uma mensagem deve ser registrada com base no nível
   * @param level - Nível de log
   * @returns true se a mensagem deve ser registrada
   */
  protected shouldLog(level: LogLevel): boolean {
    const levels = [LogLevel.DEBUG, LogLevel.INFO, LogLevel.WARN, LogLevel.ERROR, LogLevel.FATAL];

    const configLevelIndex = levels.indexOf(this.config.minLevel);
    const messageLevelIndex = levels.indexOf(level);

    return messageLevelIndex >= configLevelIndex;
  }

  /**
   * Formata uma entrada de log
   * @param level - Nível de log
   * @param message - Mensagem a ser registrada
   * @param metadata - Metadados adicionais
   * @returns Entrada de log formatada
   */
  protected formatLogEntry(level: LogLevel, message: string, metadata?: LogMetadata): any {
    const timestamp = new Date().toISOString();

    const logEntry: Record<string, any> = {
      level,
      message,
    };

    if (this.config.includeTimestamp !== false) {
      logEntry.timestamp = timestamp;
    }

    if (this.config.includeContext !== false) {
      logEntry.context = this.context;
    }

    if (metadata) {
      logEntry.metadata = metadata;
    }

    return logEntry;
  }

  /**
   * Escreve uma entrada de log
   * @param level - Nível de log
   * @param logEntry - Entrada de log formatada
   */
  protected abstract writeLog(level: LogLevel, logEntry: any): void;
}
