---
import InputText from '@components/form/InputText.astro';
import AdminLayout from '@layouts/AdminLayout.astro';
---
<AdminLayout title="Nota Fiscal">
  <template>
    <div class="search-container flex justify-between mb-4">
      <InputText id="search-input" label="Pesquisar" name="search" placeholder="Pesquisar faturas..." />
      <div>
        <button class="btn btn-primary" id="handleSearch">Pesquisar</button>
        <!-- <button class="btn btn-secondary ml-2" id="navigateToNewInvoice">Novo</button> -->
      </div>
    </div>

    <ul class="invoice-list list-disc pl-5">
    </ul>
  </template>
  
  <style>
    .search-container {
      display: flex;
      justify-content: space-between;
      margin-bottom: 20px;
    }
    .invoice-list {
      list-style: none;
      padding: 0;
    }
    .invoice-list li {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10px;
    }
  </style>
</AdminLayout>

<script>
  import { actions } from "astro:actions";

  interface Invoice {
    ulid_invoice: string; 
    ulid_order: string;
    ulid_user: string;
    value: number;
    tax: number;
    file: string; 
  }

  let invoices: Invoice[] = [];

  const handleSearch = async () => {
    const searchInputElement = document.getElementById('search-input') as HTMLInputElement;
    if (searchInputElement) {
      const searchInput = searchInputElement.value;
      if (searchInput !== null && searchInput !== undefined) {
        // Lógica para buscar faturas com base no input
        const param = { filter: "value", value: searchInput };
        loadInvoices(param);
      } else {
        console.error('O valor do input de pesquisa é nulo ou indefinido.');
      }
    } else {
      console.error('Elemento de input de pesquisa não encontrado.');
    }
  };

  // function navigateToNewInvoice() {
  //   // Lógica para redirecionar para a nova fatura
  //   window.location.href = '/admin/financial/invoice/new';
  // }

  function handleView(ulid_invoice: string) {
    window.location.href = `/admin/financial/invoice/${ulid_invoice}`;
  }

  function handleCancel(ulid_invoice: string) {
    throw new Error("Function not implemented.");
  }

  async function loadInvoices(param: any) {
    const result = await actions.invoiceAction.read(param);
    invoices = result.data as Invoice[];

    // Apagar a lista existente
    const invoiceList = document.querySelector('.invoice-list');
    if (invoiceList) {
      invoiceList.innerHTML = '';

      // Inserir o cabeçalho
      const titleRow = document.createElement('li');
      titleRow.innerHTML = `
          <strong>Número</strong>
          <strong>Data</strong>
          <strong>Valor</strong>
          <strong>Taxa</strong>
          <strong>Arquivo</strong>
          <strong>Ações</strong>
      `;
      invoiceList.appendChild(titleRow);

      if (invoices.length === 0) {
        const noDataMessage = document.createElement('li');
        noDataMessage.textContent = 'Nenhuma fatura encontrada.';
        invoiceList.appendChild(noDataMessage);
        return;
      }

      // Inserir novas faturas
      invoices.forEach((invoice: Invoice) => {
        const listItem = document.createElement('li');
        listItem.innerHTML = `
            <span>${invoice.ulid_invoice}</span>
            <span>${invoice.ulid_order}</span>
            <span>${invoice.ulid_user}</span>
            <span>${invoice.value}</span>
            <span>${invoice.tax}</span>
            <span>${invoice.file}</span>
            <div>
                <button type="button" class="btn btn-warning mr-2" onclick="handleView('${invoice.ulid_invoice}')">Visualizar</button>
                <button type="button" class="btn btn-error" onclick="handleCancel('${invoice.ulid_invoice}')">Cancelar</button>
            </div>
        `;
        invoiceList.appendChild(listItem);
      });
    }
  }

  loadInvoices({ filter: "readAll" });
</script>