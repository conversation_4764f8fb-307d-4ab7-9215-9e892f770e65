/**
 * Update Content Status Use Case
 *
 * Caso de uso para atualizar o status de um conteúdo.
 * Parte da implementação da tarefa 8.8.3 - Gestão de conteúdo
 */

import { Content, ContentStatus } from '../../entities/Content';
import { ContentRepository } from '../../repositories/ContentRepository';

export interface UpdateContentStatusRequest {
  id: string;
  status: ContentStatus;
  scheduledAt?: Date;
  userId: string;
}

export interface UpdateContentStatusResponse {
  success: boolean;
  data?: Content;
  error?: string;
}

export class UpdateContentStatusUseCase {
  constructor(private contentRepository: ContentRepository) {}

  async execute(request: UpdateContentStatusRequest): Promise<UpdateContentStatusResponse> {
    try {
      // Validar os dados de entrada
      if (!request.id) {
        return {
          success: false,
          error: 'O ID do conteúdo é obrigatório.',
        };
      }

      if (!request.status) {
        return {
          success: false,
          error: 'O status é obrigatório.',
        };
      }

      if (!request.userId) {
        return {
          success: false,
          error: 'O ID do usuário é obrigatório.',
        };
      }

      // Obter conteúdo
      const content = await this.contentRepository.getById(request.id);

      if (!content) {
        return {
          success: false,
          error: `Conteúdo com ID ${request.id} não encontrado.`,
        };
      }

      // Atualizar status
      switch (request.status) {
        case 'published':
          content.publish(request.userId);
          break;

        case 'scheduled':
          if (!request.scheduledAt) {
            return {
              success: false,
              error: 'Data de agendamento é obrigatória para conteúdo agendado.',
            };
          }

          if (request.scheduledAt <= new Date()) {
            return {
              success: false,
              error: 'Data de agendamento deve ser futura.',
            };
          }

          content.schedule(request.scheduledAt, request.userId);
          break;

        case 'archived':
          content.archive(request.userId);
          break;

        case 'review':
          content.updateStatus('review', request.userId);
          break;

        case 'draft':
          content.updateStatus('draft', request.userId);
          break;

        default:
          return {
            success: false,
            error: `Status '${request.status}' inválido.`,
          };
      }

      // Salvar conteúdo
      const updatedContent = await this.contentRepository.update(content);

      return {
        success: true,
        data: updatedContent,
      };
    } catch (error) {
      console.error('Erro ao atualizar status do conteúdo:', error);

      return {
        success: false,
        error:
          error instanceof Error
            ? error.message
            : 'Erro desconhecido ao atualizar status do conteúdo.',
      };
    }
  }
}
