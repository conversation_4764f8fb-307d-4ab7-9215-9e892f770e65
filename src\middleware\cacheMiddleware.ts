/**
 * Middleware para cache de respostas HTTP
 * Implementa cache de respostas para rotas específicas
 */

import { cacheConfig } from '@config/cache';
import { cacheService } from '@services/cacheService';
import { logger } from '@utils/logger';
import type { APIContext, MiddlewareNext } from 'astro';

/**
 * Interface para opções de cache de resposta
 */
export interface ResponseCacheOptions {
  ttl?: number;
  varyByQuery?: boolean;
  varyByUser?: boolean;
  varyByHeaders?: string[];
}

/**
 * Middleware para cache de respostas HTTP
 * @param options - Opções de cache
 * @returns Middleware para Astro
 */
export function cacheResponse(options: ResponseCacheOptions = {}) {
  return async (context: APIContext, next: MiddlewareNext) => {
    // Verificar se é uma requisição GET
    if (context.request.method !== 'GET') {
      return next();
    }

    try {
      // Gerar chave de cache
      const cacheKey = generateCacheKey(context, options);

      // Verificar se existe no cache
      const cachedResponse = await cacheService.get<{
        status: number;
        headers: Record<string, string>;
        body: string;
      }>('query', cacheKey);

      // Se encontrou no cache, retornar resposta cacheada
      if (cachedResponse) {
        if (cacheConfig.logging.enabled) {
          logger.debug(`Cache HIT para rota: ${context.request.url}`);
        }

        // Reconstruir resposta do cache
        return new Response(cachedResponse.body, {
          status: cachedResponse.status,
          headers: cachedResponse.headers,
        });
      }

      if (cacheConfig.logging.enabled) {
        logger.debug(`Cache MISS para rota: ${context.request.url}`);
      }

      // Executar próximo middleware/handler
      const response = await next();

      // Verificar se a resposta é cacheável
      if (isCacheable(response)) {
        // Clonar resposta para não modificar a original
        const clonedResponse = response.clone();

        // Extrair dados da resposta
        const status = clonedResponse.status;
        const headers: Record<string, string> = {};

        clonedResponse.headers.forEach((value, key) => {
          headers[key] = value;
        });

        const body = await clonedResponse.text();

        // Armazenar no cache
        const ttl = options.ttl || cacheConfig.policies.ttlByType.query;

        await cacheService.set('query', cacheKey, { status, headers, body }, { ttl });

        if (cacheConfig.logging.enabled) {
          logger.debug(`Cache SET para rota: ${context.request.url} (TTL: ${ttl}s)`);
        }
      }

      return response;
    } catch (error) {
      logger.error('Erro no middleware de cache:', error);

      // Em caso de erro, continuar sem cache
      return next();
    }
  };
}

/**
 * Gera uma chave de cache para a requisição
 * @param context - Contexto da API
 * @param options - Opções de cache
 * @returns Chave de cache
 */
function generateCacheKey(context: APIContext, options: ResponseCacheOptions): string {
  const { request } = context;
  const url = new URL(request.url);

  // Componentes básicos da chave
  let key = `${url.pathname}`;

  // Incluir query params se necessário
  if (options.varyByQuery && url.search) {
    key += url.search;
  }

  // Incluir usuário se necessário
  if (options.varyByUser && context.locals.user) {
    key += `:user:${context.locals.user.ulid_user}`;
  }

  // Incluir headers específicos se necessário
  if (options.varyByHeaders && options.varyByHeaders.length > 0) {
    for (const header of options.varyByHeaders) {
      const value = request.headers.get(header);
      if (value) {
        key += `:${header}:${value}`;
      }
    }
  }

  return key;
}

/**
 * Verifica se uma resposta é cacheável
 * @param response - Resposta HTTP
 * @returns Verdadeiro se a resposta é cacheável
 */
function isCacheable(response: Response): boolean {
  // Verificar status (apenas 200 OK é cacheado)
  if (response.status !== 200) {
    return false;
  }

  // Verificar Cache-Control
  const cacheControl = response.headers.get('Cache-Control');
  if (
    cacheControl &&
    (cacheControl.includes('no-store') ||
      cacheControl.includes('no-cache') ||
      cacheControl.includes('private'))
  ) {
    return false;
  }

  return true;
}
