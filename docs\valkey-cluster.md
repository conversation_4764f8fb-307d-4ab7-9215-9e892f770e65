# Cluster Valkey

## Visão Geral

Este documento descreve a configuração e operação do cluster Valkey implementado no projeto Estação da Alfabetização. O Valkey é um fork do Redis com melhorias de performance e segurança, utilizado como sistema de cache e armazenamento de dados temporários.

## Arquitetura do Cluster

O cluster Valkey é configurado para alta disponibilidade e escalabilidade, utilizando múltiplos nós distribuídos em diferentes zonas de disponibilidade.

### Topologia

A topologia do cluster segue o modelo de sharding com replicação:

- **Nós Master**: Responsáveis por receber operações de escrita e leitura
- **Nós Replica**: Réplicas dos nós master, utilizados para leitura e failover
- **Shards**: Divisão dos dados em partições (slots) distribuídas entre os nós master

### Configuração de Produção

Em ambiente de produção, o cluster é composto por:

- 3 nós master (distribuídos em 3 zonas de disponibilidade)
- 3 nós replica (um para cada master, em zonas diferentes)
- 16384 slots distribuídos entre os nós master

```
┌─────────────┐     ┌─────────────┐     ┌─────────────┐
│  Master 1   │     │  Master 2   │     │  Master 3   │
│  (Zona A)   │     │  (Zona B)   │     │  (Zona C)   │
│ Slots 0-5460│     │Slots 5461-10922│   │Slots 10923-16383│
└──────┬──────┘     └──────┬──────┘     └──────┬──────┘
       │                   │                   │
       │ Replicação        │ Replicação        │ Replicação
       │                   │                   │
┌──────▼──────┐     ┌──────▼──────┐     ┌──────▼──────┐
│  Replica 1  │     │  Replica 2  │     │  Replica 3  │
│  (Zona C)   │     │  (Zona A)   │     │  (Zona B)   │
└─────────────┘     └─────────────┘     └─────────────┘
```

### Especificações dos Nós

Cada nó do cluster possui as seguintes especificações:

| Recurso | Especificação |
|---------|---------------|
| CPU     | 4 vCPUs       |
| Memória | 16 GB RAM     |
| Disco   | 50 GB SSD     |
| Rede    | 1 Gbps        |

## Mecanismo de Failover

O cluster implementa um mecanismo de failover automático para garantir alta disponibilidade:

1. **Detecção de Falhas**: Os nós monitoram uns aos outros através de heartbeats
2. **Quórum**: É necessário um quórum de nós para confirmar a falha de um nó master
3. **Eleição**: Uma réplica é promovida a master automaticamente
4. **Reconfiguração**: Os clientes são notificados da mudança de topologia

### Parâmetros de Failover

| Parâmetro | Valor | Descrição |
|-----------|-------|-----------|
| `cluster-node-timeout` | 3000 ms | Tempo para considerar um nó como falho |
| `cluster-replica-validity-factor` | 10 | Fator de validade para réplicas |
| `cluster-migration-barrier` | 1 | Número mínimo de réplicas para migração |
| `cluster-require-full-coverage` | no | Continuar operando mesmo sem cobertura total |

## Persistência de Dados

O cluster utiliza uma estratégia de persistência híbrida para garantir a durabilidade dos dados:

### RDB (Redis Database)

Snapshots periódicos do conjunto de dados:

- **Frequência**: 
  - A cada 15 minutos se pelo menos 1 chave foi alterada
  - A cada 5 minutos se pelo menos 10 chaves foram alteradas
  - A cada 1 minuto se pelo menos 10000 chaves foram alteradas
- **Compressão**: Habilitada
- **Verificação de Checksum**: Habilitada

### AOF (Append Only File)

Log de comandos que modificam o conjunto de dados:

- **Sincronização**: A cada segundo (`appendfsync everysec`)
- **Reescrita Automática**: Quando o arquivo cresce 100% e tem pelo menos 64MB
- **Preamble RDB**: Habilitado para otimizar carregamento

## Backup e Recuperação

### Estratégia de Backup

- **Backups Automáticos**: Diariamente às 2h da manhã
- **Retenção**: 30 dias
- **Compressão**: Habilitada
- **Armazenamento Externo**: Cópias enviadas para Amazon S3

### Procedimento de Recuperação

Em caso de falha catastrófica, o procedimento de recuperação é:

1. Parar todos os nós do cluster
2. Restaurar arquivos RDB/AOF do backup mais recente
3. Iniciar os nós com a configuração de cluster
4. Verificar a integridade do cluster

## Monitoramento

O cluster é monitorado continuamente através do serviço `valkeyMonitoringService`:

### Métricas Coletadas

- **Memória**: Uso, pico, fragmentação
- **Clientes**: Conexões, bloqueios
- **CPU**: Utilização
- **Cluster**: Estado, slots
- **Persistência**: Status de salvamento
- **Comandos**: Taxa de operações, latência
- **Keyspace**: Chaves, TTL médio, taxa de acertos

### Alertas

Alertas são gerados nas seguintes condições:

| Condição | Severidade | Ação |
|----------|------------|------|
| Uso de memória > 90% | Crítico | Notificação imediata, escala automática |
| Uso de memória > 80% | Aviso | Notificação |
| Fragmentação > 1.5 | Aviso | Notificação |
| Slots não atribuídos | Aviso | Notificação |
| Último salvamento > 60 min | Aviso | Notificação |
| Taxa de acertos < 50% | Aviso | Notificação |

## Operações Comuns

### Verificação de Estado do Cluster

```bash
valkey-cli cluster info
valkey-cli cluster nodes
```

### Adição de Novo Nó

```bash
# 1. Iniciar novo nó com configuração de cluster
valkey-server /etc/valkey/valkey-new.conf

# 2. Adicionar nó ao cluster
valkey-cli --cluster add-node new-node:6379 existing-node:6379

# 3. Rebalancear slots (se for master)
valkey-cli --cluster rebalance existing-node:6379
```

### Remoção de Nó

```bash
# 1. Migrar slots (se for master)
valkey-cli --cluster reshard existing-node:6379

# 2. Remover nó
valkey-cli --cluster del-node existing-node:6379 node-id-to-remove
```

### Rebalanceamento de Slots

```bash
valkey-cli --cluster rebalance existing-node:6379 --weight node1=1 node2=1 node3=1
```

## Configuração de Cliente

Os clientes devem ser configurados para trabalhar com o cluster:

```typescript
// Exemplo de configuração de cliente
const client = createCluster({
  rootNodes: [
    { socket: { host: 'valkey-1', port: 6379 } },
    { socket: { host: 'valkey-2', port: 6379 } },
    { socket: { host: 'valkey-3', port: 6379 } }
  ],
  defaults: {
    username: process.env.VALKEY_USERNAME,
    password: process.env.VALKEY_PASSWORD,
  }
});
```

## Troubleshooting

### Problemas Comuns e Soluções

| Problema | Possível Causa | Solução |
|----------|----------------|---------|
| Nó não se junta ao cluster | Firewall bloqueando portas | Verificar regras de firewall para portas 6379 e 16379 |
| Slots não atribuídos | Falha durante resharding | Usar `valkey-cli --cluster fix` para corrigir |
| Uso de memória alto | Chaves sem expiração | Verificar TTL das chaves e políticas de evicção |
| Latência alta | Comandos bloqueantes | Identificar comandos lentos com `SLOWLOG GET` |
| Falha de persistência | Disco cheio | Verificar espaço em disco e rotação de logs |

### Comandos Úteis para Diagnóstico

```bash
# Verificar estado do cluster
valkey-cli cluster info

# Listar nós do cluster
valkey-cli cluster nodes

# Verificar distribuição de slots
valkey-cli cluster slots

# Verificar estatísticas de memória
valkey-cli info memory

# Verificar estatísticas de persistência
valkey-cli info persistence

# Verificar comandos lentos
valkey-cli slowlog get 10
```

## Referências

- [Documentação oficial do Valkey](https://valkey.io/docs)
- [Especificação do protocolo de cluster Redis](https://redis.io/topics/cluster-spec)
- [Guia de operações de cluster Redis](https://redis.io/topics/cluster-tutorial)
- [Estratégias de persistência Redis](https://redis.io/topics/persistence)
