/**
 * Testes para GoogleAnalyticsService
 *
 * Este arquivo contém testes unitários para o serviço GoogleAnalyticsService.
 * Parte da implementação da tarefa 9.1.1 - Testes unitários
 */

import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import type {
  AnalyticsConfig,
  ConversionData,
  EventData,
  PageViewData,
} from '../../../../src/domain/services/AnalyticsService';
import { GoogleAnalyticsService } from '../../../../src/infrastructure/services/GoogleAnalyticsService';

// Mock para fetch global
global.fetch = vi.fn();

describe('GoogleAnalyticsService', () => {
  let service: GoogleAnalyticsService;
  const config: AnalyticsConfig = {
    trackingId: 'UA-12345678-1',
    anonymizeIp: true,
    cookieDomain: 'auto',
    cookieExpires: 63072000,
    cookieUpdate: true,
    useSecure: true,
    sendUserId: false,
  };

  beforeEach(() => {
    service = new GoogleAnalyticsService();
    vi.resetAllMocks();

    // Mock para localStorage
    Object.defineProperty(window, 'localStorage', {
      value: {
        getItem: vi.fn(),
        setItem: vi.fn(),
        removeItem: vi.fn(),
        clear: vi.fn(),
      },
    });
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('initialize', () => {
    it('should initialize the service with the provided configuration', async () => {
      const result = await service.initialize(config);

      expect(result).toBe(true);
      expect(window.localStorage.setItem).toHaveBeenCalledTimes(2); // clientId and sessionId
    });
  });

  describe('trackPageView', () => {
    beforeEach(async () => {
      await service.initialize(config);
      vi.mocked(global.fetch).mockResolvedValue({
        ok: true,
      } as Response);
    });

    it('should track a page view', async () => {
      const pageViewData: PageViewData = {
        path: '/test-page',
        title: 'Test Page',
        referrer: 'https://example.com',
        userAgent: 'Mozilla/5.0',
      };

      const result = await service.trackPageView(pageViewData);

      expect(result).toBe(true);
      expect(global.fetch).toHaveBeenCalledTimes(1);
      expect(global.fetch).toHaveBeenCalledWith(
        'https://www.google-analytics.com/collect',
        expect.objectContaining({
          method: 'POST',
          body: expect.any(URLSearchParams),
        })
      );

      // Verificar parâmetros enviados
      const fetchCall = vi.mocked(global.fetch).mock.calls[0];
      const body = fetchCall[1]?.body as URLSearchParams;

      expect(body.get('t')).toBe('pageview');
      expect(body.get('dp')).toBe('/test-page');
      expect(body.get('dt')).toBe('Test Page');
      expect(body.get('dr')).toBe('https://example.com');
      expect(body.get('ua')).toBe('Mozilla/5.0');
      expect(body.get('tid')).toBe('UA-12345678-1');
    });

    it('should include custom dimensions if provided', async () => {
      const pageViewData: PageViewData = {
        path: '/test-page',
        title: 'Test Page',
        customDimensions: {
          '1': 'dimension1',
          '2': 'dimension2',
        },
      };

      await service.trackPageView(pageViewData);

      const fetchCall = (global.fetch as TestMockFunction<typeof fetch>).mock.calls[0];
      const body = fetchCall[1].body as URLSearchParams;

      expect(body.get('cd1')).toBe('dimension1');
      expect(body.get('cd2')).toBe('dimension2');
    });

    it('should return false if service is not initialized', async () => {
      const service = new GoogleAnalyticsService();
      const pageViewData: PageViewData = {
        path: '/test-page',
        title: 'Test Page',
      };

      const result = await service.trackPageView(pageViewData);

      expect(result).toBe(false);
      expect(global.fetch).not.toHaveBeenCalled();
    });

    it('should return false if fetch fails', async () => {
      (global.fetch as TestMockFunction<typeof fetch>).mockRejectedValue(
        new Error('Network error')
      );

      const pageViewData: PageViewData = {
        path: '/test-page',
        title: 'Test Page',
      };

      const result = await service.trackPageView(pageViewData);

      expect(result).toBe(false);
    });
  });

  describe('trackEvent', () => {
    beforeEach(async () => {
      await service.initialize(config);
      (global.fetch as TestMockFunction<typeof fetch>).mockResolvedValue({
        ok: true,
      } as Response);
    });

    it('should track an event', async () => {
      const eventData: EventData = {
        category: 'Test Category',
        action: 'Test Action',
        label: 'Test Label',
        value: 123,
        nonInteraction: false,
      };

      const result = await service.trackEvent(eventData);

      expect(result).toBe(true);
      expect(global.fetch).toHaveBeenCalledTimes(1);

      const fetchCall = (global.fetch as TestMockFunction<typeof fetch>).mock.calls[0];
      const body = fetchCall[1].body as URLSearchParams;

      expect(body.get('t')).toBe('event');
      expect(body.get('ec')).toBe('Test Category');
      expect(body.get('ea')).toBe('Test Action');
      expect(body.get('el')).toBe('Test Label');
      expect(body.get('ev')).toBe('123');
      expect(body.get('ni')).toBe('0');
    });

    it('should set nonInteraction to 1 if true', async () => {
      const eventData: EventData = {
        category: 'Test Category',
        action: 'Test Action',
        nonInteraction: true,
      };

      await service.trackEvent(eventData);

      const fetchCall = (global.fetch as TestMockFunction<typeof fetch>).mock.calls[0];
      const body = fetchCall[1].body as URLSearchParams;

      expect(body.get('ni')).toBe('1');
    });
  });

  describe('trackConversion', () => {
    beforeEach(async () => {
      await service.initialize(config);
      (global.fetch as any).mockResolvedValue({
        ok: true,
      });
    });

    it('should track a conversion', async () => {
      const conversionData: ConversionData = {
        goalId: 'goal-123',
        goalName: 'Test Goal',
        value: 99.99,
        currency: 'USD',
      };

      const result = await service.trackConversion(conversionData);

      expect(result).toBe(true);
      expect(global.fetch).toHaveBeenCalledTimes(1);

      const fetchCall = (global.fetch as any).mock.calls[0];
      const body = fetchCall[1].body as URLSearchParams;

      expect(body.get('t')).toBe('event');
      expect(body.get('ec')).toBe('Conversion');
      expect(body.get('ea')).toBe('Test Goal');
      expect(body.get('el')).toBe('goal-123');
      expect(body.get('ev')).toBe('99.99');
    });
  });

  describe('generateTrackingCode', () => {
    beforeEach(async () => {
      await service.initialize(config);
    });

    it('should generate tracking code with the correct configuration', () => {
      const code = service.generateTrackingCode();

      expect(code).toContain('Google Analytics');
      expect(code).toContain(`ga('create', '${config.trackingId}'`);
      expect(code).toContain(`cookieDomain: '${config.cookieDomain}'`);
      expect(code).toContain(`cookieExpires: ${config.cookieExpires}`);
      expect(code).toContain(`cookieUpdate: ${config.cookieUpdate}`);
      expect(code).toContain(`useSecure: ${config.useSecure}`);
      expect(code).toContain(`ga('set', 'anonymizeIp', true)`);
      expect(code).toContain(`ga('send', 'pageview')`);
    });

    it('should include userId if provided and sendUserId is true', async () => {
      const serviceWithUserId = new GoogleAnalyticsService();
      await serviceWithUserId.initialize({
        ...config,
        sendUserId: true,
      });
      serviceWithUserId.setUserId('user-123');

      const code = serviceWithUserId.generateTrackingCode();

      expect(code).toContain(`ga('set', 'userId', 'user-123')`);
    });

    it('should include custom dimensions if provided', async () => {
      const serviceWithDimensions = new GoogleAnalyticsService();
      await serviceWithDimensions.initialize({
        ...config,
        customDimensions: {
          '1': 'dimension1',
          '2': 'dimension2',
        },
      });

      const code = serviceWithDimensions.generateTrackingCode();

      expect(code).toContain(`ga('set', 'dimension1', 'dimension1')`);
      expect(code).toContain(`ga('set', 'dimension2', 'dimension2')`);
    });

    it('should include custom metrics if provided', async () => {
      const serviceWithMetrics = new GoogleAnalyticsService();
      await serviceWithMetrics.initialize({
        ...config,
        customMetrics: {
          '1': 100,
          '2': 200,
        },
      });

      const code = serviceWithMetrics.generateTrackingCode();

      expect(code).toContain(`ga('set', 'metric1', 100)`);
      expect(code).toContain(`ga('set', 'metric2', 200)`);
    });

    it('should return empty string if service is not initialized', () => {
      const uninitializedService = new GoogleAnalyticsService();
      const code = uninitializedService.generateTrackingCode();

      expect(code).toBe('');
    });
  });

  describe('clearData', () => {
    beforeEach(async () => {
      await service.initialize(config);
    });

    it('should clear all tracking data', async () => {
      const result = await service.clearData();

      expect(result).toBe(true);
      expect(window.localStorage.removeItem).toHaveBeenCalledWith('ga_client_id');
      expect(window.localStorage.removeItem).toHaveBeenCalledWith('ga_session_id');
    });
  });
});
