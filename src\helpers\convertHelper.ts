// Função para converter um arquivo em Base64
function fileToBase64(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();

    // Quando a leitura do arquivo for concluída
    reader.onloadend = () => {
      // O resultado é uma string Base64
      resolve(reader.result as string);
    };

    // Se ocorrer um erro durante a leitura
    reader.onerror = () => {
      reject(new Error('Erro ao ler o arquivo'));
    };

    // Lê o arquivo como uma URL de dados (Base64)
    reader.readAsDataURL(file);
  });
}

export const convert = {
  fileToBase64,
};
