---
import { actions } from 'astro:actions';
import ControlButtons from '@components/form/ControlButtons.astro';
import FormBase from '@components/form/FormBase.astro';
import InputHidden from '@components/form/InputHidden.astro';
import InputText from '@components/form/InputText.astro';
import BaseLayout from '@layouts/BaseLayout.astro';

const formValidation = `
  const statusInput = form.querySelector('input[name="status"]');
  if (!statusInput || !statusInput.value.trim()) {
      alert('O status de pagamento é obrigatório');
      return false;
  }
  return true;
`;
---
<BaseLayout title="Formulário de Status de Pagamento">
  <div class="container mx-auto p-4">Novo Status de Pagamento</div>

  <FormBase
    action={actions.paymentStatusAction.create}
    formType="paymentStatus"
    onSubmitValidation={formValidation}
  >
    <InputHidden field="ulid_parent" id="ulid_parent" />
    <InputText 
      label="Status" 
      name="status" 
      value="" 
      required={true} 
    />
    <ControlButtons 
      saveLabel="Criar"
      cancelHref="/admin/register/payment/status"
    />
  </FormBase>
</BaseLayout>