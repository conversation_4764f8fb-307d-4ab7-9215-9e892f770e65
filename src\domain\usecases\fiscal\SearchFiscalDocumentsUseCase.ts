/**
 * Search Fiscal Documents Use Case
 *
 * Caso de uso para buscar documentos fiscais por texto.
 * Parte da implementação da tarefa 8.7.2 - Gestão de documentos fiscais
 */

import {
  FiscalDocumentFilter,
  FiscalDocumentPaginationOptions,
  FiscalDocumentRepository,
  PaginatedFiscalDocuments,
} from '../../repositories/FiscalDocumentRepository';

export interface SearchFiscalDocumentsRequest {
  query: string;
  pagination?: {
    page: number;
    limit: number;
  };
}

export interface SearchFiscalDocumentsResponse {
  success: boolean;
  data?: PaginatedFiscalDocuments;
  error?: string;
}

export class SearchFiscalDocumentsUseCase {
  constructor(private fiscalDocumentRepository: FiscalDocumentRepository) {}

  async execute(request: SearchFiscalDocumentsRequest): Promise<SearchFiscalDocumentsResponse> {
    try {
      // Validar os dados de entrada
      if (!request.query || request.query.trim().length < 2) {
        return {
          success: false,
          error: 'Termo de busca deve ter pelo menos 2 caracteres.',
        };
      }

      if (request.pagination && (request.pagination.page < 1 || request.pagination.limit < 1)) {
        return {
          success: false,
          error: 'Parâmetros de paginação inválidos.',
        };
      }

      // Mapear paginação
      const pagination: FiscalDocumentPaginationOptions | undefined = request.pagination
        ? {
            page: request.pagination.page,
            limit: request.pagination.limit,
          }
        : undefined;

      // Construir filtro com base no termo de busca
      const filter: FiscalDocumentFilter = {};
      const query = request.query.trim();

      // Verificar se é um número de documento
      if (/^\d{1,6}$/.test(query)) {
        filter.number = query;
      }
      // Verificar se é um CPF ou CNPJ
      else if (/^\d{11}$/.test(query) || /^\d{14}$/.test(query)) {
        filter.customerDocumentNumber = query;
      }
      // Verificar se é um valor monetário (ex: 1.234,56)
      else if (/^\d{1,3}(\.\d{3})*,\d{2}$/.test(query)) {
        const value = Number.parseFloat(query.replace(/\./g, '').replace(',', '.'));
        filter.minValue = value * 0.95; // 5% de tolerância para baixo
        filter.maxValue = value * 1.05; // 5% de tolerância para cima
      }
      // Verificar se é uma data (ex: 01/01/2023)
      else if (/^\d{2}\/\d{2}\/\d{4}$/.test(query)) {
        const parts = query.split('/');
        const date = new Date(`${parts[2]}-${parts[1]}-${parts[0]}`);
        filter.startDate = date;
        filter.endDate = new Date(date.getTime() + 24 * 60 * 60 * 1000); // Adiciona 1 dia
      }
      // Caso contrário, buscar por nome do cliente
      else {
        filter.customerName = query;
      }

      // Buscar documentos fiscais
      const result = await this.fiscalDocumentRepository.find(
        filter,
        { field: 'createdAt', direction: 'desc' },
        pagination
      );

      return {
        success: true,
        data: result,
      };
    } catch (error) {
      console.error('Erro ao buscar documentos fiscais:', error);

      return {
        success: false,
        error:
          error instanceof Error
            ? error.message
            : 'Erro desconhecido ao buscar documentos fiscais.',
      };
    }
  }
}
