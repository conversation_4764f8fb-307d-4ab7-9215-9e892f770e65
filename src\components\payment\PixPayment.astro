---
/**
 * Componente de pagamento PIX
 *
 * Este componente exibe um QR Code PIX para pagamento
 * e monitora o status da transação.
 */

import { efiPayService } from '@services/efiPayService';
import { getCurrentUser } from '@utils/authUtils';
import { logger } from '@utils/logger';

// Propriedades do componente
interface Props {
  /**
   * Valor do pagamento em centavos
   */
  amount: number;

  /**
   * Descrição do pagamento
   */
  description: string;

  /**
   * ID da transação (opcional, será gerado se não fornecido)
   */
  txid?: string;

  /**
   * Tempo de expiração em segundos
   * @default 3600 (1 hora)
   */
  expiresIn?: number;

  /**
   * URL de redirecionamento após pagamento bem-sucedido
   */
  successUrl?: string;

  /**
   * URL de redirecionamento após cancelamento
   */
  cancelUrl?: string;

  /**
   * Se deve mostrar o valor
   * @default true
   */
  showAmount?: boolean;

  /**
   * Se deve mostrar o botão de copiar código PIX
   * @default true
   */
  showCopyButton?: boolean;

  /**
   * Se deve mostrar o temporizador de expiração
   * @default true
   */
  showExpirationTimer?: boolean;

  /**
   * Intervalo de verificação de status em milissegundos
   * @default 5000 (5 segundos)
   */
  checkInterval?: number;

  /**
   * Classe CSS adicional
   */
  class?: string;
}

// Valores padrão
const {
  amount,
  description,
  txid: providedTxid,
  expiresIn = 3600,
  successUrl,
  cancelUrl,
  showAmount = true,
  showCopyButton = true,
  showExpirationTimer = true,
  checkInterval = 5000,
  class: className = '',
} = Astro.props;

// Obter usuário atual
const user = await getCurrentUser(Astro.cookies);

// Gerar ID de transação se não fornecido
const txid = providedTxid || crypto.randomUUID().replace(/-/g, '');

// Criar cobrança PIX
let pixCharge: any;
let error = '';

try {
  // Preparar dados do cliente
  const customer = user
    ? {
        name: user.name,
        cpf: user.document,
        email: user.email,
      }
    : undefined;

  // Criar cobrança
  pixCharge = await efiPayService.createPixChargeWithTxid(
    txid,
    amount,
    description,
    customer,
    expiresIn
  );
} catch (e) {
  logger.error('Erro ao criar cobrança PIX:', e);
  error = 'Não foi possível gerar o QR Code PIX. Tente novamente mais tarde.';
}

// Formatar valor para exibição
const formattedAmount = new Intl.NumberFormat('pt-BR', {
  style: 'currency',
  currency: 'BRL',
}).format(amount / 100);

// Gerar ID único para o componente
const componentId = `pix-payment-${Math.random().toString(36).substring(2, 11)}`;
---

<div
  id={componentId}
  class={`pix-payment-container ${className}`}
  data-txid={txid}
  data-check-interval={checkInterval}
  data-success-url={successUrl}
  data-cancel-url={cancelUrl}
  data-expires-in={expiresIn}
>
  {
    error ? (
      <div class="error-message">
        <p>{error}</p>
        <button class="retry-button">Tentar novamente</button>
      </div>
    ) : (
      <div class="pix-content">
        <h3 class="pix-title">Pagamento via PIX</h3>

        {showAmount && (
          <div class="pix-amount">
            <span class="amount-label">Valor:</span>
            <span class="amount-value">{formattedAmount}</span>
          </div>
        )}

        <div class="pix-description">
          <p>{description}</p>
        </div>

        <div class="qrcode-container">
          {pixCharge?.qrcode?.imagemQrcode && (
            <img
              src={pixCharge.qrcode.imagemQrcode}
              alt="QR Code PIX"
              class="qrcode-image"
            />
          )}
        </div>

        {showCopyButton && pixCharge?.qrcode?.qrcode && (
          <div class="copy-code-container">
            <p class="copy-instruction">Ou copie o código PIX:</p>
            <div class="copy-code-wrapper">
              <input
                type="text"
                readonly
                value={pixCharge.qrcode.qrcode}
                class="pix-code-input"
              />
              <button class="copy-button" data-code={pixCharge.qrcode.qrcode}>
                Copiar
              </button>
            </div>
          </div>
        )}

        {showExpirationTimer && (
          <div class="expiration-timer">
            <p>
              Expira em: <span class="timer-display">--:--:--</span>
            </p>
          </div>
        )}

        <div class="payment-status">
          <p class="status-message">Aguardando pagamento...</p>
          <div class="status-spinner" />
        </div>

        {cancelUrl && (
          <div class="cancel-container">
            <a href={cancelUrl} class="cancel-link">
              Cancelar pagamento
            </a>
          </div>
        )}
      </div>
    )
  }
</div>

<script>
  // Função para formatar tempo
  function formatTime(seconds: number): string {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;

    return [
      hours.toString().padStart(2, "0"),
      minutes.toString().padStart(2, "0"),
      secs.toString().padStart(2, "0"),
    ].join(":");
  }

  // Função para verificar status do pagamento
  async function checkPaymentStatus(txid: string): Promise<string> {
    try {
      const response = await fetch(`/api/payments/pix/status?txid=${txid}`);
      const data = await response.json();

      return data.status;
    } catch (error) {
      console.error("Erro ao verificar status do pagamento:", error);
      return "error";
    }
  }

  // Inicializar componentes quando o DOM estiver pronto
  document.addEventListener("DOMContentLoaded", () => {
    const containers = document.querySelectorAll(".pix-payment-container");

    containers.forEach((container) => {
      // Obter elementos
      const statusMessage = container.querySelector(".status-message");
      const timerDisplay = container.querySelector(".timer-display");
      const copyButton = container.querySelector(".copy-button");
      const retryButton = container.querySelector(".retry-button");

      // Obter dados do componente
      const txid = container.getAttribute("data-txid");
      const checkInterval = parseInt(
        container.getAttribute("data-check-interval") || "5000",
        10,
      );
      const successUrl = container.getAttribute("data-success-url");
      const cancelUrl = container.getAttribute("data-cancel-url");
      const expiresIn = parseInt(
        container.getAttribute("data-expires-in") || "3600",
        10,
      );

      // Variáveis de controle
      let checkIntervalId: number | null = null;
      let timerIntervalId: number | null = null;
      let remainingSeconds = expiresIn;

      // Iniciar temporizador de expiração
      if (timerDisplay) {
        timerDisplay.textContent = formatTime(remainingSeconds);

        timerIntervalId = window.setInterval(() => {
          remainingSeconds--;

          if (remainingSeconds <= 0) {
            // Parar temporizador
            if (timerIntervalId !== null) {
              clearInterval(timerIntervalId);
              timerIntervalId = null;
            }

            // Parar verificação de status
            if (checkIntervalId !== null) {
              clearInterval(checkIntervalId);
              checkIntervalId = null;
            }

            // Atualizar mensagem
            if (statusMessage) {
              statusMessage.textContent = "Pagamento expirado";
              statusMessage.classList.add("expired");
            }

            // Redirecionar se necessário
            if (cancelUrl) {
              window.location.href = cancelUrl;
            }

            return;
          }

          // Atualizar exibição
          timerDisplay.textContent = formatTime(remainingSeconds);
        }, 1000);
      }

      // Configurar botão de cópia
      if (copyButton) {
        copyButton.addEventListener("click", () => {
          const code = copyButton.getAttribute("data-code");

          if (code) {
            // Copiar para a área de transferência
            navigator.clipboard
              .writeText(code)
              .then(() => {
                // Feedback visual
                copyButton.textContent = "Copiado!";
                copyButton.classList.add("copied");

                // Restaurar após 2 segundos
                setTimeout(() => {
                  copyButton.textContent = "Copiar";
                  copyButton.classList.remove("copied");
                }, 2000);
              })
              .catch((err) => {
                console.error("Erro ao copiar código:", err);
              });
          }
        });
      }

      // Configurar botão de nova tentativa
      if (retryButton) {
        retryButton.addEventListener("click", () => {
          // Recarregar a página
          window.location.reload();
        });
      }

      // Iniciar verificação de status
      if (txid) {
        checkIntervalId = window.setInterval(async () => {
          const status = await checkPaymentStatus(txid);

          if (statusMessage) {
            switch (status) {
              case "COMPLETED":
                statusMessage.textContent = "Pagamento confirmado!";
                statusMessage.classList.add("success");

                // Parar verificação
                if (checkIntervalId !== null) {
                  clearInterval(checkIntervalId);
                  checkIntervalId = null;
                }

                // Parar temporizador
                if (timerIntervalId !== null) {
                  clearInterval(timerIntervalId);
                  timerIntervalId = null;
                }

                // Redirecionar após 2 segundos
                if (successUrl) {
                  setTimeout(() => {
                    window.location.href = successUrl;
                  }, 2000);
                }
                break;

              case "CANCELLED":
                statusMessage.textContent = "Pagamento cancelado";
                statusMessage.classList.add("cancelled");

                // Parar verificação
                if (checkIntervalId !== null) {
                  clearInterval(checkIntervalId);
                  checkIntervalId = null;
                }

                // Parar temporizador
                if (timerIntervalId !== null) {
                  clearInterval(timerIntervalId);
                  timerIntervalId = null;
                }

                // Redirecionar após 2 segundos
                if (cancelUrl) {
                  setTimeout(() => {
                    window.location.href = cancelUrl;
                  }, 2000);
                }
                break;

              case "EXPIRED":
                statusMessage.textContent = "Pagamento expirado";
                statusMessage.classList.add("expired");

                // Parar verificação
                if (checkIntervalId !== null) {
                  clearInterval(checkIntervalId);
                  checkIntervalId = null;
                }

                // Parar temporizador
                if (timerIntervalId !== null) {
                  clearInterval(timerIntervalId);
                  timerIntervalId = null;
                }
                break;

              case "error":
                statusMessage.textContent = "Erro ao verificar pagamento";
                statusMessage.classList.add("error");
                break;

              default:
                // Manter mensagem de aguardando
                break;
            }
          }
        }, checkInterval);
      }
    });
  });
</script>

<style>
  .pix-payment-container {
    width: 100%;
    max-width: 400px;
    margin: 0 auto;
    padding: 1.5rem;
    background-color: var(--background-primary);
    border-radius: 8px;
    box-shadow: 0 2px 8px var(--shadow-color);
  }

  .pix-title {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
    text-align: center;
    margin-bottom: 1rem;
  }

  .pix-amount {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 1rem;
  }

  .amount-label {
    font-size: var(--font-size-md);
    color: var(--text-secondary);
    margin-right: 0.5rem;
  }

  .amount-value {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
  }

  .pix-description {
    text-align: center;
    margin-bottom: 1rem;
    color: var(--text-secondary);
  }

  .qrcode-container {
    display: flex;
    justify-content: center;
    margin: 1.5rem 0;
  }

  .qrcode-image {
    width: 200px;
    height: 200px;
    border: 1px solid var(--border-medium);
    border-radius: 4px;
  }

  .copy-code-container {
    margin-bottom: 1.5rem;
  }

  .copy-instruction {
    text-align: center;
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
  }

  .copy-code-wrapper {
    display: flex;
    gap: 0.5rem;
  }

  .pix-code-input {
    flex: 1;
    padding: 0.5rem;
    border: 1px solid var(--border-medium);
    border-radius: 4px;
    font-size: var(--font-size-sm);
    background-color: var(--background-secondary);
    color: var(--text-secondary);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .copy-button {
    padding: 0.5rem 1rem;
    background-color: var(--chico-bento-blue);
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: var(--font-weight-medium);
    transition: background-color 0.3s;
  }

  .copy-button:hover {
    background-color: var(--chico-bento-blue-dark);
  }

  .copy-button.copied {
    background-color: var(--cebolinha-green);
  }

  .expiration-timer {
    text-align: center;
    margin-bottom: 1rem;
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
  }

  .timer-display {
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
  }

  .payment-status {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 1rem;
    padding: 1rem;
    background-color: var(--background-secondary);
    border-radius: 4px;
  }

  .status-message {
    font-weight: var(--font-weight-medium);
    margin-bottom: 0.5rem;
  }

  .status-message.success {
    color: var(--cebolinha-green);
  }

  .status-message.error {
    color: var(--monica-red);
  }

  .status-message.cancelled {
    color: var(--cascao-brown);
  }

  .status-message.expired {
    color: var(--monica-red);
  }

  .status-spinner {
    width: 24px;
    height: 24px;
    border: 3px solid var(--border-medium);
    border-top-color: var(--chico-bento-blue);
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  .cancel-container {
    text-align: center;
    margin-top: 1rem;
  }

  .cancel-link {
    color: var(--text-tertiary);
    text-decoration: underline;
    font-size: var(--font-size-sm);
  }

  .cancel-link:hover {
    color: var(--monica-red);
  }

  .error-message {
    text-align: center;
    color: var(--monica-red);
    padding: 1rem;
  }

  .retry-button {
    margin-top: 1rem;
    padding: 0.5rem 1rem;
    background-color: var(--chico-bento-blue);
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: var(--font-weight-medium);
    transition: background-color 0.3s;
  }

  .retry-button:hover {
    background-color: var(--chico-bento-blue-dark);
  }

  @keyframes spin {
    to {
      transform: rotate(360deg);
    }
  }
</style>
