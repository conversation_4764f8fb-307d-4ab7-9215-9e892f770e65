/**
 * Logger composto
 *
 * Esta classe implementa um logger que delega para múltiplos loggers.
 */

import { LogLevel, LogMetadata, Logger } from '../../domain/interfaces/Logger';

/**
 * Logger composto
 */
export class CompositeLogger implements Logger {
  /**
   * Loggers para os quais delegar
   */
  private readonly loggers: Logger[];

  /**
   * Cria uma nova instância de CompositeLogger
   * @param loggers - Loggers para os quais delegar
   */
  constructor(loggers: Logger[]) {
    this.loggers = [...loggers];
  }

  /**
   * Registra uma mensagem de debug
   * @param message - Mensagem a ser registrada
   * @param metadata - Metadados adicionais
   */
  public debug(message: string, metadata?: LogMetadata): void {
    for (const logger of this.loggers) {
      logger.debug(message, metadata);
    }
  }

  /**
   * Registra uma mensagem informativa
   * @param message - Mensagem a ser registrada
   * @param metadata - Metadados adicionais
   */
  public info(message: string, metadata?: LogMetadata): void {
    for (const logger of this.loggers) {
      logger.info(message, metadata);
    }
  }

  /**
   * Registra uma mensagem de aviso
   * @param message - Mensagem a ser registrada
   * @param metadata - Metadados adicionais
   */
  public warn(message: string, metadata?: LogMetadata): void {
    for (const logger of this.loggers) {
      logger.warn(message, metadata);
    }
  }

  /**
   * Registra uma mensagem de erro
   * @param message - Mensagem a ser registrada
   * @param metadata - Metadados adicionais
   */
  public error(message: string, metadata?: LogMetadata): void {
    for (const logger of this.loggers) {
      logger.error(message, metadata);
    }
  }

  /**
   * Registra uma mensagem de erro fatal
   * @param message - Mensagem a ser registrada
   * @param metadata - Metadados adicionais
   */
  public fatal(message: string, metadata?: LogMetadata): void {
    for (const logger of this.loggers) {
      logger.fatal(message, metadata);
    }
  }

  /**
   * Registra uma mensagem com nível personalizado
   * @param level - Nível de log
   * @param message - Mensagem a ser registrada
   * @param metadata - Metadados adicionais
   */
  public log(level: LogLevel, message: string, metadata?: LogMetadata): void {
    for (const logger of this.loggers) {
      logger.log(level, message, metadata);
    }
  }

  /**
   * Adiciona um logger
   * @param logger - Logger a ser adicionado
   */
  public addLogger(logger: Logger): void {
    this.loggers.push(logger);
  }

  /**
   * Remove um logger
   * @param logger - Logger a ser removido
   * @returns true se o logger foi removido
   */
  public removeLogger(logger: Logger): boolean {
    const index = this.loggers.indexOf(logger);

    if (index >= 0) {
      this.loggers.splice(index, 1);
      return true;
    }

    return false;
  }

  /**
   * Obtém o número de loggers
   * @returns Número de loggers
   */
  public getLoggerCount(): number {
    return this.loggers.length;
  }
}
