/**
 * Send External Notification Use Case
 *
 * Caso de uso para enviar notificações externas (e-mail, SMS, push).
 * Parte da implementação da tarefa 8.5.2 - Notificações externas
 */

import { getNotificationEmailTemplate } from '../../../utils/emailUtils';
import { NotificationType } from '../../entities/Notification';
import { NotificationChannel } from '../../entities/NotificationPreference';
import { NotificationPreferenceRepository } from '../../repositories/NotificationPreferenceRepository';
import { EmailService } from '../../services/EmailService';
import { PushNotificationService } from '../../services/PushNotificationService';
import { SmsService } from '../../services/SmsService';

export interface SendExternalNotificationRequest {
  userId: string;
  title: string;
  content: string;
  type: NotificationType;
  channels?: NotificationChannel[];
  data?: Record<string, any>;
  templateName?: string;
  actionUrl?: string;
  actionLabel?: string;
  imageUrl?: string;
  scheduledAt?: Date;
}

export interface SendExternalNotificationResponse {
  success: boolean;
  sentChannels: NotificationChannel[];
  errors?: Record<NotificationChannel, string>;
}

export class SendExternalNotificationUseCase {
  constructor(
    private preferenceRepository: NotificationPreferenceRepository,
    private emailService: EmailService,
    private smsService: SmsService,
    private pushNotificationService: PushNotificationService
  ) {}

  async execute(
    request: SendExternalNotificationRequest
  ): Promise<SendExternalNotificationResponse> {
    try {
      // Validar os dados de entrada
      if (!this.validateRequest(request)) {
        return {
          success: false,
          sentChannels: [],
          errors: {
            email: 'Dados inválidos para envio de notificação externa.',
            sms: 'Dados inválidos para envio de notificação externa.',
            push: 'Dados inválidos para envio de notificação externa.',
          },
        };
      }

      // Determinar os canais para envio com base nas preferências do usuário
      const channelsToSend: NotificationChannel[] = [];
      const requestedChannels = request.channels || ['email', 'push', 'sms'];

      // Verificar cada canal solicitado (exceto in_app, que é tratado pelo SendNotificationUseCase)
      for (const channel of requestedChannels) {
        if (channel === 'in_app') continue;

        const shouldSend = await this.preferenceRepository.shouldReceiveNotification(
          request.userId,
          request.type,
          channel
        );

        if (shouldSend) {
          channelsToSend.push(channel);
        }
      }

      // Se não houver canais para envio, retornar sucesso (não há nada a fazer)
      if (channelsToSend.length === 0) {
        return {
          success: true,
          sentChannels: [],
        };
      }

      // Enviar para os canais apropriados
      const sentChannels: NotificationChannel[] = [];
      const errors: Record<NotificationChannel, string> = {};

      // Enviar por e-mail, se necessário
      if (channelsToSend.includes('email')) {
        try {
          const emailResult = await this.sendEmail(request);

          if (emailResult.success) {
            sentChannels.push('email');
          } else {
            errors.email = emailResult.error || 'Erro desconhecido ao enviar e-mail';
          }
        } catch (error) {
          errors.email =
            error instanceof Error ? error.message : 'Erro desconhecido ao enviar e-mail';
        }
      }

      // Enviar por SMS, se necessário
      if (channelsToSend.includes('sms')) {
        try {
          const smsResult = await this.sendSms(request);

          if (smsResult.success) {
            sentChannels.push('sms');
          } else {
            errors.sms = smsResult.error || 'Erro desconhecido ao enviar SMS';
          }
        } catch (error) {
          errors.sms = error instanceof Error ? error.message : 'Erro desconhecido ao enviar SMS';
        }
      }

      // Enviar por push, se necessário
      if (channelsToSend.includes('push')) {
        try {
          const pushResult = await this.sendPush(request);

          if (pushResult.success) {
            sentChannels.push('push');
          } else {
            errors.push = pushResult.error || 'Erro desconhecido ao enviar notificação push';
          }
        } catch (error) {
          errors.push =
            error instanceof Error ? error.message : 'Erro desconhecido ao enviar notificação push';
        }
      }

      return {
        success: sentChannels.length > 0,
        sentChannels,
        errors: Object.keys(errors).length > 0 ? errors : undefined,
      };
    } catch (error) {
      console.error('Erro ao enviar notificação externa:', error);

      return {
        success: false,
        sentChannels: [],
        errors: {
          email: 'Erro interno ao processar notificação externa',
          sms: 'Erro interno ao processar notificação externa',
          push: 'Erro interno ao processar notificação externa',
        },
      };
    }
  }

  private async sendEmail(request: SendExternalNotificationRequest) {
    // Verificar se deve usar um template
    if (request.templateName) {
      // Enviar e-mail com template
      return this.emailService.sendTemplateEmail(
        request.userId,
        request.templateName,
        {
          title: request.title,
          content: request.content,
          actionUrl: request.actionUrl,
          actionLabel: request.actionLabel || 'Saiba mais',
          imageUrl: request.imageUrl,
          ...request.data,
        },
        {
          scheduledAt: request.scheduledAt,
        }
      );
    }
    // Determinar template com base no tipo de notificação
    const { template, subject } = getNotificationEmailTemplate(request.type);

    // Enviar e-mail de notificação
    return this.emailService.sendNotificationEmail(
      request.userId,
      request.title || subject,
      request.content,
      {
        scheduledAt: request.scheduledAt,
        template: {
          name: template,
          subject: request.title || subject,
          data: {
            title: request.title,
            content: request.content,
            actionUrl: request.actionUrl,
            actionLabel: request.actionLabel || 'Saiba mais',
            imageUrl: request.imageUrl,
            ...request.data,
          },
        },
      }
    );
  }

  private async sendSms(request: SendExternalNotificationRequest) {
    // Preparar mensagem de SMS (versão simplificada)
    let message = request.title ? `${request.title}: ` : '';
    message += request.content;

    // Adicionar URL de ação, se fornecida
    if (request.actionUrl) {
      message += ` ${request.actionUrl}`;
    }

    // Enviar SMS
    return this.smsService.sendNotificationSms(request.userId, message, {
      scheduledAt: request.scheduledAt,
      metadata: request.data,
    });
  }

  private async sendPush(request: SendExternalNotificationRequest) {
    // Preparar opções de notificação push
    const pushOptions = {
      title: request.title,
      body: request.content,
      image: request.imageUrl,
      data: {
        type: request.type,
        ...request.data,
      },
      scheduledAt: request.scheduledAt,
    };

    // Adicionar ação, se fornecida
    if (request.actionUrl) {
      pushOptions.data.actionUrl = request.actionUrl;
      pushOptions.data.actionLabel = request.actionLabel || 'Saiba mais';
    }

    // Enviar notificação push
    return this.pushNotificationService.sendPushNotificationToUser(request.userId, pushOptions);
  }

  private validateRequest(request: SendExternalNotificationRequest): boolean {
    // Validar usuário
    if (!request.userId) {
      return false;
    }

    // Validar conteúdo
    if (!request.content) {
      return false;
    }

    // Validar tipo
    if (!this.isValidNotificationType(request.type)) {
      return false;
    }

    // Validar canais
    if (request.channels) {
      for (const channel of request.channels) {
        if (!this.isValidNotificationChannel(channel)) {
          return false;
        }
      }
    }

    // Validar data de agendamento
    if (request.scheduledAt && request.scheduledAt < new Date()) {
      return false;
    }

    return true;
  }

  private isValidNotificationType(type: string): boolean {
    const validTypes: NotificationType[] = [
      'info',
      'success',
      'warning',
      'error',
      'system',
      'payment',
      'order',
      'product',
      'content',
      'message',
    ];

    return validTypes.includes(type as NotificationType);
  }

  private isValidNotificationChannel(channel: string): boolean {
    const validChannels: NotificationChannel[] = ['in_app', 'email', 'push', 'sms'];

    return validChannels.includes(channel as NotificationChannel);
  }
}
