/**
 * Implementação do Repositório de Produtos usando PostgreSQL
 *
 * Esta implementação do repositório de produtos utiliza PostgreSQL
 * para persistência de dados, seguindo os princípios da Clean Architecture.
 */

import { Product, ProductCategory } from '../../../domain/entities/Product';
import { ProductRepository, ProductFilterOptions, PaginatedProducts } from '../../interfaces/ProductRepository';
import { pgHelper } from '../../../repository/pgHelper';

/**
 * Repositório de Produtos com PostgreSQL
 */
export class PgProductRepository implements ProductRepository {
  /**
   * Mapeia um registro do banco de dados para uma entidade de Produto
   *
   * @param row Registro do banco de dados
   * @returns Entidade de Produto
   */
  private mapToEntity(row: any): Product {
    return {
      id: row.ulid_product,
      name: row.name,
      description: row.description,
      price: parseFloat(row.price),
      category: row.category as ProductCategory,
      imageUrl: row.image_url,
      stock: parseInt(row.stock, 10),
      featured: row.featured,
      active: row.active,
      createdAt: new Date(row.created_at),
      updatedAt: new Date(row.updated_at)
    };
  }

  /**
   * Busca um produto pelo ID
   *
   * @param id ID do produto
   * @returns Produto encontrado ou null se não existir
   */
  async findById(id: string): Promise<Product | null> {
    const result = await pgHelper.query(
      `SELECT * FROM tab_product WHERE ulid_product = $1 AND active = true`,
      [id]
    );

    return result.rows.length > 0 ? this.mapToEntity(result.rows[0]) : null;
  }

  /**
   * Busca produtos com filtros
   *
   * @param options Opções de filtro
   * @returns Resultado paginado de produtos
   */
  async findAll(options?: ProductFilterOptions): Promise<PaginatedProducts> {
    const {
      category,
      featured,
      active = true,
      search,
      minPrice,
      maxPrice,
      page = 1,
      limit = 20,
      sortBy = 'name',
      sortOrder = 'asc'
    } = options || {};

    // Construir consulta
    let query = `SELECT * FROM tab_product WHERE active = $1`;
    const params: any[] = [active];
    let paramIndex = 2;

    // Adicionar filtros
    if (category) {
      query += ` AND category = $${paramIndex}`;
      params.push(category);
      paramIndex++;
    }

    if (featured !== undefined) {
      query += ` AND featured = $${paramIndex}`;
      params.push(featured);
      paramIndex++;
    }

    if (search) {
      query += ` AND (name ILIKE $${paramIndex} OR description ILIKE $${paramIndex})`;
      params.push(`%${search}%`);
      paramIndex++;
    }

    if (minPrice !== undefined) {
      query += ` AND price >= $${paramIndex}`;
      params.push(minPrice);
      paramIndex++;
    }

    if (maxPrice !== undefined) {
      query += ` AND price <= $${paramIndex}`;
      params.push(maxPrice);
      paramIndex++;
    }

    // Consulta para contar total
    const countResult = await pgHelper.query(
      `SELECT COUNT(*) as total FROM (${query}) as filtered_products`,
      params
    );
    const total = parseInt(countResult.rows[0].total, 10);

    // Adicionar ordenação e paginação
    query += ` ORDER BY ${sortBy} ${sortOrder === 'desc' ? 'DESC' : 'ASC'}`;
    query += ` LIMIT $${paramIndex} OFFSET $${paramIndex + 1}`;
    params.push(limit);
    params.push((page - 1) * limit);

    // Executar consulta
    const result = await pgHelper.query(query, params);

    // Mapear resultados
    const items = result.rows.map(row => this.mapToEntity(row));

    // Calcular total de páginas
    const totalPages = Math.ceil(total / limit);

    return {
      items,
      total,
      page,
      limit,
      totalPages
    };
  }

  /**
   * Busca produtos por categoria
   *
   * @param category Categoria para filtro
   * @param options Opções de filtro adicionais
   * @returns Resultado paginado de produtos
   */
  async findByCategory(category: ProductCategory, options?: Omit<ProductFilterOptions, 'category'>): Promise<PaginatedProducts> {
    return this.findAll({ ...options, category });
  }

  /**
   * Busca produtos em destaque
   *
   * @param options Opções de filtro adicionais
   * @returns Resultado paginado de produtos
   */
  async findFeatured(options?: Omit<ProductFilterOptions, 'featured'>): Promise<PaginatedProducts> {
    return this.findAll({ ...options, featured: true });
  }

  /**
   * Busca produtos relacionados a um produto específico
   *
   * @param productId ID do produto de referência
   * @param limit Limite de produtos a serem retornados
   * @returns Lista de produtos relacionados
   */
  async findRelated(productId: string, limit: number = 5): Promise<Product[]> {
    // Primeiro, obter a categoria do produto de referência
    const product = await this.findById(productId);

    if (!product) {
      return [];
    }

    // Buscar produtos da mesma categoria, excluindo o produto de referência
    const result = await pgHelper.query(
      `SELECT * FROM tab_product
       WHERE category = $1
       AND ulid_product != $2
       AND active = true
       ORDER BY RANDOM()
       LIMIT $3`,
      [product.category, productId, limit]
    );

    return result.rows.map(row => this.mapToEntity(row));
  }

  /**
   * Busca produtos recentes
   *
   * @param limit Limite de produtos a serem retornados
   * @returns Lista de produtos recentes
   */
  async findRecent(limit: number = 10): Promise<Product[]> {
    const result = await pgHelper.query(
      `SELECT * FROM tab_product
       WHERE active = true
       ORDER BY created_at DESC
       LIMIT $1`,
      [limit]
    );

    return result.rows.map(row => this.mapToEntity(row));
  }

  /**
   * Salva um produto (cria ou atualiza)
   *
   * @param product Produto a ser salvo
   * @returns Produto salvo
   */
  async save(product: Product): Promise<Product> {
    // Verificar se o produto já existe
    const existingProduct = product.id ? await this.findById(product.id) : null;

    if (existingProduct) {
      // Atualizar produto existente
      const result = await pgHelper.query(
        `UPDATE tab_product SET
         name = $1,
         description = $2,
         price = $3,
         category = $4,
         image_url = $5,
         stock = $6,
         featured = $7,
         active = $8,
         updated_at = NOW()
         WHERE ulid_product = $9
         RETURNING *`,
        [
          product.name,
          product.description,
          product.price,
          product.category,
          product.imageUrl,
          product.stock,
          product.featured,
          product.active,
          product.id
        ]
      );

      return this.mapToEntity(result.rows[0]);
    } else {
      // Criar novo produto
      const productId = product.id || pgHelper.generateULID();

      const result = await pgHelper.query(
        `INSERT INTO tab_product (
          ulid_product,
          name,
          description,
          price,
          category,
          image_url,
          stock,
          featured,
          active,
          created_at,
          updated_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, NOW(), NOW())
        RETURNING *`,
        [
          productId,
          product.name,
          product.description,
          product.price,
          product.category,
          product.imageUrl,
          product.stock,
          product.featured || false,
          product.active !== undefined ? product.active : true
        ]
      );

      return this.mapToEntity(result.rows[0]);
    }
  }

  /**
   * Remove um produto
   *
   * @param id ID do produto a ser removido
   * @returns true se removido com sucesso, false caso contrário
   */
  async remove(id: string): Promise<boolean> {
    // Soft delete (marcar como inativo)
    const result = await pgHelper.query(
      `UPDATE tab_product SET
       active = false,
       updated_at = NOW()
       WHERE ulid_product = $1
       RETURNING *`,
      [id]
    );

    return result.rowCount > 0;
  }

  /**
   * Atualiza o estoque de um produto
   *
   * @param id ID do produto
   * @param quantity Nova quantidade em estoque
   * @returns Produto atualizado
   */
  async updateStock(id: string, quantity: number): Promise<Product> {
    const result = await pgHelper.query(
      `UPDATE tab_product SET
       stock = $1,
       updated_at = NOW()
       WHERE ulid_product = $2
       RETURNING *`,
      [quantity, id]
    );

    if (result.rowCount === 0) {
      throw new Error(`Produto com ID ${id} não encontrado`);
    }

    return this.mapToEntity(result.rows[0]);
  }
}
