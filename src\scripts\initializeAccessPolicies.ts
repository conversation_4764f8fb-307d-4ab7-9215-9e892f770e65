/**
 * Script para inicializar políticas de acesso no banco de dados
 *
 * Este script cria os recursos, permissões e associações necessárias
 * para implementar as políticas de acesso definidas no sistema.
 */

import { accessPolicies } from '@config/accessPolicies';
import { permissionRepository } from '@repository/permissionRepository';
import { pgHelper } from '@repository/pgHelper';
import { resourceRepository } from '@repository/resourceRepository';
import { roleRepository } from '@repository/roleRepository';
import { logger } from '@utils/logger';

/**
 * Função principal para inicializar políticas de acesso
 */
async function initializeAccessPolicies() {
  try {
    logger.info('Iniciando inicialização de políticas de acesso...');

    // Iniciar transação
    await pgHelper.query('BEGIN');

    // Processar cada política de acesso
    for (const policy of accessPolicies) {
      logger.info(`Processando política para recurso: ${policy.resource}`);

      // Criar ou atualizar recurso
      const resourceResult = await resourceRepository.read(undefined, policy.resource);
      let resourceId: string;

      if (resourceResult.rowCount === 0) {
        // Criar novo recurso
        const newResource = await resourceRepository.create(policy.resource, policy.description);
        resourceId = newResource.rows[0].ulid_resource;
        logger.info(`Recurso criado: ${policy.resource}`);
      } else {
        // Atualizar recurso existente
        resourceId = resourceResult.rows[0].ulid_resource;
        await resourceRepository.update(resourceId, policy.resource, policy.description);
        logger.info(`Recurso atualizado: ${policy.resource}`);
      }

      // Processar ações/permissões para o recurso
      for (const action of policy.actions) {
        // Criar ou atualizar permissão
        const permissionName = `${policy.resource}_${action.name}`;
        const permissionResult = await permissionRepository.read(undefined, permissionName);
        let permissionId: string;

        if (permissionResult.rowCount === 0) {
          // Criar nova permissão
          const newPermission = await permissionRepository.create(
            permissionName,
            action.description,
            action.name
          );
          permissionId = newPermission.rows[0].ulid_permission;
          logger.info(`Permissão criada: ${permissionName}`);
        } else {
          // Atualizar permissão existente
          permissionId = permissionResult.rows[0].ulid_permission;
          await permissionRepository.update(
            permissionId,
            permissionName,
            action.description,
            action.name
          );
          logger.info(`Permissão atualizada: ${permissionName}`);
        }

        // Associar permissão ao recurso
        await permissionRepository.associateWithResource(resourceId, permissionId);
        logger.info(`Permissão associada ao recurso: ${permissionName}`);

        // Obter ID da associação recurso-permissão
        const resourcePermissionsResult =
          await resourceRepository.getResourcePermissions(resourceId);
        let resourcePermissionId: string | undefined;

        for (const row of resourcePermissionsResult.rows) {
          if (row.ulid_permission === permissionId) {
            resourcePermissionId = row.ulid_resource_permission;
            break;
          }
        }

        if (!resourcePermissionId) {
          logger.error(
            `Não foi possível encontrar associação recurso-permissão para ${permissionName}`
          );
          continue;
        }

        // Associar permissão aos papéis padrão
        for (const roleName of action.defaultRoles) {
          // Buscar papel pelo nome
          const roleResult = await roleRepository.read(undefined, roleName);

          if (roleResult.rowCount === 0) {
            logger.warn(`Papel não encontrado: ${roleName}`);
            continue;
          }

          const roleId = roleResult.rows[0].ulid_role;

          // Associar permissão ao papel
          await resourceRepository.associateWithRole(roleId, resourcePermissionId);
          logger.info(`Permissão ${permissionName} associada ao papel ${roleName}`);
        }
      }
    }

    // Confirmar transação
    await pgHelper.query('COMMIT');
    logger.info('Inicialização de políticas de acesso concluída com sucesso');
  } catch (error) {
    // Reverter transação em caso de erro
    await pgHelper.query('ROLLBACK');
    logger.error('Erro ao inicializar políticas de acesso:', error);
    throw error;
  } finally {
    // Encerrar conexão
    await pgHelper.end();
  }
}

// Executar script se chamado diretamente
if (require.main === module) {
  initializeAccessPolicies()
    .then(() => {
      logger.info('Script concluído com sucesso');
      process.exit(0);
    })
    .catch((error) => {
      logger.error('Erro ao executar script:', error);
      process.exit(1);
    });
}

export { initializeAccessPolicies };
