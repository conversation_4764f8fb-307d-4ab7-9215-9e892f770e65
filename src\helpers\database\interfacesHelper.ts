// src/helpers/database/interfacesHelper.ts

/**
 * Interface para resposta padrão de actions
 */
export interface ActionResponse<T = any> {
  success: boolean;
  result?: T;
  error?: string;
  message?: string;
}

/**
 * Interface para dados de categoria
 */
export interface CategoryData {
  ulid_category: string;
  name: string;
  ulid_parent?: string;
  active: boolean;
  created_at: Date;
  updated_at: Date;
}

/**
 * Interface para dados de usuário
 */
export interface UserData {
  ulid_user: string;
  ulid_user_type: string;
  ulid_school_type: string;
  email: string;
  password: string;
  is_teacher: boolean;
  name: string;
  state: string;
  county: string;
  active: boolean;
  is_active?: boolean;
  created_at: Date;
  updated_at: Date;
  last_login?: Date;
}

/**
 * Interface para dados de produto
 */
export interface ProductData {
  ulid_product: string;
  name: string;
  description: string;
  price: number;
  category: string;
  image_url?: string;
  stock: number;
  featured: boolean;
  active: boolean;
  created_at: Date;
  updated_at: Date;
}

/**
 * Interface para dados de pedido
 */
export interface OrderData {
  ulid_order: string;
  ulid_user: string;
  total_value: number;
  status: string;
  payment_status: string;
  created_at: Date;
  updated_at: Date;
}

/**
 * Interface para dados de item de pedido
 */
export interface OrderItemData {
  ulid_order_item: string;
  ulid_order: string;
  ulid_product: string;
  quantity: number;
  unit_price: number;
  total_price: number;
  created_at: Date;
  updated_at: Date;
}

/**
 * Interface para dados de pagamento
 */
export interface PaymentData {
  ulid_payment: string;
  ulid_order: string;
  ulid_payment_type: string;
  value: number;
  status: string;
  external_id?: string;
  payment_date?: Date;
  created_at: Date;
  updated_at: Date;
}

/**
 * Interface para dados de tipo de pagamento
 */
export interface PaymentTypeData {
  ulid_payment_type: string;
  type: string;
  active: boolean;
  created_at: Date;
  updated_at: Date;
}

/**
 * Interface para dados de fatura
 */
export interface InvoiceData {
  ulid_invoice: string;
  ulid_order: string;
  invoice_number: string;
  total_value: number;
  status: string;
  issued_at?: Date;
  created_at: Date;
  updated_at: Date;
}

/**
 * Interface para dados de item de fatura
 */
export interface InvoiceItemData {
  ulid_invoice_item: string;
  ulid_invoice: string;
  ulid_product: string;
  quantity: number;
  unit_price: number;
  total_price: number;
  created_at: Date;
  updated_at: Date;
}

/**
 * Interface para dados de tipo de usuário
 */
export interface UserTypeData {
  ulid_user_type: string;
  type: string;
  active: boolean;
  created_at: Date;
  updated_at: Date;
}

/**
 * Interface para dados de tipo de escola
 */
export interface SchoolTypeData {
  ulid_school_type: string;
  type: string;
  active: boolean;
  created_at: Date;
  updated_at: Date;
}

/**
 * Interface para dados de post
 */
export interface PostData {
  ulid_post: string;
  title: string;
  content: string;
  author: string;
  published: boolean;
  created_at: Date;
  updated_at: Date;
}

/**
 * Interface para dados de coluna de esquema de informação
 */
export interface InformationSchemaColumnData {
  table_catalog: string;
  table_schema: string;
  table_name: string;
  column_name: string;
  ordinal_position: number;
  column_default?: string;
  is_nullable: string;
  data_type: string;
  character_maximum_length?: number;
  character_octet_length?: number;
  numeric_precision?: number;
  numeric_precision_radix?: number;
  numeric_scale?: number;
  datetime_precision?: number;
  interval_type?: string;
  interval_precision?: number;
  character_set_catalog?: string;
  character_set_schema?: string;
  character_set_name?: string;
  collation_catalog?: string;
  collation_schema?: string;
  collation_name?: string;
  domain_catalog?: string;
  domain_schema?: string;
  domain_name?: string;
  udt_catalog?: string;
  udt_schema?: string;
  udt_name?: string;
  scope_catalog?: string;
  scope_schema?: string;
  scope_name?: string;
  maximum_cardinality?: number;
  dtd_identifier?: string;
  is_self_referencing?: string;
  is_identity?: string;
  identity_generation?: string;
  identity_start?: string;
  identity_increment?: string;
  identity_maximum?: string;
  identity_minimum?: string;
  identity_cycle?: string;
  is_generated?: string;
  generation_expression?: string;
  is_updatable?: string;
}
