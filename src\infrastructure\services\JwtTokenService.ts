/**
 * JWT Token Service
 *
 * Implementação do serviço de tokens usando JWT.
 * Parte da implementação da tarefa 8.8.2 - Gestão de usuários
 */

import jwt from 'jsonwebtoken';
import { TokenPayload, TokenService } from '../../domain/services/TokenService';

export class JwtTokenService implements TokenService {
  private readonly secretKey: string;
  private readonly passwordResetSecretKey: string;
  private readonly emailVerificationSecretKey: string;

  constructor(
    secretKey: string,
    passwordResetSecretKey: string,
    emailVerificationSecretKey: string
  ) {
    this.secretKey = secretKey;
    this.passwordResetSecretKey = passwordResetSecretKey;
    this.emailVerificationSecretKey = emailVerificationSecretKey;
  }

  /**
   * Gera um token JWT
   */
  generateToken(payload: TokenPayload, expiresIn = '1d'): string {
    return jwt.sign(payload, this.secretKey, { expiresIn });
  }

  /**
   * Verifica e decodifica um token JWT
   */
  verifyToken(token: string): TokenPayload | null {
    try {
      const decoded = jwt.verify(token, this.secretKey) as TokenPayload;
      return decoded;
    } catch (error) {
      console.error('Erro ao verificar token JWT:', error);
      return null;
    }
  }

  /**
   * Gera um token de redefinição de senha
   */
  generatePasswordResetToken(userId: string, expiresIn = '1h'): string {
    return jwt.sign({ userId }, this.passwordResetSecretKey, { expiresIn });
  }

  /**
   * Verifica um token de redefinição de senha
   */
  verifyPasswordResetToken(token: string): string | null {
    try {
      const decoded = jwt.verify(token, this.passwordResetSecretKey) as { userId: string };
      return decoded.userId;
    } catch (error) {
      console.error('Erro ao verificar token de redefinição de senha:', error);
      return null;
    }
  }

  /**
   * Gera um token de verificação de email
   */
  generateEmailVerificationToken(userId: string, email: string, expiresIn = '1d'): string {
    return jwt.sign({ userId, email }, this.emailVerificationSecretKey, { expiresIn });
  }

  /**
   * Verifica um token de verificação de email
   */
  verifyEmailVerificationToken(token: string): { userId: string; email: string } | null {
    try {
      const decoded = jwt.verify(token, this.emailVerificationSecretKey) as {
        userId: string;
        email: string;
      };
      return decoded;
    } catch (error) {
      console.error('Erro ao verificar token de verificação de email:', error);
      return null;
    }
  }
}
