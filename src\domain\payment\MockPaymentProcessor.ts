/**
 * Processador de pagamento simulado para testes
 *
 * Esta classe implementa um processador de pagamento simulado para testes.
 */

import {
  PaymentData,
  PaymentResult,
  PaymentStatusQuery,
  RefundRequest,
  RefundResult,
  PaymentStatus
} from '../interfaces/PaymentProcessor';
import { BasePaymentProcessor } from './BasePaymentProcessor';
import { logger } from '../../utils/logger';
import { nanoid } from 'nanoid';

/**
 * Processador de pagamento simulado para testes
 */
export class MockPaymentProcessor extends BasePaymentProcessor {
  /**
   * Armazenamento em memória para pagamentos
   */
  private payments: Map<string, PaymentResult> = new Map();

  /**
   * Armazenamento em memória para reembolsos
   */
  private refunds: Map<string, RefundResult> = new Map();

  /**
   * Cria uma nova instância de MockPaymentProcessor
   * @param shouldFail - Se true, simula falhas em operações
   */
  constructor(private shouldFail: boolean = false) {
    super('MockPayment');
  }

  /**
   * Implementação interna do processamento de pagamento
   * @param data - Dados do pagamento
   * @returns Resultado do pagamento
   */
  protected async processPaymentInternal(data: PaymentData): Promise<PaymentResult> {
    // Simular falha se configurado
    if (this.shouldFail) {
      throw new Error('Falha simulada no processamento de pagamento');
    }

    // Gerar ID externo
    const externalId = `mock-${nanoid()}`;

    // Criar resultado
    const result: PaymentResult = {
      id: data.id,
      externalId,
      status: PaymentStatus.COMPLETED,
      amount: data.amount,
      currency: data.currency,
      redirectUrl: `https://mock-payment.example.com/pay/${externalId}`,
      rawData: { ...data, externalId },
      createdAt: new Date(),
      updatedAt: new Date()
    };

    // Armazenar para consultas futuras
    this.payments.set(externalId, result);

    // Simular atraso de rede
    await new Promise(resolve => setTimeout(resolve, 500));

    logger.info('Pagamento simulado processado com sucesso', {
      paymentId: data.id,
      externalId
    });

    return result;
  }

  /**
   * Implementação interna da consulta de status de pagamento
   * @param query - Dados para consulta
   * @returns Resultado do pagamento atualizado
   */
  protected async checkPaymentStatusInternal(query: PaymentStatusQuery): Promise<PaymentResult> {
    // Simular falha se configurado
    if (this.shouldFail) {
      throw new Error('Falha simulada na consulta de status de pagamento');
    }

    // Buscar pagamento
    const payment = this.payments.get(query.externalId);

    // Se não encontrar, retornar erro
    if (!payment) {
      throw new Error(`Pagamento com ID externo ${query.externalId} não encontrado`);
    }

    // Simular atraso de rede
    await new Promise(resolve => setTimeout(resolve, 300));

    logger.info('Status de pagamento simulado consultado com sucesso', {
      paymentId: query.id,
      externalId: query.externalId,
      status: payment.status
    });

    return {
      ...payment,
      updatedAt: new Date()
    };
  }

  /**
   * Implementação interna do processamento de reembolso
   * @param request - Dados do reembolso
   * @returns Resultado do reembolso
   */
  protected async processRefundInternal(request: RefundRequest): Promise<RefundResult> {
    // Simular falha se configurado
    if (this.shouldFail) {
      throw new Error('Falha simulada no processamento de reembolso');
    }

    // Buscar pagamento
    const payment = this.payments.get(request.externalId);

    // Se não encontrar, retornar erro
    if (!payment) {
      throw new Error(`Pagamento com ID externo ${request.externalId} não encontrado`);
    }

    // Gerar ID para o reembolso
    const refundId = `refund-${nanoid()}`;
    const externalId = `mock-refund-${nanoid()}`;

    // Atualizar status do pagamento
    payment.status = PaymentStatus.REFUNDED;
    payment.updatedAt = new Date();

    // Criar resultado do reembolso
    const result: RefundResult = {
      id: refundId,
      externalId,
      paymentId: request.paymentId,
      status: 'completed',
      amount: request.amount || payment.amount,
      currency: payment.currency,
      rawData: {
        originalPayment: payment,
        refundRequest: request
      },
      createdAt: new Date()
    };

    // Armazenar para consultas futuras
    this.refunds.set(refundId, result);

    // Simular atraso de rede
    await new Promise(resolve => setTimeout(resolve, 700));

    logger.info('Reembolso simulado processado com sucesso', {
      paymentId: request.paymentId,
      externalId: request.externalId,
      refundId
    });

    return result;
  }

  /**
   * Validações específicas do processador
   * @param data - Dados do pagamento
   * @throws Error se os dados são inválidos
   */
  protected validatePaymentDataInternal(data: PaymentData): void {
    // Sem validações específicas para o processador simulado
  }

  /**
   * Configura se o processador deve simular falhas
   * @param shouldFail - Se true, simula falhas em operações
   */
  public setShouldFail(shouldFail: boolean): void {
    this.shouldFail = shouldFail;
  }

  /**
   * Limpa todos os dados armazenados
   */
  public clearData(): void {
    this.payments.clear();
    this.refunds.clear();
  }
}
