/**
 * Rate FAQ Item Use Case
 *
 * Caso de uso para avaliar um item de FAQ como útil ou não útil.
 * Parte da implementação da tarefa 8.7.1 - Implementação de FAQ interativo
 */

import { FaqRepository } from '../../repositories/FaqRepository';

export type FaqRating = 'helpful' | 'not_helpful';

export interface RateFaqItemRequest {
  id: string;
  rating: FaqRating;
}

export interface RateFaqItemResponse {
  success: boolean;
  error?: string;
}

export class RateFaqItemUseCase {
  constructor(private faqRepository: FaqRepository) {}

  async execute(request: RateFaqItemRequest): Promise<RateFaqItemResponse> {
    try {
      // Validar os dados de entrada
      if (!request.id) {
        return {
          success: false,
          error: 'ID do item de FAQ é obrigatório.',
        };
      }

      if (!request.rating || !['helpful', 'not_helpful'].includes(request.rating)) {
        return {
          success: false,
          error: 'Avaliação inválida. Deve ser "helpful" ou "not_helpful".',
        };
      }

      // Verificar se o item existe
      const item = await this.faqRepository.getById(request.id);

      if (!item) {
        return {
          success: false,
          error: `Item de FAQ com ID ${request.id} não encontrado.`,
        };
      }

      // Aplicar a avaliação
      let success: boolean;

      if (request.rating === 'helpful') {
        success = await this.faqRepository.markAsHelpful(request.id);
      } else {
        success = await this.faqRepository.markAsNotHelpful(request.id);
      }

      if (!success) {
        return {
          success: false,
          error: `Falha ao avaliar o item de FAQ ${request.id}.`,
        };
      }

      return {
        success: true,
      };
    } catch (error) {
      console.error('Erro ao avaliar item de FAQ:', error);

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Erro desconhecido ao avaliar item de FAQ.',
      };
    }
  }
}
