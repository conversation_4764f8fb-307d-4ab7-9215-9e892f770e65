---
import ProductFilters from '../../components/product/ProductFilters.astro';
import ProductGrid from '../../components/product/ProductGrid.astro';
import { PageTransition } from '../../components/transitions';
import DaisyPagination from '../../components/ui/DaisyPagination.astro';
import Breadcrumbs from '../../components/ui/navigation/Breadcrumbs.astro';
/**
 * Página de Catálogo de Produtos
 *
 * Esta página exibe o catálogo de produtos.
 * Parte da implementação da tarefa 8.3.3 - Catálogo de produtos
 */
import BaseLayout from '../../layouts/BaseLayout.astro';
import { Container, Section } from '../../layouts/grid';

// Título da página
const title = 'Catálogo de Produtos';

// Obter parâmetros de consulta
const categoryId = Astro.url.searchParams.get('categoryId') || undefined;
const search = Astro.url.searchParams.get('search') || undefined;
const priceMin = Astro.url.searchParams.get('priceMin')
  ? Number.parseFloat(Astro.url.searchParams.get('priceMin') as string)
  : undefined;
const priceMax = Astro.url.searchParams.get('priceMax')
  ? Number.parseFloat(Astro.url.searchParams.get('priceMax') as string)
  : undefined;
const sort = Astro.url.searchParams.get('sort') || undefined;
const page = Astro.url.searchParams.get('page')
  ? Number.parseInt(Astro.url.searchParams.get('page') as string)
  : 1;
const limit = Astro.url.searchParams.get('limit')
  ? Number.parseInt(Astro.url.searchParams.get('limit') as string)
  : 12;

// Em um cenário real, buscaríamos os produtos do repositório
// Por enquanto, usaremos dados de exemplo
const products = [
  {
    id: 'prod-001',
    name: 'Cartilha de Alfabetização',
    slug: 'cartilha-de-alfabetizacao',
    shortDescription: 'Material didático para alfabetização de crianças',
    price: 29.9,
    originalPrice: 39.9,
    discountPercentage: 25,
    imageUrl: '/images/products/cartilha.jpg',
    isFeatured: true,
    isDigital: true,
    categories: [{ id: 'cat-001', name: 'Alfabetização', slug: 'alfabetizacao' }],
    rating: 4.5,
    reviewCount: 28,
    inStock: true,
  },
  {
    id: 'prod-002',
    name: 'Kit de Atividades Matemáticas',
    slug: 'kit-de-atividades-matematicas',
    shortDescription: 'Conjunto de atividades para ensino de matemática básica',
    price: 45.0,
    imageUrl: '/images/products/matematica.jpg',
    isNew: true,
    categories: [{ id: 'cat-002', name: 'Matemática', slug: 'matematica' }],
    rating: 4.0,
    reviewCount: 15,
    inStock: true,
  },
  {
    id: 'prod-003',
    name: 'Livro de Histórias Infantis',
    slug: 'livro-de-historias-infantis',
    shortDescription: 'Coletânea de histórias para estimular a leitura',
    price: 35.5,
    originalPrice: 42.0,
    discountPercentage: 15,
    imageUrl: '/images/products/historias.jpg',
    categories: [{ id: 'cat-003', name: 'Leitura', slug: 'leitura' }],
    rating: 4.8,
    reviewCount: 42,
    inStock: true,
  },
  {
    id: 'prod-004',
    name: 'Jogo Educativo de Palavras',
    slug: 'jogo-educativo-de-palavras',
    shortDescription: 'Jogo para aprender novas palavras e melhorar o vocabulário',
    price: 59.9,
    imageUrl: '/images/products/jogo.jpg',
    categories: [
      { id: 'cat-001', name: 'Alfabetização', slug: 'alfabetizacao' },
      { id: 'cat-004', name: 'Jogos', slug: 'jogos' },
    ],
    rating: 4.2,
    reviewCount: 18,
    inStock: false,
  },
  {
    id: 'prod-005',
    name: 'Curso Online de Alfabetização',
    slug: 'curso-online-de-alfabetizacao',
    shortDescription: 'Curso completo para professores sobre métodos de alfabetização',
    price: 149.9,
    originalPrice: 199.9,
    discountPercentage: 25,
    imageUrl: '/images/products/curso.jpg',
    isDigital: true,
    categories: [
      { id: 'cat-001', name: 'Alfabetização', slug: 'alfabetizacao' },
      { id: 'cat-005', name: 'Cursos', slug: 'cursos' },
    ],
    rating: 4.9,
    reviewCount: 56,
    inStock: true,
  },
  {
    id: 'prod-006',
    name: 'Caderno de Caligrafia',
    slug: 'caderno-de-caligrafia',
    shortDescription: 'Caderno para prática de caligrafia e escrita',
    price: 19.9,
    imageUrl: '/images/products/caligrafia.jpg',
    categories: [{ id: 'cat-001', name: 'Alfabetização', slug: 'alfabetizacao' }],
    rating: 4.0,
    reviewCount: 12,
    inStock: true,
  },
];

// Categorias disponíveis
const categories = [
  { id: 'cat-001', name: 'Alfabetização', slug: 'alfabetizacao', count: 3 },
  { id: 'cat-002', name: 'Matemática', slug: 'matematica', count: 1 },
  { id: 'cat-003', name: 'Leitura', slug: 'leitura', count: 1 },
  { id: 'cat-004', name: 'Jogos', slug: 'jogos', count: 1 },
  { id: 'cat-005', name: 'Cursos', slug: 'cursos', count: 1 },
];

// Tags disponíveis
const tags = [
  { name: 'educação', count: 6 },
  { name: 'infantil', count: 4 },
  { name: 'aprendizagem', count: 5 },
  { name: 'professor', count: 2 },
  { name: 'digital', count: 2 },
];

// Filtrar produtos (simulação)
let filteredProducts = [...products];

if (categoryId) {
  filteredProducts = filteredProducts.filter((product) =>
    product.categories.some((cat) => cat.id === categoryId)
  );
}

if (search) {
  const searchLower = search.toLowerCase();
  filteredProducts = filteredProducts.filter(
    (product) =>
      product.name.toLowerCase().includes(searchLower) ||
      product.shortDescription?.toLowerCase().includes(searchLower)
  );
}

if (priceMin !== undefined) {
  filteredProducts = filteredProducts.filter((product) => product.price >= priceMin);
}

if (priceMax !== undefined) {
  filteredProducts = filteredProducts.filter((product) => product.price <= priceMax);
}

// Ordenar produtos (simulação)
if (sort) {
  switch (sort) {
    case 'name-asc':
      filteredProducts.sort((a, b) => a.name.localeCompare(b.name));
      break;
    case 'name-desc':
      filteredProducts.sort((a, b) => b.name.localeCompare(a.name));
      break;
    case 'price-asc':
      filteredProducts.sort((a, b) => a.price - b.price);
      break;
    case 'price-desc':
      filteredProducts.sort((a, b) => b.price - a.price);
      break;
    case 'newest':
      // Simulação - em um cenário real, ordenaríamos por data de criação
      filteredProducts.sort((a, b) => (a.isNew ? -1 : 1) - (b.isNew ? -1 : 1));
      break;
  }
}

// Paginação (simulação)
const totalProducts = filteredProducts.length;
const totalPages = Math.ceil(totalProducts / limit);
const startIndex = (page - 1) * limit;
const endIndex = startIndex + limit;
const paginatedProducts = filteredProducts.slice(startIndex, endIndex);

// Breadcrumbs
const breadcrumbItems = [{ href: '/', label: 'Início' }, { label: 'Produtos' }];

// Filtros atuais
const currentFilters = {
  categoryId,
  search,
  priceMin,
  priceMax,
  sort,
};
---

<BaseLayout title={title} enableViewTransitions={true} transitionType="fade">
  <Container>
    <PageTransition type="fade" duration="0.5s">
      <Section>
        <Breadcrumbs items={breadcrumbItems} class="mb-6" />
        
        <div class="flex justify-between items-center mb-6">
          <h1 class="text-3xl font-bold">{title}</h1>
          
          <div class="text-sm text-gray-500">
            Mostrando {paginatedProducts.length} de {totalProducts} produtos
          </div>
        </div>
        
        <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
          <!-- Coluna de filtros -->
          <div class="lg:col-span-1">
            <ProductFilters
              categories={categories}
              tags={tags}
              minPrice={0}
              maxPrice={200}
              currentFilters={currentFilters}
              baseUrl="/products"
            />
          </div>
          
          <!-- Coluna de produtos -->
          <div class="lg:col-span-3">
            {paginatedProducts.length > 0 ? (
              <div class="space-y-8">
                <ProductGrid
                  products={paginatedProducts}
                  columns={{ sm: 1, md: 2, lg: 3 }}
                  gap="md"
                />
                
                {totalPages > 1 && (
                  <div class="flex justify-center mt-8">
                    <DaisyPagination
                      currentPage={page}
                      totalPages={totalPages}
                      baseUrl="/products"
                      queryParams={currentFilters}
                    />
                  </div>
                )}
              </div>
            ) : (
              <div class="bg-base-200 rounded-lg p-12 text-center">
                <div class="text-5xl text-gray-400 mb-4">
                  <i class="icon icon-search"></i>
                </div>
                <h3 class="text-xl font-bold mb-2">Nenhum produto encontrado</h3>
                <p class="text-gray-500 mb-6">
                  Não encontramos produtos que correspondam aos filtros selecionados.
                </p>
                <a href="/products" class="btn btn-primary">
                  Ver todos os produtos
                </a>
              </div>
            )}
          </div>
        </div>
      </Section>
    </PageTransition>
  </Container>
</BaseLayout>
