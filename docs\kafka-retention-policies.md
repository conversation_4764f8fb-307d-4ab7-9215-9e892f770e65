# Políticas de Retenção de Mensagens Kafka

Este documento descreve as políticas de retenção de mensagens implementadas para os tópicos Kafka na plataforma Estação da Alfabetização.

## Visão Geral

As políticas de retenção definem por quanto tempo as mensagens são mantidas nos tópicos Kafka e como elas são gerenciadas ao longo do tempo. Implementamos políticas específicas para diferentes tipos de dados, baseadas na importância e no ciclo de vida esperado das informações.

## Níveis de Importância de Dados

Classificamos os dados em cinco níveis de importância, que determinam as políticas de retenção padrão:

| Nível | Descrição | Retenção Padrão | Política de Limpeza |
|-------|-----------|-----------------|---------------------|
| **CRITICAL** | Dados críticos para o negócio (transações financeiras) | 365 dias | Compactação + Exclusão |
| **HIGH** | Dados importantes (pedidos, usuários) | 180 dias | Compactação + Exclusão |
| **MEDIUM** | Dados de importância média (notificações) | 90 dias | Exclusão |
| **LOW** | Dados de baixa importância (analytics) | 30 dias | Exclusão |
| **TEMPORARY** | Dados temporários (logs de debug) | 7 dias | Exclusão |

## Políticas de Limpeza

Utilizamos duas principais políticas de limpeza:

1. **Delete (`delete`)**: Após o período de retenção, as mensagens são excluídas.
2. **Compact (`compact`)**: Mantém apenas a mensagem mais recente para cada chave.
3. **Compact+Delete (`compact,delete`)**: Combina compactação com exclusão após o período de retenção.

## Configurações por Domínio

### Domínio: Pagamentos

| Tópico | Importância | Retenção | Política | Justificativa |
|--------|-------------|----------|----------|---------------|
| `payment.transaction.created` | CRITICAL | 365 dias | Compact+Delete | Dados financeiros críticos com requisitos legais |
| `payment.transaction.updated` | CRITICAL | 365 dias | Compact+Delete | Histórico de alterações em transações financeiras |
| `payment.transaction.failed` | HIGH | 90 dias | Compact+Delete | Diagnóstico de falhas em transações |
| `payment.refund.requested` | CRITICAL | 365 dias | Compact+Delete | Requisitos legais para solicitações de reembolso |
| `payment.refund.processed` | CRITICAL | 365 dias | Compact+Delete | Comprovação de processamento de reembolsos |
| `payment.webhook.received` | MEDIUM | 30 dias | Delete | Notificações externas de pagamento |

### Domínio: Pedidos

| Tópico | Importância | Retenção | Política | Justificativa |
|--------|-------------|----------|----------|---------------|
| `order.created` | HIGH | 365 dias | Compact+Delete | Histórico de pedidos para fins legais e de suporte |
| `order.updated` | HIGH | 365 dias | Compact+Delete | Rastreamento de alterações em pedidos |
| `order.status.changed` | HIGH | 365 dias | Compact+Delete | Histórico de status para resolução de disputas |
| `order.cancelled` | HIGH | 365 dias | Compact+Delete | Documentação de cancelamentos para análise |
| `order.fulfilled` | HIGH | 365 dias | Compact+Delete | Comprovação de entrega de pedidos |

### Domínio: Usuários

| Tópico | Importância | Retenção | Política | Justificativa |
|--------|-------------|----------|----------|---------------|
| `user.registered` | HIGH | 365 dias | Compact | Histórico de registros para auditoria |
| `user.profile.updated` | MEDIUM | 180 dias | Compact | Histórico de alterações de perfil |
| `user.subscription.changed` | HIGH | 365 dias | Compact+Delete | Histórico de alterações de assinatura |

### Domínio: Notificações

| Tópico | Importância | Retenção | Política | Justificativa |
|--------|-------------|----------|----------|---------------|
| `notification.email.queued` | MEDIUM | 7 dias | Delete | Fila de emails para processamento |
| `notification.email.sent` | MEDIUM | 30 dias | Delete | Comprovação de envio de emails |
| `notification.email.failed` | MEDIUM | 30 dias | Delete | Diagnóstico de falhas em emails |
| `notification.alert.created` | MEDIUM | 30 dias | Delete | Histórico de alertas para análise |
| `notification.alert.resolved` | MEDIUM | 30 dias | Delete | Resolução de alertas para métricas |

### Domínio: Analytics

| Tópico | Importância | Retenção | Política | Justificativa |
|--------|-------------|----------|----------|---------------|
| `analytics.page.viewed` | LOW | 90 dias | Delete | Dados de visualização de páginas |
| `analytics.product.viewed` | LOW | 90 dias | Delete | Dados de visualização de produtos |
| `analytics.search.performed` | LOW | 90 dias | Delete | Histórico de buscas para análise |
| `analytics.cart.abandoned` | MEDIUM | 90 dias | Delete | Análise de carrinhos abandonados |
| `analytics.conversion.completed` | HIGH | 90 dias | Delete | Métricas de conversão para análise |

## Parâmetros de Configuração

Além do período de retenção básico, configuramos parâmetros adicionais para otimizar o armazenamento e processamento:

### Segmentação

- **segment.ms**: Define o período após o qual um segmento é considerado "fechado" e elegível para compactação ou exclusão.
  - Dados críticos: 7 dias
  - Dados importantes: 7 dias
  - Dados médios: 3 dias
  - Dados de baixa importância: 1 dia

### Compactação

Para tópicos com política de compactação:

- **min.compaction.lag.ms**: Tempo mínimo que uma mensagem deve permanecer não compactada (1 hora).
- **min.cleanable.dirty.ratio**: Proporção de dados "sujos" necessária para acionar a compactação (0.5 ou 50%).
- **delete.retention.ms**: Tempo que as mensagens excluídas são retidas antes da remoção final (24 horas).

## Monitoramento e Manutenção

Para garantir o funcionamento adequado das políticas de retenção:

1. **Monitoramento de Espaço**: Monitoramos regularmente o uso de disco pelos tópicos Kafka.
2. **Alertas de Capacidade**: Configuramos alertas para quando o uso de disco atinge 70% e 85% da capacidade.
3. **Revisão Periódica**: Revisamos as políticas de retenção trimestralmente para ajustar conforme necessário.
4. **Verificação de Compactação**: Monitoramos a eficácia da compactação para tópicos críticos.

## Considerações Legais

Algumas políticas de retenção são definidas com base em requisitos legais:

- Dados de transações financeiras: mínimo de 365 dias (requisitos fiscais)
- Dados de pedidos: mínimo de 365 dias (código de defesa do consumidor)
- Dados de usuários: conforme política de privacidade e LGPD

## Implementação Técnica

A implementação das políticas de retenção é feita através dos seguintes componentes:

1. **kafka-retention.config.ts**: Define as configurações de retenção para cada tópico.
2. **kafka-setup.ts**: Aplica as configurações durante a criação e manutenção dos tópicos.

Para mais detalhes sobre a implementação, consulte o código-fonte desses arquivos.
