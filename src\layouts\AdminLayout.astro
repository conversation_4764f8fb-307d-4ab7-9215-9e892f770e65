---
import { actions } from 'astro:actions';
import AdminFooter from '@components/layout/AdminFooter.astro';
import AdminHeader from '@components/layout/AdminHeader.astro';
import BaseLayout from './BaseLayout.astro';

export interface Props {
  title: string;
}

const { title } = Astro.props;
const user = Astro.locals.user;
---

<script>
  interface ActionEventDetail {
    result?: {
      redirect?: string;
    };
  }

  // Processa o redirecionamento retornado pela action
  document.addEventListener('astro:action', ((event: CustomEvent<ActionEventDetail>) => {
    const result = event.detail?.result;
    if (result?.redirect) {
      window.location.href = result.redirect;
    }
  }) as EventListener);
</script>

<BaseLayout title={title}>
  <div class="container">
    <div class="header">
      <AdminHeader/>
    </div>
    <div class="aside">
      <nav>
        <ul class="menu lg:menu-horizontal bg-primary rounded-box p-4 text-white">
          <li><a href="/admin">Painel</a></li>
          <li><summary>Cadastros</summary>
            <ul>
              <li><a href="/admin/register/category">Categorias</a></li>
              <li><a href="/admin/register/product">Produtos</a></li>
              <li><summary>Pagamento</summary>
                <ul>
                  <li><a href="/admin/register/payment/status">Status de Pagamento</a></li>
                  <li><a href="/admin/register/payment/type">Tipos de Pagamento</a></li>
                </ul>
              </li>
              <li><a href="/admin/register/order/status">Status de Venda</a></li>
              <li><summary>Usuário</summary>
                <ul>
                  <li><a href="/admin/register/user">Usuários</a></li>
                  <li><a href="/admin/register/user/school-type">Tipos de Escola</a></li>
                  <li><a href="/admin/register/user/type">Tipos de Usuário</a></li>
                </ul>
              </li>
            </ul>
          </li>
          <li><summary>Financeiro</summary>
            <ul>
              <li><a href="/admin/financial/invoice">Nota Fiscal</a></li>
              <li><a href="/admin/financial/payment">Pagamentos</a></li>
              <li><a href="/admin/financial/order">Vendas</a></li>
            </ul>
          </li>
          <li><a href="/admin/messaging/post">Postagens</a></li>
          <li class="ml-auto">
            <div class="flex items-center gap-4">
              <span class="text-sm opacity-90">Olá, {user?.name}</span>
              <form method="POST" action={actions.authAction.logout}>
                <button type="submit" class="btn btn-error btn-sm">Sair</button>
              </form>
            </div>
          </li>
        </ul>
      </nav>
    </div>  
    <div class="main">
      <slot />
    </div>
    <div class="footer">
      <AdminFooter/>
    </div>
  </div>
</BaseLayout>

<style>
  .aside {
    grid-area: aside;
  }
  
  .container {
    display: grid;
    grid   : "header header header header header"
             "aside aside aside aside aside"
             "main main main main main"
             "footer footer footer footer footer";
  }

  .footer {
    grid-area: footer;
  }

  .header {
    grid-area: header;
  }

  .main {
    grid-area: main;
  }
</style>