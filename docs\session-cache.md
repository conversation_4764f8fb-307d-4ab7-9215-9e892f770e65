# Sistema de Cache de Sessão com Valkey

## Visão Geral

O sistema de cache de sessão implementa armazenamento e gerenciamento de sessões utilizando o Valkey como backend, oferecendo alta performance, persistência e escalabilidade. Este sistema substitui o armazenamento tradicional de sessões em memória, proporcionando maior confiabilidade e recursos avançados.

## Arquitetura

O sistema de cache de sessão é composto pelos seguintes componentes:

1. **Serviço de Cache de Sessão (valkeySessionService)**: Implementa a lógica de armazenamento e gerenciamento de sessões utilizando o Valkey.
2. **Middleware de Sessão (sessionMiddleware)**: Intercepta requisições HTTP e gerencia cookies e sessões.
3. **Interface de Administração**: Permite visualizar e gerenciar sessões ativas.
4. **Integração com Monitoramento**: Registra métricas e eventos para análise.

## Funcionalidades

### Armazenamento de Sessões

- **Persistência**: Sessões são armazenadas no Valkey, permitindo que sobrevivam a reinicializações do servidor.
- **TTL Configurável**: Tempo de vida das sessões pode ser configurado por tipo de sessão.
- **Dados Estruturados**: Suporte para armazenamento de dados complexos em formato JSON.

### Gerenciamento de Sessões

- **Criação**: Criação de sessões para usuários autenticados e anônimos.
- **Atualização**: Atualização de dados de sessão de forma eficiente.
- **Renovação**: Renovação automática de sessões próximas da expiração.
- **Invalidação**: Invalidação seletiva por sessão, usuário ou IP.

### Segurança

- **Validação de IP**: Opção para validar IP do cliente para prevenir roubo de sessão.
- **Validação de User-Agent**: Opção para validar User-Agent para segurança adicional.
- **Invalidação de Emergência**: Capacidade de invalidar todas as sessões em caso de comprometimento.

### Monitoramento

- **Métricas**: Coleta de métricas sobre criação, uso e expiração de sessões.
- **Logs**: Registro de eventos importantes para auditoria e diagnóstico.
- **Estatísticas**: Visualização de estatísticas em tempo real sobre sessões ativas.

## Implementação Técnica

### Estrutura de Dados

As sessões são armazenadas no Valkey com a seguinte estrutura:

```typescript
interface SessionData {
  id: string;              // ID único da sessão
  userId: string;          // ID do usuário associado
  userName: string;        // Nome do usuário
  userEmail: string;       // Email do usuário
  data: Record<string, any>; // Dados adicionais da sessão
  createdAt: number;       // Timestamp de criação
  lastActivity: number;    // Timestamp da última atividade
  ipAddress?: string;      // Endereço IP associado
  userAgent?: string;      // User-Agent associado
  expiresAt?: number;      // Timestamp de expiração
}
```

### Chaves no Valkey

O sistema utiliza os seguintes padrões de chaves no Valkey:

- `session:{id}`: Armazena os dados da sessão.
- `user:sessions:{userId}`: Conjunto (Set) de IDs de sessão para um usuário.
- `ip:sessions:{ipAddress}`: Conjunto (Set) de IDs de sessão para um IP.

### Scripts Lua

O sistema utiliza scripts Lua para operações atômicas no Valkey:

- **Renovação de Sessão**: Script para renovar uma sessão de forma atômica, atualizando todos os índices.
- **Invalidação por Padrão**: Script para invalidar múltiplas sessões com base em um padrão de chave.

### Middleware

O middleware `sessionMiddleware` intercepta requisições HTTP e:

1. Verifica se há um cookie de sessão válido.
2. Obtém os dados da sessão do Valkey.
3. Verifica se a sessão expirou ou precisa ser renovada.
4. Disponibiliza métodos para manipulação da sessão no contexto da requisição.
5. Cria sessões anônimas quando necessário.

### Integração com JWT

O sistema integra-se com o sistema de autenticação JWT:

1. Se não houver sessão, mas houver um token JWT válido, uma sessão é criada automaticamente.
2. Dados do usuário do token JWT são utilizados para criar a sessão.
3. A sessão é associada ao usuário autenticado.

## Uso do Sistema

### Acesso à Sessão em Componentes Astro

```typescript
// Em um componente Astro
const { session } = Astro.locals;

// Obter valor da sessão
const userId = await session.get('userId');

// Definir valor na sessão
await session.set('lastVisited', new Date().toISOString());

// Remover valor da sessão
await session.unset('temporaryData');

// Destruir a sessão
await session.destroy();

// Renovar a sessão manualmente
await session.renew();
```

### Configuração de Opções

```typescript
// Criar sessão com opções personalizadas
const session = await valkeySessionService.create(
  userId,
  userName,
  userEmail,
  data,
  ipAddress,
  userAgent,
  {
    ttl: 3600, // 1 hora
    autoRenew: true,
    renewThreshold: 0.8, // Renovar quando 80% do TTL tiver passado
    validateIp: true,
    validateUserAgent: true
  }
);
```

### Invalidação de Sessões

```typescript
// Invalidar sessão específica
await valkeySessionService.delete(sessionId);

// Invalidar todas as sessões de um usuário
await valkeySessionService.invalidateUserSessions(userId);

// Invalidar todas as sessões de um IP
await valkeySessionService.invalidateIpSessions(ipAddress);

// Invalidar todas as sessões (emergência)
await valkeySessionService.invalidateAllSessions();
```

## Interface de Administração

A interface de administração (`/admin/sessions`) permite:

- Visualizar estatísticas de sessões ativas.
- Invalidar sessões por ID, usuário ou IP.
- Invalidar todas as sessões em caso de emergência.

## Monitoramento e Métricas

O sistema registra as seguintes métricas:

- `session_created`: Contador de sessões criadas.
- `session_created_from_jwt`: Contador de sessões criadas a partir de JWT.
- `session_created_anonymous`: Contador de sessões anônimas criadas.
- `session_renewed_auto`: Contador de renovações automáticas de sessão.
- `session_renewed_manual`: Contador de renovações manuais de sessão.
- `session_expired`: Contador de sessões expiradas.
- `session_invalid`: Contador de sessões inválidas.
- `session_destroyed`: Contador de sessões destruídas.
- `session_hit`: Contador de acessos a sessões.
- `session_error`: Contador de erros relacionados a sessões.
- `session_processing_time`: Métrica de tempo de processamento de sessão.
- `session_duration`: Métrica de duração média das sessões.

## Boas Práticas

1. **Armazenamento Mínimo**: Armazene apenas dados essenciais na sessão para minimizar o uso de memória.
2. **TTL Apropriado**: Configure TTLs apropriados para cada tipo de sessão.
3. **Renovação Automática**: Utilize a renovação automática para melhorar a experiência do usuário.
4. **Monitoramento**: Monitore métricas de sessão para identificar problemas.
5. **Segurança**: Utilize validação de IP e User-Agent para sessões sensíveis.

## Troubleshooting

### Problemas Comuns

1. **Sessões Expirando Prematuramente**: 
   - Verifique o TTL configurado.
   - Verifique se a renovação automática está habilitada.

2. **Alto Uso de Memória no Valkey**:
   - Reduza a quantidade de dados armazenados nas sessões.
   - Ajuste o TTL para valores menores.
   - Configure limpeza periódica de sessões expiradas.

3. **Lentidão no Processamento de Sessões**:
   - Verifique a performance do Valkey.
   - Otimize scripts Lua.
   - Considere sharding ou clustering do Valkey.

### Logs

O sistema registra eventos importantes nos logs:

- Inicialização do serviço
- Criação e renovação de sessões
- Invalidação de sessões
- Erros de processamento

## Referências

- [Valkey Documentation](https://valkey.io/docs/)
- [OWASP Session Management Cheat Sheet](https://cheatsheetseries.owasp.org/cheatsheets/Session_Management_Cheat_Sheet.html)
- [Redis Sessions Best Practices](https://redis.io/docs/manual/patterns/session-management/)
