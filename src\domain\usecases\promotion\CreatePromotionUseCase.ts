/**
 * Create Promotion Use Case
 *
 * Caso de uso para criar uma nova promoção.
 * Parte da implementação da tarefa 8.4.3 - Promoções
 */

import {
  Promotion,
  PromotionRestriction,
  PromotionRule,
  PromotionTarget,
} from '../../entities/Promotion';
import { PromotionRepository } from '../../repositories/PromotionRepository';

export interface CreatePromotionRequest {
  name: string;
  description?: string;
  rules: Array<{
    type: 'percentage' | 'fixed_amount' | 'buy_x_get_y' | 'bundle' | 'free_shipping';
    value: number;
    secondaryValue?: number;
    target: {
      productIds?: string[];
      categoryIds?: string[];
      collectionIds?: string[];
      excludedProductIds?: string[];
      excludedCategoryIds?: string[];
      minPurchaseAmount?: number;
      minQuantity?: number;
    };
    stackable?: boolean;
    priority?: number;
  }>;
  restrictions?: {
    userGroups?: string[];
    newCustomersOnly?: boolean;
    maxUsesPerCustomer?: number;
    maxUses?: number;
    minCartValue?: number;
    couponRequired?: boolean;
    couponCode?: string;
  };
  startDate?: Date;
  endDate?: Date;
  isActive?: boolean;
  createdBy?: string;
}

export interface CreatePromotionResponse {
  success: boolean;
  promotion?: Promotion;
  error?: string;
}

export class CreatePromotionUseCase {
  constructor(private promotionRepository: PromotionRepository) {}

  async execute(request: CreatePromotionRequest): Promise<CreatePromotionResponse> {
    try {
      // Validar os dados de entrada
      if (!this.validateRequest(request)) {
        return {
          success: false,
          error: 'Dados inválidos para criação de promoção.',
        };
      }

      // Criar regras de promoção
      const rules: PromotionRule[] = request.rules.map((rule) => ({
        type: rule.type,
        value: rule.value,
        secondaryValue: rule.secondaryValue,
        target: rule.target as PromotionTarget,
        stackable: rule.stackable !== undefined ? rule.stackable : true,
        priority: rule.priority || 0,
      }));

      // Criar restrições, se fornecidas
      let restrictions: PromotionRestriction | undefined;

      if (request.restrictions) {
        restrictions = request.restrictions as PromotionRestriction;
      }

      // Criar a promoção
      const promotion = new Promotion({
        id: crypto.randomUUID(),
        name: request.name,
        description: request.description,
        rules,
        restrictions,
        startDate: request.startDate,
        endDate: request.endDate,
        isActive: request.isActive !== undefined ? request.isActive : true,
        createdBy: request.createdBy,
      });

      // Salvar a promoção
      const savedPromotion = await this.promotionRepository.create(promotion);

      return {
        success: true,
        promotion: savedPromotion,
      };
    } catch (error) {
      console.error('Erro ao criar promoção:', error);
      return {
        success: false,
        error: 'Ocorreu um erro ao criar a promoção.',
      };
    }
  }

  private validateRequest(request: CreatePromotionRequest): boolean {
    // Validar nome
    if (!request.name || request.name.trim().length === 0) {
      return false;
    }

    // Validar regras
    if (!request.rules || request.rules.length === 0) {
      return false;
    }

    // Validar cada regra
    for (const rule of request.rules) {
      // Validar tipo de regra
      if (
        !['percentage', 'fixed_amount', 'buy_x_get_y', 'bundle', 'free_shipping'].includes(
          rule.type
        )
      ) {
        return false;
      }

      // Validar valor
      if (rule.value < 0) {
        return false;
      }

      // Validar valor percentual (máximo 100%)
      if (rule.type === 'percentage' && rule.value > 100) {
        return false;
      }

      // Validar regra de "compre X leve Y"
      if (rule.type === 'buy_x_get_y') {
        if (rule.value <= 0 || (rule.secondaryValue !== undefined && rule.secondaryValue <= 0)) {
          return false;
        }
      }

      // Validar target
      if (!rule.target) {
        return false;
      }
    }

    // Validar datas
    if (request.startDate && request.endDate && request.startDate > request.endDate) {
      return false;
    }

    return true;
  }
}
