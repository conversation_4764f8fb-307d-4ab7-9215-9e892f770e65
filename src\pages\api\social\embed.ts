/**
 * Social Media Embed API
 *
 * Endpoint para obter código de incorporação de posts de redes sociais.
 * Parte da implementação da tarefa 8.9.2 - Integração com redes sociais
 */

import type { APIRoute } from 'astro';
import axios from 'axios';

export const GET: APIRoute = async ({ request }) => {
  try {
    // Obter parâmetros da URL
    const url = new URL(request.url);
    const postUrl = url.searchParams.get('url');
    const platform = url.searchParams.get('platform');
    const maxWidth = url.searchParams.get('maxWidth') || '550';
    const maxHeight = url.searchParams.get('maxHeight') || '600';
    const theme = url.searchParams.get('theme') || 'light';

    if (!postUrl) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Parâmetro url é obrigatório.',
        }),
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    if (
      !platform ||
      !['facebook', 'twitter', 'instagram', 'linkedin', 'youtube'].includes(platform)
    ) {
      return new Response(
        JSON.stringify({
          success: false,
          error:
            'Plataforma inválida. Plataformas suportadas: facebook, twitter, instagram, linkedin, youtube.',
        }),
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    // Obter código de incorporação
    let embedCode = '';

    switch (platform) {
      case 'facebook':
        embedCode = await getFacebookEmbedCode(postUrl, maxWidth);
        break;

      case 'twitter':
        embedCode = await getTwitterEmbedCode(postUrl, theme);
        break;

      case 'instagram':
        embedCode = await getInstagramEmbedCode(postUrl, maxWidth);
        break;

      case 'linkedin':
        // LinkedIn não oferece API pública para incorporação
        embedCode = generateLinkedinEmbedCode(postUrl, maxWidth, maxHeight);
        break;

      case 'youtube':
        embedCode = generateYoutubeEmbedCode(postUrl, maxWidth, maxHeight);
        break;
    }

    return new Response(
      JSON.stringify({
        success: true,
        data: {
          html: embedCode,
          platform,
          url: postUrl,
        },
      }),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'public, max-age=3600',
        },
      }
    );
  } catch (error) {
    console.error('Erro ao obter código de incorporação:', error);

    return new Response(
      JSON.stringify({
        success: false,
        error: 'Erro ao obter código de incorporação.',
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  }
};

/**
 * Obtém código de incorporação do Facebook
 */
async function getFacebookEmbedCode(url: string, maxWidth: string): Promise<string> {
  try {
    const response = await axios.get('https://graph.facebook.com/v16.0/oembed_post', {
      params: {
        url,
        maxwidth: maxWidth,
        access_token: `${process.env.FACEBOOK_APP_ID}|${process.env.FACEBOOK_APP_SECRET}`,
      },
    });

    return response.data.html;
  } catch (error) {
    console.error('Erro ao obter código de incorporação do Facebook:', error);

    // Fallback para código de incorporação estático
    return `
      <div id="fb-root"></div>
      <div class="fb-post" 
        data-href="${url}" 
        data-width="${maxWidth}" 
        data-show-text="true">
        <blockquote cite="${url}" class="fb-xfbml-parse-ignore">
          <a href="${url}">Ver post no Facebook</a>
        </blockquote>
      </div>
      <script async defer src="https://connect.facebook.net/pt_BR/sdk.js#xfbml=1&version=v16.0"></script>
    `;
  }
}

/**
 * Obtém código de incorporação do Twitter
 */
async function getTwitterEmbedCode(url: string, theme: string): Promise<string> {
  try {
    const response = await axios.get('https://publish.twitter.com/oembed', {
      params: {
        url,
        theme,
        lang: 'pt',
        dnt: true,
      },
    });

    return response.data.html;
  } catch (error) {
    console.error('Erro ao obter código de incorporação do Twitter:', error);

    // Fallback para código de incorporação estático
    return `
      <blockquote class="twitter-tweet" data-theme="${theme}" data-lang="pt">
        <a href="${url}">Ver tweet no Twitter</a>
      </blockquote>
      <script async src="https://platform.twitter.com/widgets.js" charset="utf-8"></script>
    `;
  }
}

/**
 * Obtém código de incorporação do Instagram
 */
async function getInstagramEmbedCode(url: string, maxWidth: string): Promise<string> {
  try {
    const response = await axios.get('https://api.instagram.com/oembed', {
      params: {
        url,
        maxwidth: maxWidth,
        hidecaption: false,
      },
    });

    return response.data.html;
  } catch (error) {
    console.error('Erro ao obter código de incorporação do Instagram:', error);

    // Fallback para código de incorporação estático
    return `
      <blockquote class="instagram-media" data-instgrm-permalink="${url}" 
        data-instgrm-version="14" style="width: 100%; max-width: ${maxWidth}px;">
        <a href="${url}">Ver post no Instagram</a>
      </blockquote>
      <script async src="//www.instagram.com/embed.js"></script>
    `;
  }
}

/**
 * Gera código de incorporação do LinkedIn
 */
function generateLinkedinEmbedCode(url: string, maxWidth: string, maxHeight: string): string {
  // Extrair ID do post da URL
  const postId = url.split('/').pop() || '';

  return `
    <div class="linkedin-post">
      <iframe src="https://www.linkedin.com/embed/feed/update/${postId}" 
        height="${maxHeight}" width="${maxWidth}" frameborder="0" allowfullscreen></iframe>
    </div>
  `;
}

/**
 * Gera código de incorporação do YouTube
 */
function generateYoutubeEmbedCode(url: string, maxWidth: string, maxHeight: string): string {
  // Extrair ID do vídeo da URL
  const videoId = extractYoutubeVideoId(url);

  return `
    <div class="youtube-video">
      <iframe width="${maxWidth}" height="${maxHeight}" 
        src="https://www.youtube.com/embed/${videoId}" 
        frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" 
        allowfullscreen></iframe>
    </div>
  `;
}

/**
 * Extrai o ID de um vídeo do YouTube a partir da URL
 */
function extractYoutubeVideoId(url: string): string {
  const regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|&v=)([^#&?]*).*/;
  const match = url.match(regExp);

  return match && match[2].length === 11 ? match[2] : '';
}
