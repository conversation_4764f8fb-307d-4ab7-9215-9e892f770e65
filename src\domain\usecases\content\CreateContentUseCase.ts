/**
 * Create Content Use Case
 *
 * Caso de uso para criar um novo conteúdo.
 * Parte da implementação da tarefa 8.8.3 - Gestão de conteúdo
 */

import { nanoid } from 'nanoid';
import {
  Content,
  ContentMeta,
  ContentSEO,
  ContentStatus,
  ContentType,
} from '../../entities/Content';
import { ContentCategoryRepository } from '../../repositories/ContentCategoryRepository';
import { ContentRepository } from '../../repositories/ContentRepository';
import { ContentTagRepository } from '../../repositories/ContentTagRepository';
import { SlugService } from '../../services/SlugService';

export interface CreateContentRequest {
  title: string;
  body: string;
  type: ContentType;
  status?: ContentStatus;
  meta?: ContentMeta;
  seo?: ContentSEO;
  categoryIds?: string[];
  tagIds?: string[];
  slug?: string;
  scheduledAt?: Date;
  userId: string;
}

export interface CreateContentResponse {
  success: boolean;
  data?: Content;
  error?: string;
}

export class CreateContentUseCase {
  constructor(
    private contentRepository: ContentRepository,
    private categoryRepository: ContentCategoryRepository,
    private tagRepository: ContentTagRepository,
    private slugService: SlugService
  ) {}

  async execute(request: CreateContentRequest): Promise<CreateContentResponse> {
    try {
      // Validar os dados de entrada
      if (!request.title || request.title.trim().length === 0) {
        return {
          success: false,
          error: 'O título é obrigatório.',
        };
      }

      if (!request.body) {
        return {
          success: false,
          error: 'O conteúdo é obrigatório.',
        };
      }

      if (!request.type) {
        return {
          success: false,
          error: 'O tipo de conteúdo é obrigatório.',
        };
      }

      if (!request.userId) {
        return {
          success: false,
          error: 'O ID do usuário é obrigatório.',
        };
      }

      // Gerar slug se não fornecido
      let slug = request.slug;

      if (!slug) {
        slug = this.slugService.generate(request.title);
      } else {
        slug = this.slugService.normalize(slug);
      }

      // Verificar se o slug já existe
      const slugExists = await this.contentRepository.slugExists(slug);

      if (slugExists) {
        // Adicionar um sufixo único ao slug
        slug = `${slug}-${Date.now().toString().slice(-6)}`;
      }

      // Validar categorias
      if (request.categoryIds && request.categoryIds.length > 0) {
        for (const categoryId of request.categoryIds) {
          const category = await this.categoryRepository.getById(categoryId);

          if (!category) {
            return {
              success: false,
              error: `Categoria com ID ${categoryId} não encontrada.`,
            };
          }
        }
      }

      // Validar tags
      if (request.tagIds && request.tagIds.length > 0) {
        for (const tagId of request.tagIds) {
          const tag = await this.tagRepository.getById(tagId);

          if (!tag) {
            return {
              success: false,
              error: `Tag com ID ${tagId} não encontrada.`,
            };
          }
        }
      }

      // Definir status
      const status: ContentStatus = request.status || 'draft';

      // Se o status for 'scheduled', verificar se há data agendada
      if (status === 'scheduled' && !request.scheduledAt) {
        return {
          success: false,
          error: 'Data de agendamento é obrigatória para conteúdo agendado.',
        };
      }

      // Verificar se a data de agendamento é futura
      if (status === 'scheduled' && request.scheduledAt && request.scheduledAt <= new Date()) {
        return {
          success: false,
          error: 'Data de agendamento deve ser futura.',
        };
      }

      // Criar meta dados padrão se não fornecidos
      const meta: ContentMeta = request.meta || {
        title: request.title,
        description: request.body.substring(0, 160).replace(/<[^>]*>/g, ''),
      };

      // Criar conteúdo
      const content = new Content({
        id: nanoid(),
        slug,
        type: request.type,
        status,
        title: request.title,
        body: request.body,
        meta,
        seo: request.seo,
        categoryIds: request.categoryIds,
        tagIds: request.tagIds,
        scheduledAt: status === 'scheduled' ? request.scheduledAt : undefined,
        publishedAt: status === 'published' ? new Date() : undefined,
        createdBy: request.userId,
        updatedBy: request.userId,
      });

      // Salvar conteúdo
      const createdContent = await this.contentRepository.create(content);

      return {
        success: true,
        data: createdContent,
      };
    } catch (error) {
      console.error('Erro ao criar conteúdo:', error);

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Erro desconhecido ao criar conteúdo.',
      };
    }
  }
}
