/**
 * Serviço de usuários
 *
 * Este serviço demonstra o uso do Dependency Inversion Principle
 * ao depender da abstração Logger, não de implementações concretas.
 */

import { v4 as uuidv4 } from 'uuid';
import { queryHelper } from '../db/queryHelper';
import { Logger } from '../domain/interfaces/Logger';
import { getLogger } from '../infrastructure/logging';

/**
 * Interface para usuário
 */
export interface User {
  /**
   * ID do usuário
   */
  id: string;

  /**
   * Nome do usuário
   */
  name: string;

  /**
   * Email do usuário
   */
  email: string;

  /**
   * Data de criação
   */
  createdAt: Date;

  /**
   * Data de atualização
   */
  updatedAt: Date;
}

/**
 * Dados para criação de usuário
 */
export interface CreateUserData {
  /**
   * Nome do usuário
   */
  name: string;

  /**
   * Email do usuário
   */
  email: string;

  /**
   * Senha do usuário
   */
  password: string;
}

/**
 * Serviço de usuários
 */
export class UserService {
  /**
   * Logger para este serviço
   */
  private readonly logger: Logger;

  /**
   * Cria uma nova instância de UserService
   * @param logger - Logger a ser usado (opcional, usa o logger padrão se não fornecido)
   */
  constructor(logger?: Logger) {
    // Usar o logger fornecido ou obter um novo para este contexto
    this.logger = logger || getLogger('UserService');
  }

  /**
   * Cria um novo usuário
   * @param data - Dados do usuário
   * @returns Usuário criado
   */
  public async createUser(data: CreateUserData): Promise<User> {
    try {
      this.logger.info('Creating new user', { email: data.email });

      // Verificar se o email já está em uso
      const existingUser = await this.getUserByEmail(data.email);

      if (existingUser) {
        this.logger.warn('Email already in use', { email: data.email });
        throw new Error('Email already in use');
      }

      // Gerar ID para o usuário
      const userId = uuidv4();

      // Criar usuário no banco de dados
      const now = new Date();

      await queryHelper.query(
        `INSERT INTO tab_user (
          ulid_user, name, email, password_hash, created_at, updated_at
        ) VALUES ($1, $2, $3, $4, $5, $6)`,
        [
          userId,
          data.name,
          data.email,
          'hash_placeholder', // Em uma implementação real, a senha seria hasheada
          now,
          now,
        ]
      );

      // Retornar usuário criado
      const user: User = {
        id: userId,
        name: data.name,
        email: data.email,
        createdAt: now,
        updatedAt: now,
      };

      this.logger.info('User created successfully', { userId });

      return user;
    } catch (error) {
      this.logger.error('Failed to create user', {
        email: data.email,
        error: error instanceof Error ? error.message : String(error),
      });

      throw error;
    }
  }

  /**
   * Obtém um usuário pelo ID
   * @param userId - ID do usuário
   * @returns Usuário ou null se não encontrado
   */
  public async getUserById(userId: string): Promise<User | null> {
    try {
      this.logger.debug('Getting user by ID', { userId });

      const result = await queryHelper.queryOne(
        `SELECT ulid_user, name, email, created_at, updated_at
         FROM tab_user
         WHERE ulid_user = $1`,
        [userId]
      );

      if (!result) {
        this.logger.debug('User not found', { userId });
        return null;
      }

      const user: User = {
        id: result.ulid_user,
        name: result.name,
        email: result.email,
        createdAt: new Date(result.created_at),
        updatedAt: new Date(result.updated_at),
      };

      return user;
    } catch (error) {
      this.logger.error('Failed to get user by ID', {
        userId,
        error: error instanceof Error ? error.message : String(error),
      });

      throw error;
    }
  }

  /**
   * Obtém um usuário pelo email
   * @param email - Email do usuário
   * @returns Usuário ou null se não encontrado
   */
  public async getUserByEmail(email: string): Promise<User | null> {
    try {
      this.logger.debug('Getting user by email', { email });

      const result = await queryHelper.queryOne(
        `SELECT ulid_user, name, email, created_at, updated_at
         FROM tab_user
         WHERE email = $1`,
        [email]
      );

      if (!result) {
        this.logger.debug('User not found', { email });
        return null;
      }

      const user: User = {
        id: result.ulid_user,
        name: result.name,
        email: result.email,
        createdAt: new Date(result.created_at),
        updatedAt: new Date(result.updated_at),
      };

      return user;
    } catch (error) {
      this.logger.error('Failed to get user by email', {
        email,
        error: error instanceof Error ? error.message : String(error),
      });

      throw error;
    }
  }

  /**
   * Atualiza um usuário
   * @param userId - ID do usuário
   * @param data - Dados a serem atualizados
   * @returns Usuário atualizado
   */
  public async updateUser(
    userId: string,
    data: Partial<{ name: string; email: string }>
  ): Promise<User> {
    try {
      this.logger.info('Updating user', { userId });

      // Verificar se o usuário existe
      const existingUser = await this.getUserById(userId);

      if (!existingUser) {
        this.logger.warn('User not found', { userId });
        throw new Error('User not found');
      }

      // Verificar se o email já está em uso por outro usuário
      if (data.email && data.email !== existingUser.email) {
        const userWithEmail = await this.getUserByEmail(data.email);

        if (userWithEmail && userWithEmail.id !== userId) {
          this.logger.warn('Email already in use', { email: data.email });
          throw new Error('Email already in use');
        }
      }

      // Construir query de atualização
      const updates: string[] = [];
      const values: any[] = [];
      let paramIndex = 1;

      if (data.name) {
        updates.push(`name = $${paramIndex++}`);
        values.push(data.name);
      }

      if (data.email) {
        updates.push(`email = $${paramIndex++}`);
        values.push(data.email);
      }

      updates.push(`updated_at = $${paramIndex++}`);
      const now = new Date();
      values.push(now);

      values.push(userId);

      // Atualizar usuário no banco de dados
      await queryHelper.query(
        `UPDATE tab_user
         SET ${updates.join(', ')}
         WHERE ulid_user = $${paramIndex}`,
        values
      );

      // Retornar usuário atualizado
      const updatedUser: User = {
        ...existingUser,
        name: data.name || existingUser.name,
        email: data.email || existingUser.email,
        updatedAt: now,
      };

      this.logger.info('User updated successfully', { userId });

      return updatedUser;
    } catch (error) {
      this.logger.error('Failed to update user', {
        userId,
        error: error instanceof Error ? error.message : String(error),
      });

      throw error;
    }
  }

  /**
   * Remove um usuário
   * @param userId - ID do usuário
   * @returns true se o usuário foi removido
   */
  public async deleteUser(userId: string): Promise<boolean> {
    try {
      this.logger.info('Deleting user', { userId });

      // Verificar se o usuário existe
      const existingUser = await this.getUserById(userId);

      if (!existingUser) {
        this.logger.warn('User not found', { userId });
        return false;
      }

      // Remover usuário do banco de dados
      await queryHelper.query('DELETE FROM tab_user WHERE ulid_user = $1', [userId]);

      this.logger.info('User deleted successfully', { userId });

      return true;
    } catch (error) {
      this.logger.error('Failed to delete user', {
        userId,
        error: error instanceof Error ? error.message : String(error),
      });

      throw error;
    }
  }
}
