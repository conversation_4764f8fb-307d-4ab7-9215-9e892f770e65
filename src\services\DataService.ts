/**
 * Serviço de dados com cache
 *
 * Este serviço implementa métodos para obter dados com cache,
 * seguindo a arquitetura sem API tradicional.
 */

import { PostgresConnection } from '../infrastructure/database/PostgresConnection';
import { ConsoleLogger } from '../infrastructure/logging/ConsoleLogger';
import { getDatabaseConfig } from '../infrastructure/config/database.config';
import { LogLevel } from '../application/interfaces/services/Logger';
import { getCacheValue, setCacheValue } from '../infrastructure/cache/CacheService';
import { EducationalContent } from '../domain/entities/EducationalContent';
import { ContentType } from '../domain/value-objects/ContentType';
import { ContentStatus } from '../domain/value-objects/ContentStatus';
import { AgeRange } from '../domain/value-objects/AgeRange';

// Inicializar logger
const logger = new ConsoleLogger('DataService', {
  level: LogLevel.INFO,
  useColors: true,
  format: 'text'
});

// Inicializar conexão com o banco de dados
const dbConfig = getDatabaseConfig();
const dbConnection = new PostgresConnection(dbConfig, logger);

/**
 * Obtém conteúdos educacionais em destaque
 *
 * @param limit Limite de conteúdos a serem retornados
 * @returns Lista de conteúdos educacionais em destaque
 */
export async function getFeaturedContent(limit: number = 6): Promise<EducationalContent[]> {
  try {
    // Chave de cache
    const cacheKey = `featured_content:${limit}`;

    // Tentar obter do cache
    const cachedData = await getCacheValue<EducationalContent[]>(cacheKey);

    // Se encontrou no cache, retornar
    if (cachedData) {
      logger.info(`Obtido conteúdo em destaque do cache (${limit} itens)`);
      return cachedData;
    }

    // Consultar banco de dados
    const query = `
      SELECT * FROM educational_contents
      WHERE status = $1 AND featured = true
      ORDER BY published_at DESC
      LIMIT $2
    `;

    const result = await dbConnection.query(query, [ContentStatus.PUBLISHED, limit]);

    // Mapear resultados para entidades
    const contents = result.rows.map(row => mapToEducationalContent(row));

    // Armazenar no cache (30 minutos)
    await setCacheValue(cacheKey, contents, 1800);

    return contents;
  } catch (error) {
    // Registrar erro
    logger.error('Erro ao obter conteúdos em destaque', error as Error);

    // Em caso de erro, retornar array vazio
    return [];
  }
}

/**
 * Obtém conteúdos educacionais por tipo
 *
 * @param type Tipo de conteúdo
 * @param limit Limite de conteúdos a serem retornados
 * @returns Lista de conteúdos educacionais do tipo especificado
 */
export async function getContentByType(type: ContentType, limit: number = 10): Promise<EducationalContent[]> {
  try {
    // Chave de cache
    const cacheKey = `content_by_type:${type}:${limit}`;

    // Tentar obter do cache
    const cachedData = await getCacheValue<EducationalContent[]>(cacheKey);

    // Se encontrou no cache, retornar
    if (cachedData) {
      logger.info(`Obtido conteúdo do tipo ${type} do cache (${limit} itens)`);
      return cachedData;
    }

    // Consultar banco de dados
    const query = `
      SELECT * FROM educational_contents
      WHERE status = $1 AND type = $2
      ORDER BY published_at DESC
      LIMIT $3
    `;

    const result = await dbConnection.query(query, [ContentStatus.PUBLISHED, type, limit]);

    // Mapear resultados para entidades
    const contents = result.rows.map(row => mapToEducationalContent(row));

    // Armazenar no cache (1 hora)
    await setCacheValue(cacheKey, contents, 3600);

    return contents;
  } catch (error) {
    // Registrar erro
    logger.error(`Erro ao obter conteúdos do tipo ${type}`, error as Error);

    // Em caso de erro, retornar array vazio
    return [];
  }
}

/**
 * Obtém conteúdos educacionais por faixa etária
 *
 * @param minAge Idade mínima
 * @param maxAge Idade máxima
 * @param limit Limite de conteúdos a serem retornados
 * @returns Lista de conteúdos educacionais para a faixa etária especificada
 */
export async function getContentByAgeRange(minAge: number, maxAge: number, limit: number = 10): Promise<EducationalContent[]> {
  try {
    // Chave de cache
    const cacheKey = `content_by_age:${minAge}-${maxAge}:${limit}`;

    // Tentar obter do cache
    const cachedData = await getCacheValue<EducationalContent[]>(cacheKey);

    // Se encontrou no cache, retornar
    if (cachedData) {
      logger.info(`Obtido conteúdo para idade ${minAge}-${maxAge} do cache (${limit} itens)`);
      return cachedData;
    }

    // Consultar banco de dados
    const query = `
      SELECT * FROM educational_contents
      WHERE status = $1
      AND age_range_min <= $3
      AND age_range_max >= $2
      ORDER BY published_at DESC
      LIMIT $4
    `;

    const result = await dbConnection.query(query, [ContentStatus.PUBLISHED, minAge, maxAge, limit]);

    // Mapear resultados para entidades
    const contents = result.rows.map(row => mapToEducationalContent(row));

    // Armazenar no cache (1 hora)
    await setCacheValue(cacheKey, contents, 3600);

    return contents;
  } catch (error) {
    // Registrar erro
    logger.error(`Erro ao obter conteúdos para idade ${minAge}-${maxAge}`, error as Error);

    // Em caso de erro, retornar array vazio
    return [];
  }
}

/**
 * Obtém um conteúdo educacional pelo ID
 *
 * @param id ID do conteúdo
 * @returns Conteúdo educacional ou null se não encontrado
 */
export async function getContentById(id: string): Promise<EducationalContent | null> {
  try {
    // Chave de cache
    const cacheKey = `content:${id}`;

    // Tentar obter do cache
    const cachedData = await getCacheValue<EducationalContent>(cacheKey);

    // Se encontrou no cache, retornar
    if (cachedData) {
      logger.info(`Obtido conteúdo com ID ${id} do cache`);
      return cachedData;
    }

    // Consultar banco de dados
    const query = `
      SELECT * FROM educational_contents
      WHERE id = $1 AND status = $2
    `;

    const result = await dbConnection.query(query, [id, ContentStatus.PUBLISHED]);

    // Se não encontrou, retornar null
    if (result.rows.length === 0) {
      return null;
    }

    // Mapear resultado para entidade
    const content = mapToEducationalContent(result.rows[0]);

    // Armazenar no cache (2 horas)
    await setCacheValue(cacheKey, content, 7200);

    return content;
  } catch (error) {
    // Registrar erro
    logger.error(`Erro ao obter conteúdo com ID ${id}`, error as Error);

    // Em caso de erro, retornar null
    return null;
  }
}

/**
 * Mapeia um registro do banco de dados para uma entidade EducationalContent
 *
 * @param row Registro do banco de dados
 * @returns Entidade EducationalContent
 */
function mapToEducationalContent(row: any): EducationalContent {
  // Converter tags de string JSON para array
  const tags = typeof row.tags === 'string' ? JSON.parse(row.tags) : row.tags || [];

  // Criar objeto AgeRange
  const ageRange = new AgeRange(row.age_range_min, row.age_range_max);

  // Criar entidade
  return new EducationalContent(
    row.id,
    row.title,
    row.description,
    row.content,
    row.author_id,
    row.type as ContentType,
    tags,
    [row.age_range_min, row.age_range_max],
    row.status as ContentStatus,
    new Date(row.created_at),
    new Date(row.updated_at),
    row.published_at ? new Date(row.published_at) : undefined,
    row.featured_image_url
  );
}
