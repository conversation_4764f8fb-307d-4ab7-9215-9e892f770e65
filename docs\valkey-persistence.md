# Persistência de Dados no Valkey

## Visão Geral

Este documento descreve a estratégia de persistência de dados implementada para o Valkey no projeto Estação da Alfabetização. A persistência adequada é essencial para garantir a durabilidade dos dados em caso de falhas ou reinicializações do sistema.

## Estratégias de Persistência

O Valkey oferece duas estratégias principais de persistência, que podem ser usadas individualmente ou em conjunto:

### RDB (Redis Database)

O RDB é um mecanismo de snapshot que salva o estado completo do banco de dados em um único arquivo em momentos específicos.

#### Vantagens do RDB

- **Compacto**: Arquivos únicos e compactos, ideais para backups
- **Performance**: Impacto mínimo na performance durante operação normal
- **Recuperação rápida**: Carregamento mais rápido na inicialização
- **Tolerância a falhas**: Uma falha durante o salvamento não afeta o arquivo existente

#### Desvantagens do RDB

- **Perda de dados**: Possível perda de dados entre snapshots
- **Bloqueio**: Pode causar bloqueio em bancos de dados muito grandes

### AOF (Append Only File)

O AOF é um log de transações que registra cada operação de escrita realizada no banco de dados.

#### Vantagens do AOF

- **Durabilidade**: Perda mínima de dados (até 1 segundo com `appendfsync everysec`)
- **Robustez**: Arquivo auto-curável em caso de corrupção
- **Operação incremental**: Apenas novas operações são adicionadas ao arquivo

#### Desvantagens do AOF

- **Tamanho**: Arquivos AOF geralmente são maiores que RDB
- **Performance**: Ligeiramente mais lento que RDB
- **Recuperação mais lenta**: Carregamento mais demorado na inicialização

## Configuração Implementada

No projeto Estação da Alfabetização, implementamos uma estratégia híbrida que combina RDB e AOF para maximizar a durabilidade e a performance.

### Configuração RDB

```
# Configuração de RDB (snapshots)
save 900 1      # Salvar a cada 15 minutos se pelo menos 1 chave mudou
save 300 10     # Salvar a cada 5 minutos se pelo menos 10 chaves mudaram
save 60 10000   # Salvar a cada 1 minuto se pelo menos 10000 chaves mudaram

stop-writes-on-bgsave-error yes  # Parar escritas em caso de erro no BGSAVE
rdbcompression yes               # Comprimir arquivo RDB
rdbchecksum yes                  # Verificar checksum do arquivo RDB
dbfilename dump.rdb              # Nome do arquivo RDB
dir /var/lib/valkey              # Diretório para armazenamento
```

### Configuração AOF

```
# Configuração de AOF (append only file)
appendonly yes                    # Habilitar AOF
appendfilename "appendonly.aof"   # Nome do arquivo AOF
appendfsync everysec              # Sincronizar a cada segundo
no-appendfsync-on-rewrite no      # Continuar sincronizando durante reescrita
auto-aof-rewrite-percentage 100   # Reescrever quando o arquivo crescer 100%
auto-aof-rewrite-min-size 64mb    # Tamanho mínimo para reescrita
aof-load-truncated yes            # Carregar AOF truncado
aof-use-rdb-preamble yes          # Usar preamble RDB no AOF
```

## Estratégia de Backup

Além da persistência nativa do Valkey, implementamos uma estratégia de backup para garantir a recuperação em caso de falhas catastróficas.

### Backups Automáticos

- **Frequência**: Diariamente às 2h da manhã
- **Retenção**: 30 dias (configurável)
- **Compressão**: Habilitada para economizar espaço
- **Conteúdo**: Arquivos RDB e AOF

### Armazenamento Externo

Os backups são armazenados em múltiplos locais para garantir redundância:

- **Local**: Armazenamento local no servidor
- **S3**: Cópia enviada para bucket S3 dedicado
- **Retenção diferenciada**: Backups diários por 30 dias, semanais por 3 meses, mensais por 1 ano

### Script de Backup

Um script automatizado realiza o processo de backup:

```bash
#!/bin/bash

# Configurações
BACKUP_DIR="/var/backups/valkey"
RETENTION=30
COMPRESS=true

# Criar diretório de backup se não existir
mkdir -p "$BACKUP_DIR"

# Executar backup
echo "Iniciando backup do Valkey..."
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BACKUP_FILE="$BACKUP_DIR/valkey_backup_$TIMESTAMP"

# Executar comando de backup
valkey-cli SAVE

# Copiar arquivos de dados
cp /var/lib/valkey/dump.rdb "$BACKUP_FILE.rdb"
cp /var/lib/valkey/appendonly.aof "$BACKUP_FILE.aof"

# Comprimir backup
tar -czf "$BACKUP_FILE.tar.gz" "$BACKUP_FILE.rdb" "$BACKUP_FILE.aof"
rm "$BACKUP_FILE.rdb" "$BACKUP_FILE.aof"

# Copiar para S3
aws s3 cp "$BACKUP_FILE.tar.gz" "s3://estacao-backups/valkey/"

# Limpar backups antigos
find "$BACKUP_DIR" -name "valkey_backup_*" -type f -mtime +$RETENTION -delete

echo "Backup concluído com sucesso"
```

## Monitoramento da Persistência

O sistema monitora continuamente o estado da persistência para garantir seu funcionamento adequado.

### Métricas Monitoradas

- **Último salvamento RDB**: Tempo desde o último snapshot bem-sucedido
- **Mudanças desde último salvamento**: Número de operações desde o último RDB
- **Status do BGSAVE**: Se há um processo de salvamento em andamento
- **Status do AOF**: Se o AOF está habilitado e funcionando
- **Reescrita de AOF**: Se há um processo de reescrita em andamento
- **Tamanho dos arquivos**: Monitoramento do crescimento dos arquivos RDB e AOF

### Alertas

O sistema gera alertas nas seguintes condições:

| Condição | Severidade | Ação |
|----------|------------|------|
| Último salvamento RDB > 60 minutos | Aviso | Notificação |
| Mudanças desde último salvamento > 10000 | Aviso | Notificação |
| Falha no BGSAVE | Crítico | Notificação imediata |
| Falha na reescrita de AOF | Crítico | Notificação imediata |
| Espaço em disco < 20% | Crítico | Notificação imediata |

## Procedimento de Recuperação

Em caso de falha, o procedimento de recuperação segue estas etapas:

### Recuperação Automática

Na inicialização, o Valkey tenta carregar os dados na seguinte ordem:

1. Se AOF estiver habilitado, tenta carregar o arquivo AOF
2. Se o carregamento do AOF falhar ou não estiver habilitado, tenta carregar o arquivo RDB

### Recuperação Manual

Em caso de corrupção de dados ou falha na recuperação automática:

1. Parar o serviço Valkey
   ```bash
   systemctl stop valkey
   ```

2. Fazer backup dos arquivos corrompidos
   ```bash
   mv /var/lib/valkey/dump.rdb /var/lib/valkey/dump.rdb.corrupted
   mv /var/lib/valkey/appendonly.aof /var/lib/valkey/appendonly.aof.corrupted
   ```

3. Restaurar o backup mais recente
   ```bash
   # Encontrar backup mais recente
   LATEST_BACKUP=$(ls -t /var/backups/valkey/valkey_backup_*.tar.gz | head -1)
   
   # Extrair backup
   tar -xzf $LATEST_BACKUP -C /var/lib/valkey/
   ```

4. Iniciar o serviço Valkey
   ```bash
   systemctl start valkey
   ```

5. Verificar a integridade dos dados
   ```bash
   valkey-cli --stat
   valkey-cli dbsize
   ```

## Considerações para Ambiente de Cluster

Em um ambiente de cluster Valkey, cada nó mantém sua própria persistência. Considerações adicionais:

- **Consistência**: A persistência é local para cada nó, não há garantia de snapshot consistente em todo o cluster
- **Backups**: O backup deve ser realizado em todos os nós do cluster
- **Recuperação**: A recuperação pode exigir reconstrução do cluster após restauração dos nós individuais

## Boas Práticas

- **Monitoramento regular**: Verificar logs e métricas de persistência diariamente
- **Testes de recuperação**: Realizar testes periódicos de recuperação a partir de backups
- **Ajuste de configuração**: Adaptar configurações de persistência conforme o crescimento do volume de dados
- **Espaço em disco**: Manter pelo menos 3x o tamanho do conjunto de dados em espaço livre
- **Verificação de integridade**: Executar `valkey-check-rdb` e `valkey-check-aof` periodicamente

## Referências

- [Documentação de persistência do Redis](https://redis.io/topics/persistence)
- [Guia de backup e recuperação do Redis](https://redis.io/topics/admin)
- [Estratégias de persistência para alta disponibilidade](https://redis.io/topics/liveness)
- [Documentação do Valkey](https://valkey.io/docs)
