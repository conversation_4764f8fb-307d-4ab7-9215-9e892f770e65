/**
 * API para listar políticas de acesso
 */

import { requirePermission } from '@middleware/authorizationMiddleware';
import { policyEnforcementService } from '@services/policyEnforcementService';
import { logger } from '@utils/logger';
import type { APIRoute } from 'astro';

// Middleware para verificar permissão de leitura de papéis
export const GET = requirePermission({
  resource: 'roles',
  action: 'read',
})(async ({ request }) => {
  try {
    // Obter parâmetros de consulta
    const url = new URL(request.url);
    const resource = url.searchParams.get('resource');

    let policies: any[];

    if (resource) {
      // Obter política para um recurso específico
      const policy = policyEnforcementService.getResourcePolicy(resource);

      if (!policy) {
        return new Response(
          JSON.stringify({
            success: false,
            error: `Política não encontrada para o recurso: ${resource}`,
          }),
          {
            status: 404,
            headers: {
              'Content-Type': 'application/json',
            },
          }
        );
      }

      policies = [policy];
    } else {
      // Obter todas as políticas
      policies = policyEnforcementService.getPolicies();
    }

    // Retornar políticas
    return new Response(
      JSON.stringify({
        success: true,
        policies,
      }),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  } catch (error) {
    logger.error('Erro ao buscar políticas:', error);

    return new Response(
      JSON.stringify({
        success: false,
        error: 'Erro ao buscar políticas',
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  }
});
