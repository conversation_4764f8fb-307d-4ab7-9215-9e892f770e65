/**
 * Configuração de estratégias de particionamento para o Kafka
 * 
 * Este arquivo contém as configurações de estratégias de particionamento
 * para os tópicos Kafka, incluindo funções de particionamento, chaves de
 * particionamento e outras configurações relacionadas.
 */

// Tipos de estratégias de particionamento
export enum PartitioningStrategy {
  RANDOM = 'random',           // Distribuição aleatória
  ROUND_ROBIN = 'round-robin', // Distribuição round-robin
  KEY_BASED = 'key-based',     // Baseado na chave da mensagem
  CUSTOM = 'custom',           // Estratégia personalizada
}

// Tipos de chaves para particionamento
export enum PartitionKey {
  USER_ID = 'userId',          // ID do usuário
  ORDER_ID = 'orderId',        // ID do pedido
  TRANSACTION_ID = 'transactionId', // ID da transação
  PRODUCT_ID = 'productId',    // ID do produto
  CUSTOMER_ID = 'customerId',  // ID do cliente
  ENTITY_ID = 'entityId',      // ID genérico da entidade
  SESSION_ID = 'sessionId',    // ID da sessão
  NONE = 'none',               // Sem chave específica
}

// Interface para configuração de particionamento
export interface PartitioningConfig {
  strategy: PartitioningStrategy;
  keyField?: PartitionKey;
  customPartitioner?: string;  // Nome da função de particionamento personalizada
  description: string;
}

// Configurações de particionamento por tópico
const partitioningConfigs: Record<string, PartitioningConfig> = {
  // Domínio: Pagamentos
  'payment.transaction.created': {
    strategy: PartitioningStrategy.KEY_BASED,
    keyField: PartitionKey.TRANSACTION_ID,
    description: 'Particionamento por ID de transação para garantir ordenação de eventos relacionados à mesma transação',
  },
  'payment.transaction.updated': {
    strategy: PartitioningStrategy.KEY_BASED,
    keyField: PartitionKey.TRANSACTION_ID,
    description: 'Particionamento por ID de transação para garantir ordenação de eventos relacionados à mesma transação',
  },
  'payment.transaction.failed': {
    strategy: PartitioningStrategy.KEY_BASED,
    keyField: PartitionKey.TRANSACTION_ID,
    description: 'Particionamento por ID de transação para garantir ordenação de eventos relacionados à mesma transação',
  },
  'payment.refund.requested': {
    strategy: PartitioningStrategy.KEY_BASED,
    keyField: PartitionKey.TRANSACTION_ID,
    description: 'Particionamento por ID de transação para garantir ordenação de eventos relacionados à mesma transação',
  },
  'payment.refund.processed': {
    strategy: PartitioningStrategy.KEY_BASED,
    keyField: PartitionKey.TRANSACTION_ID,
    description: 'Particionamento por ID de transação para garantir ordenação de eventos relacionados à mesma transação',
  },
  'payment.webhook.received': {
    strategy: PartitioningStrategy.KEY_BASED,
    keyField: PartitionKey.ENTITY_ID,
    description: 'Particionamento por ID da entidade relacionada ao webhook',
  },
  
  // Domínio: Pedidos
  'order.created': {
    strategy: PartitioningStrategy.KEY_BASED,
    keyField: PartitionKey.ORDER_ID,
    description: 'Particionamento por ID de pedido para garantir ordenação de eventos relacionados ao mesmo pedido',
  },
  'order.updated': {
    strategy: PartitioningStrategy.KEY_BASED,
    keyField: PartitionKey.ORDER_ID,
    description: 'Particionamento por ID de pedido para garantir ordenação de eventos relacionados ao mesmo pedido',
  },
  'order.status.changed': {
    strategy: PartitioningStrategy.KEY_BASED,
    keyField: PartitionKey.ORDER_ID,
    description: 'Particionamento por ID de pedido para garantir ordenação de eventos relacionados ao mesmo pedido',
  },
  'order.cancelled': {
    strategy: PartitioningStrategy.KEY_BASED,
    keyField: PartitionKey.ORDER_ID,
    description: 'Particionamento por ID de pedido para garantir ordenação de eventos relacionados ao mesmo pedido',
  },
  'order.fulfilled': {
    strategy: PartitioningStrategy.KEY_BASED,
    keyField: PartitionKey.ORDER_ID,
    description: 'Particionamento por ID de pedido para garantir ordenação de eventos relacionados ao mesmo pedido',
  },
  
  // Domínio: Usuários
  'user.registered': {
    strategy: PartitioningStrategy.KEY_BASED,
    keyField: PartitionKey.USER_ID,
    description: 'Particionamento por ID de usuário para garantir ordenação de eventos relacionados ao mesmo usuário',
  },
  'user.profile.updated': {
    strategy: PartitioningStrategy.KEY_BASED,
    keyField: PartitionKey.USER_ID,
    description: 'Particionamento por ID de usuário para garantir ordenação de eventos relacionados ao mesmo usuário',
  },
  'user.subscription.changed': {
    strategy: PartitioningStrategy.KEY_BASED,
    keyField: PartitionKey.USER_ID,
    description: 'Particionamento por ID de usuário para garantir ordenação de eventos relacionados ao mesmo usuário',
  },
  
  // Domínio: Notificações
  'notification.email.queued': {
    strategy: PartitioningStrategy.KEY_BASED,
    keyField: PartitionKey.USER_ID,
    description: 'Particionamento por ID de usuário para agrupar notificações do mesmo usuário',
  },
  'notification.email.sent': {
    strategy: PartitioningStrategy.KEY_BASED,
    keyField: PartitionKey.USER_ID,
    description: 'Particionamento por ID de usuário para agrupar notificações do mesmo usuário',
  },
  'notification.email.failed': {
    strategy: PartitioningStrategy.KEY_BASED,
    keyField: PartitionKey.USER_ID,
    description: 'Particionamento por ID de usuário para agrupar notificações do mesmo usuário',
  },
  
  // Domínio: Analytics
  'analytics.page.viewed': {
    strategy: PartitioningStrategy.KEY_BASED,
    keyField: PartitionKey.SESSION_ID,
    description: 'Particionamento por ID de sessão para agrupar eventos da mesma sessão',
  },
  'analytics.product.viewed': {
    strategy: PartitioningStrategy.KEY_BASED,
    keyField: PartitionKey.SESSION_ID,
    description: 'Particionamento por ID de sessão para agrupar eventos da mesma sessão',
  },
  'analytics.search.performed': {
    strategy: PartitioningStrategy.KEY_BASED,
    keyField: PartitionKey.SESSION_ID,
    description: 'Particionamento por ID de sessão para agrupar eventos da mesma sessão',
  },
  'analytics.cart.abandoned': {
    strategy: PartitioningStrategy.KEY_BASED,
    keyField: PartitionKey.USER_ID,
    description: 'Particionamento por ID de usuário para agrupar eventos do mesmo usuário',
  },
};

// Configurações padrão por estratégia
const defaultPartitioningByStrategy: Record<PartitioningStrategy, PartitioningConfig> = {
  [PartitioningStrategy.RANDOM]: {
    strategy: PartitioningStrategy.RANDOM,
    description: 'Distribuição aleatória de mensagens entre partições',
  },
  [PartitioningStrategy.ROUND_ROBIN]: {
    strategy: PartitioningStrategy.ROUND_ROBIN,
    description: 'Distribuição sequencial de mensagens entre partições',
  },
  [PartitioningStrategy.KEY_BASED]: {
    strategy: PartitioningStrategy.KEY_BASED,
    keyField: PartitionKey.ENTITY_ID,
    description: 'Particionamento baseado em hash da chave da mensagem',
  },
  [PartitioningStrategy.CUSTOM]: {
    strategy: PartitioningStrategy.CUSTOM,
    customPartitioner: 'defaultCustomPartitioner',
    description: 'Estratégia de particionamento personalizada',
  },
};

// Exportar configuração de particionamento
export const kafkaPartitioningConfig = {
  /**
   * Obtém a configuração de particionamento para um tópico específico
   * @param topicName - Nome do tópico
   * @returns Configuração de particionamento para o tópico
   */
  getTopicPartitioningConfig(topicName: string): PartitioningConfig | null {
    return partitioningConfigs[topicName] || null;
  },

  /**
   * Obtém a configuração padrão para uma estratégia
   * @param strategy - Estratégia de particionamento
   * @returns Configuração padrão para a estratégia
   */
  getDefaultPartitioningConfig(strategy: PartitioningStrategy): PartitioningConfig {
    return defaultPartitioningByStrategy[strategy];
  },

  /**
   * Obtém todas as configurações de particionamento
   * @returns Configurações de particionamento para todos os tópicos
   */
  getAllPartitioningConfigs(): Record<string, PartitioningConfig> {
    return partitioningConfigs;
  },
};
