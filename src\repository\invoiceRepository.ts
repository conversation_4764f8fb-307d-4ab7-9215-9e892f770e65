import type { QueryResult } from 'pg';
import { pgH<PERSON>per } from './pgHelper';

async function create(
  ulid_order: string,
  ulid_user: string,
  cod_status: number,
  value: number,
  tax: number,
  invoice_no: string
): Promise<QueryResult> {
  return await pgHelper.query(
    `INSERT INTO tab_invoice(
       ulid_order,
       ulid_user,
       cod_status,
       value,
       tax,
       invoice_no) 
     VALUES ('$1', '$2', $3, $4, $5, $6) 
     RETURNING *`,
    [ulid_order, ulid_user, cod_status, value, tax, invoice_no]
  );
}

async function read(
  ulid_invoice?: string,
  ulid_order?: string,
  ulid_user?: string,
  cod_status?: number
): Promise<QueryResult> {
  return await pgHelper.query(
    `SELECT * 
       FROM tab_invoice 
      WHERE TRUE 
        ${ulid_invoice ? 'AND ulid_invoice = $1' : ''}
        ${ulid_order ? 'AND ulid_order = $2' : ''}
        ${ulid_user ? 'AND ulid_user = $3' : ''}
        ${cod_status ? 'AND cod_status = $4' : ''}`,
    [ulid_invoice, ulid_order, ulid_user, cod_status]
  );
}

async function update(ulid_invoice: string, cod_status: number): Promise<QueryResult> {
  return await pgHelper.query(
    `UPDATE tab_invoice 
        SET cod_status   = $2 
      WHERE ulid_invoice = $1 
     RETURNING *`,
    [ulid_invoice, cod_status]
  );
}

async function deleteByUlid(ulid_invoice: string): Promise<QueryResult> {
  return await pgHelper.query(
    `DELETE FROM tab_invoice 
      WHERE ulid_invoice = $1 
     RETURNING *`,
    [ulid_invoice]
  );
}

export const invoiceRepository = {
  create,
  read,
  update,
  deleteByUlid,
};
