/**
 * Tipos comuns para testes
 *
 * Este arquivo define interfaces e tipos reutilizáveis para todos os testes
 * do projeto, eliminando a necessidade de usar 'any' em dados de teste.
 */

// Tipos básicos para dados de teste
export type TestId = string;
export type TestEmail = string;
export type TestPassword = string;
export type TestTimestamp = Date | string;

// Interface base para objetos de teste
export interface TestEntity {
  id: TestId;
  createdAt?: TestTimestamp;
  updatedAt?: TestTimestamp;
}

// Dados de usuário para testes
export interface TestUser extends TestEntity {
  email: TestEmail;
  password: TestPassword;
  name: string;
  role?: string;
  active?: boolean;
  verified?: boolean;
}

// Dados de produto para testes
export interface TestProduct extends TestEntity {
  name: string;
  description?: string;
  price: number;
  category?: string;
  active?: boolean;
  stock?: number;
}

// Dados de pedido para testes
export interface TestOrder extends TestEntity {
  userId: TestId;
  products: TestOrderItem[];
  total: number;
  status: string;
  paymentMethod?: string;
}

export interface TestOrderItem {
  productId: TestId;
  quantity: number;
  price: number;
  name?: string;
}

// Dados de endereço para testes
export interface TestAddress {
  street: string;
  number: string;
  complement?: string;
  neighborhood: string;
  city: string;
  state: string;
  zipcode: string;
  country?: string;
}

// Respostas de API para testes
export interface TestApiResponse<T = unknown> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  statusCode?: number;
}

// Dados de autenticação para testes
export interface TestAuthData {
  token: string;
  refreshToken?: string;
  expiresIn?: number;
  user: TestUser;
}

// Dados de sessão para testes
export interface TestSession {
  id: TestId;
  userId: TestId;
  token: string;
  expiresAt: TestTimestamp;
  data?: Record<string, unknown>;
}

// Configuração de mock para testes
export interface TestMockConfig {
  enabled: boolean;
  delay?: number;
  errorRate?: number;
  responses?: Record<string, unknown>;
}

// Dados de pagamento para testes
export interface TestPaymentData {
  method: string;
  amount: number;
  currency?: string;
  cardNumber?: string;
  cardHolder?: string;
  expiryDate?: string;
  cvv?: string;
}

// Dados de notificação para testes
export interface TestNotification extends TestEntity {
  userId: TestId;
  title: string;
  message: string;
  type: string;
  read?: boolean;
  data?: Record<string, unknown>;
}

// Dados de analytics para testes
export interface TestAnalyticsEvent {
  event: string;
  userId?: TestId;
  sessionId?: TestId;
  timestamp: TestTimestamp;
  properties?: Record<string, unknown>;
}

// Dados de conteúdo educacional para testes
export interface TestEducationalContent extends TestEntity {
  title: string;
  description?: string;
  content: string;
  category: string;
  difficulty?: string;
  ageRange?: string;
  tags?: string[];
  active?: boolean;
}

// Dados de categoria para testes
export interface TestCategory extends TestEntity {
  name: string;
  description?: string;
  parentId?: TestId;
  active?: boolean;
  order?: number;
}

// Dados de cupom para testes
export interface TestCoupon extends TestEntity {
  code: string;
  type: 'percentage' | 'fixed';
  value: number;
  minAmount?: number;
  maxDiscount?: number;
  expiresAt?: TestTimestamp;
  usageLimit?: number;
  usageCount?: number;
  active?: boolean;
}

// Dados de afiliado para testes
export interface TestAffiliate extends TestEntity {
  userId: TestId;
  code: string;
  commission: number;
  active?: boolean;
  totalEarnings?: number;
  totalSales?: number;
}

// Dados de FAQ para testes
export interface TestFaqItem extends TestEntity {
  question: string;
  answer: string;
  category?: string;
  order?: number;
  active?: boolean;
  helpful?: number;
  notHelpful?: number;
}

// Dados de contato para testes
export interface TestContactMessage extends TestEntity {
  name: string;
  email: TestEmail;
  subject: string;
  message: string;
  category?: string;
  status?: string;
  replied?: boolean;
  replyMessage?: string;
}

// Dados de banner promocional para testes
export interface TestPromotionalBanner extends TestEntity {
  title: string;
  description?: string;
  imageUrl?: string;
  linkUrl?: string;
  startDate?: TestTimestamp;
  endDate?: TestTimestamp;
  active?: boolean;
  position?: string;
}

// Dados de documento fiscal para testes
export interface TestFiscalDocument extends TestEntity {
  orderId: TestId;
  type: string;
  number: string;
  series?: string;
  amount: number;
  taxAmount?: number;
  status: string;
  issuedAt?: TestTimestamp;
  canceledAt?: TestTimestamp;
}

// Dados de SEO para testes
export interface TestSeoData {
  title: string;
  description: string;
  keywords?: string[];
  ogTitle?: string;
  ogDescription?: string;
  ogImage?: string;
  twitterCard?: string;
  canonicalUrl?: string;
}

// Dados de sitemap para testes
export interface TestSitemapEntry {
  url: string;
  lastModified?: TestTimestamp;
  changeFrequency?: string;
  priority?: number;
}

// Dados de compartilhamento para testes
export interface TestShareLink extends TestEntity {
  url: string;
  title: string;
  description?: string;
  imageUrl?: string;
  platform: string;
  clicks?: number;
  active?: boolean;
}

// Dados de A/B testing para testes
export interface TestABTestVariant {
  id: string;
  name: string;
  weight: number;
  isControl?: boolean;
}

export interface TestABTest extends TestEntity {
  name: string;
  description?: string;
  status: string;
  variants: TestABTestVariant[];
  trafficAllocation: number;
  startDate?: TestTimestamp;
  endDate?: TestTimestamp;
}

// Tipos utilitários para testes
export type TestPartial<T> = Partial<T>;
export type TestRequired<T, K extends keyof T> = T & Required<Pick<T, K>>;
export type TestOmit<T, K extends keyof T> = Omit<T, K>;
export type TestPick<T, K extends keyof T> = Pick<T, K>;

// Função utilitária para criar dados de teste
export type TestDataFactory<T> = (overrides?: TestPartial<T>) => T;

// Mock de função para testes
export type TestMockFunction<T extends (...args: unknown[]) => unknown> = jest.MockedFunction<T>;

// Mock de classe para testes
export type TestMockClass<T> = jest.Mocked<T>;

// Dados de erro para testes
export interface TestError {
  code: string;
  message: string;
  details?: Record<string, unknown>;
  stack?: string;
}

// Dados de log para testes
export interface TestLogEntry {
  level: string;
  message: string;
  timestamp: TestTimestamp;
  context?: Record<string, unknown>;
  error?: TestError;
}
