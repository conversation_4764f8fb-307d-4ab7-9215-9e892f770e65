/**
 * Restore Content Version Use Case
 *
 * Caso de uso para restaurar uma versão de conteúdo.
 * Parte da implementação da tarefa 8.8.3 - Gestão de conteúdo
 */

import { Content } from '../../entities/Content';
import { ContentRepository } from '../../repositories/ContentRepository';

export interface RestoreContentVersionRequest {
  versionId: string;
  userId: string;
}

export interface RestoreContentVersionResponse {
  success: boolean;
  data?: Content;
  error?: string;
}

export class RestoreContentVersionUseCase {
  constructor(private contentRepository: ContentRepository) {}

  async execute(request: RestoreContentVersionRequest): Promise<RestoreContentVersionResponse> {
    try {
      // Validar os dados de entrada
      if (!request.versionId) {
        return {
          success: false,
          error: 'O ID da versão é obrigatório.',
        };
      }

      if (!request.userId) {
        return {
          success: false,
          error: 'O ID do usuário é obrigatório.',
        };
      }

      // Verificar se a versão existe
      const version = await this.contentRepository.getVersionById(request.versionId);

      if (!version) {
        return {
          success: false,
          error: `Versão com ID ${request.versionId} não encontrada.`,
        };
      }

      // Verificar se o conteúdo existe
      const content = await this.contentRepository.getById(version.contentId);

      if (!content) {
        return {
          success: false,
          error: `Conteúdo com ID ${version.contentId} não encontrado.`,
        };
      }

      // Restaurar versão
      const restoredContent = await this.contentRepository.restoreVersion(
        request.versionId,
        request.userId
      );

      if (!restoredContent) {
        return {
          success: false,
          error: 'Erro ao restaurar versão do conteúdo.',
        };
      }

      return {
        success: true,
        data: restoredContent,
      };
    } catch (error) {
      console.error('Erro ao restaurar versão do conteúdo:', error);

      return {
        success: false,
        error:
          error instanceof Error
            ? error.message
            : 'Erro desconhecido ao restaurar versão do conteúdo.',
      };
    }
  }
}
