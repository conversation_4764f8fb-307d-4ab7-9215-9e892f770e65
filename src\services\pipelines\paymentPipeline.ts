/**
 * Pipeline de processamento de pagamentos
 *
 * Este arquivo define o pipeline para processamento de eventos de pagamento,
 * incluindo validação, enriquecimento, processamento e notificação.
 */

import { logger } from '@utils/logger';
import { queryHelper } from '@db/queryHelper';
import { eventPipelineService, type PipelineStep, type PipelineContext } from '@services/eventPipelineService';
import { type PaymentEvent } from '@services/eventProducerService';
import { paymentEventFlow, type ProcessedPaymentEvent } from '@services/flows/paymentEventFlow';
import { kafkaLoggingService } from '@services/kafka-logging.service';
import { alertService } from '@services/alertService';
import { emailService } from '@services/emailService';

/**
 * Etapa de validação de pagamento
 */
const paymentValidationStep: PipelineStep<PaymentEvent, PaymentEvent> = {
  name: 'payment-validation',
  async process(event: PaymentEvent, context: PipelineContext): Promise<PaymentEvent> {
    logger.debug(`Validando evento de pagamento: ${event.paymentId}`);

    // Verificar campos obrigatórios
    if (!event.paymentId) {
      throw new Error('Campo obrigatório ausente: paymentId');
    }

    if (!event.status) {
      throw new Error('Campo obrigatório ausente: status');
    }

    // Verificar se o pagamento existe no banco de dados
    const paymentResult = await queryHelper.query(
      'SELECT * FROM tab_payment WHERE ulid_payment = $1',
      [event.paymentId]
    );

    if (paymentResult.rowCount === 0) {
      logger.warn(`Pagamento não encontrado: ${event.paymentId}`);
      context.data.paymentExists = false;
    } else {
      context.data.paymentExists = true;
      context.data.paymentData = paymentResult.rows[0];
    }

    return event;
  },
  async handleError(error: Error, event: PaymentEvent, context: PipelineContext): Promise<PaymentEvent> {
    logger.error(`Erro na validação do pagamento ${event.paymentId}:`, error);
    
    // Registrar alerta para erro de validação
    await alertService.createAlert('payment_validation_error', {
      paymentId: event.paymentId,
      error: error.message,
      status: event.status,
    });
    
    // Continuar processamento mesmo com erro de validação
    return event;
  }
};

/**
 * Etapa de enriquecimento de pagamento
 */
const paymentEnrichmentStep: PipelineStep<PaymentEvent, ProcessedPaymentEvent> = {
  name: 'payment-enrichment',
  async process(event: PaymentEvent, context: PipelineContext): Promise<ProcessedPaymentEvent> {
    logger.debug(`Enriquecendo evento de pagamento: ${event.paymentId}`);

    // Se o pagamento não existe, usar apenas os dados do evento
    if (!context.data.paymentExists) {
      return {
        paymentId: event.paymentId,
        orderId: event.orderId,
        userId: event.userId,
        value: event.value,
        paymentType: event.paymentType,
        status: event.status,
        externalId: event.externalId,
        metadata: event.metadata || {},
        processingTimestamp: new Date().toISOString(),
      };
    }

    // Obter dados completos do pagamento
    const paymentData = context.data.paymentData;
    
    // Obter dados do pedido e usuário
    let orderData = null;
    let userData = null;
    
    if (paymentData.ulid_order) {
      const orderResult = await queryHelper.query(
        'SELECT * FROM tab_order WHERE ulid_order = $1',
        [paymentData.ulid_order]
      );
      
      if (orderResult.rowCount > 0) {
        orderData = orderResult.rows[0];
        context.data.orderData = orderData;
        
        // Obter dados do usuário
        if (orderData.ulid_user) {
          const userResult = await queryHelper.query(
            'SELECT * FROM tab_user WHERE ulid_user = $1',
            [orderData.ulid_user]
          );
          
          if (userResult.rowCount > 0) {
            userData = userResult.rows[0];
            context.data.userData = userData;
          }
        }
      }
    }
    
    // Obter tipo de pagamento
    let paymentType = event.paymentType;
    if (paymentData.ulid_payment_type && !paymentType) {
      const paymentTypeResult = await queryHelper.query(
        'SELECT * FROM tab_payment_type WHERE ulid_payment_type = $1',
        [paymentData.ulid_payment_type]
      );
      
      if (paymentTypeResult.rowCount > 0) {
        paymentType = paymentTypeResult.rows[0].type;
      }
    }
    
    // Criar evento enriquecido
    return {
      paymentId: event.paymentId,
      orderId: paymentData.ulid_order || event.orderId,
      userId: orderData?.ulid_user || event.userId,
      value: paymentData.value || event.value,
      paymentType: paymentType,
      status: event.status,
      externalId: paymentData.external_id || event.externalId,
      metadata: {
        ...event.metadata || {},
        paymentData: {
          createdAt: paymentData.created_at,
          updatedAt: paymentData.updated_at,
          status: paymentData.cod_status,
        },
        orderData: orderData ? {
          total: orderData.total,
          status: orderData.cod_status,
          createdAt: orderData.created_at,
        } : undefined,
        userData: userData ? {
          name: userData.name,
          email: userData.email,
        } : undefined,
      },
      processingTimestamp: new Date().toISOString(),
    };
  },
};

/**
 * Etapa de processamento de pagamento
 */
const paymentProcessingStep: PipelineStep<ProcessedPaymentEvent, ProcessedPaymentEvent> = {
  name: 'payment-processing',
  async process(event: ProcessedPaymentEvent, context: PipelineContext): Promise<ProcessedPaymentEvent> {
    logger.debug(`Processando pagamento: ${event.paymentId}`);
    
    // Processar pagamento usando o fluxo de pagamento
    await paymentEventFlow.processPaymentEvent(event, context);
    
    return event;
  },
};

/**
 * Etapa de notificação de pagamento
 */
const paymentNotificationStep: PipelineStep<ProcessedPaymentEvent, ProcessedPaymentEvent> = {
  name: 'payment-notification',
  async process(event: ProcessedPaymentEvent, context: PipelineContext): Promise<ProcessedPaymentEvent> {
    logger.debug(`Enviando notificações para pagamento: ${event.paymentId}`);
    
    // Verificar se há dados de usuário para enviar notificações
    const userData = context.data.userData;
    if (!userData || !userData.email) {
      logger.debug(`Sem dados de usuário para enviar notificações: ${event.paymentId}`);
      return event;
    }
    
    // Enviar notificação com base no status
    try {
      switch (event.status) {
        case 'approved':
        case 'completed':
          // Notificação já enviada no fluxo de processamento
          break;
          
        case 'pending':
          // Notificação já enviada no fluxo de processamento
          break;
          
        case 'failed':
        case 'rejected':
          // Notificação já enviada no fluxo de processamento
          break;
          
        case 'refunded':
          // Notificação já enviada no fluxo de processamento
          break;
          
        default:
          // Enviar notificação genérica para outros status
          await emailService.sendGenericNotification({
            email: userData.email,
            name: userData.name,
            subject: `Atualização de pagamento #${event.paymentId}`,
            message: `Seu pagamento foi atualizado para o status: ${event.status}`,
            data: {
              paymentId: event.paymentId,
              orderId: event.orderId,
              status: event.status,
              date: new Date().toISOString(),
            },
          });
      }
    } catch (error) {
      logger.error(`Erro ao enviar notificação para pagamento ${event.paymentId}:`, error);
      // Não propagar erro para não interromper o pipeline
    }
    
    return event;
  },
  async handleError(error: Error, event: ProcessedPaymentEvent, context: PipelineContext): Promise<ProcessedPaymentEvent> {
    logger.error(`Erro ao enviar notificações para pagamento ${event.paymentId}:`, error);
    // Continuar processamento mesmo com erro de notificação
    return event;
  }
};

/**
 * Etapa de auditoria de pagamento
 */
const paymentAuditStep: PipelineStep<ProcessedPaymentEvent, ProcessedPaymentEvent> = {
  name: 'payment-audit',
  async process(event: ProcessedPaymentEvent, context: PipelineContext): Promise<ProcessedPaymentEvent> {
    logger.debug(`Registrando auditoria para pagamento: ${event.paymentId}`);
    
    // Registrar evento de auditoria
    try {
      await queryHelper.query(
        `INSERT INTO tab_audit_log (
          ulid_audit_log,
          entity_type,
          entity_id,
          action,
          user_id,
          details,
          created_at
        ) VALUES (
          $1, $2, $3, $4, $5, $6, NOW()
        )`,
        [
          crypto.randomUUID(),
          'payment',
          event.paymentId,
          `payment_${event.status}`,
          event.userId || null,
          JSON.stringify({
            orderId: event.orderId,
            value: event.value,
            paymentType: event.paymentType,
            status: event.status,
            externalId: event.externalId,
            metadata: event.metadata,
          }),
        ]
      );
    } catch (error) {
      logger.error(`Erro ao registrar auditoria para pagamento ${event.paymentId}:`, error);
      // Não propagar erro para não interromper o pipeline
    }
    
    return event;
  },
  async handleError(error: Error, event: ProcessedPaymentEvent, context: PipelineContext): Promise<ProcessedPaymentEvent> {
    logger.error(`Erro ao registrar auditoria para pagamento ${event.paymentId}:`, error);
    // Continuar processamento mesmo com erro de auditoria
    return event;
  }
};

// Registrar pipeline de pagamento
eventPipelineService.registerPipeline({
  name: 'payment-processing-pipeline',
  description: 'Pipeline para processamento de eventos de pagamento',
  steps: [
    paymentValidationStep,
    paymentEnrichmentStep,
    paymentProcessingStep,
    paymentNotificationStep,
    paymentAuditStep,
  ],
  timeoutMs: 30000, // 30 segundos
  continueOnError: true,
  validateSchema: true,
  schemaName: 'payment.event',
  transformMessage: false,
  storeMetrics: true,
  metricsTtl: 86400 * 7, // 7 dias
});

/**
 * Executa o pipeline de processamento de pagamento
 * @param event - Evento de pagamento
 * @param context - Contexto de processamento
 * @returns Evento processado
 */
export async function executePaymentPipeline(
  event: PaymentEvent,
  context: PipelineContext
): Promise<ProcessedPaymentEvent> {
  return await eventPipelineService.executePipeline<PaymentEvent, ProcessedPaymentEvent>(
    'payment-processing-pipeline',
    event,
    context
  );
}
