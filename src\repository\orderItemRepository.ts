import type { QueryResult } from 'pg';
import { pgHelper } from './pgHelper';

async function create(
  ulid_order: string,
  ulid_product: string,
  qty: number,
  price: number
): Promise<QueryResult> {
  return await pgHelper.query(
    `INSERT INTO tab_order_item (
       ulid_order, 
       ulid_product, 
       qty, 
       price) 
     VALUES ('$1', '$2', $3, $4)
     RETURNING *`,
    [ulid_order, ulid_product, qty, price]
  );
}

async function read(ulid_order_item?: string, ulid_order?: string): Promise<QueryResult> {
  return await pgHelper.query(
    `SELECT * 
       FROM tab_order_item 
      WHERE TRUE 
      ${ulid_order_item ? ` AND ulid_order_item = '$1'` : ''}
      ${ulid_order ? ` AND ulid_order      = '$2'` : ''}`,
    [ulid_order_item, ulid_order]
  );
}

async function update(ulid_order_item: string, qty: number, price: number): Promise<QueryResult> {
  return await pgHelper.query(
    `UPDATE tab_order_item 
        SET qty        = $2, 
            price      = $3,
            updated_at = NOW()
      WHERE ulid_order_item = $1 
     RETURNING *`,
    [qty, price, ulid_order_item]
  );
}

async function deleteByUlid(ulid_order_item: string): Promise<QueryResult> {
  return await pgHelper.query(
    `DELETE FROM tab_order_item 
      WHERE ulid_order_item = $1 
     RETURNING *`,
    [ulid_order_item]
  );
}

export const orderItemRepository = {
  create,
  read,
  update,
  deleteByUlid,
};
