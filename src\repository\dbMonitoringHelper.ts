/**
 * Helper para Monitoramento de Banco de Dados
 * 
 * Este módulo implementa monitoramento para operações de banco de dados,
 * coletando métricas de performance e uso para o serviço de monitoramento.
 */

import { Pool, PoolClient, QueryResult } from 'pg';
import { monitoringEvents } from '@services/applicationMonitoringService';
import { logger } from '@utils/logger';
import { performance } from 'perf_hooks';

/**
 * Interface para dados de consulta
 */
interface QueryData {
  id: string;
  text: string;
  values?: any[];
  startTime: number;
}

/**
 * Mapa de consultas em andamento
 */
const activeQueries = new Map<string, QueryData>();

/**
 * Gera um ID único para a consulta
 * @returns ID único
 */
function generateQueryId(): string {
  return Math.random().toString(36).substring(2, 15) + 
         Math.random().toString(36).substring(2, 15);
}

/**
 * Intercepta e monitora consultas de um pool PostgreSQL
 * @param pool Pool PostgreSQL
 * @returns Pool com monitoramento
 */
export function monitorPool(pool: Pool): Pool {
  // Interceptar método query
  const originalQuery = pool.query.bind(pool);
  
  // Substituir com versão monitorada
  pool.query = function monitoredQuery<T>(
    textOrConfig: string | any,
    values?: any[]
  ): Promise<QueryResult<T>> {
    // Extrair texto da consulta
    const text = typeof textOrConfig === 'string' ? textOrConfig : textOrConfig.text;
    const queryValues = typeof textOrConfig === 'string' ? values : textOrConfig.values;
    
    // Gerar ID único para a consulta
    const queryId = generateQueryId();
    
    // Registrar dados da consulta
    const queryData: QueryData = {
      id: queryId,
      text,
      values: queryValues,
      startTime: performance.now()
    };
    
    // Armazenar consulta ativa
    activeQueries.set(queryId, queryData);
    
    // Emitir evento de início de consulta
    monitoringEvents.emit('db_query_start', {
      id: queryId,
      query: text
    });
    
    // Executar consulta original
    return originalQuery(textOrConfig, values)
      .then((result: QueryResult<T>) => {
        // Obter dados da consulta
        const data = activeQueries.get(queryId);
        
        if (data) {
          // Calcular duração
          const duration = performance.now() - data.startTime;
          
          // Emitir evento de fim de consulta
          monitoringEvents.emit('db_query_end', {
            id: queryId,
            query: data.text,
            duration,
            rowCount: result.rowCount
          });
          
          // Remover consulta do mapa de ativas
          activeQueries.delete(queryId);
        }
        
        return result;
      })
      .catch((error: Error) => {
        // Obter dados da consulta
        const data = activeQueries.get(queryId);
        
        if (data) {
          // Calcular duração
          const duration = performance.now() - data.startTime;
          
          // Emitir evento de erro de consulta
          monitoringEvents.emit('db_error', {
            id: queryId,
            query: data.text,
            error: error.message,
            duration
          });
          
          // Registrar erro no log
          logger.error(`Erro em consulta SQL: ${error.message}`, {
            query: data.text,
            values: data.values,
            error: error.message,
            stack: error.stack
          });
          
          // Remover consulta do mapa de ativas
          activeQueries.delete(queryId);
        }
        
        throw error;
      });
  };
  
  return pool;
}

/**
 * Intercepta e monitora consultas de um cliente PostgreSQL
 * @param client Cliente PostgreSQL
 * @returns Cliente com monitoramento
 */
export function monitorClient(client: PoolClient): PoolClient {
  // Interceptar método query
  const originalQuery = client.query.bind(client);
  
  // Substituir com versão monitorada
  client.query = function monitoredQuery<T>(
    textOrConfig: string | any,
    values?: any[]
  ): Promise<QueryResult<T>> {
    // Extrair texto da consulta
    const text = typeof textOrConfig === 'string' ? textOrConfig : textOrConfig.text;
    const queryValues = typeof textOrConfig === 'string' ? values : textOrConfig.values;
    
    // Gerar ID único para a consulta
    const queryId = generateQueryId();
    
    // Registrar dados da consulta
    const queryData: QueryData = {
      id: queryId,
      text,
      values: queryValues,
      startTime: performance.now()
    };
    
    // Armazenar consulta ativa
    activeQueries.set(queryId, queryData);
    
    // Emitir evento de início de consulta
    monitoringEvents.emit('db_query_start', {
      id: queryId,
      query: text
    });
    
    // Executar consulta original
    return originalQuery(textOrConfig, values)
      .then((result: QueryResult<T>) => {
        // Obter dados da consulta
        const data = activeQueries.get(queryId);
        
        if (data) {
          // Calcular duração
          const duration = performance.now() - data.startTime;
          
          // Emitir evento de fim de consulta
          monitoringEvents.emit('db_query_end', {
            id: queryId,
            query: data.text,
            duration,
            rowCount: result.rowCount
          });
          
          // Remover consulta do mapa de ativas
          activeQueries.delete(queryId);
        }
        
        return result;
      })
      .catch((error: Error) => {
        // Obter dados da consulta
        const data = activeQueries.get(queryId);
        
        if (data) {
          // Calcular duração
          const duration = performance.now() - data.startTime;
          
          // Emitir evento de erro de consulta
          monitoringEvents.emit('db_error', {
            id: queryId,
            query: data.text,
            error: error.message,
            duration
          });
          
          // Registrar erro no log
          logger.error(`Erro em consulta SQL: ${error.message}`, {
            query: data.text,
            values: data.values,
            error: error.message,
            stack: error.stack
          });
          
          // Remover consulta do mapa de ativas
          activeQueries.delete(queryId);
        }
        
        throw error;
      });
  };
  
  return client;
}

/**
 * Obtém estatísticas de consultas ativas
 * @returns Estatísticas de consultas
 */
export function getQueryStats(): Record<string, any> {
  const stats = {
    activeQueries: activeQueries.size,
    queriesByType: {} as Record<string, number>
  };
  
  // Agrupar consultas por tipo
  for (const [_, data] of activeQueries.entries()) {
    // Extrair tipo de consulta (SELECT, INSERT, UPDATE, DELETE, etc.)
    const queryType = data.text.trim().split(' ')[0].toUpperCase();
    
    if (!stats.queriesByType[queryType]) {
      stats.queriesByType[queryType] = 0;
    }
    stats.queriesByType[queryType]++;
  }
  
  return stats;
}
