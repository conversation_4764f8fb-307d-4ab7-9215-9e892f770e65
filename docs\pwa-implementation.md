# Implementação de Progressive Web App (PWA)

## Visão Geral

A implementação de Progressive Web App (PWA) permite que a aplicação Estação Alfabetização seja instalada em dispositivos móveis e desktop, funcionando offline e oferecendo uma experiência semelhante a um aplicativo nativo. Esta documentação descreve a arquitetura, componentes e funcionalidades da implementação PWA.

## Arquitetura

A implementação PWA é composta pelos seguintes componentes:

1. **Manifesto da Aplicação**: Arquivo JSON que define metadados da aplicação.
2. **Service Worker**: Script JavaScript que gerencia cache e funcionalidades offline.
3. **Componente PWAInstaller**: Componente Astro que gerencia o registro do service worker e a instalação da aplicação.
4. **Página Offline**: Página HTML exibida quando o usuário está offline.
5. **Meta Tags e Links**: Elementos HTML que definem a aparência da aplicação quando instalada.

## Manifesto da Aplicação

O arquivo `manifest.json` define os metadados da aplicação, incluindo:

- Nome e descrição da aplicação
- Ícones em diferentes tamanhos
- Cores de tema e fundo
- Configurações de exibição
- Orientação preferida
- URLs para inicialização
- Atalhos para funcionalidades principais

```json
{
  "name": "Estação Alfabetização",
  "short_name": "Alfabetização",
  "description": "Plataforma de recursos educacionais para alfabetização",
  "start_url": "/",
  "display": "standalone",
  "background_color": "#ffffff",
  "theme_color": "#0078d7",
  "orientation": "portrait-primary",
  "icons": [
    {
      "src": "/icons/icon-192x192.png",
      "sizes": "192x192",
      "type": "image/png",
      "purpose": "any maskable"
    },
    {
      "src": "/icons/icon-512x512.png",
      "sizes": "512x512",
      "type": "image/png",
      "purpose": "any maskable"
    }
  ]
}
```

## Service Worker

O service worker (`service-worker.js`) implementa as seguintes funcionalidades:

### Cache de Assets

O service worker utiliza diferentes estratégias de cache para diferentes tipos de recursos:

- **Cache First**: Para assets estáticos (JS, CSS, imagens, fontes)
- **Network First**: Para páginas HTML e navegação
- **Network Only**: Para APIs e rotas de autenticação
- **Stale While Revalidate**: Para outros recursos

```javascript
// Estratégia para assets estáticos: Cache First
if (CACHE_FIRST_PATTERNS.some(pattern => pattern.test(event.request.url))) {
  event.respondWith(
    caches.match(event.request)
      .then(cachedResponse => {
        if (cachedResponse) {
          // Atualizar o cache em background
          fetch(event.request)
            .then(response => {
              if (response.ok) {
                caches.open(CACHE_NAME)
                  .then(cache => cache.put(event.request, response));
              }
            })
            .catch(() => {});
          
          return cachedResponse;
        }
        
        // Se não estiver no cache, buscar da rede e cachear
        return fetch(event.request)
          .then(response => {
            // Clonar a resposta para poder armazenar no cache
            const clonedResponse = response.clone();
            
            if (response.ok) {
              caches.open(CACHE_NAME)
                .then(cache => cache.put(event.request, clonedResponse));
            }
            
            return response;
          });
      })
  );
}
```

### Sincronização em Background

O service worker implementa sincronização em background para enviar dados quando o usuário está offline:

```javascript
// Evento de sincronização em background
self.addEventListener('sync', event => {
  if (event.tag === 'sync-data') {
    event.waitUntil(syncData());
  }
});

// Função para sincronizar dados em background
async function syncData() {
  // Obter dados pendentes do IndexedDB
  const db = await openDatabase();
  const pendingData = await getPendingData(db);
  
  // Enviar dados pendentes para o servidor
  for (const data of pendingData) {
    try {
      const response = await fetch('/api/sync', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
      });
      
      if (response.ok) {
        // Marcar como sincronizado
        await markAsSynced(db, data.id);
      }
    } catch (error) {
      console.error('Erro ao sincronizar:', error);
    }
  }
}
```

### Notificações Push

O service worker implementa suporte a notificações push:

```javascript
// Evento de notificação push
self.addEventListener('push', event => {
  if (!event.data) return;
  
  try {
    const data = event.data.json();
    
    const options = {
      body: data.body || 'Nova notificação',
      icon: data.icon || '/icons/icon-192x192.png',
      badge: data.badge || '/icons/badge-96x96.png',
      data: data.data || {},
      actions: data.actions || [],
      vibrate: data.vibrate || [100, 50, 100],
      tag: data.tag || 'default',
      renotify: data.renotify || false
    };
    
    event.waitUntil(
      self.registration.showNotification(data.title || 'Estação Alfabetização', options)
    );
  } catch (error) {
    console.error('Erro ao processar notificação push:', error);
  }
});
```

### Atualização Automática

O service worker implementa detecção e aplicação de atualizações:

```javascript
// Evento de ativação - limpeza de caches antigos
self.addEventListener('activate', event => {
  event.waitUntil(
    caches.keys()
      .then(cacheNames => {
        return Promise.all(
          cacheNames
            .filter(cacheName => {
              // Remover caches antigos
              return (cacheName.startsWith('estacao-alfabetizacao-') && 
                     cacheName !== CACHE_NAME && 
                     cacheName !== OFFLINE_CACHE_NAME);
            })
            .map(cacheName => caches.delete(cacheName))
        );
      })
      .then(() => self.clients.claim())
  );
});

// Evento de mensagem
self.addEventListener('message', event => {
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting();
  }
});
```

## Componente PWAInstaller

O componente `PWAInstaller.astro` gerencia o registro do service worker e a instalação da aplicação:

### Registro do Service Worker

```javascript
// Registrar o service worker
async function registerServiceWorker() {
  if ('serviceWorker' in navigator) {
    try {
      // Registrar o service worker
      registration = await navigator.serviceWorker.register(swPath);
      console.log('Service Worker registrado com sucesso:', registration.scope);
      
      // Verificar atualizações
      checkForUpdates();
      
      // Adicionar listener para atualizações
      navigator.serviceWorker.addEventListener('controllerchange', () => {
        if (!refreshing) {
          refreshing = true;
          window.location.reload();
        }
      });
    } catch (error) {
      console.error('Erro ao registrar Service Worker:', error);
    }
  }
}
```

### Prompt de Instalação

```javascript
// Configurar evento de instalação
function setupInstallPrompt() {
  window.addEventListener('beforeinstallprompt', (e) => {
    // Prevenir o prompt automático
    e.preventDefault();
    
    // Armazenar o evento para uso posterior
    deferredPrompt = e;
    
    // Mostrar o botão de instalação
    const installButton = document.getElementById('pwa-install-button');
    if (installButton) {
      installButton.style.display = 'flex';
    }
  });
  
  // Esconder o botão se o app já estiver instalado
  window.addEventListener('appinstalled', () => {
    const installButton = document.getElementById('pwa-install-button');
    if (installButton) {
      installButton.style.display = 'none';
    }
    
    deferredPrompt = null;
    console.log('PWA instalado com sucesso');
  });
}
```

### Notificação de Atualização

```javascript
// Mostrar notificação de atualização
function showUpdateNotification() {
  const updateToast = document.getElementById('pwa-update-toast');
  if (updateToast) {
    updateToast.style.display = 'block';
  }
}

// Aplicar atualização
function applyUpdate() {
  if (registration && registration.waiting) {
    // Enviar mensagem para o service worker ativar a nova versão
    registration.waiting.postMessage({ type: 'SKIP_WAITING' });
  }
}
```

## Página Offline

A página `offline.html` é exibida quando o usuário está offline e tenta acessar uma página não cacheada:

- Exibe uma mensagem informando que o usuário está offline
- Oferece um botão para tentar novamente
- Lista páginas disponíveis offline
- Monitora o status da conexão e atualiza a interface

```javascript
// Listar páginas em cache
async function listCachedPages() {
  if ('caches' in window) {
    try {
      const cache = await caches.open('estacao-alfabetizacao-v1');
      const keys = await cache.keys();
      const htmlPages = keys.filter(request => 
        request.url.endsWith('.html') || 
        request.url.endsWith('/') ||
        !request.url.includes('.')
      );
      
      const cachedList = document.getElementById('cached-pages');
      
      // Limpar lista atual
      while (cachedList.firstChild) {
        cachedList.removeChild(cachedList.firstChild);
      }
      
      // Adicionar página inicial
      const homeItem = document.createElement('li');
      homeItem.innerHTML = '<a href="/">Página inicial</a>';
      cachedList.appendChild(homeItem);
      
      // Adicionar outras páginas em cache
      htmlPages.forEach(request => {
        const url = new URL(request.url);
        
        // Ignorar a própria página offline e a página inicial (já adicionada)
        if (url.pathname === '/offline.html' || url.pathname === '/') {
          return;
        }
        
        const item = document.createElement('li');
        const link = document.createElement('a');
        link.href = url.pathname;
        
        // Criar título amigável a partir do caminho
        const title = url.pathname
          .split('/')
          .filter(Boolean)
          .map(part => part.replace(/[-_]/g, ' '))
          .map(part => part.charAt(0).toUpperCase() + part.slice(1))
          .join(' > ');
        
        link.textContent = title || url.pathname;
        item.appendChild(link);
        cachedList.appendChild(item);
      });
    } catch (error) {
      console.error('Erro ao listar páginas em cache:', error);
    }
  }
}
```

## Meta Tags e Links

As meta tags e links no `BaseLayout.astro` definem a aparência da aplicação quando instalada:

```html
{/* Meta tags para PWA */}
<meta name="theme-color" content="#0078d7">
<meta name="apple-mobile-web-app-capable" content="yes">
<meta name="apple-mobile-web-app-status-bar-style" content="default">
<meta name="apple-mobile-web-app-title" content="Estação Alfabetização">
<meta name="application-name" content="Estação Alfabetização">
<meta name="format-detection" content="telephone=no">
<meta name="mobile-web-app-capable" content="yes">

{/* Links para PWA */}
<link rel="manifest" href="/manifest.json">
<link rel="apple-touch-icon" href="/icons/apple-touch-icon.png">
<link rel="apple-touch-startup-image" href="/icons/apple-splash-2048-2732.jpg" media="(device-width: 1024px) and (device-height: 1366px) and (-webkit-device-pixel-ratio: 2) and (orientation: portrait)">
```

## Boas Práticas

### Performance

- **Precache de Assets Críticos**: Armazenar em cache os assets críticos durante a instalação do service worker.
- **Estratégias de Cache Apropriadas**: Usar diferentes estratégias de cache para diferentes tipos de recursos.
- **Atualização em Background**: Atualizar o cache em background para manter o conteúdo fresco.

### Experiência do Usuário

- **Feedback de Instalação**: Fornecer feedback claro sobre a instalação da aplicação.
- **Notificação de Atualização**: Notificar o usuário quando uma nova versão estiver disponível.
- **Experiência Offline**: Fornecer uma experiência útil quando o usuário está offline.

### Segurança

- **HTTPS**: Garantir que a aplicação seja servida via HTTPS.
- **Escopo Limitado**: Limitar o escopo do service worker para evitar problemas de segurança.
- **Validação de Dados**: Validar dados antes de armazenar no cache ou IndexedDB.

## Troubleshooting

### Problemas Comuns

1. **Service Worker Não Registrado**:
   - Verificar se o navegador suporta service workers.
   - Verificar se a aplicação está sendo servida via HTTPS.
   - Verificar erros no console do navegador.

2. **Conteúdo Não Cacheado**:
   - Verificar se os padrões de URL estão corretos.
   - Verificar se o service worker está ativo.
   - Verificar se há espaço de armazenamento disponível.

3. **Atualizações Não Aplicadas**:
   - Verificar se o service worker está sendo atualizado.
   - Verificar se a mensagem `SKIP_WAITING` está sendo enviada.
   - Verificar se o evento `controllerchange` está sendo tratado.

## Referências

- [Web App Manifest](https://developer.mozilla.org/en-US/docs/Web/Manifest)
- [Service Workers](https://developer.mozilla.org/en-US/docs/Web/API/Service_Worker_API)
- [Workbox](https://developers.google.com/web/tools/workbox)
- [PWA Builder](https://www.pwabuilder.com/)
- [Google PWA Checklist](https://web.dev/pwa-checklist/)
