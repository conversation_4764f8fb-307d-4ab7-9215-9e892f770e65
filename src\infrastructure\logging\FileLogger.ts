/**
 * Implementação de logger para arquivo
 *
 * Esta classe implementa um logger que escreve em arquivos.
 */

import * as fs from 'node:fs';
import * as path from 'node:path';
import { LogLevel, LoggerConfig } from '../../domain/interfaces/Logger';
import { BaseLogger } from './BaseLogger';

/**
 * Logger para arquivo
 */
export class FileLogger extends BaseLogger {
  /**
   * Caminho do arquivo de log
   */
  private readonly filePath: string;

  /**
   * Tamanho máximo do arquivo em bytes
   */
  private readonly maxSize: number;

  /**
   * Número máximo de arquivos de backup
   */
  private readonly maxFiles: number;

  /**
   * Cria uma nova instância de FileLogger
   * @param context - Contexto do logger
   * @param config - Configuração do logger
   */
  constructor(context: string, config: LoggerConfig) {
    super(context, config);

    // Obter configurações específicas
    const fileConfig = config.destinations?.find((d) => d.type === 'file')?.config || {};

    this.filePath = fileConfig.filePath || 'logs/app.log';
    this.maxSize = fileConfig.maxSize || 10 * 1024 * 1024; // 10 MB
    this.maxFiles = fileConfig.maxFiles || 5;

    // Garantir que o diretório de logs existe
    this.ensureLogDirectory();
  }

  /**
   * Escreve uma entrada de log no arquivo
   * @param level - Nível de log
   * @param logEntry - Entrada de log formatada
   */
  protected writeLog(level: LogLevel, logEntry: any): void {
    try {
      // Verificar se o arquivo precisa ser rotacionado
      this.checkRotation();

      // Formatar a entrada de log
      const format = this.config.format || 'text';
      let logLine: string;

      if (format === 'json') {
        logLine = `${JSON.stringify(logEntry)}\n`;
      } else {
        logLine = `${this.formatTextLog(logEntry)}\n`;
      }

      // Escrever no arquivo
      fs.appendFileSync(this.filePath, logLine);
    } catch (error) {
      // Não podemos usar o logger para registrar erros do próprio logger
      console.error(
        `Failed to write to log file: ${error instanceof Error ? error.message : String(error)}`
      );
    }
  }

  /**
   * Formata uma entrada de log como texto
   * @param logEntry - Entrada de log formatada
   * @returns Linha de log formatada
   */
  private formatTextLog(logEntry: any): string {
    let message = '';

    // Adicionar timestamp
    if (logEntry.timestamp) {
      message += `[${logEntry.timestamp}] `;
    }

    // Adicionar nível
    message += `${logEntry.level.toUpperCase().padEnd(5)} `;

    // Adicionar contexto
    if (logEntry.context) {
      message += `[${logEntry.context}] `;
    }

    // Adicionar mensagem
    message += logEntry.message;

    // Adicionar metadados
    if (logEntry.metadata) {
      const metadataStr = JSON.stringify(logEntry.metadata);
      message += ` ${metadataStr}`;
    }

    return message;
  }

  /**
   * Garante que o diretório de logs existe
   */
  private ensureLogDirectory(): void {
    const dir = path.dirname(this.filePath);

    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
  }

  /**
   * Verifica se o arquivo de log precisa ser rotacionado
   */
  private checkRotation(): void {
    try {
      // Verificar se o arquivo existe
      if (!fs.existsSync(this.filePath)) {
        return;
      }

      // Verificar o tamanho do arquivo
      const stats = fs.statSync(this.filePath);

      if (stats.size >= this.maxSize) {
        this.rotateLogFiles();
      }
    } catch (error) {
      console.error(
        `Failed to check log rotation: ${error instanceof Error ? error.message : String(error)}`
      );
    }
  }

  /**
   * Rotaciona os arquivos de log
   */
  private rotateLogFiles(): void {
    try {
      // Remover o arquivo mais antigo se necessário
      const oldestLog = `${this.filePath}.${this.maxFiles}`;
      if (fs.existsSync(oldestLog)) {
        fs.unlinkSync(oldestLog);
      }

      // Rotacionar os arquivos existentes
      for (let i = this.maxFiles - 1; i >= 1; i--) {
        const oldFile = `${this.filePath}.${i}`;
        const newFile = `${this.filePath}.${i + 1}`;

        if (fs.existsSync(oldFile)) {
          fs.renameSync(oldFile, newFile);
        }
      }

      // Renomear o arquivo atual
      fs.renameSync(this.filePath, `${this.filePath}.1`);
    } catch (error) {
      console.error(
        `Failed to rotate log files: ${error instanceof Error ? error.message : String(error)}`
      );
    }
  }
}
