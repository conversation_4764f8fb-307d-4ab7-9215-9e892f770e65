/**
 * Default SEO Service
 *
 * Implementação padrão do serviço de SEO.
 * Parte da implementação da tarefa 8.9.1 - Otimização para buscadores
 */

import { Pool } from 'pg';
import { SEO, SocialMediaMeta, StructuredData } from '../../domain/entities/SEO';
import { PageSEOData, SEOService } from '../../domain/services/SEOService';
import { getDbConnection } from '../database/connection';

export class DefaultSEOService implements SEOService {
  private pool: Pool;
  private baseUrl: string;
  private defaultTitle: string;
  private defaultDescription: string;
  private defaultKeywords: string[];
  private defaultSocialMedia: Partial<SocialMediaMeta>;

  constructor(
    baseUrl: string,
    defaultTitle: string,
    defaultDescription: string,
    defaultKeywords: string[] = [],
    defaultSocialMedia: Partial<SocialMediaMeta> = {}
  ) {
    this.pool = getDbConnection();
    this.baseUrl = baseUrl;
    this.defaultTitle = defaultTitle;
    this.defaultDescription = defaultDescription;
    this.defaultKeywords = defaultKeywords;
    this.defaultSocialMedia = defaultSocialMedia;
  }

  /**
   * Obtém as configurações de SEO para uma página
   */
  async getPageSEO(path: string): Promise<SEO | null> {
    try {
      // Normalizar o caminho
      const normalizedPath = this.normalizePath(path);

      // Consultar o banco de dados
      const query = `
        SELECT * FROM page_seo
        WHERE path = $1
      `;

      const result = await this.pool.query(query, [normalizedPath]);

      if (result.rows.length === 0) {
        return null;
      }

      const row = result.rows[0];

      // Obter dados estruturados
      const structuredDataQuery = `
        SELECT type, data FROM page_structured_data
        WHERE page_path = $1
      `;

      const structuredDataResult = await this.pool.query(structuredDataQuery, [normalizedPath]);

      const structuredData: StructuredData[] = structuredDataResult.rows.map((row) => ({
        type: row.type,
        data: row.data,
      }));

      // Obter linguagens alternativas
      const alternateLanguagesQuery = `
        SELECT language, url FROM page_alternate_languages
        WHERE page_path = $1
      `;

      const alternateLanguagesResult = await this.pool.query(alternateLanguagesQuery, [
        normalizedPath,
      ]);

      const alternateLanguages: Record<string, string> = {};

      alternateLanguagesResult.rows.forEach((row) => {
        alternateLanguages[row.language] = row.url;
      });

      // Construir objeto SEO
      return new SEO({
        title: row.title,
        description: row.description,
        keywords: row.keywords || [],
        canonicalUrl: row.canonical_url,
        robots: row.robots,
        socialMedia: {
          ogTitle: row.og_title,
          ogDescription: row.og_description,
          ogImage: row.og_image,
          ogUrl: row.og_url,
          ogType: row.og_type,
          ogSiteName: row.og_site_name,
          twitterCard: row.twitter_card as any,
          twitterTitle: row.twitter_title,
          twitterDescription: row.twitter_description,
          twitterImage: row.twitter_image,
          twitterSite: row.twitter_site,
          twitterCreator: row.twitter_creator,
        },
        structuredData,
        alternateLanguages,
      });
    } catch (error) {
      console.error('Erro ao obter configurações de SEO:', error);
      return null;
    }
  }

  /**
   * Salva as configurações de SEO para uma página
   */
  async savePageSEO(data: PageSEOData): Promise<SEO> {
    const client = await this.pool.connect();

    try {
      await client.query('BEGIN');

      // Normalizar o caminho
      const normalizedPath = this.normalizePath(data.path);

      // Verificar se a página já tem configurações de SEO
      const existingQuery = `
        SELECT COUNT(*) FROM page_seo
        WHERE path = $1
      `;

      const existingResult = await client.query(existingQuery, [normalizedPath]);
      const exists = Number.parseInt(existingResult.rows[0].count) > 0;

      if (exists) {
        // Atualizar configurações existentes
        const updateQuery = `
          UPDATE page_seo
          SET
            title = $1,
            description = $2,
            keywords = $3,
            canonical_url = $4,
            robots = $5,
            og_title = $6,
            og_description = $7,
            og_image = $8,
            og_url = $9,
            og_type = $10,
            og_site_name = $11,
            twitter_card = $12,
            twitter_title = $13,
            twitter_description = $14,
            twitter_image = $15,
            twitter_site = $16,
            twitter_creator = $17,
            updated_at = NOW()
          WHERE path = $18
        `;

        await client.query(updateQuery, [
          data.title,
          data.description,
          data.keywords || [],
          data.canonicalUrl,
          data.robots,
          data.socialMedia?.ogTitle,
          data.socialMedia?.ogDescription,
          data.socialMedia?.ogImage,
          data.socialMedia?.ogUrl,
          data.socialMedia?.ogType,
          data.socialMedia?.ogSiteName,
          data.socialMedia?.twitterCard,
          data.socialMedia?.twitterTitle,
          data.socialMedia?.twitterDescription,
          data.socialMedia?.twitterImage,
          data.socialMedia?.twitterSite,
          data.socialMedia?.twitterCreator,
          normalizedPath,
        ]);
      } else {
        // Inserir novas configurações
        const insertQuery = `
          INSERT INTO page_seo (
            path,
            title,
            description,
            keywords,
            canonical_url,
            robots,
            og_title,
            og_description,
            og_image,
            og_url,
            og_type,
            og_site_name,
            twitter_card,
            twitter_title,
            twitter_description,
            twitter_image,
            twitter_site,
            twitter_creator,
            created_at,
            updated_at
          ) VALUES (
            $1, $2, $3, $4, $5, $6, $7, $8, $9, $10,
            $11, $12, $13, $14, $15, $16, $17, $18, NOW(), NOW()
          )
        `;

        await client.query(insertQuery, [
          normalizedPath,
          data.title,
          data.description,
          data.keywords || [],
          data.canonicalUrl,
          data.robots,
          data.socialMedia?.ogTitle,
          data.socialMedia?.ogDescription,
          data.socialMedia?.ogImage,
          data.socialMedia?.ogUrl,
          data.socialMedia?.ogType,
          data.socialMedia?.ogSiteName,
          data.socialMedia?.twitterCard,
          data.socialMedia?.twitterTitle,
          data.socialMedia?.twitterDescription,
          data.socialMedia?.twitterImage,
          data.socialMedia?.twitterSite,
          data.socialMedia?.twitterCreator,
        ]);
      }

      // Limpar dados estruturados existentes
      await client.query('DELETE FROM page_structured_data WHERE page_path = $1', [normalizedPath]);

      // Inserir novos dados estruturados
      if (data.structuredData && data.structuredData.length > 0) {
        for (const structuredData of data.structuredData) {
          const insertStructuredDataQuery = `
            INSERT INTO page_structured_data (
              page_path,
              type,
              data,
              created_at
            ) VALUES (
              $1, $2, $3, NOW()
            )
          `;

          await client.query(insertStructuredDataQuery, [
            normalizedPath,
            structuredData.type,
            structuredData.data,
          ]);
        }
      }

      // Limpar linguagens alternativas existentes
      await client.query('DELETE FROM page_alternate_languages WHERE page_path = $1', [
        normalizedPath,
      ]);

      // Inserir novas linguagens alternativas
      if (data.alternateLanguages && Object.keys(data.alternateLanguages).length > 0) {
        for (const [language, url] of Object.entries(data.alternateLanguages)) {
          const insertAlternateLanguageQuery = `
            INSERT INTO page_alternate_languages (
              page_path,
              language,
              url,
              created_at
            ) VALUES (
              $1, $2, $3, NOW()
            )
          `;

          await client.query(insertAlternateLanguageQuery, [normalizedPath, language, url]);
        }
      }

      await client.query('COMMIT');

      // Construir e retornar objeto SEO
      return new SEO({
        title: data.title,
        description: data.description,
        keywords: data.keywords,
        canonicalUrl: data.canonicalUrl,
        robots: data.robots,
        socialMedia: data.socialMedia,
        structuredData: data.structuredData,
        alternateLanguages: data.alternateLanguages,
      });
    } catch (error) {
      await client.query('ROLLBACK');
      console.error('Erro ao salvar configurações de SEO:', error);

      // Retornar SEO padrão em caso de erro
      return this.generateDefaultSEO(data.title, data.description, data.path, this.baseUrl);
    } finally {
      client.release();
    }
  }

  /**
   * Atualiza as configurações de SEO para uma página
   */
  async updatePageSEO(
    path: string,
    updates: Partial<Omit<PageSEOData, 'path'>>
  ): Promise<SEO | null> {
    try {
      // Obter configurações atuais
      const currentSEO = await this.getPageSEO(path);

      if (!currentSEO) {
        return null;
      }

      // Mesclar atualizações com configurações atuais
      const updatedData: PageSEOData = {
        path,
        title: updates.title || currentSEO.title,
        description: updates.description || currentSEO.description,
        keywords: updates.keywords || currentSEO.keywords,
        canonicalUrl:
          updates.canonicalUrl !== undefined ? updates.canonicalUrl : currentSEO.canonicalUrl,
        robots: updates.robots !== undefined ? updates.robots : currentSEO.robots,
        socialMedia: updates.socialMedia
          ? { ...currentSEO.socialMedia, ...updates.socialMedia }
          : currentSEO.socialMedia,
        structuredData: updates.structuredData || currentSEO.structuredData,
        alternateLanguages: updates.alternateLanguages || currentSEO.alternateLanguages,
      };

      // Salvar configurações atualizadas
      return await this.savePageSEO(updatedData);
    } catch (error) {
      console.error('Erro ao atualizar configurações de SEO:', error);
      return null;
    }
  }

  /**
   * Remove as configurações de SEO para uma página
   */
  async removePageSEO(path: string): Promise<boolean> {
    const client = await this.pool.connect();

    try {
      await client.query('BEGIN');

      // Normalizar o caminho
      const normalizedPath = this.normalizePath(path);

      // Remover linguagens alternativas
      await client.query('DELETE FROM page_alternate_languages WHERE page_path = $1', [
        normalizedPath,
      ]);

      // Remover dados estruturados
      await client.query('DELETE FROM page_structured_data WHERE page_path = $1', [normalizedPath]);

      // Remover configurações de SEO
      await client.query('DELETE FROM page_seo WHERE path = $1', [normalizedPath]);

      await client.query('COMMIT');

      return true;
    } catch (error) {
      await client.query('ROLLBACK');
      console.error('Erro ao remover configurações de SEO:', error);
      return false;
    } finally {
      client.release();
    }
  }

  /**
   * Gera as meta tags para uma página
   */
  async generateMetaTags(path: string): Promise<Record<string, string>> {
    try {
      // Obter configurações de SEO
      const seo = await this.getPageSEO(path);

      if (seo) {
        return seo.generateMetaTags();
      }

      // Retornar meta tags padrão se não houver configurações específicas
      const defaultSEO = this.generateDefaultSEO(
        this.defaultTitle,
        this.defaultDescription,
        path,
        this.baseUrl
      );

      return defaultSEO.generateMetaTags();
    } catch (error) {
      console.error('Erro ao gerar meta tags:', error);

      // Retornar meta tags mínimas em caso de erro
      return {
        title: this.defaultTitle,
        description: this.defaultDescription,
      };
    }
  }

  /**
   * Gera os dados estruturados em formato JSON-LD para uma página
   */
  async generateStructuredDataJsonLd(path: string): Promise<string> {
    try {
      // Obter configurações de SEO
      const seo = await this.getPageSEO(path);

      if (seo) {
        return seo.generateStructuredDataJsonLd();
      }

      return '';
    } catch (error) {
      console.error('Erro ao gerar dados estruturados JSON-LD:', error);
      return '';
    }
  }

  /**
   * Gera as configurações de SEO padrão para uma página
   */
  generateDefaultSEO(title: string, description: string, path: string, baseUrl: string): SEO {
    const url = `${baseUrl}${path}`;

    return new SEO({
      title,
      description,
      keywords: this.defaultKeywords,
      canonicalUrl: url,
      socialMedia: {
        ...this.defaultSocialMedia,
        ogTitle: title,
        ogDescription: description,
        ogUrl: url,
        ogType: 'website',
        twitterTitle: title,
        twitterDescription: description,
      },
    });
  }

  /**
   * Adiciona dados estruturados a uma página
   */
  async addStructuredData(path: string, structuredData: StructuredData): Promise<boolean> {
    try {
      // Normalizar o caminho
      const normalizedPath = this.normalizePath(path);

      // Verificar se a página existe
      const pageExists = await this.hasPageSEO(normalizedPath);

      if (!pageExists) {
        return false;
      }

      // Inserir dados estruturados
      const query = `
        INSERT INTO page_structured_data (
          page_path,
          type,
          data,
          created_at
        ) VALUES (
          $1, $2, $3, NOW()
        )
      `;

      await this.pool.query(query, [normalizedPath, structuredData.type, structuredData.data]);

      return true;
    } catch (error) {
      console.error('Erro ao adicionar dados estruturados:', error);
      return false;
    }
  }

  /**
   * Remove dados estruturados de uma página pelo tipo
   */
  async removeStructuredDataByType(path: string, type: string): Promise<boolean> {
    try {
      // Normalizar o caminho
      const normalizedPath = this.normalizePath(path);

      // Remover dados estruturados
      const query = `
        DELETE FROM page_structured_data
        WHERE page_path = $1 AND type = $2
      `;

      await this.pool.query(query, [normalizedPath, type]);

      return true;
    } catch (error) {
      console.error('Erro ao remover dados estruturados:', error);
      return false;
    }
  }

  /**
   * Adiciona uma linguagem alternativa a uma página
   */
  async addAlternateLanguage(path: string, language: string, url: string): Promise<boolean> {
    try {
      // Normalizar o caminho
      const normalizedPath = this.normalizePath(path);

      // Verificar se a página existe
      const pageExists = await this.hasPageSEO(normalizedPath);

      if (!pageExists) {
        return false;
      }

      // Verificar se a linguagem já existe
      const existingQuery = `
        SELECT COUNT(*) FROM page_alternate_languages
        WHERE page_path = $1 AND language = $2
      `;

      const existingResult = await this.pool.query(existingQuery, [normalizedPath, language]);
      const exists = Number.parseInt(existingResult.rows[0].count) > 0;

      if (exists) {
        // Atualizar URL existente
        const updateQuery = `
          UPDATE page_alternate_languages
          SET url = $1
          WHERE page_path = $2 AND language = $3
        `;

        await this.pool.query(updateQuery, [url, normalizedPath, language]);
      } else {
        // Inserir nova linguagem alternativa
        const insertQuery = `
          INSERT INTO page_alternate_languages (
            page_path,
            language,
            url,
            created_at
          ) VALUES (
            $1, $2, $3, NOW()
          )
        `;

        await this.pool.query(insertQuery, [normalizedPath, language, url]);
      }

      return true;
    } catch (error) {
      console.error('Erro ao adicionar linguagem alternativa:', error);
      return false;
    }
  }

  /**
   * Remove uma linguagem alternativa de uma página
   */
  async removeAlternateLanguage(path: string, language: string): Promise<boolean> {
    try {
      // Normalizar o caminho
      const normalizedPath = this.normalizePath(path);

      // Remover linguagem alternativa
      const query = `
        DELETE FROM page_alternate_languages
        WHERE page_path = $1 AND language = $2
      `;

      await this.pool.query(query, [normalizedPath, language]);

      return true;
    } catch (error) {
      console.error('Erro ao remover linguagem alternativa:', error);
      return false;
    }
  }

  /**
   * Verifica se uma página tem configurações de SEO
   */
  async hasPageSEO(path: string): Promise<boolean> {
    try {
      // Normalizar o caminho
      const normalizedPath = this.normalizePath(path);

      // Verificar se a página existe
      const query = `
        SELECT COUNT(*) FROM page_seo
        WHERE path = $1
      `;

      const result = await this.pool.query(query, [normalizedPath]);

      return Number.parseInt(result.rows[0].count) > 0;
    } catch (error) {
      console.error('Erro ao verificar existência de configurações de SEO:', error);
      return false;
    }
  }

  /**
   * Normaliza um caminho
   */
  private normalizePath(path: string): string {
    // Remover barra no início se existir
    let normalizedPath = path.startsWith('/') ? path.substring(1) : path;

    // Remover barra no final se existir
    normalizedPath = normalizedPath.endsWith('/') ? normalizedPath.slice(0, -1) : normalizedPath;

    return normalizedPath;
  }
}
