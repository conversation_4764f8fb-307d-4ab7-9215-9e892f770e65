/**
 * Get Customer Fiscal Documents Use Case
 *
 * Caso de uso para obter documentos fiscais de um cliente específico.
 * Parte da implementação da tarefa 8.7.3 - Portal do cliente para documentos fiscais
 */

import { FiscalDocumentStatus, FiscalDocumentType } from '../../entities/FiscalDocument';
import {
  FiscalDocumentPaginationOptions,
  FiscalDocumentRepository,
  PaginatedFiscalDocuments,
} from '../../repositories/FiscalDocumentRepository';

export interface GetCustomerFiscalDocumentsRequest {
  customerId: string;
  type?: FiscalDocumentType | FiscalDocumentType[];
  status?: FiscalDocumentStatus | FiscalDocumentStatus[];
  startDate?: Date;
  endDate?: Date;
  pagination?: {
    page: number;
    limit: number;
  };
}

export interface GetCustomerFiscalDocumentsResponse {
  success: boolean;
  data?: PaginatedFiscalDocuments;
  error?: string;
}

export class GetCustomerFiscalDocumentsUseCase {
  constructor(private fiscalDocumentRepository: FiscalDocumentRepository) {}

  async execute(
    request: GetCustomerFiscalDocumentsRequest
  ): Promise<GetCustomerFiscalDocumentsResponse> {
    try {
      // Validar os dados de entrada
      if (!request.customerId) {
        return {
          success: false,
          error: 'ID do cliente é obrigatório.',
        };
      }

      if (request.pagination && (request.pagination.page < 1 || request.pagination.limit < 1)) {
        return {
          success: false,
          error: 'Parâmetros de paginação inválidos.',
        };
      }

      // Construir filtro
      const filter: any = {
        customerDocumentNumber: request.customerId,
      };

      if (request.type) {
        filter.type = request.type;
      }

      if (request.status) {
        filter.status = request.status;
      } else {
        // Por padrão, mostrar apenas documentos emitidos ou cancelados
        filter.status = ['ISSUED', 'CANCELLED'];
      }

      if (request.startDate) {
        filter.startDate = request.startDate;
      }

      if (request.endDate) {
        filter.endDate = request.endDate;
      }

      // Mapear paginação
      const pagination: FiscalDocumentPaginationOptions | undefined = request.pagination
        ? {
            page: request.pagination.page,
            limit: request.pagination.limit,
          }
        : undefined;

      // Buscar documentos fiscais
      const result = await this.fiscalDocumentRepository.find(
        filter,
        { field: 'issueDate', direction: 'desc' },
        pagination
      );

      return {
        success: true,
        data: result,
      };
    } catch (error) {
      console.error('Erro ao obter documentos fiscais do cliente:', error);

      return {
        success: false,
        error:
          error instanceof Error
            ? error.message
            : 'Erro desconhecido ao obter documentos fiscais do cliente.',
      };
    }
  }
}
