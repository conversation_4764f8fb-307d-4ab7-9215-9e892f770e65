---
import { PageTransition } from '../../../components/transitions';
import DaisyButton from '../../../components/ui/DaisyButton.astro';
import DaisyCard from '../../../components/ui/DaisyCard.astro';
import DaisyPagination from '../../../components/ui/DaisyPagination.astro';
import DaisyTable from '../../../components/ui/DaisyTable.astro';
import Breadcrumbs from '../../../components/ui/navigation/Breadcrumbs.astro';
/**
 * Página de Gerenciamento de Documentos
 *
 * Interface para gerenciar documentos PDF no sistema.
 * Parte da implementação da tarefa 8.3.1 - Armazenamento de PDFs
 */
import BaseLayout from '../../../layouts/BaseLayout.astro';
import { Container, Section } from '../../../layouts/grid';
import { formatFileSize } from '../../../utils/fileUtils';

// T<PERSON><PERSON>lo da página
const title = 'Gerenciamento de Documentos';

// Breadcrumbs para a página atual
const breadcrumbItems = [
  { href: '/', label: 'Início' },
  { href: '/admin', label: 'Administração' },
  { label: 'Documentos' },
];

// Obter parâmetros de consulta
const page = Astro.url.searchParams.get('page')
  ? Number.parseInt(Astro.url.searchParams.get('page') as string)
  : 1;
const limit = Astro.url.searchParams.get('limit')
  ? Number.parseInt(Astro.url.searchParams.get('limit') as string)
  : 10;
const search = Astro.url.searchParams.get('search') || '';
const category = Astro.url.searchParams.get('category') || '';
const sortField = Astro.url.searchParams.get('sort') || 'updatedAt';
const sortDirection = Astro.url.searchParams.get('dir') || 'desc';

// Buscar documentos do repositório
// Em um cenário real, isso seria feito através de um controlador/serviço
// Por enquanto, usaremos dados de exemplo
const documents = [
  {
    id: 'doc-001',
    title: 'Guia de Alfabetização',
    description: 'Guia completo para o processo de alfabetização',
    category: 'Alfabetização',
    fileSize: 2500000,
    pageCount: 45,
    version: 2,
    createdAt: new Date('2023-01-15'),
    updatedAt: new Date('2023-03-20'),
    isPublic: true,
  },
  {
    id: 'doc-002',
    title: 'Atividades de Matemática',
    description: 'Conjunto de atividades para ensino de matemática básica',
    category: 'Matemática',
    fileSize: 1800000,
    pageCount: 32,
    version: 1,
    createdAt: new Date('2023-02-10'),
    updatedAt: new Date('2023-02-10'),
    isPublic: false,
  },
  {
    id: 'doc-003',
    title: 'Cartilha de Português',
    description: 'Cartilha para ensino de português para crianças',
    category: 'Português',
    fileSize: 3200000,
    pageCount: 60,
    version: 3,
    createdAt: new Date('2022-11-05'),
    updatedAt: new Date('2023-04-15'),
    isPublic: true,
  },
  {
    id: 'doc-004',
    title: 'Manual do Professor',
    description: 'Manual com orientações para professores',
    category: 'Geral',
    fileSize: 4500000,
    pageCount: 85,
    version: 1,
    createdAt: new Date('2023-03-01'),
    updatedAt: new Date('2023-03-01'),
    isPublic: false,
  },
  {
    id: 'doc-005',
    title: 'Atividades de Ciências',
    description: 'Conjunto de atividades para ensino de ciências',
    category: 'Ciências',
    fileSize: 2100000,
    pageCount: 38,
    version: 2,
    createdAt: new Date('2023-01-20'),
    updatedAt: new Date('2023-02-28'),
    isPublic: true,
  },
];

// Categorias disponíveis
const categories = [
  { value: '', label: 'Todas as categorias' },
  { value: 'Alfabetização', label: 'Alfabetização' },
  { value: 'Matemática', label: 'Matemática' },
  { value: 'Português', label: 'Português' },
  { value: 'Ciências', label: 'Ciências' },
  { value: 'Geral', label: 'Geral' },
];

// Total de documentos (para paginação)
const totalDocuments = 15;
const totalPages = Math.ceil(totalDocuments / limit);
---

<BaseLayout title={title} enableViewTransitions={true} transitionType="fade">
  <Container>
    <PageTransition type="fade" duration="0.5s">
      <Section>
        <Breadcrumbs items={breadcrumbItems} class="mb-6" />
        
        <div class="flex justify-between items-center mb-6">
          <h1 class="text-3xl font-bold">{title}</h1>
          <DaisyButton 
            href="/admin/documents/upload" 
            variant="primary" 
            icon="upload"
            text="Novo Documento"
          />
        </div>
        
        <DaisyCard class="mb-8">
          <div class="p-4">
            <h2 class="text-xl font-bold mb-4">Filtros</h2>
            
            <form class="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div class="form-control">
                <label class="label">
                  <span class="label-text">Buscar</span>
                </label>
                <input 
                  type="text" 
                  name="search" 
                  placeholder="Título ou descrição..." 
                  class="input input-bordered w-full" 
                  value={search}
                />
              </div>
              
              <div class="form-control">
                <label class="label">
                  <span class="label-text">Categoria</span>
                </label>
                <select name="category" class="select select-bordered w-full">
                  {categories.map(cat => (
                    <option value={cat.value} selected={cat.value === category}>{cat.label}</option>
                  ))}
                </select>
              </div>
              
              <div class="form-control mt-8">
                <button type="submit" class="btn btn-primary">
                  <i class="icon icon-search mr-2"></i>
                  Filtrar
                </button>
              </div>
            </form>
          </div>
        </DaisyCard>
        
        <DaisyCard>
          <div class="overflow-x-auto">
            <DaisyTable>
              <thead>
                <tr>
                  <th>Título</th>
                  <th>Categoria</th>
                  <th>Tamanho</th>
                  <th>Páginas</th>
                  <th>Versão</th>
                  <th>Atualizado</th>
                  <th>Público</th>
                  <th>Ações</th>
                </tr>
              </thead>
              <tbody>
                {documents.map(doc => (
                  <tr>
                    <td>
                      <div class="font-bold">{doc.title}</div>
                      <div class="text-sm opacity-50">{doc.description}</div>
                    </td>
                    <td>{doc.category}</td>
                    <td>{formatFileSize(doc.fileSize)}</td>
                    <td>{doc.pageCount}</td>
                    <td>v{doc.version}</td>
                    <td>{doc.updatedAt.toLocaleDateString()}</td>
                    <td>
                      <div class={`badge ${doc.isPublic ? 'badge-success' : 'badge-ghost'}`}>
                        {doc.isPublic ? 'Sim' : 'Não'}
                      </div>
                    </td>
                    <td>
                      <div class="flex space-x-2">
                        <a href={`/admin/documents/view/${doc.id}`} class="btn btn-sm btn-ghost btn-square">
                          <i class="icon icon-eye"></i>
                        </a>
                        <a href={`/admin/documents/edit/${doc.id}`} class="btn btn-sm btn-ghost btn-square">
                          <i class="icon icon-edit"></i>
                        </a>
                        <button class="btn btn-sm btn-ghost btn-square text-error">
                          <i class="icon icon-trash"></i>
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </DaisyTable>
          </div>
          
          <div class="flex justify-between items-center p-4">
            <div class="text-sm text-gray-500">
              Mostrando {(page - 1) * limit + 1} a {Math.min(page * limit, totalDocuments)} de {totalDocuments} documentos
            </div>
            
            <DaisyPagination 
              currentPage={page} 
              totalPages={totalPages} 
              baseUrl="/admin/documents" 
              queryParams={{ search, category, sort: sortField, dir: sortDirection }}
            />
          </div>
        </DaisyCard>
      </Section>
    </PageTransition>
  </Container>
</BaseLayout>

<script>
  // Script para confirmar exclusão de documentos
  document.addEventListener('DOMContentLoaded', () => {
    const deleteButtons = document.querySelectorAll('.icon-trash');
    
    deleteButtons.forEach(button => {
      button.addEventListener('click', (e) => {
        if (confirm('Tem certeza que deseja excluir este documento?')) {
          // Implementar lógica de exclusão
          console.log('Documento excluído');
        }
      });
    });
  });
</script>
