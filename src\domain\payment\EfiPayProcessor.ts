/**
 * Processador de pagamento para Efí Pay (antigo Gerencianet)
 *
 * Esta classe implementa o processador de pagamento para a plataforma Efí Pay.
 */

import { efiPayService } from '../../services/efiPayService';
import { logger } from '../../utils/logger';
import {
  PaymentData,
  PaymentResult,
  PaymentStatus,
  PaymentStatusQuery,
  RefundRequest,
  RefundResult,
} from '../interfaces/PaymentProcessor';
import { BasePaymentProcessor } from './BasePaymentProcessor';

// Interface para requisição do Efí Pay
interface EfiPayRequest {
  items: Array<{
    name: string;
    value: number;
    amount: number;
  }>;
  customer: {
    name: string;
    email: string;
    cpf: string;
    phone_number?: string;
  };
  metadata: Record<string, unknown>;
}

/**
 * Processador de pagamento para Efí Pay
 */
export class EfiPayProcessor extends BasePaymentProcessor {
  /**
   * Cria uma nova instância de EfiPayProcessor
   */
  constructor() {
    super('EfiPay');
  }

  /**
   * Implementação interna do processamento de pagamento
   * @param data - Dados do pagamento
   * @returns Resultado do pagamento
   */
  protected async processPaymentInternal(data: PaymentData): Promise<PaymentResult> {
    try {
      // Converter dados para o formato do Efí Pay
      const efiPayData = this.convertToEfiPayFormat(data);

      // Chamar API do Efí Pay
      const response = await efiPayService.createCharge(efiPayData);

      // Converter resposta para o formato padrão
      return {
        id: data.id,
        externalId: response.charge_id.toString(),
        status: this.mapEfiPayStatus(response.status),
        amount: data.amount,
        currency: data.currency,
        redirectUrl: response.payment_url,
        rawData: response,
        createdAt: new Date(),
        updatedAt: new Date(),
      };
    } catch (error) {
      logger.error('Erro ao processar pagamento no Efí Pay', {
        paymentId: data.id,
        error: error instanceof Error ? error.message : String(error),
      });

      throw error;
    }
  }

  /**
   * Implementação interna da consulta de status de pagamento
   * @param query - Dados para consulta
   * @returns Resultado do pagamento atualizado
   */
  protected async checkPaymentStatusInternal(query: PaymentStatusQuery): Promise<PaymentResult> {
    try {
      // Chamar API do Efí Pay
      const response = await efiPayService.getCharge(query.externalId);

      // Converter resposta para o formato padrão
      return {
        id: query.id,
        externalId: query.externalId,
        status: this.mapEfiPayStatus(response.status),
        amount: response.total / 100, // Efí Pay retorna em centavos
        currency: 'BRL',
        rawData: response,
        createdAt: new Date(response.created_at),
        updatedAt: new Date(response.updated_at || response.created_at),
      };
    } catch (error) {
      logger.error('Erro ao consultar status de pagamento no Efí Pay', {
        paymentId: query.id,
        externalId: query.externalId,
        error: error instanceof Error ? error.message : String(error),
      });

      throw error;
    }
  }

  /**
   * Implementação interna do processamento de reembolso
   * @param request - Dados do reembolso
   * @returns Resultado do reembolso
   */
  protected async processRefundInternal(request: RefundRequest): Promise<RefundResult> {
    try {
      // Chamar API do Efí Pay
      const response = await efiPayService.createRefund(request.externalId, request.amount);

      // Converter resposta para o formato padrão
      return {
        id: response.refund_id.toString(),
        externalId: response.refund_id.toString(),
        paymentId: request.paymentId,
        status: response.status === 'refunded' ? 'completed' : 'pending',
        amount: response.value / 100, // Efí Pay retorna em centavos
        currency: 'BRL',
        rawData: response,
        createdAt: new Date(response.refund_date),
      };
    } catch (error) {
      logger.error('Erro ao processar reembolso no Efí Pay', {
        paymentId: request.paymentId,
        externalId: request.externalId,
        error: error instanceof Error ? error.message : String(error),
      });

      throw error;
    }
  }

  /**
   * Validações específicas do processador
   * @param data - Dados do pagamento
   * @throws Error se os dados são inválidos
   */
  protected validatePaymentDataInternal(data: PaymentData): void {
    // Validações específicas para o Efí Pay
    if (data.currency !== 'BRL') {
      throw new Error('Efí Pay só aceita pagamentos em BRL');
    }

    if (!data.customer.document) {
      throw new Error('CPF/CNPJ do cliente é obrigatório para o Efí Pay');
    }
  }

  /**
   * Converte dados de pagamento para o formato do Efí Pay
   * @param data - Dados do pagamento
   * @returns Dados no formato do Efí Pay
   */
  private convertToEfiPayFormat(data: PaymentData): EfiPayRequest {
    // Converter para o formato esperado pelo Efí Pay
    return {
      items: [
        {
          name: data.description || `Pedido #${data.orderId}`,
          value: Math.round(data.amount * 100), // Efí Pay espera em centavos
          amount: 1,
        },
      ],
      customer: {
        name: data.customer.name,
        email: data.customer.email,
        cpf: data.customer.document,
        phone_number: data.metadata?.phoneNumber as string,
      },
      metadata: {
        order_id: data.orderId,
        payment_id: data.id,
        ...data.metadata,
      },
    };
  }

  /**
   * Mapeia status do Efí Pay para o formato padrão
   * @param efiPayStatus - Status no formato do Efí Pay
   * @returns Status no formato padrão
   */
  private mapEfiPayStatus(efiPayStatus: string): PaymentStatus {
    switch (efiPayStatus) {
      case 'new':
      case 'waiting':
        return PaymentStatus.PENDING;
      case 'in_progress':
        return PaymentStatus.PROCESSING;
      case 'paid':
      case 'settled':
        return PaymentStatus.COMPLETED;
      case 'canceled':
        return PaymentStatus.CANCELLED;
      case 'refunded':
        return PaymentStatus.REFUNDED;
      default:
        return PaymentStatus.FAILED;
    }
  }
}
