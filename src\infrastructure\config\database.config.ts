/**
 * Database configuration
 */
interface DatabaseConfig {
  host: string;
  port: number;
  database: string;
  user: string;
  password: string;
  ssl: boolean;
  maxConnections: number;
  idleTimeoutMillis: number;
}

/**
 * Environment-specific database configurations
 */
const configs: Record<string, DatabaseConfig> = {
  development: {
    host: 'localhost',
    port: 5432,
    database: 'estacao_alfabetizacao_dev',
    user: 'postgres',
    password: 'postgres',
    ssl: false,
    maxConnections: 10,
    idleTimeoutMillis: 30000,
  },
  test: {
    host: 'localhost',
    port: 5432,
    database: 'estacao_alfabetizacao_test',
    user: 'postgres',
    password: 'postgres',
    ssl: false,
    maxConnections: 5,
    idleTimeoutMillis: 10000,
  },
  production: {
    host: process.env.DB_HOST || 'localhost',
    port: Number.parseInt(process.env.DB_PORT || '5432', 10),
    database: process.env.DB_NAME || 'estacao_alfabetizacao',
    user: process.env.DB_USER || 'postgres',
    password: process.env.DB_PASSWORD || '',
    ssl: process.env.DB_SSL === 'true',
    maxConnections: Number.parseInt(process.env.DB_MAX_CONNECTIONS || '20', 10),
    idleTimeoutMillis: Number.parseInt(process.env.DB_IDLE_TIMEOUT || '30000', 10),
  },
};

/**
 * Get the database configuration for the current environment
 * @returns Database configuration
 */
export function getDatabaseConfig(): DatabaseConfig {
  const env = process.env.NODE_ENV || 'development';
  return configs[env] || configs.development;
}

/**
 * Get the connection string for the current environment
 * @returns Database connection string
 */
export function getConnectionString(): string {
  const config = getDatabaseConfig();
  const sslParam = config.ssl ? '?sslmode=require' : '';
  return `postgresql://${config.user}:${config.password}@${config.host}:${config.port}/${config.database}${sslParam}`;
}
