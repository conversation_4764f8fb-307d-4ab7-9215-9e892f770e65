---
import CartProducts from '../components/data/CartProducts.astro';
import MainHeader from '../components/layout/MainHeader.astro';
import BaseLayout from './BaseLayout.astro';

export interface Props {
  url?: string;
}

const { url } = Astro.props;
---

<script>  
  const cartStepElement         = document.getElementById('cartStep')         as HTMLLIElement;
  const paymentStepElement      = document.getElementById('paymentStep')      as HTMLLIElement;
  const confirmationStepElement = document.getElementById('confirmationStep') as HTMLLIElement;
  const finishedStepElement     = document.getElementById('finishedStep')     as HTMLLIElement;

  let cartStep        : boolean = true;
  let paymentStep     : boolean = true;
  let confirmationStep: boolean = true;
  let finishedStep    : boolean = true;

  cartStepElement.classList.remove('step-success');
  paymentStepElement.classList.remove('step-success');
  confirmationStepElement.classList.remove('step-success');
  finishedStepElement.classList.remove('step-success');

  if (cartStep)         { cartStepElement.classList.add('step-success')        ; }
  if (paymentStep)      { paymentStepElement.classList.add('step-success')     ; }
  if (confirmationStep) { confirmationStepElement.classList.add('step-success'); }
  if (finishedStep)     { finishedStepElement.classList.add('step-success')    ; }
</script>

<BaseLayout title="Pagamento">
  <MainHeader />
  <div class="l-grid-adds">
    <div class="l-grid-area-status c-shadowed">
      <ul class="steps steps-adds c-text-shadow-payment">
        <li id="cartStep"         class="step">Carrinho</li>
        <li id="paymentStep"      class="step">Pagamento</li>
        <li id="confirmationStep" class="step">Confirmação</li>
        <li id="finishedStep"     class="step">Finalizado</li>
      </ul>
    </div>
    <div class="l-grid-area-gap" />
    <div class="l-grid-area-products">
      <CartProducts />      
    </div>
    <div class="l-grid-area-payment">
      
    </div>
  </div>
  
</BaseLayout>

<style>
  h1 {
    background-color: var(--color-primary-100);
    font-family: var(--font-name);
    font-size  : 1.5rem;
    font-weight: bold;
    padding    : 8px;
    text-align : center;
  }

  li {
    /* color: var(--color-success-100); */
  }

  .l-grid-adds {
    /* background-color     : var(--color-secondary-100); */
    display              : grid;
    gap                  : 4px;
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows   : repeat(3, 1fr);
    grid-template-areas  :
      "status   status" 
      "gap      gap"
      "products products";
    padding              : 8px;
    position             : relative;
    top                  : var(--header-height);
  }

  .l-grid-adds > div {
    /* margin: 8px; */
  }

  .l-grid-area-gap {
    grid-area: gap;
  }

  .l-grid-area-payment {
    border       : 2px solid var(--color-primary-100);
    border-radius: 8px;
    grid-area    : payment;
  }

  .l-grid-area-products {
    border       : 2px solid var(--color-primary-100);
    border-radius: 8px;
    grid-area    : products;
  }

  .l-grid-area-status {
    border-radius: 8px;
    grid-area    : status;
  }

  .l-title {
    background-color: var(--color-primary-100);
    border          : 2px solid var(--color-secondary-100);
    border-radius   : 8px;
    padding         : 4px;
  }

  .steps-adds {
    background-color: var(--color-error-100);
    border          : 1px solid var(--color-primary-100);
    border-radius   : 8px;
    font-family     : var(--font-name);
    font-weight     : bold;
    padding         : 8px;
    width           : 100%;
    z-index         : 1;
  }
</style>