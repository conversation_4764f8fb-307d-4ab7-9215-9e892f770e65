/**
 * API para gerenciar sessões de usuário
 */

import { AuditEventType, AuditSeverity, auditService } from '@services/auditService';
import { sessionService } from '@services/sessionService';
import { getCurrentUser } from '@utils/authUtils';
import { logger } from '@utils/logger';
import type { APIRoute } from 'astro';

export const DELETE: APIRoute = async ({ params, cookies, request }) => {
  try {
    // Verificar autenticação
    const user = await getCurrentUser(cookies);

    if (!user) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Não autorizado',
        }),
        {
          status: 401,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    // Obter ID da sessão a ser encerrada
    const { id } = params;

    if (!id) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'ID da sessão não fornecido',
        }),
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    // Obter ID da sessão atual
    const currentSessionId = cookies.get('session_id')?.value;

    // Não permitir encerrar a sessão atual por esta API
    if (id === currentSessionId) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Não é possível encerrar a sessão atual por esta API',
        }),
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    // Obter sessão para verificar se pertence ao usuário
    const session = await sessionService.get(id);

    if (!session) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Sessão não encontrada',
        }),
        {
          status: 404,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    // Verificar se a sessão pertence ao usuário
    if (session.userId !== user.ulid_user) {
      // Registrar tentativa de acesso não autorizado
      await auditService.logEvent({
        eventType: AuditEventType.PERMISSION_DENIED,
        userId: user.ulid_user,
        userName: user.name,
        ipAddress:
          request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown',
        resource: 'session',
        resourceId: id,
        action: 'delete',
        result: 'failure',
        severity: AuditSeverity.WARNING,
        metadata: {
          reason: 'Tentativa de encerrar sessão de outro usuário',
          sessionUserId: session.userId,
        },
      });

      return new Response(
        JSON.stringify({
          success: false,
          error: 'Não autorizado a encerrar esta sessão',
        }),
        {
          status: 403,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    // Encerrar sessão
    const result = await sessionService.delete(id);

    if (!result) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Erro ao encerrar sessão',
        }),
        {
          status: 500,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    // Registrar ação no log de auditoria
    await auditService.logEvent({
      eventType: AuditEventType.SYSTEM_CONFIG_CHANGED,
      userId: user.ulid_user,
      userName: user.name,
      ipAddress:
        request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',
      userAgent: request.headers.get('user-agent') || 'unknown',
      resource: 'session',
      resourceId: id,
      action: 'delete',
      result: 'success',
      severity: AuditSeverity.INFO,
      metadata: {
        sessionCreatedAt: new Date(session.createdAt).toISOString(),
        sessionLastActivity: new Date(session.lastActivity).toISOString(),
        sessionIpAddress: session.ipAddress,
      },
    });

    return new Response(
      JSON.stringify({
        success: true,
        message: 'Sessão encerrada com sucesso',
      }),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  } catch (error) {
    logger.error('Erro ao encerrar sessão:', error);

    return new Response(
      JSON.stringify({
        success: false,
        error: 'Erro interno do servidor',
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  }
};
