---
import { PageTransition } from '../../../components/transitions';
import DaisyButton from '../../../components/ui/DaisyButton.astro';
import DaisyCard from '../../../components/ui/DaisyCard.astro';
import Breadcrumbs from '../../../components/ui/navigation/Breadcrumbs.astro';
import { GetContactMessageStatsUseCase } from '../../../domain/usecases/contact/GetContactMessageStatsUseCase';
import { PostgresContactMessageRepository } from '../../../infrastructure/database/repositories/PostgresContactMessageRepository';
/**
 * Página de Estatísticas de Mensagens
 *
 * Interface para visualizar estatísticas de mensagens de contato.
 * Parte da implementação da tarefa 8.6.2 - Gestão de mensagens
 */
import BaseLayout from '../../../layouts/BaseLayout.astro';
import { Container, Section } from '../../../layouts/grid';

// Título da página
const title = 'Estatísticas de Mensagens';

// Breadcrumbs para a página atual
const breadcrumbItems = [
  { href: '/', label: 'Início' },
  { href: '/admin', label: 'Administração' },
  { href: '/admin/mensagens', label: 'Mensagens' },
  { label: 'Estatísticas' },
];

// Inicializar repositório e caso de uso
const contactMessageRepository = new PostgresContactMessageRepository();
const getContactMessageStatsUseCase = new GetContactMessageStatsUseCase(contactMessageRepository);

// Obter estatísticas
const statsResult = await getContactMessageStatsUseCase.execute();
const stats = statsResult.success
  ? statsResult.data
  : {
      total: 0,
      pending: 0,
      read: 0,
      replied: 0,
      archived: 0,
      spam: 0,
      avgResponseTime: 0,
      categoryDistribution: {},
    };

// Categorias disponíveis
const categories = [
  { value: 'general', label: 'Geral' },
  { value: 'support', label: 'Suporte' },
  { value: 'sales', label: 'Vendas' },
  { value: 'billing', label: 'Faturamento' },
  { value: 'technical', label: 'Técnico' },
  { value: 'partnership', label: 'Parcerias' },
  { value: 'feedback', label: 'Feedback' },
  { value: 'other', label: 'Outro' },
];

// Função para formatar número
const formatNumber = (num: number): string => {
  return num.toLocaleString('pt-BR');
};

// Função para obter cor da categoria
const getCategoryColor = (category: string): string => {
  const colors = {
    general: '#4B5563',
    support: '#3B82F6',
    sales: '#10B981',
    billing: '#F59E0B',
    technical: '#6366F1',
    partnership: '#EC4899',
    feedback: '#8B5CF6',
    other: '#9CA3AF',
  };

  return colors[category as keyof typeof colors] || '#9CA3AF';
};

// Calcular porcentagens para o gráfico de status
const totalMessages = stats?.total || 0;
const statusPercentages = {
  pending: totalMessages > 0 ? ((stats?.pending || 0) / totalMessages) * 100 : 0,
  read: totalMessages > 0 ? ((stats?.read || 0) / totalMessages) * 100 : 0,
  replied: totalMessages > 0 ? ((stats?.replied || 0) / totalMessages) * 100 : 0,
  archived: totalMessages > 0 ? ((stats?.archived || 0) / totalMessages) * 100 : 0,
  spam: totalMessages > 0 ? ((stats?.spam || 0) / totalMessages) * 100 : 0,
};

// Preparar dados para o gráfico de categorias
const categoryData = categories.map((category) => {
  const count =
    stats?.categoryDistribution?.[category.value as keyof typeof stats.categoryDistribution] || 0;
  const percentage = totalMessages > 0 ? (count / totalMessages) * 100 : 0;

  return {
    ...category,
    count,
    percentage,
    color: getCategoryColor(category.value),
  };
});
---

<BaseLayout title={title} enableViewTransitions={true} transitionType="fade">
  <Container>
    <PageTransition type="fade" duration="0.5s">
      <Section>
        <Breadcrumbs items={breadcrumbItems} class="mb-6" />
        
        <div class="flex justify-between items-center mb-6">
          <h1 class="text-3xl font-bold">{title}</h1>
          
          <div class="flex gap-2">
            <DaisyButton 
              href="/admin/mensagens" 
              variant="outline" 
              icon="arrow-left"
              text="Voltar"
            />
            
            <DaisyButton 
              href="/admin/mensagens/estatisticas" 
              variant="outline" 
              icon="refresh-cw"
              text="Atualizar"
            />
          </div>
        </div>
        
        <div class="stats shadow mb-6 w-full">
          <div class="stat">
            <div class="stat-figure text-primary">
              <i class="icon icon-mail text-3xl"></i>
            </div>
            <div class="stat-title">Total</div>
            <div class="stat-value">{formatNumber(stats?.total || 0)}</div>
          </div>
          
          <div class="stat">
            <div class="stat-figure text-warning">
              <i class="icon icon-clock text-3xl"></i>
            </div>
            <div class="stat-title">Pendentes</div>
            <div class="stat-value">{formatNumber(stats?.pending || 0)}</div>
            <div class="stat-desc">
              {statusPercentages.pending.toFixed(1)}% do total
            </div>
          </div>
          
          <div class="stat">
            <div class="stat-figure text-success">
              <i class="icon icon-check-circle text-3xl"></i>
            </div>
            <div class="stat-title">Respondidas</div>
            <div class="stat-value">{formatNumber(stats?.replied || 0)}</div>
            <div class="stat-desc">
              {statusPercentages.replied.toFixed(1)}% do total
            </div>
          </div>
          
          <div class="stat">
            <div class="stat-figure text-info">
              <i class="icon icon-clock text-3xl"></i>
            </div>
            <div class="stat-title">Tempo médio de resposta</div>
            <div class="stat-value">{(stats?.avgResponseTime || 0).toFixed(1)}h</div>
          </div>
        </div>
        
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
          <DaisyCard>
            <div class="p-6">
              <h2 class="text-xl font-bold mb-4">Distribuição por Status</h2>
              
              <div class="space-y-4">
                <div class="flex items-center">
                  <div class="w-32">Pendentes</div>
                  <div class="flex-1">
                    <div class="w-full bg-gray-200 rounded-full h-4">
                      <div 
                        class="bg-warning h-4 rounded-full" 
                        style={`width: ${statusPercentages.pending}%`}
                      ></div>
                    </div>
                  </div>
                  <div class="w-20 text-right">
                    {formatNumber(stats?.pending || 0)} ({statusPercentages.pending.toFixed(1)}%)
                  </div>
                </div>
                
                <div class="flex items-center">
                  <div class="w-32">Lidas</div>
                  <div class="flex-1">
                    <div class="w-full bg-gray-200 rounded-full h-4">
                      <div 
                        class="bg-info h-4 rounded-full" 
                        style={`width: ${statusPercentages.read}%`}
                      ></div>
                    </div>
                  </div>
                  <div class="w-20 text-right">
                    {formatNumber(stats?.read || 0)} ({statusPercentages.read.toFixed(1)}%)
                  </div>
                </div>
                
                <div class="flex items-center">
                  <div class="w-32">Respondidas</div>
                  <div class="flex-1">
                    <div class="w-full bg-gray-200 rounded-full h-4">
                      <div 
                        class="bg-success h-4 rounded-full" 
                        style={`width: ${statusPercentages.replied}%`}
                      ></div>
                    </div>
                  </div>
                  <div class="w-20 text-right">
                    {formatNumber(stats?.replied || 0)} ({statusPercentages.replied.toFixed(1)}%)
                  </div>
                </div>
                
                <div class="flex items-center">
                  <div class="w-32">Arquivadas</div>
                  <div class="flex-1">
                    <div class="w-full bg-gray-200 rounded-full h-4">
                      <div 
                        class="bg-neutral h-4 rounded-full" 
                        style={`width: ${statusPercentages.archived}%`}
                      ></div>
                    </div>
                  </div>
                  <div class="w-20 text-right">
                    {formatNumber(stats?.archived || 0)} ({statusPercentages.archived.toFixed(1)}%)
                  </div>
                </div>
                
                <div class="flex items-center">
                  <div class="w-32">Spam</div>
                  <div class="flex-1">
                    <div class="w-full bg-gray-200 rounded-full h-4">
                      <div 
                        class="bg-error h-4 rounded-full" 
                        style={`width: ${statusPercentages.spam}%`}
                      ></div>
                    </div>
                  </div>
                  <div class="w-20 text-right">
                    {formatNumber(stats?.spam || 0)} ({statusPercentages.spam.toFixed(1)}%)
                  </div>
                </div>
              </div>
            </div>
          </DaisyCard>
          
          <DaisyCard>
            <div class="p-6">
              <h2 class="text-xl font-bold mb-4">Distribuição por Categoria</h2>
              
              <div class="space-y-4">
                {categoryData.map(category => (
                  <div class="flex items-center">
                    <div class="w-32">{category.label}</div>
                    <div class="flex-1">
                      <div class="w-full bg-gray-200 rounded-full h-4">
                        <div 
                          class="h-4 rounded-full" 
                          style={`width: ${category.percentage}%; background-color: ${category.color}`}
                        ></div>
                      </div>
                    </div>
                    <div class="w-20 text-right">
                      {formatNumber(category.count)} ({category.percentage.toFixed(1)}%)
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </DaisyCard>
        </div>
        
        <DaisyCard>
          <div class="p-6">
            <h2 class="text-xl font-bold mb-4">Tempo de Resposta</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div class="stat bg-base-200 rounded-lg">
                <div class="stat-title">Tempo médio</div>
                <div class="stat-value">{(stats?.avgResponseTime || 0).toFixed(1)}h</div>
                <div class="stat-desc">Média de todas as mensagens respondidas</div>
              </div>
              
              <div class="stat bg-base-200 rounded-lg">
                <div class="stat-title">Taxa de resposta</div>
                <div class="stat-value">
                  {totalMessages > 0 ? ((stats?.replied || 0) / totalMessages * 100).toFixed(1) : 0}%
                </div>
                <div class="stat-desc">Mensagens respondidas / total</div>
              </div>
              
              <div class="stat bg-base-200 rounded-lg">
                <div class="stat-title">Pendentes</div>
                <div class="stat-value">{formatNumber(stats?.pending || 0)}</div>
                <div class="stat-desc">Mensagens aguardando resposta</div>
              </div>
            </div>
          </div>
        </DaisyCard>
      </Section>
    </PageTransition>
  </Container>
</BaseLayout>

<script>
  // Script para funcionalidade da página de estatísticas
  document.addEventListener('DOMContentLoaded', () => {
    // Aqui poderia ser adicionada lógica para gráficos interativos
    // usando bibliotecas como Chart.js ou D3.js
  });
</script>
