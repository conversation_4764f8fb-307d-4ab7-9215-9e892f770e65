---
/**
 * Componente de Flex
 *
 * Este componente cria um layout flexbox responsivo.
 */

interface Props {
  direction?: 'row' | 'row-reverse' | 'col' | 'col-reverse';
  wrap?: 'wrap' | 'wrap-reverse' | 'nowrap';
  justify?: 'start' | 'center' | 'end' | 'between' | 'around' | 'evenly';
  items?: 'start' | 'center' | 'end' | 'stretch' | 'baseline';
  content?: 'start' | 'center' | 'end' | 'between' | 'around' | 'evenly';
  gap?: number;
  inline?: boolean;
  class?: string;
  id?: string;
}

const {
  direction = 'row',
  wrap = 'wrap',
  justify = 'start',
  items = 'start',
  content,
  gap,
  inline = false,
  class: className = '',
  id,
} = Astro.props;

// Gerar classes para o flex
const flexClass = inline ? 'inline-flex' : 'flex';
const directionClass = `flex-${direction}`;
const wrapClass = `flex-${wrap}`;
const justifyClass = `justify-${justify}`;
const itemsClass = `items-${items}`;
const contentClass = content ? `content-${content}` : '';
const gapClass = gap !== undefined ? `gap-${gap}` : '';

// Combinar todas as classes
const flexClasses = [
  flexClass,
  directionClass,
  wrapClass,
  justifyClass,
  itemsClass,
  contentClass,
  gapClass,
  className,
]
  .filter(Boolean)
  .join(' ');
---

<div class={flexClasses} id={id}>
  <slot />
</div>
