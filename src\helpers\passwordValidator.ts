// src/helpers/passwordValidator.ts
const MIN_LENGTH = 8;
const REQUIRED_PATTERNS = [
  { pattern: /[A-Z]/, message: 'uma letra maiúscula' },
  { pattern: /[a-z]/, message: 'uma letra minúscula' },
  { pattern: /[0-9]/, message: 'um número' },
  { pattern: /[!@#$%^&*]/, message: 'um caractere especial' },
];

export function validatePassword(password: string): {
  isValid: boolean;
  score: number;
  errors: string[];
} {
  const errors: string[] = [];
  let score = 0;

  // Verificar comprimento mínimo
  if (password.length < MIN_LENGTH) {
    errors.push(`A senha deve ter pelo menos ${MIN_LENGTH} caracteres`);
  } else {
    score += 2;
  }

  // Verificar padrões requeridos
  for (const { pattern, message } of REQUIRED_PATTERNS) {
    if (!pattern.test(password)) {
      errors.push(`A senha deve conter ${message}`);
    } else {
      score += 1;
    }
  }

  // Verificar repetições
  if (/(.)\1{2,}/.test(password)) {
    errors.push('A senha não deve conter caracteres repetidos em sequência');
    score -= 1;
  }

  // Verificar sequências comuns
  const commonSequences = ['123', 'abc', 'qwerty'];
  for (const seq of commonSequences) {
    if (password.toLowerCase().includes(seq)) {
      errors.push('A senha não deve conter sequências óbvias');
      score -= 1;
      break;
    }
  }

  return {
    isValid: errors.length === 0,
    score: Math.max(0, Math.min(10, score)),
    errors,
  };
}
