/**
 * Serviço de logging para o Kafka
 *
 * Este serviço implementa funcionalidades de logging específicas para o Kafka,
 * incluindo rotação de logs, níveis de log e armazenamento centralizado.
 */

import * as fs from 'fs';
import * as path from 'path';
import * as os from 'os';
import { nanoid } from 'nanoid';
import {
  kafkaLoggingConfig,
  KafkaLogLevel,
  KafkaLogDestination,
} from '../config/kafka-logging.config';
import { queryHelper } from '@db/queryHelper';
import { producer } from '../config/kafka';

// Interface para mensagem de log
interface KafkaLogMessage {
  id: string;
  timestamp: string;
  level: KafkaLogLevel;
  component: string;
  message: string;
  hostname?: string;
  processId?: number;
  metadata?: Record<string, any>;
}

// Classe para o serviço de logging do Kafka
class KafkaLoggingService {
  private static instance: KafkaLoggingService;
  private logBuffer: KafkaLogMessage[] = [];
  private fileStreams: Map<string, fs.WriteStream> = new Map();
  private flushTimer: NodeJS.Timeout | null = null;

  /**
   * Construtor privado para implementar Singleton
   */
  private constructor() {
    this.initializeLogDirectories();
    this.setupFlushTimer();
    process.on('exit', () => this.flushLogs());
  }

  /**
   * Obtém a instância única do serviço
   */
  public static getInstance(): KafkaLoggingService {
    if (!KafkaLoggingService.instance) {
      KafkaLoggingService.instance = new KafkaLoggingService();
    }
    return KafkaLoggingService.instance;
  }

  /**
   * Inicializa os diretórios de log
   */
  private initializeLogDirectories(): void {
    const fileDestinations = kafkaLoggingConfig.destinations.filter(
      dest => dest.type === KafkaLogDestination.FILE && dest.enabled
    );

    for (const dest of fileDestinations) {
      if (dest.config?.filePath) {
        const logDir = path.dirname(dest.config.filePath);
        if (!fs.existsSync(logDir)) {
          fs.mkdirSync(logDir, { recursive: true });
        }
      }
    }
  }

  /**
   * Configura o timer para flush periódico dos logs
   */
  private setupFlushTimer(): void {
    const dbDestination = kafkaLoggingConfig.destinations.find(
      dest => dest.type === KafkaLogDestination.DATABASE && dest.enabled
    );

    if (dbDestination && dbDestination.config?.flushInterval) {
      this.flushTimer = setInterval(() => {
        this.flushLogs();
      }, dbDestination.config.flushInterval);
    }
  }

  /**
   * Registra uma mensagem de log
   * @param level - Nível do log
   * @param component - Componente que gerou o log
   * @param message - Mensagem do log
   * @param metadata - Metadados adicionais
   */
  public log(
    level: KafkaLogLevel,
    component: string,
    message: string,
    metadata?: Record<string, any>
  ): void {
    // Verificar se o nível de log é suficiente
    if (!this.shouldLog(level, component)) {
      return;
    }

    // Criar mensagem de log
    const logMessage: KafkaLogMessage = {
      id: nanoid(),
      timestamp: new Date().toISOString(),
      level,
      component,
      message,
      metadata,
    };

    // Adicionar informações adicionais conforme configuração
    if (kafkaLoggingConfig.includeHostname) {
      logMessage.hostname = os.hostname();
    }

    if (kafkaLoggingConfig.includeProcessId) {
      logMessage.processId = process.pid;
    }

    // Processar log para cada destino
    this.processLogMessage(logMessage);
  }

  /**
   * Verifica se o log deve ser registrado com base no nível e componente
   * @param level - Nível do log
   * @param component - Componente que gerou o log
   * @returns Verdadeiro se o log deve ser registrado
   */
  private shouldLog(level: KafkaLogLevel, component: string): boolean {
    // Obter nível de log para o componente
    let componentLevel = kafkaLoggingConfig.defaultLevel;

    // Verificar se há configuração específica para o componente
    const componentConfig = component.split('.')[0].toLowerCase();
    if (componentConfig in kafkaLoggingConfig.components) {
      // @ts-ignore - Acessar propriedade dinamicamente
      componentLevel = kafkaLoggingConfig.components[componentConfig].level;
    }

    // Comparar níveis de log
    const levels = Object.values(KafkaLogLevel);
    return levels.indexOf(level) >= levels.indexOf(componentLevel);
  }

  /**
   * Processa uma mensagem de log para todos os destinos configurados
   * @param logMessage - Mensagem de log
   */
  private processLogMessage(logMessage: KafkaLogMessage): void {
    for (const destination of kafkaLoggingConfig.destinations) {
      if (!destination.enabled) {
        continue;
      }

      // Verificar nível de log para o destino
      const levels = Object.values(KafkaLogLevel);
      if (
        levels.indexOf(logMessage.level) < levels.indexOf(destination.level)
      ) {
        continue;
      }

      // Processar log de acordo com o tipo de destino
      switch (destination.type) {
        case KafkaLogDestination.CONSOLE:
          this.logToConsole(logMessage);
          break;
        case KafkaLogDestination.FILE:
          this.logToFile(
            logMessage,
            destination.config?.filePath || 'logs/kafka.log'
          );
          break;
        case KafkaLogDestination.DATABASE:
          this.bufferLogForDatabase(logMessage);
          break;
        case KafkaLogDestination.KAFKA:
          this.logToKafka(
            logMessage,
            destination.config?.topic || 'system.logs.kafka'
          );
          break;
      }
    }
  }

  /**
   * Registra uma mensagem no console
   * @param logMessage - Mensagem de log
   */
  private logToConsole(logMessage: KafkaLogMessage): void {
    const timestamp = kafkaLoggingConfig.includeTimestamp
      ? `[${logMessage.timestamp}] `
      : '';
    const levelColors = {
      [KafkaLogLevel.DEBUG]: '\x1b[34m', // Azul
      [KafkaLogLevel.INFO]: '\x1b[32m', // Verde
      [KafkaLogLevel.WARN]: '\x1b[33m', // Amarelo
      [KafkaLogLevel.ERROR]: '\x1b[31m', // Vermelho
      [KafkaLogLevel.FATAL]: '\x1b[35m', // Magenta
    };

    const colorCode = levelColors[logMessage.level] || '';
    const resetColor = '\x1b[0m';

    console.log(
      `${timestamp}${colorCode}[${logMessage.level}]${resetColor} [${logMessage.component}] ${logMessage.message}`,
      logMessage.metadata || ''
    );
  }

  /**
   * Registra uma mensagem em um arquivo
   * @param logMessage - Mensagem de log
   * @param filePath - Caminho do arquivo de log
   */
  private logToFile(logMessage: KafkaLogMessage, filePath: string): void {
    try {
      // Obter ou criar stream para o arquivo
      let stream = this.fileStreams.get(filePath);

      if (!stream) {
        stream = fs.createWriteStream(filePath, { flags: 'a' });
        this.fileStreams.set(filePath, stream);
      }

      // Formatar mensagem de log
      const logLine =
        JSON.stringify({
          timestamp: logMessage.timestamp,
          level: logMessage.level,
          component: logMessage.component,
          message: logMessage.message,
          hostname: logMessage.hostname,
          processId: logMessage.processId,
          metadata: logMessage.metadata,
        }) + '\n';

      // Escrever no arquivo
      stream.write(logLine);

      // Verificar rotação de logs
      this.checkLogRotation(filePath);
    } catch (error) {
      console.error('Erro ao escrever log em arquivo:', error);
    }
  }

  /**
   * Verifica se é necessário rotacionar o arquivo de log
   * @param filePath - Caminho do arquivo de log
   */
  private checkLogRotation(filePath: string): void {
    try {
      const fileDestination = kafkaLoggingConfig.destinations.find(
        dest =>
          dest.type === KafkaLogDestination.FILE &&
          dest.config?.filePath === filePath
      );

      if (!fileDestination || !fileDestination.config) {
        return;
      }

      const { maxSize, maxFiles } = fileDestination.config;

      if (!maxSize) {
        return;
      }

      // Verificar tamanho do arquivo
      const stats = fs.statSync(filePath);

      if (stats.size >= maxSize) {
        // Fechar stream atual
        const stream = this.fileStreams.get(filePath);
        if (stream) {
          stream.end();
          this.fileStreams.delete(filePath);
        }

        // Rotacionar arquivos
        this.rotateLogFiles(filePath, maxFiles || 5);
      }
    } catch (error) {
      console.error('Erro ao verificar rotação de logs:', error);
    }
  }

  /**
   * Rotaciona arquivos de log
   * @param filePath - Caminho do arquivo de log
   * @param maxFiles - Número máximo de arquivos de backup
   */
  private rotateLogFiles(filePath: string, maxFiles: number): void {
    try {
      // Remover arquivo mais antigo se necessário
      if (maxFiles > 0) {
        const oldestFile = `${filePath}.${maxFiles}`;
        if (fs.existsSync(oldestFile)) {
          fs.unlinkSync(oldestFile);
        }
      }

      // Rotacionar arquivos existentes
      for (let i = maxFiles - 1; i >= 1; i--) {
        const oldFile = `${filePath}.${i}`;
        const newFile = `${filePath}.${i + 1}`;

        if (fs.existsSync(oldFile)) {
          fs.renameSync(oldFile, newFile);
        }
      }

      // Renomear arquivo atual
      if (fs.existsSync(filePath)) {
        fs.renameSync(filePath, `${filePath}.1`);
      }
    } catch (error) {
      console.error('Erro ao rotacionar arquivos de log:', error);
    }
  }

  /**
   * Adiciona uma mensagem ao buffer para posterior inserção no banco de dados
   * @param logMessage - Mensagem de log
   */
  private bufferLogForDatabase(logMessage: KafkaLogMessage): void {
    this.logBuffer.push(logMessage);

    const dbDestination = kafkaLoggingConfig.destinations.find(
      dest => dest.type === KafkaLogDestination.DATABASE && dest.enabled
    );

    // Verificar se o buffer atingiu o tamanho máximo
    if (
      dbDestination?.config?.batchSize &&
      this.logBuffer.length >= dbDestination.config.batchSize
    ) {
      this.flushLogs();
    }
  }

  /**
   * Envia logs armazenados em buffer para o banco de dados
   */
  private async flushLogs(): Promise<void> {
    if (this.logBuffer.length === 0) {
      return;
    }

    const logsToFlush = [...this.logBuffer];
    this.logBuffer = [];

    try {
      // Preparar valores para inserção em lote
      const values = logsToFlush.map(log => [
        log.id,
        log.timestamp,
        log.level,
        log.component,
        log.message,
        log.hostname || null,
        log.processId || null,
        log.metadata ? JSON.stringify(log.metadata) : null,
      ]);

      // Inserir logs no banco de dados
      const dbDestination = kafkaLoggingConfig.destinations.find(
        dest => dest.type === KafkaLogDestination.DATABASE && dest.enabled
      );

      if (dbDestination?.config?.tableName) {
        await queryHelper.batchQuery(
          `INSERT INTO ${dbDestination.config.tableName} (
            log_id,
            timestamp,
            level,
            component,
            message,
            hostname,
            process_id,
            metadata,
            created_at
          ) VALUES (
            $1, $2, $3, $4, $5, $6, $7, $8, NOW()
          )`,
          values
        );
      }
    } catch (error) {
      console.error('Erro ao inserir logs no banco de dados:', error);

      // Readicionar logs ao buffer em caso de erro
      this.logBuffer = [...logsToFlush, ...this.logBuffer];
    }
  }

  /**
   * Envia uma mensagem de log para um tópico Kafka
   * @param logMessage - Mensagem de log
   * @param topic - Tópico Kafka
   */
  private async logToKafka(
    logMessage: KafkaLogMessage,
    topic: string
  ): Promise<void> {
    try {
      await producer.send({
        topic,
        messages: [
          {
            key: logMessage.component,
            value: JSON.stringify(logMessage),
            headers: {
              level: logMessage.level,
              timestamp: logMessage.timestamp,
            },
          },
        ],
      });
    } catch (error) {
      console.error('Erro ao enviar log para Kafka:', error);
    }
  }

  /**
   * Registra uma mensagem de debug
   * @param component - Componente que gerou o log
   * @param message - Mensagem do log
   * @param metadata - Metadados adicionais
   */
  public debug(
    component: string,
    message: string,
    metadata?: Record<string, any>
  ): void {
    this.log(KafkaLogLevel.DEBUG, component, message, metadata);
  }

  /**
   * Registra uma mensagem de informação
   * @param component - Componente que gerou o log
   * @param message - Mensagem do log
   * @param metadata - Metadados adicionais
   */
  public info(
    component: string,
    message: string,
    metadata?: Record<string, any>
  ): void {
    this.log(KafkaLogLevel.INFO, component, message, metadata);
  }

  /**
   * Registra uma mensagem de aviso
   * @param component - Componente que gerou o log
   * @param message - Mensagem do log
   * @param metadata - Metadados adicionais
   */
  public warn(
    component: string,
    message: string,
    metadata?: Record<string, any>
  ): void {
    this.log(KafkaLogLevel.WARN, component, message, metadata);
  }

  /**
   * Registra uma mensagem de erro
   * @param component - Componente que gerou o log
   * @param message - Mensagem do log
   * @param errorOrMetadata - Erro ou metadados adicionais
   * @param metadata - Metadados adicionais quando o primeiro parâmetro é um erro
   */
  public error(
    component: string,
    message: string,
    errorOrMetadata?: Error | Record<string, any>,
    metadata?: Record<string, any>
  ): void {
    let finalMetadata = metadata || {};

    if (errorOrMetadata instanceof Error) {
      finalMetadata = {
        ...finalMetadata,
        error: {
          message: errorOrMetadata.message,
          name: errorOrMetadata.name,
          stack: errorOrMetadata.stack,
        },
      };
    } else if (errorOrMetadata && typeof errorOrMetadata === 'object') {
      finalMetadata = errorOrMetadata;
    }

    this.log(KafkaLogLevel.ERROR, component, message, finalMetadata);
  }

  /**
   * Registra uma mensagem de erro fatal
   * @param component - Componente que gerou o log
   * @param message - Mensagem do log
   * @param metadata - Metadados adicionais
   */
  public fatal(
    component: string,
    message: string,
    metadata?: Record<string, any>
  ): void {
    this.log(KafkaLogLevel.FATAL, component, message, metadata);
  }
}

// Exportar instância única do serviço
export const kafkaLoggingService = KafkaLoggingService.getInstance();
