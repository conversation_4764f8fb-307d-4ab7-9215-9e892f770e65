/**
 * Serviço de autenticação
 *
 * Este serviço é responsável por autenticar usuários e gerenciar tokens.
 * Parte da implementação da tarefa 9.1.1 - Testes unitários
 */

import * as bcrypt from 'bcrypt';
import { User } from '../entities/User';
import { UserRepository } from '../repositories/UserRepository';
import { TokenPayload, TokenResponse, TokenService } from './TokenService';
import { AuthenticationError } from './errors/AuthenticationError';

/**
 * Resposta de autenticação
 */
export interface AuthResponse extends TokenResponse {
  user: {
    id: string;
    name: string;
    email: string;
    role: string;
  };
}

/**
 * Serviço de autenticação
 */
export class AuthenticationService {
  /**
   * Cria uma nova instância do serviço de autenticação
   * @param userRepository Repositório de usuários
   * @param tokenService Serviço de tokens
   */
  constructor(
    private readonly userRepository: UserRepository,
    private readonly tokenService: TokenService
  ) {}

  /**
   * Autentica um usuário com email e senha
   * @param email Email do usuário
   * @param password Senha do usuário
   * @returns Resposta de autenticação com token e dados do usuário
   * @throws AuthenticationError se as credenciais forem inválidas
   */
  async login(email: string, password: string): Promise<AuthResponse> {
    // Buscar usuário pelo email
    const user = await this.userRepository.findByEmail(email);

    // Verificar se o usuário existe
    if (!user) {
      throw new AuthenticationError('Credenciais inválidas');
    }

    // Verificar se a senha está correta
    const isPasswordValid = await this.verifyPassword(password, user.password);
    if (!isPasswordValid) {
      throw new AuthenticationError('Credenciais inválidas');
    }

    // Verificar se o usuário está ativo
    if (!user.isActive) {
      throw new AuthenticationError('Conta desativada');
    }

    // Gerar token
    const tokenPayload: TokenPayload = {
      userId: user.id,
      role: user.role,
    };

    const token = await this.tokenService.generateToken(tokenPayload);

    // Retornar resposta
    return {
      user: {
        id: user.id,
        name: user.name,
        email: user.email,
        role: user.role,
      },
      ...token,
    };
  }

  /**
   * Atualiza um token de acesso usando um refresh token
   * @param refreshToken Refresh token
   * @returns Novo token de acesso, refresh token e tempo de expiração
   * @throws AuthenticationError se o refresh token for inválido
   */
  async refreshToken(refreshToken: string): Promise<TokenResponse> {
    try {
      // Verificar se o refresh token é válido
      const payload = await this.tokenService.verifyToken(refreshToken);

      // Buscar usuário pelo ID
      const user = await this.userRepository.findById(payload.userId);

      // Verificar se o usuário existe
      if (!user) {
        throw new AuthenticationError('Usuário não encontrado');
      }

      // Verificar se o usuário está ativo
      if (!user.isActive) {
        throw new AuthenticationError('Conta desativada');
      }

      // Gerar novo token
      const tokenPayload: TokenPayload = {
        userId: user.id,
        role: user.role,
      };

      return await this.tokenService.refreshToken(refreshToken, tokenPayload);
    } catch (error) {
      throw new AuthenticationError('Token inválido ou expirado');
    }
  }

  /**
   * Encerra a sessão de um usuário
   * @param refreshToken Refresh token
   */
  async logout(refreshToken: string): Promise<void> {
    await this.tokenService.revokeToken(refreshToken);
  }

  /**
   * Verifica se uma senha está correta
   * @param password Senha a ser verificada
   * @param hashedPassword Hash da senha armazenada
   * @returns true se a senha estiver correta, false caso contrário
   */
  private async verifyPassword(password: string, hashedPassword: string): Promise<boolean> {
    return await bcrypt.compare(password, hashedPassword);
  }
}
