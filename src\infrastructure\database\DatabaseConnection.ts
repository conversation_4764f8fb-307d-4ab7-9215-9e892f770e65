/**
 * Interface para conexão com banco de dados
 *
 * Esta interface define o contrato que qualquer implementação de conexão
 * com banco de dados deve seguir. Seguindo o Dependency Inversion Principle,
 * os adaptadores dependem desta abstração, não de implementações concretas.
 */

/**
 * Resultado de uma consulta
 */
export interface QueryResult {
  /**
   * Linhas retornadas pela consulta
   */
  rows: any[];

  /**
   * Número de linhas afetadas
   */
  rowCount: number;
}

/**
 * Interface para conexão com banco de dados
 */
export interface DatabaseConnection {
  /**
   * Executa uma consulta SQL
   * @param text - Texto da consulta
   * @param params - Parâmetros da consulta
   * @returns Resultado da consulta
   */
  query(text: string, params?: any[]): Promise<QueryResult>;

  /**
   * Executa uma consulta SQL dentro de uma transação
   * @param callback - Função que recebe um cliente de transação
   * @returns Resultado da função de callback
   */
  transaction<T>(callback: (client: TransactionClient) => Promise<T>): Promise<T>;

  /**
   * Fecha a conexão com o banco de dados
   */
  close(): Promise<void>;
}

/**
 * Interface para cliente de transação
 */
export interface TransactionClient {
  /**
   * Executa uma consulta SQL dentro da transação
   * @param text - Texto da consulta
   * @param params - Parâmetros da consulta
   * @returns Resultado da consulta
   */
  query(text: string, params?: any[]): Promise<QueryResult>;
}
