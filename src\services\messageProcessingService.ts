/**
 * Serviço de processamento de mensagens
 *
 * Este serviço fornece funcionalidades para processamento avançado de mensagens,
 * incluindo validação de esquema, transformação, idempotência e rastreamento.
 */

import { queryHelper } from '@db/queryHelper';
import { cacheService } from '@services/cacheService';
import { logger } from '@utils/logger';
import { v4 as uuidv4 } from 'uuid';
import { BaseEvent } from './eventProducerService';

/**
 * Interface para validador de esquema
 */
export interface SchemaValidator {
  /**
   * Valida um evento contra um esquema
   * @param event - Evento a ser validado
   * @returns Verdadeiro se o evento for válido
   */
  validate(event: Record<string, unknown>): boolean;

  /**
   * Obtém erros de validação
   * @returns Lista de erros de validação
   */
  getErrors(): string[];
}

/**
 * Interface para transformador de mensagem
 */
export interface MessageTransformer<T = Record<string, unknown>, R = Record<string, unknown>> {
  /**
   * Transforma uma mensagem
   * @param message - Mensagem a ser transformada
   * @returns Mensagem transformada
   */
  transform(message: T): R;
}

/**
 * Tipo para função de processamento de mensagem
 */
export type MessageProcessor<T = Record<string, unknown>> = (
  message: T,
  context: ProcessingContext
) => Promise<void>;

/**
 * Interface para contexto de processamento
 */
export interface ProcessingContext {
  /**
   * ID da mensagem
   */
  messageId: string;

  /**
   * Tópico da mensagem
   */
  topic: string;

  /**
   * Partição da mensagem
   */
  partition: number;

  /**
   * Offset da mensagem
   */
  offset: string;

  /**
   * Chave da mensagem
   */
  key?: string;

  /**
   * Timestamp da mensagem
   */
  timestamp: number;

  /**
   * Cabeçalhos da mensagem
   */
  headers: Record<string, string>;

  /**
   * ID de rastreamento
   */
  traceId: string;

  /**
   * Tentativa atual
   */
  attempt: number;
}

/**
 * Opções para processamento de mensagem
 */
export interface ProcessingOptions {
  /**
   * Se deve verificar idempotência
   */
  checkIdempotence?: boolean;

  /**
   * Tempo de expiração do registro de idempotência (segundos)
   */
  idempotenceExpiration?: number;

  /**
   * Validador de esquema
   */
  schemaValidator?: SchemaValidator;

  /**
   * Transformador de mensagem
   */
  transformer?: MessageTransformer;

  /**
   * Se deve registrar mensagem no banco de dados
   */
  logToDatabase?: boolean;

  /**
   * Domínio da mensagem
   */
  domain?: string;

  /**
   * Entidade da mensagem
   */
  entity?: string;

  /**
   * Tipo de evento
   */
  eventType?: string;
}

/**
 * Serviço de processamento de mensagens
 */
export const messageProcessingService = {
  /**
   * Processa uma mensagem com verificação de idempotência e validação
   * @param message - Mensagem a ser processada
   * @param processor - Função de processamento
   * @param context - Contexto de processamento
   * @param options - Opções de processamento
   */
  async processMessage<T extends Record<string, unknown>>(
    message: T,
    processor: MessageProcessor<T>,
    context: ProcessingContext,
    options: ProcessingOptions = {}
  ): Promise<void> {
    const {
      checkIdempotence = true,
      idempotenceExpiration = 86400, // 24 horas
      schemaValidator,
      transformer,
      logToDatabase = true,
      domain,
      entity,
      eventType,
    } = options;

    try {
      // Validar esquema se fornecido
      if (schemaValidator && !schemaValidator.validate(message)) {
        const errors = schemaValidator.getErrors();
        logger.error(`Erro de validação de esquema para mensagem ${context.messageId}:`, {
          errors,
          topic: context.topic,
          key: context.key,
        });

        // Registrar erro de validação
        if (logToDatabase) {
          await this.logProcessingError(
            context.messageId,
            'schema_validation_error',
            errors.join(', '),
            context,
            message
          );
        }

        throw new Error(`Erro de validação de esquema: ${errors.join(', ')}`);
      }

      // Verificar idempotência se habilitado
      if (checkIdempotence) {
        const isProcessed = await this.checkIdempotence(context.messageId);

        if (isProcessed) {
          logger.info(`Mensagem ${context.messageId} já processada, ignorando`);
          return;
        }
      }

      // Transformar mensagem se fornecido
      let processedMessage: Record<string, unknown> = message;
      if (transformer) {
        processedMessage = transformer.transform(message);
      }

      // Registrar início do processamento
      if (logToDatabase) {
        await this.logProcessingStart(context.messageId, context, processedMessage as T);
      }

      // Processar mensagem
      await processor(processedMessage as T, context);

      // Registrar idempotência se habilitado
      if (checkIdempotence) {
        await this.markAsProcessed(context.messageId, idempotenceExpiration);
      }

      // Registrar conclusão do processamento
      if (logToDatabase) {
        await this.logProcessingComplete(context.messageId);
      }
    } catch (error) {
      logger.error(`Erro ao processar mensagem ${context.messageId}:`, error);

      // Registrar erro de processamento
      if (logToDatabase) {
        await this.logProcessingError(
          context.messageId,
          'processing_error',
          error instanceof Error ? error.message : 'Erro desconhecido',
          context,
          message
        );
      }

      throw error;
    }
  },

  /**
   * Verifica se uma mensagem já foi processada
   * @param messageId - ID da mensagem
   * @returns Verdadeiro se a mensagem já foi processada
   */
  async checkIdempotence(messageId: string): Promise<boolean> {
    try {
      // Verificar no cache primeiro
      const cacheKey = `msg:processed:${messageId}`;
      const cachedResult = await cacheService.get(cacheKey);

      if (cachedResult) {
        return true;
      }

      // Verificar no banco de dados
      const result = await queryHelper.queryOne(
        `SELECT 1 FROM tab_message_processing 
         WHERE message_id = $1 AND status = 'completed'`,
        [messageId]
      );

      return !!result;
    } catch (error) {
      logger.error(`Erro ao verificar idempotência para mensagem ${messageId}:`, error);
      // Em caso de erro, assumir que não foi processada
      return false;
    }
  },

  /**
   * Marca uma mensagem como processada
   * @param messageId - ID da mensagem
   * @param expiration - Tempo de expiração em segundos
   */
  async markAsProcessed(messageId: string, expiration: number): Promise<void> {
    try {
      // Armazenar no cache
      const cacheKey = `msg:processed:${messageId}`;
      await cacheService.set(cacheKey, 'true', expiration);
    } catch (error) {
      logger.error(`Erro ao marcar mensagem ${messageId} como processada:`, error);
      // Não propagar erro para não interromper o fluxo
    }
  },

  /**
   * Registra início do processamento de uma mensagem
   * @param messageId - ID da mensagem
   * @param context - Contexto de processamento
   * @param message - Mensagem a ser processada
   */
  async logProcessingStart<T>(
    messageId: string,
    context: ProcessingContext,
    message: T
  ): Promise<void> {
    try {
      await queryHelper.query(
        `INSERT INTO tab_message_processing (
          message_id, topic, partition, offset, 
          key, trace_id, attempt, status, 
          message_data, started_at
        ) VALUES (
          $1, $2, $3, $4, $5, $6, $7, $8, $9, NOW()
        ) ON CONFLICT (message_id) DO UPDATE SET
          attempt = tab_message_processing.attempt + 1,
          status = $8,
          started_at = NOW()`,
        [
          messageId,
          context.topic,
          context.partition,
          context.offset,
          context.key || null,
          context.traceId,
          context.attempt,
          'processing',
          JSON.stringify(message),
        ]
      );
    } catch (error) {
      logger.error(`Erro ao registrar início do processamento da mensagem ${messageId}:`, error);
      // Não propagar erro para não interromper o fluxo
    }
  },

  /**
   * Registra conclusão do processamento de uma mensagem
   * @param messageId - ID da mensagem
   */
  async logProcessingComplete(messageId: string): Promise<void> {
    try {
      await queryHelper.query(
        `UPDATE tab_message_processing
         SET status = 'completed', completed_at = NOW()
         WHERE message_id = $1`,
        [messageId]
      );
    } catch (error) {
      logger.error(`Erro ao registrar conclusão do processamento da mensagem ${messageId}:`, error);
      // Não propagar erro para não interromper o fluxo
    }
  },

  /**
   * Registra erro no processamento de uma mensagem
   * @param messageId - ID da mensagem
   * @param errorType - Tipo de erro
   * @param errorMessage - Mensagem de erro
   * @param context - Contexto de processamento
   * @param message - Mensagem que falhou
   */
  async logProcessingError<T>(
    messageId: string,
    errorType: string,
    errorMessage: string,
    context: ProcessingContext,
    message: T
  ): Promise<void> {
    try {
      await queryHelper.query(
        `UPDATE tab_message_processing
         SET status = 'failed', 
             error_type = $1,
             error_message = $2,
             completed_at = NOW()
         WHERE message_id = $3`,
        [errorType, errorMessage, messageId]
      );
    } catch (error) {
      logger.error(`Erro ao registrar erro de processamento da mensagem ${messageId}:`, error);
      // Não propagar erro para não interromper o fluxo
    }
  },

  /**
   * Cria um contexto de processamento
   * @param topic - Tópico da mensagem
   * @param partition - Partição da mensagem
   * @param offset - Offset da mensagem
   * @param key - Chave da mensagem
   * @param headers - Cabeçalhos da mensagem
   * @returns Contexto de processamento
   */
  createContext(
    topic: string,
    partition: number,
    offset: string,
    key?: string,
    headers: Record<string, string> = {}
  ): ProcessingContext {
    return {
      messageId: headers['message-id'] || uuidv4(),
      topic,
      partition,
      offset,
      key,
      timestamp: Number.parseInt(headers.timestamp || Date.now().toString(), 10),
      headers,
      traceId: headers['trace-id'] || uuidv4(),
      attempt: 1,
    };
  },
};
