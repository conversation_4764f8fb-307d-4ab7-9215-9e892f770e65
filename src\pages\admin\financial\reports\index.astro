---
import FinancialNav from '@components/navigation/FinancialNav.astro';
import { queryHelper } from '@db/queryHelper';
import { checkAdmin } from '@helpers/authGuard';
import AdminLayout from '@layouts/AdminLayout.astro';

// Protege a rota verificando se o usuário é admin
const authRedirect = await checkAdmin(Astro);
if (authRedirect) return authRedirect;

// Obter parâmetros de filtro
const startDate = Astro.url.searchParams.get('start_date') || '';
const endDate = Astro.url.searchParams.get('end_date') || '';
const paymentType = Astro.url.searchParams.get('payment_type') || '';
const status = Astro.url.searchParams.get('status') || '';

// Construir consulta SQL com filtros
let query = `
  SELECT
    o.ulid_order,
    o.created_at as order_date,
    u.name as customer_name,
    u.email as customer_email,
    o.total as order_total,
    p.value as payment_value,
    pt.type as payment_type,
    s.status as payment_status,
    p.created_at as payment_date
  FROM tab_order o
  JOIN tab_user u ON o.ulid_user = u.ulid_user
  LEFT JOIN tab_payment p ON o.ulid_order = p.ulid_order
  LEFT JOIN tab_payment_type pt ON p.ulid_payment_type = pt.ulid_payment_type
  LEFT JOIN tab_status s ON p.cod_status = s.cod_status
  WHERE 1=1
`;

const params = [];
let paramIndex = 1;

if (startDate) {
  query += ` AND o.created_at >= $${paramIndex}`;
  params.push(startDate);
  paramIndex++;
}

if (endDate) {
  query += ` AND o.created_at <= $${paramIndex}`;
  params.push(endDate);
  paramIndex++;
}

if (paymentType) {
  query += ` AND pt.type = $${paramIndex}`;
  params.push(paymentType);
  paramIndex++;
}

if (status) {
  query += ` AND s.status = $${paramIndex}`;
  params.push(status);
  paramIndex++;
}

query += ' ORDER BY o.created_at DESC';

// Executar consulta
const transactions = await queryHelper.query(query, params);

// Buscar tipos de pagamento para o filtro
const paymentTypes = await queryHelper.query(
  'SELECT type FROM tab_payment_type WHERE active = true ORDER BY type'
);

// Buscar status para o filtro
const statuses = await queryHelper.query('SELECT status FROM tab_status ORDER BY cod_status');

// Calcular totais
const totalRevenue = transactions.rows.reduce(
  (sum, row) => sum + Number.parseFloat(row.order_total || 0),
  0
);
const totalPaid = transactions.rows.reduce((sum, row) => {
  return sum + (row.payment_status === 'Aprovado' ? Number.parseFloat(row.payment_value || 0) : 0);
}, 0);

// Formatar valores monetários
const formatCurrency = (value) => {
  return new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: 'BRL',
  }).format(value || 0);
};

// Formatar data
const formatDate = (dateString) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  return `${date.toLocaleDateString('pt-BR')} ${date.toLocaleTimeString('pt-BR')}`;
};
---

<AdminLayout title="Relatórios Financeiros">
  <div class="container mx-auto p-4">
    <h1 class="text-2xl font-bold mb-4">Gestão Financeira</h1>

    <FinancialNav />

    <h2 class="text-xl font-bold mb-6">Relatórios Financeiros</h2>

    <!-- Filtros -->
    <div class="card bg-base-100 shadow-xl mb-6">
      <div class="card-body">
        <h2 class="card-title">Filtros</h2>

        <form class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div class="form-control">
            <label class="label">
              <span class="label-text">Data Inicial</span>
            </label>
            <input
              type="date"
              name="start_date"
              value={startDate}
              class="input input-bordered"
            />
          </div>

          <div class="form-control">
            <label class="label">
              <span class="label-text">Data Final</span>
            </label>
            <input
              type="date"
              name="end_date"
              value={endDate}
              class="input input-bordered"
            />
          </div>

          <div class="form-control">
            <label class="label">
              <span class="label-text">Método de Pagamento</span>
            </label>
            <select name="payment_type" class="select select-bordered">
              <option value="">Todos</option>
              {paymentTypes.rows.map(pt => (
                <option value={pt.type} selected={pt.type === paymentType}>{pt.type}</option>
              ))}
            </select>
          </div>

          <div class="form-control">
            <label class="label">
              <span class="label-text">Status</span>
            </label>
            <select name="status" class="select select-bordered">
              <option value="">Todos</option>
              {statuses.rows.map(s => (
                <option value={s.status} selected={s.status === status}>{s.status}</option>
              ))}
            </select>
          </div>

          <div class="col-span-1 md:col-span-2 lg:col-span-4 flex justify-end">
            <button type="submit" class="btn btn-primary">Filtrar</button>
            <a href="/admin/financial/reports" class="btn btn-ghost ml-2">Limpar</a>
            <div class="dropdown dropdown-end ml-2">
              <label tabindex="0" class="btn btn-secondary">Exportar</label>
              <ul tabindex="0" class="dropdown-content z-[1] menu p-2 shadow bg-base-100 rounded-box w-52">
                <li><a id="export-csv">CSV</a></li>
                <li><a id="export-json">JSON</a></li>
                <li><a id="export-pdf">PDF</a></li>
              </ul>
            </div>
          </div>
        </form>
      </div>
    </div>

    <!-- Resumo -->
    <div class="stats shadow mb-6 w-full">
      <div class="stat">
        <div class="stat-title">Total de Transações</div>
        <div class="stat-value">{transactions.rows.length}</div>
      </div>

      <div class="stat">
        <div class="stat-title">Receita Total</div>
        <div class="stat-value text-primary">{formatCurrency(totalRevenue)}</div>
      </div>

      <div class="stat">
        <div class="stat-title">Total Pago</div>
        <div class="stat-value text-success">{formatCurrency(totalPaid)}</div>
      </div>

      <div class="stat">
        <div class="stat-title">Taxa de Conversão</div>
        <div class="stat-value text-secondary">
          {totalRevenue > 0 ? Math.round((totalPaid / totalRevenue) * 100) : 0}%
        </div>
      </div>
    </div>

    <!-- Tabela de Transações -->
    <div class="overflow-x-auto">
      <table class="table table-zebra w-full">
        <thead>
          <tr>
            <th>ID do Pedido</th>
            <th>Data</th>
            <th>Cliente</th>
            <th>Valor</th>
            <th>Método</th>
            <th>Status</th>
            <th>Ações</th>
          </tr>
        </thead>
        <tbody>
          {transactions.rows.length === 0 ? (
            <tr>
              <td colspan="7" class="text-center py-4">Nenhuma transação encontrada</td>
            </tr>
          ) : (
            transactions.rows.map(transaction => (
              <tr>
                <td>{transaction.ulid_order.substring(0, 8)}...</td>
                <td>{formatDate(transaction.order_date)}</td>
                <td>
                  <div>{transaction.customer_name}</div>
                  <div class="text-xs opacity-60">{transaction.customer_email}</div>
                </td>
                <td>{formatCurrency(transaction.order_total)}</td>
                <td>{transaction.payment_type || 'N/A'}</td>
                <td>
                  <div class={`badge ${
                    transaction.payment_status === 'Aprovado' ? 'badge-success' :
                    transaction.payment_status === 'Pendente' ? 'badge-warning' :
                    transaction.payment_status === 'Rejeitado' ? 'badge-error' :
                    transaction.payment_status === 'Reembolsado' ? 'badge-info' :
                    'badge-ghost'
                  }`}>
                    {transaction.payment_status || 'N/A'}
                  </div>
                </td>
                <td>
                  <a href={`/admin/financial/order/${transaction.ulid_order}`} class="btn btn-xs btn-ghost">
                    Detalhes
                  </a>
                </td>
              </tr>
            ))
          )}
        </tbody>
      </table>
    </div>
  </div>
</AdminLayout>

<script>
  // Função para obter dados da tabela
  function getTableData() {
    const table = document.querySelector('table');
    if (!table) return null;

    const rows = Array.from(table.querySelectorAll('tr'));

    // Obter cabeçalhos
    const headers = Array.from(rows[0].querySelectorAll('th'))
      .map(th => th.textContent?.trim())
      .filter(header => header !== 'Ações');

    // Obter dados
    const data = rows.slice(1).map(row => {
      const cells = Array.from(row.querySelectorAll('td'));
      // Excluir a coluna de ações
      return cells.slice(0, -1).map(cell => {
        // Remover quebras de linha
        return cell.textContent?.trim().replace(/\n/g, ' ') || '';
      });
    });

    return { headers, data };
  }

  // Função para exportar dados para CSV
  document.getElementById('export-csv')?.addEventListener('click', () => {
    const tableData = getTableData();
    if (!tableData) return;

    const { headers, data } = tableData;

    // Formatar dados para CSV
    const formattedData = data.map(row => {
      return row.map(cell => `"${cell.replace(/"/g, '""')}"`);
    });

    // Criar conteúdo CSV
    const csvContent = [
      headers.join(','),
      ...formattedData.map(row => row.join(','))
    ].join('\n');

    // Criar blob e link para download
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');

    // Configurar link
    link.setAttribute('href', url);
    link.setAttribute('download', `relatorio-financeiro-${new Date().toISOString().split('T')[0]}.csv`);
    link.style.display = 'none';

    // Adicionar ao documento, clicar e remover
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  });

  // Função para exportar dados para JSON
  document.getElementById('export-json')?.addEventListener('click', () => {
    const tableData = getTableData();
    if (!tableData) return;

    const { headers, data } = tableData;

    // Converter para array de objetos
    const jsonData = data.map(row => {
      const obj = {};
      headers.forEach((header, index) => {
        obj[header] = row[index];
      });
      return obj;
    });

    // Criar conteúdo JSON
    const jsonContent = JSON.stringify(jsonData, null, 2);

    // Criar blob e link para download
    const blob = new Blob([jsonContent], { type: 'application/json;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');

    // Configurar link
    link.setAttribute('href', url);
    link.setAttribute('download', `relatorio-financeiro-${new Date().toISOString().split('T')[0]}.json`);
    link.style.display = 'none';

    // Adicionar ao documento, clicar e remover
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  });

  // Função para exportar dados para PDF
  document.getElementById('export-pdf')?.addEventListener('click', async () => {
    const tableData = getTableData();
    if (!tableData) return;

    const { headers, data } = tableData;

    // Converter para array de objetos
    const jsonData = data.map(row => {
      const obj = {};
      headers.forEach((header, index) => {
        obj[header] = row[index];
      });
      return obj;
    });

    // Obter parâmetros de filtro da URL
    const urlParams = new URLSearchParams(window.location.search);
    const filters = {
      startDate: urlParams.get('start_date') || undefined,
      endDate: urlParams.get('end_date') || undefined,
      paymentType: urlParams.get('payment_type') || undefined,
      status: urlParams.get('status') || undefined
    };

    try {
      // Exibir indicador de carregamento
      const loadingToast = document.createElement('div');
      loadingToast.className = 'toast toast-center';
      loadingToast.innerHTML = `
        <div class="alert alert-info">
          <span>Gerando PDF, aguarde...</span>
        </div>
      `;
      document.body.appendChild(loadingToast);

      // Fazer requisição para a API
      const response = await fetch('/api/reports/export', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          reportType: 'transactions',
          format: 'pdf',
          filters
        })
      });

      // Remover indicador de carregamento
      document.body.removeChild(loadingToast);

      if (!response.ok) {
        throw new Error('Erro ao gerar PDF');
      }

      // Obter blob do PDF
      const pdfBlob = await response.blob();

      // Criar URL e link para download
      const url = URL.createObjectURL(pdfBlob);
      const link = document.createElement('a');

      // Configurar link
      link.setAttribute('href', url);
      link.setAttribute('download', `relatorio-financeiro-${new Date().toISOString().split('T')[0]}.pdf`);
      link.style.display = 'none';

      // Adicionar ao documento, clicar e remover
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

    } catch (error) {
      console.error('Erro ao exportar PDF:', error);

      // Exibir mensagem de erro
      const errorToast = document.createElement('div');
      errorToast.className = 'toast toast-center';
      errorToast.innerHTML = `
        <div class="alert alert-error">
          <span>Erro ao gerar PDF: ${error.message}</span>
        </div>
      `;
      document.body.appendChild(errorToast);

      // Remover mensagem após 5 segundos
      setTimeout(() => {
        document.body.removeChild(errorToast);
      }, 5000);
    }
  });
</script>
