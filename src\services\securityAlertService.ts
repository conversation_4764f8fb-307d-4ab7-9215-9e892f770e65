/**
 * Serviço de alertas de segurança
 *
 * Este serviço é responsável por detectar e notificar sobre eventos
 * de segurança relevantes no sistema.
 */

import { auditRepository } from '@repository/auditRepository';
import { userRepository } from '@repository/userRepository';
import { AuditEventType, AuditSeverity } from '@services/auditService';
import { cacheService } from '@services/cacheService';
import { logger } from '@utils/logger';
import { ulid } from 'ulid';

/**
 * Tipos de alertas de segurança
 */
export enum SecurityAlertType {
  // Alertas de autenticação
  MULTIPLE_LOGIN_FAILURES = 'auth.login.multiple_failures',
  UNUSUAL_LOGIN_LOCATION = 'auth.login.unusual_location',
  ACCOUNT_LOCKED = 'auth.account.locked',

  // Alertas de autorização
  PERMISSION_ESCALATION = 'auth.permission.escalation',
  UNUSUAL_PERMISSION_CHANGE = 'auth.permission.unusual_change',
  ADMIN_ROLE_ASSIGNED = 'auth.role.admin_assigned',

  // Alertas de sistema
  CRITICAL_ERROR = 'system.error.critical',
  MULTIPLE_ERRORS = 'system.error.multiple',
  CONFIGURATION_CHANGE = 'system.config.changed',

  // Alertas de acesso
  UNUSUAL_ACCESS_PATTERN = 'access.unusual_pattern',
  SENSITIVE_DATA_ACCESS = 'access.sensitive_data',
  MULTIPLE_RESOURCE_DELETIONS = 'access.multiple_deletions',
}

/**
 * Níveis de severidade para alertas de segurança
 */
export enum SecurityAlertSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical',
}

/**
 * Interface para dados de alerta de segurança
 */
export interface SecurityAlertData {
  /**
   * Tipo do alerta
   */
  alertType: SecurityAlertType;

  /**
   * Título do alerta
   */
  title: string;

  /**
   * Descrição do alerta
   */
  description: string;

  /**
   * Severidade do alerta
   */
  severity: SecurityAlertSeverity;

  /**
   * ID do usuário relacionado (se aplicável)
   */
  userId?: string;

  /**
   * Nome do usuário relacionado (se aplicável)
   */
  userName?: string;

  /**
   * Endereço IP relacionado (se aplicável)
   */
  ipAddress?: string;

  /**
   * Recurso afetado (se aplicável)
   */
  resource?: string;

  /**
   * ID do recurso afetado (se aplicável)
   */
  resourceId?: string;

  /**
   * IDs dos eventos de auditoria relacionados
   */
  relatedEventIds?: string[];

  /**
   * Dados adicionais específicos do alerta
   */
  metadata?: Record<string, any>;

  /**
   * Se o alerta foi resolvido
   */
  resolved?: boolean;

  /**
   * ID do usuário que resolveu o alerta (se aplicável)
   */
  resolvedBy?: string;

  /**
   * Data de resolução do alerta (se aplicável)
   */
  resolvedAt?: Date;

  /**
   * Comentário de resolução (se aplicável)
   */
  resolutionComment?: string;
}

/**
 * Serviço de alertas de segurança
 */
export const securityAlertService = {
  /**
   * Cria um novo alerta de segurança
   * @param alertData - Dados do alerta
   * @returns ID do alerta criado ou null em caso de erro
   */
  async createAlert(alertData: SecurityAlertData): Promise<string | null> {
    try {
      // Gerar ID único para o alerta
      const alertId = ulid();

      // Definir timestamp atual
      const timestamp = new Date();

      // Preparar metadados para armazenamento
      const metadata = alertData.metadata ? JSON.stringify(alertData.metadata) : null;

      // Preparar IDs de eventos relacionados
      const relatedEventIds = alertData.relatedEventIds
        ? JSON.stringify(alertData.relatedEventIds)
        : null;

      // Inserir alerta no banco de dados
      const query = `
        INSERT INTO tab_security_alerts (
          ulid_security_alert,
          alert_type,
          title,
          description,
          severity,
          ulid_user,
          user_name,
          ip_address,
          resource,
          resource_id,
          related_event_ids,
          metadata,
          is_resolved,
          created_at
        ) VALUES (
          $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14
        )
        RETURNING ulid_security_alert
      `;

      const values = [
        alertId,
        alertData.alertType,
        alertData.title,
        alertData.description,
        alertData.severity,
        alertData.userId || null,
        alertData.userName || null,
        alertData.ipAddress || null,
        alertData.resource || null,
        alertData.resourceId || null,
        relatedEventIds,
        metadata,
        false, // is_resolved
        timestamp,
      ];

      const result = await auditRepository.query(query, values);

      // Armazenar alerta em cache para acesso rápido
      if (result.rowCount > 0) {
        await this.cacheAlert(alertId, alertData, timestamp);

        // Enviar notificações para os canais configurados
        await this.sendAlertNotifications(alertId, alertData);

        return alertId;
      }

      return null;
    } catch (error) {
      logger.error('Erro ao criar alerta de segurança:', error);
      return null;
    }
  },

  /**
   * Armazena alerta em cache para acesso rápido
   * @param alertId - ID do alerta
   * @param alertData - Dados do alerta
   * @param timestamp - Timestamp do alerta
   */
  async cacheAlert(alertId: string, alertData: SecurityAlertData, timestamp: Date): Promise<void> {
    try {
      // Chave para lista de alertas recentes
      const recentAlertsKey = 'security:recent_alerts';

      // Chave para detalhes do alerta específico
      const alertKey = `security:alert:${alertId}`;

      // Armazenar detalhes do alerta (TTL de 24 horas)
      await cacheService.set(
        alertKey,
        JSON.stringify({
          id: alertId,
          ...alertData,
          timestamp: timestamp.toISOString(),
        }),
        86400 // 24 horas em segundos
      );

      // Adicionar à lista de alertas recentes (limitada a 50 alertas)
      await cacheService.zadd(recentAlertsKey, timestamp.getTime(), alertId);

      // Manter apenas os 50 alertas mais recentes
      await cacheService.zremrangebyrank(recentAlertsKey, 0, -51);

      // Definir TTL para a lista de alertas recentes (24 horas)
      await cacheService.expire(recentAlertsKey, 86400);
    } catch (error) {
      logger.error('Erro ao armazenar alerta de segurança em cache:', error);
      // Não propagar erro, pois o cache é apenas uma otimização
    }
  },

  /**
   * Envia notificações para os canais configurados
   * @param alertId - ID do alerta
   * @param alertData - Dados do alerta
   */
  async sendAlertNotifications(alertId: string, alertData: SecurityAlertData): Promise<void> {
    try {
      // Obter configurações de notificação
      const notificationConfig = await this.getNotificationConfig();

      // Verificar se o alerta deve ser notificado com base na severidade
      const shouldNotify = this.shouldNotifyAlert(alertData.severity, notificationConfig);

      if (!shouldNotify) {
        return;
      }

      // Preparar mensagem de notificação
      const message = this.formatAlertMessage(alertId, alertData);

      // Enviar para cada canal configurado
      if (
        notificationConfig.email.enabled &&
        this.shouldNotifyChannel('email', alertData.severity, notificationConfig)
      ) {
        await this.sendEmailNotification(
          notificationConfig.email.recipients,
          `Alerta de Segurança: ${alertData.title}`,
          message
        );
      }

      if (
        notificationConfig.slack.enabled &&
        this.shouldNotifyChannel('slack', alertData.severity, notificationConfig)
      ) {
        await this.sendSlackNotification(
          notificationConfig.slack.webhook,
          alertData.title,
          message,
          this.getAlertColor(alertData.severity)
        );
      }

      if (
        notificationConfig.webhook.enabled &&
        this.shouldNotifyChannel('webhook', alertData.severity, notificationConfig)
      ) {
        await this.sendWebhookNotification(notificationConfig.webhook.url, {
          id: alertId,
          ...alertData,
          timestamp: new Date().toISOString(),
        });
      }

      // Armazenar notificação no sistema para administradores
      await this.storeSystemNotification(alertId, alertData);
    } catch (error) {
      logger.error('Erro ao enviar notificações de alerta:', error);
    }
  },

  /**
   * Verifica se um alerta deve ser notificado com base na severidade
   * @param severity - Severidade do alerta
   * @param config - Configuração de notificação
   * @returns Verdadeiro se o alerta deve ser notificado
   */
  shouldNotifyAlert(severity: SecurityAlertSeverity, config: any): boolean {
    // Mapear severidade para nível numérico
    const severityLevel = {
      [SecurityAlertSeverity.LOW]: 1,
      [SecurityAlertSeverity.MEDIUM]: 2,
      [SecurityAlertSeverity.HIGH]: 3,
      [SecurityAlertSeverity.CRITICAL]: 4,
    };

    // Mapear nível mínimo configurado
    const minLevel = {
      [SecurityAlertSeverity.LOW]: 1,
      [SecurityAlertSeverity.MEDIUM]: 2,
      [SecurityAlertSeverity.HIGH]: 3,
      [SecurityAlertSeverity.CRITICAL]: 4,
    }[config.minSeverity || SecurityAlertSeverity.MEDIUM];

    // Verificar se a severidade do alerta é maior ou igual ao mínimo configurado
    return severityLevel[severity] >= minLevel;
  },

  /**
   * Verifica se um canal específico deve ser notificado
   * @param channel - Nome do canal
   * @param severity - Severidade do alerta
   * @param config - Configuração de notificação
   * @returns Verdadeiro se o canal deve ser notificado
   */
  shouldNotifyChannel(channel: string, severity: SecurityAlertSeverity, config: any): boolean {
    // Verificar configuração específica do canal
    const channelConfig = config[channel];

    if (!channelConfig || !channelConfig.enabled) {
      return false;
    }

    // Se o canal tem uma configuração específica de severidade mínima
    if (channelConfig.minSeverity) {
      // Mapear severidade para nível numérico
      const severityLevel = {
        [SecurityAlertSeverity.LOW]: 1,
        [SecurityAlertSeverity.MEDIUM]: 2,
        [SecurityAlertSeverity.HIGH]: 3,
        [SecurityAlertSeverity.CRITICAL]: 4,
      };

      // Mapear nível mínimo configurado para o canal
      const minLevel = {
        [SecurityAlertSeverity.LOW]: 1,
        [SecurityAlertSeverity.MEDIUM]: 2,
        [SecurityAlertSeverity.HIGH]: 3,
        [SecurityAlertSeverity.CRITICAL]: 4,
      }[channelConfig.minSeverity];

      // Verificar se a severidade do alerta é maior ou igual ao mínimo configurado para o canal
      return severityLevel[severity] >= minLevel;
    }

    // Se não há configuração específica, usar a verificação global
    return this.shouldNotifyAlert(severity, config);
  },

  /**
   * Formata mensagem de alerta para notificação
   * @param alertId - ID do alerta
   * @param alertData - Dados do alerta
   * @returns Mensagem formatada
   */
  formatAlertMessage(alertId: string, alertData: SecurityAlertData): string {
    const timestamp = new Date().toLocaleString();

    let message = `
      *Alerta de Segurança: ${alertData.title}*
      
      ID: ${alertId}
      Severidade: ${alertData.severity.toUpperCase()}
      Data/Hora: ${timestamp}
      
      ${alertData.description}
    `;

    // Adicionar detalhes adicionais se disponíveis
    if (alertData.userName) {
      message += `\nUsuário: ${alertData.userName}`;
    }

    if (alertData.ipAddress) {
      message += `\nEndereço IP: ${alertData.ipAddress}`;
    }

    if (alertData.resource) {
      message += `\nRecurso: ${alertData.resource}`;

      if (alertData.resourceId) {
        message += ` (ID: ${alertData.resourceId})`;
      }
    }

    // Adicionar link para visualizar o alerta
    message += `\n\nLink para visualizar: ${process.env.PUBLIC_URL}/admin/security/alerts/${alertId}`;

    return message;
  },

  /**
   * Obtém cor para alerta com base na severidade
   * @param severity - Severidade do alerta
   * @returns Código de cor
   */
  getAlertColor(severity: SecurityAlertSeverity): string {
    switch (severity) {
      case SecurityAlertSeverity.LOW:
        return '#2196F3'; // Azul
      case SecurityAlertSeverity.MEDIUM:
        return '#FF9800'; // Laranja
      case SecurityAlertSeverity.HIGH:
        return '#F44336'; // Vermelho
      case SecurityAlertSeverity.CRITICAL:
        return '#9C27B0'; // Roxo
      default:
        return '#757575'; // Cinza
    }
  },

  /**
   * Envia notificação por e-mail
   * @param recipients - Destinatários
   * @param subject - Assunto
   * @param message - Mensagem
   */
  async sendEmailNotification(
    recipients: string[],
    subject: string,
    message: string
  ): Promise<void> {
    try {
      // Implementar integração com serviço de e-mail
      logger.info('Enviando notificação por e-mail:', { recipients, subject });

      // TODO: Implementar envio de e-mail
    } catch (error) {
      logger.error('Erro ao enviar notificação por e-mail:', error);
    }
  },

  /**
   * Envia notificação para Slack
   * @param webhook - URL do webhook do Slack
   * @param title - Título da mensagem
   * @param message - Mensagem
   * @param color - Cor da barra lateral
   */
  async sendSlackNotification(
    webhook: string,
    title: string,
    message: string,
    color: string
  ): Promise<void> {
    try {
      // Implementar integração com Slack
      logger.info('Enviando notificação para Slack:', { title });

      // TODO: Implementar envio para Slack
    } catch (error) {
      logger.error('Erro ao enviar notificação para Slack:', error);
    }
  },

  /**
   * Envia notificação para webhook personalizado
   * @param url - URL do webhook
   * @param data - Dados a serem enviados
   */
  async sendWebhookNotification(url: string, data: any): Promise<void> {
    try {
      // Implementar envio para webhook
      logger.info('Enviando notificação para webhook:', { url });

      // TODO: Implementar envio para webhook
    } catch (error) {
      logger.error('Erro ao enviar notificação para webhook:', error);
    }
  },

  /**
   * Armazena notificação no sistema para administradores
   * @param alertId - ID do alerta
   * @param alertData - Dados do alerta
   */
  async storeSystemNotification(alertId: string, alertData: SecurityAlertData): Promise<void> {
    try {
      // Implementar armazenamento de notificação no sistema
      logger.info('Armazenando notificação no sistema:', { alertId });

      // TODO: Implementar armazenamento de notificação
    } catch (error) {
      logger.error('Erro ao armazenar notificação no sistema:', error);
    }
  },

  /**
   * Obtém configuração de notificação
   * @returns Configuração de notificação
   */
  async getNotificationConfig(): Promise<any> {
    try {
      // Tentar obter do cache primeiro
      const cachedConfig = await cacheService.get('security:notification_config');

      if (cachedConfig) {
        return JSON.parse(cachedConfig);
      }

      // Se não estiver em cache, obter do banco de dados
      // TODO: Implementar obtenção do banco de dados

      // Configuração padrão
      const defaultConfig = {
        minSeverity: SecurityAlertSeverity.MEDIUM,
        email: {
          enabled: false,
          recipients: [],
          minSeverity: SecurityAlertSeverity.HIGH,
        },
        slack: {
          enabled: false,
          webhook: '',
          minSeverity: SecurityAlertSeverity.MEDIUM,
        },
        webhook: {
          enabled: false,
          url: '',
          minSeverity: SecurityAlertSeverity.HIGH,
        },
      };

      // Armazenar em cache
      await cacheService.set(
        'security:notification_config',
        JSON.stringify(defaultConfig),
        3600 // 1 hora em segundos
      );

      return defaultConfig;
    } catch (error) {
      logger.error('Erro ao obter configuração de notificação:', error);

      // Retornar configuração padrão em caso de erro
      return {
        minSeverity: SecurityAlertSeverity.MEDIUM,
        email: { enabled: false },
        slack: { enabled: false },
        webhook: { enabled: false },
      };
    }
  },

  /**
   * Detecta tentativas de login malsucedidas repetidas
   * @param userId - ID do usuário
   * @param ipAddress - Endereço IP
   * @returns Verdadeiro se um alerta foi criado
   */
  async detectMultipleLoginFailures(
    userId: string | undefined,
    ipAddress: string
  ): Promise<boolean> {
    try {
      // Definir período de verificação (últimos 30 minutos)
      const checkPeriod = 30 * 60 * 1000; // 30 minutos em milissegundos
      const startDate = new Date(Date.now() - checkPeriod);
      const endDate = new Date();

      // Buscar falhas de login recentes
      const loginFailures = await auditRepository.read(
        undefined,
        AuditEventType.LOGIN_FAILURE,
        userId,
        undefined,
        undefined,
        undefined,
        startDate,
        endDate,
        100
      );

      // Filtrar por IP
      const failuresFromIp = loginFailures.rows.filter((event) => event.ip_address === ipAddress);

      // Verificar se atingiu o limite (5 falhas em 30 minutos)
      if (failuresFromIp.length >= 5) {
        // Obter informações do usuário, se disponível
        let userName = undefined;

        if (userId) {
          const userResult = await userRepository.read(userId);
          if (userResult.rowCount > 0) {
            userName = userResult.rows[0].name;
          }
        }

        // Criar alerta
        const alertId = await this.createAlert({
          alertType: SecurityAlertType.MULTIPLE_LOGIN_FAILURES,
          title: 'Múltiplas tentativas de login malsucedidas',
          description: `Foram detectadas ${failuresFromIp.length} tentativas de login malsucedidas em um curto período de tempo.`,
          severity: SecurityAlertSeverity.MEDIUM,
          userId,
          userName,
          ipAddress,
          resource: 'auth',
          relatedEventIds: failuresFromIp.map((event) => event.id),
          metadata: {
            failureCount: failuresFromIp.length,
            timeWindow: '30 minutos',
          },
        });

        return !!alertId;
      }

      return false;
    } catch (error) {
      logger.error('Erro ao detectar múltiplas falhas de login:', error);
      return false;
    }
  },
};
