---
import DaisyButton from '../../../components/ui/DaisyButton.astro';
import DaisyCard from '../../../components/ui/DaisyCard.astro';
import Container from '../../../components/ui/layout/Container.astro';
import Grid from '../../../components/ui/layout/Grid.astro';
import Section from '../../../components/ui/layout/Section.astro';
import Breadcrumbs from '../../../components/ui/navigation/Breadcrumbs.astro';
import BaseLayout from '../../../layouts/BaseLayout.astro';

const title = 'Documentação de Componentes';

// Breadcrumbs para a página atual
const breadcrumbItems = [
  { href: '/', label: 'Iní<PERSON>' },
  { href: '/docs', label: 'Documentação' },
  { label: 'Componentes' },
];

// Categorias de componentes
const categories = [
  {
    title: 'Navegação',
    description: 'Componentes para navegação e estruturação do fluxo do usuário.',
    icon: `<svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7" />
    </svg>`,
    components: ['Navbar', 'Breadcrumbs', 'Tabs', 'Pagination'],
    url: '/docs/components/navigation',
  },
  {
    title: 'Layout',
    description: 'Componentes para estruturação e organização do layout.',
    icon: `<svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 5a1 1 0 011-1h14a1 1 0 011 1v2a1 1 0 01-1 1H5a1 1 0 01-1-1V5zM4 13a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H5a1 1 0 01-1-1v-6zM16 13a1 1 0 011-1h2a1 1 0 011 1v6a1 1 0 01-1 1h-2a1 1 0 01-1-1v-6z" />
    </svg>`,
    components: ['Container', 'Grid', 'Section', 'Divider'],
    url: '/docs/components/layout',
  },
  {
    title: 'Feedback',
    description: 'Componentes para fornecer feedback ao usuário.',
    icon: `<svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
    </svg>`,
    components: ['Alert', 'Toast', 'Progress', 'Skeleton'],
    url: '/docs/components/feedback',
  },
  {
    title: 'Dados',
    description: 'Componentes para exibição e manipulação de dados.',
    icon: `<svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4m0 5c0 2.21-3.582 4-8 4s-8-1.79-8-4" />
    </svg>`,
    components: ['Table', 'Badge', 'Stat', 'Avatar'],
    url: '/docs/components/data',
  },
  {
    title: 'Formulários',
    description: 'Componentes para entrada e manipulação de dados pelo usuário.',
    icon: `<svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
    </svg>`,
    components: ['Input', 'Select', 'Checkbox', 'Radio', 'Textarea', 'Form'],
    url: '/docs/components/forms',
  },
  {
    title: 'Ações',
    description: 'Componentes para interação e ações do usuário.',
    icon: `<svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 15l-2 5L9 9l11 4-5 2zm0 0l5 5M7.188 2.239l.777 2.897M5.136 7.965l-2.898-.777M13.95 4.05l-2.122 2.122m-5.657 5.656l-2.12 2.122" />
    </svg>`,
    components: ['Button', 'Dropdown', 'Modal', 'Drawer', 'Menu'],
    url: '/docs/components/actions',
  },
];
---

<BaseLayout title={title}>
  <Container>
    <Breadcrumbs items={breadcrumbItems} class="my-4" />
    
    <Section title={title} subtitle="Biblioteca de componentes reutilizáveis para o projeto Estação da Alfabetização">
      <div class="mb-8">
        <p>
          A biblioteca de componentes foi desenvolvida utilizando Astro e DaisyUI como base, seguindo as melhores práticas de acessibilidade e design responsivo.
          Os componentes são organizados em categorias para facilitar o uso e a manutenção.
        </p>
        
        <p class="mt-4">
          Selecione uma categoria abaixo para ver a documentação detalhada dos componentes disponíveis.
        </p>
      </div>
      
      <Grid cols={{ sm: 1, md: 2, lg: 3 }} gap={6}>
        {categories.map(category => (
          <DaisyCard>
            <div class="flex items-center mb-4">
              <div class="mr-4 text-primary" set:html={category.icon} />
              <h2 class="card-title">{category.title}</h2>
            </div>
            
            <p class="mb-4">{category.description}</p>
            
            <ul class="mb-4 list-disc list-inside">
              {category.components.map(component => (
                <li>{component}</li>
              ))}
            </ul>
            
            <div class="card-actions justify-end">
              <DaisyButton href={category.url} variant="primary">Ver Documentação</DaisyButton>
            </div>
          </DaisyCard>
        ))}
      </Grid>
      
      <div class="mt-12 p-6 bg-base-200 rounded-box">
        <h2 class="text-2xl font-bold mb-4">Documentação Completa</h2>
        
        <p class="mb-4">
          Para uma documentação completa de todos os componentes, consulte o arquivo:
        </p>
        
        <div class="bg-base-300 p-4 rounded-box">
          <code>docs/COMPONENTS.md</code>
        </div>
        
        <p class="mt-4">
          Este arquivo contém informações detalhadas sobre todos os componentes, suas propriedades, exemplos de uso e boas práticas.
        </p>
        
        <div class="mt-6">
          <DaisyButton href="/docs/COMPONENTS.md" variant="primary">Ver Documentação Completa</DaisyButton>
        </div>
      </div>
    </Section>
  </Container>
</BaseLayout>
