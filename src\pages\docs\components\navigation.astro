---
import DaisyCard from '../../../components/ui/DaisyCard.astro';
import Container from '../../../components/ui/layout/Container.astro';
import Divider from '../../../components/ui/layout/Divider.astro';
import Grid from '../../../components/ui/layout/Grid.astro';
import Section from '../../../components/ui/layout/Section.astro';
import Breadcrumbs from '../../../components/ui/navigation/Breadcrumbs.astro';
import Navbar from '../../../components/ui/navigation/Navbar.astro';
import Pagination from '../../../components/ui/navigation/Pagination.astro';
import Tabs from '../../../components/ui/navigation/Tabs.astro';
import BaseLayout from '../../../layouts/BaseLayout.astro';

const title = 'Componentes de Navegação';

// Breadcrumbs para a página atual
const breadcrumbItems = [
  { href: '/', label: 'Início' },
  { href: '/docs', label: 'Documentação' },
  { href: '/docs/components', label: 'Componentes' },
  { label: 'Navegação' },
];

// Exemplos de código
const navbarCode = `import Navbar from '../components/ui/navigation/Navbar.astro';

<Navbar 
  logo={{ text: 'Estação da Alfabetização', src: '/logo.png' }}
  links={[
    { href: '/', label: 'Início', isActive: true },
    { href: '/sobre', label: 'Sobre' },
    { href: '/contato', label: 'Contato' }
  ]}
  actions={[<ThemeSwitcher />]}
  sticky
  bordered
/>`;

const breadcrumbsCode = `import Breadcrumbs from '../components/ui/navigation/Breadcrumbs.astro';

<Breadcrumbs 
  items={[
    { href: '/', label: 'Início' },
    { href: '/exemplos', label: 'Exemplos' },
    { label: 'Componentes' }
  ]}
  homeIcon
/>`;

const tabsCode = `import Tabs from '../components/ui/navigation/Tabs.astro';

<Tabs 
  tabs={[
    { id: 'tab1', label: 'Aba 1', isActive: true },
    { id: 'tab2', label: 'Aba 2' },
    { id: 'tab3', label: 'Aba 3' }
  ]}
  variant="boxed"
>
  <div slot="tab1">Conteúdo da Aba 1</div>
  <div slot="tab2">Conteúdo da Aba 2</div>
  <div slot="tab3">Conteúdo da Aba 3</div>
</Tabs>`;

const paginationCode = `import Pagination from '../components/ui/navigation/Pagination.astro';

<Pagination 
  currentPage={3} 
  totalPages={10} 
  baseUrl="/blog" 
  showFirst 
  showLast 
/>`;
---

<BaseLayout title={title}>
  <Container>
    <Breadcrumbs items={breadcrumbItems} class="my-4" />
    
    <Section title={title} subtitle="Componentes para navegação e estruturação do fluxo do usuário">
      <div class="mb-8">
        <p>
          Os componentes de navegação são essenciais para criar uma experiência de usuário intuitiva e eficiente.
          Eles ajudam os usuários a se orientarem no site, entenderem onde estão e para onde podem ir.
        </p>
      </div>
      
      <Divider />
      
      <!-- Navbar -->
      <div id="navbar" class="pt-4 mb-12">
        <h2 class="text-2xl font-bold mb-4">Navbar</h2>
        
        <p class="mb-4">
          Barra de navegação responsiva com suporte a logo, links e ações.
        </p>
        
        <Grid cols={{ sm: 1, md: 2 }} gap={6} class="mb-6">
          <div>
            <h3 class="text-xl font-bold mb-2">Exemplo</h3>
            <div class="border rounded-box p-4 bg-base-200">
              <Navbar 
                links={[
                  { href: "#", label: "Início", isActive: true },
                  { href: "#", label: "Sobre" },
                  { href: "#", label: "Contato" }
                ]}
                bordered
                class="bg-base-100"
              />
            </div>
          </div>
          
          <div>
            <h3 class="text-xl font-bold mb-2">Código</h3>
            <pre class="bg-base-300 p-4 rounded-box overflow-x-auto text-sm"><code>{navbarCode}</code></pre>
          </div>
        </Grid>
        
        <h3 class="text-xl font-bold mb-2">Props</h3>
        <div class="overflow-x-auto mb-6">
          <table class="table table-zebra w-full">
            <thead>
              <tr>
                <th>Prop</th>
                <th>Tipo</th>
                <th>Padrão</th>
                <th>Descrição</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td><code>logo</code></td>
                <td>Objeto</td>
                <td><code>{ text: 'Estação da Alfabetização' }</code></td>
                <td>Objeto com propriedades <code>src</code>, <code>alt</code> e <code>text</code></td>
              </tr>
              <tr>
                <td><code>links</code></td>
                <td>Array</td>
                <td><code>[]</code></td>
                <td>Array de objetos com propriedades <code>href</code>, <code>label</code>, <code>isActive</code> e <code>icon</code></td>
              </tr>
              <tr>
                <td><code>actions</code></td>
                <td>Array</td>
                <td><code>[]</code></td>
                <td>Array de elementos a serem renderizados na parte direita</td>
              </tr>
              <tr>
                <td><code>transparent</code></td>
                <td>Boolean</td>
                <td><code>false</code></td>
                <td>Define se o fundo é transparente</td>
              </tr>
              <tr>
                <td><code>sticky</code></td>
                <td>Boolean</td>
                <td><code>false</code></td>
                <td>Define se a navbar fica fixa no topo</td>
              </tr>
              <tr>
                <td><code>bordered</code></td>
                <td>Boolean</td>
                <td><code>false</code></td>
                <td>Adiciona borda inferior</td>
              </tr>
              <tr>
                <td><code>rounded</code></td>
                <td>Boolean</td>
                <td><code>false</code></td>
                <td>Adiciona cantos arredondados</td>
              </tr>
              <tr>
                <td><code>class</code></td>
                <td>String</td>
                <td><code>''</code></td>
                <td>Classes adicionais</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
      
      <Divider />
      
      <!-- Breadcrumbs -->
      <div id="breadcrumbs" class="pt-4 mb-12">
        <h2 class="text-2xl font-bold mb-4">Breadcrumbs</h2>
        
        <p class="mb-4">
          Navegação estrutural que mostra o caminho atual.
        </p>
        
        <Grid cols={{ sm: 1, md: 2 }} gap={6} class="mb-6">
          <div>
            <h3 class="text-xl font-bold mb-2">Exemplo</h3>
            <div class="border rounded-box p-4 bg-base-200">
              <Breadcrumbs items={breadcrumbItems} />
            </div>
          </div>
          
          <div>
            <h3 class="text-xl font-bold mb-2">Código</h3>
            <pre class="bg-base-300 p-4 rounded-box overflow-x-auto text-sm"><code>{breadcrumbsCode}</code></pre>
          </div>
        </Grid>
        
        <h3 class="text-xl font-bold mb-2">Props</h3>
        <div class="overflow-x-auto mb-6">
          <table class="table table-zebra w-full">
            <thead>
              <tr>
                <th>Prop</th>
                <th>Tipo</th>
                <th>Padrão</th>
                <th>Descrição</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td><code>items</code></td>
                <td>Array</td>
                <td><code>[]</code></td>
                <td>Array de objetos com propriedades <code>href</code>, <code>label</code> e <code>icon</code></td>
              </tr>
              <tr>
                <td><code>homeIcon</code></td>
                <td>Boolean</td>
                <td><code>true</code></td>
                <td>Mostra ícone de casa no início</td>
              </tr>
              <tr>
                <td><code>separator</code></td>
                <td>String</td>
                <td><code>/</code></td>
                <td>Separador personalizado</td>
              </tr>
              <tr>
                <td><code>class</code></td>
                <td>String</td>
                <td><code>''</code></td>
                <td>Classes adicionais</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
      
      <Divider />
      
      <!-- Tabs -->
      <div id="tabs" class="pt-4 mb-12">
        <h2 class="text-2xl font-bold mb-4">Tabs</h2>
        
        <p class="mb-4">
          Abas para alternar entre diferentes seções de conteúdo.
        </p>
        
        <Grid cols={{ sm: 1, md: 2 }} gap={6} class="mb-6">
          <div>
            <h3 class="text-xl font-bold mb-2">Exemplo</h3>
            <div class="border rounded-box p-4 bg-base-200">
              <Tabs 
                tabs={[
                  { id: "tab1", label: "Aba 1", isActive: true },
                  { id: "tab2", label: "Aba 2" },
                  { id: "tab3", label: "Aba 3" }
                ]}
              >
                <div slot="tab1">Conteúdo da Aba 1</div>
                <div slot="tab2">Conteúdo da Aba 2</div>
                <div slot="tab3">Conteúdo da Aba 3</div>
              </Tabs>
            </div>
          </div>
          
          <div>
            <h3 class="text-xl font-bold mb-2">Código</h3>
            <pre class="bg-base-300 p-4 rounded-box overflow-x-auto text-sm"><code>{tabsCode}</code></pre>
          </div>
        </Grid>
        
        <h3 class="text-xl font-bold mb-2">Props</h3>
        <div class="overflow-x-auto mb-6">
          <table class="table table-zebra w-full">
            <thead>
              <tr>
                <th>Prop</th>
                <th>Tipo</th>
                <th>Padrão</th>
                <th>Descrição</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td><code>tabs</code></td>
                <td>Array</td>
                <td><code>[]</code></td>
                <td>Array de objetos com propriedades <code>id</code>, <code>label</code>, <code>icon</code>, <code>isActive</code> e <code>disabled</code></td>
              </tr>
              <tr>
                <td><code>variant</code></td>
                <td>String</td>
                <td><code>'default'</code></td>
                <td>Variante de estilo: <code>'default'</code>, <code>'boxed'</code> ou <code>'lifted'</code></td>
              </tr>
              <tr>
                <td><code>size</code></td>
                <td>String</td>
                <td><code>'md'</code></td>
                <td>Tamanho: <code>'xs'</code>, <code>'sm'</code>, <code>'md'</code> ou <code>'lg'</code></td>
              </tr>
              <tr>
                <td><code>fullWidth</code></td>
                <td>Boolean</td>
                <td><code>false</code></td>
                <td>Ocupa toda a largura disponível</td>
              </tr>
              <tr>
                <td><code>class</code></td>
                <td>String</td>
                <td><code>''</code></td>
                <td>Classes adicionais para as abas</td>
              </tr>
              <tr>
                <td><code>contentClass</code></td>
                <td>String</td>
                <td><code>''</code></td>
                <td>Classes adicionais para o conteúdo</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
      
      <Divider />
      
      <!-- Pagination -->
      <div id="pagination" class="pt-4 mb-12">
        <h2 class="text-2xl font-bold mb-4">Pagination</h2>
        
        <p class="mb-4">
          Controles de navegação para conteúdo paginado.
        </p>
        
        <Grid cols={{ sm: 1, md: 2 }} gap={6} class="mb-6">
          <div>
            <h3 class="text-xl font-bold mb-2">Exemplo</h3>
            <div class="border rounded-box p-4 bg-base-200">
              <Pagination currentPage={3} totalPages={10} />
            </div>
          </div>
          
          <div>
            <h3 class="text-xl font-bold mb-2">Código</h3>
            <pre class="bg-base-300 p-4 rounded-box overflow-x-auto text-sm"><code>{paginationCode}</code></pre>
          </div>
        </Grid>
        
        <h3 class="text-xl font-bold mb-2">Props</h3>
        <div class="overflow-x-auto mb-6">
          <table class="table table-zebra w-full">
            <thead>
              <tr>
                <th>Prop</th>
                <th>Tipo</th>
                <th>Padrão</th>
                <th>Descrição</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td><code>currentPage</code></td>
                <td>Number</td>
                <td><code>1</code></td>
                <td>Número da página atual</td>
              </tr>
              <tr>
                <td><code>totalPages</code></td>
                <td>Number</td>
                <td><code>1</code></td>
                <td>Número total de páginas</td>
              </tr>
              <tr>
                <td><code>baseUrl</code></td>
                <td>String</td>
                <td><code>''</code></td>
                <td>URL base para links de página</td>
              </tr>
              <tr>
                <td><code>showFirst</code></td>
                <td>Boolean</td>
                <td><code>true</code></td>
                <td>Mostra botão de primeira página</td>
              </tr>
              <tr>
                <td><code>showLast</code></td>
                <td>Boolean</td>
                <td><code>true</code></td>
                <td>Mostra botão de última página</td>
              </tr>
              <tr>
                <td><code>showPrevNext</code></td>
                <td>Boolean</td>
                <td><code>true</code></td>
                <td>Mostra botões de anterior/próximo</td>
              </tr>
              <tr>
                <td><code>maxVisiblePages</code></td>
                <td>Number</td>
                <td><code>5</code></td>
                <td>Número máximo de páginas visíveis</td>
              </tr>
              <tr>
                <td><code>class</code></td>
                <td>String</td>
                <td><code>''</code></td>
                <td>Classes adicionais</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </Section>
  </Container>
</BaseLayout>
