/**
 * Serviço de registro de esquemas
 *
 * Este serviço fornece funcionalidades para gerenciamento de esquemas de mensagens,
 * incluindo registro, versionamento e compatibilidade.
 */

import { queryHelper } from '@db/queryHelper';
import { cacheService } from '@services/cacheService';
import { schemaValidationService } from '@services/schemaValidationService';
import { logger } from '@utils/logger';
import Ajv, { JSONSchemaType } from 'ajv';
import { v4 as uuidv4 } from 'uuid';

// Instância do validador AJV
const ajv = new Ajv({
  allErrors: true,
  verbose: true,
});

/**
 * Tipo de compatibilidade de esquema
 */
export enum SchemaCompatibility {
  /**
   * Sem verificação de compatibilidade
   */
  NONE = 'NONE',

  /**
   * Compatibilidade para frente (novos esquemas podem ler dados antigos)
   */
  FORWARD = 'FORWARD',

  /**
   * Compatibilidade para trás (esquemas antigos podem ler dados novos)
   */
  BACKWARD = 'BACKWARD',

  /**
   * Compatibilidade total (para frente e para trás)
   */
  FULL = 'FULL',
}

/**
 * Informações de esquema
 */
export interface SchemaInfo {
  /**
   * ID do esquema
   */
  id: string;

  /**
   * Nome do esquema
   */
  name: string;

  /**
   * Versão do esquema
   */
  version: number;

  /**
   * Definição do esquema
   */
  definition: Record<string, unknown>;

  /**
   * Tipo de compatibilidade
   */
  compatibility: SchemaCompatibility;

  /**
   * Se o esquema está ativo
   */
  isActive: boolean;

  /**
   * Data de criação
   */
  createdAt: Date;

  /**
   * Data de atualização
   */
  updatedAt: Date;
}

/**
 * Opções para registro de esquema
 */
export interface SchemaRegistrationOptions {
  /**
   * Tipo de compatibilidade
   */
  compatibility?: SchemaCompatibility;

  /**
   * Se deve validar o esquema
   */
  validateSchema?: boolean;

  /**
   * Se deve verificar compatibilidade
   */
  checkCompatibility?: boolean;
}

/**
 * Serviço de registro de esquemas
 */
export const schemaRegistryService = {
  /**
   * Registra um novo esquema
   * @param name - Nome do esquema
   * @param definition - Definição do esquema
   * @param options - Opções de registro
   * @returns Informações do esquema registrado
   */
  async registerSchema(
    name: string,
    definition: Record<string, unknown>,
    options: SchemaRegistrationOptions = {}
  ): Promise<SchemaInfo> {
    try {
      // Validar nome do esquema
      if (!name || name.trim() === '') {
        throw new Error('Nome do esquema é obrigatório');
      }

      // Validar definição do esquema
      if (!definition || Object.keys(definition).length === 0) {
        throw new Error('Definição do esquema é obrigatória');
      }

      // Validar esquema com AJV
      if (options.validateSchema !== false) {
        try {
          ajv.compile(definition as JSONSchemaType<any>);
        } catch (validationError) {
          logger.error(`Esquema inválido ${name}:`, validationError);
          throw new Error(
            `Esquema inválido: ${validationError instanceof Error ? validationError.message : 'Erro desconhecido'}`
          );
        }
      }

      // Obter última versão do esquema
      const latestVersion = await this.getLatestSchemaVersion(name);
      const newVersion = latestVersion + 1;

      // Verificar compatibilidade se necessário
      if (options.checkCompatibility !== false && latestVersion > 0) {
        const latestSchema = await this.getSchema(name, latestVersion);

        if (latestSchema) {
          const compatibility = options.compatibility || SchemaCompatibility.BACKWARD;
          const isCompatible = await this.checkCompatibility(
            definition,
            latestSchema.definition,
            compatibility
          );

          if (!isCompatible) {
            throw new Error(`Esquema incompatível com a versão anterior (${compatibility})`);
          }
        }
      }

      // Gerar ID do esquema
      const schemaId = uuidv4();

      // Inserir esquema no banco de dados
      await queryHelper.query(
        `INSERT INTO tab_message_schema (
          schema_id, schema_name, schema_version,
          schema_definition, compatibility, is_active,
          created_at, updated_at
        ) VALUES (
          $1, $2, $3, $4, $5, $6, NOW(), NOW()
        )`,
        [
          schemaId,
          name,
          newVersion,
          JSON.stringify(definition),
          options.compatibility || SchemaCompatibility.BACKWARD,
          true,
        ]
      );

      // Limpar cache
      await cacheService.delete(`schema:${name}:${newVersion}`);
      await cacheService.delete(`schema:${name}:latest`);

      // Invalidar cache do validador
      schemaValidationService.validators.delete(`${name}:${newVersion}`);

      // Retornar informações do esquema
      return {
        id: schemaId,
        name,
        version: newVersion,
        definition,
        compatibility: options.compatibility || SchemaCompatibility.BACKWARD,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      };
    } catch (error) {
      logger.error(`Erro ao registrar esquema ${name}:`, error);
      throw error;
    }
  },

  /**
   * Obtém um esquema específico
   * @param name - Nome do esquema
   * @param version - Versão do esquema (opcional, usa a mais recente se não fornecido)
   * @returns Informações do esquema
   */
  async getSchema(name: string, version?: number): Promise<SchemaInfo | null> {
    try {
      // Verificar cache
      const cacheKey = `schema:${name}:${version || 'latest'}`;
      const cachedSchema = await cacheService.get(cacheKey);

      if (cachedSchema) {
        return JSON.parse(cachedSchema);
      }

      // Consultar banco de dados
      let query = `
        SELECT
          schema_id, schema_name, schema_version,
          schema_definition, compatibility, is_active,
          created_at, updated_at
        FROM tab_message_schema
        WHERE schema_name = $1
      `;

      const params = [name];

      if (version) {
        query += ' AND schema_version = $2';
        params.push(version);
      } else {
        query += ' ORDER BY schema_version DESC LIMIT 1';
      }

      const result = await queryHelper.queryOne(query, params);

      if (!result) {
        return null;
      }

      // Converter resultado
      const schemaInfo: SchemaInfo = {
        id: result.schema_id,
        name: result.schema_name,
        version: result.schema_version,
        definition: JSON.parse(result.schema_definition),
        compatibility: result.compatibility as SchemaCompatibility,
        isActive: result.is_active,
        createdAt: result.created_at,
        updatedAt: result.updated_at,
      };

      // Armazenar em cache
      await cacheService.set(cacheKey, JSON.stringify(schemaInfo), 3600); // 1 hora

      return schemaInfo;
    } catch (error) {
      logger.error(`Erro ao obter esquema ${name}:`, error);
      return null;
    }
  },

  /**
   * Obtém a versão mais recente de um esquema
   * @param name - Nome do esquema
   * @returns Versão mais recente do esquema
   */
  async getLatestSchemaVersion(name: string): Promise<number> {
    try {
      const result = await queryHelper.queryOne(
        `SELECT MAX(schema_version) as latest_version
         FROM tab_message_schema
         WHERE schema_name = $1`,
        [name]
      );

      return result?.latest_version || 0;
    } catch (error) {
      logger.error(`Erro ao obter versão mais recente do esquema ${name}:`, error);
      return 0;
    }
  },

  /**
   * Verifica compatibilidade entre esquemas
   * @param newSchema - Novo esquema
   * @param oldSchema - Esquema antigo
   * @param compatibility - Tipo de compatibilidade
   * @returns Verdadeiro se os esquemas são compatíveis
   */
  async checkCompatibility(
    newSchema: Record<string, unknown>,
    oldSchema: Record<string, unknown>,
    compatibility: SchemaCompatibility
  ): Promise<boolean> {
    try {
      // Se não houver verificação de compatibilidade, retornar verdadeiro
      if (compatibility === SchemaCompatibility.NONE) {
        return true;
      }

      // Implementação básica de verificação de compatibilidade
      // Em uma implementação real, seria necessário um algoritmo mais sofisticado

      // Verificar compatibilidade para frente (novos esquemas podem ler dados antigos)
      if (
        compatibility === SchemaCompatibility.FORWARD ||
        compatibility === SchemaCompatibility.FULL
      ) {
        // Verificar se novos esquemas podem ler dados antigos
        // Isso significa que todos os campos obrigatórios no novo esquema devem existir no esquema antigo
        const newRequired = (newSchema.required as string[]) || [];
        const oldProperties = oldSchema.properties as Record<string, unknown>;

        for (const field of newRequired) {
          if (!oldProperties || !oldProperties[field]) {
            return false;
          }
        }
      }

      // Verificar compatibilidade para trás (esquemas antigos podem ler dados novos)
      if (
        compatibility === SchemaCompatibility.BACKWARD ||
        compatibility === SchemaCompatibility.FULL
      ) {
        // Verificar se esquemas antigos podem ler dados novos
        // Isso significa que todos os campos obrigatórios no esquema antigo devem existir no novo esquema
        const oldRequired = (oldSchema.required as string[]) || [];
        const newProperties = newSchema.properties as Record<string, unknown>;

        for (const field of oldRequired) {
          if (!newProperties || !newProperties[field]) {
            return false;
          }
        }
      }

      return true;
    } catch (error) {
      logger.error('Erro ao verificar compatibilidade de esquemas:', error);
      return false;
    }
  },
};
