# Webhooks

## Vis<PERSON> Geral

Os webhooks permitem que sua aplicação receba notificações em tempo real quando eventos específicos ocorrem em nossa plataforma. Em vez de fazer polling constante para verificar mudanças, você pode configurar endpoints que serão chamados automaticamente quando eventos relevantes acontecerem.

## Eventos Disponíveis

Nossa plataforma suporta os seguintes eventos de webhook:

### Pagamentos
- `payment.created` - Um novo pagamento foi criado
- `payment.updated` - Um pagamento foi atualizado
- `payment.completed` - Um pagamento foi concluído com sucesso
- `payment.failed` - Um pagamento falhou
- `payment.refunded` - Um pagamento foi reembolsado

### Pedidos
- `order.created` - Um novo pedido foi criado
- `order.updated` - Um pedido foi atualizado
- `order.completed` - Um pedido foi concluído
- `order.cancelled` - Um pedido foi cancelado

### Us<PERSON><PERSON><PERSON>s
- `user.created` - Um novo usuário foi criado
- `user.updated` - Um usuário foi atualizado

### Produtos
- `product.created` - Um novo produto foi criado
- `product.updated` - Um produto foi atualizado
- `product.deleted` - Um produto foi excluído

## Formato do Payload

Todos os webhooks são enviados como requisições HTTP POST com um payload JSON. O formato do payload é consistente para todos os eventos:

```json
{
  "id": "webhook-delivery-uuid",
  "event": "payment.completed",
  "timestamp": 1619712000,
  "data": {
    // Dados específicos do evento
  }
}
```

| Campo | Descrição |
|-------|-----------|
| `id` | Identificador único da entrega do webhook |
| `event` | Tipo de evento que acionou o webhook |
| `timestamp` | Timestamp Unix (em segundos) de quando o evento ocorreu |
| `data` | Objeto contendo os dados específicos do evento |

## Segurança

### Assinatura de Webhook

Para garantir que os webhooks recebidos são legítimos e não foram adulterados, cada requisição inclui uma assinatura HMAC SHA-256 no cabeçalho `X-Webhook-Signature`. Esta assinatura é gerada usando a chave secreta fornecida durante a criação da assinatura de webhook.

Para verificar a assinatura:

1. Obtenha o payload bruto da requisição como string
2. Obtenha o valor do cabeçalho `X-Webhook-Signature`
3. Calcule o HMAC SHA-256 do payload usando sua chave secreta
4. Compare o resultado com o valor do cabeçalho

Exemplo em Node.js:

```javascript
const crypto = require('crypto');

function verifyWebhookSignature(payload, signature, secret) {
  const expectedSignature = crypto
    .createHmac('sha256', secret)
    .update(payload)
    .digest('hex');
  
  return crypto.timingSafeEqual(
    Buffer.from(signature, 'hex'),
    Buffer.from(expectedSignature, 'hex')
  );
}
```

### Cabeçalhos Adicionais

Cada requisição de webhook inclui os seguintes cabeçalhos:

| Cabeçalho | Descrição |
|-----------|-----------|
| `Content-Type` | Sempre `application/json` |
| `X-Webhook-Signature` | Assinatura HMAC SHA-256 do payload |
| `X-Webhook-Event` | Tipo de evento (ex: `payment.completed`) |
| `X-Webhook-ID` | ID único da entrega do webhook |
| `X-Webhook-Timestamp` | Timestamp Unix (em segundos) |

## Melhores Práticas

### Resposta Rápida

Seu endpoint deve responder rapidamente às requisições de webhook, idealmente em menos de 5 segundos. Se o processamento for demorado, recomendamos:

1. Armazenar o payload em uma fila
2. Responder imediatamente com um código 200
3. Processar o evento de forma assíncrona

### Tratamento de Falhas

Se nosso sistema não receber uma resposta 2xx, tentaremos reenviar o webhook até 3 vezes com intervalos crescentes. Certifique-se de que seu endpoint seja idempotente para lidar com entregas duplicadas.

### Verificação de Assinatura

Sempre verifique a assinatura do webhook para garantir que a requisição é legítima e não foi adulterada.

### Validação de Timestamp

Recomendamos verificar o timestamp do webhook para evitar ataques de replay. Considere rejeitar webhooks com mais de 5 minutos de idade.

## API de Webhooks

Nossa API permite gerenciar assinaturas de webhook programaticamente:

### Criar Assinatura

```
POST /api/webhooks/subscriptions
```

Corpo da requisição:
```json
{
  "name": "Meu Webhook",
  "url": "https://exemplo.com/webhooks",
  "events": ["payment.completed", "payment.failed"],
  "secretKey": "chave-secreta-opcional",
  "description": "Descrição opcional",
  "headers": {
    "X-Custom-Header": "Valor personalizado"
  }
}
```

### Listar Assinaturas

```
GET /api/webhooks/subscriptions
```

### Obter Assinatura

```
GET /api/webhooks/subscriptions/{id}
```

### Atualizar Assinatura

```
PUT /api/webhooks/subscriptions/{id}
```

### Excluir Assinatura

```
DELETE /api/webhooks/subscriptions/{id}
```

### Listar Entregas

```
GET /api/webhooks/subscriptions/{id}/deliveries
```

### Testar Webhook

```
POST /api/webhooks/test
```

Corpo da requisição:
```json
{
  "subscriptionId": "id-da-assinatura",
  "event": "payment.completed",
  "payload": {
    "test": true,
    "customData": "valor personalizado"
  }
}
```

## Solução de Problemas

### Webhook não está sendo recebido

1. Verifique se a URL está correta e acessível publicamente
2. Verifique se a assinatura está ativa
3. Verifique os logs de entrega na interface administrativa
4. Certifique-se de que seu servidor não está bloqueando requisições POST

### Falha na verificação de assinatura

1. Verifique se está usando a chave secreta correta
2. Certifique-se de que está calculando a assinatura sobre o payload bruto
3. Verifique se não há transformação do payload antes da verificação

### Processamento duplicado de webhooks

1. Implemente idempotência usando o ID do webhook
2. Armazene IDs de webhooks processados para evitar processamento duplicado
