/**
 * Email Notification Service
 *
 * Implementação do serviço de notificações por email.
 * Parte da implementação da tarefa 8.7.3 - Portal do cliente para documentos fiscais
 */

import nodemailer from 'nodemailer';
import {
  EmailNotification,
  InAppNotification,
  NotificationService,
  PushNotification,
  SmsNotification,
} from '../../domain/services/NotificationService';

export interface EmailConfig {
  host: string;
  port: number;
  secure: boolean;
  auth: {
    user: string;
    pass: string;
  };
  from: string;
}

export class EmailNotificationService implements NotificationService {
  private transporter: nodemailer.Transporter;
  private config: EmailConfig;
  private inAppNotifications: Map<string, InAppNotification[]> = new Map();

  constructor(config: EmailConfig) {
    this.config = config;

    // Inicializar transporter
    this.transporter = nodemailer.createTransport({
      host: config.host,
      port: config.port,
      secure: config.secure,
      auth: {
        user: config.auth.user,
        pass: config.auth.pass,
      },
    });
  }

  /**
   * Envia uma notificação por email
   */
  async sendEmail(notification: EmailNotification): Promise<boolean> {
    try {
      const to = Array.isArray(notification.to) ? notification.to.join(', ') : notification.to;
      const cc = notification.cc
        ? Array.isArray(notification.cc)
          ? notification.cc.join(', ')
          : notification.cc
        : undefined;
      const bcc = notification.bcc
        ? Array.isArray(notification.bcc)
          ? notification.bcc.join(', ')
          : notification.bcc
        : undefined;

      const attachments = notification.attachments?.map((attachment) => ({
        filename: attachment.filename,
        content: attachment.content,
        contentType: attachment.contentType,
      }));

      const result = await this.transporter.sendMail({
        from: this.config.from,
        to,
        cc,
        bcc,
        subject: notification.subject,
        html: notification.body,
        attachments,
      });

      console.log('Email enviado:', result.messageId);

      return true;
    } catch (error) {
      console.error('Erro ao enviar email:', error);
      return false;
    }
  }

  /**
   * Envia uma notificação por SMS
   */
  async sendSms(notification: SmsNotification): Promise<boolean> {
    // Em um cenário real, integraria com um serviço de SMS
    console.log('Enviando SMS para:', notification.to);
    console.log('Mensagem:', notification.message);

    return true;
  }

  /**
   * Envia uma notificação push
   */
  async sendPush(notification: PushNotification): Promise<boolean> {
    // Em um cenário real, integraria com um serviço de push notifications
    console.log('Enviando push para:', notification.userId);
    console.log('Título:', notification.title);
    console.log('Corpo:', notification.body);

    return true;
  }

  /**
   * Envia uma notificação in-app
   */
  async sendInApp(notification: InAppNotification): Promise<boolean> {
    try {
      const userIds = Array.isArray(notification.userId)
        ? notification.userId
        : [notification.userId];

      for (const userId of userIds) {
        const userNotifications = this.inAppNotifications.get(userId) || [];

        const newNotification: InAppNotification = {
          ...notification,
          userId,
          read: false,
        };

        userNotifications.push(newNotification);

        this.inAppNotifications.set(userId, userNotifications);
      }

      return true;
    } catch (error) {
      console.error('Erro ao enviar notificação in-app:', error);
      return false;
    }
  }

  /**
   * Obtém notificações in-app de um usuário
   */
  async getInAppNotifications(
    userId: string,
    options?: {
      unreadOnly?: boolean;
      limit?: number;
      offset?: number;
    }
  ): Promise<{
    notifications: InAppNotification[];
    total: number;
  }> {
    try {
      const userNotifications = this.inAppNotifications.get(userId) || [];

      let filteredNotifications = userNotifications;

      if (options?.unreadOnly) {
        filteredNotifications = filteredNotifications.filter((notification) => !notification.read);
      }

      const total = filteredNotifications.length;

      // Aplicar paginação
      if (options?.offset !== undefined && options?.limit !== undefined) {
        filteredNotifications = filteredNotifications.slice(
          options.offset,
          options.offset + options.limit
        );
      }

      return {
        notifications: filteredNotifications,
        total,
      };
    } catch (error) {
      console.error('Erro ao obter notificações in-app:', error);

      return {
        notifications: [],
        total: 0,
      };
    }
  }

  /**
   * Marca uma notificação in-app como lida
   */
  async markInAppNotificationAsRead(notificationId: string): Promise<boolean> {
    try {
      for (const [userId, notifications] of this.inAppNotifications.entries()) {
        const notificationIndex = notifications.findIndex((n) => n.userId === notificationId);

        if (notificationIndex !== -1) {
          notifications[notificationIndex].read = true;
          this.inAppNotifications.set(userId, notifications);
          return true;
        }
      }

      return false;
    } catch (error) {
      console.error('Erro ao marcar notificação como lida:', error);
      return false;
    }
  }

  /**
   * Marca todas as notificações in-app de um usuário como lidas
   */
  async markAllInAppNotificationsAsRead(userId: string): Promise<boolean> {
    try {
      const userNotifications = this.inAppNotifications.get(userId) || [];

      const updatedNotifications = userNotifications.map((notification) => ({
        ...notification,
        read: true,
      }));

      this.inAppNotifications.set(userId, updatedNotifications);

      return true;
    } catch (error) {
      console.error('Erro ao marcar todas as notificações como lidas:', error);
      return false;
    }
  }

  /**
   * Exclui uma notificação in-app
   */
  async deleteInAppNotification(notificationId: string): Promise<boolean> {
    try {
      for (const [userId, notifications] of this.inAppNotifications.entries()) {
        const updatedNotifications = notifications.filter((n) => n.userId !== notificationId);

        if (updatedNotifications.length !== notifications.length) {
          this.inAppNotifications.set(userId, updatedNotifications);
          return true;
        }
      }

      return false;
    } catch (error) {
      console.error('Erro ao excluir notificação:', error);
      return false;
    }
  }
}
