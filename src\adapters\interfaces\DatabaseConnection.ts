/**
 * Interface para conexão com banco de dados
 *
 * Esta interface define os métodos necessários para uma conexão com banco de dados.
 * Seguindo o princípio de inversão de dependência, a camada de adaptadores define a interface
 * que será implementada pela camada de infraestrutura.
 */

import { type DatabaseRow, type UnknownRecord } from '../../types/common';

/**
 * Resultado de uma consulta
 */
export interface QueryResult {
  rows: DatabaseRow[];
  rowCount: number;
  fields?: UnknownRecord[];
}

/**
 * Interface para conexão com banco de dados
 */
export interface DatabaseConnection {
  /**
   * Executa uma consulta SQL
   *
   * @param text Texto da consulta
   * @param params Parâmetros da consulta
   * @returns Resultado da consulta
   */
  query(text: string, params?: unknown[]): Promise<QueryResult>;

  /**
   * Inicia uma transação
   *
   * @returns Cliente de transação
   */
  beginTransaction(): Promise<TransactionClient>;

  /**
   * Verifica se a conexão está ativa
   *
   * @returns Verdadeiro se a conexão estiver ativa
   */
  isConnected(): Promise<boolean>;

  /**
   * Fecha a conexão com o banco de dados
   */
  disconnect(): Promise<void>;
}

/**
 * Interface para cliente de transação
 */
export interface TransactionClient {
  /**
   * Executa uma consulta SQL dentro da transação
   *
   * @param text Texto da consulta
   * @param params Parâmetros da consulta
   * @returns Resultado da consulta
   */
  query(text: string, params?: unknown[]): Promise<QueryResult>;

  /**
   * Confirma a transação
   */
  commit(): Promise<void>;

  /**
   * Reverte a transação
   */
  rollback(): Promise<void>;

  /**
   * Libera o cliente de transação
   */
  release(): Promise<void>;
}
