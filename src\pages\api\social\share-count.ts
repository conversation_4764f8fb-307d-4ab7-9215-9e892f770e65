/**
 * Share Count API
 *
 * Endpoint para obter contagem de compartilhamentos em redes sociais.
 * Parte da implementação da tarefa 8.9.2 - Integração com redes sociais
 */

import type { APIRoute } from 'astro';
import axios from 'axios';

export const GET: APIRoute = async ({ request }) => {
  try {
    // Obter parâmetros da URL
    const url = new URL(request.url);
    const targetUrl = url.searchParams.get('url');
    const platform = url.searchParams.get('platform');

    if (!targetUrl) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Parâmetro url é obrigatório.',
        }),
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json',
            'Cache-Control': 'no-cache, no-store, must-revalidate',
          },
        }
      );
    }

    if (!platform || !['facebook', 'twitter', 'linkedin', 'pinterest'].includes(platform)) {
      return new Response(
        JSON.stringify({
          success: false,
          error:
            'Plataforma inválida. Plataformas suportadas: facebook, twitter, linkedin, pinterest.',
        }),
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json',
            'Cache-Control': 'no-cache, no-store, must-revalidate',
          },
        }
      );
    }

    // Obter contagem de compartilhamentos
    let count = 0;

    switch (platform) {
      case 'facebook':
        count = await getFacebookShareCount(targetUrl);
        break;

      case 'twitter':
        // Twitter não oferece mais API pública para contagem de compartilhamentos
        count = 0;
        break;

      case 'linkedin':
        // LinkedIn não oferece mais API pública para contagem de compartilhamentos
        count = 0;
        break;

      case 'pinterest':
        count = await getPinterestShareCount(targetUrl);
        break;
    }

    return new Response(
      JSON.stringify({
        success: true,
        data: {
          url: targetUrl,
          platform,
          count,
        },
      }),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'public, max-age=300',
        },
      }
    );
  } catch (error) {
    console.error('Erro ao obter contagem de compartilhamentos:', error);

    return new Response(
      JSON.stringify({
        success: false,
        error: 'Erro ao obter contagem de compartilhamentos.',
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache, no-store, must-revalidate',
        },
      }
    );
  }
};

/**
 * Obtém contagem de compartilhamentos do Facebook
 *
 * Nota: Esta função não funcionará devido às restrições da API do Facebook.
 * O Facebook não oferece mais uma API pública para contagem de compartilhamentos.
 */
async function getFacebookShareCount(url: string): Promise<number> {
  try {
    // Esta API não está mais disponível publicamente
    // Implementação alternativa usando scraping ou serviços de terceiros seria necessária
    return 0;
  } catch (error) {
    console.error('Erro ao obter contagem de compartilhamentos do Facebook:', error);
    return 0;
  }
}

/**
 * Obtém contagem de compartilhamentos do Pinterest
 */
async function getPinterestShareCount(url: string): Promise<number> {
  try {
    const response = await axios.get(
      `https://api.pinterest.com/v1/urls/count.json?url=${encodeURIComponent(url)}&callback=`
    );

    // A resposta vem em formato JSONP, precisamos extrair o JSON
    const text = response.data;
    const json = JSON.parse(text.replace(/^receiveCount\((.*)\)$/, '$1'));

    return json.count || 0;
  } catch (error) {
    console.error('Erro ao obter contagem de compartilhamentos do Pinterest:', error);
    return 0;
  }
}
