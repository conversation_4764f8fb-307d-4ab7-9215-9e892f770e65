/**
 * Configuração de Breakpoints
 *
 * Este arquivo define os breakpoints para diferentes tamanhos de tela e orientações.
 * Os breakpoints são baseados no Tailwind CSS, com algumas adições específicas para o projeto.
 */

// Importar tokens de breakpoints
import { breakpoints } from '../../tokens';

// Valores de breakpoints (em pixels)
const values = {
  xs: breakpoints.values.xs, // 320px
  sm: breakpoints.values.sm, // 640px
  md: breakpoints.values.md, // 768px
  lg: breakpoints.values.lg, // 1024px
  xl: breakpoints.values.xl, // 1280px
  '2xl': breakpoints.values['2xl'], // 1536px
};

// Breakpoints em formato de string para CSS
const screens = {
  xs: `${values.xs}px`,
  sm: `${values.sm}px`,
  md: `${values.md}px`,
  lg: `${values.lg}px`,
  xl: `${values.xl}px`,
  '2xl': `${values['2xl']}px`,
};

// Media queries para breakpoints
const mediaQueries = {
  xs: `@media (min-width: ${screens.xs})`,
  sm: `@media (min-width: ${screens.sm})`,
  md: `@media (min-width: ${screens.md})`,
  lg: `@media (min-width: ${screens.lg})`,
  xl: `@media (min-width: ${screens.xl})`,
  '2xl': `@media (min-width: ${screens['2xl']})`,

  // Media queries para dispositivos específicos
  mobile: `@media (max-width: ${values.md - 1}px)`,
  tablet: `@media (min-width: ${screens.md}) and (max-width: ${values.lg - 1}px)`,
  desktop: `@media (min-width: ${screens.lg})`,

  // Media queries para orientação
  portrait: '@media (orientation: portrait)',
  landscape: '@media (orientation: landscape)',

  // Media queries para características específicas
  touch: '@media (hover: none) and (pointer: coarse)',
  mouse: '@media (hover: hover) and (pointer: fine)',

  // Media queries para preferências do usuário
  reducedMotion: '@media (prefers-reduced-motion: reduce)',
  darkMode: '@media (prefers-color-scheme: dark)',
  lightMode: '@media (prefers-color-scheme: light)',

  // Media queries para dispositivos específicos
  phone: `@media (max-width: ${values.sm - 1}px)`,
  tabletPortrait: `@media (min-width: ${screens.sm}) and (max-width: ${values.md - 1}px)`,
  tabletLandscape: `@media (min-width: ${screens.md}) and (max-width: ${values.lg - 1}px)`,
  desktopSmall: `@media (min-width: ${screens.lg}) and (max-width: ${values.xl - 1}px)`,
  desktopLarge: `@media (min-width: ${screens.xl})`,
};

// Larguras de contêiner
const container = {
  xs: `${values.xs}px`,
  sm: `${values.sm}px`,
  md: `${values.md}px`,
  lg: `${values.lg}px`,
  xl: `${values.xl}px`,
  '2xl': `${values['2xl']}px`,
};

// Função para verificar se estamos em um determinado breakpoint
const isBreakpoint = (breakpoint) => {
  if (typeof window === 'undefined') return false;

  switch (breakpoint) {
    case 'xs':
      return window.matchMedia(`(min-width: ${screens.xs})`).matches;
    case 'sm':
      return window.matchMedia(`(min-width: ${screens.sm})`).matches;
    case 'md':
      return window.matchMedia(`(min-width: ${screens.md})`).matches;
    case 'lg':
      return window.matchMedia(`(min-width: ${screens.lg})`).matches;
    case 'xl':
      return window.matchMedia(`(min-width: ${screens.xl})`).matches;
    case '2xl':
      return window.matchMedia(`(min-width: ${screens['2xl']})`).matches;
    case 'mobile':
      return window.matchMedia(`(max-width: ${values.md - 1}px)`).matches;
    case 'tablet':
      return window.matchMedia(`(min-width: ${screens.md}) and (max-width: ${values.lg - 1}px)`)
        .matches;
    case 'desktop':
      return window.matchMedia(`(min-width: ${screens.lg})`).matches;
    case 'portrait':
      return window.matchMedia('(orientation: portrait)').matches;
    case 'landscape':
      return window.matchMedia('(orientation: landscape)').matches;
    default:
      return false;
  }
};

// Função para obter o breakpoint atual
const getCurrentBreakpoint = () => {
  if (typeof window === 'undefined') return 'md';

  if (window.matchMedia(`(min-width: ${screens['2xl']})`).matches) return '2xl';
  if (window.matchMedia(`(min-width: ${screens.xl})`).matches) return 'xl';
  if (window.matchMedia(`(min-width: ${screens.lg})`).matches) return 'lg';
  if (window.matchMedia(`(min-width: ${screens.md})`).matches) return 'md';
  if (window.matchMedia(`(min-width: ${screens.sm})`).matches) return 'sm';
  if (window.matchMedia(`(min-width: ${screens.xs})`).matches) return 'xs';

  return 'xs';
};

// Função para verificar se estamos em um dispositivo móvel
const isMobile = () => {
  if (typeof window === 'undefined') return false;

  return window.matchMedia(`(max-width: ${values.md - 1}px)`).matches;
};

// Função para verificar se estamos em um tablet
const isTablet = () => {
  if (typeof window === 'undefined') return false;

  return window.matchMedia(`(min-width: ${screens.md}) and (max-width: ${values.lg - 1}px)`)
    .matches;
};

// Função para verificar se estamos em um desktop
const isDesktop = () => {
  if (typeof window === 'undefined') return false;

  return window.matchMedia(`(min-width: ${screens.lg})`).matches;
};

// Função para verificar a orientação
const isPortrait = () => {
  if (typeof window === 'undefined') return false;

  return window.matchMedia('(orientation: portrait)').matches;
};

// Função para verificar a orientação
const isLandscape = () => {
  if (typeof window === 'undefined') return false;

  return window.matchMedia('(orientation: landscape)').matches;
};

// Exportar configuração de breakpoints
const responsiveConfig = {
  values,
  screens,
  mediaQueries,
  container,
  isBreakpoint,
  getCurrentBreakpoint,
  isMobile,
  isTablet,
  isDesktop,
  isPortrait,
  isLandscape,
};

export default responsiveConfig;
