/**
 * Testes de integração para API de pedidos
 * 
 * Este arquivo contém testes que verificam a integração entre a API de pedidos
 * e os serviços relacionados.
 * Parte da implementação da tarefa 9.1.2 - Testes de integração
 */

import { test, expect } from '@playwright/test';

// Dados de teste
const testUser = {
  email: '<EMAIL>',
  password: 'User@123456',
  name: 'User Orders Test',
  role: 'user'
};

const testAdmin = {
  email: '<EMAIL>',
  password: 'Admin@123456',
  name: 'Admin Orders User',
  role: 'admin'
};

// Configuração para testes de API
test.describe('API de Pedidos', () => {
  // Variáveis para armazenar tokens e IDs
  let userToken: string;
  let adminToken: string;
  let productId: string;
  let orderId: string;
  
  // Antes de todos os testes, criar usuários e produto de teste
  test.beforeAll(async ({ request }) => {
    // Criar usuário normal
    try {
      const userRegisterResponse = await request.post('/api/auth/register', {
        data: testUser
      });
      
      if (userRegisterResponse.ok()) {
        // Fazer login como usuário
        const userLoginResponse = await request.post('/api/auth/login', {
          data: {
            email: testUser.email,
            password: testUser.password
          }
        });
        
        if (userLoginResponse.ok()) {
          const userData = await userLoginResponse.json();
          userToken = userData.accessToken;
        }
      }
    } catch (error) {
      console.error('Erro ao configurar usuário normal:', error);
    }
    
    // Criar usuário admin
    try {
      const adminRegisterResponse = await request.post('/api/auth/register', {
        data: {
          ...testAdmin,
          role: 'user' // Inicialmente registrar como usuário normal
        }
      });
      
      if (adminRegisterResponse.ok()) {
        // Fazer login como admin
        const adminLoginResponse = await request.post('/api/auth/login', {
          data: {
            email: testAdmin.email,
            password: testAdmin.password
          }
        });
        
        if (adminLoginResponse.ok()) {
          const adminData = await adminLoginResponse.json();
          adminToken = adminData.accessToken;
          
          // Promover a admin usando uma rota especial de teste
          await request.post('/api/test/promote-to-admin', {
            headers: {
              'Authorization': `Bearer ${adminToken}`,
              'X-Test-Key': process.env.TEST_SECRET_KEY || 'test-integration-key'
            },
            data: {
              userId: adminData.user.id
            }
          });
          
          // Fazer login novamente para obter token com permissões de admin
          const newAdminLoginResponse = await request.post('/api/auth/login', {
            data: {
              email: testAdmin.email,
              password: testAdmin.password
            }
          });
          
          if (newAdminLoginResponse.ok()) {
            const newAdminData = await newAdminLoginResponse.json();
            adminToken = newAdminData.accessToken;
          }
        }
      }
      
      // Criar produto de teste
      const productResponse = await request.post('/api/products', {
        headers: {
          'Authorization': `Bearer ${adminToken}`
        },
        data: {
          name: 'Produto para Pedido',
          description: 'Produto de teste para pedidos',
          price: 49.99,
          categoryId: '1', // Será substituído por uma categoria real
          stock: 100,
          isActive: true,
          images: ['https://example.com/image1.jpg']
        }
      });
      
      if (productResponse.ok()) {
        const productData = await productResponse.json();
        productId = productData.id;
      } else {
        // Tentar obter um produto existente
        const productsResponse = await request.get('/api/products');
        if (productsResponse.ok()) {
          const productsData = await productsResponse.json();
          if (productsData.products && productsData.products.length > 0) {
            productId = productsData.products[0].id;
          }
        }
      }
    } catch (error) {
      console.error('Erro ao configurar produto de teste:', error);
    }
  });
  
  // Após todos os testes, excluir os usuários e produto de teste
  test.afterAll(async ({ request }) => {
    // Excluir produto de teste
    if (productId && adminToken) {
      await request.delete(`/api/products/${productId}`, {
        headers: {
          'Authorization': `Bearer ${adminToken}`
        }
      });
    }
    
    // Excluir usuário normal
    if (userToken) {
      await request.delete('/api/users/me', {
        headers: {
          'Authorization': `Bearer ${userToken}`
        }
      });
    }
    
    // Excluir usuário admin
    if (adminToken) {
      await request.delete('/api/users/me', {
        headers: {
          'Authorization': `Bearer ${adminToken}`
        }
      });
    }
  });
  
  test('usuário deve criar um novo pedido', async ({ request }) => {
    // Garantir que temos um token de usuário e ID de produto
    expect(userToken).toBeDefined();
    expect(productId).toBeDefined();
    
    const orderData = {
      items: [
        {
          productId,
          quantity: 2
        }
      ],
      shippingAddress: {
        street: 'Rua de Teste',
        number: '123',
        complement: 'Apto 101',
        neighborhood: 'Bairro Teste',
        city: 'Cidade Teste',
        state: 'ST',
        zipCode: '12345-678',
        country: 'Brasil'
      }
    };
    
    const response = await request.post('/api/orders', {
      headers: {
        'Authorization': `Bearer ${userToken}`
      },
      data: orderData
    });
    
    expect(response.status()).toBe(201);
    
    const responseData = await response.json();
    expect(responseData).toHaveProperty('id');
    expect(responseData).toHaveProperty('status', 'pending');
    expect(responseData).toHaveProperty('items');
    expect(Array.isArray(responseData.items)).toBeTruthy();
    expect(responseData.items.length).toBe(1);
    expect(responseData.items[0].productId).toBe(productId);
    expect(responseData.items[0].quantity).toBe(2);
    
    // Armazenar ID do pedido para testes subsequentes
    orderId = responseData.id;
  });
  
  test('usuário deve listar seus próprios pedidos', async ({ request }) => {
    // Garantir que temos um token de usuário
    expect(userToken).toBeDefined();
    
    const response = await request.get('/api/orders', {
      headers: {
        'Authorization': `Bearer ${userToken}`
      }
    });
    
    expect(response.status()).toBe(200);
    
    const ordersData = await response.json();
    expect(Array.isArray(ordersData.orders)).toBeTruthy();
    expect(ordersData.orders.length).toBeGreaterThan(0);
    
    // Verificar se nosso pedido de teste está na lista
    const testOrderInList = ordersData.orders.find((o: any) => o.id === orderId);
    expect(testOrderInList).toBeDefined();
    expect(testOrderInList.status).toBe('pending');
  });
  
  test('usuário deve obter detalhes de um pedido específico', async ({ request }) => {
    // Garantir que temos um token de usuário e ID de pedido
    expect(userToken).toBeDefined();
    expect(orderId).toBeDefined();
    
    const response = await request.get(`/api/orders/${orderId}`, {
      headers: {
        'Authorization': `Bearer ${userToken}`
      }
    });
    
    expect(response.status()).toBe(200);
    
    const orderData = await response.json();
    expect(orderData).toHaveProperty('id', orderId);
    expect(orderData).toHaveProperty('status', 'pending');
    expect(orderData).toHaveProperty('items');
    expect(Array.isArray(orderData.items)).toBeTruthy();
    expect(orderData.items.length).toBe(1);
    expect(orderData.items[0].productId).toBe(productId);
  });
  
  test('usuário não deve acessar pedidos de outros usuários', async ({ request }) => {
    // Criar outro usuário e pedido
    const otherUser = {
      email: '<EMAIL>',
      password: 'Other@123456',
      name: 'Other User'
    };
    
    const registerResponse = await request.post('/api/auth/register', {
      data: otherUser
    });
    
    expect(registerResponse.status()).toBe(201);
    
    const loginResponse = await request.post('/api/auth/login', {
      data: {
        email: otherUser.email,
        password: otherUser.password
      }
    });
    
    expect(loginResponse.status()).toBe(200);
    
    const otherUserToken = (await loginResponse.json()).accessToken;
    
    // Tentar acessar o pedido do primeiro usuário
    const response = await request.get(`/api/orders/${orderId}`, {
      headers: {
        'Authorization': `Bearer ${otherUserToken}`
      }
    });
    
    expect(response.status()).toBe(403);
    
    // Limpar o outro usuário
    await request.delete('/api/users/me', {
      headers: {
        'Authorization': `Bearer ${otherUserToken}`
      }
    });
  });
  
  test('admin deve listar todos os pedidos', async ({ request }) => {
    // Garantir que temos um token de admin
    expect(adminToken).toBeDefined();
    
    const response = await request.get('/api/admin/orders', {
      headers: {
        'Authorization': `Bearer ${adminToken}`
      }
    });
    
    expect(response.status()).toBe(200);
    
    const ordersData = await response.json();
    expect(Array.isArray(ordersData.orders)).toBeTruthy();
    
    // Verificar se nosso pedido de teste está na lista
    const testOrderInList = ordersData.orders.find((o: any) => o.id === orderId);
    expect(testOrderInList).toBeDefined();
  });
  
  test('admin deve atualizar o status de um pedido', async ({ request }) => {
    // Garantir que temos um token de admin e ID de pedido
    expect(adminToken).toBeDefined();
    expect(orderId).toBeDefined();
    
    const response = await request.patch(`/api/admin/orders/${orderId}/status`, {
      headers: {
        'Authorization': `Bearer ${adminToken}`
      },
      data: {
        status: 'processing'
      }
    });
    
    expect(response.status()).toBe(200);
    
    const orderData = await response.json();
    expect(orderData).toHaveProperty('id', orderId);
    expect(orderData).toHaveProperty('status', 'processing');
    
    // Verificar se a atualização foi persistida
    const getResponse = await request.get(`/api/orders/${orderId}`, {
      headers: {
        'Authorization': `Bearer ${userToken}`
      }
    });
    
    const getOrderData = await getResponse.json();
    expect(getOrderData).toHaveProperty('status', 'processing');
  });
  
  test('usuário deve cancelar seu próprio pedido pendente', async ({ request }) => {
    // Primeiro, criar um novo pedido para cancelar
    const orderData = {
      items: [
        {
          productId,
          quantity: 1
        }
      ],
      shippingAddress: {
        street: 'Rua de Teste',
        number: '123',
        complement: 'Apto 101',
        neighborhood: 'Bairro Teste',
        city: 'Cidade Teste',
        state: 'ST',
        zipCode: '12345-678',
        country: 'Brasil'
      }
    };
    
    const createResponse = await request.post('/api/orders', {
      headers: {
        'Authorization': `Bearer ${userToken}`
      },
      data: orderData
    });
    
    expect(createResponse.status()).toBe(201);
    
    const newOrderId = (await createResponse.json()).id;
    
    // Cancelar o pedido
    const response = await request.post(`/api/orders/${newOrderId}/cancel`, {
      headers: {
        'Authorization': `Bearer ${userToken}`
      }
    });
    
    expect(response.status()).toBe(200);
    
    const orderData2 = await response.json();
    expect(orderData2).toHaveProperty('id', newOrderId);
    expect(orderData2).toHaveProperty('status', 'cancelled');
    
    // Verificar se a atualização foi persistida
    const getResponse = await request.get(`/api/orders/${newOrderId}`, {
      headers: {
        'Authorization': `Bearer ${userToken}`
      }
    });
    
    const getOrderData = await getResponse.json();
    expect(getOrderData).toHaveProperty('status', 'cancelled');
  });
});
