import type { QueryResult } from 'pg';
import { pg<PERSON><PERSON>per } from './pgHelper';

async function create(
  ulid_user: string,
  ulid_product: string,
  ulid_parent: string,
  title: string,
  comment: string,
  published: boolean,
  rating_value: number
): Promise<QueryResult> {
  return await pgHelper.query(
    `INSERT INTO tab_post (
       ulid_post, 
       ulid_user, 
       ulid_product, 
       ulid_parent, 
       title, 
       comment, 
       published, 
       rating_value) 
     VALUES ($1, $2, $3, $4, $5, $6, $7, $8) 
     RETURNING *`,
    [
      pgHelper.generateULID(),
      ulid_user,
      ulid_product,
      ulid_parent,
      title,
      comment,
      published,
      rating_value,
    ]
  );
}

async function read(
  ulid_post?: string,
  ulid_user?: string,
  ulid_product?: string,
  ulid_parent?: string,
  published?: boolean,
  rating_value?: number
): Promise<QueryResult> {
  return await pgHelper.query(
    `SELECT * 
       FROM tab_post 
      WHERE TRUE
        ${ulid_post ? 'AND ulid_post = $1' : ''}
        ${ulid_user ? 'AND ulid_user = $2' : ''}
        ${ulid_product ? 'AND ulid_product = $3' : ''}
        ${ulid_parent ? 'AND ulid_parent = $4' : ''}
        ${published !== undefined ? 'AND published = $7' : ''}
        ${rating_value ? 'AND rating_value = $8' : ''}
     ORDER BY created_at DESC`,
    [ulid_post, ulid_user, ulid_product, ulid_parent, published, rating_value]
  );
}

async function update(
  ulid_post: string,
  ulid_user: string,
  ulid_product: string,
  ulid_parent: string,
  title: string,
  comment: string,
  published: boolean,
  rating_value: number
): Promise<QueryResult> {
  return await pgHelper.query(
    `UPDATE tab_post 
        SET ulid_user    = $2, 
            ulid_product = $3, 
            ulid_parent  = $4, 
            title        = $5, 
            comment      = $6, 
            published    = $7, 
            rating_value = $8, 
            updated_at   = CURRENT_TIMESTAMP 
      WHERE ulid_post = $9 
        AND published = true 
     RETURNING *`,
    [
      ulid_post,
      ulid_user,
      ulid_product,
      ulid_parent,
      title,
      comment,
      published,
      rating_value,
      ulid_post,
    ]
  );
}

async function inactivate(ulid_post: string): Promise<QueryResult> {
  return await pgHelper.query(
    `UPDATE tab_post 
        SET published = false,
            updated_at = CURRENT_TIMESTAMP 
      WHERE ulid_post = $1 
        AND published = true 
     RETURNING *`,
    [ulid_post]
  );
}

async function deleteByUlid(ulid_post: string): Promise<QueryResult> {
  return await pgHelper.query(
    `DELETE FROM tab_post 
      WHERE ulid_post = $1 
     RETURNING *`,
    [ulid_post]
  );
}

export const postRepository = {
  create,
  read,
  update,
  inactivate,
  deleteByUlid,
};
