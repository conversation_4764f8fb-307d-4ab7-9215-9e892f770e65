/**
 * API de Versões de Conteúdo
 *
 * Endpoint para gerenciamento de versões de conteúdo.
 * Parte da implementação da tarefa 8.8.3 - Gestão de conteúdo
 */

import type { APIRoute } from 'astro';
import { ContentRepository } from '../../../../../domain/repositories/ContentRepository';
import { TokenService } from '../../../../../domain/services/TokenService';
import { GetContentVersionsUseCase } from '../../../../../domain/usecases/content/GetContentVersionsUseCase';
import { RestoreContentVersionUseCase } from '../../../../../domain/usecases/content/RestoreContentVersionUseCase';
import { PostgresContentRepository } from '../../../../../infrastructure/database/repositories/PostgresContentRepository';
import { JwtTokenService } from '../../../../../infrastructure/services/JwtTokenService';

// Inicializar repositório
const contentRepository: ContentRepository = new PostgresContentRepository();

// Inicializar serviços
const tokenService: TokenService = new JwtTokenService(
  process.env.JWT_SECRET || 'default-secret-key',
  process.env.PASSWORD_RESET_SECRET || 'password-reset-secret-key',
  process.env.EMAIL_VERIFICATION_SECRET || 'email-verification-secret-key'
);

// Inicializar casos de uso
const getContentVersionsUseCase = new GetContentVersionsUseCase(contentRepository);
const restoreContentVersionUseCase = new RestoreContentVersionUseCase(contentRepository);

// Verificar autenticação e permissões
const checkAuth = (
  request: Request
): { userId: string; canRead: boolean; canWrite: boolean } | null => {
  const authHeader = request.headers.get('Authorization');

  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null;
  }

  const token = authHeader.substring(7);
  const payload = tokenService.verifyToken(token);

  if (!payload) {
    return null;
  }

  // Verificar permissões
  const canRead = payload.role === 'admin' || payload.permissions?.includes('content:read');

  const canWrite = payload.role === 'admin' || payload.permissions?.includes('content:write');

  return {
    userId: payload.id,
    canRead,
    canWrite,
  };
};

export const GET: APIRoute = async ({ params, request }) => {
  try {
    const contentId = params.contentId;

    if (!contentId) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'ID do conteúdo é obrigatório.',
        }),
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    // Verificar autenticação
    const auth = checkAuth(request);

    if (!auth) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Não autorizado. Faça login para continuar.',
        }),
        {
          status: 401,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    if (!auth.canRead) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Você não tem permissão para acessar este recurso.',
        }),
        {
          status: 403,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    // Obter parâmetros de consulta
    const url = new URL(request.url);
    const page = url.searchParams.get('page');
    const limit = url.searchParams.get('limit');

    // Executar caso de uso
    const result = await getContentVersionsUseCase.execute({
      contentId,
      page: page ? Number.parseInt(page) : undefined,
      limit: limit ? Number.parseInt(limit) : undefined,
    });

    if (result.success && result.data) {
      return new Response(
        JSON.stringify({
          success: true,
          data: result.data,
        }),
        {
          status: 200,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }
    return new Response(
      JSON.stringify({
        success: false,
        error: result.error || 'Erro desconhecido ao obter versões do conteúdo.',
      }),
      {
        status: 400,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  } catch (error) {
    console.error('Erro ao processar obtenção de versões de conteúdo:', error);

    return new Response(
      JSON.stringify({
        success: false,
        error:
          'Ocorreu um erro ao processar a obtenção das versões do conteúdo. Por favor, tente novamente.',
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  }
};

export const POST: APIRoute = async ({ params, request }) => {
  try {
    // Verificar autenticação
    const auth = checkAuth(request);

    if (!auth) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Não autorizado. Faça login para continuar.',
        }),
        {
          status: 401,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    if (!auth.canWrite) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Você não tem permissão para restaurar versões de conteúdo.',
        }),
        {
          status: 403,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    // Obter dados do corpo da requisição
    const body = await request.json();

    if (!body.versionId) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'ID da versão é obrigatório.',
        }),
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    // Executar caso de uso
    const result = await restoreContentVersionUseCase.execute({
      versionId: body.versionId,
      userId: auth.userId,
    });

    if (result.success && result.data) {
      return new Response(
        JSON.stringify({
          success: true,
          data: result.data,
        }),
        {
          status: 200,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }
    return new Response(
      JSON.stringify({
        success: false,
        error: result.error || 'Erro desconhecido ao restaurar versão do conteúdo.',
      }),
      {
        status: 400,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  } catch (error) {
    console.error('Erro ao processar restauração de versão de conteúdo:', error);

    return new Response(
      JSON.stringify({
        success: false,
        error:
          'Ocorreu um erro ao processar a restauração da versão do conteúdo. Por favor, tente novamente.',
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  }
};
