/**
 * Social Integrations API
 *
 * Endpoint para gerenciar integrações com redes sociais.
 * Parte da implementação da tarefa 8.9.2 - Integração com redes sociais
 */

import type { APIRoute } from 'astro';
import { SocialAuthService } from '../../../domain/services/SocialAuthService';
import { TokenService } from '../../../domain/services/TokenService';
import { JwtTokenService } from '../../../infrastructure/services/JwtTokenService';
import { OAuthSocialAuthService } from '../../../infrastructure/services/OAuthSocialAuthService';

// Inicializar serviços
const socialAuthService: SocialAuthService = new OAuthSocialAuthService();
const tokenService: TokenService = new JwtTokenService(
  process.env.JWT_SECRET || 'default-secret-key',
  process.env.PASSWORD_RESET_SECRET || 'password-reset-secret-key',
  process.env.EMAIL_VERIFICATION_SECRET || 'email-verification-secret-key'
);

// Configurar serviço de autenticação social
await socialAuthService.initialize({
  facebook: {
    appId: process.env.FACEBOOK_APP_ID || '',
    appSecret: process.env.FACEBOOK_APP_SECRET || '',
    redirectUri: `${process.env.BASE_URL || 'https://example.com'}/api/auth/social/callback/facebook`,
  },
  google: {
    clientId: process.env.GOOGLE_CLIENT_ID || '',
    clientSecret: process.env.GOOGLE_CLIENT_SECRET || '',
    redirectUri: `${process.env.BASE_URL || 'https://example.com'}/api/auth/social/callback/google`,
  },
  twitter: {
    apiKey: process.env.TWITTER_API_KEY || '',
    apiSecret: process.env.TWITTER_API_SECRET || '',
    redirectUri: `${process.env.BASE_URL || 'https://example.com'}/api/auth/social/callback/twitter`,
  },
  linkedin: {
    clientId: process.env.LINKEDIN_CLIENT_ID || '',
    clientSecret: process.env.LINKEDIN_CLIENT_SECRET || '',
    redirectUri: `${process.env.BASE_URL || 'https://example.com'}/api/auth/social/callback/linkedin`,
  },
});

// Verificar autenticação e permissões
const checkAuth = (request: Request): { userId: string; isAuthorized: boolean } | null => {
  const authHeader = request.headers.get('Authorization');

  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null;
  }

  const token = authHeader.substring(7);
  const payload = tokenService.verifyToken(token);

  if (!payload) {
    return null;
  }

  // Verificar permissões
  const isAuthorized = payload.role === 'admin' || payload.permissions?.includes('social:manage');

  return {
    userId: payload.id,
    isAuthorized,
  };
};

export const GET: APIRoute = async ({ request }) => {
  try {
    // Verificar autenticação
    const auth = checkAuth(request);

    if (!auth) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Não autorizado. Faça login para continuar.',
        }),
        {
          status: 401,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    // Obter parâmetros da URL
    const url = new URL(request.url);
    const provider = url.searchParams.get('provider');

    // Obter configurações de integração
    const integrations = {
      facebook: {
        enabled: !!process.env.FACEBOOK_APP_ID,
        appId: process.env.FACEBOOK_APP_ID,
        pageId: process.env.FACEBOOK_PAGE_ID,
        pageUrl: process.env.FACEBOOK_PAGE_URL,
      },
      twitter: {
        enabled: !!process.env.TWITTER_API_KEY,
        handle: process.env.TWITTER_HANDLE,
        profileUrl: process.env.TWITTER_PROFILE_URL,
      },
      instagram: {
        enabled: !!process.env.INSTAGRAM_USERNAME,
        username: process.env.INSTAGRAM_USERNAME,
        profileUrl: process.env.INSTAGRAM_PROFILE_URL,
      },
      linkedin: {
        enabled: !!process.env.LINKEDIN_CLIENT_ID,
        companyId: process.env.LINKEDIN_COMPANY_ID,
        companyUrl: process.env.LINKEDIN_COMPANY_URL,
      },
    };

    // Retornar configurações específicas ou todas
    if (provider && ['facebook', 'twitter', 'instagram', 'linkedin'].includes(provider)) {
      return new Response(
        JSON.stringify({
          success: true,
          data: integrations[provider as keyof typeof integrations],
        }),
        {
          status: 200,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }
    return new Response(
      JSON.stringify({
        success: true,
        data: integrations,
      }),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  } catch (error) {
    console.error('Erro ao obter configurações de integração social:', error);

    return new Response(
      JSON.stringify({
        success: false,
        error: 'Ocorreu um erro ao processar a requisição. Por favor, tente novamente.',
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  }
};

export const POST: APIRoute = async ({ request }) => {
  try {
    // Verificar autenticação
    const auth = checkAuth(request);

    if (!auth) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Não autorizado. Faça login para continuar.',
        }),
        {
          status: 401,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    if (!auth.isAuthorized) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Você não tem permissão para gerenciar integrações sociais.',
        }),
        {
          status: 403,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    // Obter dados do corpo da requisição
    const body = await request.json();

    // Validar dados
    if (
      !body.provider ||
      !['facebook', 'twitter', 'instagram', 'linkedin'].includes(body.provider)
    ) {
      return new Response(
        JSON.stringify({
          success: false,
          error:
            'Provedor inválido. Provedores suportados: facebook, twitter, instagram, linkedin.',
        }),
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    // Atualizar configurações (simulado - em um ambiente real, isso seria salvo em um banco de dados)
    // Aqui estamos apenas retornando os dados enviados

    return new Response(
      JSON.stringify({
        success: true,
        message: `Configurações de ${body.provider} atualizadas com sucesso.`,
        data: body,
      }),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  } catch (error) {
    console.error('Erro ao atualizar configurações de integração social:', error);

    return new Response(
      JSON.stringify({
        success: false,
        error: 'Ocorreu um erro ao processar a requisição. Por favor, tente novamente.',
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  }
};
