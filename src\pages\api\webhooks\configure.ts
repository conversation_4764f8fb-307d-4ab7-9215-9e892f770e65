import { isAuthenticated } from '@middleware/authMiddleware';
import { webhookService } from '@services/webhookService';
// src/pages/api/webhooks/configure.ts
import type { APIRoute } from 'astro';

/**
 * Endpoint para configurar o webhook da Efí Pay
 *
 * Este endpoint permite configurar o webhook para receber notificações
 * da Efí Pay. Apenas usuários autenticados com permissões de administrador
 * podem acessar este endpoint.
 */
export const POST: APIRoute = async ({ request, cookies }) => {
  try {
    // Verificar autenticação
    const authResult = await isAuthenticated({ request, cookies } as any);

    if (!authResult.isAuthenticated) {
      return new Response(JSON.stringify({ error: 'Não autorizado' }), {
        status: 401,
        headers: {
          'Content-Type': 'application/json',
        },
      });
    }

    // Verificar se o usuário é administrador
    if (authResult.user?.role !== 'admin') {
      return new Response(JSON.stringify({ error: 'Permissão negada' }), {
        status: 403,
        headers: {
          'Content-Type': 'application/json',
        },
      });
    }

    // Configurar webhook
    const result = await webhookService.configureMainWebhook();

    // Retornar resposta de sucesso
    return new Response(JSON.stringify({ success: true, result }), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  } catch (error) {
    console.error('Erro ao configurar webhook:', error);

    // Retornar resposta de erro
    return new Response(
      JSON.stringify({
        error: error instanceof Error ? error.message : 'Erro interno',
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  }
};

/**
 * Endpoint para obter informações do webhook configurado
 */
export const GET: APIRoute = async ({ request, cookies }) => {
  try {
    // Verificar autenticação
    const authResult = await isAuthenticated({ request, cookies } as any);

    if (!authResult.isAuthenticated) {
      return new Response(JSON.stringify({ error: 'Não autorizado' }), {
        status: 401,
        headers: {
          'Content-Type': 'application/json',
        },
      });
    }

    // Verificar se o usuário é administrador
    if (authResult.user?.role !== 'admin') {
      return new Response(JSON.stringify({ error: 'Permissão negada' }), {
        status: 403,
        headers: {
          'Content-Type': 'application/json',
        },
      });
    }

    // Obter informações do webhook
    const result = await webhookService.getWebhook(process.env.EFIPAY_PIX_KEY || '');

    // Retornar resposta de sucesso
    return new Response(JSON.stringify({ success: true, result }), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  } catch (error) {
    console.error('Erro ao obter informações do webhook:', error);

    // Retornar resposta de erro
    return new Response(
      JSON.stringify({
        error: error instanceof Error ? error.message : 'Erro interno',
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  }
};
