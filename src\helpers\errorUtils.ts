import type { QueryResult } from 'pg';
import { helper } from '.';

export function createErrorMessage(action: string, error: Error): string {
  return `Erro ao executar a ação ${action}: ${error.message}`;
}

export function handleNoResults(result: QueryResult, entityName: string) {
  if (result.rowCount === 0) {
    return {
      success: false,
      error: `${entityName} não encontrado`,
    };
  }
  return { success: true, data: result.rows };
}

export function handleError(action: string, error: unknown) {
  const errorMessage = createErrorMessage(action, error as Error);
  helper.email.sendingError(error as Error);
  return {
    success: false,
    error: errorMessage,
  };
}
