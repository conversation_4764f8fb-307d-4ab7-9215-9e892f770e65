---
import InputText from '@components/form/InputText.astro';
import AdminLayout from '@layouts/AdminLayout.astro';
---
<AdminLayout title="Cadastro de Tipo de Pagamento">
  <template>
    <div class="search-container flex justify-between mb-4">
      <InputText id="search-input" label="Pesquisar" name="search" placeholder="Pesquisar tipos de pagamento..." />
      <div>
        <button class="btn btn-primary" id="handleSearch">Pesquisar</button>
        <button class="btn btn-secondary ml-2" id="navigateToNewPaymentType">Novo</button>
      </div>
    </div>

    <ul class="payment-type-list list-disc pl-5">
    </ul>
  </template>

  <style>
    .search-container {
      display: flex;
      justify-content: space-between;
      margin-bottom: 20px;
    }
    .payment-type-list {
      list-style: none;
      padding: 0;
    }
    .payment-type-list li {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10px;
    }
  </style>

  <script>
    import { actions } from "astro:actions";
    
    interface PaymentType {
      ulid_payment_type: string;
      type: string;
    }

    let paymentTypes: PaymentType[] = []; // Declare and initialize the 'paymentTypes' variable

    async function handleSearch() {
      const searchInputElement = document.getElementById('search-input') as HTMLInputElement;
      if (searchInputElement) {
        const searchInput = searchInputElement.value;
        if (searchInput !== null && searchInput !== undefined) {
          // Lógica para buscar tipos de pagamento com base no input
          const param = { filter: "type", type: searchInput };
          loadPaymentTypes(param);
        } else {
          console.error('O valor do input de pesquisa é nulo ou indefinido.');
        }
      } else {
        console.error('Elemento de input de pesquisa não encontrado.');
      }
    }

    function navigateToNewPaymentType() {
      // Lógica para redirecionar para o novo tipo de pagamento
      window.location.href = '/admin/register/payment/type/new';
    }

    async function handleEdit(ulid_payment_type: string) {
      // Lógica para editar o tipo de pagamento
      window.location.href = `/admin/register/payment/type/${ulid_payment_type}`;
    }

    async function handleDelete(ulid_payment_type: string) {
      // Lógica para excluir o tipo de pagamento
      if (!confirm('Tem certeza que deseja excluir este tipo de pagamento?')) return;
      await actions.paymentTypeAction.delete({ ulid_payment_type });
      loadPaymentTypes({ filter: "all" }); // Recarregar tipos de pagamento após exclusão
    }

    async function loadPaymentTypes(param: any) {
      const result = await actions.paymentTypeAction.read(param);
      paymentTypes = result.data || [];

      // Apagar a lista existente
      const paymentTypeList = document.querySelector('.payment-type-list');

      if (paymentTypeList) {
        paymentTypeList.innerHTML = '';

        // Inserir o cabeçalho
        const titleRow = document.createElement('li');
        titleRow.innerHTML = `
            <strong>Nome</strong>
            <strong>Ações</strong>
        `;
        paymentTypeList.appendChild(titleRow);

        if (paymentTypes.length === 0) {
          const noDataMessage = document.createElement('li');
          noDataMessage.textContent = 'Nenhum tipo de pagamento encontrado.';
          paymentTypeList.appendChild(noDataMessage);
          return;
        }
        
        // Inserir novos tipos de pagamento
        paymentTypes.forEach((paymentType: PaymentType) => {
            const listItem = document.createElement('li');
            listItem.innerHTML = `
                <span>${paymentType.type}</span>
                <div>
                    <button type="button" class="btn btn-warning mr-2" onclick="handleEdit('${paymentType.ulid_payment_type}')">Alterar</button>
                    <button type="button" class="btn btn-error" onclick="handleDelete('${paymentType.ulid_payment_type}')">Excluir</button>
                </div>
            `;
            paymentTypeList.appendChild(listItem);
        });
      }
    }
    
    loadPaymentTypes({ filter: "all" });
  </script>
</AdminLayout>