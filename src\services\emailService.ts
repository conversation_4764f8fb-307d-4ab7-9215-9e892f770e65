import { logger } from '@utils/logger';
// src/services/emailService.ts
import nodemailer from 'nodemailer';
import { PaymentStatus } from './paymentService';

/**
 * Interface para dados de email
 */
interface EmailData {
  to: string;
  subject: string;
  html: string;
  from?: string;
  text?: string;
  attachments?: Array<{
    filename: string;
    content: Buffer | string;
    contentType?: string;
  }>;
}

/**
 * Serviço para envio de emails
 */
export const emailService = {
  /**
   * Obtém o transportador de email
   * @returns Transportador de email
   */
  getTransporter(): nodemailer.Transporter {
    return nodemailer.createTransport({
      host: process.env.SERVICE_EMAIL_HOST || 'smtp.example.com',
      port: Number.parseInt(process.env.SERVICE_EMAIL_PORT || '587'),
      secure: process.env.SERVICE_EMAIL_SECURE === 'true',
      auth: {
        user: process.env.SERVICE_EMAIL_USER,
        pass: process.env.SERVICE_EMAIL_PASSWORD,
      },
    });
  },

  /**
   * Envia um email
   * @param emailData - Dados do email
   * @returns Resultado do envio
   */
  async sendEmail(emailData: EmailData): Promise<any> {
    try {
      const transporter = this.getTransporter();

      const mailOptions = {
        from:
          emailData.from ||
          `"${process.env.SERVICE_EMAIL_NAME}" <${process.env.SERVICE_EMAIL_USER}>`,
        to: emailData.to,
        subject: emailData.subject,
        text: emailData.text,
        html: emailData.html,
        attachments: emailData.attachments,
      };

      const result = await transporter.sendMail(mailOptions);

      logger.info('Email enviado com sucesso:', {
        messageId: result.messageId,
        to: emailData.to,
        subject: emailData.subject,
      });

      return result;
    } catch (error) {
      logger.error('Erro ao enviar email:', error);
      throw error;
    }
  },

  /**
   * Envia email de confirmação de pagamento
   * @param payment - Dados do pagamento
   * @returns Resultado do envio
   */
  async sendPaymentConfirmationEmail(payment: any): Promise<any> {
    try {
      const subject = 'Pagamento Confirmado - Estação Alfabetização';

      const html = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2>Pagamento Confirmado</h2>
          <p>Olá ${payment.user_name},</p>
          <p>Seu pagamento foi confirmado com sucesso!</p>
          
          <div style="background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin: 20px 0;">
            <h3 style="margin-top: 0;">Detalhes do Pagamento</h3>
            <p><strong>ID do Pagamento:</strong> ${payment.ulid_payment}</p>
            <p><strong>Método:</strong> ${payment.payment_type}</p>
            <p><strong>Valor:</strong> R$ ${payment.value.toFixed(2)}</p>
            <p><strong>Data:</strong> ${new Date(payment.updated_at).toLocaleString()}</p>
          </div>
          
          <p>Agradecemos pela sua compra!</p>
          <p>Atenciosamente,<br>Equipe Estação Alfabetização</p>
        </div>
      `;

      return await this.sendEmail({
        to: payment.user_email,
        subject,
        html,
      });
    } catch (error) {
      logger.error('Erro ao enviar email de confirmação de pagamento:', error);
      throw error;
    }
  },

  /**
   * Envia email de falha no pagamento
   * @param payment - Dados do pagamento
   * @param status - Status do pagamento
   * @returns Resultado do envio
   */
  async sendPaymentFailureEmail(payment: any, status: PaymentStatus): Promise<any> {
    try {
      const statusText = {
        [PaymentStatus.REJECTED]: 'Rejeitado',
        [PaymentStatus.CANCELLED]: 'Cancelado',
      };

      const subject = `Pagamento ${statusText[status]} - Estação Alfabetização`;

      const html = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2>Pagamento ${statusText[status]}</h2>
          <p>Olá ${payment.user_name},</p>
          <p>Infelizmente, seu pagamento foi ${statusText[status].toLowerCase()}.</p>
          
          <div style="background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin: 20px 0;">
            <h3 style="margin-top: 0;">Detalhes do Pagamento</h3>
            <p><strong>ID do Pagamento:</strong> ${payment.ulid_payment}</p>
            <p><strong>Método:</strong> ${payment.payment_type}</p>
            <p><strong>Valor:</strong> R$ ${payment.value.toFixed(2)}</p>
            <p><strong>Data:</strong> ${new Date(payment.updated_at).toLocaleString()}</p>
          </div>
          
          <p>Por favor, tente novamente ou entre em contato com nosso suporte.</p>
          <p>Atenciosamente,<br>Equipe Estação Alfabetização</p>
        </div>
      `;

      return await this.sendEmail({
        to: payment.user_email,
        subject,
        html,
      });
    } catch (error) {
      logger.error('Erro ao enviar email de falha no pagamento:', error);
      throw error;
    }
  },

  /**
   * Envia email de confirmação de reembolso
   * @param payment - Dados do pagamento
   * @returns Resultado do envio
   */
  async sendRefundConfirmationEmail(payment: any): Promise<any> {
    try {
      const subject = 'Reembolso Confirmado - Estação Alfabetização';

      const html = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2>Reembolso Confirmado</h2>
          <p>Olá ${payment.user_name},</p>
          <p>Seu reembolso foi processado com sucesso!</p>
          
          <div style="background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin: 20px 0;">
            <h3 style="margin-top: 0;">Detalhes do Reembolso</h3>
            <p><strong>ID do Pagamento:</strong> ${payment.ulid_payment}</p>
            <p><strong>Método:</strong> ${payment.payment_type}</p>
            <p><strong>Valor:</strong> R$ ${payment.value.toFixed(2)}</p>
            <p><strong>Data:</strong> ${new Date(payment.updated_at).toLocaleString()}</p>
          </div>
          
          <p>O valor será creditado de acordo com as políticas da sua instituição financeira.</p>
          <p>Atenciosamente,<br>Equipe Estação Alfabetização</p>
        </div>
      `;

      return await this.sendEmail({
        to: payment.user_email,
        subject,
        html,
      });
    } catch (error) {
      logger.error('Erro ao enviar email de confirmação de reembolso:', error);
      throw error;
    }
  },

  /**
   * Envia email de notificação de acesso a produto digital
   * @param user - Dados do usuário
   * @param product - Dados do produto
   * @returns Resultado do envio
   */
  async sendDigitalProductAccessEmail(user: any, product: any): Promise<any> {
    try {
      const subject = `Acesso Liberado: ${product.name} - Estação Alfabetização`;

      const html = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2>Acesso Liberado</h2>
          <p>Olá ${user.name},</p>
          <p>Seu acesso ao produto digital <strong>${product.name}</strong> foi liberado!</p>
          
          <div style="background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin: 20px 0;">
            <h3 style="margin-top: 0;">Detalhes do Produto</h3>
            <p><strong>Nome:</strong> ${product.name}</p>
            <p><strong>Descrição:</strong> ${product.description}</p>
          </div>
          
          <p>Para acessar o produto, faça login em sua conta e acesse a seção "Meus Produtos".</p>
          <p>Atenciosamente,<br>Equipe Estação Alfabetização</p>
        </div>
      `;

      return await this.sendEmail({
        to: user.email,
        subject,
        html,
      });
    } catch (error) {
      logger.error('Erro ao enviar email de acesso a produto digital:', error);
      throw error;
    }
  },
};
