# Sistema de Monitoramento Kafka

Este documento descreve o sistema de monitoramento implementado para o cluster Kafka na plataforma Estação da Alfabetização.

## Visão Geral

O sistema de monitoramento Kafka é composto por três componentes principais:

1. **Sistema de Logging**: Coleta, armazena e gerencia logs do Kafka
2. **Sistema de Alertas**: Detecta e notifica sobre problemas no cluster Kafka
3. **Dashboard**: Visualização em tempo real do estado do cluster Kafka

## 1. Sistema de Logging

### 1.1 Configuração

O sistema de logging do Kafka foi implementado com as seguintes características:

- **Múltiplos destinos**: Console, arquivo, banco de dados e tópico Kafka
- **Níveis de log**: DEBUG, INFO, WARN, ERROR, FATAL
- **Rotação de logs**: Configurável por tamanho e tempo
- **Armazenamento centralizado**: Logs armazenados em banco de dados para consulta e análise

### 1.2 Estrutura de Logs

Cada entrada de log contém as seguintes informações:

- **ID**: Identificador único do log
- **Timestamp**: Data e hora da ocorrência
- **Nível**: Nível de severidade (DEBUG, INFO, WARN, ERROR, FATAL)
- **Componente**: Componente que gerou o log (broker, consumer, producer, etc.)
- **Mensagem**: Descrição do evento
- **Metadados**: Informações adicionais em formato JSON
- **Hostname**: Nome do host que gerou o log
- **Process ID**: ID do processo que gerou o log

### 1.3 Configuração por Componente

É possível configurar níveis de log específicos para cada componente:

- **Broker**: Logs relacionados aos brokers Kafka
- **Consumer**: Logs relacionados aos consumidores
- **Producer**: Logs relacionados aos produtores
- **Admin**: Logs relacionados às operações administrativas
- **Topics**: Logs relacionados aos tópicos

### 1.4 Consulta e Análise

Os logs podem ser consultados e analisados através de:

- **Visualizações SQL**: Visualizações pré-definidas para análise de logs
- **Funções SQL**: Funções para consulta e agregação de logs
- **Dashboard**: Visualização gráfica de logs e tendências

## 2. Sistema de Alertas

### 2.1 Tipos de Alertas

O sistema monitora e gera alertas para os seguintes problemas:

- **Broker Down**: Quando um broker fica indisponível
- **Consumer Lag**: Quando o lag de um consumidor excede o limite configurado
- **Disk Space**: Quando o espaço em disco está ficando baixo
- **Replication Failure**: Quando ocorre falha na replicação de partições
- **Partition Reassignment**: Quando ocorre reatribuição de partições
- **Topic Unavailable**: Quando um tópico fica indisponível
- **High CPU Usage**: Quando o uso de CPU está alto
- **High Memory Usage**: Quando o uso de memória está alto
- **Under Replicated Partitions**: Quando existem partições sub-replicadas
- **Offline Partitions**: Quando existem partições offline
- **Producer Error Rate**: Quando a taxa de erros de produtores excede o limite
- **Consumer Error Rate**: Quando a taxa de erros de consumidores excede o limite

### 2.2 Severidade de Alertas

Os alertas são classificados em quatro níveis de severidade:

- **INFO**: Informações que não requerem ação imediata
- **WARNING**: Situações que podem requerer atenção
- **ERROR**: Problemas que requerem atenção imediata
- **CRITICAL**: Problemas críticos que requerem ação urgente

### 2.3 Canais de Notificação

Os alertas podem ser enviados através dos seguintes canais:

- **Email**: Notificações por e-mail
- **Slack**: Notificações no Slack
- **SMS**: Notificações por SMS (apenas para alertas críticos)
- **Webhook**: Notificações via webhook para integração com outros sistemas
- **Dashboard**: Exibição no dashboard de monitoramento
- **Log**: Registro no sistema de logs

### 2.4 Gerenciamento de Alertas

Os alertas podem ter os seguintes estados:

- **Active**: Alerta ativo que requer atenção
- **Acknowledged**: Alerta reconhecido por um operador
- **Resolved**: Alerta resolvido (automaticamente ou manualmente)

### 2.5 Configuração de Alertas

Cada tipo de alerta pode ser configurado com:

- **Thresholds**: Limiares para diferentes níveis de severidade
- **Channels**: Canais de notificação a serem utilizados
- **Cooldown**: Período mínimo entre alertas do mesmo tipo
- **Additional Config**: Configurações específicas para cada tipo de alerta

## 3. Dashboard

### 3.1 Visão Geral

O dashboard fornece uma visão geral do estado do cluster Kafka, incluindo:

- **Brokers**: Número de brokers e seu estado
- **Tópicos**: Número de tópicos, partições e estado de replicação
- **Consumidores**: Grupos de consumidores, membros e lag
- **Mensagens**: Taxa de mensagens, total de mensagens
- **Alertas**: Alertas ativos, por tipo e severidade
- **Sistema**: Uso de CPU, memória, disco e rede

### 3.2 Páginas Específicas

O dashboard inclui as seguintes páginas:

- **Dashboard Principal**: Visão geral do cluster
- **Tópicos**: Lista de tópicos e detalhes
- **Consumidores**: Lista de grupos de consumidores e detalhes
- **Alertas**: Lista de alertas e gerenciamento
- **Logs**: Consulta e análise de logs

### 3.3 Métricas Coletadas

O sistema coleta e exibe as seguintes métricas:

- **Broker Metrics**: Métricas relacionadas aos brokers
- **Topic Metrics**: Métricas relacionadas aos tópicos
- **Consumer Metrics**: Métricas relacionadas aos consumidores
- **Producer Metrics**: Métricas relacionadas aos produtores
- **System Metrics**: Métricas relacionadas ao sistema (CPU, memória, disco, rede)

### 3.4 Histórico de Métricas

O sistema armazena histórico de métricas para análise de tendências:

- **Métricas gerais**: Armazenadas a cada 5 minutos
- **Métricas de tópicos**: Armazenadas a cada hora
- **Métricas de consumidores**: Armazenadas a cada hora
- **Métricas de sistema**: Armazenadas a cada 5 minutos

## 4. Implementação Técnica

### 4.1 Componentes Principais

O sistema de monitoramento é composto pelos seguintes componentes:

- **kafka-logging.service.ts**: Serviço de logging para o Kafka
- **kafka-alerts.service.ts**: Serviço de alertas para o Kafka
- **kafka-dashboard.service.ts**: Serviço de dashboard para o Kafka
- **kafka-logging.config.ts**: Configuração de logging para o Kafka
- **kafka-alerts.config.ts**: Configuração de alertas para o Kafka

### 4.2 Banco de Dados

O sistema utiliza as seguintes tabelas no banco de dados:

- **tab_kafka_logs**: Armazena logs do Kafka
- **tab_kafka_alerts**: Armazena alertas do Kafka
- **tab_kafka_metrics_history**: Armazena histórico de métricas gerais
- **tab_kafka_topic_metrics**: Armazena métricas de tópicos
- **tab_kafka_consumer_metrics**: Armazena métricas de consumidores

### 4.3 Páginas do Dashboard

O dashboard é composto pelas seguintes páginas:

- **/admin/kafka/dashboard**: Dashboard principal
- **/admin/kafka/topics**: Lista de tópicos
- **/admin/kafka/topic/[name]**: Detalhes de um tópico
- **/admin/kafka/consumers**: Lista de consumidores
- **/admin/kafka/consumer/[id]**: Detalhes de um consumidor
- **/admin/kafka/alerts**: Lista de alertas
- **/admin/kafka/alert/[id]**: Detalhes de um alerta
- **/admin/kafka/logs**: Consulta de logs

### 4.4 APIs

O sistema expõe as seguintes APIs:

- **/api/kafka/metrics**: Obtém métricas atuais do Kafka
- **/api/kafka/metrics/history**: Obtém histórico de métricas
- **/api/kafka/alerts**: Lista alertas
- **/api/kafka/alerts/[id]/acknowledge**: Reconhece um alerta
- **/api/kafka/logs**: Consulta logs

## 5. Configuração e Personalização

### 5.1 Variáveis de Ambiente

O sistema pode ser configurado através das seguintes variáveis de ambiente:

- **KAFKA_LOG_LEVEL**: Nível de log padrão (INFO, DEBUG, etc.)
- **KAFKA_LOG_CONSOLE_ENABLED**: Habilita logs no console
- **KAFKA_LOG_FILE_ENABLED**: Habilita logs em arquivo
- **KAFKA_LOG_DB_ENABLED**: Habilita logs em banco de dados
- **KAFKA_ALERT_*_ENABLED**: Habilita/desabilita tipos específicos de alertas
- **KAFKA_ALERT_*_THRESHOLD**: Define limiares para tipos específicos de alertas
- **KAFKA_ALERT_EMAIL_RECIPIENTS**: Destinatários de alertas por e-mail
- **KAFKA_ALERT_SLACK_WEBHOOK_URL**: URL do webhook do Slack para alertas

### 5.2 Personalização de Alertas

Novos tipos de alertas podem ser adicionados seguindo estes passos:

1. Adicionar o novo tipo ao enum `KafkaAlertType`
2. Adicionar configuração padrão em `defaultAlertConfigs`
3. Implementar a função de verificação no método `checkAlert`
4. Adicionar suporte na interface do usuário

### 5.3 Personalização de Métricas

Novas métricas podem ser adicionadas seguindo estes passos:

1. Adicionar coleta da métrica no método `collectMetrics`
2. Adicionar armazenamento da métrica no método `storeMetricsHistory`
3. Adicionar suporte na interface do usuário

## 6. Melhores Práticas

### 6.1 Monitoramento Proativo

- Configurar alertas para detectar problemas antes que afetem os usuários
- Monitorar tendências para identificar problemas potenciais
- Realizar verificações regulares do sistema

### 6.2 Resposta a Incidentes

- Reconhecer alertas prontamente
- Investigar a causa raiz dos problemas
- Documentar incidentes e soluções
- Implementar melhorias para evitar recorrência

### 6.3 Manutenção

- Limpar logs e métricas antigas regularmente
- Revisar e ajustar limiares de alertas conforme necessário
- Atualizar configurações conforme o cluster evolui
