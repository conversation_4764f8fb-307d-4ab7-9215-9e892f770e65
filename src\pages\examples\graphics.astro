---
import IconSystem from '../../components/icons/IconSystem.astro';
import IllustrationSystem from '../../components/illustrations/IllustrationSystem.astro';
import { PageTransition } from '../../components/transitions';
import DaisyCard from '../../components/ui/DaisyCard.astro';
import Breadcrumbs from '../../components/ui/navigation/Breadcrumbs.astro';
/**
 * Página de demonstração de elementos gráficos
 * Esta página mostra todos os elementos gráficos disponíveis no sistema
 */
import BaseLayout from '../../layouts/BaseLayout.astro';
import { Container, Grid, Section } from '../../layouts/grid';

// Título da página
const title = 'Elementos Gráficos';

// Breadcrumbs para a página atual
const breadcrumbItems = [
  { href: '/', label: '<PERSON><PERSON><PERSON>' },
  { href: '/examples', label: 'Exemplos' },
  { label: 'Elementos Gráficos' },
];

// Lista de ícones para demonstração
const iconCategories = [
  {
    name: 'Navegação',
    icons: ['home', 'menu', 'arrow-left', 'arrow-right', 'close'],
  },
  {
    name: 'Ação',
    icons: ['search', 'settings', 'edit', 'delete'],
  },
  {
    name: 'Conteúdo',
    icons: ['book', 'document', 'folder'],
  },
  {
    name: 'Comunicação',
    icons: ['email', 'chat'],
  },
  {
    name: 'Usuário',
    icons: ['person', 'group'],
  },
  {
    name: 'Notificação',
    icons: ['notification', 'info'],
  },
  {
    name: 'Mídia',
    icons: ['image', 'video', 'audio'],
  },
  {
    name: 'Educação',
    icons: ['school', 'assignment', 'quiz'],
  },
  {
    name: 'Pagamento',
    icons: ['payment', 'credit-card', 'receipt'],
  },
];

// Lista de ilustrações para demonstração
const illustrations = ['reading', 'writing', 'math', 'science', 'alphabet', 'train'];

// Exemplos de animações
const animations = [
  { name: 'fade-in', description: 'Fade In', class: 'animate-fade-in' },
  { name: 'fade-out', description: 'Fade Out', class: 'animate-fade-out' },
  { name: 'slide-in-left', description: 'Slide In Left', class: 'animate-slide-in-left' },
  { name: 'slide-in-right', description: 'Slide In Right', class: 'animate-slide-in-right' },
  { name: 'scale-in', description: 'Scale In', class: 'animate-scale-in' },
  { name: 'rotate', description: 'Rotate', class: 'animate-rotate' },
  { name: 'pulse', description: 'Pulse', class: 'animate-pulse' },
  { name: 'bounce', description: 'Bounce', class: 'animate-bounce' },
  { name: 'letter-pop', description: 'Letter Pop', class: 'animate-letter-pop' },
  { name: 'train-move', description: 'Train Move', class: 'animate-train-move' },
];
---

<BaseLayout title={title} enableViewTransitions={true} transitionType="fade">
  <Container>
    <PageTransition type="fade" duration="0.5s">
      <Section>
        <Breadcrumbs items={breadcrumbItems} class="mb-6" />
        
        <h1 class="text-3xl font-bold mb-6">{title}</h1>
        
        <p class="mb-8">
          Esta página demonstra os elementos gráficos disponíveis no projeto Estação da Alfabetização,
          incluindo ícones, ilustrações e animações básicas.
        </p>
        
        <h2 class="text-2xl font-bold mb-4">Ícones</h2>
        <p class="mb-4">
          O sistema de ícones fornece um conjunto consistente de símbolos visuais para a interface.
          Os ícones são implementados como SVGs e podem ser personalizados em tamanho e cor.
        </p>
        
        {iconCategories.map(category => (
          <div class="mb-8">
            <h3 class="text-xl font-semibold mb-3">{category.name}</h3>
            <div class="flex flex-wrap gap-6 p-4 bg-base-200 rounded-box">
              {category.icons.map(icon => (
                <div class="flex flex-col items-center">
                  <div class="p-4 bg-white rounded-lg shadow-sm mb-2">
                    <IconSystem name={icon} size={32} color="var(--primary-blue)" />
                  </div>
                  <span class="text-sm">{icon}</span>
                </div>
              ))}
            </div>
          </div>
        ))}
        
        <h2 class="text-2xl font-bold mb-4 mt-12">Ilustrações</h2>
        <p class="mb-4">
          As ilustrações são elementos visuais mais complexos que comunicam conceitos educacionais.
          Elas são implementadas como SVGs e podem ser redimensionadas conforme necessário.
        </p>
        
        <Grid cols={{ sm: 1, md: 2, lg: 3 }} gap={6} class="mb-12">
          {illustrations.map(illustration => (
            <DaisyCard title={illustration.charAt(0).toUpperCase() + illustration.slice(1)}>
              <div class="flex justify-center p-4">
                <IllustrationSystem name={illustration} width={200} height={150} />
              </div>
            </DaisyCard>
          ))}
        </Grid>
        
        <h2 class="text-2xl font-bold mb-4 mt-12">Animações CSS</h2>
        <p class="mb-4">
          O sistema inclui animações CSS básicas que podem ser aplicadas a elementos da interface.
          Animações mais complexas são implementadas com AnimeJS.
        </p>
        
        <Grid cols={{ sm: 1, md: 2, lg: 3 }} gap={6} class="mb-12">
          {animations.map(animation => (
            <div class="border rounded-box p-4">
              <h3 class="text-lg font-semibold mb-2">{animation.description}</h3>
              <div class="flex justify-center p-6 bg-base-200 rounded-box h-32 items-center">
                <div class={animation.class}>
                  {animation.name.includes("train") ? (
                    <IllustrationSystem name="train" width={150} height={80} />
                  ) : (
                    <IconSystem name="school" size={48} color="var(--primary-blue)" />
                  )}
                </div>
              </div>
              <pre class="mt-2 p-2 bg-base-200 rounded-box text-xs overflow-x-auto">{animation.class}</pre>
            </div>
          ))}
        </Grid>
        
        <h2 class="text-2xl font-bold mb-4 mt-12">Uso em Componentes</h2>
        <p class="mb-6">
          Exemplos de como usar os elementos gráficos em componentes da interface:
        </p>
        
        <Grid cols={{ sm: 1, md: 2 }} gap={6} class="mb-12">
          <DaisyCard title="Botão com Ícone">
            <div class="p-4 flex flex-col gap-4">
              <button class="btn btn-primary">
                <IconSystem name="search" size={20} class="mr-2" /> Pesquisar
              </button>
              
              <button class="btn btn-secondary">
                <IconSystem name="edit" size={20} class="mr-2" /> Editar
              </button>
              
              <button class="btn btn-accent">
                <IconSystem name="book" size={20} class="mr-2" /> Ler
              </button>
            </div>
            <pre class="mt-2 p-2 bg-base-200 rounded-box text-xs overflow-x-auto">
&lt;button class="btn btn-primary"&gt;
  &lt;IconSystem name="search" size={20} class="mr-2" /&gt; Pesquisar
&lt;/button&gt;
            </pre>
          </DaisyCard>
          
          <DaisyCard title="Card com Ilustração">
            <div class="p-4">
              <div class="card bg-base-100 shadow-md">
                <figure class="px-4 pt-4">
                  <IllustrationSystem name="reading" width={200} height={150} />
                </figure>
                <div class="card-body">
                  <h2 class="card-title">Leitura Infantil</h2>
                  <p>Atividades de leitura para crianças em fase de alfabetização.</p>
                </div>
              </div>
            </div>
            <pre class="mt-2 p-2 bg-base-200 rounded-box text-xs overflow-x-auto">
&lt;div class="card bg-base-100 shadow-md"&gt;
  &lt;figure class="px-4 pt-4"&gt;
    &lt;IllustrationSystem name="reading" width={200} height={150} /&gt;
  &lt;/figure&gt;
  &lt;div class="card-body"&gt;
    &lt;h2 class="card-title"&gt;Leitura Infantil&lt;/h2&gt;
    &lt;p&gt;Atividades de leitura...&lt;/p&gt;
  &lt;/div&gt;
&lt;/div&gt;
            </pre>
          </DaisyCard>
        </Grid>
      </Section>
    </PageTransition>
  </Container>
</BaseLayout>
