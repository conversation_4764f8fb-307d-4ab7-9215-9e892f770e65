---
import { actions } from 'astro:actions';
import ControlButtons from '@components/form/ControlButtons.astro';
import FormBase from '@components/form/FormBase.astro';
import InputHidden from '@components/form/InputHidden.astro';
import InputText from '@components/form/InputText.astro';
import BaseLayout from '@layouts/BaseLayout.astro';
import type { PaymentTypeData } from 'src/database/interfacesHelper';

// Define interfaces for type safety
interface ActionResult {
  data?: PaymentTypeData | PaymentTypeData[];
  error?: string;
}

// Obter parâmetro da URL
const { ulid_payment_type = '' } = Astro.params;

// Busca dados do tipo de pagamento
let data: PaymentTypeData = {};
try {
  const result = (await Astro.callAction(actions.paymentTypeAction.read, {
    filter: 'ulid_payment_type',
    ulid_payment_type,
  })) as ActionResult;
  data = (result.data as PaymentTypeData) || {};
} catch (error) {
  console.error('Erro ao carregar tipo de pagamento:', error);
  return Astro.redirect('/admin/register/payment/type?error=load');
}

const formValidation = `
    const typeInput = form.querySelector('input[name="type"]');
    if (!typeInput || !typeInput.value.trim()) {
        alert('O tipo de pagamento é obrigatório');
        return false;
    }
    return true;
`;
---
<BaseLayout title="Formulário de Tipo de Pagamento">
    <div class="container mx-auto p-4">
        <h1 class="text-2xl font-bold mb-4">
            {ulid_payment_type ? "Editar" : "Novo"} Tipo de Pagamento
        </h1>

        <FormBase
            action={actions.paymentTypeAction.update}
            formType="paymentType"
            onSubmitValidation={formValidation}
        >
            <!-- Campos ocultos -->
            <InputHidden ulid={data.ulid_payment_type ?? ""} field="ulid_payment_type" />

            <!-- Campo nome -->
            <InputText 
                label="Tipo" 
                name="type"
                value={data.type ?? ""}
                required={true}
            />

            <!-- Botões de controle -->
            <ControlButtons 
                saveLabel="Salvar"
                cancelHref="/admin/register/payment/type"
            />
        </FormBase>
    </div>
</BaseLayout>