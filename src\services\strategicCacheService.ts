/**
 * Serviço de Cache Estratégico
 * 
 * Este serviço implementa diferentes estratégias de cache para otimizar
 * o armazenamento e recuperação de dados no Valkey.
 */

import { cacheService } from '@services/cacheService';
import { logger } from '@utils/logger';
import { EventEmitter } from 'events';

/**
 * Estratégias de cache disponíveis
 */
export enum CacheStrategy {
  /**
   * Cache padrão com TTL fixo
   */
  STANDARD = 'standard',
  
  /**
   * Cache com stale-while-revalidate
   * Retorna dados expirados enquanto atualiza em background
   */
  STALE_WHILE_REVALIDATE = 'stale-while-revalidate',
  
  /**
   * Cache com invalidação baseada em eventos
   */
  EVENT_BASED = 'event-based',
  
  /**
   * Cache com variação por usuário
   */
  USER_AWARE = 'user-aware',
  
  /**
   * Cache com variação por dispositivo/navegador
   */
  DEVICE_AWARE = 'device-aware',
  
  /**
   * Cache com variação por localização
   */
  GEO_AWARE = 'geo-aware',
  
  /**
   * Cache com prioridade (LRU com prioridade)
   */
  PRIORITY_BASED = 'priority-based',
  
  /**
   * Cache com prefetching proativo
   */
  PREFETCH = 'prefetch'
}

/**
 * Níveis de prioridade para cache
 */
export enum CachePriority {
  /**
   * Prioridade muito baixa (candidato principal para evicção)
   */
  VERY_LOW = 0,
  
  /**
   * Prioridade baixa
   */
  LOW = 1,
  
  /**
   * Prioridade normal (padrão)
   */
  NORMAL = 2,
  
  /**
   * Prioridade alta
   */
  HIGH = 3,
  
  /**
   * Prioridade muito alta (evitar evicção)
   */
  VERY_HIGH = 4
}

/**
 * Interface para configuração de cache
 */
export interface CacheConfig {
  /**
   * Prefixo para chaves de cache
   */
  keyPrefix: string;
  
  /**
   * TTL padrão em segundos
   */
  defaultTTL: number;
  
  /**
   * Estratégia de cache
   */
  strategy: CacheStrategy;
  
  /**
   * TTL para dados obsoletos (stale)
   * Usado apenas com STALE_WHILE_REVALIDATE
   */
  staleTTL?: number;
  
  /**
   * Eventos que invalidam o cache
   * Usado apenas com EVENT_BASED
   */
  invalidationEvents?: string[];
  
  /**
   * Função para gerar chave de cache
   */
  keyGenerator: (params: Record<string, any>) => string;
  
  /**
   * Prioridade do cache
   * Usado apenas com PRIORITY_BASED
   */
  priority?: CachePriority;
  
  /**
   * Padrões para prefetching
   * Usado apenas com PREFETCH
   */
  prefetchPatterns?: string[];
  
  /**
   * Função para determinar se deve usar cache
   */
  shouldCache?: (value: any) => boolean;
  
  /**
   * Função para transformar dados antes de armazenar
   */
  transformer?: (value: any) => any;
}

/**
 * Configurações de cache por tipo de conteúdo
 */
export const cacheConfigs: Record<string, CacheConfig> = {
  // Dados de usuário
  'user': {
    keyPrefix: 'user',
    defaultTTL: 1800, // 30 minutos
    strategy: CacheStrategy.USER_AWARE,
    invalidationEvents: ['user:update', 'user:delete', 'user:logout'],
    keyGenerator: (params) => `${params.userId}`,
    priority: CachePriority.HIGH,
    shouldCache: (user) => !!user && !user.isGuest
  },
  
  // Dados de produto
  'product': {
    keyPrefix: 'product',
    defaultTTL: 7200, // 2 horas
    strategy: CacheStrategy.STALE_WHILE_REVALIDATE,
    staleTTL: 86400, // 24 horas
    invalidationEvents: ['product:update', 'product:delete', 'product:price'],
    keyGenerator: (params) => `${params.productId || 'list'}:${params.category || 'all'}`,
    priority: CachePriority.NORMAL
  },
  
  // Dados de categoria
  'category': {
    keyPrefix: 'category',
    defaultTTL: 7200, // 2 horas
    strategy: CacheStrategy.STALE_WHILE_REVALIDATE,
    staleTTL: 86400, // 24 horas
    invalidationEvents: ['category:update', 'category:delete'],
    keyGenerator: (params) => `${params.categoryId || 'all'}`,
    priority: CachePriority.HIGH,
    prefetchPatterns: ['category:all']
  },
  
  // Resultados de consulta
  'query': {
    keyPrefix: 'query',
    defaultTTL: 300, // 5 minutos
    strategy: CacheStrategy.STANDARD,
    keyGenerator: (params) => {
      const { query, filters, page, limit } = params;
      return `${query}:${JSON.stringify(filters || {})}:${page || 1}:${limit || 20}`;
    },
    priority: CachePriority.LOW,
    shouldCache: (results) => Array.isArray(results) && results.length > 0
  },
  
  // Dados de sessão
  'session': {
    keyPrefix: 'session',
    defaultTTL: 86400, // 24 horas
    strategy: CacheStrategy.USER_AWARE,
    keyGenerator: (params) => `${params.sessionId}`,
    priority: CachePriority.VERY_HIGH
  },
  
  // Dados de permissão
  'permission': {
    keyPrefix: 'permission',
    defaultTTL: 1800, // 30 minutos
    strategy: CacheStrategy.USER_AWARE,
    invalidationEvents: ['permission:update', 'role:update'],
    keyGenerator: (params) => `${params.userId}:${params.resourceType || 'all'}`,
    priority: CachePriority.HIGH,
    prefetchPatterns: ['permission:admin:all']
  },
  
  // Conteúdo estático
  'static': {
    keyPrefix: 'static',
    defaultTTL: 86400 * 7, // 7 dias
    strategy: CacheStrategy.STANDARD,
    keyGenerator: (params) => `${params.path}`,
    priority: CachePriority.NORMAL
  }
};

/**
 * Emissor de eventos para invalidação baseada em eventos
 */
export const cacheEvents = new EventEmitter();

// Configurar limite de listeners para evitar vazamentos de memória
cacheEvents.setMaxListeners(100);

/**
 * Serviço de cache estratégico
 */
export const strategicCacheService = {
  /**
   * Obtém um item do cache usando a estratégia apropriada
   * @param type Tipo de conteúdo
   * @param params Parâmetros para gerar a chave
   * @param fetchFn Função para buscar dados se não estiverem em cache
   * @returns Dados do cache ou da função de busca
   */
  async get<T>(
    type: string,
    params: Record<string, any>,
    fetchFn?: () => Promise<T>
  ): Promise<T | null> {
    try {
      // Obter configuração para o tipo
      const config = cacheConfigs[type];
      
      if (!config) {
        logger.warn(`Configuração de cache não encontrada para o tipo: ${type}`);
        return fetchFn ? await fetchFn() : null;
      }
      
      // Gerar chave de cache
      const key = `${config.keyPrefix}:${config.keyGenerator(params)}`;
      
      // Estratégia específica para cada tipo de cache
      switch (config.strategy) {
        case CacheStrategy.STALE_WHILE_REVALIDATE:
          return await this.getWithStaleWhileRevalidate<T>(key, config, fetchFn);
          
        case CacheStrategy.EVENT_BASED:
          return await this.getWithEventBasedInvalidation<T>(key, config, fetchFn);
          
        case CacheStrategy.USER_AWARE:
          return await this.getWithUserAwareness<T>(key, params, config, fetchFn);
          
        case CacheStrategy.PRIORITY_BASED:
          return await this.getWithPriority<T>(key, config, fetchFn);
          
        case CacheStrategy.PREFETCH:
          return await this.getWithPrefetch<T>(key, config, fetchFn);
          
        case CacheStrategy.STANDARD:
        default:
          return await this.getStandard<T>(key, config, fetchFn);
      }
    } catch (error) {
      logger.error('Erro ao obter item do cache estratégico:', error);
      
      // Em caso de erro no cache, tentar buscar dados diretamente
      if (fetchFn) {
        try {
          return await fetchFn();
        } catch (fetchError) {
          logger.error('Erro ao buscar dados após falha de cache:', fetchError);
          return null;
        }
      }
      
      return null;
    }
  },
  
  /**
   * Estratégia padrão de cache
   * @param key Chave de cache
   * @param config Configuração de cache
   * @param fetchFn Função para buscar dados
   * @returns Dados do cache ou da função de busca
   */
  async getStandard<T>(
    key: string,
    config: CacheConfig,
    fetchFn?: () => Promise<T>
  ): Promise<T | null> {
    // Tentar obter do cache
    const cachedData = await cacheService.get<T>(key);
    
    if (cachedData) {
      return cachedData;
    }
    
    // Se não estiver em cache e não tiver função de busca, retornar null
    if (!fetchFn) {
      return null;
    }
    
    // Buscar dados
    const data = await fetchFn();
    
    // Verificar se deve armazenar em cache
    if (data && (!config.shouldCache || config.shouldCache(data))) {
      // Transformar dados se necessário
      const valueToCache = config.transformer ? config.transformer(data) : data;
      
      // Armazenar em cache
      await cacheService.set(key, valueToCache, config.defaultTTL);
    }
    
    return data;
  },
  
  /**
   * Estratégia stale-while-revalidate
   * Retorna dados expirados enquanto atualiza em background
   * @param key Chave de cache
   * @param config Configuração de cache
   * @param fetchFn Função para buscar dados
   * @returns Dados do cache ou da função de busca
   */
  async getWithStaleWhileRevalidate<T>(
    key: string,
    config: CacheConfig,
    fetchFn?: () => Promise<T>
  ): Promise<T | null> {
    // Chaves para dados frescos e obsoletos
    const freshKey = key;
    const staleKey = `${key}:stale`;
    const metaKey = `${key}:meta`;
    
    // Tentar obter dados frescos
    const freshData = await cacheService.get<T>(freshKey);
    
    if (freshData) {
      return freshData;
    }
    
    // Se não tiver dados frescos, verificar dados obsoletos
    const staleData = await cacheService.get<T>(staleKey);
    
    // Se não tiver função de busca, retornar dados obsoletos ou null
    if (!fetchFn) {
      return staleData || null;
    }
    
    // Verificar metadados para evitar múltiplas atualizações simultâneas
    const meta = await cacheService.get<{ updating: boolean }>(metaKey);
    
    if (meta && meta.updating) {
      // Se estiver atualizando, retornar dados obsoletos
      return staleData || null;
    }
    
    // Marcar como atualizando
    await cacheService.set(metaKey, { updating: true }, 60); // 1 minuto
    
    try {
      // Buscar dados em background
      const fetchPromise = fetchFn().then(async (data) => {
        if (data && (!config.shouldCache || config.shouldCache(data))) {
          // Transformar dados se necessário
          const valueToCache = config.transformer ? config.transformer(data) : data;
          
          // Armazenar como dados frescos
          await cacheService.set(freshKey, valueToCache, config.defaultTTL);
          
          // Armazenar como dados obsoletos com TTL maior
          await cacheService.set(staleKey, valueToCache, config.staleTTL || config.defaultTTL * 2);
          
          // Limpar metadados
          await cacheService.del(metaKey);
        }
        
        return data;
      }).catch((error) => {
        logger.error('Erro ao atualizar cache em background:', error);
        // Limpar metadados em caso de erro
        cacheService.del(metaKey).catch(() => {});
        return null;
      });
      
      // Se tiver dados obsoletos, retornar imediatamente e atualizar em background
      if (staleData) {
        // Executar atualização em background
        fetchPromise.catch(() => {});
        return staleData;
      }
      
      // Se não tiver dados obsoletos, aguardar a busca
      const data = await fetchPromise;
      return data;
    } catch (error) {
      // Limpar metadados em caso de erro
      await cacheService.del(metaKey);
      throw error;
    }
  },
  
  /**
   * Estratégia baseada em eventos
   * @param key Chave de cache
   * @param config Configuração de cache
   * @param fetchFn Função para buscar dados
   * @returns Dados do cache ou da função de busca
   */
  async getWithEventBasedInvalidation<T>(
    key: string,
    config: CacheConfig,
    fetchFn?: () => Promise<T>
  ): Promise<T | null> {
    // Registrar listeners para eventos de invalidação
    this.registerInvalidationEvents(key, config);
    
    // Usar estratégia padrão para o resto
    return this.getStandard<T>(key, config, fetchFn);
  },
  
  /**
   * Estratégia com consciência de usuário
   * @param key Chave de cache
   * @param params Parâmetros para gerar a chave
   * @param config Configuração de cache
   * @param fetchFn Função para buscar dados
   * @returns Dados do cache ou da função de busca
   */
  async getWithUserAwareness<T>(
    key: string,
    params: Record<string, any>,
    config: CacheConfig,
    fetchFn?: () => Promise<T>
  ): Promise<T | null> {
    // Adicionar ID do usuário à chave se disponível
    const userId = params.userId || 'anonymous';
    const userAwareKey = `${key}:user:${userId}`;
    
    // Registrar eventos de invalidação específicos do usuário
    if (config.invalidationEvents) {
      this.registerUserInvalidationEvents(userAwareKey, userId, config);
    }
    
    // Usar estratégia padrão com a chave específica do usuário
    return this.getStandard<T>(userAwareKey, config, fetchFn);
  },
  
  /**
   * Estratégia baseada em prioridade
   * @param key Chave de cache
   * @param config Configuração de cache
   * @param fetchFn Função para buscar dados
   * @returns Dados do cache ou da função de busca
   */
  async getWithPriority<T>(
    key: string,
    config: CacheConfig,
    fetchFn?: () => Promise<T>
  ): Promise<T | null> {
    // Chave para metadados de prioridade
    const priorityKey = `${key}:priority`;
    
    // Definir prioridade nos metadados
    const priority = config.priority || CachePriority.NORMAL;
    await cacheService.set(priorityKey, { priority }, config.defaultTTL);
    
    // Usar estratégia padrão para o resto
    return this.getStandard<T>(key, config, fetchFn);
  },
  
  /**
   * Estratégia com prefetching
   * @param key Chave de cache
   * @param config Configuração de cache
   * @param fetchFn Função para buscar dados
   * @returns Dados do cache ou da função de busca
   */
  async getWithPrefetch<T>(
    key: string,
    config: CacheConfig,
    fetchFn?: () => Promise<T>
  ): Promise<T | null> {
    // Usar estratégia padrão para o item atual
    const result = await this.getStandard<T>(key, config, fetchFn);
    
    // Se tiver padrões de prefetch e função de busca, iniciar prefetch em background
    if (config.prefetchPatterns && fetchFn) {
      this.prefetchRelatedItems(key, config);
    }
    
    return result;
  },
  
  /**
   * Registra eventos de invalidação para uma chave
   * @param key Chave de cache
   * @param config Configuração de cache
   */
  registerInvalidationEvents(key: string, config: CacheConfig): void {
    if (!config.invalidationEvents || config.invalidationEvents.length === 0) {
      return;
    }
    
    // Para cada evento de invalidação
    for (const event of config.invalidationEvents) {
      // Verificar se já tem listener para evitar duplicação
      if (cacheEvents.listenerCount(event) < 100) { // Limite de segurança
        cacheEvents.on(event, (eventData: any) => {
          // Invalidar cache para a chave
          cacheService.del(key).catch((error) => {
            logger.error(`Erro ao invalidar cache para ${key} após evento ${event}:`, error);
          });
          
          logger.debug(`Cache invalidado para ${key} após evento ${event}`, eventData);
        });
      }
    }
  },
  
  /**
   * Registra eventos de invalidação específicos para um usuário
   * @param key Chave de cache
   * @param userId ID do usuário
   * @param config Configuração de cache
   */
  registerUserInvalidationEvents(key: string, userId: string, config: CacheConfig): void {
    if (!config.invalidationEvents || config.invalidationEvents.length === 0) {
      return;
    }
    
    // Para cada evento de invalidação
    for (const event of config.invalidationEvents) {
      // Criar evento específico para o usuário
      const userEvent = `${event}:${userId}`;
      
      // Verificar se já tem listener para evitar duplicação
      if (cacheEvents.listenerCount(userEvent) < 100) { // Limite de segurança
        cacheEvents.on(userEvent, (eventData: any) => {
          // Invalidar cache para a chave
          cacheService.del(key).catch((error) => {
            logger.error(`Erro ao invalidar cache para ${key} após evento ${userEvent}:`, error);
          });
          
          logger.debug(`Cache invalidado para ${key} após evento ${userEvent}`, eventData);
        });
      }
    }
  },
  
  /**
   * Realiza prefetch de itens relacionados
   * @param key Chave de cache atual
   * @param config Configuração de cache
   */
  prefetchRelatedItems(key: string, config: CacheConfig): void {
    if (!config.prefetchPatterns || config.prefetchPatterns.length === 0) {
      return;
    }
    
    // Para cada padrão de prefetch
    for (const pattern of config.prefetchPatterns) {
      // Buscar chaves que correspondem ao padrão
      cacheService.keys(`${config.keyPrefix}:${pattern}`).then((keys) => {
        // Filtrar a chave atual
        const relatedKeys = keys.filter((k) => k !== key);
        
        // Prefetch para cada chave relacionada
        for (const relatedKey of relatedKeys) {
          // Verificar se já está em cache
          cacheService.exists(relatedKey).then((exists) => {
            if (!exists) {
              // Se não estiver em cache, buscar em background
              logger.debug(`Prefetching para chave relacionada: ${relatedKey}`);
              
              // Aqui seria necessário ter uma função de busca específica para cada chave
              // Como não temos isso disponível, apenas registramos a intenção
              logger.debug(`Prefetch para ${relatedKey} seria executado aqui`);
            }
          }).catch((error) => {
            logger.error(`Erro ao verificar existência de ${relatedKey} para prefetch:`, error);
          });
        }
      }).catch((error) => {
        logger.error(`Erro ao buscar chaves para prefetch com padrão ${pattern}:`, error);
      });
    }
  },
  
  /**
   * Define um item no cache
   * @param type Tipo de conteúdo
   * @param params Parâmetros para gerar a chave
   * @param data Dados a serem armazenados
   * @returns Sucesso da operação
   */
  async set<T>(
    type: string,
    params: Record<string, any>,
    data: T
  ): Promise<boolean> {
    try {
      // Obter configuração para o tipo
      const config = cacheConfigs[type];
      
      if (!config) {
        logger.warn(`Configuração de cache não encontrada para o tipo: ${type}`);
        return false;
      }
      
      // Verificar se deve armazenar em cache
      if (config.shouldCache && !config.shouldCache(data)) {
        return false;
      }
      
      // Gerar chave de cache
      const key = `${config.keyPrefix}:${config.keyGenerator(params)}`;
      
      // Transformar dados se necessário
      const valueToCache = config.transformer ? config.transformer(data) : data;
      
      // Estratégia específica para cada tipo de cache
      switch (config.strategy) {
        case CacheStrategy.STALE_WHILE_REVALIDATE:
          // Armazenar como dados frescos e obsoletos
          await cacheService.set(key, valueToCache, config.defaultTTL);
          await cacheService.set(`${key}:stale`, valueToCache, config.staleTTL || config.defaultTTL * 2);
          break;
          
        case CacheStrategy.USER_AWARE:
          // Adicionar ID do usuário à chave se disponível
          const userId = params.userId || 'anonymous';
          const userAwareKey = `${key}:user:${userId}`;
          await cacheService.set(userAwareKey, valueToCache, config.defaultTTL);
          break;
          
        case CacheStrategy.PRIORITY_BASED:
          // Armazenar dados e metadados de prioridade
          await cacheService.set(key, valueToCache, config.defaultTTL);
          await cacheService.set(`${key}:priority`, { priority: config.priority || CachePriority.NORMAL }, config.defaultTTL);
          break;
          
        case CacheStrategy.STANDARD:
        case CacheStrategy.EVENT_BASED:
        case CacheStrategy.PREFETCH:
        default:
          // Armazenar normalmente
          await cacheService.set(key, valueToCache, config.defaultTTL);
          break;
      }
      
      return true;
    } catch (error) {
      logger.error('Erro ao definir item no cache estratégico:', error);
      return false;
    }
  },
  
  /**
   * Remove um item do cache
   * @param type Tipo de conteúdo
   * @param params Parâmetros para gerar a chave
   * @returns Sucesso da operação
   */
  async del(
    type: string,
    params: Record<string, any>
  ): Promise<boolean> {
    try {
      // Obter configuração para o tipo
      const config = cacheConfigs[type];
      
      if (!config) {
        logger.warn(`Configuração de cache não encontrada para o tipo: ${type}`);
        return false;
      }
      
      // Gerar chave de cache
      const key = `${config.keyPrefix}:${config.keyGenerator(params)}`;
      
      // Estratégia específica para cada tipo de cache
      switch (config.strategy) {
        case CacheStrategy.STALE_WHILE_REVALIDATE:
          // Remover dados frescos, obsoletos e metadados
          await Promise.all([
            cacheService.del(key),
            cacheService.del(`${key}:stale`),
            cacheService.del(`${key}:meta`)
          ]);
          break;
          
        case CacheStrategy.USER_AWARE:
          // Adicionar ID do usuário à chave se disponível
          const userId = params.userId || 'anonymous';
          const userAwareKey = `${key}:user:${userId}`;
          await cacheService.del(userAwareKey);
          break;
          
        case CacheStrategy.PRIORITY_BASED:
          // Remover dados e metadados de prioridade
          await Promise.all([
            cacheService.del(key),
            cacheService.del(`${key}:priority`)
          ]);
          break;
          
        case CacheStrategy.STANDARD:
        case CacheStrategy.EVENT_BASED:
        case CacheStrategy.PREFETCH:
        default:
          // Remover normalmente
          await cacheService.del(key);
          break;
      }
      
      return true;
    } catch (error) {
      logger.error('Erro ao remover item do cache estratégico:', error);
      return false;
    }
  },
  
  /**
   * Emite um evento para invalidar cache
   * @param event Nome do evento
   * @param data Dados do evento
   */
  emitInvalidationEvent(event: string, data?: any): void {
    cacheEvents.emit(event, data);
    logger.debug(`Evento de invalidação emitido: ${event}`, data);
  },
  
  /**
   * Emite um evento de invalidação específico para um usuário
   * @param event Nome do evento base
   * @param userId ID do usuário
   * @param data Dados do evento
   */
  emitUserInvalidationEvent(event: string, userId: string, data?: any): void {
    const userEvent = `${event}:${userId}`;
    cacheEvents.emit(userEvent, data);
    logger.debug(`Evento de invalidação para usuário emitido: ${userEvent}`, data);
  },
  
  /**
   * Limpa todo o cache para um tipo específico
   * @param type Tipo de conteúdo
   * @returns Número de chaves removidas
   */
  async clearType(type: string): Promise<number> {
    try {
      // Obter configuração para o tipo
      const config = cacheConfigs[type];
      
      if (!config) {
        logger.warn(`Configuração de cache não encontrada para o tipo: ${type}`);
        return 0;
      }
      
      // Padrão para buscar todas as chaves do tipo
      const pattern = `${config.keyPrefix}:*`;
      
      // Buscar todas as chaves que correspondem ao padrão
      const keys = await cacheService.keys(pattern);
      
      if (keys.length === 0) {
        return 0;
      }
      
      // Remover todas as chaves
      const result = await cacheService.del(...keys);
      
      logger.info(`Cache limpo para tipo ${type}: ${result} chaves removidas`);
      
      return result;
    } catch (error) {
      logger.error(`Erro ao limpar cache para tipo ${type}:`, error);
      return 0;
    }
  }
};
