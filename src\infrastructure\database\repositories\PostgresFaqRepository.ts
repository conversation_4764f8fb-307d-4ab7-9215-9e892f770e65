/**
 * PostgreSQL FAQ Repository
 *
 * Implementação do repositório de itens de FAQ usando PostgreSQL.
 * Parte da implementação da tarefa 8.7.1 - Implementação de FAQ interativo
 */

import { Pool } from 'pg';
import { FaqCategory, FaqItem } from '../../../domain/entities/FaqItem';
import {
  FaqFilter,
  FaqPaginationOptions,
  FaqRepository,
  FaqSortOptions,
  PaginatedFaqItems,
} from '../../../domain/repositories/FaqRepository';
import { getDbConnection } from '../connection';

export class PostgresFaqRepository implements FaqRepository {
  private pool: Pool;

  constructor() {
    this.pool = getDbConnection();
  }

  async create(faqItem: FaqItem): Promise<FaqItem> {
    const query = `
      INSERT INTO faq_items (
        id, question, answer, category, tags, is_published,
        view_count, helpful_count, not_helpful_count, related_faq_ids,
        created_at, updated_at
      )
      VALUES (
        $1, $2, $3, $4, $5, $6,
        $7, $8, $9, $10,
        $11, $12
      )
      RETURNING *
    `;

    const values = [
      faqItem.id,
      faqItem.question,
      faqItem.answer,
      faqItem.category,
      JSON.stringify(faqItem.tags),
      faqItem.isPublished,
      faqItem.viewCount,
      faqItem.helpfulCount,
      faqItem.notHelpfulCount,
      JSON.stringify(faqItem.relatedFaqIds),
      faqItem.createdAt,
      faqItem.updatedAt,
    ];

    const result = await this.pool.query(query, values);

    return this.mapDbFaqItemToEntity(result.rows[0]);
  }

  async update(faqItem: FaqItem): Promise<FaqItem> {
    const query = `
      UPDATE faq_items
      SET
        question = $2,
        answer = $3,
        category = $4,
        tags = $5,
        is_published = $6,
        view_count = $7,
        helpful_count = $8,
        not_helpful_count = $9,
        related_faq_ids = $10,
        updated_at = $11
      WHERE id = $1
      RETURNING *
    `;

    const values = [
      faqItem.id,
      faqItem.question,
      faqItem.answer,
      faqItem.category,
      JSON.stringify(faqItem.tags),
      faqItem.isPublished,
      faqItem.viewCount,
      faqItem.helpfulCount,
      faqItem.notHelpfulCount,
      JSON.stringify(faqItem.relatedFaqIds),
      faqItem.updatedAt,
    ];

    const result = await this.pool.query(query, values);

    if (result.rows.length === 0) {
      throw new Error(`Item de FAQ com ID ${faqItem.id} não encontrado`);
    }

    return this.mapDbFaqItemToEntity(result.rows[0]);
  }

  async getById(id: string): Promise<FaqItem | null> {
    const query = `
      SELECT * FROM faq_items
      WHERE id = $1
    `;

    const result = await this.pool.query(query, [id]);

    if (result.rows.length === 0) {
      return null;
    }

    return this.mapDbFaqItemToEntity(result.rows[0]);
  }

  async find(
    filter: FaqFilter,
    sort?: FaqSortOptions,
    pagination?: FaqPaginationOptions
  ): Promise<PaginatedFaqItems> {
    // Construir a consulta base
    let query = 'SELECT * FROM faq_items WHERE 1=1';
    let countQuery = 'SELECT COUNT(*) FROM faq_items WHERE 1=1';
    const values: any[] = [];
    let paramIndex = 1;

    // Adicionar filtros
    if (filter.ids && filter.ids.length > 0) {
      query += ` AND id = ANY($${paramIndex})`;
      countQuery += ` AND id = ANY($${paramIndex})`;
      values.push(filter.ids);
      paramIndex++;
    }

    if (filter.category) {
      if (Array.isArray(filter.category)) {
        query += ` AND category = ANY($${paramIndex})`;
        countQuery += ` AND category = ANY($${paramIndex})`;
        values.push(filter.category);
      } else {
        query += ` AND category = $${paramIndex}`;
        countQuery += ` AND category = $${paramIndex}`;
        values.push(filter.category);
      }
      paramIndex++;
    }

    if (filter.tags && filter.tags.length > 0) {
      // Filtrar por tags usando operador de array
      query += ` AND tags @> $${paramIndex}::jsonb`;
      countQuery += ` AND tags @> $${paramIndex}::jsonb`;
      values.push(JSON.stringify(filter.tags));
      paramIndex++;
    }

    if (filter.isPublished !== undefined) {
      query += ` AND is_published = $${paramIndex}`;
      countQuery += ` AND is_published = $${paramIndex}`;
      values.push(filter.isPublished);
      paramIndex++;
    }

    if (filter.search) {
      query += ` AND (
        question ILIKE $${paramIndex} OR
        answer ILIKE $${paramIndex}
      )`;
      countQuery += ` AND (
        question ILIKE $${paramIndex} OR
        answer ILIKE $${paramIndex}
      )`;
      values.push(`%${filter.search}%`);
      paramIndex++;
    }

    // Adicionar ordenação
    if (sort) {
      const fieldMap: Record<string, string> = {
        question: 'question',
        viewCount: 'view_count',
        helpfulCount: 'helpful_count',
        createdAt: 'created_at',
        updatedAt: 'updated_at',
      };

      const field = fieldMap[sort.field] || 'created_at';
      const direction = sort.direction.toUpperCase() === 'DESC' ? 'DESC' : 'ASC';

      query += ` ORDER BY ${field} ${direction}`;
    } else {
      query += ' ORDER BY created_at DESC';
    }

    // Adicionar paginação
    if (pagination) {
      const limit = pagination.limit || 10;
      const offset = ((pagination.page || 1) - 1) * limit;

      query += ` LIMIT $${paramIndex} OFFSET $${paramIndex + 1}`;
      values.push(limit, offset);
      paramIndex += 2;
    }

    // Executar consultas
    const [itemsResult, countResult] = await Promise.all([
      this.pool.query(query, values),
      this.pool.query(countQuery, values.slice(0, values.length - (pagination ? 2 : 0))),
    ]);

    // Mapear resultados
    const items = itemsResult.rows.map((row) => this.mapDbFaqItemToEntity(row));
    const total = Number.parseInt(countResult.rows[0].count, 10);

    return {
      items,
      total,
      page: pagination?.page || 1,
      limit: pagination?.limit || items.length,
      totalPages: pagination ? Math.ceil(total / pagination.limit) : 1,
    };
  }

  async getPublishedItems(
    filter?: FaqFilter,
    sort?: FaqSortOptions,
    pagination?: FaqPaginationOptions
  ): Promise<PaginatedFaqItems> {
    return this.find({ ...filter, isPublished: true }, sort, pagination);
  }

  async getByCategory(
    category: FaqCategory,
    onlyPublished?: boolean,
    pagination?: FaqPaginationOptions
  ): Promise<PaginatedFaqItems> {
    return this.find(
      {
        category,
        isPublished: onlyPublished === undefined ? undefined : onlyPublished,
      },
      { field: 'question', direction: 'asc' },
      pagination
    );
  }

  async getByTag(
    tag: string,
    onlyPublished?: boolean,
    pagination?: FaqPaginationOptions
  ): Promise<PaginatedFaqItems> {
    return this.find(
      {
        tags: [tag],
        isPublished: onlyPublished === undefined ? undefined : onlyPublished,
      },
      { field: 'question', direction: 'asc' },
      pagination
    );
  }

  async search(
    query: string,
    onlyPublished?: boolean,
    pagination?: FaqPaginationOptions
  ): Promise<PaginatedFaqItems> {
    return this.find(
      {
        search: query,
        isPublished: onlyPublished === undefined ? undefined : onlyPublished,
      },
      { field: 'viewCount', direction: 'desc' },
      pagination
    );
  }

  async getRelatedItems(
    faqId: string,
    onlyPublished?: boolean,
    limit?: number
  ): Promise<FaqItem[]> {
    // Primeiro, obter o item de FAQ para acessar seus IDs relacionados
    const faqItem = await this.getById(faqId);

    if (!faqItem || faqItem.relatedFaqIds.length === 0) {
      return [];
    }

    // Construir a consulta
    let query = `
      SELECT * FROM faq_items
      WHERE id = ANY($1)
    `;

    const values: any[] = [faqItem.relatedFaqIds];
    let paramIndex = 2;

    if (onlyPublished) {
      query += ` AND is_published = $${paramIndex}`;
      values.push(true);
      paramIndex++;
    }

    query += ' ORDER BY view_count DESC';

    if (limit) {
      query += ` LIMIT $${paramIndex}`;
      values.push(limit);
    }

    const result = await this.pool.query(query, values);

    return result.rows.map((row) => this.mapDbFaqItemToEntity(row));
  }

  async incrementViewCount(id: string): Promise<boolean> {
    const query = `
      UPDATE faq_items
      SET view_count = view_count + 1, updated_at = NOW()
      WHERE id = $1
      RETURNING id
    `;

    const result = await this.pool.query(query, [id]);

    return result.rows.length > 0;
  }

  async markAsHelpful(id: string): Promise<boolean> {
    const query = `
      UPDATE faq_items
      SET helpful_count = helpful_count + 1, updated_at = NOW()
      WHERE id = $1
      RETURNING id
    `;

    const result = await this.pool.query(query, [id]);

    return result.rows.length > 0;
  }

  async markAsNotHelpful(id: string): Promise<boolean> {
    const query = `
      UPDATE faq_items
      SET not_helpful_count = not_helpful_count + 1, updated_at = NOW()
      WHERE id = $1
      RETURNING id
    `;

    const result = await this.pool.query(query, [id]);

    return result.rows.length > 0;
  }

  async publish(id: string): Promise<boolean> {
    const query = `
      UPDATE faq_items
      SET is_published = TRUE, updated_at = NOW()
      WHERE id = $1
      RETURNING id
    `;

    const result = await this.pool.query(query, [id]);

    return result.rows.length > 0;
  }

  async unpublish(id: string): Promise<boolean> {
    const query = `
      UPDATE faq_items
      SET is_published = FALSE, updated_at = NOW()
      WHERE id = $1
      RETURNING id
    `;

    const result = await this.pool.query(query, [id]);

    return result.rows.length > 0;
  }

  async addTag(id: string, tag: string): Promise<boolean> {
    const query = `
      UPDATE faq_items
      SET tags = CASE
        WHEN tags IS NULL THEN jsonb_build_array($2)
        WHEN NOT tags @> jsonb_build_array($2) THEN tags || jsonb_build_array($2)
        ELSE tags
      END,
      updated_at = NOW()
      WHERE id = $1
      RETURNING id
    `;

    const result = await this.pool.query(query, [id, tag]);

    return result.rows.length > 0;
  }

  async removeTag(id: string, tag: string): Promise<boolean> {
    const query = `
      UPDATE faq_items
      SET tags = (
        SELECT jsonb_agg(t) FROM (
          SELECT jsonb_array_elements(tags) AS t
          WHERE t <> $2::jsonb
        ) subquery
      ),
      updated_at = NOW()
      WHERE id = $1 AND tags @> jsonb_build_array($2)
      RETURNING id
    `;

    const result = await this.pool.query(query, [id, tag]);

    return result.rows.length > 0;
  }

  async addRelatedItem(id: string, relatedId: string): Promise<boolean> {
    const query = `
      UPDATE faq_items
      SET related_faq_ids = CASE
        WHEN related_faq_ids IS NULL THEN jsonb_build_array($2)
        WHEN NOT related_faq_ids @> jsonb_build_array($2) THEN related_faq_ids || jsonb_build_array($2)
        ELSE related_faq_ids
      END,
      updated_at = NOW()
      WHERE id = $1
      RETURNING id
    `;

    const result = await this.pool.query(query, [id, relatedId]);

    return result.rows.length > 0;
  }

  async removeRelatedItem(id: string, relatedId: string): Promise<boolean> {
    const query = `
      UPDATE faq_items
      SET related_faq_ids = (
        SELECT jsonb_agg(r) FROM (
          SELECT jsonb_array_elements(related_faq_ids) AS r
          WHERE r <> $2::jsonb
        ) subquery
      ),
      updated_at = NOW()
      WHERE id = $1 AND related_faq_ids @> jsonb_build_array($2)
      RETURNING id
    `;

    const result = await this.pool.query(query, [id, relatedId]);

    return result.rows.length > 0;
  }

  async delete(id: string): Promise<boolean> {
    const query = `
      DELETE FROM faq_items
      WHERE id = $1
      RETURNING id
    `;

    const result = await this.pool.query(query, [id]);

    return result.rows.length > 0;
  }

  async getStats(): Promise<{
    totalItems: number;
    publishedItems: number;
    totalViews: number;
    mostViewedItem: {
      id: string;
      question: string;
      viewCount: number;
    } | null;
    mostHelpfulItem: {
      id: string;
      question: string;
      helpfulnessRate: number;
    } | null;
    categoryDistribution: Record<FaqCategory, number>;
  }> {
    const query = `
      SELECT * FROM faq_stats
      ORDER BY updated_at DESC
      LIMIT 1
    `;

    const result = await this.pool.query(query);

    if (result.rows.length === 0) {
      return {
        totalItems: 0,
        publishedItems: 0,
        totalViews: 0,
        mostViewedItem: null,
        mostHelpfulItem: null,
        categoryDistribution: {} as Record<FaqCategory, number>,
      };
    }

    const stats = result.rows[0];

    return {
      totalItems: stats.total_items,
      publishedItems: stats.published_items,
      totalViews: stats.total_views,
      mostViewedItem: stats.most_viewed_item_id
        ? {
            id: stats.most_viewed_item_id,
            question: stats.most_viewed_item_question,
            viewCount: stats.most_viewed_item_count,
          }
        : null,
      mostHelpfulItem: stats.most_helpful_item_id
        ? {
            id: stats.most_helpful_item_id,
            question: stats.most_helpful_item_question,
            helpfulnessRate: stats.most_helpful_item_rate,
          }
        : null,
      categoryDistribution: stats.category_distribution,
    };
  }

  private mapDbFaqItemToEntity(dbFaqItem: any): FaqItem {
    return new FaqItem({
      id: dbFaqItem.id,
      question: dbFaqItem.question,
      answer: dbFaqItem.answer,
      category: dbFaqItem.category as FaqCategory,
      tags: dbFaqItem.tags || [],
      isPublished: dbFaqItem.is_published,
      viewCount: dbFaqItem.view_count,
      helpfulCount: dbFaqItem.helpful_count,
      notHelpfulCount: dbFaqItem.not_helpful_count,
      relatedFaqIds: dbFaqItem.related_faq_ids || [],
      createdAt: dbFaqItem.created_at,
      updatedAt: dbFaqItem.updated_at,
    });
  }
}
