---
import { PageTransition } from '../../../components/transitions';
import DaisyButton from '../../../components/ui/DaisyButton.astro';
import DaisyCard from '../../../components/ui/DaisyCard.astro';
import Breadcrumbs from '../../../components/ui/navigation/Breadcrumbs.astro';
/**
 * Página de Upload de Documentos
 *
 * Interface para fazer upload de documentos PDF no sistema.
 * Parte da implementação da tarefa 8.3.1 - Armazenamento de PDFs
 */
import BaseLayout from '../../../layouts/BaseLayout.astro';
import { Container, Section } from '../../../layouts/grid';

// Título da página
const title = 'Upload de Documento';

// Breadcrumbs para a página atual
const breadcrumbItems = [
  { href: '/', label: 'Início' },
  { href: '/admin', label: 'Administração' },
  { href: '/admin/documents', label: 'Documentos' },
  { label: 'Upload' },
];

// Categorias disponíveis
const categories = [
  { value: 'Alfabetização', label: 'Alfabetização' },
  { value: 'Matemática', label: 'Matemática' },
  { value: 'Português', label: 'Português' },
  { value: 'Ciências', label: 'Ciências' },
  { value: 'Geral', label: 'Geral' },
];

// Processar o formulário de upload
if (Astro.request.method === 'POST') {
  try {
    // Em um cenário real, aqui seria implementada a lógica para:
    // 1. Receber o arquivo PDF
    // 2. Validar o arquivo (tipo, tamanho, etc.)
    // 3. Processar metadados
    // 4. Salvar no repositório
    // 5. Redirecionar para a página de detalhes ou lista

    // Por enquanto, apenas simularemos o redirecionamento
    return Astro.redirect('/admin/documents?success=true');
  } catch (error) {
    console.error('Erro ao processar upload:', error);
    // Em caso de erro, continuamos na página com uma mensagem de erro
  }
}
---

<BaseLayout title={title} enableViewTransitions={true} transitionType="fade">
  <Container>
    <PageTransition type="fade" duration="0.5s">
      <Section>
        <Breadcrumbs items={breadcrumbItems} class="mb-6" />
        
        <h1 class="text-3xl font-bold mb-6">{title}</h1>
        
        <DaisyCard>
          <div class="p-6">
            <form method="POST" enctype="multipart/form-data" id="upload-form" class="space-y-6">
              <div class="form-control">
                <label class="label">
                  <span class="label-text font-medium">Título do Documento*</span>
                </label>
                <input 
                  type="text" 
                  name="title" 
                  placeholder="Digite o título do documento" 
                  class="input input-bordered w-full" 
                  required
                />
              </div>
              
              <div class="form-control">
                <label class="label">
                  <span class="label-text font-medium">Descrição</span>
                </label>
                <textarea 
                  name="description" 
                  placeholder="Digite uma descrição para o documento" 
                  class="textarea textarea-bordered h-24"
                ></textarea>
              </div>
              
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="form-control">
                  <label class="label">
                    <span class="label-text font-medium">Categoria*</span>
                  </label>
                  <select name="category" class="select select-bordered w-full" required>
                    <option value="" disabled selected>Selecione uma categoria</option>
                    {categories.map(cat => (
                      <option value={cat.value}>{cat.label}</option>
                    ))}
                  </select>
                </div>
                
                <div class="form-control">
                  <label class="label">
                    <span class="label-text font-medium">Autor</span>
                  </label>
                  <input 
                    type="text" 
                    name="author" 
                    placeholder="Nome do autor" 
                    class="input input-bordered w-full"
                  />
                </div>
              </div>
              
              <div class="form-control">
                <label class="label">
                  <span class="label-text font-medium">Tags</span>
                </label>
                <input 
                  type="text" 
                  name="tags" 
                  placeholder="Digite tags separadas por vírgula" 
                  class="input input-bordered w-full"
                />
                <label class="label">
                  <span class="label-text-alt">Ex: alfabetização, leitura, educação</span>
                </label>
              </div>
              
              <div class="form-control">
                <label class="label">
                  <span class="label-text font-medium">Arquivo PDF*</span>
                </label>
                <div class="border-2 border-dashed border-base-300 rounded-lg p-8 text-center" id="drop-area">
                  <input 
                    type="file" 
                    name="pdfFile" 
                    id="pdf-file" 
                    accept="application/pdf" 
                    class="hidden" 
                    required
                  />
                  
                  <div class="flex flex-col items-center justify-center">
                    <i class="icon icon-upload text-4xl mb-4 text-primary"></i>
                    <p class="mb-2">Arraste e solte seu arquivo PDF aqui</p>
                    <p class="text-sm text-gray-500 mb-4">ou</p>
                    <button type="button" id="select-file-btn" class="btn btn-primary">
                      Selecionar Arquivo
                    </button>
                    <p class="text-xs text-gray-500 mt-4">Tamanho máximo: 10MB</p>
                  </div>
                  
                  <div id="file-info" class="mt-4 hidden">
                    <div class="flex items-center justify-center">
                      <i class="icon icon-file-pdf text-2xl mr-2 text-error"></i>
                      <span id="file-name" class="font-medium"></span>
                      <button type="button" id="remove-file-btn" class="btn btn-ghost btn-xs ml-2">
                        <i class="icon icon-x"></i>
                      </button>
                    </div>
                    <div class="text-sm text-gray-500 mt-1" id="file-size"></div>
                  </div>
                </div>
              </div>
              
              <div class="form-control">
                <label class="flex items-center gap-2 cursor-pointer">
                  <input type="checkbox" name="isPublic" class="checkbox checkbox-primary" />
                  <span class="label-text">Tornar este documento público</span>
                </label>
              </div>
              
              <div class="flex justify-end space-x-4 mt-8">
                <DaisyButton 
                  href="/admin/documents" 
                  variant="ghost" 
                  text="Cancelar"
                />
                
                <button type="submit" class="btn btn-primary">
                  <i class="icon icon-upload mr-2"></i>
                  Fazer Upload
                </button>
              </div>
            </form>
          </div>
        </DaisyCard>
      </Section>
    </PageTransition>
  </Container>
</BaseLayout>

<script>
  // Script para manipulação do upload de arquivos
  document.addEventListener('DOMContentLoaded', () => {
    const dropArea = document.getElementById('drop-area');
    const fileInput = document.getElementById('pdf-file');
    const selectFileBtn = document.getElementById('select-file-btn');
    const removeFileBtn = document.getElementById('remove-file-btn');
    const fileInfo = document.getElementById('file-info');
    const fileName = document.getElementById('file-name');
    const fileSize = document.getElementById('file-size');
    
    // Função para formatar o tamanho do arquivo
    function formatFileSize(bytes) {
      if (bytes === 0) return '0 Bytes';
      const k = 1024;
      const sizes = ['Bytes', 'KB', 'MB', 'GB'];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
    
    // Função para exibir informações do arquivo
    function showFileInfo(file) {
      if (file) {
        fileName.textContent = file.name;
        fileSize.textContent = formatFileSize(file.size);
        fileInfo.classList.remove('hidden');
        
        // Verificar tamanho do arquivo (máximo 10MB)
        if (file.size > 10 * 1024 * 1024) {
          alert('O arquivo excede o tamanho máximo de 10MB.');
          clearFileInput();
          return;
        }
        
        // Verificar tipo de arquivo
        if (file.type !== 'application/pdf') {
          alert('Por favor, selecione um arquivo PDF válido.');
          clearFileInput();
          return;
        }
      }
    }
    
    // Função para limpar o input de arquivo
    function clearFileInput() {
      fileInput.value = '';
      fileInfo.classList.add('hidden');
    }
    
    // Evento de clique no botão de selecionar arquivo
    selectFileBtn.addEventListener('click', () => {
      fileInput.click();
    });
    
    // Evento de mudança no input de arquivo
    fileInput.addEventListener('change', (e) => {
      const file = e.target.files[0];
      showFileInfo(file);
    });
    
    // Evento de clique no botão de remover arquivo
    removeFileBtn.addEventListener('click', clearFileInput);
    
    // Eventos de drag and drop
    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
      dropArea.addEventListener(eventName, (e) => {
        e.preventDefault();
        e.stopPropagation();
      }, false);
    });
    
    ['dragenter', 'dragover'].forEach(eventName => {
      dropArea.addEventListener(eventName, () => {
        dropArea.classList.add('border-primary');
      }, false);
    });
    
    ['dragleave', 'drop'].forEach(eventName => {
      dropArea.addEventListener(eventName, () => {
        dropArea.classList.remove('border-primary');
      }, false);
    });
    
    dropArea.addEventListener('drop', (e) => {
      const file = e.dataTransfer.files[0];
      if (file) {
        fileInput.files = e.dataTransfer.files;
        showFileInfo(file);
      }
    }, false);
  });
</script>
