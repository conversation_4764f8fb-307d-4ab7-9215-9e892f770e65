/**
 * API para autenticação de usuários
 */

import { AuditEventType, AuditSeverity, auditService } from '@services/auditService';
import { authJwtService } from '@services/authJwtService';
import { authService } from '@services/authService';
import { logger } from '@utils/logger';
import type { APIRoute } from 'astro';

export const POST: APIRoute = async ({ request, cookies }) => {
  try {
    // Obter dados da requisição
    const body = await request.json();
    const { email, password } = body;

    // Obter informações da requisição para auditoria
    const ipAddress =
      request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown';

    const userAgent = request.headers.get('user-agent') || 'unknown';

    // Validar dados
    if (!email || !password) {
      // Registrar tentativa de login com dados incompletos
      await auditService.logEvent({
        eventType: AuditEventType.LOGIN_FAILURE,
        ipAddress,
        userAgent,
        resource: 'auth',
        action: 'login',
        result: 'failure',
        severity: AuditSeverity.WARNING,
        metadata: {
          reason: 'Dados incompletos',
          email: email || 'não fornecido',
        },
      });

      return new Response(
        JSON.stringify({
          success: false,
          error: 'Email e senha são obrigatórios',
        }),
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    // Autenticar usuário
    const authResult = await authService.authenticate(email, password);

    if (!authResult.success) {
      // Registrar falha de login
      await auditService.logEvent({
        eventType: AuditEventType.LOGIN_FAILURE,
        ipAddress,
        userAgent,
        resource: 'auth',
        action: 'login',
        result: 'failure',
        severity: AuditSeverity.WARNING,
        metadata: {
          reason: authResult.error,
          email,
        },
      });

      return new Response(
        JSON.stringify({
          success: false,
          error: authResult.error,
        }),
        {
          status: 401,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    // Gerar token JWT
    const user = authResult.user;
    const token = await authJwtService.generateToken(user);

    // Definir cookie de autenticação
    cookies.set('access_token', token, {
      path: '/',
      httpOnly: true,
      secure: import.meta.env.PROD,
      sameSite: 'strict',
      maxAge: 60 * 60 * 24, // 24 horas
    });

    // Registrar login bem-sucedido
    await auditService.logEvent({
      eventType: AuditEventType.LOGIN_SUCCESS,
      userId: user.ulid_user,
      userName: user.name,
      ipAddress,
      userAgent,
      resource: 'auth',
      action: 'login',
      result: 'success',
      severity: AuditSeverity.INFO,
      metadata: {
        email,
      },
    });

    // Remover senha do objeto de usuário
    user.password = undefined;

    return new Response(
      JSON.stringify({
        success: true,
        message: 'Autenticação bem-sucedida',
        user,
        token,
      }),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  } catch (error) {
    logger.error('Erro na autenticação:', error);

    return new Response(
      JSON.stringify({
        success: false,
        error: 'Erro interno do servidor',
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  }
};
