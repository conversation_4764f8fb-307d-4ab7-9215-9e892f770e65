// src/repositories/userRepository.ts
import { queryHelper } from '@db/queryHelper';

export interface User {
  id: number;
  name: string;
  email: string;
  password_hash: string;
  created_at: Date;
  updated_at: Date;
}

/**
 * Repositório para operações com usuários
 */
export const userRepository = {
  /**
   * Busca um usuário pelo ID
   * @param id - ID do usuário
   * @returns Usuário encontrado ou null
   */
  async findById(id: number): Promise<User | null> {
    return await queryHelper.queryOne<User>('SELECT * FROM users WHERE id = $1', [id]);
  },

  /**
   * Busca um usuário pelo email
   * @param email - Email do usuário
   * @returns Usuário encontrado ou null
   */
  async findByEmail(email: string): Promise<User | null> {
    return await queryHelper.queryOne<User>('SELECT * FROM users WHERE email = $1', [email]);
  },

  /**
   * Lista todos os usuários
   * @returns Array de usuários
   */
  async findAll(): Promise<User[]> {
    return await queryHelper.queryAll<User>('SELECT * FROM users ORDER BY name');
  },

  /**
   * Cria um novo usuário
   * @param user - Dados do usuário
   * @returns ID do usuário criado
   */
  async create(user: Omit<User, 'id' | 'created_at' | 'updated_at'>): Promise<number | null> {
    return await queryHelper.insert<User>(
      `INSERT INTO users (name, email, password_hash, created_at, updated_at)
       VALUES ($1, $2, $3, NOW(), NOW())
       RETURNING id`,
      [user.name, user.email, user.password_hash]
    );
  },

  /**
   * Atualiza um usuário existente
   * @param id - ID do usuário
   * @param user - Dados atualizados do usuário
   * @returns Verdadeiro se a atualização foi bem-sucedida
   */
  async update(
    id: number,
    user: Partial<Omit<User, 'id' | 'created_at' | 'updated_at'>>
  ): Promise<boolean> {
    const fields: string[] = [];
    const values: any[] = [];
    let paramIndex = 1;

    // Construir dinamicamente a query de atualização
    if (user.name) {
      fields.push(`name = $${paramIndex++}`);
      values.push(user.name);
    }

    if (user.email) {
      fields.push(`email = $${paramIndex++}`);
      values.push(user.email);
    }

    if (user.password_hash) {
      fields.push(`password_hash = $${paramIndex++}`);
      values.push(user.password_hash);
    }

    // Adicionar updated_at
    fields.push('updated_at = NOW()');

    // Adicionar ID como último parâmetro
    values.push(id);

    if (fields.length === 0) {
      return false;
    }

    const result = await queryHelper.query(
      `UPDATE users SET ${fields.join(', ')} WHERE id = $${paramIndex}`,
      values
    );

    return result.rowCount > 0;
  },

  /**
   * Remove um usuário
   * @param id - ID do usuário
   * @returns Verdadeiro se a remoção foi bem-sucedida
   */
  async delete(id: number): Promise<boolean> {
    const result = await queryHelper.query('DELETE FROM users WHERE id = $1', [id]);

    return result.rowCount > 0;
  },
};
