/**
 * Send Notification Use Case
 *
 * Caso de uso para enviar uma notificação.
 * Parte da implementação da tarefa 8.5.1 - Notificações internas
 */

import { Notification, NotificationAction, NotificationType } from '../../entities/Notification';
import { NotificationChannel } from '../../entities/NotificationPreference';
import { NotificationPreferenceRepository } from '../../repositories/NotificationPreferenceRepository';
import { NotificationRepository } from '../../repositories/NotificationRepository';

export interface SendNotificationRequest {
  userId: string;
  title: string;
  content: string;
  type: NotificationType;
  actions?: NotificationAction[];
  metadata?: Record<string, any>;
  expiresAt?: Date;
  channels?: NotificationChannel[];
}

export interface SendNotificationResponse {
  success: boolean;
  notification?: Notification;
  sentChannels?: NotificationChannel[];
  error?: string;
}

export class SendNotificationUseCase {
  constructor(
    private notificationRepository: NotificationRepository,
    private preferenceRepository: NotificationPreferenceRepository,
    private notificationServices: {
      sendEmail?: (
        userId: string,
        title: string,
        content: string,
        metadata?: any
      ) => Promise<boolean>;
      sendPush?: (
        userId: string,
        title: string,
        content: string,
        metadata?: any
      ) => Promise<boolean>;
      sendSms?: (
        userId: string,
        title: string,
        content: string,
        metadata?: any
      ) => Promise<boolean>;
    } = {}
  ) {}

  async execute(request: SendNotificationRequest): Promise<SendNotificationResponse> {
    try {
      // Validar os dados de entrada
      if (!this.validateRequest(request)) {
        return {
          success: false,
          error: 'Dados inválidos para envio de notificação.',
        };
      }

      // Criar a notificação
      const notification = new Notification({
        id: crypto.randomUUID(),
        userId: request.userId,
        title: request.title,
        content: request.content,
        type: request.type,
        actions: request.actions,
        metadata: request.metadata,
        expiresAt: request.expiresAt,
      });

      // Salvar a notificação (sempre salva no banco, independente das preferências)
      const savedNotification = await this.notificationRepository.create(notification);

      // Determinar os canais para envio com base nas preferências do usuário
      const channelsToSend: NotificationChannel[] = [];
      const requestedChannels = request.channels || ['in_app', 'email', 'push', 'sms'];

      // Verificar cada canal solicitado
      for (const channel of requestedChannels) {
        const shouldSend = await this.preferenceRepository.shouldReceiveNotification(
          request.userId,
          request.type,
          channel
        );

        if (shouldSend) {
          channelsToSend.push(channel);
        }
      }

      // Enviar para os canais apropriados
      const sentChannels: NotificationChannel[] = ['in_app']; // In-app sempre é enviado (já foi salvo no banco)

      // Enviar por e-mail, se necessário
      if (channelsToSend.includes('email') && this.notificationServices.sendEmail) {
        const emailSent = await this.notificationServices.sendEmail(
          request.userId,
          request.title,
          request.content,
          request.metadata
        );

        if (emailSent) {
          sentChannels.push('email');
        }
      }

      // Enviar por push, se necessário
      if (channelsToSend.includes('push') && this.notificationServices.sendPush) {
        const pushSent = await this.notificationServices.sendPush(
          request.userId,
          request.title,
          request.content,
          request.metadata
        );

        if (pushSent) {
          sentChannels.push('push');
        }
      }

      // Enviar por SMS, se necessário
      if (channelsToSend.includes('sms') && this.notificationServices.sendSms) {
        const smsSent = await this.notificationServices.sendSms(
          request.userId,
          request.title,
          request.content,
          request.metadata
        );

        if (smsSent) {
          sentChannels.push('sms');
        }
      }

      return {
        success: true,
        notification: savedNotification,
        sentChannels,
      };
    } catch (error) {
      console.error('Erro ao enviar notificação:', error);
      return {
        success: false,
        error: 'Ocorreu um erro ao enviar a notificação.',
      };
    }
  }

  private validateRequest(request: SendNotificationRequest): boolean {
    // Validar usuário
    if (!request.userId) {
      return false;
    }

    // Validar título e conteúdo
    if (!request.title || !request.content) {
      return false;
    }

    // Validar tipo
    if (!this.isValidNotificationType(request.type)) {
      return false;
    }

    // Validar ações, se fornecidas
    if (request.actions) {
      for (const action of request.actions) {
        if (!action.label || !action.url) {
          return false;
        }
      }
    }

    // Validar data de expiração
    if (request.expiresAt && request.expiresAt < new Date()) {
      return false;
    }

    return true;
  }

  private isValidNotificationType(type: string): boolean {
    const validTypes: NotificationType[] = [
      'info',
      'success',
      'warning',
      'error',
      'system',
      'payment',
      'order',
      'product',
      'content',
      'message',
    ];

    return validTypes.includes(type as NotificationType);
  }
}
