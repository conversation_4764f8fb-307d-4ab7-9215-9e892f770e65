/**
 * Script para gerar configuração de segurança do Valkey
 *
 * Este script gera um arquivo de configuração para o Valkey com
 * configurações de segurança, incluindo autenticação, TLS/SSL e políticas de acesso.
 */

import * as fs from 'node:fs';
import * as path from 'node:path';
import { generateACLFile } from '@config/cache/access-policy.config';
import { generateClusterConfig } from '@config/cache/cluster.config';
import { generateValkeyConfig, getMemoryLimits } from '@config/cache/memory-limits.config';
import { generatePersistenceConfig } from '@config/cache/persistence.config';
import { generateSecurityConfig, saveSecurityConfig } from '@config/cache/security.config';
import {
  certificatesExist,
  generateSelfSignedCertificates,
  generateTLSConfig,
  getTLSConfig,
} from '@config/cache/tls-config';
import { logger } from '@utils/logger';

/**
 * Gera a configuração completa do Valkey
 * @param outputPath Caminho para o arquivo de saída
 */
async function generateValkeyConfiguration(outputPath: string): Promise<void> {
  try {
    logger.info('Gerando configuração do Valkey...');

    // Criar diretório de saída se não existir
    const outputDir = path.dirname(outputPath);
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }

    // Gerar configurações
    const securityConfig = generateSecurityConfig();
    const tlsConfig = generateTLSConfig();
    const memoryConfig = generateValkeyConfig();
    const clusterConfig = generateClusterConfig();
    const persistenceConfig = generatePersistenceConfig();

    // Combinar configurações
    const config = [
      '# Configuração do Valkey',
      `# Gerado automaticamente em ${new Date().toISOString()}`,
      '',
      '# Configurações de segurança',
      ...securityConfig,
      '',
      '# Configurações de TLS/SSL',
      ...tlsConfig,
      '',
      '# Configurações de memória',
      ...memoryConfig,
      '',
      '# Configurações de cluster',
      ...clusterConfig,
      '',
      '# Configurações de persistência',
      ...persistenceConfig,
      '',
      '# Configurações adicionais',
      'daemonize yes',
      'supervised auto',
      'loglevel notice',
      'logfile /var/log/valkey/valkey-server.log',
      'databases 16',
      'always-show-logo no',
      '',
      '# Configurações de rede',
      'bind 127.0.0.1',
      'protected-mode yes',
      'port 6379',
      'tcp-backlog 511',
      'timeout 0',
      'tcp-keepalive 300',
    ];

    // Salvar configuração
    fs.writeFileSync(outputPath, config.join('\n'), 'utf8');
    logger.info(`Configuração do Valkey salva em ${outputPath}`);

    // Gerar arquivo ACL
    const aclPath = path.join(outputDir, 'users.acl');
    const aclConfig = generateACLFile();
    fs.writeFileSync(aclPath, aclConfig, 'utf8');
    logger.info(`Arquivo ACL salvo em ${aclPath}`);

    // Verificar certificados TLS
    const tlsSettings = getTLSConfig();
    if (tlsSettings.enabled) {
      if (!certificatesExist()) {
        logger.warn('Certificados TLS não encontrados. Gerando certificados auto-assinados...');

        // Gerar certificados auto-assinados para desenvolvimento
        if (process.env.NODE_ENV !== 'production') {
          await generateSelfSignedCertificates(tlsSettings.certPath, tlsSettings.keyPath);
        } else {
          logger.error(
            'Certificados TLS não encontrados em ambiente de produção. Por favor, configure certificados válidos.'
          );
        }
      } else {
        logger.info('Certificados TLS encontrados e configurados.');
      }
    }

    logger.info('Configuração do Valkey gerada com sucesso!');
  } catch (error) {
    logger.error('Erro ao gerar configuração do Valkey:', error);
    throw error;
  }
}

/**
 * Função principal
 */
async function main(): Promise<void> {
  try {
    // Obter caminho de saída
    const outputPath = process.argv[2] || path.join(process.cwd(), 'valkey.conf');

    // Gerar configuração
    await generateValkeyConfiguration(outputPath);

    process.exit(0);
  } catch (error) {
    logger.error('Erro ao executar script:', error);
    process.exit(1);
  }
}

// Executar script
main();
