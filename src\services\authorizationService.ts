/**
 * Serviço de autorização baseado em RBAC
 *
 * Este serviço é uma fachada (facade) que coordena os serviços de permissão e papel
 * para fornecer funcionalidades de autorização.
 *
 * @deprecated Este serviço está sendo substituído por serviços mais específicos.
 * Use permissionService, roleService e permissionCacheService diretamente.
 */

import type {
  FullPermissionData,
  FullRoleData,
  PermissionCheckData,
  PermissionCheckResult,
} from '@repository/interfaces/rbacInterfaces';
import { permissionCacheService } from './permissionCacheService';
import { permissionService } from './permissionService';
import { roleService } from './roleService';

/**
 * Serviço de autorização
 */
export const authorizationService = {
  /**
   * Verifica se um usuário tem permissão para uma ação específica
   * @param userId - ID do usuário
   * @param resource - Nome do recurso
   * @param action - Ação a ser verificada
   * @returns Resultado da verificação
   */
  async checkPermission(
    userId: string,
    resource: string,
    action: string
  ): Promise<PermissionCheckResult> {
    return permissionService.checkPermission(userId, resource, action);
  },

  /**
   * Verifica múltiplas permissões de uma vez
   * @param userId - ID do usuário
   * @param permissions - Lista de permissões a verificar
   * @returns Mapa de resultados por permissão
   */
  async checkMultiplePermissions(
    userId: string,
    permissions: PermissionCheckData[]
  ): Promise<Record<string, PermissionCheckResult>> {
    const results: Record<string, PermissionCheckResult> = {};

    for (const perm of permissions) {
      const key = `${perm.resource}:${perm.action}`;
      results[key] = await this.checkPermission(userId, perm.resource, perm.action);
    }

    return results;
  },

  /**
   * Obtém todas as permissões de um usuário
   * @param userId - ID do usuário
   * @returns Lista de permissões completas
   */
  async getUserPermissions(userId: string): Promise<FullPermissionData[]> {
    return permissionService.getUserPermissions(userId);
  },

  /**
   * Obtém todos os papéis de um usuário
   * @param userId - ID do usuário
   * @returns Lista de papéis
   */
  async getUserRoles(userId: string): Promise<unknown[]> {
    return roleService.getUserRoles(userId);
  },

  /**
   * Atribui um papel a um usuário
   * @param userId - ID do usuário
   * @param roleId - ID do papel
   * @returns Verdadeiro se a operação foi bem-sucedida
   */
  async assignRoleToUser(userId: string, roleId: string): Promise<boolean> {
    return roleService.assignRoleToUser(userId, roleId);
  },

  /**
   * Remove um papel de um usuário
   * @param userId - ID do usuário
   * @param roleId - ID do papel
   * @returns Verdadeiro se a operação foi bem-sucedida
   */
  async removeRoleFromUser(userId: string, roleId: string): Promise<boolean> {
    return roleService.removeRoleFromUser(userId, roleId);
  },

  /**
   * Obtém detalhes completos de um papel, incluindo permissões
   * @param roleId - ID do papel
   * @returns Dados completos do papel
   */
  async getRoleDetails(roleId: string): Promise<FullRoleData | null> {
    return roleService.getRoleDetails(roleId);
  },

  /**
   * Associa uma permissão de recurso a um papel
   * @param roleId - ID do papel
   * @param resourceId - ID do recurso
   * @param permissionId - ID da permissão
   * @returns Verdadeiro se a operação foi bem-sucedida
   */
  async assignPermissionToRole(
    roleId: string,
    resourceId: string,
    permissionId: string
  ): Promise<boolean> {
    return roleService.assignPermissionToRole(roleId, resourceId, permissionId);
  },

  /**
   * Remove uma permissão de recurso de um papel
   * @param roleId - ID do papel
   * @param resourcePermissionId - ID da associação recurso-permissão
   * @returns Verdadeiro se a operação foi bem-sucedida
   */
  async removePermissionFromRole(roleId: string, resourcePermissionId: string): Promise<boolean> {
    return roleService.removePermissionFromRole(roleId, resourcePermissionId);
  },

  /**
   * Invalida o cache de permissões de um usuário
   * @param userId - ID do usuário
   */
  async invalidateUserPermissionsCache(userId: string): Promise<void> {
    return permissionCacheService.invalidateUserPermissionsCache(userId);
  },

  /**
   * Invalida o cache de permissões para todos os usuários com um papel específico
   * @param roleId - ID do papel
   */
  async invalidateRolePermissionsCache(roleId: string): Promise<void> {
    return permissionCacheService.invalidateRolePermissionsCache(roleId);
  },

  /**
   * Invalida o cache de permissões para todos os usuários com papéis filhos
   * @param parentRoleId - ID do papel pai
   */
  async invalidateChildRolesPermissionsCache(parentRoleId: string): Promise<void> {
    return permissionCacheService.invalidateChildRolesPermissionsCache(parentRoleId);
  },

  /**
   * Adiciona uma relação de hierarquia entre papéis
   * @param parentRoleId - ID do papel pai
   * @param childRoleId - ID do papel filho
   * @returns Verdadeiro se a operação foi bem-sucedida
   */
  async addRoleHierarchy(parentRoleId: string, childRoleId: string): Promise<boolean> {
    return roleService.addRoleHierarchy(parentRoleId, childRoleId);
  },

  /**
   * Remove uma relação de hierarquia entre papéis
   * @param parentRoleId - ID do papel pai
   * @param childRoleId - ID do papel filho
   * @returns Verdadeiro se a operação foi bem-sucedida
   */
  async removeRoleHierarchy(parentRoleId: string, childRoleId: string): Promise<boolean> {
    return roleService.removeRoleHierarchy(parentRoleId, childRoleId);
  },

  /**
   * Verifica se adicionar uma relação de hierarquia criaria um ciclo
   * @param parentRoleId - ID do papel pai
   * @param childRoleId - ID do papel filho
   * @returns Verdadeiro se criaria um ciclo
   */
  async wouldCreateHierarchyCycle(parentRoleId: string, childRoleId: string): Promise<boolean> {
    return roleService.wouldCreateHierarchyCycle(parentRoleId, childRoleId);
  },

  /**
   * Verifica se um papel é ancestral de outro na hierarquia
   * @param ancestorRoleId - ID do possível papel ancestral
   * @param descendantRoleId - ID do papel descendente
   * @returns Verdadeiro se o papel é ancestral
   */
  async isRoleAncestor(ancestorRoleId: string, descendantRoleId: string): Promise<boolean> {
    return roleService.isRoleAncestor(ancestorRoleId, descendantRoleId);
  },

  /**
   * Obtém todas as permissões de um papel, incluindo as herdadas
   * @param roleId - ID do papel
   * @returns Lista de permissões com informações de origem
   */
  async getRolePermissionsWithInheritance(roleId: string): Promise<unknown[]> {
    return roleService.getRolePermissionsWithInheritance(roleId);
  },
};
