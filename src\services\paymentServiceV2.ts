/**
 * Serviço de pagamento (V2)
 * 
 * Este serviço é responsável por processar pagamentos, consultar status e processar reembolsos.
 * Ele utiliza o processador de pagamento configurado através da fábrica.
 * 
 * Esta versão implementa o Liskov Substitution Principle, permitindo que diferentes
 * processadores de pagamento sejam usados de forma intercambiável.
 */

import { v4 as uuidv4 } from 'uuid';
import { queryHelper } from '../db/queryHelper';
import { 
  PaymentData, 
  PaymentResult, 
  PaymentStatus, 
  PaymentStatusQuery, 
  RefundRequest, 
  RefundResult 
} from '../domain/interfaces/PaymentProcessor';
import { PaymentProcessorFactory, SupportedPaymentProcessor } from '../domain/payment/PaymentProcessorFactory';
import { logger } from '../utils/logger';
import { alertService } from './alertService';
import { eventProducerService } from './eventProducerService';

/**
 * Serviço de pagamento
 */
export const paymentServiceV2 = {
  /**
   * Processa um pagamento
   * @param data - Dados do pagamento
   * @param processorType - Tipo de processador (opcional)
   * @returns Resultado do pagamento
   */
  async processPayment(
    data: Omit<PaymentData, 'id'>,
    processorType?: SupportedPaymentProcessor
  ): Promise<PaymentResult> {
    try {
      // Gerar ID para o pagamento
      const paymentId = uuidv4();
      
      // Obter processador de pagamento
      const processor = PaymentProcessorFactory.getProcessor(processorType);
      
      logger.info(`Iniciando processamento de pagamento via ${processor.name}`, {
        orderId: data.orderId,
        amount: data.amount,
        currency: data.currency
      });
      
      // Processar pagamento
      const result = await processor.processPayment({
        ...data,
        id: paymentId
      });
      
      // Salvar resultado no banco de dados
      await this.savePaymentResult(result);
      
      // Publicar evento de pagamento
      await this.publishPaymentEvent(result);
      
      // Verificar e disparar alertas
      await this.checkAndTriggerAlerts(result);
      
      return result;
    } catch (error) {
      logger.error('Erro ao processar pagamento', {
        orderId: data.orderId,
        error: error instanceof Error ? error.message : String(error)
      });
      
      throw error;
    }
  },
  
  /**
   * Consulta o status de um pagamento
   * @param query - Dados para consulta
   * @param processorType - Tipo de processador (opcional)
   * @returns Resultado do pagamento atualizado
   */
  async checkPaymentStatus(
    query: PaymentStatusQuery,
    processorType?: SupportedPaymentProcessor
  ): Promise<PaymentResult> {
    try {
      // Obter processador de pagamento
      const processor = PaymentProcessorFactory.getProcessor(processorType);
      
      logger.info(`Consultando status de pagamento via ${processor.name}`, {
        paymentId: query.id,
        externalId: query.externalId
      });
      
      // Consultar status
      const result = await processor.checkPaymentStatus(query);
      
      // Verificar se o status mudou
      const currentStatus = await this.getPaymentStatus(query.id);
      
      if (currentStatus !== result.status) {
        // Atualizar status no banco de dados
        await this.updatePaymentStatus(result);
        
        // Publicar evento de atualização de status
        await this.publishPaymentEvent(result);
        
        // Verificar e disparar alertas
        await this.checkAndTriggerAlerts(result);
      }
      
      return result;
    } catch (error) {
      logger.error('Erro ao consultar status de pagamento', {
        paymentId: query.id,
        externalId: query.externalId,
        error: error instanceof Error ? error.message : String(error)
      });
      
      throw error;
    }
  },
  
  /**
   * Processa um reembolso
   * @param request - Dados do reembolso
   * @param processorType - Tipo de processador (opcional)
   * @returns Resultado do reembolso
   */
  async processRefund(
    request: RefundRequest,
    processorType?: SupportedPaymentProcessor
  ): Promise<RefundResult> {
    try {
      // Obter processador de pagamento
      const processor = PaymentProcessorFactory.getProcessor(processorType);
      
      logger.info(`Processando reembolso via ${processor.name}`, {
        paymentId: request.paymentId,
        externalId: request.externalId,
        amount: request.amount
      });
      
      // Processar reembolso
      const result = await processor.processRefund(request);
      
      // Salvar resultado no banco de dados
      await this.saveRefundResult(result);
      
      // Atualizar status do pagamento
      await this.updatePaymentStatus({
        id: request.paymentId,
        externalId: request.externalId,
        status: PaymentStatus.REFUNDED,
        amount: result.amount,
        currency: result.currency,
        createdAt: new Date(),
        updatedAt: new Date()
      });
      
      // Publicar evento de reembolso
      await eventProducerService.publishEvent('payment.refund.processed', {
        paymentId: request.paymentId,
        refundId: result.id,
        externalId: result.externalId,
        amount: result.amount,
        status: result.status
      });
      
      return result;
    } catch (error) {
      logger.error('Erro ao processar reembolso', {
        paymentId: request.paymentId,
        externalId: request.externalId,
        error: error instanceof Error ? error.message : String(error)
      });
      
      throw error;
    }
  },
  
  /**
   * Salva o resultado do pagamento no banco de dados
   * @param result - Resultado do pagamento
   */
  private async savePaymentResult(result: PaymentResult): Promise<void> {
    try {
      await queryHelper.query(
        `INSERT INTO tab_payment (
          ulid_payment, external_id, ulid_order, status, value, currency,
          redirect_url, error_message, error_code, raw_data, created_at, updated_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)`,
        [
          result.id,
          result.externalId,
          result.rawData?.orderId || '',
          result.status,
          result.amount,
          result.currency,
          result.redirectUrl || null,
          result.errorMessage || null,
          result.errorCode || null,
          JSON.stringify(result.rawData || {}),
          result.createdAt,
          result.updatedAt
        ]
      );
    } catch (error) {
      logger.error('Erro ao salvar resultado do pagamento', {
        paymentId: result.id,
        error: error instanceof Error ? error.message : String(error)
      });
      
      throw error;
    }
  },
  
  /**
   * Atualiza o status do pagamento no banco de dados
   * @param result - Resultado do pagamento
   */
  private async updatePaymentStatus(result: PaymentResult): Promise<void> {
    try {
      await queryHelper.query(
        `UPDATE tab_payment SET
          status = $1,
          error_message = $2,
          error_code = $3,
          updated_at = $4
        WHERE ulid_payment = $5`,
        [
          result.status,
          result.errorMessage || null,
          result.errorCode || null,
          result.updatedAt,
          result.id
        ]
      );
    } catch (error) {
      logger.error('Erro ao atualizar status do pagamento', {
        paymentId: result.id,
        error: error instanceof Error ? error.message : String(error)
      });
      
      throw error;
    }
  },
  
  /**
   * Obtém o status atual do pagamento no banco de dados
   * @param paymentId - ID do pagamento
   * @returns Status do pagamento ou null se não encontrado
   */
  private async getPaymentStatus(paymentId: string): Promise<PaymentStatus | null> {
    try {
      const result = await queryHelper.queryOne(
        'SELECT status FROM tab_payment WHERE ulid_payment = $1',
        [paymentId]
      );
      
      return result ? (result.status as PaymentStatus) : null;
    } catch (error) {
      logger.error('Erro ao obter status do pagamento', {
        paymentId,
        error: error instanceof Error ? error.message : String(error)
      });
      
      return null;
    }
  },
  
  /**
   * Salva o resultado do reembolso no banco de dados
   * @param result - Resultado do reembolso
   */
  private async saveRefundResult(result: RefundResult): Promise<void> {
    try {
      await queryHelper.query(
        `INSERT INTO tab_refund (
          ulid_refund, external_id, ulid_payment, status, value, currency,
          error_message, raw_data, created_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)`,
        [
          result.id,
          result.externalId,
          result.paymentId,
          result.status,
          result.amount,
          result.currency,
          result.errorMessage || null,
          JSON.stringify(result.rawData || {}),
          result.createdAt
        ]
      );
    } catch (error) {
      logger.error('Erro ao salvar resultado do reembolso', {
        refundId: result.id,
        paymentId: result.paymentId,
        error: error instanceof Error ? error.message : String(error)
      });
      
      throw error;
    }
  },
  
  /**
   * Publica evento de pagamento
   * @param result - Resultado do pagamento
   */
  private async publishPaymentEvent(result: PaymentResult): Promise<void> {
    try {
      const eventType = this.getEventTypeForStatus(result.status);
      
      await eventProducerService.publishEvent(eventType, {
        paymentId: result.id,
        externalId: result.externalId,
        orderId: result.rawData?.orderId,
        status: result.status,
        amount: result.amount,
        currency: result.currency,
        errorMessage: result.errorMessage,
        errorCode: result.errorCode
      });
    } catch (error) {
      logger.error('Erro ao publicar evento de pagamento', {
        paymentId: result.id,
        error: error instanceof Error ? error.message : String(error)
      });
    }
  },
  
  /**
   * Verifica e dispara alertas relacionados a pagamentos
   * @param result - Resultado do pagamento
   */
  private async checkAndTriggerAlerts(result: PaymentResult): Promise<void> {
    try {
      // Obter dados do pagamento e do pedido
      const paymentData = await queryHelper.queryOne(
        `
        SELECT
          p.ulid_payment,
          p.ulid_order,
          p.value,
          o.ulid_user,
          u.name as user_name,
          u.email as user_email,
          o.total as order_total
        FROM tab_payment p
        JOIN tab_order o ON p.ulid_order = o.ulid_order
        JOIN tab_user u ON o.ulid_user = u.ulid_user
        WHERE p.ulid_payment = $1
        `,
        [result.id]
      );
      
      if (!paymentData) {
        logger.warn(`Dados do pagamento não encontrados para alertas: ${result.id}`);
        return;
      }
      
      // Preparar dados para o alerta
      const alertData = {
        orderId: paymentData.ulid_order,
        customerId: paymentData.ulid_user,
        customerName: paymentData.user_name,
        customerEmail: paymentData.user_email,
        orderTotal: Number.parseFloat(paymentData.order_total),
        paymentMethod: result.rawData?.paymentMethod || 'unknown',
        paymentStatus: result.status,
        transactionDate: result.createdAt,
        additionalInfo: {
          paymentId: result.id,
          externalId: result.externalId,
          paymentValue: result.amount,
        },
      };
      
      // Determinar o tipo de alerta com base no status
      switch (result.status) {
        case PaymentStatus.COMPLETED:
          await alertService.triggerAlert('payment_success', alertData);
          
          // Verificar também se é uma transação de alto valor
          if (alertData.orderTotal >= 1000) {
            await alertService.triggerAlert('high_value_transaction', alertData);
          }
          break;
          
        case PaymentStatus.FAILED:
          await alertService.triggerAlert('payment_failure', alertData);
          break;
          
        case PaymentStatus.REFUNDED:
          await alertService.triggerAlert('refund_processed', alertData);
          break;
      }
    } catch (error) {
      logger.error('Erro ao verificar e disparar alertas', {
        paymentId: result.id,
        error: error instanceof Error ? error.message : String(error)
      });
    }
  },
  
  /**
   * Obtém o tipo de evento para o status do pagamento
   * @param status - Status do pagamento
   * @returns Tipo de evento
   */
  private getEventTypeForStatus(status: PaymentStatus): string {
    switch (status) {
      case PaymentStatus.PENDING:
        return 'payment.transaction.created';
      case PaymentStatus.PROCESSING:
        return 'payment.transaction.processing';
      case PaymentStatus.COMPLETED:
        return 'payment.transaction.completed';
      case PaymentStatus.FAILED:
        return 'payment.transaction.failed';
      case PaymentStatus.REFUNDED:
        return 'payment.transaction.refunded';
      case PaymentStatus.CANCELLED:
        return 'payment.transaction.cancelled';
      default:
        return 'payment.transaction.updated';
    }
  }
};

// Re-exportar enums e tipos
export { PaymentStatus } from '../domain/interfaces/PaymentProcessor';
