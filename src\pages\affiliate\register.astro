---
import { PageTransition } from '../../components/transitions';
import DaisyCard from '../../components/ui/DaisyCard.astro';
import Breadcrumbs from '../../components/ui/navigation/Breadcrumbs.astro';
/**
 * Página de Registro de Afiliado
 *
 * Interface para registro no programa de afiliados.
 * Parte da implementação da tarefa 8.4.2 - Compartilhamento
 */
import BaseLayout from '../../layouts/BaseLayout.astro';
import { Container, Section } from '../../layouts/grid';

// Título da página
const title = 'Programa de Afiliados - Registro';

// Breadcrumbs para a página atual
const breadcrumbItems = [
  { href: '/', label: 'Início' },
  { href: '/affiliate', label: 'Programa de Afiliados' },
  { label: 'Registro' },
];

// Processar o formulário de registro
let formSubmitted = false;
let formError = '';
let formSuccess = false;

if (Astro.request.method === 'POST') {
  try {
    // Em um cenário real, aqui seria implementada a lógica para:
    // 1. Receber os dados do formulário
    // 2. Validar os dados
    // 3. Registrar o afiliado
    // 4. Redirecionar ou exibir mensagem de sucesso

    formSubmitted = true;
    formSuccess = true;

    // Por enquanto, apenas simulamos o sucesso
  } catch (error) {
    console.error('Erro ao processar registro de afiliado:', error);
    formSubmitted = true;
    formError = 'Ocorreu um erro ao processar seu registro. Por favor, tente novamente.';
  }
}
---

<BaseLayout title={title} enableViewTransitions={true} transitionType="fade">
  <Container>
    <PageTransition type="fade" duration="0.5s">
      <Section>
        <Breadcrumbs items={breadcrumbItems} class="mb-6" />
        
        <h1 class="text-3xl font-bold mb-6">{title}</h1>
        
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <!-- Coluna de informações -->
          <div class="lg:col-span-1">
            <DaisyCard class="mb-6">
              <div class="p-6">
                <h2 class="text-xl font-bold mb-4">Benefícios do Programa</h2>
                
                <ul class="space-y-4">
                  <li class="flex items-start">
                    <i class="icon icon-check-circle text-success mt-1 mr-2"></i>
                    <div>
                      <h3 class="font-bold">Comissões Atrativas</h3>
                      <p class="text-sm text-gray-600">Ganhe até 20% de comissão em cada venda realizada através do seu link.</p>
                    </div>
                  </li>
                  
                  <li class="flex items-start">
                    <i class="icon icon-check-circle text-success mt-1 mr-2"></i>
                    <div>
                      <h3 class="font-bold">Materiais Exclusivos</h3>
                      <p class="text-sm text-gray-600">Acesso a banners, textos e materiais promocionais para impulsionar suas vendas.</p>
                    </div>
                  </li>
                  
                  <li class="flex items-start">
                    <i class="icon icon-check-circle text-success mt-1 mr-2"></i>
                    <div>
                      <h3 class="font-bold">Painel de Controle</h3>
                      <p class="text-sm text-gray-600">Acompanhe seus cliques, conversões e comissões em tempo real.</p>
                    </div>
                  </li>
                  
                  <li class="flex items-start">
                    <i class="icon icon-check-circle text-success mt-1 mr-2"></i>
                    <div>
                      <h3 class="font-bold">Pagamentos Mensais</h3>
                      <p class="text-sm text-gray-600">Receba suas comissões mensalmente a partir de R$ 100,00 acumulados.</p>
                    </div>
                  </li>
                </ul>
              </div>
            </DaisyCard>
            
            <DaisyCard>
              <div class="p-6">
                <h2 class="text-xl font-bold mb-4">Como Funciona</h2>
                
                <ol class="space-y-4">
                  <li class="flex">
                    <div class="flex-shrink-0 w-8 h-8 flex items-center justify-center rounded-full bg-primary text-white font-bold">
                      1
                    </div>
                    <div class="ml-4">
                      <h3 class="font-bold">Cadastre-se</h3>
                      <p class="text-sm text-gray-600">Preencha o formulário ao lado para se tornar um afiliado.</p>
                    </div>
                  </li>
                  
                  <li class="flex">
                    <div class="flex-shrink-0 w-8 h-8 flex items-center justify-center rounded-full bg-primary text-white font-bold">
                      2
                    </div>
                    <div class="ml-4">
                      <h3 class="font-bold">Receba seu Link</h3>
                      <p class="text-sm text-gray-600">Após aprovação, você receberá seu link de afiliado único.</p>
                    </div>
                  </li>
                  
                  <li class="flex">
                    <div class="flex-shrink-0 w-8 h-8 flex items-center justify-center rounded-full bg-primary text-white font-bold">
                      3
                    </div>
                    <div class="ml-4">
                      <h3 class="font-bold">Compartilhe</h3>
                      <p class="text-sm text-gray-600">Divulgue os produtos em suas redes sociais, blog ou site.</p>
                    </div>
                  </li>
                  
                  <li class="flex">
                    <div class="flex-shrink-0 w-8 h-8 flex items-center justify-center rounded-full bg-primary text-white font-bold">
                      4
                    </div>
                    <div class="ml-4">
                      <h3 class="font-bold">Ganhe Comissões</h3>
                      <p class="text-sm text-gray-600">Receba comissões por cada venda realizada através do seu link.</p>
                    </div>
                  </li>
                </ol>
              </div>
            </DaisyCard>
          </div>
          
          <!-- Coluna do formulário -->
          <div class="lg:col-span-2">
            <DaisyCard>
              <div class="p-6">
                {formSubmitted && formSuccess ? (
                  <div class="text-center py-8">
                    <div class="text-6xl text-success mb-4">
                      <i class="icon icon-check-circle"></i>
                    </div>
                    <h2 class="text-2xl font-bold mb-2">Registro Enviado com Sucesso!</h2>
                    <p class="text-gray-600 mb-6">
                      Obrigado pelo seu interesse em se tornar um afiliado. Sua solicitação foi recebida e está em análise.
                    </p>
                    <p class="text-gray-600 mb-6">
                      Você receberá um e-mail com mais informações em breve.
                    </p>
                    <a href="/affiliate" class="btn btn-primary">
                      Voltar para Programa de Afiliados
                    </a>
                  </div>
                ) : (
                  <>
                    <h2 class="text-xl font-bold mb-6">Formulário de Registro</h2>
                    
                    {formError && (
                      <div class="alert alert-error mb-6">
                        <i class="icon icon-alert-circle"></i>
                        <span>{formError}</span>
                      </div>
                    )}
                    
                    <form action="/affiliate/register" method="POST" class="space-y-6">
                      <!-- Informações Pessoais -->
                      <div>
                        <h3 class="font-bold mb-4">Informações Pessoais</h3>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div class="form-control">
                            <label class="label">
                              <span class="label-text">Nome Completo*</span>
                            </label>
                            <input 
                              type="text" 
                              name="name" 
                              placeholder="Seu nome completo" 
                              class="input input-bordered w-full" 
                              required
                            />
                          </div>
                          
                          <div class="form-control">
                            <label class="label">
                              <span class="label-text">E-mail*</span>
                            </label>
                            <input 
                              type="email" 
                              name="email" 
                              placeholder="<EMAIL>" 
                              class="input input-bordered w-full" 
                              required
                            />
                          </div>
                        </div>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                          <div class="form-control">
                            <label class="label">
                              <span class="label-text">Telefone*</span>
                            </label>
                            <input 
                              type="tel" 
                              name="phone" 
                              placeholder="(00) 00000-0000" 
                              class="input input-bordered w-full" 
                              required
                            />
                          </div>
                          
                          <div class="form-control">
                            <label class="label">
                              <span class="label-text">CPF/CNPJ*</span>
                            </label>
                            <input 
                              type="text" 
                              name="document" 
                              placeholder="000.000.000-00" 
                              class="input input-bordered w-full" 
                              required
                            />
                          </div>
                        </div>
                      </div>
                      
                      <!-- Informações Profissionais -->
                      <div>
                        <h3 class="font-bold mb-4">Informações Profissionais</h3>
                        
                        <div class="form-control">
                          <label class="label">
                            <span class="label-text">Área de Atuação*</span>
                          </label>
                          <select name="profession" class="select select-bordered w-full" required>
                            <option value="" disabled selected>Selecione sua área</option>
                            <option value="professor">Professor</option>
                            <option value="coordenador">Coordenador Pedagógico</option>
                            <option value="diretor">Diretor Escolar</option>
                            <option value="influenciador">Influenciador Digital</option>
                            <option value="outro">Outro</option>
                          </select>
                        </div>
                        
                        <div class="form-control mt-4">
                          <label class="label">
                            <span class="label-text">Onde pretende divulgar nossos produtos?*</span>
                          </label>
                          <div class="grid grid-cols-1 md:grid-cols-2 gap-2">
                            <label class="flex items-center gap-2">
                              <input type="checkbox" name="channels[]" value="instagram" class="checkbox checkbox-sm" />
                              <span>Instagram</span>
                            </label>
                            <label class="flex items-center gap-2">
                              <input type="checkbox" name="channels[]" value="facebook" class="checkbox checkbox-sm" />
                              <span>Facebook</span>
                            </label>
                            <label class="flex items-center gap-2">
                              <input type="checkbox" name="channels[]" value="youtube" class="checkbox checkbox-sm" />
                              <span>YouTube</span>
                            </label>
                            <label class="flex items-center gap-2">
                              <input type="checkbox" name="channels[]" value="blog" class="checkbox checkbox-sm" />
                              <span>Blog/Site</span>
                            </label>
                            <label class="flex items-center gap-2">
                              <input type="checkbox" name="channels[]" value="whatsapp" class="checkbox checkbox-sm" />
                              <span>WhatsApp</span>
                            </label>
                            <label class="flex items-center gap-2">
                              <input type="checkbox" name="channels[]" value="email" class="checkbox checkbox-sm" />
                              <span>E-mail Marketing</span>
                            </label>
                          </div>
                        </div>
                        
                        <div class="form-control mt-4">
                          <label class="label">
                            <span class="label-text">Experiência com Marketing de Afiliados</span>
                          </label>
                          <select name="experience" class="select select-bordered w-full">
                            <option value="" disabled selected>Selecione sua experiência</option>
                            <option value="none">Nenhuma experiência</option>
                            <option value="beginner">Iniciante (menos de 1 ano)</option>
                            <option value="intermediate">Intermediário (1-3 anos)</option>
                            <option value="advanced">Avançado (mais de 3 anos)</option>
                          </select>
                        </div>
                      </div>
                      
                      <!-- Informações de Pagamento -->
                      <div>
                        <h3 class="font-bold mb-4">Informações de Pagamento</h3>
                        
                        <div class="form-control">
                          <label class="label">
                            <span class="label-text">Método de Pagamento Preferido*</span>
                          </label>
                          <select name="paymentMethod" class="select select-bordered w-full" required>
                            <option value="" disabled selected>Selecione o método</option>
                            <option value="pix">PIX</option>
                            <option value="bank">Transferência Bancária</option>
                          </select>
                        </div>
                        
                        <div class="form-control mt-4">
                          <label class="label">
                            <span class="label-text">Chave PIX (se aplicável)</span>
                          </label>
                          <input 
                            type="text" 
                            name="pixKey" 
                            placeholder="CPF, E-mail, Telefone ou Chave Aleatória" 
                            class="input input-bordered w-full"
                          />
                        </div>
                      </div>
                      
                      <!-- Termos e Condições -->
                      <div class="form-control">
                        <label class="flex items-start gap-2 cursor-pointer">
                          <input 
                            type="checkbox" 
                            name="termsAccepted" 
                            class="checkbox checkbox-primary mt-1" 
                            required
                          />
                          <span class="label-text">
                            Li e aceito os <a href="/terms/affiliate" class="link link-primary">Termos e Condições</a> do Programa de Afiliados, incluindo a Política de Comissões e Pagamentos.
                          </span>
                        </label>
                      </div>
                      
                      <!-- Botão de envio -->
                      <div class="pt-4">
                        <button type="submit" class="btn btn-primary w-full">
                          Enviar Solicitação de Registro
                        </button>
                      </div>
                    </form>
                  </>
                )}
              </div>
            </DaisyCard>
          </div>
        </div>
      </Section>
    </PageTransition>
  </Container>
</BaseLayout>

<script>
  // Script para validação do formulário
  document.addEventListener('DOMContentLoaded', () => {
    const form = document.querySelector('form');
    
    if (form) {
      // Validar CPF/CNPJ
      const documentInput = form.querySelector('input[name="document"]');
      if (documentInput) {
        documentInput.addEventListener('blur', () => {
          const value = documentInput.value.replace(/[^\d]/g, '');
          
          // Validação básica de comprimento
          if (value.length !== 11 && value.length !== 14) {
            documentInput.setCustomValidity('CPF deve ter 11 dígitos ou CNPJ deve ter 14 dígitos');
          } else {
            documentInput.setCustomValidity('');
          }
        });
      }
      
      // Validar seleção de pelo menos um canal de divulgação
      form.addEventListener('submit', (e) => {
        const channelCheckboxes = form.querySelectorAll('input[name="channels[]"]:checked');
        
        if (channelCheckboxes.length === 0) {
          e.preventDefault();
          alert('Por favor, selecione pelo menos um canal de divulgação.');
        }
      });
    }
  });
</script>
