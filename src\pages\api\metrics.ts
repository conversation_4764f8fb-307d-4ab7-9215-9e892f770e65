/**
 * API para receber métricas de performance
 *
 * Este endpoint recebe métricas de performance do cliente e as armazena
 * para análise posterior.
 */

import type { APIRoute } from 'astro';
import { type PerformanceMetric, saveMetric } from '../../services/metrics-service';

/**
 * Endpoint POST para receber métricas
 */
export const post: APIRoute = async ({ request }) => {
  try {
    // Verificar método
    if (request.method !== 'POST') {
      return new Response(JSON.stringify({ error: 'Método não permitido' }), {
        status: 405,
        headers: {
          'Content-Type': 'application/json',
        },
      });
    }

    // Obter dados da requisição
    const metric = (await request.json()) as PerformanceMetric;

    // Validar dados recebidos
    if (!metric.name || metric.value === undefined || !metric.id) {
      return new Response(JSON.stringify({ error: 'Dados inválidos' }), {
        status: 400,
        headers: {
          'Content-Type': 'application/json',
        },
      });
    }

    // Salvar métrica
    await saveMetric(metric);

    // Retornar sucesso
    return new Response(JSON.stringify({ success: true }), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  } catch (error) {
    // Logar erro
    console.error('Erro ao processar métrica:', error);

    // Retornar erro
    return new Response(JSON.stringify({ error: 'Erro interno' }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  }
};

/**
 * Endpoint GET para obter métricas (apenas para administradores)
 */
export const get: APIRoute = async ({ request, cookies }) => {
  try {
    // Verificar autenticação (simplificado para exemplo)
    const authToken = cookies.get('auth_token')?.value;

    // Se não houver token ou token for inválido, retornar erro
    if (!authToken || authToken !== process.env.ADMIN_TOKEN) {
      return new Response(JSON.stringify({ error: 'Não autorizado' }), {
        status: 401,
        headers: {
          'Content-Type': 'application/json',
        },
      });
    }

    // Em uma implementação real, aqui recuperaríamos as métricas
    // e retornaríamos para o cliente

    // Retornar mensagem temporária
    return new Response(
      JSON.stringify({
        message: 'Endpoint de consulta de métricas não implementado',
      }),
      {
        status: 501,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  } catch (error) {
    // Logar erro
    console.error('Erro ao obter métricas:', error);

    // Retornar erro
    return new Response(JSON.stringify({ error: 'Erro interno' }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  }
};
