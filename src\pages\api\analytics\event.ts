/**
 * Analytics Event API
 *
 * Endpoint para rastrear eventos de analytics no lado do servidor.
 * Parte da implementação da tarefa 8.9.3 - Analytics
 */

import type { APIRoute } from 'astro';
import { AnalyticsService } from '../../../domain/services/AnalyticsService';
import { GoogleAnalyticsService } from '../../../infrastructure/services/GoogleAnalyticsService';

// Inicializar serviço de analytics
const analyticsService: AnalyticsService = new GoogleAnalyticsService();

// Configurar serviço
await analyticsService.initialize({
  trackingId: process.env.GOOGLE_ANALYTICS_ID || '',
  anonymizeIp: true,
});

export const POST: APIRoute = async ({ request }) => {
  try {
    // Obter dados do corpo da requisição
    const body = await request.json();

    // Validar dados
    if (!body.category || !body.action) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Parâmetros obrigatórios: category, action',
        }),
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    // Rastrear evento
    const success = await analyticsService.trackEvent({
      category: body.category,
      action: body.action,
      label: body.label,
      value: body.value,
      nonInteraction: body.nonInteraction,
      userId: body.userId,
      clientId: body.clientId,
      timestamp: body.timestamp ? new Date(body.timestamp) : new Date(),
      sessionId: body.sessionId,
      customDimensions: body.customDimensions,
    });

    if (!success) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Erro ao rastrear evento',
        }),
        {
          status: 500,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    return new Response(
      JSON.stringify({
        success: true,
        message: 'Evento rastreado com sucesso',
      }),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  } catch (error) {
    console.error('Erro ao processar requisição de evento de analytics:', error);

    return new Response(
      JSON.stringify({
        success: false,
        error: 'Erro ao processar requisição',
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  }
};
