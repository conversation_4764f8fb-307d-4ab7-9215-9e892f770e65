---
import AdminLayout from '@layouts/AdminLayout.astro';
import { kafkaDashboardService } from '@services/kafka-dashboard.service';
import { kafkaAlertService } from '@services/kafka-alerts.service';
import { formatNumber, formatDate } from '@utils/formatters';

// Obter métricas do Kafka
const metrics = await kafkaDashboardService.getMetrics();

// Obter alertas ativos
const activeAlerts = kafkaAlertService.getActiveAlerts();

// Função para obter classe CSS com base na severidade
function getSeverityClass(severity: string): string {
  switch (severity.toLowerCase()) {
    case 'critical':
      return 'bg-red-100 text-red-800';
    case 'error':
      return 'bg-orange-100 text-orange-800';
    case 'warning':
      return 'bg-yellow-100 text-yellow-800';
    case 'info':
      return 'bg-blue-100 text-blue-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
}

// Função para obter ícone com base na severidade
function getSeverityIcon(severity: string): string {
  switch (severity.toLowerCase()) {
    case 'critical':
      return 'exclamation-circle';
    case 'error':
      return 'exclamation-triangle';
    case 'warning':
      return 'exclamation';
    case 'info':
      return 'info-circle';
    default:
      return 'question-circle';
  }
}

// Função para obter classe CSS com base no status
function getStatusClass(status: string): string {
  switch (status.toLowerCase()) {
    case 'online':
      return 'bg-green-100 text-green-800';
    case 'offline':
      return 'bg-red-100 text-red-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
}

// Função para obter classe CSS com base no valor
function getValueClass(value: number, threshold: number, inverse: boolean = false): string {
  if (inverse) {
    return value <= threshold ? 'text-green-600' : 'text-red-600';
  } else {
    return value >= threshold ? 'text-green-600' : 'text-red-600';
  }
}

// Função para obter classe CSS com base no percentual
function getPercentClass(percent: number): string {
  if (percent < 70) {
    return 'bg-green-500';
  } else if (percent < 90) {
    return 'bg-yellow-500';
  } else {
    return 'bg-red-500';
  }
}
---

<AdminLayout title="Dashboard Kafka">
  <div class="container mx-auto px-4 py-8">
    <h1 class="text-3xl font-bold mb-8">Dashboard Kafka</h1>
    
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <!-- Card: Brokers -->
      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center justify-between mb-4">
          <h2 class="text-lg font-semibold">Brokers</h2>
          <span class="text-2xl font-bold">{metrics.brokers.count}</span>
        </div>
        <div class="space-y-2">
          {metrics.brokers.status.map((broker) => (
            <div class="flex items-center justify-between">
              <span>{broker.host}:{broker.port}</span>
              <span class={`px-2 py-1 rounded-full text-xs font-medium ${getStatusClass(broker.status)}`}>
                {broker.status}
              </span>
            </div>
          ))}
        </div>
      </div>
      
      <!-- Card: Tópicos -->
      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center justify-between mb-4">
          <h2 class="text-lg font-semibold">Tópicos</h2>
          <span class="text-2xl font-bold">{metrics.topics.count}</span>
        </div>
        <div class="space-y-2">
          <div class="flex items-center justify-between">
            <span>Partições</span>
            <span class="font-medium">{metrics.topics.partitions}</span>
          </div>
          <div class="flex items-center justify-between">
            <span>Partições sub-replicadas</span>
            <span class={`font-medium ${metrics.topics.underReplicatedPartitions > 0 ? 'text-red-600' : 'text-green-600'}`}>
              {metrics.topics.underReplicatedPartitions}
            </span>
          </div>
          <div class="flex items-center justify-between">
            <span>Partições offline</span>
            <span class={`font-medium ${metrics.topics.offlinePartitions > 0 ? 'text-red-600' : 'text-green-600'}`}>
              {metrics.topics.offlinePartitions}
            </span>
          </div>
        </div>
      </div>
      
      <!-- Card: Consumidores -->
      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center justify-between mb-4">
          <h2 class="text-lg font-semibold">Consumidores</h2>
          <span class="text-2xl font-bold">{metrics.consumers.count}</span>
        </div>
        <div class="space-y-2">
          <div class="flex items-center justify-between">
            <span>Grupos ativos</span>
            <span class="font-medium">
              {metrics.consumers.groups.filter(g => g.state === 'Stable').length}
            </span>
          </div>
          <div class="flex items-center justify-between">
            <span>Lag total</span>
            <span class="font-medium">
              {formatNumber(metrics.consumers.groups.reduce((sum, g) => sum + g.lag, 0))}
            </span>
          </div>
          <div class="flex items-center justify-between">
            <span>Grupos com lag</span>
            <span class="font-medium">
              {metrics.consumers.groups.filter(g => g.lag > 0).length}
            </span>
          </div>
        </div>
      </div>
      
      <!-- Card: Mensagens -->
      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center justify-between mb-4">
          <h2 class="text-lg font-semibold">Mensagens</h2>
          <span class="text-2xl font-bold">{formatNumber(metrics.messages.inRate)}/s</span>
        </div>
        <div class="space-y-2">
          <div class="flex items-center justify-between">
            <span>Taxa de saída</span>
            <span class="font-medium">{formatNumber(metrics.messages.outRate)}/s</span>
          </div>
          <div class="flex items-center justify-between">
            <span>Total recebidas</span>
            <span class="font-medium">{formatNumber(metrics.messages.totalIn)}</span>
          </div>
          <div class="flex items-center justify-between">
            <span>Total processadas</span>
            <span class="font-medium">{formatNumber(metrics.messages.totalOut)}</span>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Alertas ativos -->
    <div class="bg-white rounded-lg shadow p-6 mb-8">
      <h2 class="text-xl font-semibold mb-4">Alertas Ativos ({activeAlerts.length})</h2>
      
      {activeAlerts.length === 0 ? (
        <div class="text-center py-4 text-gray-500">
          Nenhum alerta ativo no momento.
        </div>
      ) : (
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Severidade</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tipo</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Mensagem</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Timestamp</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              {activeAlerts.map((alert) => (
                <tr>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span class={`px-2 py-1 rounded-full text-xs font-medium ${getSeverityClass(alert.severity)}`}>
                      {alert.severity}
                    </span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">{alert.type}</td>
                  <td class="px-6 py-4">{alert.message}</td>
                  <td class="px-6 py-4 whitespace-nowrap">{formatDate(new Date(alert.timestamp))}</td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span class={`px-2 py-1 rounded-full text-xs font-medium ${
                      alert.status === 'active' ? 'bg-red-100 text-red-800' :
                      alert.status === 'acknowledged' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-green-100 text-green-800'
                    }`}>
                      {alert.status}
                    </span>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
    
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
      <!-- Utilização do sistema -->
      <div class="bg-white rounded-lg shadow p-6">
        <h2 class="text-xl font-semibold mb-4">Utilização do Sistema</h2>
        
        <div class="space-y-4">
          <!-- CPU -->
          <div>
            <div class="flex justify-between mb-1">
              <span>CPU</span>
              <span>{metrics.system.cpu.toFixed(1)}%</span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2.5">
              <div class={`h-2.5 rounded-full ${getPercentClass(metrics.system.cpu)}`} style={`width: ${metrics.system.cpu}%`}></div>
            </div>
          </div>
          
          <!-- Memória -->
          <div>
            <div class="flex justify-between mb-1">
              <span>Memória</span>
              <span>{metrics.system.memory.toFixed(1)}%</span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2.5">
              <div class={`h-2.5 rounded-full ${getPercentClass(metrics.system.memory)}`} style={`width: ${metrics.system.memory}%`}></div>
            </div>
          </div>
          
          <!-- Disco -->
          <div>
            <div class="flex justify-between mb-1">
              <span>Disco</span>
              <span>{metrics.system.disk.toFixed(1)}%</span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2.5">
              <div class={`h-2.5 rounded-full ${getPercentClass(metrics.system.disk)}`} style={`width: ${metrics.system.disk}%`}></div>
            </div>
          </div>
          
          <!-- Rede -->
          <div class="grid grid-cols-2 gap-4">
            <div>
              <div class="flex justify-between mb-1">
                <span>Rede (entrada)</span>
                <span>{formatNumber(metrics.system.network.in)} KB/s</span>
              </div>
            </div>
            <div>
              <div class="flex justify-between mb-1">
                <span>Rede (saída)</span>
                <span>{formatNumber(metrics.system.network.out)} KB/s</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Grupos de consumidores com maior lag -->
      <div class="bg-white rounded-lg shadow p-6">
        <h2 class="text-xl font-semibold mb-4">Top Grupos de Consumidores (por Lag)</h2>
        
        {metrics.consumers.groups.length === 0 ? (
          <div class="text-center py-4 text-gray-500">
            Nenhum grupo de consumidores encontrado.
          </div>
        ) : (
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Grupo</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Estado</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Membros</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Lag</th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                {metrics.consumers.groups
                  .sort((a, b) => b.lag - a.lag)
                  .slice(0, 5)
                  .map((group) => (
                    <tr>
                      <td class="px-6 py-4 whitespace-nowrap">{group.groupId}</td>
                      <td class="px-6 py-4 whitespace-nowrap">
                        <span class={`px-2 py-1 rounded-full text-xs font-medium ${
                          group.state === 'Stable' ? 'bg-green-100 text-green-800' :
                          group.state === 'PreparingRebalance' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {group.state}
                        </span>
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap">{group.members}</td>
                      <td class="px-6 py-4 whitespace-nowrap">
                        <span class={group.lag > 1000 ? 'text-red-600 font-medium' : 'text-gray-900'}>
                          {formatNumber(group.lag)}
                        </span>
                      </td>
                    </tr>
                  ))
                }
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
    
    <div class="text-center text-sm text-gray-500 mt-8">
      <p>Última atualização: {formatDate(new Date(metrics.timestamp))}</p>
      <p class="mt-2">
        <a href="/admin/kafka/dashboard" class="text-blue-600 hover:text-blue-800">
          Atualizar dados
        </a>
      </p>
    </div>
  </div>
  
  <script>
    // Atualizar página a cada 30 segundos
    setTimeout(() => {
      window.location.reload();
    }, 30000);
  </script>
</AdminLayout>
---
