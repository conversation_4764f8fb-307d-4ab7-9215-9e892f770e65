/**
 * Serviço de cache de dados frequentes
 *
 * Este serviço gerencia o cache de dados frequentemente acessados
 * para melhorar a performance do sistema.
 */

import { cacheService } from '@services/cacheService';
import { logger } from '@utils/logger';

/**
 * Tipos de dados que podem ser armazenados em cache
 */
export enum CacheableDataType {
  USER = 'user',
  PRODUCT = 'product',
  CATEGORY = 'category',
  ORDER = 'order',
  PAYMENT = 'payment',
  PERMISSION = 'permission',
  ROLE = 'role',
  RESOURCE = 'resource',
  QUERY_RESULT = 'query_result',
}

/**
 * Interface para configuração de cache
 */
export interface CacheConfig {
  /**
   * Tempo de vida em segundos
   * @default 300 (5 minutos)
   */
  ttl?: number;

  /**
   * Se deve atualizar o TTL ao acessar o item
   * @default true
   */
  refreshOnAccess?: boolean;

  /**
   * Tags para categorizar o item em cache
   */
  tags?: string[];
}

/**
 * Configurações padrão para cada tipo de dado
 */
const DEFAULT_CONFIGS: Record<CacheableDataType, CacheConfig> = {
  [CacheableDataType.USER]: {
    ttl: 15 * 60, // 15 minutos
    refreshOnAccess: true,
    tags: ['user'],
  },
  [CacheableDataType.PRODUCT]: {
    ttl: 30 * 60, // 30 minutos
    refreshOnAccess: true,
    tags: ['product'],
  },
  [CacheableDataType.CATEGORY]: {
    ttl: 60 * 60, // 1 hora
    refreshOnAccess: true,
    tags: ['category'],
  },
  [CacheableDataType.ORDER]: {
    ttl: 10 * 60, // 10 minutos
    refreshOnAccess: true,
    tags: ['order'],
  },
  [CacheableDataType.PAYMENT]: {
    ttl: 5 * 60, // 5 minutos
    refreshOnAccess: true,
    tags: ['payment'],
  },
  [CacheableDataType.PERMISSION]: {
    ttl: 30 * 60, // 30 minutos
    refreshOnAccess: true,
    tags: ['permission', 'auth'],
  },
  [CacheableDataType.ROLE]: {
    ttl: 30 * 60, // 30 minutos
    refreshOnAccess: true,
    tags: ['role', 'auth'],
  },
  [CacheableDataType.RESOURCE]: {
    ttl: 60 * 60, // 1 hora
    refreshOnAccess: true,
    tags: ['resource', 'auth'],
  },
  [CacheableDataType.QUERY_RESULT]: {
    ttl: 5 * 60, // 5 minutos
    refreshOnAccess: false,
    tags: ['query'],
  },
};

/**
 * Serviço de cache de dados
 */
export const dataCacheService = {
  /**
   * Prefixo para chaves de cache
   */
  CACHE_PREFIX: 'data:',

  /**
   * Prefixo para índice de tags
   */
  TAG_PREFIX: 'tag:',

  /**
   * Armazena um item no cache
   * @param type - Tipo de dado
   * @param key - Chave do item
   * @param data - Dados a serem armazenados
   * @param config - Configuração de cache (opcional)
   * @returns Verdadeiro se o item foi armazenado com sucesso
   */
  async set(
    type: CacheableDataType,
    key: string,
    data: any,
    config?: Partial<CacheConfig>
  ): Promise<boolean> {
    try {
      // Obter configuração completa
      const fullConfig = {
        ...DEFAULT_CONFIGS[type],
        ...config,
      };

      // Gerar chave de cache
      const cacheKey = this.getCacheKey(type, key);

      // Preparar dados para armazenamento
      const cacheData = {
        data,
        type,
        key,
        createdAt: Date.now(),
        tags: fullConfig.tags || [],
      };

      // Armazenar no cache
      const success = await cacheService.set(type, cacheKey, JSON.stringify(cacheData), {
        ttl: fullConfig.ttl,
      });

      if (success && fullConfig.tags && fullConfig.tags.length > 0) {
        // Adicionar item a cada tag
        for (const tag of fullConfig.tags) {
          await this.addToTag(tag, type, key);
        }
      }

      return success;
    } catch (error) {
      logger.error('Erro ao armazenar item em cache:', error);
      return false;
    }
  },

  /**
   * Recupera um item do cache
   * @param type - Tipo de dado
   * @param key - Chave do item
   * @param config - Configuração de cache (opcional)
   * @returns Dados armazenados ou null se não encontrado
   */
  async get<T = any>(
    type: CacheableDataType,
    key: string,
    config?: Partial<CacheConfig>
  ): Promise<T | null> {
    try {
      // Obter configuração completa
      const fullConfig = {
        ...DEFAULT_CONFIGS[type],
        ...config,
      };

      // Gerar chave de cache
      const cacheKey = this.getCacheKey(type, key);

      // Buscar do cache
      const cachedData = await cacheService.get(type, cacheKey);

      // Registrar métrica (se o serviço estiver disponível)
      try {
        const { cacheMetricsService } = await import('@services/cacheMetricsService');
        cacheMetricsService.recordRequest(type, !!cachedData);
      } catch (e) {
        // Ignorar erro se o serviço de métricas não estiver disponível
      }

      if (!cachedData) {
        return null;
      }

      // Parsear dados
      const parsedData = JSON.parse(cachedData);

      // Atualizar TTL se necessário
      if (fullConfig.refreshOnAccess) {
        await cacheService.expire(cacheKey, fullConfig.ttl || 300);
      }

      return parsedData.data;
    } catch (error) {
      logger.error('Erro ao recuperar item do cache:', error);
      return null;
    }
  },

  /**
   * Remove um item do cache
   * @param type - Tipo de dado
   * @param key - Chave do item
   * @returns Verdadeiro se o item foi removido com sucesso
   */
  async delete(type: CacheableDataType, key: string): Promise<boolean> {
    try {
      // Gerar chave de cache
      const cacheKey = this.getCacheKey(type, key);

      // Buscar do cache para obter tags
      const cachedData = await cacheService.get(type, cacheKey);

      if (cachedData) {
        // Parsear dados
        const parsedData = JSON.parse(cachedData);

        // Remover item de cada tag
        if (parsedData.tags && parsedData.tags.length > 0) {
          for (const tag of parsedData.tags) {
            await this.removeFromTag(tag, type, key);
          }
        }
      }

      // Remover do cache
      return await cacheService.delete(type, cacheKey);
    } catch (error) {
      logger.error('Erro ao remover item do cache:', error);
      return false;
    }
  },

  /**
   * Invalida todos os itens com uma determinada tag
   * @param tag - Tag para invalidar
   * @returns Número de itens invalidados
   */
  async invalidateByTag(tag: string): Promise<number> {
    try {
      // Gerar chave de tag
      const tagKey = this.getTagKey(tag);

      // Obter itens da tag
      const tagItems = await cacheService.smembers(tagKey);

      if (!tagItems || tagItems.length === 0) {
        return 0;
      }

      // Remover cada item
      let invalidatedCount = 0;

      for (const item of tagItems) {
        // Formato do item: "type:key"
        const [type, ...keyParts] = item.split(':');
        const key = keyParts.join(':');

        // Remover do cache
        const success = await this.delete(type as CacheableDataType, key);

        if (success) {
          invalidatedCount++;
        }
      }

      // Limpar a tag
      await cacheService.del(tagKey);

      return invalidatedCount;
    } catch (error) {
      logger.error('Erro ao invalidar itens por tag:', error);
      return 0;
    }
  },

  /**
   * Adiciona um item a uma tag
   * @param tag - Tag
   * @param type - Tipo de dado
   * @param key - Chave do item
   * @returns Verdadeiro se o item foi adicionado com sucesso
   */
  async addToTag(tag: string, type: CacheableDataType, key: string): Promise<boolean> {
    try {
      // Gerar chave de tag
      const tagKey = this.getTagKey(tag);

      // Adicionar item à tag
      await cacheService.sadd(tagKey, `${type}:${key}`);

      // Definir TTL para a tag (1 dia)
      await cacheService.expire(tagKey, 24 * 60 * 60);

      return true;
    } catch (error) {
      logger.error('Erro ao adicionar item à tag:', error);
      return false;
    }
  },

  /**
   * Remove um item de uma tag
   * @param tag - Tag
   * @param type - Tipo de dado
   * @param key - Chave do item
   * @returns Verdadeiro se o item foi removido com sucesso
   */
  async removeFromTag(tag: string, type: CacheableDataType, key: string): Promise<boolean> {
    try {
      // Gerar chave de tag
      const tagKey = this.getTagKey(tag);

      // Remover item da tag
      await cacheService.srem(tagKey, `${type}:${key}`);

      return true;
    } catch (error) {
      logger.error('Erro ao remover item da tag:', error);
      return false;
    }
  },

  /**
   * Gera a chave de cache para um item
   * @param type - Tipo de dado
   * @param key - Chave do item
   * @returns Chave de cache
   */
  getCacheKey(type: CacheableDataType, key: string): string {
    return `${this.CACHE_PREFIX}${type}:${key}`;
  },

  /**
   * Gera a chave para uma tag
   * @param tag - Tag
   * @returns Chave de tag
   */
  getTagKey(tag: string): string {
    return `${this.TAG_PREFIX}${tag}`;
  },
};
