---
/**
 * Página de detalhes do produto
 *
 * Esta página demonstra o uso de comunicação de dados server-side sem API tradicional,
 * seguindo a arquitetura Zero-JS.
 */

import Layout from '../../layouts/Layout.astro';
import { getProductById } from '../../services/data-service';
import { processFormData } from '../../utils/server-actions';

// Obter ID do produto da URL
const { id } = Astro.params;

if (!id) {
  return Astro.redirect('/produtos');
}

// Buscar produto pelo ID
const product = await getProductById(id);

// Se o produto não existir, redirecionar para a lista de produtos
if (!product) {
  return Astro.redirect('/produtos');
}

// Processar formulário de adição ao carrinho
let cartMessage = '';
let cartError = '';

if (Astro.request.method === 'POST') {
  const formData = await Astro.request.formData();

  // Processar adição ao carrinho
  const result = await processFormData(formData, {
    validate: (data) => {
      const errors: Record<string, string> = {};

      const quantity = Number.parseInt(data.quantity as string);
      if (Number.isNaN(quantity) || quantity < 1) {
        errors.quantity = 'Quantidade inválida';
      }

      return Object.keys(errors).length > 0 ? errors : null;
    },
    process: async (data) => {
      // Simulação de adição ao carrinho
      // Em uma implementação real, isso seria uma chamada a um serviço de carrinho

      return {
        success: true,
        data: {
          productId: id,
          quantity: Number.parseInt(data.quantity as string),
        },
      };
    },
  });

  if (result.success) {
    cartMessage = 'Produto adicionado ao carrinho com sucesso!';
  } else {
    cartError = result.errors?.form || 'Erro ao adicionar produto ao carrinho';
  }
}

// Função para formatar preço
function formatPrice(price: number): string {
  return price.toLocaleString('pt-BR', {
    style: 'currency',
    currency: 'BRL',
  });
}

// Função para formatar data
function formatDate(date: Date): string {
  return new Date(date).toLocaleDateString('pt-BR', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });
}
---

<Layout title={`${product.name} | Estação da Alfabetização`}>
  <main class="container mx-auto px-4 py-8">
    <!-- Breadcrumb -->
    <nav class="text-sm mb-6">
      <ol class="flex items-center space-x-2">
        <li>
          <a href="/" class="text-gray-500 hover:text-primary-blue">Início</a>
        </li>
        <li class="text-gray-500">/</li>
        <li>
          <a href="/produtos" class="text-gray-500 hover:text-primary-blue">Produtos</a>
        </li>
        <li class="text-gray-500">/</li>
        <li class="text-primary-blue font-medium truncate max-w-xs">
          {product.name}
        </li>
      </ol>
    </nav>
    
    <div class="bg-white rounded-lg shadow-sm overflow-hidden">
      <div class="grid grid-cols-1 md:grid-cols-2 gap-8 p-6">
        <!-- Imagem do produto -->
        <div class="product-image">
          <img 
            src={product.imageUrl} 
            alt={product.name}
            class="w-full h-auto rounded-lg"
          >
        </div>
        
        <!-- Detalhes do produto -->
        <div class="product-details">
          <h1 class="text-3xl font-bold mb-2">{product.name}</h1>
          
          {product.featured && (
            <div class="mb-4">
              <span class="bg-primary-yellow-light text-primary-yellow-dark text-sm px-3 py-1 rounded-full">
                Produto em Destaque
              </span>
            </div>
          )}
          
          <div class="text-2xl font-bold text-primary-blue mb-4">
            {formatPrice(product.price)}
          </div>
          
          <div class="mb-6">
            <h2 class="text-lg font-semibold mb-2">Descrição</h2>
            <p class="text-gray-700">{product.description}</p>
          </div>
          
          <div class="mb-6">
            <h2 class="text-lg font-semibold mb-2">Detalhes</h2>
            <ul class="space-y-2 text-gray-700">
              <li>
                <span class="font-medium">Categoria:</span> {product.category}
              </li>
              <li>
                <span class="font-medium">Adicionado em:</span> {formatDate(product.createdAt)}
              </li>
              <li>
                <span class="font-medium">Última atualização:</span> {formatDate(product.updatedAt)}
              </li>
            </ul>
          </div>
          
          <!-- Formulário de adição ao carrinho -->
          <form method="post" class="mt-6">
            <input type="hidden" name="productId" value={product.id}>
            
            <div class="flex items-center gap-4 mb-4">
              <label for="quantity" class="font-medium">Quantidade:</label>
              <input 
                type="number" 
                id="quantity" 
                name="quantity" 
                value="1" 
                min="1" 
                class="w-20 p-2 border rounded"
              >
            </div>
            
            <button 
              type="submit" 
              class="w-full py-3 bg-primary-blue text-white rounded-lg font-medium hover:bg-primary-blue-dark transition-colors"
            >
              Adicionar ao Carrinho
            </button>
            
            {cartMessage && (
              <div class="mt-4 p-3 bg-green-100 text-green-800 rounded-lg">
                {cartMessage}
              </div>
            )}
            
            {cartError && (
              <div class="mt-4 p-3 bg-red-100 text-red-800 rounded-lg">
                {cartError}
              </div>
            )}
          </form>
        </div>
      </div>
    </div>
    
    <!-- Navegação entre produtos -->
    <div class="mt-12 flex justify-between">
      <a 
        href="/produtos" 
        class="inline-flex items-center text-primary-blue hover:underline"
      >
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
          <path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd" />
        </svg>
        Voltar para Produtos
      </a>
    </div>
  </main>
</Layout>

<style>
  .product-image img {
    transition: transform 0.3s ease;
  }
  
  .product-image img:hover {
    transform: scale(1.03);
  }
</style>
