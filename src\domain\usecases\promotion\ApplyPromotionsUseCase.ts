/**
 * Apply Promotions Use Case
 *
 * Caso de uso para aplicar promoções a um carrinho.
 * Parte da implementação da tarefa 8.4.3 - Promoções
 */

import { Promotion } from '../../entities/Promotion';
import { PromotionRepository } from '../../repositories/PromotionRepository';

export interface CartItem {
  productId: string;
  categoryIds: string[];
  price: number;
  quantity: number;
  name: string;
  imageUrl?: string;
}

export interface ApplyPromotionsRequest {
  items: CartItem[];
  cartTotal: number;
  userId?: string;
  isNewCustomer?: boolean;
  userGroups?: string[];
  couponCode?: string;
}

export interface DiscountBreakdown {
  promotionId: string;
  promotionName: string;
  discountAmount: number;
  items?: Array<{
    productId: string;
    discountAmount: number;
  }>;
}

export interface ApplyPromotionsResponse {
  success: boolean;
  originalTotal: number;
  discountedTotal: number;
  totalDiscount: number;
  discountBreakdown: DiscountBreakdown[];
  appliedPromotions: Promotion[];
  error?: string;
}

export class ApplyPromotionsUseCase {
  constructor(private promotionRepository: PromotionRepository) {}

  async execute(request: ApplyPromotionsRequest): Promise<ApplyPromotionsResponse> {
    try {
      // Validar os dados de entrada
      if (!this.validateRequest(request)) {
        return {
          success: false,
          originalTotal: request.cartTotal,
          discountedTotal: request.cartTotal,
          totalDiscount: 0,
          discountBreakdown: [],
          appliedPromotions: [],
          error: 'Dados inválidos para aplicação de promoções.',
        };
      }

      // Obter promoções aplicáveis ao carrinho
      const productIds = request.items.map((item) => item.productId);
      const categoryIds = request.items.flatMap((item) => item.categoryIds);

      const availablePromotions = await this.promotionRepository.getPromotionsForCart(
        productIds,
        categoryIds,
        request.cartTotal
      );

      if (availablePromotions.length === 0) {
        return {
          success: true,
          originalTotal: request.cartTotal,
          discountedTotal: request.cartTotal,
          totalDiscount: 0,
          discountBreakdown: [],
          appliedPromotions: [],
        };
      }

      // Ordenar promoções por prioridade
      const sortedPromotions = this.sortPromotionsByPriority(availablePromotions);

      // Aplicar promoções
      const result = this.calculateDiscounts(
        sortedPromotions,
        request.items,
        request.cartTotal,
        request.isNewCustomer || false,
        request.userGroups || []
      );

      return {
        success: true,
        originalTotal: request.cartTotal,
        discountedTotal: request.cartTotal - result.totalDiscount,
        totalDiscount: result.totalDiscount,
        discountBreakdown: result.discountBreakdown,
        appliedPromotions: result.appliedPromotions,
      };
    } catch (error) {
      console.error('Erro ao aplicar promoções:', error);
      return {
        success: false,
        originalTotal: request.cartTotal,
        discountedTotal: request.cartTotal,
        totalDiscount: 0,
        discountBreakdown: [],
        appliedPromotions: [],
        error: 'Ocorreu um erro ao aplicar as promoções.',
      };
    }
  }

  private validateRequest(request: ApplyPromotionsRequest): boolean {
    // Verificar se há itens no carrinho
    if (!request.items || request.items.length === 0) {
      return false;
    }

    // Verificar se o valor total do carrinho é válido
    if (request.cartTotal <= 0) {
      return false;
    }

    // Verificar se os itens têm dados válidos
    for (const item of request.items) {
      if (!item.productId || item.price < 0 || item.quantity <= 0) {
        return false;
      }
    }

    return true;
  }

  private sortPromotionsByPriority(promotions: Promotion[]): Promotion[] {
    return [...promotions].sort((a, b) => {
      // Ordenar por prioridade (maior primeiro)
      const priorityA = Math.max(...a.rules.map((rule) => rule.priority));
      const priorityB = Math.max(...b.rules.map((rule) => rule.priority));

      return priorityB - priorityA;
    });
  }

  private calculateDiscounts(
    promotions: Promotion[],
    items: CartItem[],
    cartTotal: number,
    isNewCustomer: boolean,
    userGroups: string[]
  ): {
    totalDiscount: number;
    discountBreakdown: DiscountBreakdown[];
    appliedPromotions: Promotion[];
  } {
    let remainingTotal = cartTotal;
    const discountBreakdown: DiscountBreakdown[] = [];
    const appliedPromotions: Promotion[] = [];
    const appliedPromotionIds = new Set<string>();

    // Mapa para rastrear descontos por item
    const itemDiscounts = new Map<string, number>();
    items.forEach((item) => itemDiscounts.set(item.productId, 0));

    // Aplicar promoções em ordem de prioridade
    for (const promotion of promotions) {
      // Verificar se a promoção já foi aplicada (para promoções não empilháveis)
      if (appliedPromotionIds.has(promotion.id)) {
        continue;
      }

      let promotionDiscount = 0;
      const itemDiscountBreakdown: Array<{ productId: string; discountAmount: number }> = [];

      // Verificar se a promoção tem regras que se aplicam ao carrinho como um todo
      const hasCartLevelRules = promotion.rules.some(
        (rule) =>
          rule.type === 'percentage' ||
          rule.type === 'fixed_amount' ||
          rule.type === 'free_shipping' ||
          rule.type === 'bundle'
      );

      if (hasCartLevelRules) {
        // Aplicar desconto ao carrinho como um todo
        const cartDiscount = promotion.calculateCartDiscount(
          items.map((item) => ({
            productId: item.productId,
            categoryIds: item.categoryIds,
            price: item.price,
            quantity: item.quantity,
          })),
          remainingTotal,
          isNewCustomer,
          userGroups
        );

        promotionDiscount += cartDiscount;
      } else {
        // Aplicar desconto item por item
        for (const item of items) {
          const itemDiscount = promotion.calculateItemDiscount(
            {
              productId: item.productId,
              categoryIds: item.categoryIds,
              price: item.price,
              quantity: item.quantity,
            },
            remainingTotal,
            isNewCustomer,
            userGroups
          );

          if (itemDiscount > 0) {
            promotionDiscount += itemDiscount;
            itemDiscountBreakdown.push({
              productId: item.productId,
              discountAmount: itemDiscount,
            });

            // Atualizar o desconto acumulado para o item
            const currentItemDiscount = itemDiscounts.get(item.productId) || 0;
            itemDiscounts.set(item.productId, currentItemDiscount + itemDiscount);
          }
        }
      }

      // Se a promoção gerou algum desconto, registrar
      if (promotionDiscount > 0) {
        discountBreakdown.push({
          promotionId: promotion.id,
          promotionName: promotion.name,
          discountAmount: promotionDiscount,
          items: itemDiscountBreakdown.length > 0 ? itemDiscountBreakdown : undefined,
        });

        appliedPromotions.push(promotion);
        appliedPromotionIds.add(promotion.id);

        // Atualizar o valor restante do carrinho
        remainingTotal -= promotionDiscount;

        // Registrar uso da promoção
        promotion.use();
      }

      // Verificar se há regras não empilháveis
      const hasNonStackableRules = promotion.rules.some((rule) => !rule.stackable);

      if (hasNonStackableRules && promotionDiscount > 0) {
        break; // Parar de aplicar mais promoções
      }
    }

    // Calcular desconto total
    const totalDiscount = cartTotal - remainingTotal;

    return {
      totalDiscount,
      discountBreakdown,
      appliedPromotions,
    };
  }
}
