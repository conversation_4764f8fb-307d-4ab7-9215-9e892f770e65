/**
 * API Endpoint para obter estatísticas de Core Web Vitals
 *
 * Este endpoint fornece estatísticas agregadas das métricas de Core Web Vitals
 * coletadas pelo componente WebVitalsMonitor.
 */

import { createClient } from '@vercel/kv';
import type { APIRoute } from 'astro';
import { logger } from '../../../../utils/logger';

// Configuração do cliente KV
const kv = createClient({
  url: import.meta.env.KV_REST_API_URL || 'https://example.upstash.io',
  token: import.meta.env.KV_REST_API_TOKEN || 'example_token',
});

// Tipos de métricas
const metricTypes = ['lcp', 'fid', 'cls', 'ttfb', 'fcp'];

// Endpoint para obter estatísticas
export const GET: APIRoute = async ({ request }) => {
  try {
    // Obter parâmetros da URL
    const url = new URL(request.url);
    const pageId = url.searchParams.get('pageId');
    const period = Number.parseInt(url.searchParams.get('period') || '7');

    // Validar período
    if (Number.isNaN(period) || period < 1 || period > 365) {
      return new Response(JSON.stringify({ error: 'Período inválido' }), {
        status: 400,
        headers: {
          'Content-Type': 'application/json',
        },
      });
    }

    // Calcular timestamp de início do período
    const startTimestamp = Date.now() - period * 24 * 60 * 60 * 1000;

    // Obter estatísticas para cada tipo de métrica
    const statsPromises = metricTypes.map(async (type) => {
      try {
        // Chave para estatísticas
        const statsKey = pageId ? `web-vitals:stats:${type}:${pageId}` : `web-vitals:stats:${type}`;

        // Obter estatísticas do KV
        const statsJson = await kv.get(statsKey);

        if (!statsJson) {
          // Se não houver estatísticas, retornar valores padrão
          return [
            type,
            {
              count: 0,
              sum: 0,
              min: 0,
              max: 0,
              ratings: {
                good: 0,
                'needs-improvement': 0,
                poor: 0,
              },
              lastUpdated: null,
            },
          ];
        }

        // Converter para objeto
        const stats = JSON.parse(statsJson);

        // Filtrar por período se necessário
        if (stats.lastUpdated && stats.lastUpdated < startTimestamp) {
          // Se as estatísticas são mais antigas que o período, buscar métricas individuais
          return await getStatsForPeriod(type, pageId, startTimestamp);
        }

        return [type, stats];
      } catch (error) {
        logger.error(`Erro ao obter estatísticas para ${type}:`, error);

        // Retornar valores padrão em caso de erro
        return [
          type,
          {
            count: 0,
            sum: 0,
            min: 0,
            max: 0,
            ratings: {
              good: 0,
              'needs-improvement': 0,
              poor: 0,
            },
            lastUpdated: null,
            error: 'Erro ao obter estatísticas',
          },
        ];
      }
    });

    // Aguardar todas as promessas
    const statsResults = await Promise.all(statsPromises);

    // Converter array de resultados para objeto
    const stats = Object.fromEntries(statsResults);

    // Adicionar metadados
    const response = {
      meta: {
        period,
        pageId: pageId || 'all',
        timestamp: Date.now(),
      },
      ...stats,
    };

    return new Response(JSON.stringify(response), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'public, max-age=60',
      },
    });
  } catch (error) {
    logger.error('Erro ao processar requisição de estatísticas:', error);

    return new Response(JSON.stringify({ error: 'Erro interno do servidor' }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  }
};

/**
 * Obtém estatísticas para um período específico
 *
 * @param type Tipo de métrica
 * @param pageId ID da página (opcional)
 * @param startTimestamp Timestamp de início do período
 * @returns Estatísticas para o período
 */
async function getStatsForPeriod(type: string, pageId: string | null, startTimestamp: number) {
  try {
    // Chave para lista de métricas
    const metricListKey = `web-vitals:${type}`;

    // Obter IDs de métricas
    const metricIds = await kv.lrange(metricListKey, 0, -1);

    if (!metricIds || metricIds.length === 0) {
      return [
        type,
        {
          count: 0,
          sum: 0,
          min: 0,
          max: 0,
          ratings: {
            good: 0,
            'needs-improvement': 0,
            poor: 0,
          },
          lastUpdated: null,
        },
      ];
    }

    // Inicializar estatísticas
    const stats = {
      count: 0,
      sum: 0,
      min: Number.POSITIVE_INFINITY,
      max: Number.NEGATIVE_INFINITY,
      ratings: {
        good: 0,
        'needs-improvement': 0,
        poor: 0,
      },
      lastUpdated: null,
    };

    // Obter métricas individuais
    const metricPromises = metricIds.map(async (id) => {
      const metricKey = `web-vitals:${type}:${id}`;
      const metricJson = await kv.get(metricKey);

      if (!metricJson) return null;

      const metric = JSON.parse(metricJson);

      // Filtrar por período
      if (metric.timestamp < startTimestamp) return null;

      // Filtrar por página
      if (pageId && metric.pageId !== pageId) return null;

      return metric;
    });

    const metrics = (await Promise.all(metricPromises)).filter(Boolean);

    // Calcular estatísticas
    for (const metric of metrics) {
      stats.count++;
      stats.sum += metric.value;
      stats.min = Math.min(stats.min, metric.value);
      stats.max = Math.max(stats.max, metric.value);
      stats.ratings[metric.rating]++;

      if (!stats.lastUpdated || metric.timestamp > stats.lastUpdated) {
        stats.lastUpdated = metric.timestamp;
      }
    }

    // Ajustar valores se não houver métricas
    if (stats.count === 0) {
      stats.min = 0;
      stats.max = 0;
    }

    return [type, stats];
  } catch (error) {
    logger.error(`Erro ao obter estatísticas para período (${type}):`, error);

    return [
      type,
      {
        count: 0,
        sum: 0,
        min: 0,
        max: 0,
        ratings: {
          good: 0,
          'needs-improvement': 0,
          poor: 0,
        },
        lastUpdated: null,
        error: 'Erro ao obter estatísticas para período',
      },
    ];
  }
}
