/**
 * Interface de Repositório de Usuários
 *
 * Esta interface define os métodos para acesso e manipulação de usuários
 * no sistema, seguindo o princípio de inversão de dependência.
 */

import { User } from '../../domain/entities/User';

/**
 * Opções de filtro para busca de usuários
 */
export interface UserFilterOptions {
  role?: string;
  active?: boolean;
  search?: string;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

/**
 * Resultado paginado de usuários
 */
export interface PaginatedUsers {
  items: User[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

/**
 * Interface do Repositório de Usuários
 */
export interface UserRepository {
  /**
   * Busca um usuário pelo ID
   *
   * @param id ID do usuário
   * @returns Usuário encontrado ou null se não existir
   */
  findById(id: string): Promise<User | null>;

  /**
   * Busca um usuário pelo email
   *
   * @param email Email do usuário
   * @returns Usuário encontrado ou null se não existir
   */
  findByEmail(email: string): Promise<User | null>;

  /**
   * Busca usuários com filtros
   *
   * @param options Opções de filtro
   * @returns Resultado paginado de usuários
   */
  findAll(options?: UserFilterOptions): Promise<PaginatedUsers>;

  /**
   * Busca usuários por papel/função
   *
   * @param role Papel/função para filtro
   * @param options Opções de filtro adicionais
   * @returns Resultado paginado de usuários
   */
  findByRole(role: string, options?: Omit<UserFilterOptions, 'role'>): Promise<PaginatedUsers>;

  /**
   * Salva um usuário (cria ou atualiza)
   *
   * @param user Usuário a ser salvo
   * @returns Usuário salvo
   */
  save(user: User): Promise<User>;

  /**
   * Remove um usuário
   *
   * @param id ID do usuário a ser removido
   * @returns true se removido com sucesso, false caso contrário
   */
  remove(id: string): Promise<boolean>;

  /**
   * Atualiza o status de ativação de um usuário
   *
   * @param id ID do usuário
   * @param active Status de ativação
   * @returns Usuário atualizado
   */
  updateActiveStatus(id: string, active: boolean): Promise<User>;

  /**
   * Atualiza a senha de um usuário
   *
   * @param id ID do usuário
   * @param passwordHash Hash da nova senha
   * @returns Usuário atualizado
   */
  updatePassword(id: string, passwordHash: string): Promise<User>;
}
