// Interface base para respostas de actions
export interface ActionResponse<T> {
  error?: string;
  result?: T;
  success: boolean;
}

// Interfaces de dados
export interface CategoryData {
  ulid_category: string;
  ulid_parent?: string;
  active: boolean;
  name: string;
  owner_name?: string;
  created_at: Date;
  updated_at: Date;
}

export interface InvoiceData {
  ulid_invoice: string;
  ulid_order: string;
  ulid_user: string;
  cod_status: number;
  value: number;
  tax: number;
  file?: Buffer;
  invoice_no: string;
  created_at: Date;
  updated_at: Date;
}

export interface InvoiceItemData {
  ulid_invoice_item: string;
  ulid_invoice: string;
  ulid_product: string;
  qty: number;
  price: number;
  created_at: Date;
  updated_at: Date;
}

export interface OrderData {
  ulid_order: string;
  ulid_user: string;
  cod_status: number;
  total: number;
  created_at: Date;
  updated_at: Date;
}

export interface OrderItemData {
  ulid_order_item: string;
  ulid_order: string;
  ulid_product: string;
  qty: number;
  price: number;
  created_at: Date;
  updated_at: Date;
}

export interface PaymentData {
  ulid_payment: string;
  ulid_order: string;
  ulid_payment_type: string;
  cod_status: number;
  value: number;
  external_id?: string;
  created_at: Date;
  updated_at: Date;
}

export interface PaymentTypeData {
  ulid_payment_type?: string;
  active: boolean;
  type?: string;
  created_at: Date;
  updated_at: Date;
}

export interface PostData {
  ulid_post: string;
  ulid_user: string;
  ulid_product: string;
  ulid_parent: string;
  title: string;
  comment: string;
  published: boolean;
  rating_value: number;
  created_at: Date;
  updated_at: Date;
}

export interface ProductData {
  ulid_product: string;
  ulid_category: string;
  active: boolean;
  name: string;
  description: string;
  file: Buffer;
  price: number;
  created_at: Date;
  updated_at: Date;
}

export interface SchoolTypeData {
  ulid_school_type?: string;
  active: boolean;
  type?: string;
  created_at: Date;
  updated_at: Date;
}

export interface StatusData {
  cod_status: number;
  status: string;
  created_at: Date;
  updated_at: Date;
}

export interface UserData {
  ulid_user: string;
  ulid_user_type: string;
  ulid_school_type?: string;
  active: boolean;
  email: string;
  password?: string;
  is_teacher: boolean;
  name: string;
  last_login: Date;
  created_at: Date;
  updated_at: Date;
}

export interface UserTypeData {
  ulid_user_type: string;
  active: boolean;
  type?: string;
  created_at: Date;
  updated_at: Date;
}
