---
/**
 * Componente de Container
 *
 * Este componente cria um container responsivo para limitar a largura do conteúdo.
 */

import GridSystem from './GridSystem';

interface Props {
  size?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | 'full' | 'none';
  centered?: boolean;
  fluid?: boolean;
  px?: number;
  py?: number;
  p?: number;
  class?: string;
  id?: string;
}

const {
  size = 'lg',
  centered = true,
  fluid = false,
  px,
  py,
  p,
  class: className = '',
  id,
} = Astro.props;

// Determinar o tamanho do container
const containerSize = fluid ? 'full' : size;
const containerClass = GridSystem.containers[containerSize] || GridSystem.containers.lg;

// Determinar o padding
const paddingX = px !== undefined ? GridSystem.paddingX[px] : '';
const paddingY = py !== undefined ? GridSystem.paddingY[py] : '';
const padding = p !== undefined ? GridSystem.padding[p] : '';

// Combinar todas as classes
const containerClasses = [
  containerClass,
  centered && !containerClass.includes('mx-auto') ? 'mx-auto' : '',
  paddingX,
  paddingY,
  padding,
  className,
]
  .filter(Boolean)
  .join(' ');
---

<div class={containerClasses} id={id}>
  <slot />
</div>
