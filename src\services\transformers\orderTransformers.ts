/**
 * Transformadores para eventos de pedido
 *
 * Este arquivo define transformadores para converter eventos de pedido
 * entre diferentes formatos e versões.
 */

import { MessageTransformer } from '@services/messageProcessingService';
import { FieldMappingTransformer } from '@services/messageTransformationService';
import { logger } from '@utils/logger';

/**
 * Transformador para converter eventos de pedido legados para o novo formato
 */
export class LegacyToNewOrderTransformer implements MessageTransformer {
  /**
   * Transforma um evento de pedido legado para o novo formato
   * @param message - Evento de pedido legado
   * @returns Evento de pedido no novo formato
   */
  transform(message: Record<string, unknown>): Record<string, unknown> {
    try {
      // Mapear campos do formato legado para o novo formato
      const result: Record<string, unknown> = {
        orderId: message.id || message.order_id,
        userId: message.user_id,
        status: this.mapStatus(message.order_status || message.status),
        total: message.total_amount || message.total,
        timestamp: message.timestamp || new Date().toISOString(),
        version: '1.0',
        metadata: {
          ...message.additional_data || message.metadata || {},
          originalFormat: 'legacy',
        },
      };

      // Mapear itens do pedido se disponíveis
      if (Array.isArray(message.items) || Array.isArray(message.order_items)) {
        const items = (message.items || message.order_items) as Array<Record<string, unknown>>;
        result.items = items.map(item => ({
          productId: item.product_id || item.id,
          name: item.product_name || item.name,
          quantity: item.quantity || item.qty || 1,
          price: item.price || item.unit_price || 0,
        }));
      }

      return result;
    } catch (error) {
      logger.error('Erro ao transformar evento de pedido legado:', error);
      throw error;
    }
  }

  /**
   * Mapeia status legado para o novo formato
   * @param status - Status legado
   * @returns Status no novo formato
   */
  private mapStatus(status: unknown): string {
    if (!status) return 'unknown';

    const statusStr = String(status).toLowerCase();

    switch (statusStr) {
      case '1':
      case 'pending':
      case 'new':
        return 'pending';

      case '2':
      case 'paid':
      case 'payment_confirmed':
        return 'paid';

      case '3':
      case 'processing':
      case 'in_progress':
        return 'processing';

      case '4':
      case 'shipped':
      case 'sent':
        return 'shipped';

      case '5':
      case 'delivered':
      case 'completed':
        return 'delivered';

      case '6':
      case 'cancelled':
      case 'canceled':
        return 'cancelled';

      case '7':
      case 'refunded':
        return 'refunded';

      default:
        return statusStr;
    }
  }
}

/**
 * Transformador para converter eventos de pedido do formato interno para o formato de notificação
 */
export class InternalToNotificationOrderTransformer implements MessageTransformer {
  /**
   * Transforma um evento de pedido interno para o formato de notificação
   * @param message - Evento de pedido interno
   * @returns Evento de pedido no formato de notificação
   */
  transform(message: Record<string, unknown>): Record<string, unknown> {
    try {
      // Mapear campos do formato interno para o formato de notificação
      const result: Record<string, unknown> = {
        notificationType: 'order',
        notificationId: crypto.randomUUID(),
        orderId: message.orderId,
        userId: message.userId,
        status: message.status,
        statusLabel: this.getStatusLabel(String(message.status)),
        total: message.total,
        formattedTotal: this.formatCurrency(Number(message.total)),
        timestamp: message.timestamp || new Date().toISOString(),
        metadata: message.metadata,
      };

      // Incluir itens do pedido se disponíveis
      if (Array.isArray(message.items)) {
        result.items = message.items;
        result.itemCount = message.items.length;
        result.itemSummary = this.generateItemSummary(message.items as Array<Record<string, unknown>>);
      }

      return result;
    } catch (error) {
      logger.error('Erro ao transformar evento de pedido para notificação:', error);
      throw error;
    }
  }

  /**
   * Obtém rótulo para status
   * @param status - Status
   * @returns Rótulo do status
   */
  private getStatusLabel(status: string): string {
    switch (status) {
      case 'pending':
        return 'Pendente';
      case 'paid':
        return 'Pago';
      case 'processing':
        return 'Em processamento';
      case 'shipped':
        return 'Enviado';
      case 'delivered':
        return 'Entregue';
      case 'cancelled':
        return 'Cancelado';
      case 'refunded':
        return 'Reembolsado';
      default:
        return 'Desconhecido';
    }
  }

  /**
   * Formata valor como moeda
   * @param value - Valor
   * @returns Valor formatado
   */
  private formatCurrency(value: number): string {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
    }).format(value);
  }

  /**
   * Gera resumo de itens do pedido
   * @param items - Itens do pedido
   * @returns Resumo de itens
   */
  private generateItemSummary(items: Array<Record<string, unknown>>): string {
    if (!items.length) return '';

    // Se houver apenas um item
    if (items.length === 1) {
      const item = items[0];
      return `${item.quantity || 1}x ${item.name}`;
    }

    // Se houver múltiplos itens
    const firstItem = items[0];
    return `${firstItem.quantity || 1}x ${firstItem.name} e mais ${items.length - 1} ${items.length - 1 > 1 ? 'itens' : 'item'}`;
  }
}

// Registrar transformadores usando o serviço de transformação
import { messageTransformationService } from '@services/messageTransformationService';

// Transformador de campo para formato legado para novo
const legacyToNewOrderFieldMapping = new FieldMappingTransformer({
  'id': 'orderId',
  'order_id': 'orderId',
  'user_id': 'userId',
  'order_status': 'status',
  'total_amount': 'total',
  'additional_data': 'metadata',
  'order_items': 'items',
});

// Registrar transformadores
messageTransformationService.registerTransformer(
  'order.legacy_to_new',
  new LegacyToNewOrderTransformer()
);

messageTransformationService.registerTransformer(
  'order.internal_to_notification',
  new InternalToNotificationOrderTransformer()
);

messageTransformationService.registerTransformer(
  'order.field_mapping.legacy_to_new',
  legacyToNewOrderFieldMapping
);
