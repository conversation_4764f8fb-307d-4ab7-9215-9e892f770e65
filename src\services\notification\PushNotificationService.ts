/**
 * Serviço de notificação push
 *
 * Este serviço implementa apenas a interface PushNotificationChannel,
 * seguindo o Interface Segregation Principle.
 */

import { nanoid } from 'nanoid';
import { config } from '../../config';
import {
  NotificationMessage,
  PushNotificationChannel,
} from '../../domain/interfaces/NotificationChannels';
import { logger } from '../../utils/logger';

/**
 * Serviço de notificação push
 */
export class PushNotificationService implements PushNotificationChannel {
  /**
   * Nome do provedor de notificações push
   */
  private readonly providerName: string;

  /**
   * Chave de API para o provedor
   */
  private readonly apiKey: string;

  /**
   * Cria uma nova instância do serviço de notificação push
   */
  constructor() {
    this.providerName = config.push.provider;
    this.apiKey = config.push.apiKey;
  }

  /**
   * Envia uma notificação push
   * @param message - Mensagem de notificação
   * @param deviceToken - Token do dispositivo do destinatário
   * @param options - Opções adicionais para a notificação push
   * @returns ID da notificação enviada
   */
  async sendPush(
    message: NotificationMessage,
    deviceToken: string,
    options?: {
      actionUrl?: string;
      imageUrl?: string;
      sound?: string;
      badge?: number;
      data?: Record<string, unknown>;
      ttl?: number;
    }
  ): Promise<string> {
    try {
      // Gerar ID para a notificação
      const notificationId = uuidv4();

      // Preparar dados da notificação
      const pushData = {
        to: deviceToken,
        notification: {
          title: message.title,
          body: message.body,
          image: options?.imageUrl,
          sound: options?.sound || 'default',
          badge: options?.badge,
          click_action: options?.actionUrl,
        },
        data: {
          notificationId,
          userId: message.userId,
          ...options?.data,
          ...message.metadata,
        },
        android: {
          priority: 'high',
          ttl: options?.ttl ? `${options.ttl}s` : '2419200s', // Default: 4 semanas
        },
        apns: {
          headers: {
            'apns-priority': '10',
            'apns-expiration': options?.ttl
              ? `${Math.floor(Date.now() / 1000) + options.ttl}`
              : '0',
          },
        },
      };

      // Enviar notificação push
      // Aqui seria implementada a chamada real para o serviço de push
      // Este é um exemplo simplificado para demonstração
      logger.debug('Sending push notification', {
        notificationId,
        userId: message.userId,
        deviceToken: this.maskToken(deviceToken),
        provider: this.providerName,
      });

      // Simular resposta do serviço
      const response = {
        success: true,
        messageId: `${this.providerName}-${Date.now()}-${Math.floor(Math.random() * 1000)}`,
      };

      // Registrar envio
      logger.info('Push notification sent', {
        notificationId,
        messageId: response.messageId,
        userId: message.userId,
        deviceToken: this.maskToken(deviceToken),
      });

      // Salvar registro no banco de dados
      await this.saveNotificationRecord(notificationId, message, deviceToken, response.messageId);

      return notificationId;
    } catch (error) {
      // Registrar erro
      logger.error('Failed to send push notification', {
        error: error instanceof Error ? error.message : String(error),
        userId: message.userId,
        deviceToken: this.maskToken(deviceToken),
      });

      // Propagar erro
      throw error;
    }
  }

  /**
   * Mascara o token do dispositivo para logs
   * @param token - Token do dispositivo
   * @returns Token mascarado
   */
  private maskToken(token: string): string {
    if (token.length <= 8) {
      return '****';
    }

    return `${token.substring(0, 4)}...${token.substring(token.length - 4)}`;
  }

  /**
   * Salva registro da notificação no banco de dados
   * @param notificationId - ID da notificação
   * @param message - Mensagem de notificação
   * @param deviceToken - Token do dispositivo
   * @param messageId - ID da mensagem no serviço de push
   */
  private async saveNotificationRecord(
    notificationId: string,
    message: NotificationMessage,
    deviceToken: string,
    messageId: string
  ): Promise<void> {
    try {
      // Aqui seria implementada a lógica para salvar no banco de dados
      // Exemplo simplificado para demonstração
      logger.debug('Saving push notification record', {
        notificationId,
        messageId,
        userId: message.userId,
        deviceToken: this.maskToken(deviceToken),
      });
    } catch (error) {
      logger.error('Failed to save notification record', {
        error: error instanceof Error ? error.message : String(error),
        notificationId,
        userId: message.userId,
      });
    }
  }
}
