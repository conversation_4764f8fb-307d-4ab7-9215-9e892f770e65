/**
 * Serviço de retenção de logs de auditoria
 *
 * Este serviço gerencia a retenção e arquivamento de logs de auditoria,
 * implementando políticas de retenção e rotação de logs.
 */

import * as fs from 'node:fs';
import * as path from 'node:path';
import { pipeline } from 'node:stream';
import { promisify } from 'node:util';
import { createGzip } from 'node:zlib';
import { auditRepository } from '@repository/auditRepository';
import { pgHelper } from '@repository/pgHelper';
import { AuditEventType, AuditSeverity } from '@services/auditService';
import { cacheService } from '@services/cacheService';
import { logger } from '@utils/logger';

// Promisificar funções de filesystem
const mkdir = promisify(fs.mkdir);
const writeFile = promisify(fs.writeFile);
const pipelineAsync = promisify(pipeline);

/**
 * Interface para configuração de retenção
 */
export interface RetentionPolicy {
  /**
   * Nome da política
   */
  name: string;

  /**
   * Descrição da política
   */
  description: string;

  /**
   * Período de retenção em dias
   */
  retentionDays: number;

  /**
   * Tipos de eventos afetados pela política
   */
  eventTypes: AuditEventType[];

  /**
   * Severidades afetadas pela política
   */
  severities: AuditSeverity[];

  /**
   * Se deve arquivar logs antes da exclusão
   */
  archiveBeforeDelete: boolean;

  /**
   * Formato de arquivo para arquivamento
   */
  archiveFormat: 'json' | 'csv';

  /**
   * Se deve comprimir arquivos
   */
  compressArchive: boolean;
}

/**
 * Políticas de retenção padrão
 */
export const defaultRetentionPolicies: RetentionPolicy[] = [
  {
    name: 'security-critical',
    description: 'Logs de segurança críticos',
    retentionDays: 730, // 2 anos
    eventTypes: [
      AuditEventType.LOGIN_FAILED,
      AuditEventType.PERMISSION_CHANGED,
      AuditEventType.USER_CREATED,
      AuditEventType.USER_DELETED,
      AuditEventType.PASSWORD_CHANGED,
      AuditEventType.TWO_FACTOR_ENABLED,
      AuditEventType.TWO_FACTOR_DISABLED,
      AuditEventType.ADMIN_ACTION,
    ],
    severities: [AuditSeverity.CRITICAL, AuditSeverity.ERROR, AuditSeverity.WARNING],
    archiveBeforeDelete: true,
    archiveFormat: 'json',
    compressArchive: true,
  },
  {
    name: 'security-standard',
    description: 'Logs de segurança padrão',
    retentionDays: 365, // 1 ano
    eventTypes: [
      AuditEventType.LOGIN_SUCCESS,
      AuditEventType.LOGOUT,
      AuditEventType.PASSWORD_RESET_REQUESTED,
      AuditEventType.EMAIL_CHANGED,
      AuditEventType.PROFILE_UPDATED,
    ],
    severities: [AuditSeverity.INFO, AuditSeverity.WARNING],
    archiveBeforeDelete: true,
    archiveFormat: 'json',
    compressArchive: true,
  },
  {
    name: 'resource-access',
    description: 'Logs de acesso a recursos',
    retentionDays: 180, // 6 meses
    eventTypes: [
      AuditEventType.RESOURCE_ACCESSED,
      AuditEventType.RESOURCE_CREATED,
      AuditEventType.RESOURCE_UPDATED,
      AuditEventType.RESOURCE_DELETED,
    ],
    severities: [AuditSeverity.INFO, AuditSeverity.WARNING, AuditSeverity.ERROR],
    archiveBeforeDelete: true,
    archiveFormat: 'csv',
    compressArchive: true,
  },
  {
    name: 'system-events',
    description: 'Eventos de sistema',
    retentionDays: 90, // 3 meses
    eventTypes: [
      AuditEventType.SYSTEM_STARTED,
      AuditEventType.SYSTEM_STOPPED,
      AuditEventType.CONFIG_CHANGED,
      AuditEventType.MAINTENANCE_MODE,
    ],
    severities: [
      AuditSeverity.INFO,
      AuditSeverity.WARNING,
      AuditSeverity.ERROR,
      AuditSeverity.CRITICAL,
    ],
    archiveBeforeDelete: true,
    archiveFormat: 'json',
    compressArchive: true,
  },
  {
    name: 'standard-activity',
    description: 'Atividades padrão',
    retentionDays: 90, // 3 meses
    eventTypes: [
      AuditEventType.CONTENT_VIEWED,
      AuditEventType.REPORT_GENERATED,
      AuditEventType.EXPORT_CREATED,
      AuditEventType.IMPORT_COMPLETED,
    ],
    severities: [AuditSeverity.INFO],
    archiveBeforeDelete: false,
    archiveFormat: 'csv',
    compressArchive: true,
  },
];

/**
 * Serviço de retenção de logs de auditoria
 */
export const auditRetentionService = {
  /**
   * Inicializa o serviço de retenção
   */
  async initialize(): Promise<void> {
    try {
      logger.info('Inicializando serviço de retenção de logs de auditoria');

      // Verificar se as políticas estão configuradas no banco de dados
      const policies = await this.getPolicies();

      if (policies.length === 0) {
        // Inserir políticas padrão
        await this.setupDefaultPolicies();
      }

      logger.info('Serviço de retenção de logs de auditoria inicializado com sucesso');
    } catch (error) {
      logger.error('Erro ao inicializar serviço de retenção de logs de auditoria:', error);
      throw error;
    }
  },

  /**
   * Configura políticas padrão
   */
  async setupDefaultPolicies(): Promise<void> {
    try {
      logger.info('Configurando políticas de retenção padrão');

      for (const policy of defaultRetentionPolicies) {
        await this.createPolicy(policy);
      }

      logger.info('Políticas de retenção padrão configuradas com sucesso');
    } catch (error) {
      logger.error('Erro ao configurar políticas de retenção padrão:', error);
      throw error;
    }
  },

  /**
   * Cria uma nova política de retenção
   * @param policy - Política de retenção
   * @returns ID da política criada
   */
  async createPolicy(policy: RetentionPolicy): Promise<string> {
    try {
      const policyId = crypto.randomUUID();

      const query = `
        INSERT INTO tab_audit_retention_policy (
          ulid_policy,
          name,
          description,
          retention_days,
          event_types,
          severities,
          archive_before_delete,
          archive_format,
          compress_archive,
          created_at,
          updated_at
        ) VALUES (
          $1, $2, $3, $4, $5, $6, $7, $8, $9, NOW(), NOW()
        )
        RETURNING ulid_policy
      `;

      const values = [
        policyId,
        policy.name,
        policy.description,
        policy.retentionDays,
        JSON.stringify(policy.eventTypes),
        JSON.stringify(policy.severities),
        policy.archiveBeforeDelete,
        policy.archiveFormat,
        policy.compressArchive,
      ];

      await pgHelper.query(query, values);

      return policyId;
    } catch (error) {
      logger.error('Erro ao criar política de retenção:', error);
      throw error;
    }
  },

  /**
   * Obtém todas as políticas de retenção
   * @returns Lista de políticas
   */
  async getPolicies(): Promise<RetentionPolicy[]> {
    try {
      const query = `
        SELECT
          name,
          description,
          retention_days,
          event_types,
          severities,
          archive_before_delete,
          archive_format,
          compress_archive
        FROM
          tab_audit_retention_policy
        ORDER BY
          name
      `;

      const result = await pgHelper.query(query);

      return result.rows.map((row) => ({
        name: row.name,
        description: row.description,
        retentionDays: row.retention_days,
        eventTypes: JSON.parse(row.event_types),
        severities: JSON.parse(row.severities),
        archiveBeforeDelete: row.archive_before_delete,
        archiveFormat: row.archive_format,
        compressArchive: row.compress_archive,
      }));
    } catch (error) {
      logger.error('Erro ao obter políticas de retenção:', error);
      return [];
    }
  },

  /**
   * Executa o processo de limpeza de logs
   * @returns Número de registros processados
   */
  async executeCleanup(): Promise<number> {
    try {
      logger.info('Iniciando processo de limpeza de logs de auditoria');

      // Obter políticas de retenção
      const policies = await this.getPolicies();

      let totalProcessed = 0;

      // Processar cada política
      for (const policy of policies) {
        const processed = await this.processPolicy(policy);
        totalProcessed += processed;
      }

      logger.info(
        `Processo de limpeza de logs de auditoria concluído. ${totalProcessed} registros processados.`
      );

      return totalProcessed;
    } catch (error) {
      logger.error('Erro ao executar limpeza de logs de auditoria:', error);
      throw error;
    }
  },

  /**
   * Processa uma política de retenção
   * @param policy - Política de retenção
   * @returns Número de registros processados
   */
  async processPolicy(policy: RetentionPolicy): Promise<number> {
    try {
      logger.info(`Processando política de retenção: ${policy.name}`);

      // Calcular data limite
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - policy.retentionDays);

      // Buscar logs afetados pela política
      const logs = await this.getLogsForCleanup(policy.eventTypes, policy.severities, cutoffDate);

      if (logs.length === 0) {
        logger.info(`Nenhum log encontrado para política: ${policy.name}`);
        return 0;
      }

      logger.info(`Encontrados ${logs.length} logs para processamento na política: ${policy.name}`);

      // Arquivar logs se necessário
      if (policy.archiveBeforeDelete) {
        await this.archiveLogs(logs, policy);
      }

      // Excluir logs
      const deleted = await this.deleteLogs(logs.map((log) => log.ulid_audit_log));

      logger.info(
        `Processamento da política ${policy.name} concluído. ${deleted} registros excluídos.`
      );

      return deleted;
    } catch (error) {
      logger.error(`Erro ao processar política de retenção ${policy.name}:`, error);
      return 0;
    }
  },

  /**
   * Obtém logs para limpeza
   * @param eventTypes - Tipos de eventos
   * @param severities - Severidades
   * @param cutoffDate - Data limite
   * @returns Lista de logs
   */
  async getLogsForCleanup(
    eventTypes: AuditEventType[],
    severities: AuditSeverity[],
    cutoffDate: Date
  ): Promise<any[]> {
    try {
      const query = `
        SELECT
          *
        FROM
          tab_audit_log
        WHERE
          event_type = ANY($1)
          AND severity = ANY($2)
          AND created_at < $3
        ORDER BY
          created_at
        LIMIT 1000
      `;

      const values = [eventTypes, severities, cutoffDate];

      const result = await pgHelper.query(query, values);

      return result.rows;
    } catch (error) {
      logger.error('Erro ao obter logs para limpeza:', error);
      return [];
    }
  },

  /**
   * Arquiva logs
   * @param logs - Logs a serem arquivados
   * @param policy - Política de retenção
   */
  async archiveLogs(logs: any[], policy: RetentionPolicy): Promise<void> {
    try {
      if (logs.length === 0) {
        return;
      }

      logger.info(`Arquivando ${logs.length} logs para política: ${policy.name}`);

      // Criar diretório de arquivos se não existir
      const archiveDir = path.join(process.cwd(), 'archives', 'audit');
      await mkdir(archiveDir, { recursive: true });

      // Gerar nome do arquivo
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const fileName = `audit_${policy.name}_${timestamp}.${policy.archiveFormat}`;
      const filePath = path.join(archiveDir, fileName);

      // Converter logs para o formato desejado
      let content = '';

      if (policy.archiveFormat === 'json') {
        content = JSON.stringify(logs, null, 2);
      } else {
        // Formato CSV
        const headers = Object.keys(logs[0]).join(',');
        const rows = logs.map((log) => {
          return Object.values(log)
            .map((value) => {
              if (typeof value === 'object') {
                return `"${JSON.stringify(value).replace(/"/g, '""')}"`;
              }
              return `"${String(value).replace(/"/g, '""')}"`;
            })
            .join(',');
        });

        content = [headers, ...rows].join('\n');
      }

      // Salvar arquivo
      if (policy.compressArchive) {
        const gzip = createGzip();
        const source = fs.createReadStream(filePath);
        const destination = fs.createWriteStream(`${filePath}.gz`);

        await writeFile(filePath, content);
        await pipelineAsync(source, gzip, destination);

        // Remover arquivo original
        fs.unlinkSync(filePath);

        logger.info(`Logs arquivados em ${filePath}.gz`);
      } else {
        await writeFile(filePath, content);
        logger.info(`Logs arquivados em ${filePath}`);
      }
    } catch (error) {
      logger.error('Erro ao arquivar logs:', error);
      throw error;
    }
  },

  /**
   * Exclui logs
   * @param logIds - IDs dos logs a serem excluídos
   * @returns Número de registros excluídos
   */
  async deleteLogs(logIds: string[]): Promise<number> {
    try {
      if (logIds.length === 0) {
        return 0;
      }

      const query = `
        DELETE FROM
          tab_audit_log
        WHERE
          ulid_audit_log = ANY($1)
        RETURNING
          ulid_audit_log
      `;

      const values = [logIds];

      const result = await pgHelper.query(query, values);

      return result.rowCount;
    } catch (error) {
      logger.error('Erro ao excluir logs:', error);
      return 0;
    }
  },
};
