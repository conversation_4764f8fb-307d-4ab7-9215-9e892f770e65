/**
 * API de Monitoramento
 * 
 * Este endpoint fornece acesso às métricas e estatísticas
 * de monitoramento do sistema.
 */

import type { APIRoute } from 'astro';
import { applicationMonitoringService, MetricType } from '@services/applicationMonitoringService';
import { getRequestStats } from '@middlewares/monitoringMiddleware';
import { getQueryStats } from '@repository/dbMonitoringHelper';
import { getCacheOperationStats } from '@services/cacheMonitoringHelper';
import { valkeyMonitoringService } from '@services/valkeyMonitoringService';
import { kafkaService } from '@services/kafkaService';
import { isAdmin } from '@helpers/authGuard';
import os from 'os';

/**
 * Endpoint GET para obter estatísticas gerais do sistema
 */
export const GET: APIRoute = async ({ request, cookies }) => {
  try {
    // Verificar se o usuário é administrador
    const adminResult = await isAdmin({ request, cookies } as any);
    
    if (!adminResult) {
      return new Response(JSON.stringify({ error: 'Não autorizado' }), {
        status: 403,
        headers: {
          'Content-Type': 'application/json'
        }
      });
    }
    
    // Obter estatísticas do sistema
    const stats = applicationMonitoringService.getSystemStats();
    
    // Adicionar informações do sistema operacional
    stats.os = {
      platform: process.platform,
      arch: process.arch,
      version: process.version,
      cpus: os.cpus().length,
      totalMemory: os.totalmem(),
      freeMemory: os.freemem(),
      uptime: os.uptime()
    };
    
    // Adicionar informações do processo
    stats.process = {
      pid: process.pid,
      uptime: process.uptime(),
      memoryUsage: process.memoryUsage(),
      cpuUsage: process.cpuUsage()
    };
    
    return new Response(JSON.stringify({
      success: true,
      data: stats
    }), {
      status: 200,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  } catch (error) {
    return new Response(JSON.stringify({
      success: false,
      error: error.message
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }
};
