/**
 * Serviço de Cache de Sessão com Valkey
 *
 * Este serviço implementa armazenamento e gerenciamento de sessões
 * utilizando o Valkey como backend, oferecendo alta performance,
 * persistência e escalabilidade.
 *
 * Características:
 * - Armazenamento de sessões com TTL configurável
 * - Renovação automática de sessões
 * - Invalidação seletiva por usuário, IP ou critérios
 * - Suporte para invalidação global em emergências
 * - Integração com sistema de monitoramento
 */

import { createHash } from 'node:crypto';
import { applicationMonitoringService } from '@services/applicationMonitoringService';
import { cacheService } from '@services/cacheService';
import { logger } from '@utils/logger';
import { ulid } from 'ulid';

/**
 * Interface para dados de sessão
 */
export interface SessionData {
  /**
   * ID único da sessão
   */
  id: string;

  /**
   * ID do usuário associado à sessão
   */
  userId: string;

  /**
   * Nome do usuário
   */
  userName: string;

  /**
   * Email do usuário
   */
  userEmail: string;

  /**
   * Dados adicionais da sessão
   */
  data: Record<string, any>;

  /**
   * Timestamp de criação da sessão
   */
  createdAt: number;

  /**
   * Timestamp da última atividade
   */
  lastActivity: number;

  /**
   * Endereço IP associado à sessão
   */
  ipAddress?: string;

  /**
   * User-Agent associado à sessão
   */
  userAgent?: string;

  /**
   * Timestamp de expiração da sessão
   */
  expiresAt?: number;
}

/**
 * Interface para opções de sessão
 */
export interface SessionOptions {
  /**
   * Tempo de vida em segundos
   * @default 86400 (24 horas)
   */
  ttl?: number;

  /**
   * Renovar automaticamente a sessão quando próxima da expiração
   * @default true
   */
  autoRenew?: boolean;

  /**
   * Limiar para renovação automática (porcentagem do TTL)
   * @default 0.8 (80%)
   */
  renewThreshold?: number;

  /**
   * Verificar IP para validação de sessão
   * @default true
   */
  validateIp?: boolean;

  /**
   * Verificar User-Agent para validação de sessão
   * @default true
   */
  validateUserAgent?: boolean;
}

/**
 * Serviço de Cache de Sessão com Valkey
 */
export const valkeySessionService = {
  /**
   * Prefixo para chaves de sessão
   */
  SESSION_PREFIX: 'session:',

  /**
   * Prefixo para índice de sessões por usuário
   */
  USER_SESSIONS_PREFIX: 'user:sessions:',

  /**
   * Prefixo para índice de sessões por IP
   */
  IP_SESSIONS_PREFIX: 'ip:sessions:',

  /**
   * Tempo de vida padrão para sessões (24 horas)
   */
  DEFAULT_TTL: 24 * 60 * 60,

  /**
   * Inicializa o serviço de sessão
   */
  async initialize(): Promise<void> {
    try {
      logger.info('Inicializando serviço de cache de sessão com Valkey');

      // Verificar conexão com o Valkey
      const client = await cacheService.getClient();
      await client.ping();

      // Carregar scripts Lua para operações atômicas
      await this.loadScripts();

      logger.info('Serviço de cache de sessão inicializado com sucesso');
    } catch (error) {
      logger.error('Erro ao inicializar serviço de cache de sessão:', error);
      throw error;
    }
  },

  /**
   * Carrega scripts Lua para operações atômicas
   */
  async loadScripts(): Promise<void> {
    try {
      const client = await cacheService.getClient();

      // Script para renovação de sessão
      await client.scriptLoad(`
        local sessionKey = KEYS[1]
        local userSessionsKey = KEYS[2]
        local ipSessionsKey = KEYS[3]
        local newSessionKey = KEYS[4]
        local ttl = tonumber(ARGV[1])
        local sessionData = ARGV[2]
        
        -- Verificar se a sessão existe
        if redis.call('EXISTS', sessionKey) == 0 then
          return 0
        end
        
        -- Obter dados da sessão atual
        local currentData = redis.call('GET', sessionKey)
        
        -- Armazenar nova sessão
        redis.call('SET', newSessionKey, sessionData, 'EX', ttl)
        
        -- Atualizar índices
        if userSessionsKey ~= "" then
          redis.call('SADD', userSessionsKey, newSessionKey)
          redis.call('EXPIRE', userSessionsKey, ttl)
        end
        
        if ipSessionsKey ~= "" then
          redis.call('SADD', ipSessionsKey, newSessionKey)
          redis.call('EXPIRE', ipSessionsKey, ttl)
        end
        
        -- Remover sessão antiga
        redis.call('DEL', sessionKey)
        
        -- Remover da lista de sessões do usuário
        if userSessionsKey ~= "" then
          redis.call('SREM', userSessionsKey, sessionKey)
        end
        
        -- Remover da lista de sessões do IP
        if ipSessionsKey ~= "" then
          redis.call('SREM', ipSessionsKey, sessionKey)
        end
        
        return 1
      `);

      // Script para invalidação de sessões por critérios
      await client.scriptLoad(`
        local pattern = KEYS[1]
        local count = 0
        
        -- Obter todas as chaves que correspondem ao padrão
        local keys = redis.call('KEYS', pattern)
        
        -- Remover cada chave
        for i, key in ipairs(keys) do
          redis.call('DEL', key)
          count = count + 1
        end
        
        return count
      `);

      logger.info('Scripts Lua carregados com sucesso');
    } catch (error) {
      logger.error('Erro ao carregar scripts Lua:', error);
      throw error;
    }
  },

  /**
   * Cria uma nova sessão
   * @param userId - ID do usuário
   * @param userName - Nome do usuário
   * @param userEmail - E-mail do usuário
   * @param data - Dados adicionais da sessão
   * @param ipAddress - Endereço IP
   * @param userAgent - User-Agent
   * @param options - Opções da sessão
   * @returns Dados da sessão criada
   */
  async create(
    userId: string,
    userName: string,
    userEmail: string,
    data: Record<string, any> = {},
    ipAddress?: string,
    userAgent?: string,
    options: SessionOptions = {}
  ): Promise<SessionData> {
    try {
      // Gerar ID único para a sessão
      const sessionId = ulid();

      // Timestamp atual
      const now = Date.now();

      // Calcular TTL
      const ttl = options.ttl || this.DEFAULT_TTL;

      // Criar objeto de sessão
      const session: SessionData = {
        id: sessionId,
        userId,
        userName,
        userEmail,
        data,
        createdAt: now,
        lastActivity: now,
        ipAddress,
        userAgent,
        expiresAt: now + ttl * 1000,
      };

      // Armazenar sessão no cache
      const sessionKey = this.getSessionKey(sessionId);
      await cacheService.setItem(sessionKey, JSON.stringify(session), ttl);

      // Adicionar sessão ao índice de sessões do usuário
      if (userId && userId !== 'anonymous') {
        const userSessionsKey = this.getUserSessionsKey(userId);
        await cacheService.sadd(userSessionsKey, sessionId);
        await cacheService.expire(userSessionsKey, ttl);
      }

      // Adicionar sessão ao índice de sessões do IP
      if (ipAddress) {
        const ipSessionsKey = this.getIpSessionsKey(ipAddress);
        await cacheService.sadd(ipSessionsKey, sessionId);
        await cacheService.expire(ipSessionsKey, ttl);
      }

      // Registrar métrica
      applicationMonitoringService.incrementCounter('session_created');

      return session;
    } catch (error) {
      logger.error('Erro ao criar sessão:', error);
      throw error;
    }
  },

  /**
   * Obtém uma sessão pelo ID
   * @param sessionId - ID da sessão
   * @returns Dados da sessão ou null se não encontrada
   */
  async get(sessionId: string): Promise<SessionData | null> {
    try {
      // Obter sessão do cache
      const sessionKey = this.getSessionKey(sessionId);
      const sessionData = await cacheService.getItem(sessionKey);

      if (!sessionData) {
        return null;
      }

      // Registrar métrica
      applicationMonitoringService.incrementCounter('session_hit');

      return JSON.parse(sessionData);
    } catch (error) {
      logger.error('Erro ao obter sessão:', error);

      // Registrar métrica
      applicationMonitoringService.incrementCounter('session_error');

      return null;
    }
  },

  /**
   * Atualiza uma sessão existente
   * @param sessionId - ID da sessão
   * @param data - Dados a serem atualizados
   * @param ipAddress - Endereço IP atual
   * @param userAgent - User-Agent atual
   * @returns Verdadeiro se a sessão foi atualizada com sucesso
   */
  async update(
    sessionId: string,
    data: Record<string, any>,
    ipAddress?: string,
    userAgent?: string
  ): Promise<boolean> {
    try {
      // Obter sessão atual
      const session = await this.get(sessionId);

      if (!session) {
        return false;
      }

      // Atualizar dados
      session.data = { ...session.data, ...data };
      session.lastActivity = Date.now();

      // Atualizar IP e User-Agent se fornecidos
      if (ipAddress) {
        session.ipAddress = ipAddress;
      }

      if (userAgent) {
        session.userAgent = userAgent;
      }

      // Calcular TTL restante
      const now = Date.now();
      const expiresAt = session.expiresAt || session.createdAt + this.DEFAULT_TTL * 1000;
      const ttlRemaining = Math.max(1, Math.floor((expiresAt - now) / 1000));

      // Armazenar sessão atualizada
      const sessionKey = this.getSessionKey(sessionId);
      await cacheService.setItem(sessionKey, JSON.stringify(session), ttlRemaining);

      // Registrar métrica
      applicationMonitoringService.incrementCounter('session_updated');

      return true;
    } catch (error) {
      logger.error('Erro ao atualizar sessão:', error);

      // Registrar métrica
      applicationMonitoringService.incrementCounter('session_error');

      return false;
    }
  },

  /**
   * Atualiza o timestamp de última atividade da sessão
   * @param sessionId - ID da sessão
   * @param ipAddress - Endereço IP atual
   * @returns Verdadeiro se a sessão foi atualizada com sucesso
   */
  async touch(sessionId: string, ipAddress?: string): Promise<boolean> {
    try {
      // Obter sessão atual
      const session = await this.get(sessionId);

      if (!session) {
        return false;
      }

      // Atualizar timestamp de última atividade
      session.lastActivity = Date.now();

      // Atualizar IP se fornecido
      if (ipAddress) {
        session.ipAddress = ipAddress;
      }

      // Calcular TTL restante
      const now = Date.now();
      const expiresAt = session.expiresAt || session.createdAt + this.DEFAULT_TTL * 1000;
      const ttlRemaining = Math.max(1, Math.floor((expiresAt - now) / 1000));

      // Armazenar sessão atualizada
      const sessionKey = this.getSessionKey(sessionId);
      await cacheService.setItem(sessionKey, JSON.stringify(session), ttlRemaining);

      return true;
    } catch (error) {
      logger.error('Erro ao atualizar timestamp de sessão:', error);
      return false;
    }
  },

  /**
   * Verifica se uma sessão deve ser renovada
   * @param session - Dados da sessão
   * @param options - Opções de renovação
   * @returns Verdadeiro se a sessão deve ser renovada
   */
  shouldRenewSession(
    session: SessionData,
    options: Pick<SessionOptions, 'autoRenew' | 'renewThreshold'> = {}
  ): boolean {
    // Se a renovação automática estiver desativada
    if (options.autoRenew === false) {
      return false;
    }

    // Timestamp atual
    const now = Date.now();

    // Calcular tempo de expiração
    const expiresAt = session.expiresAt || session.createdAt + this.DEFAULT_TTL * 1000;

    // Calcular tempo restante em segundos
    const remainingTime = (expiresAt - now) / 1000;

    // Calcular TTL total
    const totalTtl = (expiresAt - session.createdAt) / 1000;

    // Calcular limiar de renovação (padrão: 80% do TTL decorrido)
    const threshold = options.renewThreshold || 0.8;
    const thresholdTime = totalTtl * (1 - threshold);

    // Renovar se o tempo restante for menor que o limiar
    return remainingTime < thresholdTime;
  },

  /**
   * Renova uma sessão existente
   * @param sessionId - ID da sessão atual
   * @param ipAddress - Endereço IP atual
   * @param userAgent - User-Agent atual
   * @param options - Opções da sessão
   * @returns Nova sessão ou null em caso de erro
   */
  async renewSession(
    sessionId: string,
    ipAddress?: string,
    userAgent?: string,
    options: SessionOptions = {}
  ): Promise<SessionData | null> {
    try {
      // Obter sessão atual
      const session = await this.get(sessionId);

      if (!session) {
        return null;
      }

      // Verificar se a sessão deve ser renovada
      if (!this.shouldRenewSession(session, options)) {
        return session;
      }

      // Gerar novo ID de sessão
      const newSessionId = ulid();

      // Timestamp atual
      const now = Date.now();

      // Calcular TTL
      const ttl = options.ttl || this.DEFAULT_TTL;

      // Criar nova sessão com os mesmos dados
      const newSession: SessionData = {
        id: newSessionId,
        userId: session.userId,
        userName: session.userName,
        userEmail: session.userEmail,
        data: session.data,
        createdAt: now,
        lastActivity: now,
        ipAddress: ipAddress || session.ipAddress,
        userAgent: userAgent || session.userAgent,
        expiresAt: now + ttl * 1000,
      };

      // Usar script Lua para operação atômica
      const client = await cacheService.getClient();

      // Chaves para o script
      const sessionKey = this.getSessionKey(sessionId);
      const newSessionKey = this.getSessionKey(newSessionId);
      const userSessionsKey = session.userId ? this.getUserSessionsKey(session.userId) : '';
      const ipSessionsKey = session.ipAddress ? this.getIpSessionsKey(session.ipAddress) : '';

      // Executar script
      const result = await client.evalSha(
        await client.scriptLoad(`
          local sessionKey = KEYS[1]
          local userSessionsKey = KEYS[2]
          local ipSessionsKey = KEYS[3]
          local newSessionKey = KEYS[4]
          local ttl = tonumber(ARGV[1])
          local sessionData = ARGV[2]
          
          -- Verificar se a sessão existe
          if redis.call('EXISTS', sessionKey) == 0 then
            return 0
          end
          
          -- Obter dados da sessão atual
          local currentData = redis.call('GET', sessionKey)
          
          -- Armazenar nova sessão
          redis.call('SET', newSessionKey, sessionData, 'EX', ttl)
          
          -- Atualizar índices
          if userSessionsKey ~= "" then
            redis.call('SADD', userSessionsKey, newSessionKey)
            redis.call('EXPIRE', userSessionsKey, ttl)
          end
          
          if ipSessionsKey ~= "" then
            redis.call('SADD', ipSessionsKey, newSessionKey)
            redis.call('EXPIRE', ipSessionsKey, ttl)
          end
          
          -- Remover sessão antiga
          redis.call('DEL', sessionKey)
          
          -- Remover da lista de sessões do usuário
          if userSessionsKey ~= "" then
            redis.call('SREM', userSessionsKey, sessionKey)
          end
          
          -- Remover da lista de sessões do IP
          if ipSessionsKey ~= "" then
            redis.call('SREM', ipSessionsKey, sessionKey)
          end
          
          return 1
        `),
        4,
        sessionKey,
        userSessionsKey,
        ipSessionsKey,
        newSessionKey,
        ttl.toString(),
        JSON.stringify(newSession)
      );

      if (result === 0) {
        return null;
      }

      // Registrar métrica
      applicationMonitoringService.incrementCounter('session_renewed');

      return newSession;
    } catch (error) {
      logger.error('Erro ao renovar sessão:', error);

      // Registrar métrica
      applicationMonitoringService.incrementCounter('session_error');

      return null;
    }
  },

  /**
   * Remove uma sessão
   * @param sessionId - ID da sessão
   * @returns Verdadeiro se a sessão foi removida com sucesso
   */
  async delete(sessionId: string): Promise<boolean> {
    try {
      // Obter sessão
      const session = await this.get(sessionId);

      if (!session) {
        return false;
      }

      // Remover sessão do cache
      const sessionKey = this.getSessionKey(sessionId);
      await cacheService.del(sessionKey);

      // Remover da lista de sessões do usuário
      if (session.userId) {
        const userSessionsKey = this.getUserSessionsKey(session.userId);
        await cacheService.srem(userSessionsKey, sessionId);
      }

      // Remover da lista de sessões do IP
      if (session.ipAddress) {
        const ipSessionsKey = this.getIpSessionsKey(session.ipAddress);
        await cacheService.srem(ipSessionsKey, sessionId);
      }

      // Registrar métrica
      applicationMonitoringService.incrementCounter('session_deleted');

      return true;
    } catch (error) {
      logger.error('Erro ao remover sessão:', error);

      // Registrar métrica
      applicationMonitoringService.incrementCounter('session_error');

      return false;
    }
  },

  /**
   * Invalida todas as sessões de um usuário
   * @param userId - ID do usuário
   * @returns Número de sessões invalidadas
   */
  async invalidateUserSessions(userId: string): Promise<number> {
    try {
      // Obter lista de sessões do usuário
      const userSessionsKey = this.getUserSessionsKey(userId);
      const sessionIds = await cacheService.smembers(userSessionsKey);

      if (!sessionIds || sessionIds.length === 0) {
        return 0;
      }

      // Remover cada sessão
      let count = 0;
      for (const sessionId of sessionIds) {
        const success = await this.delete(sessionId);
        if (success) {
          count++;
        }
      }

      // Remover a lista de sessões do usuário
      await cacheService.del(userSessionsKey);

      // Registrar métrica
      applicationMonitoringService.incrementCounter('sessions_invalidated_by_user');

      return count;
    } catch (error) {
      logger.error('Erro ao invalidar sessões do usuário:', error);

      // Registrar métrica
      applicationMonitoringService.incrementCounter('session_error');

      return 0;
    }
  },

  /**
   * Invalida todas as sessões de um endereço IP
   * @param ipAddress - Endereço IP
   * @returns Número de sessões invalidadas
   */
  async invalidateIpSessions(ipAddress: string): Promise<number> {
    try {
      // Obter lista de sessões do IP
      const ipSessionsKey = this.getIpSessionsKey(ipAddress);
      const sessionIds = await cacheService.smembers(ipSessionsKey);

      if (!sessionIds || sessionIds.length === 0) {
        return 0;
      }

      // Remover cada sessão
      let count = 0;
      for (const sessionId of sessionIds) {
        const success = await this.delete(sessionId);
        if (success) {
          count++;
        }
      }

      // Remover a lista de sessões do IP
      await cacheService.del(ipSessionsKey);

      // Registrar métrica
      applicationMonitoringService.incrementCounter('sessions_invalidated_by_ip');

      return count;
    } catch (error) {
      logger.error('Erro ao invalidar sessões do IP:', error);

      // Registrar métrica
      applicationMonitoringService.incrementCounter('session_error');

      return 0;
    }
  },

  /**
   * Invalida todas as sessões que correspondem a um critério
   * @param pattern - Padrão para correspondência (ex: "session:*")
   * @returns Número de sessões invalidadas
   */
  async invalidateByPattern(pattern: string): Promise<number> {
    try {
      // Usar script Lua para operação atômica
      const client = await cacheService.getClient();

      // Executar script
      const count = await client.evalSha(
        await client.scriptLoad(`
          local pattern = KEYS[1]
          local count = 0
          
          -- Obter todas as chaves que correspondem ao padrão
          local keys = redis.call('KEYS', pattern)
          
          -- Remover cada chave
          for i, key in ipairs(keys) do
            redis.call('DEL', key)
            count = count + 1
          end
          
          return count
        `),
        1,
        pattern
      );

      // Registrar métrica
      applicationMonitoringService.incrementCounter('sessions_invalidated_by_pattern');

      return count as number;
    } catch (error) {
      logger.error('Erro ao invalidar sessões por padrão:', error);

      // Registrar métrica
      applicationMonitoringService.incrementCounter('session_error');

      return 0;
    }
  },

  /**
   * Invalida todas as sessões (emergência)
   * @returns Número de sessões invalidadas
   */
  async invalidateAllSessions(): Promise<number> {
    try {
      // Registrar evento de segurança
      logger.warn('Invalidação global de sessões iniciada');

      // Invalidar todas as sessões
      const count = await this.invalidateByPattern(`${this.SESSION_PREFIX}*`);

      // Registrar resultado
      logger.warn(`Invalidação global de sessões concluída: ${count} sessões removidas`);

      // Registrar métrica
      applicationMonitoringService.incrementCounter('sessions_invalidated_global');

      return count;
    } catch (error) {
      logger.error('Erro ao invalidar todas as sessões:', error);

      // Registrar métrica
      applicationMonitoringService.incrementCounter('session_error');

      return 0;
    }
  },

  /**
   * Obtém estatísticas de sessões
   * @returns Estatísticas de sessões
   */
  async getStats(): Promise<{
    activeSessions: number;
    activeUsers: number;
    sessionsCreatedToday: number;
    averageSessionDuration: number;
  }> {
    try {
      const client = await cacheService.getClient();

      // Contar sessões ativas
      const activeSessions = await client.keys(`${this.SESSION_PREFIX}*`);

      // Contar usuários ativos (com sessões)
      const activeUsers = await client.keys(`${this.USER_SESSIONS_PREFIX}*`);

      // Estatísticas do monitoramento
      const sessionsCreatedToday = applicationMonitoringService.getCounterValue('session_created');
      const averageSessionDuration =
        applicationMonitoringService.getLatestMetricValue('session_duration') || 0;

      return {
        activeSessions: activeSessions.length,
        activeUsers: activeUsers.length,
        sessionsCreatedToday,
        averageSessionDuration,
      };
    } catch (error) {
      logger.error('Erro ao obter estatísticas de sessões:', error);

      return {
        activeSessions: 0,
        activeUsers: 0,
        sessionsCreatedToday: 0,
        averageSessionDuration: 0,
      };
    }
  },

  /**
   * Gera chave de sessão para o Valkey
   * @param sessionId - ID da sessão
   * @returns Chave formatada
   */
  getSessionKey(sessionId: string): string {
    return `${this.SESSION_PREFIX}${sessionId}`;
  },

  /**
   * Gera chave para lista de sessões de um usuário
   * @param userId - ID do usuário
   * @returns Chave formatada
   */
  getUserSessionsKey(userId: string): string {
    return `${this.USER_SESSIONS_PREFIX}${userId}`;
  },

  /**
   * Gera chave para lista de sessões de um IP
   * @param ipAddress - Endereço IP
   * @returns Chave formatada
   */
  getIpSessionsKey(ipAddress: string): string {
    // Normalizar IP para evitar caracteres especiais
    const normalizedIp = ipAddress.replace(/[.:]/g, '_');
    return `${this.IP_SESSIONS_PREFIX}${normalizedIp}`;
  },

  /**
   * Gera hash para identificação de sessão
   * @param sessionId - ID da sessão
   * @param ipAddress - Endereço IP
   * @param userAgent - User-Agent
   * @returns Hash de identificação
   */
  generateSessionHash(sessionId: string, ipAddress?: string, userAgent?: string): string {
    const data = `${sessionId}|${ipAddress || ''}|${userAgent || ''}`;
    return createHash('sha256').update(data).digest('hex');
  },
};
