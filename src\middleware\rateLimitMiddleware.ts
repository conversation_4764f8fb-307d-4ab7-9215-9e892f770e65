/**
 * Middleware de controle de taxa de requisições (Rate Limiting)
 *
 * Este middleware implementa controle de taxa de requisições para proteger
 * a API contra abusos, ataques de força bruta e sobrecarga.
 */

import { defineMiddleware } from 'astro:middleware';
import { RateLimitType, rateLimitService } from '@services/rateLimitService';
import { logger } from '@utils/logger';

/**
 * Configuração do middleware
 */
const CONFIG = {
  /**
   * Se o middleware está habilitado
   */
  enabled: true,

  /**
   * Caminhos que devem ser ignorados pelo rate limiting
   */
  excludePaths: ['/assets/', '/static/', '/favicon.ico', '/robots.txt', '/sitemap.xml'],

  /**
   * IPs que devem ser ignorados pelo rate limiting (ex: localhost, servidores de monitoramento)
   */
  whitelistedIPs: ['127.0.0.1', '::1'],
};

/**
 * Obtém o tipo de limite com base no caminho da requisição
 * @param path - Caminho da requisição
 * @returns Tipo de limite
 */
function getLimitTypeForPath(path: string): RateLimitType {
  // Rotas de autenticação
  if (path.startsWith('/api/auth/signin') || path.includes('/login')) {
    return RateLimitType.LOGIN;
  }

  // Rotas de registro
  if (path.startsWith('/api/auth/signup') || path.includes('/register')) {
    return RateLimitType.SIGNUP;
  }

  // Rotas de contato
  if (path.startsWith('/api/contact') || path.includes('/contact')) {
    return RateLimitType.CONTACT;
  }

  // Rotas de upload
  if (path.includes('/upload') || path.includes('/files')) {
    return RateLimitType.UPLOAD;
  }

  // Rotas de pagamento
  if (path.includes('/payment') || path.includes('/checkout')) {
    return RateLimitType.PAYMENT;
  }

  // Rotas de API
  if (path.startsWith('/api/')) {
    return RateLimitType.API;
  }

  // Padrão: limite global
  return RateLimitType.GLOBAL;
}

/**
 * Obtém o identificador para rate limiting
 * @param request - Objeto de requisição
 * @returns Identificador (geralmente IP)
 */
function getIdentifier(request: Request): string {
  // Obter IP do cabeçalho X-Forwarded-For
  const forwardedFor = request.headers.get('x-forwarded-for');

  if (forwardedFor) {
    // Pegar o primeiro IP (cliente original)
    const ips = forwardedFor.split(',');
    return ips[0].trim();
  }

  // Obter IP do cabeçalho X-Real-IP
  const realIP = request.headers.get('x-real-ip');

  if (realIP) {
    return realIP.trim();
  }

  // Fallback: usar um valor padrão
  return 'unknown-ip';
}

/**
 * Verifica se um caminho deve ser ignorado
 * @param path - Caminho da requisição
 * @returns Verdadeiro se o caminho deve ser ignorado
 */
function shouldExcludePath(path: string): boolean {
  return CONFIG.excludePaths.some(
    (excludePath) => path.startsWith(excludePath) || path === excludePath
  );
}

/**
 * Verifica se um IP está na lista de permitidos
 * @param ip - Endereço IP
 * @returns Verdadeiro se o IP está na whitelist
 */
function isWhitelistedIP(ip: string): boolean {
  return CONFIG.whitelistedIPs.includes(ip);
}

/**
 * Middleware de rate limiting
 */
export const onRequest = defineMiddleware(async (context, next) => {
  // Verificar se o middleware está habilitado
  if (!CONFIG.enabled) {
    return next();
  }

  try {
    // Obter caminho da requisição
    const path = new URL(context.request.url).pathname;

    // Verificar se o caminho deve ser ignorado
    if (shouldExcludePath(path)) {
      return next();
    }

    // Obter identificador (IP)
    const identifier = getIdentifier(context.request);

    // Verificar se o IP está na whitelist
    if (isWhitelistedIP(identifier)) {
      return next();
    }

    // Obter tipo de limite com base no caminho
    const limitType = getLimitTypeForPath(path);

    // Verificar limite
    const result = await rateLimitService.check(identifier, limitType);

    // Se o limite foi excedido, retornar erro 429
    if (result.limited) {
      logger.warn('Rate limit excedido', {
        identifier,
        path,
        limitType,
        resetInSeconds: result.resetInSeconds,
      });

      // Criar resposta de erro
      return new Response(
        JSON.stringify({
          error: 'Too Many Requests',
          message: 'Você excedeu o limite de requisições. Tente novamente mais tarde.',
          retryAfter: result.blockExpiresInSeconds || result.resetInSeconds,
        }),
        {
          status: 429,
          headers: {
            'Content-Type': 'application/json',
            'Retry-After': String(result.blockExpiresInSeconds || result.resetInSeconds),
            'X-RateLimit-Limit': String(result.remaining + (result.limited ? 0 : 1)),
            'X-RateLimit-Remaining': String(result.remaining),
            'X-RateLimit-Reset': String(result.resetInSeconds),
          },
        }
      );
    }

    // Executar próximo middleware
    const response = await next();

    // Adicionar cabeçalhos de rate limit à resposta
    const headers = new Headers(response.headers);
    headers.set('X-RateLimit-Limit', String(result.remaining + 1));
    headers.set('X-RateLimit-Remaining', String(result.remaining));
    headers.set('X-RateLimit-Reset', String(result.resetInSeconds));

    // Criar nova resposta com os cabeçalhos adicionados
    return new Response(response.body, {
      status: response.status,
      statusText: response.statusText,
      headers,
    });
  } catch (error) {
    logger.error('Erro no middleware de rate limiting:', error);

    // Em caso de erro, permitir a requisição
    return next();
  }
});
