/**
 * <PERSON><PERSON><PERSON> de Monitoramento
 *
 * <PERSON><PERSON> módulo define as rotas para acesso às métricas
 * e estatísticas de monitoramento do sistema.
 */

import * as monitoringController from '@controllers/monitoringController';
import { authMiddleware } from '@middleware/authMiddleware';
import { adminMiddleware } from '@middleware/authMiddleware'; // Assumindo que adminMiddleware foi movido para authMiddleware
import { Router } from 'express';

const router = Router();

/**
 * @route GET /api/monitoring/system
 * @desc Obtém estatísticas gerais do sistema
 * @access Admin
 */
router.get('/system', authMiddleware, adminMiddleware, monitoringController.getSystemStats);

/**
 * @route GET /api/monitoring/metrics
 * @desc Obtém métricas por tipo
 * @access Admin
 */
router.get('/metrics', authMiddleware, adminMiddleware, monitoringController.getMetrics);

/**
 * @route GET /api/monitoring/alerts
 * @desc Obtém alertas ativos
 * @access Admin
 */
router.get('/alerts', authMiddleware, adminMiddleware, monitoringController.getAlerts);

/**
 * @route GET /api/monitoring/requests
 * @desc Obtém estatísticas de requisições
 * @access Admin
 */
router.get('/requests', authMiddleware, adminMiddleware, monitoringController.getRequestMetrics);

/**
 * @route GET /api/monitoring/database
 * @desc Obtém estatísticas de banco de dados
 * @access Admin
 */
router.get('/database', authMiddleware, adminMiddleware, monitoringController.getDatabaseMetrics);

/**
 * @route GET /api/monitoring/cache
 * @desc Obtém estatísticas de cache
 * @access Admin
 */
router.get('/cache', authMiddleware, adminMiddleware, monitoringController.getCacheMetrics);

/**
 * @route GET /api/monitoring/kafka
 * @desc Obtém estatísticas do Kafka
 * @access Admin
 */
router.get('/kafka', authMiddleware, adminMiddleware, monitoringController.getKafkaMetrics);

/**
 * @route GET /api/monitoring/business
 * @desc Obtém estatísticas de negócio
 * @access Admin
 */
router.get('/business', authMiddleware, adminMiddleware, monitoringController.getBusinessMetrics);

/**
 * @route POST /api/monitoring/reset
 * @desc Reseta contadores de métricas
 * @access Admin
 */
router.post('/reset', authMiddleware, adminMiddleware, monitoringController.resetCounters);

export default router;
