/**
 * Serviço para reconciliação de transações financeiras
 */

import { queryHelper } from '@db/queryHelper';
import { email } from '@helpers/emailHelper';
import { logger } from '@utils/logger';
import { alertService } from './alertService';
import { efiPayService } from './efiPayService';
import { PaymentStatus } from './paymentService';

// Interface para discrepância de transação
export interface TransactionDiscrepancy {
  paymentId: string;
  orderId: string;
  externalId: string;
  systemStatus: string;
  providerStatus: string;
  systemValue: number;
  providerValue: number;
  discrepancyType: 'status' | 'value' | 'missing_system' | 'missing_provider';
  createdAt: Date;
}

// Interface para resultado de reconciliação
export interface ReconciliationResult {
  totalTransactions: number;
  matchedTransactions: number;
  discrepancies: TransactionDiscrepancy[];
  reconciliationDate: Date;
  success: boolean;
}

// Serviço de reconciliação
export const reconciliationService = {
  /**
   * Executa o processo de reconciliação diária
   * @returns Resultado da reconciliação
   */
  async runDailyReconciliation(): Promise<ReconciliationResult> {
    try {
      logger.info('Iniciando processo de reconciliação diária');

      // Obter transações do sistema para o período (últimas 24h por padrão)
      const systemTransactions = await this.getSystemTransactions();

      // Obter transações do provedor para o mesmo período
      const providerTransactions = await this.getProviderTransactions();

      // Comparar transações e identificar discrepâncias
      const discrepancies = await this.compareTransactions(
        systemTransactions,
        providerTransactions
      );

      // Registrar discrepâncias no banco de dados
      await this.logDiscrepancies(discrepancies);

      // Enviar alertas para discrepâncias críticas
      if (discrepancies.length > 0) {
        await this.sendDiscrepancyAlerts(discrepancies);
      }

      // Gerar relatório de reconciliação
      const result: ReconciliationResult = {
        totalTransactions: systemTransactions.length,
        matchedTransactions: systemTransactions.length - discrepancies.length,
        discrepancies,
        reconciliationDate: new Date(),
        success: true,
      };

      // Registrar resultado da reconciliação
      await this.logReconciliationResult(result);

      logger.info(
        `Reconciliação concluída: ${result.matchedTransactions}/${result.totalTransactions} transações correspondentes`
      );

      return result;
    } catch (error) {
      logger.error('Erro durante processo de reconciliação:', error);

      const result: ReconciliationResult = {
        totalTransactions: 0,
        matchedTransactions: 0,
        discrepancies: [],
        reconciliationDate: new Date(),
        success: false,
      };

      await this.logReconciliationResult(result);

      return result;
    }
  },

  /**
   * Obtém transações do sistema para reconciliação
   * @param startDate - Data inicial (padrão: 24h atrás)
   * @param endDate - Data final (padrão: agora)
   * @returns Lista de transações do sistema
   */
  async getSystemTransactions(
    startDate: Date = new Date(Date.now() - 24 * 60 * 60 * 1000),
    endDate: Date = new Date()
  ): Promise<any[]> {
    try {
      const result = await queryHelper.query(
        `SELECT 
          p.ulid_payment,
          p.ulid_order,
          p.external_id,
          p.value,
          s.status as status_name,
          p.cod_status,
          p.created_at,
          p.updated_at,
          pt.type as payment_type
         FROM tab_payment p
         JOIN tab_status s ON p.cod_status = s.cod_status
         JOIN tab_payment_type pt ON p.ulid_payment_type = pt.ulid_payment_type
         WHERE p.created_at BETWEEN $1 AND $2
         AND p.external_id IS NOT NULL
         ORDER BY p.created_at DESC`,
        [startDate.toISOString(), endDate.toISOString()]
      );

      return result.rows;
    } catch (error) {
      logger.error('Erro ao obter transações do sistema:', error);
      throw error;
    }
  },

  /**
   * Obtém transações do provedor de pagamento para reconciliação
   * @param startDate - Data inicial (padrão: 24h atrás)
   * @param endDate - Data final (padrão: agora)
   * @returns Lista de transações do provedor
   */
  async getProviderTransactions(
    startDate: Date = new Date(Date.now() - 24 * 60 * 60 * 1000),
    endDate: Date = new Date()
  ): Promise<any[]> {
    try {
      // Obter transações PIX
      const pixTransactions = await this.getPixTransactions(startDate, endDate);

      // Obter transações de cartão de crédito
      const ccTransactions = await this.getCreditCardTransactions(startDate, endDate);

      // Obter transações de boleto
      const billetTransactions = await this.getBilletTransactions(startDate, endDate);

      // Combinar todas as transações
      return [...pixTransactions, ...ccTransactions, ...billetTransactions];
    } catch (error) {
      logger.error('Erro ao obter transações do provedor:', error);
      throw error;
    }
  },

  /**
   * Obtém transações PIX do provedor
   * @param startDate - Data inicial
   * @param endDate - Data final
   * @returns Lista de transações PIX
   */
  async getPixTransactions(startDate: Date, endDate: Date): Promise<any[]> {
    try {
      const efi = efiPayService.getInstance();

      const params = {
        inicio: startDate.toISOString(),
        fim: endDate.toISOString(),
      };

      const result = await efi.pixListReceived(params);

      // Mapear para formato padrão
      return (result.pix || []).map((pix: any) => ({
        externalId: pix.txid || pix.endToEndId,
        value: Number.parseFloat(pix.valor),
        status: 'RECEBIDO',
        type: 'pix',
        createdAt: new Date(pix.horario),
        raw: pix,
      }));
    } catch (error) {
      logger.error('Erro ao obter transações PIX do provedor:', error);
      return [];
    }
  },

  /**
   * Obtém transações de cartão de crédito do provedor
   * @param startDate - Data inicial
   * @param endDate - Data final
   * @returns Lista de transações de cartão de crédito
   */
  async getCreditCardTransactions(startDate: Date, endDate: Date): Promise<any[]> {
    try {
      const efi = efiPayService.getInstance();

      const params = {
        inicio: startDate.toISOString().split('T')[0],
        fim: endDate.toISOString().split('T')[0],
      };

      const result = await efi.getCharges(params);

      // Filtrar apenas transações de cartão de crédito
      const ccCharges = (result.data || []).filter((charge: any) => charge.payment?.credit_card);

      // Mapear para formato padrão
      return ccCharges.map((charge: any) => ({
        externalId: charge.charge_id.toString(),
        value: charge.total / 100, // Converter de centavos para reais
        status: charge.status,
        type: 'credit_card',
        createdAt: new Date(charge.created_at),
        raw: charge,
      }));
    } catch (error) {
      logger.error('Erro ao obter transações de cartão de crédito do provedor:', error);
      return [];
    }
  },

  /**
   * Obtém transações de boleto do provedor
   * @param startDate - Data inicial
   * @param endDate - Data final
   * @returns Lista de transações de boleto
   */
  async getBilletTransactions(startDate: Date, endDate: Date): Promise<any[]> {
    try {
      const efi = efiPayService.getInstance();

      const params = {
        inicio: startDate.toISOString().split('T')[0],
        fim: endDate.toISOString().split('T')[0],
      };

      const result = await efi.getCharges(params);

      // Filtrar apenas transações de boleto
      const billetCharges = (result.data || []).filter(
        (charge: any) => charge.payment?.banking_billet
      );

      // Mapear para formato padrão
      return billetCharges.map((charge: any) => ({
        externalId: charge.charge_id.toString(),
        value: charge.total / 100, // Converter de centavos para reais
        status: charge.status,
        type: 'billet',
        createdAt: new Date(charge.created_at),
        raw: charge,
      }));
    } catch (error) {
      logger.error('Erro ao obter transações de boleto do provedor:', error);
      return [];
    }
  },

  /**
   * Compara transações do sistema com as do provedor
   * @param systemTransactions - Transações do sistema
   * @param providerTransactions - Transações do provedor
   * @returns Lista de discrepâncias encontradas
   */
  async compareTransactions(
    systemTransactions: any[],
    providerTransactions: any[]
  ): Promise<TransactionDiscrepancy[]> {
    const discrepancies: TransactionDiscrepancy[] = [];

    // Mapear transações do provedor por ID externo para facilitar a busca
    const providerTransactionMap = new Map();
    providerTransactions.forEach((transaction) => {
      providerTransactionMap.set(transaction.externalId, transaction);
    });

    // Verificar cada transação do sistema
    for (const systemTx of systemTransactions) {
      const providerTx = providerTransactionMap.get(systemTx.external_id);

      // Se não encontrou no provedor, registrar discrepância
      if (!providerTx) {
        discrepancies.push({
          paymentId: systemTx.ulid_payment,
          orderId: systemTx.ulid_order,
          externalId: systemTx.external_id,
          systemStatus: systemTx.status_name,
          providerStatus: 'NOT_FOUND',
          systemValue: Number.parseFloat(systemTx.value),
          providerValue: 0,
          discrepancyType: 'missing_provider',
          createdAt: new Date(),
        });
        continue;
      }

      // Remover da lista de provedor para depois verificar as que sobraram
      providerTransactionMap.delete(systemTx.external_id);

      // Verificar discrepância de status
      if (!this.statusesMatch(systemTx.status_name, providerTx.status, systemTx.payment_type)) {
        discrepancies.push({
          paymentId: systemTx.ulid_payment,
          orderId: systemTx.ulid_order,
          externalId: systemTx.external_id,
          systemStatus: systemTx.status_name,
          providerStatus: providerTx.status,
          systemValue: Number.parseFloat(systemTx.value),
          providerValue: providerTx.value,
          discrepancyType: 'status',
          createdAt: new Date(),
        });
      }

      // Verificar discrepância de valor
      // Usar tolerância de 0.01 para evitar problemas de arredondamento
      if (Math.abs(Number.parseFloat(systemTx.value) - providerTx.value) > 0.01) {
        discrepancies.push({
          paymentId: systemTx.ulid_payment,
          orderId: systemTx.ulid_order,
          externalId: systemTx.external_id,
          systemStatus: systemTx.status_name,
          providerStatus: providerTx.status,
          systemValue: Number.parseFloat(systemTx.value),
          providerValue: providerTx.value,
          discrepancyType: 'value',
          createdAt: new Date(),
        });
      }
    }

    // Verificar transações do provedor que não estão no sistema
    for (const [externalId, providerTx] of providerTransactionMap.entries()) {
      discrepancies.push({
        paymentId: '',
        orderId: '',
        externalId,
        systemStatus: 'NOT_FOUND',
        providerStatus: providerTx.status,
        systemValue: 0,
        providerValue: providerTx.value,
        discrepancyType: 'missing_system',
        createdAt: new Date(),
      });
    }

    return discrepancies;
  },

  /**
   * Verifica se os status do sistema e do provedor são compatíveis
   * @param systemStatus - Status no sistema
   * @param providerStatus - Status no provedor
   * @param paymentType - Tipo de pagamento
   * @returns Verdadeiro se os status forem compatíveis
   */
  statusesMatch(systemStatus: string, providerStatus: string, paymentType: string): boolean {
    // Mapeamento de status do provedor para status do sistema
    const statusMap: Record<string, string[]> = {
      // Boleto
      Pendente: ['waiting', 'pending', 'unpaid'],
      Aprovado: ['paid', 'settled'],
      Rejeitado: ['canceled', 'expired'],
      Reembolsado: ['refunded'],
    };

    // Verificar se o status do provedor está na lista de status compatíveis
    return statusMap[systemStatus]?.includes(providerStatus) || false;
  },

  /**
   * Registra discrepâncias no banco de dados
   * @param discrepancies - Lista de discrepâncias
   */
  async logDiscrepancies(discrepancies: TransactionDiscrepancy[]): Promise<void> {
    if (discrepancies.length === 0) return;

    try {
      for (const discrepancy of discrepancies) {
        await queryHelper.query(
          `INSERT INTO tab_reconciliation_discrepancy (
            ulid_payment,
            ulid_order,
            external_id,
            system_status,
            provider_status,
            system_value,
            provider_value,
            discrepancy_type,
            created_at,
            resolved,
            resolution_notes
          ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)`,
          [
            discrepancy.paymentId || null,
            discrepancy.orderId || null,
            discrepancy.externalId,
            discrepancy.systemStatus,
            discrepancy.providerStatus,
            discrepancy.systemValue,
            discrepancy.providerValue,
            discrepancy.discrepancyType,
            discrepancy.createdAt,
            false,
            null,
          ]
        );
      }

      logger.info(`Registradas ${discrepancies.length} discrepâncias no banco de dados`);
    } catch (error) {
      logger.error('Erro ao registrar discrepâncias:', error);
      throw error;
    }
  },

  /**
   * Envia alertas para discrepâncias críticas
   * @param discrepancies - Lista de discrepâncias
   */
  async sendDiscrepancyAlerts(discrepancies: TransactionDiscrepancy[]): Promise<void> {
    try {
      // Obter configurações de alerta
      const alertConfig = await alertService.getAlertConfig('payment_failure');

      if (!alertConfig || !alertConfig.enabled || alertConfig.recipients.length === 0) {
        logger.warn('Alertas de discrepância não configurados ou desativados');
        return;
      }

      // Preparar dados para o template
      const templateData = {
        discrepancies,
        totalDiscrepancies: discrepancies.length,
        reconciliationDate: new Date().toLocaleString('pt-BR'),
        statusDiscrepancies: discrepancies.filter((d) => d.discrepancyType === 'status').length,
        valueDiscrepancies: discrepancies.filter((d) => d.discrepancyType === 'value').length,
        missingSystemDiscrepancies: discrepancies.filter(
          (d) => d.discrepancyType === 'missing_system'
        ).length,
        missingProviderDiscrepancies: discrepancies.filter(
          (d) => d.discrepancyType === 'missing_provider'
        ).length,
      };

      // Enviar e-mail para administradores
      await email.sendEmail({
        to: alertConfig.recipients,
        subject: `Alerta: ${discrepancies.length} discrepâncias encontradas na reconciliação`,
        template: 'reconciliation-alert',
        data: templateData,
      });

      logger.info(`Alerta de discrepâncias enviado para ${alertConfig.recipients.join(', ')}`);
    } catch (error) {
      logger.error('Erro ao enviar alertas de discrepância:', error);
    }
  },

  /**
   * Registra resultado da reconciliação
   * @param result - Resultado da reconciliação
   */
  async logReconciliationResult(result: ReconciliationResult): Promise<void> {
    try {
      await queryHelper.query(
        `INSERT INTO tab_reconciliation_log (
          total_transactions,
          matched_transactions,
          discrepancy_count,
          reconciliation_date,
          success,
          created_at
        ) VALUES ($1, $2, $3, $4, $5, NOW())`,
        [
          result.totalTransactions,
          result.matchedTransactions,
          result.discrepancies.length,
          result.reconciliationDate,
          result.success,
        ]
      );

      logger.info('Resultado da reconciliação registrado com sucesso');
    } catch (error) {
      logger.error('Erro ao registrar resultado da reconciliação:', error);
    }
  },

  /**
   * Obtém histórico de reconciliações
   * @param limit - Limite de registros (padrão: 10)
   * @returns Histórico de reconciliações
   */
  async getReconciliationHistory(limit = 10): Promise<any[]> {
    try {
      const result = await queryHelper.query(
        `SELECT *
         FROM tab_reconciliation_log
         ORDER BY reconciliation_date DESC
         LIMIT $1`,
        [limit]
      );

      return result.rows;
    } catch (error) {
      logger.error('Erro ao obter histórico de reconciliações:', error);
      return [];
    }
  },

  /**
   * Obtém discrepâncias não resolvidas
   * @param limit - Limite de registros (padrão: 100)
   * @returns Lista de discrepâncias não resolvidas
   */
  async getUnresolvedDiscrepancies(limit = 100): Promise<any[]> {
    try {
      const result = await queryHelper.query(
        `SELECT *
         FROM tab_reconciliation_discrepancy
         WHERE resolved = false
         ORDER BY created_at DESC
         LIMIT $1`,
        [limit]
      );

      return result.rows;
    } catch (error) {
      logger.error('Erro ao obter discrepâncias não resolvidas:', error);
      return [];
    }
  },

  /**
   * Marca uma discrepância como resolvida
   * @param discrepancyId - ID da discrepância
   * @param resolutionNotes - Notas sobre a resolução
   * @returns Verdadeiro se a operação foi bem-sucedida
   */
  async resolveDiscrepancy(discrepancyId: string, resolutionNotes: string): Promise<boolean> {
    try {
      await queryHelper.query(
        `UPDATE tab_reconciliation_discrepancy
         SET resolved = true,
             resolution_notes = $1,
             resolved_at = NOW()
         WHERE ulid_discrepancy = $2`,
        [resolutionNotes, discrepancyId]
      );

      logger.info(`Discrepância ${discrepancyId} marcada como resolvida`);
      return true;
    } catch (error) {
      logger.error(`Erro ao resolver discrepância ${discrepancyId}:`, error);
      return false;
    }
  },
};
