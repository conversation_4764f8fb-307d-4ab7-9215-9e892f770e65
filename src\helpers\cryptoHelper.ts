const key: string = process.env.CRYPTO_KEY || '4dd8354d-9789-4e81-9d11-27ffd31513a7';

// Função simples de hash para gerar um número a partir de uma string
function simpleHash(): number {
  let hash = 0;
  for (let i = 0; i < key.length; i++) {
    hash = (hash << 5) - hash + key.charCodeAt(i); // hash * 31 + charCode
    hash = hash & hash; // Converte para 32 bits
  }
  return Math.abs(hash);
}

// Função para gerar uma matriz de mapeamento com base em um sal
function generateMappingMatrix(): [number[], number[]] {
  const asciiValues = Array.from({ length: 256 }, (_, i) => i);
  const shuffledValues = [...asciiValues];

  // Usar o sal para embaralhar a matriz de forma determinística
  const hash = simpleHash();
  for (let i = 0; i < shuffledValues.length; i++) {
    const j = (hash + i) % shuffledValues.length;
    // Troca os valores
    [shuffledValues[i], shuffledValues[j]] = [shuffledValues[j], shuffledValues[i]];
  }

  return [asciiValues, shuffledValues];
}

// Função de criptografia
function encrypt(text: string): string {
  const [asciiValues, shuffledValues] = generateMappingMatrix();
  let encryptedText = '';

  for (const char of text) {
    const asciiValue = char.charCodeAt(0);
    const index = asciiValues.indexOf(asciiValue);
    if (index !== -1) {
      encryptedText += String.fromCharCode(shuffledValues[index]);
    } else {
      encryptedText += char; // Se não for um caractere ASCII, mantém o original
    }
  }

  return encryptedText;
}

// Função de descriptografia
function decrypt(encryptedText: string): string {
  const [asciiValues, shuffledValues] = generateMappingMatrix();
  let decryptedText = '';

  for (const char of encryptedText) {
    const asciiValue = char.charCodeAt(0);
    const index = shuffledValues.indexOf(asciiValue);
    if (index !== -1) {
      decryptedText += String.fromCharCode(asciiValues[index]);
    } else {
      decryptedText += char; // Se não for um caractere ASCII, mantém o original
    }
  }

  return decryptedText;
}

export const crypto = {
  encrypt,
  decrypt,
};
