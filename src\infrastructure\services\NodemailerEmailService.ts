/**
 * Nodemailer Email Service
 *
 * Implementação do serviço de e-mail usando Nodemailer.
 * Parte da implementação da tarefa 8.5.2 - Notificações externas
 */

import nodemailer from 'nodemailer';
import {
  EmailAttachment,
  EmailOptions,
  EmailResult,
  EmailService,
  EmailTemplate,
} from '../../domain/services/EmailService';
import { renderEmailTemplate } from '../../utils/emailUtils';

export class NodemailerEmailService implements EmailService {
  private transporter: nodemailer.Transporter;
  private defaultFrom: string;
  private scheduledEmails: Map<string, NodeJS.Timeout> = new Map();

  constructor(config: {
    host: string;
    port: number;
    secure: boolean;
    auth: {
      user: string;
      pass: string;
    };
    defaultFrom: string;
  }) {
    this.transporter = nodemailer.createTransport({
      host: config.host,
      port: config.port,
      secure: config.secure,
      auth: config.auth,
    });

    this.defaultFrom = config.defaultFrom;
  }

  async sendEmail(options: EmailOptions): Promise<EmailResult> {
    try {
      // Verificar se é um e-mail agendado
      if (options.scheduledAt && options.scheduledAt > new Date()) {
        return this.scheduleEmail(options, options.scheduledAt);
      }

      // Preparar opções de e-mail
      const mailOptions: nodemailer.SendMailOptions = {
        from: options.from || this.defaultFrom,
        to: options.to,
        cc: options.cc,
        bcc: options.bcc,
        subject: options.subject,
        text: options.text,
        html: options.html,
        replyTo: options.replyTo,
        headers: options.headers,
        attachments: options.attachments?.map((attachment) => ({
          filename: attachment.filename,
          content: attachment.content,
          contentType: attachment.contentType,
        })),
      };

      // Processar template, se fornecido
      if (options.template) {
        const rendered = await this.renderTemplate(options.template);
        mailOptions.subject = rendered.subject || mailOptions.subject;
        mailOptions.html = rendered.html;
        mailOptions.text = rendered.text || mailOptions.text;
      }

      // Enviar e-mail
      const info = await this.transporter.sendMail(mailOptions);

      return {
        success: true,
        messageId: info.messageId,
        timestamp: new Date(),
      };
    } catch (error) {
      console.error('Erro ao enviar e-mail:', error);

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Erro desconhecido ao enviar e-mail',
        timestamp: new Date(),
      };
    }
  }

  async sendTemplateEmail(
    to: string | string[],
    templateName: string,
    data: Record<string, any>,
    options?: Partial<EmailOptions>
  ): Promise<EmailResult> {
    const emailOptions: EmailOptions = {
      to,
      ...options,
      template: {
        name: templateName,
        subject: options?.subject || '',
        data,
      },
    };

    return this.sendEmail(emailOptions);
  }

  async sendNotificationEmail(
    to: string | string[],
    subject: string,
    message: string,
    options?: Partial<EmailOptions>
  ): Promise<EmailResult> {
    // Criar HTML básico para a notificação
    const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background-color: #f5f5f5; padding: 20px; text-align: center;">
          <h1 style="color: #333; margin: 0;">${subject}</h1>
        </div>
        <div style="padding: 20px; border: 1px solid #f5f5f5;">
          <p style="font-size: 16px; line-height: 1.5; color: #333;">${message}</p>
        </div>
        <div style="background-color: #f5f5f5; padding: 10px; text-align: center; font-size: 12px; color: #777;">
          <p>Esta é uma notificação automática. Por favor, não responda a este e-mail.</p>
        </div>
      </div>
    `;

    const emailOptions: EmailOptions = {
      to,
      subject,
      text: message,
      html,
      ...options,
    };

    return this.sendEmail(emailOptions);
  }

  async scheduleEmail(options: EmailOptions, scheduledAt: Date): Promise<EmailResult> {
    try {
      const now = new Date();

      if (scheduledAt <= now) {
        // Se a data agendada já passou, enviar imediatamente
        return this.sendEmail({
          ...options,
          scheduledAt: undefined,
        });
      }

      // Gerar ID para o e-mail agendado
      const messageId = `scheduled_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;

      // Calcular o atraso em milissegundos
      const delay = scheduledAt.getTime() - now.getTime();

      // Agendar o envio
      const timeout = setTimeout(async () => {
        await this.sendEmail({
          ...options,
          scheduledAt: undefined,
        });

        // Remover da lista de e-mails agendados
        this.scheduledEmails.delete(messageId);
      }, delay);

      // Armazenar o timeout para possível cancelamento
      this.scheduledEmails.set(messageId, timeout);

      return {
        success: true,
        messageId,
        timestamp: now,
      };
    } catch (error) {
      console.error('Erro ao agendar e-mail:', error);

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Erro desconhecido ao agendar e-mail',
        timestamp: new Date(),
      };
    }
  }

  async cancelScheduledEmail(messageId: string): Promise<boolean> {
    const timeout = this.scheduledEmails.get(messageId);

    if (timeout) {
      clearTimeout(timeout);
      this.scheduledEmails.delete(messageId);
      return true;
    }

    return false;
  }

  async getEmailStatus(messageId: string): Promise<{
    status:
      | 'sent'
      | 'delivered'
      | 'opened'
      | 'clicked'
      | 'bounced'
      | 'rejected'
      | 'scheduled'
      | 'cancelled'
      | 'unknown';
    timestamp?: Date;
    details?: Record<string, any>;
  }> {
    // Verificar se é um e-mail agendado
    if (messageId.startsWith('scheduled_') && this.scheduledEmails.has(messageId)) {
      return {
        status: 'scheduled',
        timestamp: new Date(),
      };
    }

    // Em um cenário real, aqui seria feita uma consulta à API do provedor de e-mail
    // Por enquanto, retornamos um status desconhecido
    return {
      status: 'unknown',
    };
  }

  async getEmailStats(
    startDate?: Date,
    endDate?: Date
  ): Promise<{
    sent: number;
    delivered: number;
    opened: number;
    clicked: number;
    bounced: number;
    rejected: number;
    openRate: number;
    clickRate: number;
  }> {
    // Em um cenário real, aqui seria feita uma consulta à API do provedor de e-mail
    // Por enquanto, retornamos estatísticas fictícias
    return {
      sent: 100,
      delivered: 95,
      opened: 60,
      clicked: 30,
      bounced: 3,
      rejected: 2,
      openRate: 60,
      clickRate: 30,
    };
  }

  private async renderTemplate(template: EmailTemplate): Promise<{
    subject: string;
    html: string;
    text?: string;
  }> {
    try {
      // Renderizar o template usando a função utilitária
      const rendered = await renderEmailTemplate(template.name, template.data || {});

      return {
        subject: template.subject,
        html: rendered.html,
        text: rendered.text,
      };
    } catch (error) {
      console.error('Erro ao renderizar template de e-mail:', error);

      // Fallback para um template básico em caso de erro
      const subject = template.subject || 'Notificação';
      const message = 'Não foi possível renderizar o conteúdo desta mensagem.';

      const html = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <div style="padding: 20px; border: 1px solid #f5f5f5;">
            <p style="font-size: 16px; line-height: 1.5; color: #333;">${message}</p>
          </div>
        </div>
      `;

      return {
        subject,
        html,
        text: message,
      };
    }
  }
}
