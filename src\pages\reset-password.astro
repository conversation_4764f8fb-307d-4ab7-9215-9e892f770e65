---
import { actions } from '@actions';
import SignContainer from '@components/auth/SignContainer.astro';
import MainHeaderSign from '@components/header/MainHeaderSign.astro';
import { csrfHelper } from '@helpers/csrfHelper';
import BaseLayout from '@layouts/BaseLayout.astro';
import { jwtService } from '@services/jwtService';

// Obter token da URL
const token = Astro.url.searchParams.get('token');

// Verificar se o token é válido
let isValidToken = false;
let tokenError = '';

if (token) {
  try {
    const payload = jwtService.verifyToken(token, 'reset');
    if (payload && payload.type === 'reset') {
      isValidToken = true;
    } else {
      tokenError = 'Token inválido ou expirado';
    }
  } catch (error) {
    tokenError = 'Token inválido ou expirado';
  }
}

// Obter resultado da ação (se houver)
const result = Astro.getActionResult(actions.authAction.resetPassword);

// Gerar token CSRF
const csrfToken = await csrfHelper.generateToken(Astro);

// Verificar se o usuário já está autenticado
const isAuthenticated = await Astro.session.get('isAuthenticated');
if (isAuthenticated) {
  return Astro.redirect('/dashboard');
}
---

<BaseLayout title="Redefinir Senha">
  <MainHeaderSign />
  
  <SignContainer title="Redefinir Senha">
    <div class="text-sm mb-4">
      {isValidToken ? 
        'Digite sua nova senha abaixo.' : 
        'Para redefinir sua senha, você precisa de um token válido.'}
    </div>
    
    {result?.success && (
      <div class="alert alert-success mb-4">
        <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
        <span>Senha redefinida com sucesso! <a href="/signin" class="link link-primary">Faça login</a> com sua nova senha.</span>
      </div>
    )}
    
    {(result?.error || tokenError) && (
      <div class="alert alert-error mb-4">
        <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
        <span>{result?.error || tokenError}</span>
      </div>
    )}
    
    {isValidToken && !result?.success && (
      <form id="reset-password-form" method="POST" action={actions.authAction.resetPassword} class="space-y-4">
        <!-- Token CSRF -->
        <input type="hidden" name="csrf_token" value={csrfToken} />
        
        <!-- Token de reset -->
        <input type="hidden" name="token" value={token} />
        
        <!-- Nova Senha -->
        <div class="form-control">
          <label class="label">
            <span class="label-text">Nova Senha</span>
          </label>
          <div class="relative">
            <input 
              type="password" 
              name="password" 
              id="password"
              class="input input-bordered input-sm w-full" 
              placeholder="Digite sua nova senha"
              required
              minlength="8"
            />
            <button 
              type="button" 
              id="toggle-password" 
              class="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-500"
            >
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
              </svg>
            </button>
          </div>
          <div class="label">
            <span class="label-text-alt text-error hidden" id="password-error"></span>
          </div>
        </div>
        
        <!-- Confirmar Senha -->
        <div class="form-control">
          <label class="label">
            <span class="label-text">Confirmar Senha</span>
          </label>
          <div class="relative">
            <input 
              type="password" 
              name="confirmPassword" 
              id="confirmPassword"
              class="input input-bordered input-sm w-full" 
              placeholder="Confirme sua nova senha"
              required
            />
            <button 
              type="button" 
              id="toggle-confirm-password" 
              class="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-500"
            >
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
              </svg>
            </button>
          </div>
          <div class="label">
            <span class="label-text-alt text-error hidden" id="confirmPassword-error"></span>
          </div>
        </div>
        
        <div class="text-xs mt-2">
          <p>A senha deve conter pelo menos 8 caracteres, incluindo letras maiúsculas, minúsculas e números.</p>
        </div>
        
        <!-- Botões de Ação -->
        <div class="flex justify-between mt-6">
          <a href="/signin" class="btn btn-outline btn-sm">Voltar ao Login</a>
          <button type="submit" class="btn btn-primary btn-sm">Redefinir Senha</button>
        </div>
      </form>
    )}
    
    {!isValidToken && !result?.success && (
      <div class="mt-4">
        <p>Solicite um novo link de recuperação de senha <a href="/forgot-password" class="link link-primary">clicando aqui</a>.</p>
      </div>
    )}
  </SignContainer>
</BaseLayout>

<script>
  // Função para validar senha
  function validatePassword(password) {
    // Pelo menos 8 caracteres, uma letra maiúscula, uma minúscula e um número
    const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).{8,}$/;
    return passwordRegex.test(password);
  }
  
  // Função para mostrar erro
  function showError(elementId, message) {
    const errorElement = document.getElementById(`${elementId}-error`);
    if (errorElement) {
      errorElement.textContent = message;
      errorElement.classList.remove('hidden');
      document.getElementById(elementId).classList.add('input-error');
    }
  }
  
  // Função para limpar erro
  function clearError(elementId) {
    const errorElement = document.getElementById(`${elementId}-error`);
    if (errorElement) {
      errorElement.textContent = '';
      errorElement.classList.add('hidden');
      document.getElementById(elementId).classList.remove('input-error');
    }
  }
  
  // Inicializar quando o documento estiver pronto
  document.addEventListener('DOMContentLoaded', () => {
    const form = document.getElementById('reset-password-form');
    if (!form) return; // Formulário não está presente (token inválido)
    
    const passwordInput = document.getElementById('password');
    const confirmPasswordInput = document.getElementById('confirmPassword');
    
    // Botões para mostrar/ocultar senha
    const togglePasswordBtn = document.getElementById('toggle-password');
    const toggleConfirmPasswordBtn = document.getElementById('toggle-confirm-password');
    
    // Mostrar/ocultar senha
    togglePasswordBtn.addEventListener('click', () => {
      const type = passwordInput.type === 'password' ? 'text' : 'password';
      passwordInput.type = type;
    });
    
    toggleConfirmPasswordBtn.addEventListener('click', () => {
      const type = confirmPasswordInput.type === 'password' ? 'text' : 'password';
      confirmPasswordInput.type = type;
    });
    
    // Validar formulário antes de enviar
    form.addEventListener('submit', (event) => {
      let isValid = true;
      
      // Validar senha
      if (!validatePassword(passwordInput.value)) {
        showError('password', 'A senha deve ter pelo menos 8 caracteres, incluindo maiúsculas, minúsculas e números');
        isValid = false;
      } else {
        clearError('password');
      }
      
      // Validar confirmação de senha
      if (passwordInput.value !== confirmPasswordInput.value) {
        showError('confirmPassword', 'As senhas não coincidem');
        isValid = false;
      } else {
        clearError('confirmPassword');
      }
      
      if (!isValid) {
        event.preventDefault();
      }
    });
    
    // Limpar erros ao digitar
    passwordInput.addEventListener('input', () => {
      clearError('password');
      if (confirmPasswordInput.value) {
        if (passwordInput.value === confirmPasswordInput.value) {
          clearError('confirmPassword');
        } else {
          showError('confirmPassword', 'As senhas não coincidem');
        }
      }
    });
    
    confirmPasswordInput.addEventListener('input', () => {
      if (passwordInput.value === confirmPasswordInput.value) {
        clearError('confirmPassword');
      } else {
        showError('confirmPassword', 'As senhas não coincidem');
      }
    });
  });
</script>
