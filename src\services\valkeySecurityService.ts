/**
 * Serviço de segurança para o Valkey
 *
 * Este serviço implementa medidas de segurança para o Valkey,
 * incluindo autenticação, TLS/SSL e políticas de acesso.
 */

import { exec } from 'node:child_process';
import * as crypto from 'node:crypto';
import * as fs from 'node:fs';
import * as path from 'node:path';
import { promisify } from 'node:util';
import {
  AuthenticationType,
  generateSecurityConfig,
  generateStrongPassword,
  getSecurityConfig,
  saveSecurityConfig,
} from '@config/cache/security.config';
import { cacheService } from '@services/cacheService';
import { logger } from '@utils/logger';

// Promisificar exec
const execAsync = promisify(exec);

/**
 * Interface para usuário ACL
 */
export interface ACLUser {
  /**
   * Nome do usuário
   */
  username: string;

  /**
   * Senha do usuário
   */
  password: string;

  /**
   * Comandos permitidos
   */
  commands: string[];

  /**
   * Padrões de chaves permitidos
   */
  keyPatterns: string[];

  /**
   * Categorias de comandos permitidas
   */
  categories: string[];
}

/**
 * Serviço de segurança para o Valkey
 */
export const valkeySecurityService = {
  /**
   * Inicializa o serviço de segurança
   */
  async initialize(): Promise<void> {
    try {
      logger.info('Inicializando serviço de segurança do Valkey');

      // Verificar configuração de segurança
      const securityConfig = getSecurityConfig();

      // Aplicar configurações de segurança
      if (process.env.VALKEY_APPLY_SECURITY_CONFIG === 'true') {
        // Gerar arquivo de configuração
        const configPath =
          process.env.VALKEY_CONFIG_PATH || path.join(process.cwd(), 'valkey-security.conf');
        saveSecurityConfig(configPath);

        logger.info(`Configuração de segurança do Valkey salva em ${configPath}`);
      }

      // Verificar conexão segura
      await this.verifySecureConnection();

      logger.info('Serviço de segurança do Valkey inicializado com sucesso');
    } catch (error) {
      logger.error('Erro ao inicializar serviço de segurança do Valkey:', error);
      throw error;
    }
  },

  /**
   * Verifica se a conexão com o Valkey é segura
   */
  async verifySecureConnection(): Promise<void> {
    try {
      const client = await cacheService.getClient();

      // Verificar autenticação
      try {
        await client.ping();
        logger.info('Conexão com o Valkey autenticada com sucesso');
      } catch (authError) {
        if (authError.message.includes('NOAUTH')) {
          logger.error('Falha na autenticação com o Valkey. Verifique as credenciais.');
          throw new Error('Falha na autenticação com o Valkey');
        }
        throw authError;
      }

      // Verificar TLS/SSL
      const securityConfig = getSecurityConfig();
      if (securityConfig.tls.enabled) {
        const info = await client.info('server');
        if (!info.includes('tls_version')) {
          logger.warn(
            'TLS/SSL está habilitado na configuração, mas não está ativo no servidor Valkey'
          );
        } else {
          logger.info('Conexão TLS/SSL com o Valkey verificada com sucesso');
        }
      }
    } catch (error) {
      logger.error('Erro ao verificar conexão segura com o Valkey:', error);
      throw error;
    }
  },

  /**
   * Cria um usuário ACL
   * @param user Dados do usuário
   * @returns Verdadeiro se o usuário foi criado com sucesso
   */
  async createACLUser(user: ACLUser): Promise<boolean> {
    try {
      const client = await cacheService.getClient();

      // Construir comando ACL
      let aclCommand = `ACL SETUSER ${user.username}`;

      // Adicionar senha
      if (user.password) {
        aclCommand += ` on >${user.password}`;
      } else {
        // Gerar senha aleatória se não fornecida
        const password = generateStrongPassword(32);
        aclCommand += ` on >${password}`;
      }

      // Adicionar padrões de chaves
      if (user.keyPatterns && user.keyPatterns.length > 0) {
        for (const pattern of user.keyPatterns) {
          aclCommand += ` ~${pattern}`;
        }
      } else {
        // Acesso a todas as chaves por padrão
        aclCommand += ' ~*';
      }

      // Adicionar comandos
      if (user.commands && user.commands.length > 0) {
        // Negar todos os comandos primeiro
        aclCommand += ' -@all';

        // Permitir comandos específicos
        for (const command of user.commands) {
          aclCommand += ` +${command}`;
        }
      } else if (user.categories && user.categories.length > 0) {
        // Negar todos os comandos primeiro
        aclCommand += ' -@all';

        // Permitir categorias específicas
        for (const category of user.categories) {
          aclCommand += ` +@${category}`;
        }
      } else {
        // Permitir todos os comandos por padrão
        aclCommand += ' +@all';
      }

      // Executar comando ACL
      await client.sendCommand(['ACL', 'SETUSER', ...aclCommand.split(' ').slice(2)]);

      // Salvar configuração ACL
      await client.sendCommand(['ACL', 'SAVE']);

      logger.info(`Usuário ACL ${user.username} criado com sucesso`);
      return true;
    } catch (error) {
      logger.error(`Erro ao criar usuário ACL ${user.username}:`, error);
      return false;
    }
  },

  /**
   * Remove um usuário ACL
   * @param username Nome do usuário
   * @returns Verdadeiro se o usuário foi removido com sucesso
   */
  async deleteACLUser(username: string): Promise<boolean> {
    try {
      const client = await cacheService.getClient();

      // Executar comando ACL
      await client.sendCommand(['ACL', 'DELUSER', username]);

      // Salvar configuração ACL
      await client.sendCommand(['ACL', 'SAVE']);

      logger.info(`Usuário ACL ${username} removido com sucesso`);
      return true;
    } catch (error) {
      logger.error(`Erro ao remover usuário ACL ${username}:`, error);
      return false;
    }
  },

  /**
   * Lista todos os usuários ACL
   * @returns Lista de usuários ACL
   */
  async listACLUsers(): Promise<string[]> {
    try {
      const client = await cacheService.getClient();

      // Executar comando ACL
      const users = await client.sendCommand(['ACL', 'LIST']);

      return users;
    } catch (error) {
      logger.error('Erro ao listar usuários ACL:', error);
      return [];
    }
  },

  /**
   * Verifica se um comando está bloqueado
   * @param command Nome do comando
   * @returns Verdadeiro se o comando está bloqueado
   */
  isCommandBlocked(command: string): boolean {
    const securityConfig = getSecurityConfig();

    if (!securityConfig.accessPolicy.enabled) {
      return false;
    }

    if (!securityConfig.accessPolicy.blockedCommands) {
      return false;
    }

    return securityConfig.accessPolicy.blockedCommands.includes(command.toUpperCase());
  },

  /**
   * Obtém o nome real de um comando (considerando renomeações)
   * @param command Nome original do comando
   * @returns Nome real do comando
   */
  getRealCommandName(command: string): string {
    const securityConfig = getSecurityConfig();
    const originalCommand = command.toUpperCase();

    if (securityConfig.renameCommands[originalCommand]) {
      return securityConfig.renameCommands[originalCommand];
    }

    return originalCommand;
  },

  /**
   * Gera um relatório de segurança do Valkey
   * @returns Relatório de segurança
   */
  async generateSecurityReport(): Promise<Record<string, any>> {
    try {
      const client = await cacheService.getClient();

      // Obter informações do servidor
      const info = await client.info();

      // Obter configuração ACL
      let aclEnabled = false;
      let aclUsers: string[] = [];

      try {
        aclUsers = await this.listACLUsers();
        aclEnabled = true;
      } catch (aclError) {
        logger.warn('ACL não está habilitado ou não há permissão para acessá-lo');
      }

      // Verificar TLS/SSL
      const tlsEnabled = info.includes('tls_version');

      // Verificar comandos perigosos
      const dangerousCommands = ['FLUSHALL', 'FLUSHDB', 'CONFIG', 'SHUTDOWN', 'DEBUG'];
      const blockedDangerousCommands = dangerousCommands.filter((cmd) =>
        this.isCommandBlocked(cmd)
      );

      // Construir relatório
      const report = {
        timestamp: new Date().toISOString(),
        version: info.match(/redis_version:(.*?)(\r\n|\n)/)?.[1]?.trim() || 'unknown',
        authentication: {
          enabled: aclEnabled || info.includes('requirepass'),
          aclEnabled,
          userCount: aclUsers.length,
        },
        tls: {
          enabled: tlsEnabled,
          version: info.match(/tls_version:(.*?)(\r\n|\n)/)?.[1]?.trim() || 'none',
        },
        commandSecurity: {
          dangerousCommandsCount: dangerousCommands.length,
          blockedDangerousCommandsCount: blockedDangerousCommands.length,
          securityScore: Math.round(
            (blockedDangerousCommands.length / dangerousCommands.length) * 100
          ),
        },
        recommendations: [] as string[],
      };

      // Adicionar recomendações
      if (!report.authentication.enabled) {
        report.recommendations.push('Habilitar autenticação para proteger o acesso ao Valkey');
      }

      if (!report.tls.enabled) {
        report.recommendations.push(
          'Habilitar TLS/SSL para criptografar a comunicação com o Valkey'
        );
      }

      if (report.commandSecurity.securityScore < 100) {
        report.recommendations.push(
          'Bloquear todos os comandos perigosos para aumentar a segurança'
        );
      }

      return report;
    } catch (error) {
      logger.error('Erro ao gerar relatório de segurança do Valkey:', error);
      return {
        error: 'Falha ao gerar relatório de segurança',
        message: error instanceof Error ? error.message : String(error),
      };
    }
  },
};
