/**
 * Políticas de Invalidação de Cache
 *
 * Define regras e mecanismos para invalidação de cache no sistema
 */

import { deleteCache, deleteCacheByPattern } from './cacheClient';
import { invalidateCacheByEvent } from './strategicCache';

// Tipos de eventos que podem invalidar cache
export enum InvalidationEventType {
  // Eventos de conteúdo
  CONTENT_CREATE = 'content:create',
  CONTENT_UPDATE = 'content:update',
  CONTENT_DELETE = 'content:delete',

  // Eventos de curso
  COURSE_CREATE = 'course:create',
  COURSE_UPDATE = 'course:update',
  COURSE_DELETE = 'course:delete',

  // Eventos de material
  MATERIAL_CREATE = 'material:create',
  MATERIAL_UPDATE = 'material:update',
  MATERIAL_DELETE = 'material:delete',

  // Eventos de usuário
  USER_CREATE = 'user:create',
  USER_UPDATE = 'user:update',
  USER_DELETE = 'user:delete',
  USER_PREFERENCES = 'user:preferences',

  // Eventos de sistema
  SYSTEM_CONFIG = 'system:config',
  SYSTEM_MAINTENANCE = 'system:maintenance',
  SYSTEM_DEPLOY = 'system:deploy',
}

// Interface para políticas de invalidação
interface InvalidationPolicy {
  // Nome da política
  name: string;

  // Descrição da política
  description: string;

  // Eventos que acionam esta política
  events: InvalidationEventType[];

  // Padrões de chave a serem invalidados
  patterns: string[];

  // Função para gerar padrões dinâmicos com base nos parâmetros do evento
  patternGenerator?: (eventType: InvalidationEventType, params: Record<string, any>) => string[];
}

// Políticas de invalidação definidas
const invalidationPolicies: InvalidationPolicy[] = [
  // Política para cursos
  {
    name: 'course-invalidation',
    description: 'Invalida cache relacionado a cursos',
    events: [
      InvalidationEventType.COURSE_CREATE,
      InvalidationEventType.COURSE_UPDATE,
      InvalidationEventType.COURSE_DELETE,
    ],
    patterns: ['courses:list:*', 'odr:/courses*'],
    patternGenerator: (eventType, params) => {
      const patterns = [];

      if (params.courseId) {
        patterns.push(`courses:${params.courseId}:*`);
        patterns.push(`odr:/courses/${params.courseId}*`);
      }

      return patterns;
    },
  },

  // Política para materiais
  {
    name: 'material-invalidation',
    description: 'Invalida cache relacionado a materiais didáticos',
    events: [
      InvalidationEventType.MATERIAL_CREATE,
      InvalidationEventType.MATERIAL_UPDATE,
      InvalidationEventType.MATERIAL_DELETE,
    ],
    patterns: ['materials:list:*', 'odr:/materials*'],
    patternGenerator: (eventType, params) => {
      const patterns = [];

      if (params.materialId) {
        patterns.push(`materials:${params.materialId}:*`);
        patterns.push(`odr:/materials/${params.materialId}*`);
      }

      if (params.categoryId) {
        patterns.push(`materials:*:${params.categoryId}:*`);
        patterns.push(`odr:/materials/category/${params.categoryId}*`);
      }

      return patterns;
    },
  },

  // Política para usuários
  {
    name: 'user-invalidation',
    description: 'Invalida cache relacionado a usuários',
    events: [InvalidationEventType.USER_UPDATE, InvalidationEventType.USER_PREFERENCES],
    patterns: [],
    patternGenerator: (eventType, params) => {
      const patterns = [];

      if (params.userId) {
        patterns.push(`profile:user:${params.userId}:*`);
        patterns.push(`odr:/profile/${params.userId}*`);
        patterns.push(`odr:/dashboard/${params.userId}*`);
      }

      return patterns;
    },
  },

  // Política para configurações do sistema
  {
    name: 'system-invalidation',
    description: 'Invalida cache relacionado a configurações do sistema',
    events: [
      InvalidationEventType.SYSTEM_CONFIG,
      InvalidationEventType.SYSTEM_MAINTENANCE,
      InvalidationEventType.SYSTEM_DEPLOY,
    ],
    patterns: ['static:*', 'odr:/*'],
  },
];

/**
 * Processa um evento de invalidação
 * @param eventType Tipo do evento
 * @param params Parâmetros do evento
 */
export async function processInvalidationEvent(
  eventType: InvalidationEventType,
  params: Record<string, any> = {}
): Promise<void> {
  console.log(`Processando evento de invalidação: ${eventType}`);

  // Encontrar políticas aplicáveis a este evento
  const applicablePolicies = invalidationPolicies.filter((policy) =>
    policy.events.includes(eventType)
  );

  if (applicablePolicies.length === 0) {
    console.log(`Nenhuma política de invalidação encontrada para o evento: ${eventType}`);
    return;
  }

  // Processar cada política
  for (const policy of applicablePolicies) {
    console.log(`Aplicando política de invalidação: ${policy.name}`);

    // Invalidar padrões estáticos
    for (const pattern of policy.patterns) {
      await deleteCacheByPattern(pattern);
      console.log(`Invalidado cache com padrão: ${pattern}`);
    }

    // Invalidar padrões dinâmicos
    if (policy.patternGenerator) {
      const dynamicPatterns = policy.patternGenerator(eventType, params);

      for (const pattern of dynamicPatterns) {
        await deleteCacheByPattern(pattern);
        console.log(`Invalidado cache com padrão dinâmico: ${pattern}`);
      }
    }
  }

  // Propagar evento para o sistema de cache estratégico
  await invalidateCacheByEvent(eventType, params);
}

/**
 * Invalida cache para um curso específico
 * @param courseId ID do curso
 */
export async function invalidateCourseCache(courseId: string): Promise<void> {
  await processInvalidationEvent(InvalidationEventType.COURSE_UPDATE, { courseId });
}

/**
 * Invalida cache para um material específico
 * @param materialId ID do material
 * @param categoryId ID da categoria (opcional)
 */
export async function invalidateMaterialCache(
  materialId: string,
  categoryId?: string
): Promise<void> {
  await processInvalidationEvent(InvalidationEventType.MATERIAL_UPDATE, { materialId, categoryId });
}

/**
 * Invalida cache para um usuário específico
 * @param userId ID do usuário
 */
export async function invalidateUserCache(userId: string): Promise<void> {
  await processInvalidationEvent(InvalidationEventType.USER_UPDATE, { userId });
}

/**
 * Invalida todo o cache do sistema
 * Usar com cautela - apenas em manutenções ou implantações
 */
export async function invalidateAllCache(): Promise<void> {
  await processInvalidationEvent(InvalidationEventType.SYSTEM_DEPLOY);
}
