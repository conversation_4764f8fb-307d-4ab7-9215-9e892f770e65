/**
 * API de Conteúdo por ID
 *
 * Endpoint para gerenciamento de um conteúdo específico.
 * Parte da implementação da tarefa 8.8.3 - Gestão de conteúdo
 */

import type { APIRoute } from 'astro';
import { ContentStatus } from '../../../../domain/entities/Content';
import { ContentCategoryRepository } from '../../../../domain/repositories/ContentCategoryRepository';
import { ContentRepository } from '../../../../domain/repositories/ContentRepository';
import { ContentTagRepository } from '../../../../domain/repositories/ContentTagRepository';
import { SlugService } from '../../../../domain/services/SlugService';
import { TokenService } from '../../../../domain/services/TokenService';
import { UpdateContentStatusUseCase } from '../../../../domain/usecases/content/UpdateContentStatusUseCase';
import { UpdateContentUseCase } from '../../../../domain/usecases/content/UpdateContentUseCase';
import { PostgresContentCategoryRepository } from '../../../../infrastructure/database/repositories/PostgresContentCategoryRepository';
import { PostgresContentRepository } from '../../../../infrastructure/database/repositories/PostgresContentRepository';
import { PostgresContentTagRepository } from '../../../../infrastructure/database/repositories/PostgresContentTagRepository';
import { DefaultSlugService } from '../../../../infrastructure/services/DefaultSlugService';
import { JwtTokenService } from '../../../../infrastructure/services/JwtTokenService';

// Inicializar repositórios
const contentRepository: ContentRepository = new PostgresContentRepository();
const categoryRepository: ContentCategoryRepository = new PostgresContentCategoryRepository();
const tagRepository: ContentTagRepository = new PostgresContentTagRepository();

// Inicializar serviços
const slugService: SlugService = new DefaultSlugService();
const tokenService: TokenService = new JwtTokenService(
  process.env.JWT_SECRET || 'default-secret-key',
  process.env.PASSWORD_RESET_SECRET || 'password-reset-secret-key',
  process.env.EMAIL_VERIFICATION_SECRET || 'email-verification-secret-key'
);

// Inicializar casos de uso
const updateContentUseCase = new UpdateContentUseCase(
  contentRepository,
  categoryRepository,
  tagRepository,
  slugService
);

const updateContentStatusUseCase = new UpdateContentStatusUseCase(contentRepository);

// Verificar autenticação e permissões
const checkAuth = (
  request: Request
): { userId: string; canRead: boolean; canWrite: boolean; canDelete: boolean } | null => {
  const authHeader = request.headers.get('Authorization');

  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null;
  }

  const token = authHeader.substring(7);
  const payload = tokenService.verifyToken(token);

  if (!payload) {
    return null;
  }

  // Verificar permissões
  const canRead = payload.role === 'admin' || payload.permissions?.includes('content:read');

  const canWrite = payload.role === 'admin' || payload.permissions?.includes('content:write');

  const canDelete = payload.role === 'admin' || payload.permissions?.includes('content:delete');

  return {
    userId: payload.id,
    canRead,
    canWrite,
    canDelete,
  };
};

export const GET: APIRoute = async ({ params, request }) => {
  try {
    const id = params.id;

    if (!id) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'ID do conteúdo é obrigatório.',
        }),
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    // Verificar autenticação
    const auth = checkAuth(request);

    if (!auth) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Não autorizado. Faça login para continuar.',
        }),
        {
          status: 401,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    if (!auth.canRead) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Você não tem permissão para acessar este recurso.',
        }),
        {
          status: 403,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    // Obter conteúdo
    const content = await contentRepository.getById(id);

    if (!content) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Conteúdo não encontrado.',
        }),
        {
          status: 404,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    return new Response(
      JSON.stringify({
        success: true,
        data: content,
      }),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  } catch (error) {
    console.error('Erro ao processar obtenção de conteúdo:', error);

    return new Response(
      JSON.stringify({
        success: false,
        error: 'Ocorreu um erro ao processar a obtenção do conteúdo. Por favor, tente novamente.',
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  }
};

export const PUT: APIRoute = async ({ params, request }) => {
  try {
    const id = params.id;

    if (!id) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'ID do conteúdo é obrigatório.',
        }),
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    // Verificar autenticação
    const auth = checkAuth(request);

    if (!auth) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Não autorizado. Faça login para continuar.',
        }),
        {
          status: 401,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    if (!auth.canWrite) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Você não tem permissão para atualizar conteúdo.',
        }),
        {
          status: 403,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    // Obter dados do corpo da requisição
    const body = await request.json();

    // Verificar se é uma atualização de status
    if (body.status && Object.keys(body).length === 1) {
      // Executar caso de uso de atualização de status
      const result = await updateContentStatusUseCase.execute({
        id,
        status: body.status as ContentStatus,
        scheduledAt: body.scheduledAt ? new Date(body.scheduledAt) : undefined,
        userId: auth.userId,
      });

      if (result.success && result.data) {
        return new Response(
          JSON.stringify({
            success: true,
            data: result.data,
          }),
          {
            status: 200,
            headers: {
              'Content-Type': 'application/json',
            },
          }
        );
      }
      return new Response(
        JSON.stringify({
          success: false,
          error: result.error || 'Erro desconhecido ao atualizar status do conteúdo.',
        }),
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }
    // Executar caso de uso de atualização de conteúdo
    const result = await updateContentUseCase.execute({
      id,
      title: body.title,
      body: body.body,
      meta: body.meta,
      seo: body.seo,
      categoryIds: body.categoryIds,
      tagIds: body.tagIds,
      slug: body.slug,
      createVersion: body.createVersion,
      versionComment: body.versionComment,
      userId: auth.userId,
    });

    if (result.success && result.data) {
      return new Response(
        JSON.stringify({
          success: true,
          data: result.data,
          version: result.version,
        }),
        {
          status: 200,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }
    return new Response(
      JSON.stringify({
        success: false,
        error: result.error || 'Erro desconhecido ao atualizar conteúdo.',
      }),
      {
        status: 400,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  } catch (error) {
    console.error('Erro ao processar atualização de conteúdo:', error);

    return new Response(
      JSON.stringify({
        success: false,
        error:
          'Ocorreu um erro ao processar a atualização do conteúdo. Por favor, tente novamente.',
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  }
};

export const DELETE: APIRoute = async ({ params, request }) => {
  try {
    const id = params.id;

    if (!id) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'ID do conteúdo é obrigatório.',
        }),
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    // Verificar autenticação
    const auth = checkAuth(request);

    if (!auth) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Não autorizado. Faça login para continuar.',
        }),
        {
          status: 401,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    if (!auth.canDelete) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Você não tem permissão para excluir conteúdo.',
        }),
        {
          status: 403,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    // Verificar se o conteúdo existe
    const content = await contentRepository.getById(id);

    if (!content) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Conteúdo não encontrado.',
        }),
        {
          status: 404,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    // Excluir conteúdo
    const success = await contentRepository.delete(id);

    if (success) {
      return new Response(
        JSON.stringify({
          success: true,
          message: 'Conteúdo excluído com sucesso.',
        }),
        {
          status: 200,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }
    return new Response(
      JSON.stringify({
        success: false,
        error: 'Erro ao excluir conteúdo.',
      }),
      {
        status: 400,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  } catch (error) {
    console.error('Erro ao processar exclusão de conteúdo:', error);

    return new Response(
      JSON.stringify({
        success: false,
        error: 'Ocorreu um erro ao processar a exclusão do conteúdo. Por favor, tente novamente.',
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  }
};
