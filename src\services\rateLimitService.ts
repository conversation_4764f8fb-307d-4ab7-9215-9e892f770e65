/**
 * Serviço de controle de taxa de requisições (Rate Limiting)
 *
 * Este serviço implementa controle de taxa de requisições para proteger
 * a API contra abusos, ataques de força bruta e sobrecarga.
 */

import { cacheService } from '@services/cacheService';
import { logger } from '@utils/logger';

/**
 * Interface para configuração de limite de taxa
 */
export interface RateLimitConfig {
  /**
   * Número máximo de requisições permitidas no período
   */
  maxRequests: number;

  /**
   * Período de tempo em segundos
   */
  windowSizeInSeconds: number;

  /**
   * Tempo de bloqueio em segundos quando o limite é excedido
   * @default 0 (sem bloqueio)
   */
  blockDurationInSeconds?: number;
}

/**
 * Interface para resultado de verificação de limite
 */
export interface RateLimitResult {
  /**
   * Se o limite foi excedido
   */
  limited: boolean;

  /**
   * Número de requisições restantes no período atual
   */
  remaining: number;

  /**
   * Tempo em segundos até o reset do limite
   */
  resetInSeconds: number;

  /**
   * Tempo em segundos até o fim do bloqueio (se aplicável)
   */
  blockExpiresInSeconds?: number;
}

/**
 * Tipos de recursos que podem ser limitados
 */
export enum RateLimitType {
  /**
   * Limite global por IP
   */
  GLOBAL = 'global',

  /**
   * Limite para tentativas de login
   */
  LOGIN = 'login',

  /**
   * Limite para criação de conta
   */
  SIGNUP = 'signup',

  /**
   * Limite para envio de formulário de contato
   */
  CONTACT = 'contact',

  /**
   * Limite para API pública
   */
  API = 'api',

  /**
   * Limite para upload de arquivos
   */
  UPLOAD = 'upload',

  /**
   * Limite para requisições de pagamento
   */
  PAYMENT = 'payment',
}

/**
 * Configurações padrão por tipo de limite
 */
const DEFAULT_CONFIGS: Record<RateLimitType, RateLimitConfig> = {
  [RateLimitType.GLOBAL]: {
    maxRequests: 1000,
    windowSizeInSeconds: 60 * 60, // 1 hora
    blockDurationInSeconds: 0, // Sem bloqueio
  },
  [RateLimitType.LOGIN]: {
    maxRequests: 5,
    windowSizeInSeconds: 60 * 5, // 5 minutos
    blockDurationInSeconds: 60 * 15, // 15 minutos de bloqueio
  },
  [RateLimitType.SIGNUP]: {
    maxRequests: 3,
    windowSizeInSeconds: 60 * 60, // 1 hora
    blockDurationInSeconds: 60 * 60 * 24, // 24 horas de bloqueio
  },
  [RateLimitType.CONTACT]: {
    maxRequests: 5,
    windowSizeInSeconds: 60 * 60, // 1 hora
    blockDurationInSeconds: 60 * 60 * 3, // 3 horas de bloqueio
  },
  [RateLimitType.API]: {
    maxRequests: 100,
    windowSizeInSeconds: 60, // 1 minuto
    blockDurationInSeconds: 60 * 5, // 5 minutos de bloqueio
  },
  [RateLimitType.UPLOAD]: {
    maxRequests: 10,
    windowSizeInSeconds: 60 * 10, // 10 minutos
    blockDurationInSeconds: 60 * 30, // 30 minutos de bloqueio
  },
  [RateLimitType.PAYMENT]: {
    maxRequests: 10,
    windowSizeInSeconds: 60 * 10, // 10 minutos
    blockDurationInSeconds: 0, // Sem bloqueio
  },
};

/**
 * Serviço de controle de taxa de requisições
 */
export const rateLimitService = {
  /**
   * Prefixo para chaves de cache
   */
  CACHE_PREFIX: 'ratelimit:',

  /**
   * Prefixo para chaves de bloqueio
   */
  BLOCK_PREFIX: 'ratelimit:block:',

  /**
   * Verifica se uma requisição deve ser limitada
   * @param identifier - Identificador único (IP, usuário, etc)
   * @param type - Tipo de limite
   * @param config - Configuração personalizada (opcional)
   * @returns Resultado da verificação
   */
  async check(
    identifier: string,
    type: RateLimitType,
    config?: Partial<RateLimitConfig>
  ): Promise<RateLimitResult> {
    try {
      // Obter configuração completa
      const fullConfig = {
        ...DEFAULT_CONFIGS[type],
        ...config,
      };

      // Verificar se o identificador está bloqueado
      const isBlocked = await this.isBlocked(identifier, type);

      if (isBlocked.blocked) {
        return {
          limited: true,
          remaining: 0,
          resetInSeconds: 0,
          blockExpiresInSeconds: isBlocked.expiresInSeconds,
        };
      }

      // Gerar chave de cache
      const cacheKey = this.getCacheKey(identifier, type);

      // Obter contagem atual e timestamp
      const data = await this.getRateLimitData(cacheKey);

      // Calcular tempo decorrido desde o início da janela
      const now = Math.floor(Date.now() / 1000);
      const windowStartTime = data.timestamp || now;
      const elapsedSeconds = now - windowStartTime;

      // Verificar se a janela de tempo expirou
      if (elapsedSeconds >= fullConfig.windowSizeInSeconds) {
        // Reiniciar contagem
        await this.resetCounter(cacheKey, now);

        return {
          limited: false,
          remaining: fullConfig.maxRequests - 1,
          resetInSeconds: fullConfig.windowSizeInSeconds,
        };
      }

      // Incrementar contador
      const count = data.count + 1;

      // Verificar se excedeu o limite
      const limited = count > fullConfig.maxRequests;

      // Atualizar contador no cache
      await this.updateCounter(cacheKey, count, windowStartTime, fullConfig.windowSizeInSeconds);

      // Se excedeu o limite e tem duração de bloqueio, bloquear o identificador
      if (limited && fullConfig.blockDurationInSeconds && fullConfig.blockDurationInSeconds > 0) {
        await this.blockIdentifier(identifier, type, fullConfig.blockDurationInSeconds);
      }

      // Calcular tempo restante para reset
      const resetInSeconds = fullConfig.windowSizeInSeconds - elapsedSeconds;

      return {
        limited,
        remaining: limited ? 0 : fullConfig.maxRequests - count,
        resetInSeconds,
        blockExpiresInSeconds:
          limited && fullConfig.blockDurationInSeconds
            ? fullConfig.blockDurationInSeconds
            : undefined,
      };
    } catch (error) {
      logger.error('Erro ao verificar limite de taxa:', error);

      // Em caso de erro, permitir a requisição
      return {
        limited: false,
        remaining: 1,
        resetInSeconds: 60,
      };
    }
  },

  /**
   * Verifica se um identificador está bloqueado
   * @param identifier - Identificador único
   * @param type - Tipo de limite
   * @returns Objeto com status de bloqueio e tempo restante
   */
  async isBlocked(
    identifier: string,
    type: RateLimitType
  ): Promise<{ blocked: boolean; expiresInSeconds: number }> {
    try {
      // Gerar chave de bloqueio
      const blockKey = this.getBlockKey(identifier, type);

      // Verificar se existe no cache
      const blockData = await cacheService.getItem(blockKey);

      if (!blockData) {
        return { blocked: false, expiresInSeconds: 0 };
      }

      // Obter tempo de expiração
      const ttl = await cacheService.ttl(blockKey);

      return {
        blocked: true,
        expiresInSeconds: ttl > 0 ? ttl : 0,
      };
    } catch (error) {
      logger.error('Erro ao verificar bloqueio:', error);
      return { blocked: false, expiresInSeconds: 0 };
    }
  },

  /**
   * Bloqueia um identificador por um período de tempo
   * @param identifier - Identificador único
   * @param type - Tipo de limite
   * @param durationInSeconds - Duração do bloqueio em segundos
   * @returns Verdadeiro se o bloqueio foi aplicado com sucesso
   */
  async blockIdentifier(
    identifier: string,
    type: RateLimitType,
    durationInSeconds: number
  ): Promise<boolean> {
    try {
      // Gerar chave de bloqueio
      const blockKey = this.getBlockKey(identifier, type);

      // Armazenar no cache com TTL
      const success = await cacheService.setItem(
        blockKey,
        JSON.stringify({
          identifier,
          type,
          blockedAt: Date.now(),
        }),
        durationInSeconds
      );

      return success;
    } catch (error) {
      logger.error('Erro ao bloquear identificador:', error);
      return false;
    }
  },

  /**
   * Remove o bloqueio de um identificador
   * @param identifier - Identificador único
   * @param type - Tipo de limite
   * @returns Verdadeiro se o bloqueio foi removido com sucesso
   */
  async unblockIdentifier(identifier: string, type: RateLimitType): Promise<boolean> {
    try {
      // Gerar chave de bloqueio
      const blockKey = this.getBlockKey(identifier, type);

      // Remover do cache
      const success = await cacheService.delItem(blockKey);

      return success;
    } catch (error) {
      logger.error('Erro ao desbloquear identificador:', error);
      return false;
    }
  },

  /**
   * Obtém dados de limite de taxa do cache
   * @param cacheKey - Chave de cache
   * @returns Dados de limite de taxa
   */
  async getRateLimitData(cacheKey: string): Promise<{ count: number; timestamp: number }> {
    try {
      // Buscar do cache
      const data = await cacheService.getItem(cacheKey);

      if (!data) {
        return { count: 0, timestamp: 0 };
      }

      // Parsear dados
      return JSON.parse(data);
    } catch (error) {
      logger.error('Erro ao obter dados de limite de taxa:', error);
      return { count: 0, timestamp: 0 };
    }
  },

  /**
   * Atualiza contador no cache
   * @param cacheKey - Chave de cache
   * @param count - Nova contagem
   * @param timestamp - Timestamp de início da janela
   * @param windowSizeInSeconds - Tamanho da janela em segundos
   * @returns Verdadeiro se a atualização foi bem-sucedida
   */
  async updateCounter(
    cacheKey: string,
    count: number,
    timestamp: number,
    windowSizeInSeconds: number
  ): Promise<boolean> {
    try {
      // Armazenar no cache
      const success = await cacheService.setItem(
        cacheKey,
        JSON.stringify({ count, timestamp }),
        windowSizeInSeconds
      );

      return success;
    } catch (error) {
      logger.error('Erro ao atualizar contador:', error);
      return false;
    }
  },

  /**
   * Reinicia contador no cache
   * @param cacheKey - Chave de cache
   * @param timestamp - Novo timestamp
   * @returns Verdadeiro se o reset foi bem-sucedido
   */
  async resetCounter(cacheKey: string, timestamp: number): Promise<boolean> {
    try {
      // Armazenar no cache
      const success = await cacheService.setItem(
        cacheKey,
        JSON.stringify({ count: 1, timestamp }),
        DEFAULT_CONFIGS[RateLimitType.GLOBAL].windowSizeInSeconds
      );

      return success;
    } catch (error) {
      logger.error('Erro ao reiniciar contador:', error);
      return false;
    }
  },

  /**
   * Gera chave de cache para limite de taxa
   * @param identifier - Identificador único
   * @param type - Tipo de limite
   * @returns Chave de cache
   */
  getCacheKey(identifier: string, type: RateLimitType): string {
    return `${this.CACHE_PREFIX}${type}:${identifier}`;
  },

  /**
   * Gera chave de cache para bloqueio
   * @param identifier - Identificador único
   * @param type - Tipo de limite
   * @returns Chave de bloqueio
   */
  getBlockKey(identifier: string, type: RateLimitType): string {
    return `${this.BLOCK_PREFIX}${type}:${identifier}`;
  },

  /**
   * Obtém todos os bloqueios ativos
   * @returns Lista de chaves de bloqueio
   */
  async getActiveBlocks(): Promise<string[]> {
    try {
      // Buscar todas as chaves de bloqueio
      const blockKeys = await cacheService.keys(`${this.BLOCK_PREFIX}*`);

      return blockKeys || [];
    } catch (error) {
      logger.error('Erro ao obter bloqueios ativos:', error);
      return [];
    }
  },

  /**
   * Obtém a configuração para um tipo de limite
   * @param type - Tipo de limite
   * @returns Configuração do limite
   */
  getConfig(type: RateLimitType): RateLimitConfig {
    return DEFAULT_CONFIGS[type];
  },
};
