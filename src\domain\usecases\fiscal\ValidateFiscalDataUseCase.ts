/**
 * Validate Fiscal Data Use Case
 *
 * Caso de uso para validação de dados fiscais.
 * Parte da implementação da tarefa 8.7.1 - Integração fiscal
 */

import { FiscalCustomer, FiscalItem } from '../../entities/FiscalDocument';

export interface ValidateFiscalDataRequest {
  documentType: 'NFE' | 'NFSE' | 'NFCE';
  customer?: FiscalCustomer;
  items?: FiscalItem[];
}

export interface ValidationResult {
  valid: boolean;
  field?: string;
  message?: string;
}

export interface ValidateFiscalDataResponse {
  success: boolean;
  isValid: boolean;
  validations: ValidationResult[];
  error?: string;
}

export class ValidateFiscalDataUseCase {
  async execute(request: ValidateFiscalDataRequest): Promise<ValidateFiscalDataResponse> {
    try {
      // Validar os dados de entrada
      if (!request.documentType) {
        return {
          success: false,
          isValid: false,
          validations: [],
          error: 'Tipo de documento é obrigatório.',
        };
      }

      const validations: ValidationResult[] = [];

      // Validar tipo de documento
      if (!['NFE', 'NFSE', 'NFCE'].includes(request.documentType)) {
        validations.push({
          valid: false,
          field: 'documentType',
          message: `Tipo de documento inválido: ${request.documentType}`,
        });
      } else {
        validations.push({
          valid: true,
          field: 'documentType',
        });
      }

      // Validar cliente
      if (request.customer) {
        validations.push(...this.validateCustomer(request.customer));
      }

      // Validar itens
      if (request.items && request.items.length > 0) {
        request.items.forEach((item, index) => {
          validations.push(...this.validateItem(item, index));
        });
      } else {
        validations.push({
          valid: false,
          field: 'items',
          message: 'Pelo menos um item é obrigatório',
        });
      }

      // Verificar se todos os campos são válidos
      const isValid = validations.every((v) => v.valid);

      return {
        success: true,
        isValid,
        validations,
      };
    } catch (error) {
      console.error('Erro ao validar dados fiscais:', error);

      return {
        success: false,
        isValid: false,
        validations: [],
        error:
          error instanceof Error ? error.message : 'Erro desconhecido ao validar dados fiscais.',
      };
    }
  }

  /**
   * Valida os dados do cliente
   */
  private validateCustomer(customer: FiscalCustomer): ValidationResult[] {
    const validations: ValidationResult[] = [];

    // Validar tipo de documento
    if (!['CPF', 'CNPJ'].includes(customer.documentType)) {
      validations.push({
        valid: false,
        field: 'customer.documentType',
        message: `Tipo de documento inválido: ${customer.documentType}`,
      });
    } else {
      validations.push({
        valid: true,
        field: 'customer.documentType',
      });
    }

    // Validar número do documento
    if (!customer.documentNumber) {
      validations.push({
        valid: false,
        field: 'customer.documentNumber',
        message: 'Número do documento é obrigatório',
      });
    } else {
      const documentNumber = customer.documentNumber.replace(/[^\d]/g, '');

      if (customer.documentType === 'CPF' && documentNumber.length !== 11) {
        validations.push({
          valid: false,
          field: 'customer.documentNumber',
          message: 'CPF deve ter 11 dígitos',
        });
      } else if (customer.documentType === 'CNPJ' && documentNumber.length !== 14) {
        validations.push({
          valid: false,
          field: 'customer.documentNumber',
          message: 'CNPJ deve ter 14 dígitos',
        });
      } else {
        validations.push({
          valid: true,
          field: 'customer.documentNumber',
        });
      }
    }

    // Validar nome
    if (!customer.name || customer.name.trim().length < 3) {
      validations.push({
        valid: false,
        field: 'customer.name',
        message: 'Nome deve ter pelo menos 3 caracteres',
      });
    } else {
      validations.push({
        valid: true,
        field: 'customer.name',
      });
    }

    // Validar endereço
    const address = customer.address;

    if (!address) {
      validations.push({
        valid: false,
        field: 'customer.address',
        message: 'Endereço é obrigatório',
      });
    } else {
      // Validar campos obrigatórios do endereço
      const requiredFields = [
        { field: 'street', name: 'Logradouro' },
        { field: 'number', name: 'Número' },
        { field: 'district', name: 'Bairro' },
        { field: 'city', name: 'Cidade' },
        { field: 'state', name: 'Estado' },
        { field: 'zipCode', name: 'CEP' },
      ];

      for (const { field, name } of requiredFields) {
        const value = address[field as keyof FiscalAddress];

        if (!value || (typeof value === 'string' && value.trim().length === 0)) {
          validations.push({
            valid: false,
            field: `customer.address.${field}`,
            message: `${name} é obrigatório`,
          });
        } else {
          validations.push({
            valid: true,
            field: `customer.address.${field}`,
          });
        }
      }

      // Validar CEP
      if (address.zipCode) {
        const zipCode = address.zipCode.replace(/[^\d]/g, '');

        if (zipCode.length !== 8) {
          validations.push({
            valid: false,
            field: 'customer.address.zipCode',
            message: 'CEP deve ter 8 dígitos',
          });
        }
      }

      // Validar UF
      if (address.state) {
        const validStates = [
          'AC',
          'AL',
          'AP',
          'AM',
          'BA',
          'CE',
          'DF',
          'ES',
          'GO',
          'MA',
          'MT',
          'MS',
          'MG',
          'PA',
          'PB',
          'PR',
          'PE',
          'PI',
          'RJ',
          'RN',
          'RS',
          'RO',
          'RR',
          'SC',
          'SP',
          'SE',
          'TO',
        ];

        if (!validStates.includes(address.state.toUpperCase())) {
          validations.push({
            valid: false,
            field: 'customer.address.state',
            message: 'Estado inválido',
          });
        }
      }
    }

    return validations;
  }

  /**
   * Valida os dados de um item
   */
  private validateItem(item: FiscalItem, index: number): ValidationResult[] {
    const validations: ValidationResult[] = [];
    const prefix = `items[${index}]`;

    // Validar código do produto
    if (!item.productCode) {
      validations.push({
        valid: false,
        field: `${prefix}.productCode`,
        message: 'Código do produto é obrigatório',
      });
    } else {
      validations.push({
        valid: true,
        field: `${prefix}.productCode`,
      });
    }

    // Validar descrição
    if (!item.description || item.description.trim().length < 3) {
      validations.push({
        valid: false,
        field: `${prefix}.description`,
        message: 'Descrição deve ter pelo menos 3 caracteres',
      });
    } else {
      validations.push({
        valid: true,
        field: `${prefix}.description`,
      });
    }

    // Validar quantidade
    if (!item.quantity || item.quantity <= 0) {
      validations.push({
        valid: false,
        field: `${prefix}.quantity`,
        message: 'Quantidade deve ser maior que zero',
      });
    } else {
      validations.push({
        valid: true,
        field: `${prefix}.quantity`,
      });
    }

    // Validar valor unitário
    if (!item.unitValue || item.unitValue <= 0) {
      validations.push({
        valid: false,
        field: `${prefix}.unitValue`,
        message: 'Valor unitário deve ser maior que zero',
      });
    } else {
      validations.push({
        valid: true,
        field: `${prefix}.unitValue`,
      });
    }

    // Validar valor total
    if (!item.totalValue || item.totalValue <= 0) {
      validations.push({
        valid: false,
        field: `${prefix}.totalValue`,
        message: 'Valor total deve ser maior que zero',
      });
    } else {
      // Verificar se o valor total é consistente com quantidade e valor unitário
      const calculatedTotal = item.quantity * item.unitValue;
      const tolerance = 0.01; // Tolerância para arredondamentos

      if (Math.abs(calculatedTotal - item.totalValue) > tolerance) {
        validations.push({
          valid: false,
          field: `${prefix}.totalValue`,
          message: `Valor total inconsistente: esperado ${calculatedTotal.toFixed(2)}, encontrado ${item.totalValue.toFixed(2)}`,
        });
      } else {
        validations.push({
          valid: true,
          field: `${prefix}.totalValue`,
        });
      }
    }

    // Validar código de imposto
    if (!item.taxCode) {
      validations.push({
        valid: false,
        field: `${prefix}.taxCode`,
        message: 'Código de imposto é obrigatório',
      });
    } else {
      validations.push({
        valid: true,
        field: `${prefix}.taxCode`,
      });
    }

    return validations;
  }
}
