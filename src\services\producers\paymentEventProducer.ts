/**
 * Produtor de eventos de pagamento
 *
 * Este serviço é responsável por produzir eventos relacionados a pagamentos
 * e reembolsos para o Kafka.
 */

import { PaymentEvent, eventProducerService } from '@services/eventProducerService';
import { PaymentStatus } from '@services/paymentService';
import { RefundStatus } from '@services/refundService';
import { logger } from '@utils/logger';

/**
 * Interface para dados de pagamento
 */
interface PaymentData {
  paymentId: string;
  orderId?: string;
  userId?: string;
  value: number;
  paymentType: string;
  status: PaymentStatus;
  externalId?: string;
  metadata?: Record<string, unknown>;
}

/**
 * Interface para dados de reembolso
 */
interface RefundData {
  refundId: string;
  paymentId: string;
  value: number;
  reason?: string;
  status: RefundStatus;
  metadata?: Record<string, unknown>;
}

/**
 * Produtor de eventos de pagamento
 */
export const paymentEventProducer = {
  /**
   * Envia evento de criação de pagamento
   * @param paymentData - Dados do pagamento
   */
  async paymentCreated(paymentData: PaymentData): Promise<void> {
    try {
      const event: PaymentEvent = {
        ...eventProducerService.createBaseEvent(
          `payment-created-${paymentData.paymentId}`,
          paymentData.metadata || {}
        ),
        paymentId: paymentData.paymentId,
        orderId: paymentData.orderId,
        userId: paymentData.userId,
        value: paymentData.value,
        paymentType: paymentData.paymentType,
        status: paymentData.status,
        externalId: paymentData.externalId,
      };

      await eventProducerService.sendPaymentEvent('created', event);

      logger.info(`Evento de criação de pagamento enviado: ${paymentData.paymentId}`);
    } catch (error) {
      logger.error(
        `Erro ao enviar evento de criação de pagamento ${paymentData.paymentId}:`,
        error
      );
      // Não propagar erro para não interromper o fluxo principal
    }
  },

  /**
   * Envia evento de atualização de pagamento
   * @param paymentData - Dados do pagamento
   */
  async paymentUpdated(paymentData: PaymentData): Promise<void> {
    try {
      const event: PaymentEvent = {
        ...eventProducerService.createBaseEvent(
          `payment-updated-${paymentData.paymentId}`,
          paymentData.metadata || {}
        ),
        paymentId: paymentData.paymentId,
        orderId: paymentData.orderId,
        userId: paymentData.userId,
        value: paymentData.value,
        paymentType: paymentData.paymentType,
        status: paymentData.status,
        externalId: paymentData.externalId,
      };

      await eventProducerService.sendPaymentEvent('updated', event);

      logger.info(`Evento de atualização de pagamento enviado: ${paymentData.paymentId}`);
    } catch (error) {
      logger.error(
        `Erro ao enviar evento de atualização de pagamento ${paymentData.paymentId}:`,
        error
      );
      // Não propagar erro para não interromper o fluxo principal
    }
  },

  /**
   * Envia evento de falha de pagamento
   * @param paymentData - Dados do pagamento
   * @param errorDetails - Detalhes do erro
   */
  async paymentFailed(
    paymentData: PaymentData,
    errorDetails: { code: string; message: string }
  ): Promise<void> {
    try {
      const event: PaymentEvent = {
        ...eventProducerService.createBaseEvent(`payment-failed-${paymentData.paymentId}`, {
          ...(paymentData.metadata || {}),
          error: errorDetails,
        }),
        paymentId: paymentData.paymentId,
        orderId: paymentData.orderId,
        userId: paymentData.userId,
        value: paymentData.value,
        paymentType: paymentData.paymentType,
        status: PaymentStatus.FAILED,
        externalId: paymentData.externalId,
      };

      await eventProducerService.sendPaymentEvent('failed', event);

      logger.info(`Evento de falha de pagamento enviado: ${paymentData.paymentId}`);
    } catch (error) {
      logger.error(`Erro ao enviar evento de falha de pagamento ${paymentData.paymentId}:`, error);
      // Não propagar erro para não interromper o fluxo principal
    }
  },

  /**
   * Envia evento de solicitação de reembolso
   * @param refundData - Dados do reembolso
   * @param paymentData - Dados do pagamento associado
   */
  async refundRequested(refundData: RefundData, paymentData: PaymentData): Promise<void> {
    try {
      const event: PaymentEvent & { refundId: string } = {
        ...eventProducerService.createBaseEvent(`refund-requested-${refundData.refundId}`, {
          ...(refundData.metadata || {}),
          reason: refundData.reason,
        }),
        refundId: refundData.refundId,
        paymentId: paymentData.paymentId,
        orderId: paymentData.orderId,
        userId: paymentData.userId,
        value: refundData.value,
        paymentType: paymentData.paymentType,
        status: refundData.status,
        externalId: paymentData.externalId,
      };

      await eventProducerService.sendRefundEvent('requested', event);

      logger.info(`Evento de solicitação de reembolso enviado: ${refundData.refundId}`);
    } catch (error) {
      logger.error(
        `Erro ao enviar evento de solicitação de reembolso ${refundData.refundId}:`,
        error
      );
      // Não propagar erro para não interromper o fluxo principal
    }
  },

  /**
   * Envia evento de processamento de reembolso
   * @param refundData - Dados do reembolso
   * @param paymentData - Dados do pagamento associado
   */
  async refundProcessed(refundData: RefundData, paymentData: PaymentData): Promise<void> {
    try {
      const event: PaymentEvent & { refundId: string } = {
        ...eventProducerService.createBaseEvent(
          `refund-processed-${refundData.refundId}`,
          refundData.metadata || {}
        ),
        refundId: refundData.refundId,
        paymentId: paymentData.paymentId,
        orderId: paymentData.orderId,
        userId: paymentData.userId,
        value: refundData.value,
        paymentType: paymentData.paymentType,
        status: refundData.status,
        externalId: paymentData.externalId,
      };

      await eventProducerService.sendRefundEvent('processed', event);

      logger.info(`Evento de processamento de reembolso enviado: ${refundData.refundId}`);
    } catch (error) {
      logger.error(
        `Erro ao enviar evento de processamento de reembolso ${refundData.refundId}:`,
        error
      );
      // Não propagar erro para não interromper o fluxo principal
    }
  },

  /**
   * Envia evento de recebimento de webhook de pagamento
   * @param paymentId - ID do pagamento
   * @param webhookData - Dados do webhook
   */
  async webhookReceived(paymentId: string, webhookData: Record<string, unknown>): Promise<void> {
    try {
      const event = {
        ...eventProducerService.createBaseEvent(`webhook-received-${paymentId}-${Date.now()}`, {}),
        paymentId,
        webhookData,
      };

      await eventProducerService.sendDomainEvent(
        'payment',
        'webhook',
        'received',
        event,
        paymentId
      );

      logger.info(`Evento de webhook de pagamento enviado: ${paymentId}`);
    } catch (error) {
      logger.error(`Erro ao enviar evento de webhook de pagamento ${paymentId}:`, error);
      // Não propagar erro para não interromper o fluxo principal
    }
  },
};
