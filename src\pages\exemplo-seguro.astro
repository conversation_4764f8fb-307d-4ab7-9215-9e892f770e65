---
import { secureFormAction } from '@actions/secureFormAction';
import FormBase from '@components/form/FormBase.astro';
import Layout from '@layouts/Layout.astro';

// Obter resultado da action (se houver)
const result = Astro.getActionResult();
---

<Layout title="Formulário Seguro com CSRF">
  <div class="container mx-auto p-4">
    <h1 class="text-2xl font-bold mb-4">Formulário Seguro com CSRF</h1>
    
    {result && result.success && (
      <div class="alert alert-success mb-4">
        <span>{result.data?.message}</span>
      </div>
    )}
    
    <FormBase action="/exemplo-seguro" csrfProtection={true}>
      <div class="form-control">
        <label class="label" for="name">
          <span class="label-text">Nome</span>
        </label>
        <input
          type="text"
          id="name"
          name="name"
          class="input input-bordered"
          required
        />
      </div>
      
      <div class="form-control">
        <label class="label" for="email">
          <span class="label-text">Email</span>
        </label>
        <input
          type="email"
          id="email"
          name="email"
          class="input input-bordered"
          required
        />
      </div>
      
      <button type="submit" class="btn btn-primary mt-4">Enviar</button>
    </FormBase>
  </div>
</Layout>
