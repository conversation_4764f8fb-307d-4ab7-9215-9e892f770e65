/**
 * API para resolver alertas de segurança
 */

import { AuditEventType, AuditSeverity, auditService } from '@services/auditService';
import { getCurrentUser } from '@utils/authUtils';
import { logger } from '@utils/logger';
import type { APIRoute } from 'astro';

export const POST: APIRoute = async ({ request, cookies }) => {
  try {
    // Verificar autenticação
    const user = await getCurrentUser(cookies);

    if (!user) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Não autorizado',
        }),
        {
          status: 401,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    // Obter dados da requisição
    const formData = await request.formData();
    const alertId = formData.get('alertId') as string;
    const resolutionComment = formData.get('resolutionComment') as string;

    // Validar dados
    if (!alertId) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'ID do alerta não fornecido',
        }),
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    // Verificar se o alerta existe
    const query = `
      SELECT * FROM tab_security_alerts
      WHERE ulid_security_alert = $1
    `;

    const result = await pgHelper.query(query, [alertId]);

    if (result.rowCount === 0) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Alerta não encontrado',
        }),
        {
          status: 404,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    const alert = result.rows[0];

    // Verificar se o alerta já está resolvido
    if (alert.is_resolved) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Alerta já está resolvido',
        }),
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    // Resolver alerta
    const updateQuery = `
      UPDATE tab_security_alerts
      SET 
        is_resolved = TRUE,
        resolved_by = $1,
        resolved_at = NOW(),
        resolution_comment = $2
      WHERE ulid_security_alert = $3
      RETURNING *
    `;

    const updateResult = await pgHelper.query(updateQuery, [
      user.ulid_user,
      resolutionComment,
      alertId,
    ]);

    // Registrar ação no log de auditoria
    await auditService.logEvent({
      eventType: AuditEventType.SYSTEM_CONFIG_CHANGED,
      userId: user.ulid_user,
      userName: user.name,
      ipAddress:
        request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',
      userAgent: request.headers.get('user-agent') || 'unknown',
      resource: 'security',
      resourceId: alertId,
      action: 'resolve',
      result: 'success',
      severity: AuditSeverity.INFO,
      metadata: {
        alertType: alert.alert_type,
        alertTitle: alert.title,
        resolutionComment,
      },
    });

    return new Response(
      JSON.stringify({
        success: true,
        message: 'Alerta resolvido com sucesso',
        alert: updateResult.rows[0],
      }),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  } catch (error) {
    logger.error('Erro ao resolver alerta de segurança:', error);

    return new Response(
      JSON.stringify({
        success: false,
        error: 'Erro ao processar requisição',
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  }
};
