/**
 * Configuração de estratégias de invalidação de cache
 *
 * Este arquivo define as estratégias de invalidação para diferentes tipos de dados
 * armazenados no cache Valkey.
 */

/**
 * Tipos de estratégias de invalidação
 */
export enum InvalidationStrategy {
  /**
   * Invalidação baseada em tempo (TTL) - o item expira automaticamente após um tempo
   */
  TIME_BASED = 'time-based',

  /**
   * Invalidação baseada em eventos - o item é invalidado quando ocorrem eventos específicos
   */
  EVENT_BASED = 'event-based',

  /**
   * Invalidação baseada em versão - o item é invalidado quando a versão muda
   */
  VERSION_BASED = 'version-based',

  /**
   * Invalidação baseada em hash - o item é invalidado quando o hash do conteúdo muda
   */
  HASH_BASED = 'hash-based',

  /**
   * Invalidação baseada em dependências - o item é invalidado quando suas dependências mudam
   */
  DEPENDENCY_BASED = 'dependency-based',

  /**
   * Invalidação manual - o item só é invalidado manualmente
   */
  MANUAL = 'manual',
}

/**
 * Interface para configuração de invalidação
 */
export interface InvalidationConfig {
  /**
   * Estratégia de invalidação
   */
  strategy: InvalidationStrategy;

  /**
   * Eventos que invalidam o cache (para EVENT_BASED)
   */
  events?: string[];

  /**
   * Padrão de chave para invalidação em massa
   */
  keyPattern?: string;

  /**
   * Dependências para invalidação (para DEPENDENCY_BASED)
   */
  dependencies?: string[];

  /**
   * Descrição da estratégia
   */
  description: string;
}

/**
 * Configurações de invalidação por tipo de conteúdo
 */
export const invalidationStrategies: Record<string, InvalidationConfig> = {
  // Dados de usuário
  user: {
    strategy: InvalidationStrategy.EVENT_BASED,
    events: ['user:update', 'user:delete', 'user:preferences:update'],
    keyPattern: 'user:*',
    description: 'Invalidação de dados de usuário',
  },

  // Dados de produto
  product: {
    strategy: InvalidationStrategy.EVENT_BASED,
    events: ['product:update', 'product:delete', 'product:price:update', 'product:stock:update'],
    keyPattern: 'product:*',
    dependencies: ['category'],
    description: 'Invalidação de dados de produto',
  },

  // Dados de categoria
  category: {
    strategy: InvalidationStrategy.EVENT_BASED,
    events: ['category:update', 'category:delete', 'category:order:update'],
    keyPattern: 'category:*',
    description: 'Invalidação de dados de categoria',
  },

  // Dados de pedido
  order: {
    strategy: InvalidationStrategy.EVENT_BASED,
    events: ['order:update', 'order:status:update', 'order:payment:update'],
    keyPattern: 'order:*',
    dependencies: ['user', 'payment'],
    description: 'Invalidação de dados de pedido',
  },

  // Dados de pagamento
  payment: {
    strategy: InvalidationStrategy.EVENT_BASED,
    events: ['payment:update', 'payment:status:update'],
    keyPattern: 'payment:*',
    dependencies: ['order'],
    description: 'Invalidação de dados de pagamento',
  },

  // Dados de sessão
  session: {
    strategy: InvalidationStrategy.TIME_BASED,
    events: ['user:logout', 'user:password:update', 'session:revoke'],
    keyPattern: 'session:*',
    description: 'Invalidação de dados de sessão',
  },

  // Tokens de autenticação
  token: {
    strategy: InvalidationStrategy.TIME_BASED,
    events: ['user:logout', 'user:password:update', 'token:revoke'],
    keyPattern: 'token:*',
    description: 'Invalidação de tokens de autenticação',
  },

  // Resultados de busca
  search: {
    strategy: InvalidationStrategy.EVENT_BASED,
    events: ['product:update', 'product:delete', 'category:update', 'category:delete'],
    keyPattern: 'search:*',
    description: 'Invalidação de resultados de busca',
  },

  // Cache de página
  page: {
    strategy: InvalidationStrategy.DEPENDENCY_BASED,
    events: ['content:update', 'content:delete', 'settings:update'],
    keyPattern: 'page:*',
    dependencies: ['product', 'category', 'static'],
    description: 'Invalidação de cache de página',
  },

  // Conteúdo estático
  static: {
    strategy: InvalidationStrategy.VERSION_BASED,
    events: ['static:update', 'static:delete', 'deploy'],
    keyPattern: 'static:*',
    description: 'Invalidação de conteúdo estático',
  },

  // Dados de configuração
  config: {
    strategy: InvalidationStrategy.EVENT_BASED,
    events: ['config:update', 'settings:update'],
    keyPattern: 'config:*',
    description: 'Invalidação de dados de configuração',
  },

  // Dados de relatório
  report: {
    strategy: InvalidationStrategy.TIME_BASED,
    events: ['report:regenerate', 'data:update'],
    keyPattern: 'report:*',
    description: 'Invalidação de dados de relatório',
  },

  // Dados de métricas
  metrics: {
    strategy: InvalidationStrategy.TIME_BASED,
    keyPattern: 'metrics:*',
    description: 'Invalidação de dados de métricas',
  },
};

/**
 * Obtém a configuração de invalidação para um tipo de conteúdo
 * @param type Tipo de conteúdo
 * @returns Configuração de invalidação
 */
export function getInvalidationStrategy(type: string): InvalidationConfig {
  return (
    invalidationStrategies[type] || {
      strategy: InvalidationStrategy.TIME_BASED,
      description: 'Estratégia padrão baseada em tempo',
    }
  );
}

/**
 * Verifica se um evento deve invalidar um tipo de cache
 * @param type Tipo de conteúdo
 * @param event Nome do evento
 * @returns Verdadeiro se o evento deve invalidar o cache
 */
export function shouldInvalidateOnEvent(type: string, event: string): boolean {
  const strategy = getInvalidationStrategy(type);

  if (
    strategy.strategy !== InvalidationStrategy.EVENT_BASED &&
    strategy.strategy !== InvalidationStrategy.DEPENDENCY_BASED
  ) {
    return false;
  }

  return strategy.events?.includes(event) || false;
}

/**
 * Obtém o padrão de chave para invalidação em massa
 * @param type Tipo de conteúdo
 * @returns Padrão de chave ou null se não houver
 */
export function getInvalidationKeyPattern(type: string): string | null {
  const strategy = getInvalidationStrategy(type);
  return strategy.keyPattern || null;
}

/**
 * Obtém as dependências para invalidação em cascata
 * @param type Tipo de conteúdo
 * @returns Lista de tipos dependentes ou array vazio
 */
export function getInvalidationDependencies(type: string): string[] {
  const strategy = getInvalidationStrategy(type);
  return strategy.dependencies || [];
}

/**
 * Obtém todos os tipos que dependem de um tipo específico
 * @param type Tipo de conteúdo
 * @returns Lista de tipos que dependem do tipo especificado
 */
export function getDependentTypes(type: string): string[] {
  return Object.entries(invalidationStrategies)
    .filter(([_, config]) => config.dependencies?.includes(type))
    .map(([dependentType, _]) => dependentType);
}
