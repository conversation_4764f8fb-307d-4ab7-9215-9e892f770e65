/**
 * Exportações principais do módulo de repositórios
 *
 * Este arquivo exporta as implementações padrão dos repositórios
 * para uso em toda a aplicação.
 */

// Interfaces
export * from './interfaces/ProductRepository';
export * from './interfaces/UserRepository';
export * from './interfaces/EducationalContentRepository';

// Implementações PostgreSQL
import { PgProductRepository } from './implementations/postgres/PgProductRepository';

// Implementações com Cache
import { CachedProductRepository } from './implementations/cached/CachedProductRepository';

// Serviços necessários
import { cacheService } from '../infrastructure/cache/cacheService';

// Criar instâncias das implementações PostgreSQL
const pgProductRepository = new PgProductRepository();

// Criar instâncias das implementações com cache
const cachedProductRepository = new CachedProductRepository(
  pgProductRepository,
  cacheService,
  300 // 5 minutos de TTL
);

// Exportar implementações padrão para uso na aplicação
export const repositories = {
  // Usar implementações com cache por padrão quando disponíveis
  productRepository: cachedProductRepository,

  // Exportar implementações PostgreSQL para uso direto quando necessário
  postgres: {
    productRepository: pgProductRepository,
  },
};

// Exportar factory functions para criar instâncias personalizadas
export const createProductRepository = (useCache = true) => {
  return useCache ? cachedProductRepository : pgProductRepository;
};

// Exportar camada de compatibilidade para código legado
// Isso mantém a compatibilidade com o código existente que usa a estrutura antiga
// @deprecated Use as novas implementações acima
export * from './compatibility/productRepository';

// Aviso de compatibilidade
console.info(
  'Nota: A camada de compatibilidade em src/repositories/compatibility está disponível ' +
    'para manter a retrocompatibilidade com código existente. Novos códigos devem usar ' +
    'as interfaces e implementações da nova estrutura.'
);
