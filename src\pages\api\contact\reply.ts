/**
 * API de Resposta de Contato
 *
 * Endpoint para processamento de respostas a mensagens de contato.
 * Parte da implementação da tarefa 8.6.2 - Gest<PERSON> de mensagens
 */

import type { APIRoute } from 'astro';
import { getConfig } from '../../../config';
import { ContactMessageRepository } from '../../../domain/repositories/ContactMessageRepository';
import { EmailService } from '../../../domain/services/EmailService';
import { ReplyToContactMessageUseCase } from '../../../domain/usecases/contact/ReplyToContactMessageUseCase';
import { PostgresContactMessageRepository } from '../../../infrastructure/database/repositories/PostgresContactMessageRepository';
import { NodemailerEmailService } from '../../../infrastructure/services/NodemailerEmailService';

// Obter configurações
const config = getConfig();

// Inicializar serviços
const contactMessageRepository: ContactMessageRepository = new PostgresContactMessageRepository();
const emailService: EmailService = new NodemailerEmailService({
  host: config.email.host,
  port: config.email.port,
  secure: config.email.secure,
  auth: {
    user: config.email.user,
    pass: config.email.password,
  },
  defaultFrom: config.email.defaultFrom,
});

// Inicializar caso de uso
const replyToContactMessageUseCase = new ReplyToContactMessageUseCase(
  contactMessageRepository,
  emailService
);

export const POST: APIRoute = async ({ request, redirect }) => {
  try {
    // Verificar se a requisição é multipart/form-data
    const contentType = request.headers.get('content-type') || '';
    let formData: FormData;

    if (contentType.includes('multipart/form-data')) {
      formData = await request.formData();
    } else {
      const body = await request.text();
      const params = new URLSearchParams(body);
      formData = new FormData();

      for (const [key, value] of params.entries()) {
        formData.append(key, value);
      }
    }

    // Extrair dados do formulário
    const messageId = formData.get('messageId') as string;
    const content = formData.get('content') as string;
    const markAsReplied = formData.get('markAsReplied') === 'on';

    // Em um cenário real, obteríamos o ID e nome do usuário atual da sessão
    const userId = 'current-user-id';
    const userName = 'Usuário Atual';

    // Processar anexos, se houver
    const attachments: string[] = [];

    if (contentType.includes('multipart/form-data')) {
      const files = formData.getAll('attachments') as File[];

      for (const file of files) {
        if (file.size > 0) {
          // Em um cenário real, aqui seria feito o upload do arquivo
          // e o caminho seria armazenado em attachments
          console.log(`Processando anexo: ${file.name} (${file.size} bytes)`);

          // Simular caminho do arquivo
          attachments.push(`/uploads/${Date.now()}_${file.name}`);
        }
      }
    }

    // Validar dados básicos
    if (!messageId || !content) {
      return redirect(
        `/admin/mensagens/${messageId}?error=Todos os campos obrigatórios devem ser preenchidos.`,
        302
      );
    }

    // Enviar resposta
    const result = await replyToContactMessageUseCase.execute({
      messageId,
      userId,
      userName,
      content,
      attachments,
      markAsReplied,
    });

    if (result.success) {
      return redirect(`/admin/mensagens/${messageId}?success=true`, 302);
    }
    return redirect(
      `/admin/mensagens/${messageId}?error=${encodeURIComponent(result.error || 'Erro desconhecido')}`,
      302
    );
  } catch (error) {
    console.error('Erro ao processar resposta de mensagem de contato:', error);

    return redirect(
      '/admin/mensagens?error=Ocorreu um erro ao processar sua resposta. Por favor, tente novamente.',
      302
    );
  }
};
