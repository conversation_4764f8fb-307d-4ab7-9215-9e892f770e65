// src/actions/secureFormAction.ts
import { defineAction } from 'astro:actions';
import { validateCsrfToken } from '@middleware/csrfMiddleware';

export const secureFormAction = defineAction({
  async handler({ request, session, redirect }) {
    const formData = await request.formData();

    // Validar token CSRF
    const csrfValidation = await validateCsrfToken({ session }, formData);

    if (!csrfValidation.isValid) {
      return {
        success: false,
        error: {
          message: csrfValidation.error || 'Erro de validação CSRF',
        },
      };
    }

    // Processar dados do formulário
    const name = formData.get('name') as string;
    const email = formData.get('email') as string;

    // Validação básica
    if (!name || !email) {
      return {
        success: false,
        error: {
          message: 'Nome e email são obrigatórios',
        },
      };
    }

    try {
      // Lógica de processamento do formulário
      // ...

      return {
        success: true,
        data: {
          message: 'Formulário processado com sucesso!',
        },
      };
    } catch (error) {
      return {
        success: false,
        error: {
          message: error instanceof Error ? error.message : 'Erro ao processar formulário',
        },
      };
    }
  },
});
