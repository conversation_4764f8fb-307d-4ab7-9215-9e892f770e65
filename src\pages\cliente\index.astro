---
import FiscalDocumentNotification from '../../components/cliente/FiscalDocumentNotification.astro';
import FiscalDocumentsWidget from '../../components/cliente/FiscalDocumentsWidget.astro';
import { PageTransition } from '../../components/transitions';
import DaisyCard from '../../components/ui/DaisyCard.astro';
import Breadcrumbs from '../../components/ui/navigation/Breadcrumbs.astro';
/**
 * Página de Dashboard do Cliente
 *
 * Dashboard principal da área do cliente.
 * Inclui widget de documentos fiscais.
 */
import BaseLayout from '../../layouts/BaseLayout.astro';
import { Container, Section } from '../../layouts/grid';

// Título da página
const title = 'Dashboard do Cliente';

// Breadcrumbs para a página atual
const breadcrumbItems = [{ href: '/', label: '<PERSON><PERSON><PERSON>' }, { label: 'Área do Cliente' }];

// Em um cenário real, obteríamos o ID do cliente da sessão
// Para este exemplo, vamos usar um ID fixo
const customerId = '12345678901'; // CPF do cliente

// Dados do cliente (em um cenário real, viriam do banco de dados)
const customer = {
  id: customerId,
  name: 'João da Silva',
  email: '<EMAIL>',
  phone: '(11) 98765-4321',
  documentType: 'CPF',
  documentNumber: customerId,
  address: {
    street: 'Rua das Flores',
    number: '123',
    complement: 'Apto 45',
    district: 'Jardim Primavera',
    city: 'São Paulo',
    state: 'SP',
    zipCode: '01234-567',
    country: 'Brasil',
  },
};
---

<BaseLayout title={title} enableViewTransitions={true} transitionType="fade">
  <Container>
    <PageTransition type="fade" duration="0.5s">
      <Section>
        <Breadcrumbs items={breadcrumbItems} class="mb-6" />
        
        <div class="flex justify-between items-center mb-6">
          <h1 class="text-3xl font-bold">{title}</h1>
        </div>
        
        <FiscalDocumentNotification customerId={customerId} maxDays={7} />
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
          <DaisyCard class="md:col-span-2">
            <div class="p-6">
              <h2 class="text-xl font-bold mb-4">
                <i class="icon icon-user mr-2"></i>
                Bem-vindo, {customer.name}!
              </h2>
              
              <p class="mb-4">
                Este é o seu painel de controle, onde você pode gerenciar seus dados, acessar documentos fiscais e muito mais.
              </p>
              
              <div class="stats shadow">
                <div class="stat">
                  <div class="stat-figure text-primary">
                    <i class="icon icon-file-text text-3xl"></i>
                  </div>
                  <div class="stat-title">Documentos Fiscais</div>
                  <div class="stat-value">12</div>
                  <div class="stat-desc">Últimos 30 dias</div>
                </div>
                
                <div class="stat">
                  <div class="stat-figure text-secondary">
                    <i class="icon icon-shopping-cart text-3xl"></i>
                  </div>
                  <div class="stat-title">Compras</div>
                  <div class="stat-value">4</div>
                  <div class="stat-desc">Últimos 30 dias</div>
                </div>
              </div>
            </div>
          </DaisyCard>
          
          <DaisyCard>
            <div class="p-6">
              <h2 class="text-xl font-bold mb-4">
                <i class="icon icon-menu mr-2"></i>
                Menu Rápido
              </h2>
              
              <ul class="menu bg-base-200 rounded-box">
                <li>
                  <a href="/cliente/perfil">
                    <i class="icon icon-user"></i>
                    Meu Perfil
                  </a>
                </li>
                <li>
                  <a href="/cliente/pedidos">
                    <i class="icon icon-shopping-bag"></i>
                    Meus Pedidos
                  </a>
                </li>
                <li>
                  <a href="/cliente/documentos-fiscais">
                    <i class="icon icon-file-text"></i>
                    Documentos Fiscais
                  </a>
                </li>
                <li>
                  <a href="/cliente/suporte">
                    <i class="icon icon-help-circle"></i>
                    Suporte
                  </a>
                </li>
                <li>
                  <a href="/logout">
                    <i class="icon icon-log-out"></i>
                    Sair
                  </a>
                </li>
              </ul>
            </div>
          </DaisyCard>
        </div>
        
        <FiscalDocumentsWidget />
      </Section>
    </PageTransition>
  </Container>
</BaseLayout>
