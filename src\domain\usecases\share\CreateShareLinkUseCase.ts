/**
 * Create Share Link Use Case
 *
 * Caso de uso para criar um link de compartilhamento.
 * Parte da implementação da tarefa 8.4.2 - Compartilhamento
 */

import { generateShareCode } from '../../../utils/shareUtils';
import { ShareLink, ShareLinkTarget, ShareLinkType } from '../../entities/ShareLink';
import { ShareLinkRepository } from '../../repositories/ShareLinkRepository';

export interface CreateShareLinkRequest {
  userId: string;
  targetId: string;
  targetType: ShareLinkType;
  targetTitle?: string;
  targetSlug?: string;
  code?: string;
  utmSource?: string;
  utmMedium?: string;
  utmCampaign?: string;
  utmTerm?: string;
  utmContent?: string;
  expiresAt?: Date;
  maxClicks?: number;
}

export interface CreateShareLinkResponse {
  success: boolean;
  shareLink?: ShareLink;
  shareUrl?: string;
  error?: string;
}

export class CreateShareLinkUseCase {
  constructor(
    private shareLinkRepository: ShareLinkRepository,
    private baseUrl: string
  ) {}

  async execute(request: CreateShareLinkRequest): Promise<CreateShareLinkResponse> {
    try {
      // Validar os dados de entrada
      if (!this.validateRequest(request)) {
        return {
          success: false,
          error: 'Dados inválidos para criação de link de compartilhamento.',
        };
      }

      // Gerar código de compartilhamento se não fornecido
      let code = request.code?.trim();

      if (!code) {
        code = await this.generateUniqueCode();
      } else {
        // Verificar se o código já existe
        const codeExists = await this.shareLinkRepository.codeExists(code);

        if (codeExists) {
          return {
            success: false,
            error: 'Este código de compartilhamento já está em uso.',
          };
        }
      }

      // Criar o target
      const target: ShareLinkTarget = {
        id: request.targetId,
        type: request.targetType,
        title: request.targetTitle,
        slug: request.targetSlug,
      };

      // Criar o link de compartilhamento
      const shareLink = new ShareLink({
        id: crypto.randomUUID(),
        userId: request.userId,
        code,
        target,
        utmSource: request.utmSource,
        utmMedium: request.utmMedium,
        utmCampaign: request.utmCampaign,
        utmTerm: request.utmTerm,
        utmContent: request.utmContent,
        expiresAt: request.expiresAt,
        maxClicks: request.maxClicks,
      });

      // Salvar o link de compartilhamento
      const savedShareLink = await this.shareLinkRepository.create(shareLink);

      // Gerar URL de compartilhamento
      const shareUrl = savedShareLink.generateUrl(this.baseUrl);

      return {
        success: true,
        shareLink: savedShareLink,
        shareUrl,
      };
    } catch (error) {
      console.error('Erro ao criar link de compartilhamento:', error);
      return {
        success: false,
        error: 'Ocorreu um erro ao criar o link de compartilhamento.',
      };
    }
  }

  private validateRequest(request: CreateShareLinkRequest): boolean {
    // Validar usuário
    if (!request.userId) {
      return false;
    }

    // Validar target
    if (!request.targetId || !request.targetType) {
      return false;
    }

    // Validar tipo de target
    if (!['product', 'content', 'document', 'collection', 'custom'].includes(request.targetType)) {
      return false;
    }

    // Validar código, se fornecido
    if (request.code && !/^[a-zA-Z0-9_-]+$/.test(request.code)) {
      return false;
    }

    // Validar datas
    if (request.expiresAt && request.expiresAt < new Date()) {
      return false;
    }

    // Validar limite de cliques
    if (request.maxClicks !== undefined && request.maxClicks <= 0) {
      return false;
    }

    return true;
  }

  private async generateUniqueCode(): Promise<string> {
    const maxAttempts = 10;
    let attempts = 0;

    while (attempts < maxAttempts) {
      // Gerar código aleatório
      const code = generateShareCode();

      // Verificar se o código já existe
      const codeExists = await this.shareLinkRepository.codeExists(code);

      if (!codeExists) {
        return code;
      }

      attempts++;
    }

    throw new Error('Não foi possível gerar um código único após várias tentativas.');
  }
}
