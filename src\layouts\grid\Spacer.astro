---
/**
 * Componente de Espaçador
 *
 * Este componente cria um espaço vertical ou horizontal entre elementos.
 */

interface Props {
  size?: number;
  axis?: 'horizontal' | 'vertical' | 'both';
  visible?: boolean;
  class?: string;
}

const { size = 4, axis = 'vertical', visible = false, class: className = '' } = Astro.props;

// Determinar as classes de espaçamento
let spacerClasses = '';

if (axis === 'vertical' || axis === 'both') {
  spacerClasses += ` my-${size}`;
}

if (axis === 'horizontal' || axis === 'both') {
  spacerClasses += ` mx-${size}`;
}

// Adicionar classes para tornar o espaçador visível
if (visible) {
  spacerClasses += ' border border-base-300';
}

// Combinar todas as classes
const classes = ['block', spacerClasses, className].filter(Boolean).join(' ');
---

<div class={classes} aria-hidden="true"></div>
