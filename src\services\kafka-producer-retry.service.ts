/**
 * Serviço de retry para produtores Kafka
 *
 * Este serviço implementa estratégias de retry para produtores Kafka,
 * incluindo backoff exponencial, dead letter queue e tratamento de erros.
 */

import type { Producer, Message, TopicMessages } from 'kafkajs';
import { v4 as uuidv4 } from 'uuid';
// Mock do logger para testes
import { logger } from '../utils/logger';
import {
  RetryStrategy,
  type ProducerErrorType,
  kafkaProducerRetryConfig,
  getTopicRetryConfig,
  mapKafkaErrorToProducerErrorType,
} from '../config/kafka-retry.config';
import { kafkaLoggingService } from './kafka-logging.service';
import { producer } from '../config/kafka';

/**
 * Interface para mensagem com metadados de retry
 */
export interface RetryableMessage extends Message {
  /**
   * Metadados de retry
   */
  retryMetadata?: {
    /**
     * ID único da tentativa
     */
    attemptId: string;

    /**
     * Número da tentativa atual
     */
    attemptCount: number;

    /**
     * Timestamp da primeira tentativa
     */
    firstAttemptTimestamp: number;

    /**
     * Timestamp da última tentativa
     */
    lastAttemptTimestamp: number;

    /**
     * Erro da última tentativa
     */
    lastError?: string;

    /**
     * Tipo de erro da última tentativa
     */
    lastErrorType?: ProducerErrorType;

    /**
     * Tópico original
     */
    originalTopic: string;
  };
}

/**
 * Interface para resultado de envio com retry
 */
export interface SendWithRetryResult {
  /**
   * Se o envio foi bem-sucedido
   */
  success: boolean;

  /**
   * Número de tentativas realizadas
   */
  attempts: number;

  /**
   * Erro final (se houver)
   */
  error?: Error;

  /**
   * Se a mensagem foi enviada para a dead letter queue
   */
  sentToDLQ: boolean;

  /**
   * Tempo total gasto em ms
   */
  totalTimeMs: number;
}

/**
 * Classe para o serviço de retry de produtores Kafka
 */
class KafkaProducerRetryService {
  private static instance: KafkaProducerRetryService;

  /**
   * Construtor privado para implementar Singleton
   */
  private constructor() {}

  /**
   * Obtém a instância única do serviço
   */
  public static getInstance(): KafkaProducerRetryService {
    if (!KafkaProducerRetryService.instance) {
      KafkaProducerRetryService.instance = new KafkaProducerRetryService();
    }
    return KafkaProducerRetryService.instance;
  }

  /**
   * Envia uma mensagem com retry
   * @param topic - Tópico para enviar a mensagem
   * @param message - Mensagem a ser enviada
   * @param producer - Instância do produtor Kafka
   * @returns Resultado do envio
   */
  public async sendWithRetry(
    topic: string,
    message: Message,
    producer: Producer = producer
  ): Promise<SendWithRetryResult> {
    const startTime = Date.now();
    const topicConfig = getTopicRetryConfig(topic);
    let attempts = 0;
    let lastError: Error | undefined;
    let sentToDLQ = false;

    // Preparar mensagem com metadados de retry
    const retryableMessage: RetryableMessage = {
      ...message,
      retryMetadata: {
        attemptId: uuidv4(),
        attemptCount: 0,
        firstAttemptTimestamp: startTime,
        lastAttemptTimestamp: startTime,
        originalTopic: topic,
      },
    };

    // Tentar enviar a mensagem com retry
    while (attempts <= topicConfig.maxRetries) {
      try {
        // Incrementar contador de tentativas
        attempts++;
        retryableMessage.retryMetadata!.attemptCount = attempts;
        retryableMessage.retryMetadata!.lastAttemptTimestamp = Date.now();

        // Adicionar metadados de retry aos headers
        const headers = {
          ...(retryableMessage.headers || {}),
          'retry-attempt-id': retryableMessage.retryMetadata!.attemptId,
          'retry-attempt-count': attempts.toString(),
          'retry-first-attempt-timestamp':
            retryableMessage.retryMetadata!.firstAttemptTimestamp.toString(),
          'retry-last-attempt-timestamp':
            retryableMessage.retryMetadata!.lastAttemptTimestamp.toString(),
        };

        // Enviar mensagem
        await producer.send({
          topic,
          messages: [
            {
              ...retryableMessage,
              headers,
            },
          ],
        });

        // Se chegou aqui, o envio foi bem-sucedido
        kafkaLoggingService.info(
          'kafka.producer.retry',
          `Mensagem enviada com sucesso para tópico ${topic} na tentativa ${attempts}`,
          {
            topic,
            attemptId: retryableMessage.retryMetadata!.attemptId,
            attemptCount: attempts,
            totalTimeMs: Date.now() - startTime,
          }
        );

        return {
          success: true,
          attempts,
          sentToDLQ: false,
          totalTimeMs: Date.now() - startTime,
        };
      } catch (error) {
        lastError = error as Error;

        // Mapear tipo de erro
        const errorType = mapKafkaErrorToProducerErrorType(lastError);
        retryableMessage.retryMetadata!.lastError = lastError.message;
        retryableMessage.retryMetadata!.lastErrorType = errorType;

        // Verificar configuração específica para o tipo de erro
        const errorTypeConfig = topicConfig.errorTypeConfigs?.[errorType];
        const maxRetries =
          errorTypeConfig?.maxRetries ?? topicConfig.maxRetries;

        // Verificar se deve continuar tentando
        if (attempts > maxRetries) {
          kafkaLoggingService.error(
            'kafka.producer.retry',
            `Máximo de tentativas (${maxRetries}) excedido para tópico ${topic}`,
            {
              topic,
              attemptId: retryableMessage.retryMetadata!.attemptId,
              attemptCount: attempts,
              errorType,
              error: lastError.message,
            }
          );

          // Enviar para dead letter queue se configurado
          if (
            topicConfig.deadLetterQueue.enabled &&
            (errorTypeConfig?.useDeadLetterQueue ?? true)
          ) {
            await this.sendToDeadLetterQueue(
              topic,
              retryableMessage,
              lastError,
              errorType,
              producer
            );
            sentToDLQ = true;
          }

          break;
        }

        // Calcular tempo de espera para próxima tentativa
        const retryDelayMs = this.calculateRetryDelay(
          attempts,
          errorType,
          topicConfig
        );

        kafkaLoggingService.warn(
          'kafka.producer.retry',
          `Erro ao enviar mensagem para tópico ${topic}. Tentativa ${attempts}/${maxRetries}. Próxima tentativa em ${retryDelayMs}ms`,
          {
            topic,
            attemptId: retryableMessage.retryMetadata!.attemptId,
            attemptCount: attempts,
            errorType,
            error: lastError.message,
            retryDelayMs,
          }
        );

        // Aguardar antes da próxima tentativa
        await new Promise(resolve => setTimeout(resolve, retryDelayMs));
      }
    }

    // Se chegou aqui, todas as tentativas falharam
    return {
      success: false,
      attempts,
      error: lastError,
      sentToDLQ,
      totalTimeMs: Date.now() - startTime,
    };
  }

  /**
   * Envia múltiplas mensagens com retry
   * @param topicMessages - Mensagens agrupadas por tópico
   * @param producer - Instância do produtor Kafka
   * @returns Resultado do envio
   */
  public async sendBatchWithRetry(
    topicMessages: TopicMessages[],
    producer: Producer = producer
  ): Promise<Record<string, SendWithRetryResult[]>> {
    const results: Record<string, SendWithRetryResult[]> = {};

    // Processar cada tópico separadamente
    for (const { topic, messages } of topicMessages) {
      results[topic] = [];

      // Processar cada mensagem do tópico
      for (const message of messages) {
        const result = await this.sendWithRetry(topic, message, producer);
        results[topic].push(result);
      }
    }

    return results;
  }

  /**
   * Envia uma mensagem para a dead letter queue
   * @param originalTopic - Tópico original
   * @param message - Mensagem original
   * @param error - Erro que causou o envio para DLQ
   * @param errorType - Tipo de erro
   * @param producer - Instância do produtor Kafka
   */
  private async sendToDeadLetterQueue(
    originalTopic: string,
    message: RetryableMessage,
    error: Error,
    errorType: ProducerErrorType,
    producer: Producer
  ): Promise<void> {
    try {
      const topicConfig = getTopicRetryConfig(originalTopic);
      const dlqConfig = topicConfig.deadLetterQueue;

      // Preparar mensagem para DLQ
      const dlqMessage: Message = {
        key: message.key,
        headers: {
          ...(message.headers || {}),
          'dlq-original-topic': originalTopic,
          'dlq-error-type': errorType,
          'dlq-timestamp': Date.now().toString(),
          'dlq-attempt-count':
            message.retryMetadata?.attemptCount.toString() || '0',
        },
        value: Buffer.from(
          JSON.stringify({
            originalMessage: dlqConfig.includeOriginalMessage
              ? typeof message.value === 'string'
                ? message.value
                : message.value?.toString() || null
              : null,
            error: {
              message: error.message,
              name: error.name,
              stack: dlqConfig.includeStackTrace ? error.stack : undefined,
            },
            metadata: dlqConfig.includeMetadata
              ? {
                  originalTopic,
                  errorType,
                  timestamp: Date.now(),
                  retryMetadata: message.retryMetadata,
                }
              : undefined,
          })
        ),
      };

      // Enviar para DLQ
      await producer.send({
        topic: dlqConfig.topic,
        messages: [dlqMessage],
      });

      kafkaLoggingService.info(
        'kafka.producer.dlq',
        `Mensagem enviada para dead letter queue ${dlqConfig.topic}`,
        {
          originalTopic,
          dlqTopic: dlqConfig.topic,
          errorType,
          attemptCount: message.retryMetadata?.attemptCount || 0,
        }
      );
    } catch (dlqError) {
      kafkaLoggingService.error(
        'kafka.producer.dlq',
        `Erro ao enviar mensagem para dead letter queue`,
        {
          originalTopic,
          error: dlqError.message,
          originalError: error.message,
        }
      );
    }
  }

  /**
   * Calcula o tempo de espera para a próxima tentativa
   * @param attempt - Número da tentativa atual
   * @param errorType - Tipo de erro
   * @param config - Configuração de retry
   * @returns Tempo de espera em ms
   */
  private calculateRetryDelay(
    attempt: number,
    errorType: ProducerErrorType,
    config: RetryStrategy | any
  ): number {
    // Obter configuração específica para o tipo de erro, se existir
    const errorTypeConfig = config.errorTypeConfigs?.[errorType];
    const initialRetryTimeMs =
      errorTypeConfig?.initialRetryTimeMs ?? config.initialRetryTimeMs;
    const maxRetryTimeMs =
      errorTypeConfig?.maxRetryTimeMs ?? config.maxRetryTimeMs;
    const multiplier = errorTypeConfig?.multiplier ?? config.multiplier;
    const jitter = errorTypeConfig?.jitter ?? config.jitter;

    // Calcular tempo de espera com base na estratégia
    let delay: number;

    switch (config.strategy) {
      case RetryStrategy.EXPONENTIAL_BACKOFF:
        // Backoff exponencial: initialRetryTime * (multiplier ^ (attempt - 1))
        delay = initialRetryTimeMs * Math.pow(multiplier, attempt - 1);
        break;

      case RetryStrategy.LINEAR_BACKOFF:
        // Backoff linear: initialRetryTime * attempt
        delay = initialRetryTimeMs * attempt;
        break;

      case RetryStrategy.FIXED_INTERVAL:
        // Intervalo fixo: initialRetryTime
        delay = initialRetryTimeMs;
        break;

      case RetryStrategy.IMMEDIATE:
        // Imediato: 0
        delay = 0;
        break;

      default:
        // Padrão: backoff exponencial
        delay = initialRetryTimeMs * Math.pow(multiplier, attempt - 1);
    }

    // Aplicar jitter para evitar tempestades de retry
    // Fórmula: delay = delay * (1 ± jitter)
    if (jitter > 0) {
      const jitterFactor = 1 - jitter + Math.random() * jitter * 2;
      delay = delay * jitterFactor;
    }

    // Limitar ao tempo máximo configurado
    return Math.min(delay, maxRetryTimeMs);
  }
}

// Exportar instância única do serviço
export const kafkaProducerRetryService =
  KafkaProducerRetryService.getInstance();
