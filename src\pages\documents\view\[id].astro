---
import SecurePdfViewer from '../../../components/document/SecurePdfViewer.astro';
import { PageTransition } from '../../../components/transitions';
import DaisyButton from '../../../components/ui/DaisyButton.astro';
import DaisyCard from '../../../components/ui/DaisyCard.astro';
import Breadcrumbs from '../../../components/ui/navigation/Breadcrumbs.astro';
/**
 * Página de Visualização de Documento
 *
 * Interface para visualização segura de documentos PDF.
 * Parte da implementação da tarefa 8.3.2 - Visualização segura
 */
import BaseLayout from '../../../layouts/BaseLayout.astro';
import { Container, Section } from '../../../layouts/grid';
import { formatFileSize } from '../../../utils/fileUtils';

// Obter ID do documento da URL
const { id } = Astro.params;

// Em um cenário real, buscaríamos o documento do repositório
// Por enquanto, usaremos dados de exemplo
const document = {
  id: id || 'doc-001',
  title: 'Guia de Alfabetização',
  description: 'Guia completo para o processo de alfabetização',
  category: 'Alfabetização',
  fileSize: 2500000,
  pageCount: 45,
  version: 2,
  createdAt: new Date('2023-01-15'),
  updatedAt: new Date('2023-03-20'),
  isPublic: true,
  author: 'Equipe Pedagógica',
  tags: ['alfabetização', 'educação', 'leitura'],
  pdfUrl: '/sample-documents/sample.pdf', // URL do PDF de exemplo
};

// Verificar permissões do usuário
// Em um cenário real, isso seria feito através de um serviço de autenticação
const userPermissions = {
  canView: true,
  canDownload: true,
  canPrint: true,
  canCopy: false,
  canEdit: false,
};

// Título da página
const title = `Visualizar: ${document.title}`;

// Breadcrumbs para a página atual
const breadcrumbItems = [
  { href: '/', label: 'Início' },
  { href: '/documents', label: 'Documentos' },
  { label: document.title },
];

// Verificar se o usuário tem permissão para visualizar
if (!userPermissions.canView) {
  return Astro.redirect('/documents?error=no-permission');
}
---

<BaseLayout title={title} enableViewTransitions={true} transitionType="fade">
  <Container>
    <PageTransition type="fade" duration="0.5s">
      <Section>
        <Breadcrumbs items={breadcrumbItems} class="mb-6" />
        
        <div class="flex justify-between items-center mb-6">
          <h1 class="text-3xl font-bold">{document.title}</h1>
          
          <div class="flex space-x-2">
            {userPermissions.canEdit && (
              <DaisyButton 
                href={`/documents/edit/${document.id}`} 
                variant="secondary" 
                icon="edit"
                text="Editar"
              />
            )}
            
            <DaisyButton 
              href="/documents" 
              variant="ghost" 
              icon="arrow-left"
              text="Voltar"
            />
          </div>
        </div>
        
        <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
          <!-- Coluna de informações -->
          <div class="lg:col-span-1">
            <DaisyCard class="mb-6">
              <div class="p-4">
                <h2 class="text-xl font-bold mb-4">Informações</h2>
                
                <div class="space-y-3">
                  <div>
                    <span class="text-sm font-medium text-gray-500">Categoria:</span>
                    <p>{document.category}</p>
                  </div>
                  
                  <div>
                    <span class="text-sm font-medium text-gray-500">Autor:</span>
                    <p>{document.author}</p>
                  </div>
                  
                  <div>
                    <span class="text-sm font-medium text-gray-500">Tamanho:</span>
                    <p>{formatFileSize(document.fileSize)}</p>
                  </div>
                  
                  <div>
                    <span class="text-sm font-medium text-gray-500">Páginas:</span>
                    <p>{document.pageCount}</p>
                  </div>
                  
                  <div>
                    <span class="text-sm font-medium text-gray-500">Versão:</span>
                    <p>v{document.version}</p>
                  </div>
                  
                  <div>
                    <span class="text-sm font-medium text-gray-500">Criado em:</span>
                    <p>{document.createdAt.toLocaleDateString()}</p>
                  </div>
                  
                  <div>
                    <span class="text-sm font-medium text-gray-500">Atualizado em:</span>
                    <p>{document.updatedAt.toLocaleDateString()}</p>
                  </div>
                  
                  <div>
                    <span class="text-sm font-medium text-gray-500">Visibilidade:</span>
                    <p>{document.isPublic ? 'Público' : 'Privado'}</p>
                  </div>
                </div>
              </div>
            </DaisyCard>
            
            <DaisyCard>
              <div class="p-4">
                <h2 class="text-xl font-bold mb-4">Tags</h2>
                
                <div class="flex flex-wrap gap-2">
                  {document.tags.map(tag => (
                    <span class="badge badge-primary">{tag}</span>
                  ))}
                </div>
              </div>
            </DaisyCard>
          </div>
          
          <!-- Coluna do visualizador -->
          <div class="lg:col-span-3">
            <DaisyCard>
              <div class="p-4">
                <SecurePdfViewer
                  pdfUrl={document.pdfUrl}
                  title={document.title}
                  description={document.description}
                  version={document.version}
                  updatedAt={document.updatedAt}
                  allowDownload={userPermissions.canDownload}
                  allowPrint={userPermissions.canPrint}
                  allowCopy={userPermissions.canCopy}
                  height="700px"
                />
              </div>
            </DaisyCard>
            
            <div class="mt-6">
              <DaisyCard>
                <div class="p-4">
                  <h2 class="text-xl font-bold mb-4">Descrição</h2>
                  <p>{document.description}</p>
                </div>
              </DaisyCard>
            </div>
            
            <div class="mt-6">
              <DaisyCard>
                <div class="p-4">
                  <h2 class="text-xl font-bold mb-4">Histórico de Versões</h2>
                  
                  <div class="overflow-x-auto">
                    <table class="table table-zebra w-full">
                      <thead>
                        <tr>
                          <th>Versão</th>
                          <th>Data</th>
                          <th>Autor</th>
                          <th>Ações</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr>
                          <td>v2 (atual)</td>
                          <td>20/03/2023</td>
                          <td>Equipe Pedagógica</td>
                          <td>
                            <a href="#" class="btn btn-xs btn-ghost">Visualizar</a>
                          </td>
                        </tr>
                        <tr>
                          <td>v1</td>
                          <td>15/01/2023</td>
                          <td>Equipe Pedagógica</td>
                          <td>
                            <a href="#" class="btn btn-xs btn-ghost">Visualizar</a>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
              </DaisyCard>
            </div>
          </div>
        </div>
      </Section>
    </PageTransition>
  </Container>
</BaseLayout>

<script>
  // Script para registrar visualização do documento
  document.addEventListener('DOMContentLoaded', () => {
    // Em um cenário real, enviaríamos uma requisição para registrar a visualização
    console.log('Documento visualizado:', document.location.pathname);
    
    // Impedir download direto de imagens e conteúdo
    document.addEventListener('contextmenu', (e) => {
      const target = e.target as HTMLElement;
      if (target.tagName === 'IMG' || target.closest('.secure-pdf-viewer')) {
        e.preventDefault();
      }
    });
    
    // Impedir arrastar e soltar de imagens
    document.addEventListener('dragstart', (e) => {
      const target = e.target as HTMLElement;
      if (target.tagName === 'IMG' || target.closest('.secure-pdf-viewer')) {
        e.preventDefault();
      }
    });
  });
</script>
