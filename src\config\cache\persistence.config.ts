/**
 * Configuração de persistência para o Valkey
 * 
 * Este arquivo contém as configurações para persistência de dados no Valkey,
 * incluindo configurações de RDB (snapshots) e AOF (append-only file).
 */

import { logger } from '@utils/logger';
import * as fs from 'fs';
import * as path from 'path';
import { promisify } from 'util';

// Promisificar funções de filesystem
const mkdir = promisify(fs.mkdir);
const writeFile = promisify(fs.writeFile);
const readFile = promisify(fs.readFile);
const stat = promisify(fs.stat);

/**
 * Tipo de persistência
 */
export enum PersistenceType {
  /**
   * RDB (Redis Database) - Snapshots periódicos
   */
  RDB = 'rdb',
  
  /**
   * AOF (Append Only File) - Log de comandos
   */
  AOF = 'aof',
  
  /**
   * Ambos RDB e AOF
   */
  BOTH = 'both',
  
  /**
   * Sem persistência
   */
  NONE = 'none'
}

/**
 * Estratégia de sincronização AOF
 */
export enum AOFSyncStrategy {
  /**
   * Sincronizar a cada segundo
   */
  EVERY_SECOND = 'everysec',
  
  /**
   * Sincronizar a cada comando
   */
  ALWAYS = 'always',
  
  /**
   * Deixar o sistema operacional decidir
   */
  NO = 'no'
}

/**
 * Interface para configuração de RDB
 */
export interface RDBConfig {
  /**
   * Se o RDB está habilitado
   */
  enabled: boolean;
  
  /**
   * Nome do arquivo de snapshot
   */
  filename: string;
  
  /**
   * Diretório para armazenamento
   */
  dir: string;
  
  /**
   * Configurações de salvamento (segundos frequência)
   * Ex: [[900, 1], [300, 10]] significa:
   * - Salvar se 900 segundos passaram e pelo menos 1 chave mudou
   * - Salvar se 300 segundos passaram e pelo menos 10 chaves mudaram
   */
  saveSettings: [number, number][];
  
  /**
   * Se deve comprimir o arquivo RDB
   */
  compression: boolean;
  
  /**
   * Se deve verificar o checksum do arquivo RDB
   */
  checksum: boolean;
  
  /**
   * Se deve parar de escrever em caso de erro no BGSAVE
   */
  stopWritesOnError: boolean;
}

/**
 * Interface para configuração de AOF
 */
export interface AOFConfig {
  /**
   * Se o AOF está habilitado
   */
  enabled: boolean;
  
  /**
   * Nome do arquivo AOF
   */
  filename: string;
  
  /**
   * Diretório para armazenamento
   */
  dir: string;
  
  /**
   * Estratégia de sincronização
   */
  syncStrategy: AOFSyncStrategy;
  
  /**
   * Se deve reescrever o AOF automaticamente
   */
  autoRewrite: boolean;
  
  /**
   * Porcentagem de crescimento para acionar reescrita
   */
  rewritePercentage: number;
  
  /**
   * Tamanho mínimo para acionar reescrita (bytes)
   */
  rewriteMinSize: number;
  
  /**
   * Se deve carregar AOF truncado
   */
  loadTruncated: boolean;
  
  /**
   * Se deve usar RDB preamble no AOF
   */
  useRdbPreamble: boolean;
}

/**
 * Interface para configuração de backup
 */
export interface BackupConfig {
  /**
   * Se o backup automático está habilitado
   */
  enabled: boolean;
  
  /**
   * Diretório para armazenamento de backups
   */
  dir: string;
  
  /**
   * Frequência de backup (cron expression)
   */
  schedule: string;
  
  /**
   * Número de backups a manter
   */
  retention: number;
  
  /**
   * Se deve comprimir backups
   */
  compress: boolean;
  
  /**
   * Destinos externos para cópia de backups
   */
  destinations?: {
    /**
     * Tipo de destino
     */
    type: 'local' | 's3' | 'ftp' | 'sftp';
    
    /**
     * Caminho ou URL do destino
     */
    path: string;
    
    /**
     * Credenciais (se necessário)
     */
    credentials?: {
      username?: string;
      password?: string;
      accessKey?: string;
      secretKey?: string;
    };
  }[];
}

/**
 * Interface para configuração completa de persistência
 */
export interface PersistenceConfig {
  /**
   * Tipo de persistência
   */
  type: PersistenceType;
  
  /**
   * Configuração de RDB
   */
  rdb: RDBConfig;
  
  /**
   * Configuração de AOF
   */
  aof: AOFConfig;
  
  /**
   * Configuração de backup
   */
  backup: BackupConfig;
}

/**
 * Configurações de persistência para diferentes ambientes
 */
export const persistenceConfigs: Record<string, PersistenceConfig> = {
  // Ambiente de desenvolvimento
  'development': {
    type: PersistenceType.RDB,
    rdb: {
      enabled: true,
      filename: 'dump.rdb',
      dir: './data',
      saveSettings: [
        [900, 1],   // 15 minutos se pelo menos 1 chave mudou
        [300, 10],  // 5 minutos se pelo menos 10 chaves mudaram
        [60, 10000] // 1 minuto se pelo menos 10000 chaves mudaram
      ],
      compression: true,
      checksum: true,
      stopWritesOnError: true
    },
    aof: {
      enabled: false,
      filename: 'appendonly.aof',
      dir: './data',
      syncStrategy: AOFSyncStrategy.EVERY_SECOND,
      autoRewrite: true,
      rewritePercentage: 100,
      rewriteMinSize: 64 * 1024 * 1024, // 64MB
      loadTruncated: true,
      useRdbPreamble: true
    },
    backup: {
      enabled: false,
      dir: './backups',
      schedule: '0 0 * * *', // Diariamente à meia-noite
      retention: 7,
      compress: true
    }
  },
  
  // Ambiente de teste
  'test': {
    type: PersistenceType.BOTH,
    rdb: {
      enabled: true,
      filename: 'dump.rdb',
      dir: './data',
      saveSettings: [
        [900, 1],
        [300, 10],
        [60, 10000]
      ],
      compression: true,
      checksum: true,
      stopWritesOnError: true
    },
    aof: {
      enabled: true,
      filename: 'appendonly.aof',
      dir: './data',
      syncStrategy: AOFSyncStrategy.EVERY_SECOND,
      autoRewrite: true,
      rewritePercentage: 100,
      rewriteMinSize: 64 * 1024 * 1024,
      loadTruncated: true,
      useRdbPreamble: true
    },
    backup: {
      enabled: true,
      dir: './backups',
      schedule: '0 0 * * *',
      retention: 7,
      compress: true
    }
  },
  
  // Ambiente de produção
  'production': {
    type: PersistenceType.BOTH,
    rdb: {
      enabled: true,
      filename: 'dump.rdb',
      dir: process.env.VALKEY_DATA_DIR || '/var/lib/valkey',
      saveSettings: [
        [900, 1],
        [300, 10],
        [60, 10000]
      ],
      compression: true,
      checksum: true,
      stopWritesOnError: true
    },
    aof: {
      enabled: true,
      filename: 'appendonly.aof',
      dir: process.env.VALKEY_DATA_DIR || '/var/lib/valkey',
      syncStrategy: AOFSyncStrategy.EVERY_SECOND,
      autoRewrite: true,
      rewritePercentage: 100,
      rewriteMinSize: 64 * 1024 * 1024,
      loadTruncated: true,
      useRdbPreamble: true
    },
    backup: {
      enabled: true,
      dir: process.env.VALKEY_BACKUP_DIR || '/var/backups/valkey',
      schedule: '0 2 * * *', // Diariamente às 2h da manhã
      retention: 30,
      compress: true,
      destinations: [
        {
          type: 's3',
          path: process.env.BACKUP_S3_PATH || 's3://estacao-backups/valkey',
          credentials: {
            accessKey: process.env.AWS_ACCESS_KEY_ID,
            secretKey: process.env.AWS_SECRET_ACCESS_KEY
          }
        }
      ]
    }
  }
};

/**
 * Obtém a configuração de persistência para o ambiente atual
 * @returns Configuração de persistência
 */
export function getPersistenceConfig(): PersistenceConfig {
  const env = process.env.NODE_ENV || 'development';
  return persistenceConfigs[env] || persistenceConfigs.development;
}

/**
 * Gera comandos de configuração de persistência para o Valkey
 * @returns Array de linhas de configuração
 */
export function generatePersistenceConfig(): string[] {
  const config = getPersistenceConfig();
  const lines: string[] = [];
  
  lines.push('# Configuração de persistência');
  
  // Configuração de RDB
  if (config.type === PersistenceType.RDB || config.type === PersistenceType.BOTH) {
    lines.push('# Configuração de RDB (snapshots)');
    
    // Configurações de salvamento
    if (config.rdb.saveSettings.length > 0) {
      config.rdb.saveSettings.forEach(([seconds, changes]) => {
        lines.push(`save ${seconds} ${changes}`);
      });
    } else {
      lines.push('save ""'); // Desabilitar salvamento automático
    }
    
    lines.push(`stop-writes-on-bgsave-error ${config.rdb.stopWritesOnError ? 'yes' : 'no'}`);
    lines.push(`rdbcompression ${config.rdb.compression ? 'yes' : 'no'}`);
    lines.push(`rdbchecksum ${config.rdb.checksum ? 'yes' : 'no'}`);
    lines.push(`dbfilename ${config.rdb.filename}`);
    lines.push(`dir ${config.rdb.dir}`);
  } else {
    lines.push('# RDB desabilitado');
    lines.push('save ""');
  }
  
  // Configuração de AOF
  if (config.type === PersistenceType.AOF || config.type === PersistenceType.BOTH) {
    lines.push('');
    lines.push('# Configuração de AOF (append only file)');
    lines.push('appendonly yes');
    lines.push(`appendfilename "${config.aof.filename}"`);
    lines.push(`appendfsync ${config.aof.syncStrategy}`);
    lines.push(`no-appendfsync-on-rewrite ${config.aof.autoRewrite ? 'yes' : 'no'}`);
    lines.push(`auto-aof-rewrite-percentage ${config.aof.rewritePercentage}`);
    lines.push(`auto-aof-rewrite-min-size ${config.aof.rewriteMinSize}`);
    lines.push(`aof-load-truncated ${config.aof.loadTruncated ? 'yes' : 'no'}`);
    lines.push(`aof-use-rdb-preamble ${config.aof.useRdbPreamble ? 'yes' : 'no'}`);
  } else {
    lines.push('');
    lines.push('# AOF desabilitado');
    lines.push('appendonly no');
  }
  
  return lines;
}

/**
 * Verifica o estado da persistência
 * @param client Cliente Valkey
 * @returns Informações sobre o estado da persistência
 */
export async function checkPersistenceStatus(client: any): Promise<any> {
  try {
    const info = await client.info('persistence');
    
    logger.info('Estado da persistência Valkey:', { info });
    
    return { info };
  } catch (error) {
    logger.error('Erro ao verificar estado da persistência Valkey:', error);
    throw error;
  }
}

/**
 * Cria diretórios necessários para persistência
 * @returns Promise que resolve quando os diretórios são criados
 */
export async function createPersistenceDirectories(): Promise<void> {
  const config = getPersistenceConfig();
  
  try {
    // Criar diretório para RDB/AOF
    if (config.type !== PersistenceType.NONE) {
      const dataDir = config.rdb.dir;
      await mkdir(dataDir, { recursive: true });
      logger.info(`Diretório de dados criado: ${dataDir}`);
    }
    
    // Criar diretório para backups
    if (config.backup.enabled) {
      await mkdir(config.backup.dir, { recursive: true });
      logger.info(`Diretório de backups criado: ${config.backup.dir}`);
    }
  } catch (error) {
    logger.error('Erro ao criar diretórios para persistência:', error);
    throw error;
  }
}
