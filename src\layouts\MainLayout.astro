---
import MainFooter from '@components/layout/MainFooter.astro';
import MainHeader from '@components/layout/MainHeader.astro';
import { PageTransition, PersistentElement } from '../components/transitions';
/**
 * Layout principal da aplicação
 * Implementa transições entre páginas usando View Transitions API
 */
import BaseLayout from './BaseLayout.astro';

interface Props {
  /**
   * T<PERSON><PERSON>lo da página
   */
  title?: string;

  /**
   * Descrição da página para SEO
   */
  description?: string;

  /**
   * Tipo de transição para a página
   * @default "fade"
   */
  transitionType?: 'fade' | 'slide' | 'scale' | 'flip';

  /**
   * Direção da transição (para slide e flip)
   * @default "right"
   */
  transitionDirection?: 'left' | 'right' | 'up' | 'down';
}

const {
  title = 'Estação da Alfabetização',
  description = 'Plataforma educacional para alfabetização',
  transitionType = 'fade',
  transitionDirection = 'right',
} = Astro.props;
---

<BaseLayout title={title} description={description} enableViewTransitions={true} transitionType={transitionType}>
	<PersistentElement id="main-header">
		<MainHeader />
	</PersistentElement>

	<PageTransition type={transitionType} direction={transitionDirection} duration="0.5s">
		<div class="l-index-container">
			<div class="l-image">
				<img src="./Designer10.jpeg" alt="Estação da Alfabetação"/>
			</div>
			<div class="l-child">
				<img src="./crianca.png" />
			</div>
			<div class="l-text">
				<div>
					<p>Olá!</p>
				</div>
				<div>
					<p>Meu nome é Renata, professora desde 1993 e apaixonada por Alfabetização. Por esse motivo desenvolvi esta plataforma com o objetivo de proporcionar aos profissionais da área da educação, ferramentas para auxiliar nesta fase tão importante.</p>
				</div>
				<div>
					<p>São conteúdos exclusivos, elaborados com muito carinho, pois sabemos que a Alfabetização é a base para trilhar novos caminhos.</p>
				</div>
				<div>
					<p>Meu desejo é que estas atividades possam chegar até vocês, proporcionando prazer e interesse por essa trajetória tão maravilhosa que é a descoberta da leitura e da escrita.</p>
				</div>
				<div>
					<p>Com amor, Renata.</p>
				</div>
				<div>
					<a href="/produtos" class="btn btn-error btn-sm l-btn-access-store" data-astro-transition data-transition-type="scale">Acessar a loja</a>
				</div>
			</div>
		</div>
	</PageTransition>

	<PersistentElement id="main-footer">
		<MainFooter />
	</PersistentElement>
</BaseLayout>

<style>
	.l-btn-access-store {
		font-size: 1rem;
		width    : 200px
	}

	.l-child {
		background-color: var(--color-secondary-100);
		border          : 4px solid var(--color-error-100);
		border-radius   : 90px;
		box-shadow      : rgba(0, 0, 0, 0.5) 0px 5px 15px;
		left            : 90%;
		margin-right    : 50px;
		position        : absolute;
		top             : 75%;
		width           : 110px;
	}

	.l-image {
		border-radius: 8px;
		box-shadow   : rgba(0, 0, 0, 0.5) 0px 5px 15px;
		margin   : 12px 0px 0px 12px;
	}

	.l-index-container {
		display        : flex;
		flex-direction : row;
		height         : auto;
		justify-content: space-between;
		padding        : 10px;
		position       : absolute;
		top            : var(--header-height);
		width          : 100vw;
	}

	.l-text {
		border-radius  : 8px;
		color          : var(--color-royal-blue-100);
		display        : flex;
		flex-direction : column;
		font-family    : var(--font-name);
		font-size      : 1rem;
		font-weight    : bold;
		height         : auto;
		justify-content: space-between;
		margin         : 8px;
		padding        : 1rem;
		text-align     : justify;
		text-indent    : 50px;
		width          : calc(100vw - var(--image-width));
	}
</style>