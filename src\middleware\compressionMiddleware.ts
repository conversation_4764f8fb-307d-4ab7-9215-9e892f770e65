/**
 * Middleware de compressão de resposta
 *
 * Este middleware comprime respostas HTTP para reduzir o tamanho
 * de transferência e melhorar a performance.
 */

import { Readable, Transform } from 'node:stream';
import { createBrotliCompress, createDeflate, createGzip } from 'node:zlib';
import { defineMiddleware } from 'astro:middleware';
import { logger } from '@utils/logger';

/**
 * Tipos de conteúdo que devem ser comprimidos
 */
const COMPRESSIBLE_CONTENT_TYPES = [
  'text/html',
  'text/css',
  'text/javascript',
  'application/javascript',
  'application/json',
  'application/xml',
  'text/xml',
  'text/plain',
  'image/svg+xml',
  'application/xhtml+xml',
  'application/rss+xml',
  'application/atom+xml',
  'font/ttf',
  'font/otf',
  'font/eot',
  'application/x-font-ttf',
];

/**
 * Tamanho mínimo para compressão (bytes)
 */
const MIN_SIZE_TO_COMPRESS = 1024; // 1KB

/**
 * Verifica se o conteúdo deve ser comprimido
 * @param contentType - Tipo de conteúdo
 * @param contentLength - Tamanho do conteúdo
 * @returns Verdadeiro se o conteúdo deve ser comprimido
 */
function shouldCompress(contentType: string | null, contentLength: number | null): boolean {
  // Verificar tamanho mínimo
  if (contentLength !== null && contentLength < MIN_SIZE_TO_COMPRESS) {
    return false;
  }

  // Verificar tipo de conteúdo
  if (!contentType) {
    return false;
  }

  // Verificar se o tipo de conteúdo é compressível
  return COMPRESSIBLE_CONTENT_TYPES.some((type) => contentType.includes(type));
}

/**
 * Determina o algoritmo de compressão com base nos cabeçalhos Accept-Encoding
 * @param acceptEncoding - Valor do cabeçalho Accept-Encoding
 * @returns Algoritmo de compressão ou null se não suportado
 */
function getCompressionAlgorithm(acceptEncoding: string | null): 'br' | 'gzip' | 'deflate' | null {
  if (!acceptEncoding) {
    return null;
  }

  // Verificar suporte a Brotli
  if (acceptEncoding.includes('br')) {
    return 'br';
  }

  // Verificar suporte a Gzip
  if (acceptEncoding.includes('gzip')) {
    return 'gzip';
  }

  // Verificar suporte a Deflate
  if (acceptEncoding.includes('deflate')) {
    return 'deflate';
  }

  return null;
}

/**
 * Cria um stream de compressão com base no algoritmo
 * @param algorithm - Algoritmo de compressão
 * @returns Stream de compressão
 */
function createCompressionStream(algorithm: 'br' | 'gzip' | 'deflate'): Transform {
  switch (algorithm) {
    case 'br':
      return createBrotliCompress();
    case 'gzip':
      return createGzip();
    case 'deflate':
      return createDeflate();
    default:
      throw new Error(`Algoritmo de compressão não suportado: ${algorithm}`);
  }
}

/**
 * Middleware de compressão
 */
export const onRequest = defineMiddleware(async (context, next) => {
  // Executar próximo middleware para obter a resposta
  const response = await next();

  try {
    // Verificar se a resposta já está comprimida
    if (response.headers.has('content-encoding')) {
      return response;
    }

    // Obter informações da resposta
    const contentType = response.headers.get('content-type');
    const contentLength = response.headers.has('content-length')
      ? Number.parseInt(response.headers.get('content-length') || '0', 10)
      : null;

    // Verificar se a resposta deve ser comprimida
    if (!shouldCompress(contentType, contentLength)) {
      return response;
    }

    // Obter algoritmo de compressão com base no cabeçalho Accept-Encoding
    const acceptEncoding = context.request.headers.get('accept-encoding');
    const algorithm = getCompressionAlgorithm(acceptEncoding);

    // Se nenhum algoritmo suportado, retornar resposta original
    if (!algorithm) {
      return response;
    }

    // Obter corpo da resposta como stream
    const body = response.body;

    // Se não há corpo, retornar resposta original
    if (!body) {
      return response;
    }

    // Criar stream de compressão
    const compressionStream = createCompressionStream(algorithm);

    // Criar stream de leitura a partir do corpo
    const bodyStream = Readable.fromWeb(body as any);

    // Comprimir corpo
    const compressedBody = bodyStream.pipe(compressionStream);

    // Criar nova resposta com corpo comprimido
    const compressedResponse = new Response(compressedBody as any, {
      status: response.status,
      statusText: response.statusText,
      headers: response.headers,
    });

    // Adicionar cabeçalho de codificação
    compressedResponse.headers.set('content-encoding', algorithm);

    // Remover cabeçalho de tamanho (não é mais válido após compressão)
    compressedResponse.headers.delete('content-length');

    // Adicionar cabeçalho Vary para cache correto
    compressedResponse.headers.set('vary', 'accept-encoding');

    return compressedResponse;
  } catch (error) {
    // Em caso de erro, retornar resposta original
    logger.error('Erro ao comprimir resposta:', error);
    return response;
  }
});
