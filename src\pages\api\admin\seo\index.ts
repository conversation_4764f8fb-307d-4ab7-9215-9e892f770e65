/**
 * API de SEO
 *
 * Endpoint para gerenciamento de configurações de SEO.
 * Parte da implementação da tarefa 8.9.1 - Otimização para buscadores
 */

import type { APIRoute } from 'astro';
import { SEOService } from '../../../../domain/services/SEOService';
import { TokenService } from '../../../../domain/services/TokenService';
import { DefaultSEOService } from '../../../../infrastructure/services/DefaultSEOService';
import { JwtTokenService } from '../../../../infrastructure/services/JwtTokenService';

// Inicializar serviços
const baseUrl = import.meta.env.BASE_URL || 'https://example.com';
const seoService: SEOService = new DefaultSEOService(
  baseUrl,
  'Site Default Title',
  'Site Default Description',
  ['keyword1', 'keyword2', 'keyword3'],
  {
    ogSiteName: 'Site Name',
    twitterSite: '@sitehandle',
  }
);

const tokenService: TokenService = new JwtTokenService(
  process.env.JWT_SECRET || 'default-secret-key',
  process.env.PASSWORD_RESET_SECRET || 'password-reset-secret-key',
  process.env.EMAIL_VERIFICATION_SECRET || 'email-verification-secret-key'
);

// Verificar autenticação e permissões
const checkAuth = (request: Request): { userId: string; isAuthorized: boolean } | null => {
  const authHeader = request.headers.get('Authorization');

  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null;
  }

  const token = authHeader.substring(7);
  const payload = tokenService.verifyToken(token);

  if (!payload) {
    return null;
  }

  // Verificar permissões
  const isAuthorized = payload.role === 'admin' || payload.permissions?.includes('seo:manage');

  return {
    userId: payload.id,
    isAuthorized,
  };
};

export const GET: APIRoute = async ({ request }) => {
  try {
    // Verificar autenticação
    const auth = checkAuth(request);

    if (!auth) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Não autorizado. Faça login para continuar.',
        }),
        {
          status: 401,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    if (!auth.isAuthorized) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Você não tem permissão para acessar este recurso.',
        }),
        {
          status: 403,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    // Obter parâmetros de consulta
    const url = new URL(request.url);
    const path = url.searchParams.get('path');

    if (!path) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Parâmetro path é obrigatório.',
        }),
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    // Obter configurações de SEO
    const seo = await seoService.getPageSEO(path);

    if (!seo) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Configurações de SEO não encontradas para o caminho especificado.',
        }),
        {
          status: 404,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    return new Response(
      JSON.stringify({
        success: true,
        data: seo,
      }),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  } catch (error) {
    console.error('Erro ao processar obtenção de configurações de SEO:', error);

    return new Response(
      JSON.stringify({
        success: false,
        error:
          'Ocorreu um erro ao processar a obtenção das configurações de SEO. Por favor, tente novamente.',
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  }
};

export const POST: APIRoute = async ({ request }) => {
  try {
    // Verificar autenticação
    const auth = checkAuth(request);

    if (!auth) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Não autorizado. Faça login para continuar.',
        }),
        {
          status: 401,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    if (!auth.isAuthorized) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Você não tem permissão para acessar este recurso.',
        }),
        {
          status: 403,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    // Obter dados do corpo da requisição
    const body = await request.json();

    // Validar dados
    if (!body.path || !body.title || !body.description) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Dados incompletos. Verifique os campos obrigatórios.',
        }),
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    // Salvar configurações de SEO
    const seo = await seoService.savePageSEO({
      path: body.path,
      title: body.title,
      description: body.description,
      keywords: body.keywords,
      canonicalUrl: body.canonicalUrl,
      robots: body.robots,
      socialMedia: body.socialMedia,
      structuredData: body.structuredData,
      alternateLanguages: body.alternateLanguages,
    });

    return new Response(
      JSON.stringify({
        success: true,
        data: seo,
      }),
      {
        status: 201,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  } catch (error) {
    console.error('Erro ao processar salvamento de configurações de SEO:', error);

    return new Response(
      JSON.stringify({
        success: false,
        error:
          'Ocorreu um erro ao processar o salvamento das configurações de SEO. Por favor, tente novamente.',
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  }
};

export const PUT: APIRoute = async ({ request }) => {
  try {
    // Verificar autenticação
    const auth = checkAuth(request);

    if (!auth) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Não autorizado. Faça login para continuar.',
        }),
        {
          status: 401,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    if (!auth.isAuthorized) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Você não tem permissão para acessar este recurso.',
        }),
        {
          status: 403,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    // Obter dados do corpo da requisição
    const body = await request.json();

    // Validar dados
    if (!body.path) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Parâmetro path é obrigatório.',
        }),
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    // Atualizar configurações de SEO
    const seo = await seoService.updatePageSEO(body.path, {
      title: body.title,
      description: body.description,
      keywords: body.keywords,
      canonicalUrl: body.canonicalUrl,
      robots: body.robots,
      socialMedia: body.socialMedia,
      structuredData: body.structuredData,
      alternateLanguages: body.alternateLanguages,
    });

    if (!seo) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Configurações de SEO não encontradas para o caminho especificado.',
        }),
        {
          status: 404,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    return new Response(
      JSON.stringify({
        success: true,
        data: seo,
      }),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  } catch (error) {
    console.error('Erro ao processar atualização de configurações de SEO:', error);

    return new Response(
      JSON.stringify({
        success: false,
        error:
          'Ocorreu um erro ao processar a atualização das configurações de SEO. Por favor, tente novamente.',
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  }
};

export const DELETE: APIRoute = async ({ request }) => {
  try {
    // Verificar autenticação
    const auth = checkAuth(request);

    if (!auth) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Não autorizado. Faça login para continuar.',
        }),
        {
          status: 401,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    if (!auth.isAuthorized) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Você não tem permissão para acessar este recurso.',
        }),
        {
          status: 403,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    // Obter parâmetros de consulta
    const url = new URL(request.url);
    const path = url.searchParams.get('path');

    if (!path) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Parâmetro path é obrigatório.',
        }),
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    // Remover configurações de SEO
    const success = await seoService.removePageSEO(path);

    if (!success) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Erro ao remover configurações de SEO.',
        }),
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    return new Response(
      JSON.stringify({
        success: true,
        message: 'Configurações de SEO removidas com sucesso.',
      }),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  } catch (error) {
    console.error('Erro ao processar remoção de configurações de SEO:', error);

    return new Response(
      JSON.stringify({
        success: false,
        error:
          'Ocorreu um erro ao processar a remoção das configurações de SEO. Por favor, tente novamente.',
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  }
};
