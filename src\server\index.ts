import { cacheConfig } from '@config/cache';
import { configureReplicationAndPartitioning } from '@scripts/kafka-replication-config';
import { setupKafkaTopics } from '@scripts/kafka-setup';
import { cacheService } from '@services/cacheService';
import { initNotificationEventConsumer } from '@services/consumers/notificationEventConsumer';
import { initOrderEventConsumer } from '@services/consumers/orderEventConsumer';
// src/server/index.ts
import { initPaymentEventConsumer } from '@services/paymentEventConsumer';
import { logger } from '@utils/logger';

// Inicializar serviços
async function initServices() {
  try {
    // Configurar tópicos Kafka
    try {
      logger.info('Configurando tópicos Kafka...');
      await setupKafkaTopics();
      logger.info('Tópicos Kafka configurados com sucesso');

      // Configurar replicação e particionamento
      logger.info('Configurando replicação e particionamento...');
      await configureReplicationAndPartitioning();
      logger.info('Replicação e particionamento configurados com sucesso');
    } catch (kafkaError) {
      logger.error('Erro ao configurar Kafka:', kafkaError);
      logger.warn(
        'O sistema continuará funcionando, mas alguns recursos podem não estar disponíveis'
      );
    }

    // Inicializar consumidores de eventos
    if (process.env.ENABLE_KAFKA_CONSUMER === 'true') {
      try {
        logger.info('Inicializando consumidores de eventos...');

        // Inicializar consumidor de eventos de pagamento
        await initPaymentEventConsumer();
        logger.info('Consumidor de eventos de pagamento inicializado com sucesso');

        // Inicializar consumidor de eventos de pedido
        await initOrderEventConsumer();
        logger.info('Consumidor de eventos de pedido inicializado com sucesso');

        // Inicializar consumidor de eventos de notificação
        await initNotificationEventConsumer();
        logger.info('Consumidor de eventos de notificação inicializado com sucesso');

        logger.info('Todos os consumidores de eventos inicializados com sucesso');
      } catch (consumerError) {
        logger.error('Erro ao inicializar consumidores de eventos:', consumerError);
        logger.warn(
          'O sistema continuará funcionando, mas o processamento de eventos pode estar indisponível'
        );
      }
    }

    // Verificar conexão com o cache
    try {
      const stats = await cacheService.getStats();
      if (stats) {
        logger.info('Conexão com Valkey estabelecida com sucesso');

        if (cacheConfig.logging.enabled) {
          logger.info('Estatísticas do Valkey:', {
            version: stats.redis_version,
            uptime: stats.uptime_in_seconds,
            memory: stats.used_memory_human,
            clients: stats.connected_clients,
          });
        }
      } else {
        logger.warn('Não foi possível obter estatísticas do Valkey');
      }
    } catch (cacheError) {
      logger.error('Erro ao conectar com Valkey:', cacheError);
      logger.warn('O sistema continuará funcionando sem cache');
    }

    logger.info('Serviços inicializados com sucesso');
  } catch (error) {
    logger.error('Erro ao inicializar serviços:', error);
  }
}

// Inicializar serviços quando o servidor for iniciado
initServices();
