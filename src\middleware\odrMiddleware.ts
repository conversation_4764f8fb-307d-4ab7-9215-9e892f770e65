/**
 * On-Demand Rendering Middleware
 *
 * Este middleware gerencia a renderização sob demanda (ODR) para o Astro.
 * Ele controla quais rotas são renderizadas sob demanda e implementa
 * estratégias de cache para otimizar a performance.
 */

import { defineMiddleware } from 'astro:middleware';
import type { MiddlewareResponseHandler } from 'astro';
import { getLayeredCache, setLayeredCache } from '../infrastructure/cache/LayeredCacheService';
import { MetricType, recordODRMetric } from '../services/ODRMetricsService';

// Configuração de ODR
const ODR_CONFIG = {
  // Rotas que devem ser sempre renderizadas sob demanda
  dynamicRoutes: [
    '/courses/',
    '/materials/',
    '/dashboard/',
    '/profile/',
    '/search',
    '/conteudos/',
    '/content/',
  ],

  // Rotas que devem ser pré-renderizadas (não usam ODR)
  staticRoutes: ['/', '/about', '/contact', '/terms', '/privacy', '/contato', '/acessibilidade'],

  // TTL padrão para cache de páginas dinâmicas (em segundos)
  defaultTTL: 60 * 5, // 5 minutos

  // TTLs específicos por rota (em segundos)
  routeTTL: {
    '/courses/': 60 * 60, // 1 hora
    '/materials/': 60 * 60, // 1 hora
    '/conteudos/': 60 * 60, // 1 hora
    '/content/': 60 * 60, // 1 hora
    '/search': 60 * 2, // 2 minutos
    '/dashboard/': 60 * 3, // 3 minutos
    '/profile/': 60 * 3, // 3 minutos
  },

  // Parâmetros de URL que invalidam o cache
  noCacheParams: ['refresh', 'nocache', 'preview', 'atualizar', 'editar'],

  // Headers para controle de cache
  cacheControlHeaders: {
    dynamic: 'public, max-age=0, s-maxage=300, stale-while-revalidate=60',
    static: 'public, max-age=86400, s-maxage=604800',
    content: 'public, max-age=0, s-maxage=3600, stale-while-revalidate=300',
  },

  // Configurações específicas por tipo de conteúdo
  contentTypeConfig: {
    // Conteúdos educacionais
    educational: {
      ttl: 60 * 60, // 1 hora
      cacheControl: 'public, max-age=0, s-maxage=3600, stale-while-revalidate=300',
      varyHeaders: ['Accept-Encoding', 'Accept-Language'],
    },
    // Conteúdos de usuário
    user: {
      ttl: 60 * 3, // 3 minutos
      cacheControl: 'private, max-age=0, s-maxage=180, must-revalidate',
      varyHeaders: ['Accept-Encoding', 'Cookie', 'Authorization'],
    },
    // Resultados de busca
    search: {
      ttl: 60 * 2, // 2 minutos
      cacheControl: 'public, max-age=0, s-maxage=120, stale-while-revalidate=60',
      varyHeaders: ['Accept-Encoding', 'Accept-Language'],
    },
  },
};

/**
 * Verifica se uma rota deve usar renderização sob demanda
 * @param path Caminho da URL
 * @returns Verdadeiro se a rota deve usar ODR
 */
function shouldUseODR(path: string): boolean {
  // Verificar se é uma rota dinâmica
  const isDynamicRoute = ODR_CONFIG.dynamicRoutes.some((route) => path.startsWith(route));

  // Verificar se é uma rota estática
  const isStaticRoute = ODR_CONFIG.staticRoutes.some(
    (route) => route === path || (route.endsWith('/') && path.startsWith(route))
  );

  // Usar ODR para rotas dinâmicas e não estáticas
  return isDynamicRoute || !isStaticRoute;
}

/**
 * Obtém o TTL para uma rota específica
 * @param path Caminho da URL
 * @returns TTL em segundos
 */
function getTTL(path: string): number {
  // Verificar TTLs específicos por rota
  for (const [route, ttl] of Object.entries(ODR_CONFIG.routeTTL)) {
    if (path.startsWith(route)) {
      return ttl;
    }
  }

  // Verificar TTLs por tipo de conteúdo
  const contentType = getContentType(path);
  if (contentType && ODR_CONFIG.contentTypeConfig[contentType]) {
    return ODR_CONFIG.contentTypeConfig[contentType].ttl;
  }

  // Usar TTL padrão
  return ODR_CONFIG.defaultTTL;
}

/**
 * Identifica o tipo de conteúdo com base no caminho
 * @param path Caminho da URL
 * @returns Tipo de conteúdo ou undefined
 */
function getContentType(path: string): 'educational' | 'user' | 'search' | undefined {
  if (
    path.startsWith('/conteudos/') ||
    path.startsWith('/content/') ||
    path.startsWith('/courses/') ||
    path.startsWith('/materials/')
  ) {
    return 'educational';
  }
  if (path.startsWith('/dashboard/') || path.startsWith('/profile/')) {
    return 'user';
  }
  if (path.startsWith('/search') || path.includes('busca')) {
    return 'search';
  }

  return undefined;
}

/**
 * Obtém os headers de cache para uma rota específica
 * @param path Caminho da URL
 * @returns Headers de cache
 */
function getCacheHeaders(path: string): Record<string, string> {
  const headers: Record<string, string> = {};

  // Verificar tipo de conteúdo
  const contentType = getContentType(path);

  if (contentType && ODR_CONFIG.contentTypeConfig[contentType]) {
    // Usar configuração específica para o tipo de conteúdo
    const config = ODR_CONFIG.contentTypeConfig[contentType];

    // Definir Cache-Control
    headers['Cache-Control'] = config.cacheControl;

    // Definir Vary
    headers.Vary = config.varyHeaders.join(', ');
  } else if (path.startsWith('/conteudos/') || path.startsWith('/content/')) {
    // Fallback para conteúdos
    headers['Cache-Control'] = ODR_CONFIG.cacheControlHeaders.content;
    headers.Vary = 'Accept-Encoding, Accept-Language';
  } else {
    // Usar configuração padrão para rotas dinâmicas
    headers['Cache-Control'] = ODR_CONFIG.cacheControlHeaders.dynamic;
    headers.Vary = 'Accept-Encoding';
  }

  return headers;
}

/**
 * Verifica se o cache deve ser ignorado com base nos parâmetros da URL
 * @param url URL da requisição
 * @returns Verdadeiro se o cache deve ser ignorado
 */
function shouldSkipCache(url: URL): boolean {
  // Verificar parâmetros que invalidam o cache
  for (const param of ODR_CONFIG.noCacheParams) {
    if (url.searchParams.has(param)) {
      return true;
    }
  }

  return false;
}

/**
 * Gera uma chave de cache para a URL
 * @param url URL da requisição
 * @returns Chave de cache
 */
function generateCacheKey(url: URL): string {
  // Remover parâmetros que invalidam o cache
  const cleanUrl = new URL(url);

  for (const param of ODR_CONFIG.noCacheParams) {
    cleanUrl.searchParams.delete(param);
  }

  // Adicionar prefixo baseado no tipo de conteúdo
  const path = cleanUrl.pathname;
  let prefix = 'odr';

  if (
    path.startsWith('/conteudos/') ||
    path.startsWith('/content/') ||
    path.startsWith('/courses/') ||
    path.startsWith('/materials/')
  ) {
    prefix = 'odr:content';
  } else if (path.startsWith('/dashboard/') || path.startsWith('/profile/')) {
    prefix = 'odr:user';
  } else if (path.startsWith('/search') || path.includes('busca')) {
    prefix = 'odr:search';
  }

  return `${prefix}:${cleanUrl.pathname}${cleanUrl.search}`;
}

/**
 * Gera um ETag para uma resposta
 * @param content Conteúdo da resposta
 * @param url URL da requisição
 * @returns ETag
 */
function generateETag(content: string, url: URL): string {
  // Gerar hash simples
  let hash = 0;
  for (let i = 0; i < content.length; i++) {
    const char = content.charCodeAt(i);
    hash = (hash << 5) - hash + char;
    hash = hash & hash; // Converter para inteiro de 32 bits
  }

  // Adicionar informações da URL para tornar o ETag mais específico
  const urlInfo = `${url.pathname}${url.search}`;
  for (let i = 0; i < urlInfo.length; i++) {
    const char = urlInfo.charCodeAt(i);
    hash = (hash << 5) - hash + char;
    hash = hash & hash;
  }

  // Converter para string hexadecimal
  return `"odr-${hash.toString(16)}"`;
}

/**
 * Middleware para On-Demand Rendering
 */
export const onRequest: MiddlewareResponseHandler = defineMiddleware(
  async ({ request, locals }, next) => {
    const url = new URL(request.url);
    const path = url.pathname;

    // Verificar se deve usar ODR
    const useODR = shouldUseODR(path);

    // Identificar tipo de conteúdo
    const contentType = getContentType(path);

    // Adicionar informações de ODR ao contexto local
    locals.odr = {
      enabled: useODR,
      path,
      cacheKey: generateCacheKey(url),
      ttl: getTTL(path),
      skipCache: shouldSkipCache(url) || request.method !== 'GET',
      contentType,
    };

    // Verificar se o cliente enviou um ETag
    const ifNoneMatch = request.headers.get('If-None-Match');

    // Iniciar temporizador para métricas de performance
    const startTime = performance.now();

    // Verificar cache para rotas ODR
    if (useODR && !locals.odr.skipCache) {
      try {
        // Tentar obter do cache em camadas
        const cachedResponse = await getLayeredCache<{
          html: string;
          status: number;
          headers: Record<string, string>;
          etag?: string;
          timestamp?: number;
        }>(locals.odr.cacheKey);

        // Se encontrou no cache, retornar resposta em cache
        if (cachedResponse) {
          // Registrar métrica de cache hit
          recordODRMetric({
            type: MetricType.CACHE_HIT,
            value: 1,
            timestamp: Date.now(),
            path,
            contentType,
            contentId: path.split('/').pop(),
            userAgent: request.headers.get('User-Agent') || undefined,
            statusCode: cachedResponse.status,
          });

          // Verificar se o cliente enviou um ETag que corresponde
          if (ifNoneMatch && cachedResponse.etag && ifNoneMatch === cachedResponse.etag) {
            // Retornar 304 Not Modified
            const notModifiedHeaders = new Headers();

            // Copiar headers relevantes
            if (cachedResponse.headers['Cache-Control']) {
              notModifiedHeaders.set('Cache-Control', cachedResponse.headers['Cache-Control']);
            }
            if (cachedResponse.headers.ETag) {
              notModifiedHeaders.set('ETag', cachedResponse.headers.ETag);
            }
            if (cachedResponse.headers.Vary) {
              notModifiedHeaders.set('Vary', cachedResponse.headers.Vary);
            }

            // Adicionar headers de cache
            notModifiedHeaders.set('X-Cache', 'HIT');
            notModifiedHeaders.set('X-Cache-Source', 'ODR-Middleware');
            notModifiedHeaders.set(
              'X-Cache-Age',
              `${Math.floor((Date.now() - (cachedResponse.timestamp || Date.now())) / 1000)}s`
            );

            return new Response(null, {
              status: 304,
              headers: notModifiedHeaders,
            });
          }

          // Criar resposta a partir do cache
          const response = new Response(cachedResponse.html, {
            status: cachedResponse.status,
            headers: cachedResponse.headers,
          });

          // Adicionar headers de cache
          response.headers.set('X-Cache', 'HIT');
          response.headers.set('X-Cache-Source', 'ODR-Middleware');
          response.headers.set(
            'X-Cache-Age',
            `${Math.floor((Date.now() - (cachedResponse.timestamp || Date.now())) / 1000)}s`
          );

          return response;
        }
        // Registrar métrica de cache miss
        recordODRMetric({
          type: MetricType.CACHE_MISS,
          value: 1,
          timestamp: Date.now(),
          path,
          contentType,
          contentId: path.split('/').pop(),
          userAgent: request.headers.get('User-Agent') || undefined,
        });
      } catch (error) {
        console.error('Erro ao verificar cache ODR:', error);

        // Registrar métrica de erro
        recordODRMetric({
          type: MetricType.ERROR,
          value: 1,
          timestamp: Date.now(),
          path,
          contentType,
          error: error instanceof Error ? error.message : String(error),
        });
      }
    }

    // Continuar para o próximo middleware ou rota
    const response = await next();

    // Armazenar em cache para rotas ODR
    if (
      useODR &&
      !locals.odr.skipCache &&
      response.status === 200 &&
      response.headers.get('Content-Type')?.includes('text/html')
    ) {
      try {
        // Clonar resposta para não consumir o corpo
        const clonedResponse = response.clone();
        const html = await clonedResponse.text();

        // Gerar ETag
        const etag = generateETag(html, url);

        // Preparar headers para cache
        const headers: Record<string, string> = {};
        clonedResponse.headers.forEach((value, key) => {
          headers[key] = value;
        });

        // Obter headers de cache específicos para o tipo de conteúdo
        const cacheHeaders = getCacheHeaders(path);

        // Aplicar headers de cache
        for (const [key, value] of Object.entries(cacheHeaders)) {
          headers[key] = value;
          response.headers.set(key, value);
        }

        // Adicionar ETag
        headers.ETag = etag;
        response.headers.set('ETag', etag);

        // Armazenar em cache em camadas
        const cacheData = {
          html,
          status: clonedResponse.status,
          headers,
          etag,
          timestamp: Date.now(),
        };

        // Configurar TTLs específicos para cada camada de cache
        const memoryTTL = Math.min(locals.odr.ttl, 300); // Máximo de 5 minutos em memória

        // Armazenar em cache em camadas
        await setLayeredCache(locals.odr.cacheKey, cacheData, locals.odr.ttl, {
          memoryTTL,
          distributedTTL: locals.odr.ttl,
        });

        // Registrar métrica de tempo de renderização
        const renderTime = performance.now() - startTime;
        recordODRMetric({
          type: MetricType.RENDER_TIME,
          value: renderTime,
          timestamp: Date.now(),
          path,
          contentType,
          contentId: path.split('/').pop(),
          statusCode: response.status,
        });

        // Adicionar header de cache miss
        response.headers.set('X-Cache', 'MISS');
        response.headers.set('X-Cache-Source', 'ODR-Middleware');
        response.headers.set('X-Render-Time', `${Math.floor(renderTime)}ms`);

        // Verificar se o cliente enviou um ETag que corresponde ao novo ETag
        if (ifNoneMatch && ifNoneMatch === etag) {
          // Retornar 304 Not Modified
          return new Response(null, {
            status: 304,
            headers: response.headers,
          });
        }
      } catch (error) {
        console.error('Erro ao armazenar cache ODR:', error);
      }
    } else if (!useODR && response.status === 200) {
      // Definir headers de cache para rotas estáticas
      response.headers.set('Cache-Control', ODR_CONFIG.cacheControlHeaders.static);
    }

    return response;
  }
);
