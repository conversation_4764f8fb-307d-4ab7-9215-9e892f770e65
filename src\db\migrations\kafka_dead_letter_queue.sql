-- Migração para criar tabelas de dead letter queue
-- Esta migração cria tabelas para armazenar mensagens que falharam após múltiplas tentativas

-- Tabela para armazenar mensagens da dead letter queue
CREATE TABLE IF NOT EXISTS tab_dead_letter_queue (
  message_id UUID PRIMARY KEY,
  original_topic VARCHAR(255) NOT NULL,
  error_type VARCHAR(50) NOT NULL,
  error_message TEXT NOT NULL,
  error_stack TEXT,
  attempt_count INTEGER NOT NULL,
  original_key VARCHAR(255),
  original_value TEXT,
  original_headers JSONB,
  retry_metadata JSONB,
  processed BOOLEAN NOT NULL DEFAULT FALSE,
  processed_at TIMESTAMP,
  processing_notes TEXT,
  created_at TIMESTAMP NOT NULL DEFAULT NOW()
);

-- Índices para consulta eficiente
CREATE INDEX IF NOT EXISTS idx_dlq_original_topic ON tab_dead_letter_queue(original_topic);
CREATE INDEX IF NOT EXISTS idx_dlq_error_type ON tab_dead_letter_queue(error_type);
CREATE INDEX IF NOT EXISTS idx_dlq_created_at ON tab_dead_letter_queue(created_at);
CREATE INDEX IF NOT EXISTS idx_dlq_processed ON tab_dead_letter_queue(processed);

-- Tabela para armazenar estatísticas de falhas por tópico
CREATE TABLE IF NOT EXISTS tab_producer_failure_stats (
  stat_id UUID PRIMARY KEY,
  topic VARCHAR(255) NOT NULL,
  error_type VARCHAR(50) NOT NULL,
  failure_count INTEGER NOT NULL,
  retry_count INTEGER NOT NULL,
  success_after_retry_count INTEGER NOT NULL,
  dlq_count INTEGER NOT NULL,
  period_start TIMESTAMP NOT NULL,
  period_end TIMESTAMP NOT NULL,
  created_at TIMESTAMP NOT NULL DEFAULT NOW()
);

-- Índices para estatísticas
CREATE INDEX IF NOT EXISTS idx_producer_failure_stats_topic ON tab_producer_failure_stats(topic);
CREATE INDEX IF NOT EXISTS idx_producer_failure_stats_period ON tab_producer_failure_stats(period_start, period_end);

-- Função para limpar mensagens antigas da DLQ
CREATE OR REPLACE FUNCTION fn_clean_dlq_messages(p_days INTEGER)
RETURNS INTEGER AS $$
DECLARE
  v_count INTEGER;
BEGIN
  DELETE FROM tab_dead_letter_queue
  WHERE created_at < NOW() - (p_days || ' days')::INTERVAL
  AND processed = TRUE;
  
  GET DIAGNOSTICS v_count = ROW_COUNT;
  RETURN v_count;
END;
$$ LANGUAGE plpgsql;

-- Visualização para resumo de mensagens na DLQ
CREATE OR REPLACE VIEW vw_dlq_summary AS
SELECT
  original_topic,
  error_type,
  COUNT(*) AS message_count,
  MIN(created_at) AS first_occurrence,
  MAX(created_at) AS last_occurrence,
  AVG(attempt_count) AS avg_attempts,
  SUM(CASE WHEN processed THEN 1 ELSE 0 END) AS processed_count
FROM
  tab_dead_letter_queue
GROUP BY
  original_topic,
  error_type
ORDER BY
  message_count DESC;

-- Visualização para mensagens recentes na DLQ
CREATE OR REPLACE VIEW vw_dlq_recent_messages AS
SELECT
  message_id,
  original_topic,
  error_type,
  error_message,
  attempt_count,
  original_key,
  processed,
  created_at
FROM
  tab_dead_letter_queue
WHERE
  created_at > NOW() - INTERVAL '24 hours'
ORDER BY
  created_at DESC;

-- Função para obter mensagens da DLQ por tópico
CREATE OR REPLACE FUNCTION fn_get_dlq_messages_by_topic(
  p_topic VARCHAR,
  p_start_time TIMESTAMP,
  p_end_time TIMESTAMP,
  p_processed BOOLEAN DEFAULT NULL
)
RETURNS TABLE (
  message_id UUID,
  original_topic VARCHAR,
  error_type VARCHAR,
  error_message TEXT,
  attempt_count INTEGER,
  original_key VARCHAR,
  original_value TEXT,
  original_headers JSONB,
  retry_metadata JSONB,
  processed BOOLEAN,
  processed_at TIMESTAMP,
  created_at TIMESTAMP
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    dlq.message_id,
    dlq.original_topic,
    dlq.error_type,
    dlq.error_message,
    dlq.attempt_count,
    dlq.original_key,
    dlq.original_value,
    dlq.original_headers,
    dlq.retry_metadata,
    dlq.processed,
    dlq.processed_at,
    dlq.created_at
  FROM
    tab_dead_letter_queue dlq
  WHERE
    dlq.original_topic = p_topic
    AND dlq.created_at BETWEEN p_start_time AND p_end_time
    AND (p_processed IS NULL OR dlq.processed = p_processed)
  ORDER BY
    dlq.created_at DESC;
END;
$$ LANGUAGE plpgsql;

-- Função para gerar estatísticas de falhas
CREATE OR REPLACE FUNCTION fn_generate_producer_failure_stats(
  p_period_start TIMESTAMP,
  p_period_end TIMESTAMP
)
RETURNS VOID AS $$
BEGIN
  -- Inserir estatísticas por tópico e tipo de erro
  INSERT INTO tab_producer_failure_stats (
    stat_id,
    topic,
    error_type,
    failure_count,
    retry_count,
    success_after_retry_count,
    dlq_count,
    period_start,
    period_end,
    created_at
  )
  SELECT
    gen_random_uuid(),
    original_topic,
    error_type,
    COUNT(*),
    SUM(attempt_count),
    0, -- Não temos essa informação diretamente
    COUNT(*),
    p_period_start,
    p_period_end,
    NOW()
  FROM
    tab_dead_letter_queue
  WHERE
    created_at BETWEEN p_period_start AND p_period_end
  GROUP BY
    original_topic,
    error_type;
END;
$$ LANGUAGE plpgsql;
