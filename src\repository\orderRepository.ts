import type { QueryResult } from 'pg';
import { pgHelper } from './pgHelper';

async function create(ulid_user: string, cod_status: number, total: number): Promise<QueryResult> {
  return await pgHelper.query(
    `INSERT INTO tab_order (
       ulid_order, 
       cod_status, 
       total) 
     VALUES ('$1', $2, $3) 
     RETURNING *`,
    [ulid_user, cod_status, total]
  );
}

async function read(
  ulid_order?: string,
  ulid_user?: string,
  cod_status?: number
): Promise<QueryResult> {
  return await pgHelper.query(
    `SELECT * 
       FROM tab_order 
      WHERE TRUE 
        ${ulid_order ? 'AND ulid_order = $1' : ''}
        ${ulid_user ? 'AND ulid_user = $2' : ''}
        ${cod_status ? 'AND cod_status = $3' : ''}`,
    [ulid_order, ulid_user, cod_status]
  );
}

async function update(ulid_order: string, cod_status: number): Promise<QueryResult> {
  return await pgHelper.query(
    `
    UPDATE tab_order 
       SET cod_status = $2 
     WHERE ulid_order = '$1',
           updated_at = NOW() 
     RETURNING *`,
    [ulid_order, cod_status]
  );
}

async function deleteByUlid(ulid_order: string): Promise<QueryResult> {
  return await pgHelper.query(
    `DELETE FROM tab_order 
      WHERE ulid_order = $1 
     RETURNING *`,
    [ulid_order]
  );
}

export const orderRepository = {
  create,
  read,
  update,
  deleteByUlid,
};
