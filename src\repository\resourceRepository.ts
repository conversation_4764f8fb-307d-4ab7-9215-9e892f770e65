/**
 * Repositório para gerenciamento de recursos no sistema RBAC
 */

import { logger } from '@utils/logger';
import type { QueryResult } from 'pg';
import type { ResourceData } from './interfaces/rbacInterfaces';
import { pgHelper } from './pgHelper';

/**
 * Repositório para gerenciamento de recursos
 */
export const resourceRepository = {
  /**
   * Cria um novo recurso
   * @param name - Nome do recurso
   * @param description - Descrição do recurso (opcional)
   * @returns Resultado da query
   */
  async create(name: string, description?: string): Promise<QueryResult> {
    try {
      return await pgHelper.query(
        `INSERT INTO tab_resource (
          ulid_resource,
          name,
          description,
          active,
          created_at,
          updated_at
        ) VALUES ($1, $2, $3, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
        RETURNING *`,
        [pgHelper.generateULID(), name, description]
      );
    } catch (error) {
      logger.error('Erro ao criar recurso:', error);
      throw error;
    }
  },

  /**
   * Busca recursos com filtros opcionais
   * @param ulid_resource - ID do recurso (opcional)
   * @param name - Nome do recurso (opcional)
   * @param active - Se o recurso está ativo (opcional)
   * @returns Resultado da query
   */
  async read(ulid_resource?: string, name?: string, active = true): Promise<QueryResult> {
    try {
      let query = 'SELECT * FROM tab_resource WHERE 1=1';
      const params: any[] = [];
      let paramIndex = 1;

      if (ulid_resource) {
        query += ` AND ulid_resource = $${paramIndex++}`;
        params.push(ulid_resource);
      }

      if (name) {
        query += ` AND name ILIKE $${paramIndex++}`;
        params.push(`%${name}%`);
      }

      if (active !== undefined) {
        query += ` AND active = $${paramIndex++}`;
        params.push(active);
      }

      query += ' ORDER BY name ASC';

      return await pgHelper.query(query, params);
    } catch (error) {
      logger.error('Erro ao buscar recursos:', error);
      throw error;
    }
  },

  /**
   * Atualiza um recurso existente
   * @param ulid_resource - ID do recurso
   * @param name - Nome do recurso
   * @param description - Descrição do recurso (opcional)
   * @param active - Se o recurso está ativo
   * @returns Resultado da query
   */
  async update(
    ulid_resource: string,
    name: string,
    description?: string,
    active = true
  ): Promise<QueryResult> {
    try {
      return await pgHelper.query(
        `UPDATE tab_resource
         SET name = $1,
             description = $2,
             active = $3,
             updated_at = CURRENT_TIMESTAMP
         WHERE ulid_resource = $4
         RETURNING *`,
        [name, description, active, ulid_resource]
      );
    } catch (error) {
      logger.error('Erro ao atualizar recurso:', error);
      throw error;
    }
  },

  /**
   * Remove um recurso (desativa)
   * @param ulid_resource - ID do recurso
   * @returns Resultado da query
   */
  async delete(ulid_resource: string): Promise<QueryResult> {
    try {
      return await pgHelper.query(
        `UPDATE tab_resource
         SET active = false,
             updated_at = CURRENT_TIMESTAMP
         WHERE ulid_resource = $1
         RETURNING *`,
        [ulid_resource]
      );
    } catch (error) {
      logger.error('Erro ao remover recurso:', error);
      throw error;
    }
  },

  /**
   * Associa um recurso a um papel com permissões específicas
   * @param ulid_role - ID do papel
   * @param ulid_resource_permission - ID da associação recurso-permissão
   * @returns Resultado da query
   */
  async associateWithRole(
    ulid_role: string,
    ulid_resource_permission: string
  ): Promise<QueryResult> {
    try {
      return await pgHelper.query(
        `INSERT INTO tab_role_resource_permission (
          ulid_role_resource_permission,
          ulid_role,
          ulid_resource_permission,
          created_at
        ) VALUES ($1, $2, $3, CURRENT_TIMESTAMP)
        ON CONFLICT (ulid_role, ulid_resource_permission) DO NOTHING
        RETURNING *`,
        [pgHelper.generateULID(), ulid_role, ulid_resource_permission]
      );
    } catch (error) {
      logger.error('Erro ao associar recurso ao papel:', error);
      throw error;
    }
  },

  /**
   * Remove a associação de um recurso a um papel
   * @param ulid_role - ID do papel
   * @param ulid_resource_permission - ID da associação recurso-permissão
   * @returns Resultado da query
   */
  async dissociateFromRole(
    ulid_role: string,
    ulid_resource_permission: string
  ): Promise<QueryResult> {
    try {
      return await pgHelper.query(
        `DELETE FROM tab_role_resource_permission
         WHERE ulid_role = $1
         AND ulid_resource_permission = $2
         RETURNING *`,
        [ulid_role, ulid_resource_permission]
      );
    } catch (error) {
      logger.error('Erro ao remover associação de recurso ao papel:', error);
      throw error;
    }
  },

  /**
   * Busca recursos associados a um papel
   * @param ulid_role - ID do papel
   * @returns Resultado da query
   */
  async getRoleResources(ulid_role: string): Promise<QueryResult> {
    try {
      return await pgHelper.query(
        `SELECT DISTINCT r.*
         FROM tab_resource r
         JOIN tab_resource_permission rp ON r.ulid_resource = rp.ulid_resource
         JOIN tab_role_resource_permission rrp ON rp.ulid_resource_permission = rrp.ulid_resource_permission
         WHERE rrp.ulid_role = $1
         AND r.active = true
         ORDER BY r.name ASC`,
        [ulid_role]
      );
    } catch (error) {
      logger.error('Erro ao buscar recursos do papel:', error);
      throw error;
    }
  },

  /**
   * Busca recursos com suas permissões
   * @returns Resultado da query
   */
  async getResourcesWithPermissions(): Promise<QueryResult> {
    try {
      return await pgHelper.query(
        `SELECT r.*, p.ulid_permission, p.name as permission_name, p.action,
                rp.ulid_resource_permission
         FROM tab_resource r
         JOIN tab_resource_permission rp ON r.ulid_resource = rp.ulid_resource
         JOIN tab_permission p ON rp.ulid_permission = p.ulid_permission
         WHERE r.active = true AND p.active = true
         ORDER BY r.name ASC, p.name ASC`
      );
    } catch (error) {
      logger.error('Erro ao buscar recursos com permissões:', error);
      throw error;
    }
  },

  /**
   * Busca permissões de um recurso específico
   * @param ulid_resource - ID do recurso
   * @returns Resultado da query
   */
  async getResourcePermissions(ulid_resource: string): Promise<QueryResult> {
    try {
      return await pgHelper.query(
        `SELECT p.*, rp.ulid_resource_permission
         FROM tab_permission p
         JOIN tab_resource_permission rp ON p.ulid_permission = rp.ulid_permission
         WHERE rp.ulid_resource = $1
         AND p.active = true
         ORDER BY p.name ASC`,
        [ulid_resource]
      );
    } catch (error) {
      logger.error('Erro ao buscar permissões do recurso:', error);
      throw error;
    }
  },

  /**
   * Busca papéis que têm acesso a um recurso específico
   * @param ulid_resource - ID do recurso
   * @returns Resultado da query
   */
  async getResourceRoles(ulid_resource: string): Promise<QueryResult> {
    try {
      return await pgHelper.query(
        `SELECT DISTINCT r.*
         FROM tab_role r
         JOIN tab_role_resource_permission rrp ON r.ulid_role = rrp.ulid_role
         JOIN tab_resource_permission rp ON rrp.ulid_resource_permission = rp.ulid_resource_permission
         WHERE rp.ulid_resource = $1
         AND r.active = true
         ORDER BY r.name ASC`,
        [ulid_resource]
      );
    } catch (error) {
      logger.error('Erro ao buscar papéis do recurso:', error);
      throw error;
    }
  },
};
