---
import { PageTransition } from '../../../components/transitions';
import DaisyButton from '../../../components/ui/DaisyButton.astro';
import DaisyCard from '../../../components/ui/DaisyCard.astro';
import DaisyTabs from '../../../components/ui/DaisyTabs.astro';
import Breadcrumbs from '../../../components/ui/navigation/Breadcrumbs.astro';
import { FiscalDocumentStatus, FiscalDocumentType } from '../../../domain/entities/FiscalDocument';
import { FiscalDocumentRepository } from '../../../domain/repositories/FiscalDocumentRepository';
import { PostgresFiscalDocumentRepository } from '../../../infrastructure/database/repositories/PostgresFiscalDocumentRepository';
/**
 * Página de Documentos Fiscais do Cliente
 *
 * Interface para o cliente visualizar seus documentos fiscais.
 * Parte da implementação da tarefa 8.7.3 - Portal do cliente para documentos fiscais
 */
import BaseLayout from '../../../layouts/BaseLayout.astro';
import { Container, Section } from '../../../layouts/grid';

// Título da página
const title = 'Meus Documentos Fiscais';

// Breadcrumbs para a página atual
const breadcrumbItems = [
  { href: '/', label: 'Início' },
  { href: '/cliente', label: 'Área do Cliente' },
  { label: 'Documentos Fiscais' },
];

// Obter parâmetros de consulta
const type = (Astro.url.searchParams.get('type') as FiscalDocumentType) || '';
const status = (Astro.url.searchParams.get('status') as FiscalDocumentStatus) || 'ISSUED';
const startDate = Astro.url.searchParams.get('startDate') || '';
const endDate = Astro.url.searchParams.get('endDate') || '';
const page = Number.parseInt(Astro.url.searchParams.get('page') || '1');
const limit = Number.parseInt(Astro.url.searchParams.get('limit') || '10');

// Em um cenário real, obteríamos o ID do cliente da sessão
// Para este exemplo, vamos usar um ID fixo
const customerId = '12345678901'; // CPF do cliente

// Inicializar repositório
const fiscalDocumentRepository: FiscalDocumentRepository = new PostgresFiscalDocumentRepository();

// Construir filtro
const filter: any = {
  customerDocumentNumber: customerId,
  status: status || 'ISSUED', // Por padrão, mostrar apenas documentos emitidos
};

if (type) {
  filter.type = type;
}

if (startDate) {
  filter.startDate = new Date(startDate);
}

if (endDate) {
  const endDateObj = new Date(endDate);
  endDateObj.setDate(endDateObj.getDate() + 1); // Adicionar um dia para incluir todo o dia final
  filter.endDate = endDateObj;
}

// Obter documentos fiscais
const result = await fiscalDocumentRepository.find(
  filter,
  { field: 'issueDate', direction: 'desc' },
  { page, limit }
);

const documents = result.documents;
const totalItems = result.total;
const totalPages = result.totalPages;

// Tipos de documentos disponíveis
const documentTypes = [
  { value: '', label: 'Todos os tipos' },
  { value: 'NFE', label: 'NF-e' },
  { value: 'NFSE', label: 'NFS-e' },
  { value: 'NFCE', label: 'NFC-e' },
];

// Status disponíveis
const documentStatuses = [
  { value: 'ISSUED', label: 'Emitidos' },
  { value: 'CANCELLED', label: 'Cancelados' },
];

// Função para formatar data
const formatDate = (dateString: string | null | undefined): string => {
  if (!dateString) return '-';
  const date = new Date(dateString);
  return date.toLocaleDateString('pt-BR', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
  });
};

// Função para formatar valor monetário
const formatCurrency = (value: number): string => {
  return value.toLocaleString('pt-BR', {
    style: 'currency',
    currency: 'BRL',
  });
};

// Função para obter label do tipo de documento
const getTypeLabel = (type: FiscalDocumentType): string => {
  const typeLabels: Record<FiscalDocumentType, string> = {
    NFE: 'NF-e',
    NFSE: 'NFS-e',
    NFCE: 'NFC-e',
  };

  return typeLabels[type] || type;
};

// Obter data atual e data de 3 meses atrás
const today = new Date();
const threeMonthsAgo = new Date();
threeMonthsAgo.setMonth(today.getMonth() - 3);

// Formatar datas para o formato YYYY-MM-DD
const formatDateForInput = (date: Date): string => {
  return date.toISOString().split('T')[0];
};
---

<BaseLayout title={title} enableViewTransitions={true} transitionType="fade">
  <Container>
    <PageTransition type="fade" duration="0.5s">
      <Section>
        <Breadcrumbs items={breadcrumbItems} class="mb-6" />
        
        <div class="flex justify-between items-center mb-6">
          <h1 class="text-3xl font-bold">{title}</h1>
        </div>
        
        <DaisyCard class="mb-6">
          <div class="p-6">
            <h2 class="text-xl font-bold mb-4">
              <i class="icon icon-search mr-2"></i>
              Filtrar Documentos
            </h2>
            
            <form action="/cliente/documentos-fiscais" method="GET" class="flex flex-col md:flex-row gap-4">
              <div class="form-control flex-1">
                <label class="label">
                  <span class="label-text">Período</span>
                </label>
                <div class="flex gap-2">
                  <div class="flex-1">
                    <input 
                      type="date" 
                      name="startDate" 
                      class="input input-bordered w-full" 
                      value={startDate || formatDateForInput(threeMonthsAgo)}
                    />
                  </div>
                  <div class="flex-1">
                    <input 
                      type="date" 
                      name="endDate" 
                      class="input input-bordered w-full" 
                      value={endDate || formatDateForInput(today)}
                    />
                  </div>
                </div>
              </div>
              
              <div class="form-control">
                <label class="label">
                  <span class="label-text">Tipo</span>
                </label>
                <select name="type" class="select select-bordered w-full">
                  {documentTypes.map(docType => (
                    <option 
                      value={docType.value} 
                      selected={docType.value === type}
                    >
                      {docType.label}
                    </option>
                  ))}
                </select>
              </div>
              
              <div class="form-control">
                <label class="label">
                  <span class="label-text">Status</span>
                </label>
                <select name="status" class="select select-bordered w-full">
                  {documentStatuses.map(docStatus => (
                    <option 
                      value={docStatus.value} 
                      selected={docStatus.value === status}
                    >
                      {docStatus.label}
                    </option>
                  ))}
                </select>
              </div>
              
              <div class="form-control flex-none self-end">
                <button type="submit" class="btn btn-primary">
                  <i class="icon icon-search mr-2"></i>
                  Filtrar
                </button>
              </div>
            </form>
          </div>
        </DaisyCard>
        
        <DaisyTabs
          tabs={[
            { id: 'ISSUED', label: 'Documentos Emitidos', active: status === 'ISSUED' },
            { id: 'CANCELLED', label: 'Documentos Cancelados', active: status === 'CANCELLED' }
          ]}
          baseUrl={`/cliente/documentos-fiscais?type=${type}&startDate=${startDate}&endDate=${endDate}`}
          paramName="status"
        />
        
        <div class="overflow-x-auto mt-4">
          {documents.length === 0 ? (
            <div class="text-center py-12">
              <div class="text-4xl text-gray-400 mb-4">
                <i class="icon icon-file-text"></i>
              </div>
              <h3 class="text-xl font-bold mb-2">Nenhum documento fiscal encontrado</h3>
              <p class="text-gray-500 mb-4">
                {type || startDate || endDate
                  ? `Não encontramos resultados para os filtros aplicados.` 
                  : 'Você ainda não possui documentos fiscais.'}
              </p>
            </div>
          ) : (
            <table class="table w-full">
              <thead>
                <tr>
                  <th>Número</th>
                  <th>Tipo</th>
                  <th>Emissão</th>
                  <th>Valor</th>
                  <th>Ações</th>
                </tr>
              </thead>
              <tbody>
                {documents.map(document => (
                  <tr>
                    <td>
                      <div class="font-medium">
                        {document.number || <span class="text-gray-400">Não emitido</span>}
                      </div>
                      {document.series && <div class="text-xs text-gray-500">Série: {document.series}</div>}
                    </td>
                    <td>
                      <div class="badge badge-outline">
                        {getTypeLabel(document.type)}
                      </div>
                    </td>
                    <td>{formatDate(document.issueDate?.toString())}</td>
                    <td>{formatCurrency(document.finalValue)}</td>
                    <td>
                      <div class="flex gap-1">
                        <a 
                          href={`/cliente/documentos-fiscais/${document.id}`} 
                          class="btn btn-sm btn-ghost"
                          aria-label="Visualizar"
                        >
                          <i class="icon icon-eye"></i>
                        </a>
                        
                        <a 
                          href={`/api/fiscal/customer/download/${document.id}?customerId=${customerId}&format=pdf`} 
                          target="_blank" 
                          class="btn btn-sm btn-ghost text-info"
                          aria-label="Download PDF"
                        >
                          <i class="icon icon-file"></i>
                        </a>
                        
                        <a 
                          href={`/api/fiscal/customer/download/${document.id}?customerId=${customerId}&format=xml`} 
                          class="btn btn-sm btn-ghost text-primary"
                          aria-label="Download XML"
                        >
                          <i class="icon icon-code"></i>
                        </a>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          )}
        </div>
        
        {totalPages > 1 && (
          <div class="flex justify-center mt-8">
            <div class="join">
              {Array.from({ length: totalPages }, (_, i) => i + 1).map(p => (
                <a 
                  href={`/cliente/documentos-fiscais?page=${p}&limit=${limit}&type=${type}&status=${status}&startDate=${startDate}&endDate=${endDate}`} 
                  class={`join-item btn ${p === page ? 'btn-active' : ''}`}
                >
                  {p}
                </a>
              ))}
            </div>
          </div>
        )}
      </Section>
    </PageTransition>
  </Container>
</BaseLayout>
