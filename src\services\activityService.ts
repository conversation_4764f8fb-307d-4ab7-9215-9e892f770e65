/**
 * Serviço de gerenciamento de atividades educacionais
 *
 * Este serviço implementa funcionalidades para criação, recuperação,
 * atualização e gerenciamento de atividades educacionais.
 */

import {
  ActivityCategory,
  ActivityResult,
  ActivityStatus,
  ActivityType,
  BaseActivity,
  DifficultyLevel,
} from '@models/Activity';
import { cacheService } from '@services/cacheService';
import { db } from '@services/dbService';
import { logger } from '@utils/logger';
import { nanoid } from 'nanoid';

/**
 * Prefixo para chaves de cache
 */
const CACHE_PREFIX = 'activity:';

/**
 * Tempo de cache para atividades (em segundos)
 */
const CACHE_TTL = 3600; // 1 hora

/**
 * Opções para busca de atividades
 */
export interface ActivitySearchOptions {
  /**
   * Tipos de atividade
   */
  types?: ActivityType[];

  /**
   * Categorias
   */
  categories?: ActivityCategory[];

  /**
   * Níveis de dificuldade
   */
  difficulties?: DifficultyLevel[];

  /**
   * Status
   */
  status?: ActivityStatus[];

  /**
   * Faixa etária mínima
   */
  minAge?: number;

  /**
   * Faixa etária máxima
   */
  maxAge?: number;

  /**
   * Série escolar
   */
  grade?: string;

  /**
   * Tags (todas devem estar presentes)
   */
  tags?: string[];

  /**
   * Termo de busca (título e descrição)
   */
  searchTerm?: string;

  /**
   * ID do criador
   */
  createdBy?: string;

  /**
   * Limite de resultados
   */
  limit?: number;

  /**
   * Deslocamento para paginação
   */
  offset?: number;

  /**
   * Campo para ordenação
   */
  orderBy?: 'title' | 'createdAt' | 'updatedAt' | 'difficulty';

  /**
   * Direção da ordenação
   */
  orderDirection?: 'asc' | 'desc';
}

/**
 * Serviço de atividades
 */
export const activityService = {
  /**
   * Cria uma nova atividade
   * @param activity - Dados da atividade
   * @param userId - ID do usuário criador
   * @returns Atividade criada
   */
  async createActivity(
    activity: Omit<BaseActivity, 'id' | 'createdBy' | 'createdAt'>,
    userId: string
  ): Promise<BaseActivity> {
    try {
      // Gerar ID único
      const id = uuidv4();

      // Preparar dados para inserção
      const now = new Date();

      // Inserir no banco de dados
      const result = await db.query(
        `INSERT INTO activities (
          id, title, description, type, category, difficulty, max_score,
          estimated_time, tags, min_age, max_age, grade, instructions,
          teacher_notes, status, created_by, created_at, metadata, content
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19)
        RETURNING id, title, description, type, category, difficulty, max_score,
                  estimated_time, tags, min_age, max_age, grade, instructions,
                  teacher_notes, status, created_by, created_at, updated_by, updated_at, metadata`,
        [
          id,
          activity.title,
          activity.description || null,
          activity.type,
          activity.category,
          activity.difficulty,
          activity.maxScore,
          activity.estimatedTime || null,
          activity.tags || [],
          activity.minAge || null,
          activity.maxAge || null,
          activity.grade || null,
          activity.instructions || null,
          activity.teacherNotes || null,
          activity.status || ActivityStatus.DRAFT,
          userId,
          now,
          JSON.stringify(activity.metadata || {}),
          JSON.stringify('content' in activity ? activity.content : {}),
        ]
      );

      // Mapear resultado
      const createdActivity = this.mapDbRowToActivity(result.rows[0]);

      // Armazenar em cache
      await this.cacheActivity(createdActivity);

      return createdActivity;
    } catch (error) {
      logger.error('Erro ao criar atividade:', error);
      throw error;
    }
  },

  /**
   * Atualiza uma atividade existente
   * @param id - ID da atividade
   * @param updates - Campos a serem atualizados
   * @param userId - ID do usuário que está atualizando
   * @returns Atividade atualizada
   */
  async updateActivity(
    id: string,
    updates: Partial<BaseActivity>,
    userId: string
  ): Promise<BaseActivity | null> {
    try {
      // Verificar se a atividade existe
      const existingActivity = await this.getActivityById(id);

      if (!existingActivity) {
        return null;
      }

      // Preparar campos para atualização
      const updateFields = [];
      const updateValues = [];
      let paramIndex = 1;

      // Mapear campos para atualização
      if ('title' in updates) {
        updateFields.push(`title = $${paramIndex++}`);
        updateValues.push(updates.title);
      }

      if ('description' in updates) {
        updateFields.push(`description = $${paramIndex++}`);
        updateValues.push(updates.description || null);
      }

      if ('type' in updates) {
        updateFields.push(`type = $${paramIndex++}`);
        updateValues.push(updates.type);
      }

      if ('category' in updates) {
        updateFields.push(`category = $${paramIndex++}`);
        updateValues.push(updates.category);
      }

      if ('difficulty' in updates) {
        updateFields.push(`difficulty = $${paramIndex++}`);
        updateValues.push(updates.difficulty);
      }

      if ('maxScore' in updates) {
        updateFields.push(`max_score = $${paramIndex++}`);
        updateValues.push(updates.maxScore);
      }

      if ('estimatedTime' in updates) {
        updateFields.push(`estimated_time = $${paramIndex++}`);
        updateValues.push(updates.estimatedTime || null);
      }

      if ('tags' in updates) {
        updateFields.push(`tags = $${paramIndex++}`);
        updateValues.push(updates.tags || []);
      }

      if ('minAge' in updates) {
        updateFields.push(`min_age = $${paramIndex++}`);
        updateValues.push(updates.minAge || null);
      }

      if ('maxAge' in updates) {
        updateFields.push(`max_age = $${paramIndex++}`);
        updateValues.push(updates.maxAge || null);
      }

      if ('grade' in updates) {
        updateFields.push(`grade = $${paramIndex++}`);
        updateValues.push(updates.grade || null);
      }

      if ('instructions' in updates) {
        updateFields.push(`instructions = $${paramIndex++}`);
        updateValues.push(updates.instructions || null);
      }

      if ('teacherNotes' in updates) {
        updateFields.push(`teacher_notes = $${paramIndex++}`);
        updateValues.push(updates.teacherNotes || null);
      }

      if ('status' in updates) {
        updateFields.push(`status = $${paramIndex++}`);
        updateValues.push(updates.status);
      }

      if ('metadata' in updates) {
        updateFields.push(`metadata = $${paramIndex++}`);
        updateValues.push(JSON.stringify(updates.metadata || {}));
      }

      if ('content' in updates) {
        updateFields.push(`content = $${paramIndex++}`);
        updateValues.push(JSON.stringify(updates.content || {}));
      }

      // Adicionar campos de atualização
      updateFields.push(`updated_by = $${paramIndex++}`);
      updateValues.push(userId);

      updateFields.push(`updated_at = $${paramIndex++}`);
      updateValues.push(new Date());

      // Adicionar ID da atividade
      updateValues.push(id);

      // Executar atualização
      const result = await db.query(
        `UPDATE activities SET ${updateFields.join(', ')}
         WHERE id = $${paramIndex}
         RETURNING id, title, description, type, category, difficulty, max_score,
                   estimated_time, tags, min_age, max_age, grade, instructions,
                   teacher_notes, status, created_by, created_at, updated_by, updated_at, metadata`,
        updateValues
      );

      if (result.rows.length === 0) {
        return null;
      }

      // Mapear resultado
      const updatedActivity = this.mapDbRowToActivity(result.rows[0]);

      // Atualizar cache
      await this.cacheActivity(updatedActivity);

      return updatedActivity;
    } catch (error) {
      logger.error('Erro ao atualizar atividade:', error);
      throw error;
    }
  },

  /**
   * Obtém uma atividade pelo ID
   * @param id - ID da atividade
   * @param includeContent - Se deve incluir o conteúdo completo
   * @returns Atividade ou null se não encontrada
   */
  async getActivityById(id: string, includeContent = true): Promise<BaseActivity | null> {
    try {
      // Tentar obter do cache
      const cachedActivity = await this.getCachedActivity(id);

      if (cachedActivity) {
        return cachedActivity;
      }

      // Construir consulta
      const contentField = includeContent ? ', content' : '';

      // Buscar do banco de dados
      const result = await db.query(
        `SELECT id, title, description, type, category, difficulty, max_score,
                estimated_time, tags, min_age, max_age, grade, instructions,
                teacher_notes, status, created_by, created_at, updated_by, updated_at, metadata${contentField}
         FROM activities
         WHERE id = $1`,
        [id]
      );

      if (result.rows.length === 0) {
        return null;
      }

      // Mapear resultado
      const activity = this.mapDbRowToActivity(result.rows[0]);

      // Armazenar em cache
      await this.cacheActivity(activity);

      return activity;
    } catch (error) {
      logger.error('Erro ao obter atividade por ID:', error);
      return null;
    }
  },

  /**
   * Busca atividades com filtros
   * @param options - Opções de busca
   * @returns Lista de atividades
   */
  async searchActivities(options: ActivitySearchOptions = {}): Promise<BaseActivity[]> {
    try {
      // Preparar condições de busca
      const conditions = [];
      const params = [];
      let paramIndex = 1;

      // Filtrar por tipos
      if (options.types && options.types.length > 0) {
        conditions.push(`type = ANY($${paramIndex++})`);
        params.push(options.types);
      }

      // Filtrar por categorias
      if (options.categories && options.categories.length > 0) {
        conditions.push(`category = ANY($${paramIndex++})`);
        params.push(options.categories);
      }

      // Filtrar por dificuldades
      if (options.difficulties && options.difficulties.length > 0) {
        conditions.push(`difficulty = ANY($${paramIndex++})`);
        params.push(options.difficulties);
      }

      // Filtrar por status
      if (options.status && options.status.length > 0) {
        conditions.push(`status = ANY($${paramIndex++})`);
        params.push(options.status);
      } else {
        // Por padrão, mostrar apenas atividades publicadas
        conditions.push(`status = $${paramIndex++}`);
        params.push(ActivityStatus.PUBLISHED);
      }

      // Filtrar por faixa etária
      if (options.minAge !== undefined) {
        conditions.push(`(min_age IS NULL OR min_age <= $${paramIndex++})`);
        params.push(options.minAge);
      }

      if (options.maxAge !== undefined) {
        conditions.push(`(max_age IS NULL OR max_age >= $${paramIndex++})`);
        params.push(options.maxAge);
      }

      // Filtrar por série
      if (options.grade) {
        conditions.push(`grade = $${paramIndex++}`);
        params.push(options.grade);
      }

      // Filtrar por tags
      if (options.tags && options.tags.length > 0) {
        const tagConditions = options.tags.map((_, i) => `$${paramIndex + i} = ANY(tags)`);
        conditions.push(`(${tagConditions.join(' AND ')})`);
        params.push(...options.tags);
        paramIndex += options.tags.length;
      }

      // Filtrar por termo de busca
      if (options.searchTerm) {
        conditions.push(`(
          title ILIKE $${paramIndex} OR
          description ILIKE $${paramIndex} OR
          instructions ILIKE $${paramIndex}
        )`);
        params.push(`%${options.searchTerm}%`);
        paramIndex++;
      }

      // Filtrar por criador
      if (options.createdBy) {
        conditions.push(`created_by = $${paramIndex++}`);
        params.push(options.createdBy);
      }

      // Construir cláusula WHERE
      const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';

      // Construir cláusula ORDER BY
      const orderField = options.orderBy || 'createdAt';
      const orderDirection = options.orderDirection || 'desc';

      let orderByClause;
      switch (orderField) {
        case 'title':
          orderByClause = 'ORDER BY title';
          break;
        case 'difficulty':
          orderByClause =
            'ORDER BY CASE difficulty ' +
            "WHEN 'easy' THEN 1 " +
            "WHEN 'medium' THEN 2 " +
            "WHEN 'hard' THEN 3 " +
            'ELSE 4 END';
          break;
        case 'updatedAt':
          orderByClause = 'ORDER BY updated_at';
          break;
        default:
          orderByClause = 'ORDER BY created_at';
      }

      orderByClause += ` ${orderDirection.toUpperCase()}`;

      // Construir cláusulas LIMIT e OFFSET
      const limitClause = options.limit ? `LIMIT ${options.limit}` : '';
      const offsetClause = options.offset ? `OFFSET ${options.offset}` : '';

      // Executar consulta
      const result = await db.query(
        `SELECT id, title, description, type, category, difficulty, max_score,
                estimated_time, tags, min_age, max_age, grade, instructions,
                teacher_notes, status, created_by, created_at, updated_by, updated_at, metadata
         FROM activities
         ${whereClause}
         ${orderByClause}
         ${limitClause}
         ${offsetClause}`,
        params
      );

      // Mapear resultados
      return result.rows.map((row) => this.mapDbRowToActivity(row));
    } catch (error) {
      logger.error('Erro ao buscar atividades:', error);
      return [];
    }
  },

  /**
   * Registra um resultado de atividade
   * @param result - Dados do resultado
   * @returns Resultado registrado
   */
  async saveActivityResult(result: Omit<ActivityResult, 'id'>): Promise<ActivityResult | null> {
    try {
      // Gerar ID único
      const id = uuidv4();

      // Inserir no banco de dados
      const dbResult = await db.query(
        `INSERT INTO activity_results (
          id, activity_id, user_id, score, max_score, percentage,
          time_spent, completed, answers, feedback, started_at, completed_at, metadata
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)
        RETURNING id, activity_id, user_id, score, max_score, percentage,
                  time_spent, completed, answers, feedback, started_at, completed_at, metadata`,
        [
          id,
          result.activityId,
          result.userId,
          result.score,
          result.maxScore,
          result.percentage,
          result.timeSpent,
          result.completed,
          JSON.stringify(result.answers),
          result.feedback || null,
          result.startedAt,
          result.completedAt || null,
          JSON.stringify(result.metadata || {}),
        ]
      );

      if (dbResult.rows.length === 0) {
        return null;
      }

      // Mapear resultado
      return this.mapDbRowToActivityResult(dbResult.rows[0]);
    } catch (error) {
      logger.error('Erro ao salvar resultado de atividade:', error);
      return null;
    }
  },

  /**
   * Obtém resultados de atividades para um usuário
   * @param userId - ID do usuário
   * @param activityId - ID da atividade (opcional)
   * @returns Lista de resultados
   */
  async getActivityResults(userId: string, activityId?: string): Promise<ActivityResult[]> {
    try {
      // Construir consulta
      let query = `
        SELECT id, activity_id, user_id, score, max_score, percentage,
               time_spent, completed, answers, feedback, started_at, completed_at, metadata
        FROM activity_results
        WHERE user_id = $1
      `;

      const params = [userId];

      // Adicionar filtro por atividade se fornecido
      if (activityId) {
        query += ' AND activity_id = $2';
        params.push(activityId);
      }

      // Ordenar por data de início (mais recente primeiro)
      query += ' ORDER BY started_at DESC';

      // Executar consulta
      const result = await db.query(query, params);

      // Mapear resultados
      return result.rows.map((row) => this.mapDbRowToActivityResult(row));
    } catch (error) {
      logger.error('Erro ao obter resultados de atividades:', error);
      return [];
    }
  },

  /**
   * Armazena uma atividade no cache
   * @param activity - Atividade a ser armazenada
   * @returns Verdadeiro se armazenada com sucesso
   */
  async cacheActivity(activity: BaseActivity): Promise<boolean> {
    try {
      const cacheKey = `${CACHE_PREFIX}${activity.id}`;

      return await cacheService.setItem(cacheKey, JSON.stringify(activity), CACHE_TTL);
    } catch (error) {
      logger.error('Erro ao armazenar atividade em cache:', error);
      return false;
    }
  },

  /**
   * Obtém uma atividade do cache
   * @param id - ID da atividade
   * @returns Atividade ou null se não encontrada
   */
  async getCachedActivity(id: string): Promise<BaseActivity | null> {
    try {
      const cacheKey = `${CACHE_PREFIX}${id}`;

      const cachedData = await cacheService.getItem(cacheKey);

      if (!cachedData) {
        return null;
      }

      return JSON.parse(cachedData);
    } catch (error) {
      logger.error('Erro ao obter atividade do cache:', error);
      return null;
    }
  },

  /**
   * Mapeia uma linha do banco de dados para um objeto Activity
   * @param row - Linha do banco de dados
   * @returns Objeto Activity
   */
  mapDbRowToActivity(row: any): BaseActivity {
    const activity: BaseActivity = {
      id: row.id,
      title: row.title,
      description: row.description,
      type: row.type,
      category: row.category,
      difficulty: row.difficulty,
      maxScore: row.max_score,
      estimatedTime: row.estimated_time,
      tags: row.tags || [],
      minAge: row.min_age,
      maxAge: row.max_age,
      grade: row.grade,
      instructions: row.instructions,
      teacherNotes: row.teacher_notes,
      status: row.status,
      createdBy: row.created_by,
      createdAt: new Date(row.created_at),
      metadata: row.metadata || {},
    };

    if (row.updated_by) {
      activity.updatedBy = row.updated_by;
    }

    if (row.updated_at) {
      activity.updatedAt = new Date(row.updated_at);
    }

    if (row.content) {
      (activity as any).content = row.content;
    }

    return activity;
  },

  /**
   * Mapeia uma linha do banco de dados para um objeto ActivityResult
   * @param row - Linha do banco de dados
   * @returns Objeto ActivityResult
   */
  mapDbRowToActivityResult(row: any): ActivityResult {
    return {
      id: row.id,
      activityId: row.activity_id,
      userId: row.user_id,
      score: row.score,
      maxScore: row.max_score,
      percentage: row.percentage,
      timeSpent: row.time_spent,
      completed: row.completed,
      answers: row.answers || {},
      feedback: row.feedback,
      startedAt: new Date(row.started_at),
      completedAt: row.completed_at ? new Date(row.completed_at) : undefined,
      metadata: row.metadata || {},
    };
  },
};
