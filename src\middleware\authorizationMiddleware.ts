/**
 * Middleware para verificação de autorização
 */

import { authJwtService } from '@services/authJwtService';
import { authorizationService } from '@services/authorizationService';
import { logger } from '@utils/logger';
import type { APIContext, MiddlewareHandler } from 'astro';

/**
 * Opções para o middleware de autorização
 */
export interface AuthorizationOptions {
  /**
   * Recurso a ser verificado
   */
  resource: string;

  /**
   * Ação a ser verificada
   */
  action: string;

  /**
   * Se deve redirecionar para página de acesso negado
   */
  redirect?: boolean;

  /**
   * URL para redirecionamento em caso de acesso negado
   */
  redirectUrl?: string;
}

/**
 * Middleware para verificação de autorização
 * @param options - Opções de autorização
 * @returns Middleware handler
 */
export function requirePermission(options: AuthorizationOptions): MiddlewareHandler {
  return async (context: APIContext, next: () => Promise<Response>) => {
    const { request, cookies, locals } = context;

    try {
      // Obter token do cabeçalho Authorization ou do cookie
      let token = request.headers.get('Authorization')?.replace('Bearer ', '');

      if (!token) {
        token = cookies.get('access_token')?.value;
      }

      // Verificar se o token foi fornecido
      if (!token) {
        if (options.redirect) {
          return new Response(null, {
            status: 302,
            headers: {
              Location: options.redirectUrl || '/signin',
            },
          });
        }

        return new Response(
          JSON.stringify({
            success: false,
            error: 'Não autenticado',
          }),
          {
            status: 401,
            headers: {
              'Content-Type': 'application/json',
            },
          }
        );
      }

      // Verificar token e obter dados do usuário
      const user = await authJwtService.verifyToken(token, true);

      // Verificar se o token é válido
      if (!user) {
        if (options.redirect) {
          return new Response(null, {
            status: 302,
            headers: {
              Location: options.redirectUrl || '/signin',
            },
          });
        }

        return new Response(
          JSON.stringify({
            success: false,
            error: 'Token inválido ou expirado',
          }),
          {
            status: 401,
            headers: {
              'Content-Type': 'application/json',
            },
          }
        );
      }

      // Verificar permissão
      const permissionResult = await authorizationService.checkPermission(
        user.ulid_user,
        options.resource,
        options.action
      );

      // Se não tem permissão
      if (!permissionResult.granted) {
        logger.warn(
          `Acesso negado: ${user.email} tentou ${options.action} em ${options.resource}`,
          {
            userId: user.ulid_user,
            resource: options.resource,
            action: options.action,
            ip: request.headers.get('x-forwarded-for') || 'unknown',
            userAgent: request.headers.get('user-agent') || 'unknown',
          }
        );

        if (options.redirect) {
          return new Response(null, {
            status: 302,
            headers: {
              Location: options.redirectUrl || '/access-denied',
            },
          });
        }

        return new Response(
          JSON.stringify({
            success: false,
            error: 'Acesso negado',
            reason: permissionResult.reason,
          }),
          {
            status: 403,
            headers: {
              'Content-Type': 'application/json',
            },
          }
        );
      }

      // Armazenar usuário e permissões nos locals para uso posterior
      locals.user = user;
      locals.permissions = {
        [options.resource]: {
          [options.action]: true,
        },
      };

      // Continuar para o próximo middleware ou rota
      return await next();
    } catch (error) {
      logger.error('Erro no middleware de autorização:', error);

      if (options.redirect) {
        return new Response(null, {
          status: 302,
          headers: {
            Location: options.redirectUrl || '/error',
          },
        });
      }

      return new Response(
        JSON.stringify({
          success: false,
          error: 'Erro ao verificar autorização',
        }),
        {
          status: 500,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }
  };
}

/**
 * Middleware para verificação de múltiplas permissões (AND)
 * @param options - Lista de opções de autorização
 * @returns Middleware handler
 */
export function requireAllPermissions(options: AuthorizationOptions[]): MiddlewareHandler {
  return async (context: APIContext, next: () => Promise<Response>) => {
    const { request, cookies, locals } = context;

    try {
      // Obter token do cabeçalho Authorization ou do cookie
      let token = request.headers.get('Authorization')?.replace('Bearer ', '');

      if (!token) {
        token = cookies.get('access_token')?.value;
      }

      // Verificar se o token foi fornecido
      if (!token) {
        if (options[0]?.redirect) {
          return new Response(null, {
            status: 302,
            headers: {
              Location: options[0]?.redirectUrl || '/signin',
            },
          });
        }

        return new Response(
          JSON.stringify({
            success: false,
            error: 'Não autenticado',
          }),
          {
            status: 401,
            headers: {
              'Content-Type': 'application/json',
            },
          }
        );
      }

      // Verificar token e obter dados do usuário
      const user = await authJwtService.verifyToken(token, true);

      // Verificar se o token é válido
      if (!user) {
        if (options[0]?.redirect) {
          return new Response(null, {
            status: 302,
            headers: {
              Location: options[0]?.redirectUrl || '/signin',
            },
          });
        }

        return new Response(
          JSON.stringify({
            success: false,
            error: 'Token inválido ou expirado',
          }),
          {
            status: 401,
            headers: {
              'Content-Type': 'application/json',
            },
          }
        );
      }

      // Verificar todas as permissões
      const permissionsToCheck = options.map((opt) => ({
        resource: opt.resource,
        action: opt.action,
      }));

      const permissionResults = await authorizationService.checkMultiplePermissions(
        user.ulid_user,
        permissionsToCheck
      );

      // Verificar se todas as permissões foram concedidas
      const deniedPermission = Object.entries(permissionResults).find(
        ([_, result]) => !result.granted
      );

      // Se alguma permissão foi negada
      if (deniedPermission) {
        const [key, result] = deniedPermission;
        const [resource, action] = key.split(':');

        logger.warn(`Acesso negado: ${user.email} não tem todas as permissões necessárias`, {
          userId: user.ulid_user,
          resource,
          action,
          ip: request.headers.get('x-forwarded-for') || 'unknown',
          userAgent: request.headers.get('user-agent') || 'unknown',
        });

        if (options[0]?.redirect) {
          return new Response(null, {
            status: 302,
            headers: {
              Location: options[0]?.redirectUrl || '/access-denied',
            },
          });
        }

        return new Response(
          JSON.stringify({
            success: false,
            error: 'Acesso negado',
            reason: result.reason,
          }),
          {
            status: 403,
            headers: {
              'Content-Type': 'application/json',
            },
          }
        );
      }

      // Armazenar usuário e permissões nos locals para uso posterior
      locals.user = user;
      locals.permissions = Object.fromEntries(
        options.map((opt) => [opt.resource, { [opt.action]: true }])
      );

      // Continuar para o próximo middleware ou rota
      return await next();
    } catch (error) {
      logger.error('Erro no middleware de autorização:', error);

      if (options[0]?.redirect) {
        return new Response(null, {
          status: 302,
          headers: {
            Location: options[0]?.redirectUrl || '/error',
          },
        });
      }

      return new Response(
        JSON.stringify({
          success: false,
          error: 'Erro ao verificar autorização',
        }),
        {
          status: 500,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }
  };
}
