/**
 * Script para configuração de tópicos Kafka
 *
 * Este script cria e configura os tópicos Kafka conforme a estrutura definida
 * na documentação. Pode ser executado durante a inicialização do sistema ou
 * manualmente para configurar o ambiente.
 */

import kafka from '../config/kafka';
import { kafkaReplicationConfig } from '../config/kafka-replication.config';
import { kafkaRetentionConfig } from '../config/kafka-retention.config';
import { logger } from '../utils/logger';

interface TopicConfig {
  name: string;
  numPartitions: number;
  replicationFactor: number;
  configs: {
    'retention.ms'?: number;
    'cleanup.policy'?: string;
    'min.insync.replicas'?: number;
    'compression.type'?: string;
    'segment.ms'?: number;
    'min.cleanable.dirty.ratio'?: number;
    'min.compaction.lag.ms'?: number;
    'max.compaction.lag.ms'?: number;
    'delete.retention.ms'?: number;
  };
}

// Função para obter configuração completa de um tópico
function getTopicConfig(topicName: string): TopicConfig {
  const replicationConfig = kafkaReplicationConfig.getTopicConfig(topicName);
  const retentionConfig = kafkaRetentionConfig.getTopicRetentionConfig(topicName);

  // Configurações padrão
  const defaultConfig = {
    numPartitions: 3,
    replicationFactor: 1,
    configs: {
      'retention.ms': 90 * 24 * 60 * 60 * 1000, // 90 dias
      'cleanup.policy': 'delete',
      'min.insync.replicas': 1,
      'compression.type': 'lz4',
    },
  };

  // Mesclar configurações
  const config: TopicConfig = {
    name: topicName,
    numPartitions: replicationConfig?.numPartitions || defaultConfig.numPartitions,
    replicationFactor: replicationConfig?.replicationFactor || defaultConfig.replicationFactor,
    configs: {
      'min.insync.replicas':
        replicationConfig?.minInsyncReplicas || defaultConfig.configs['min.insync.replicas'],
      'compression.type': defaultConfig.configs['compression.type'],
    },
  };

  // Adicionar configurações de retenção, se disponíveis
  if (retentionConfig) {
    config.configs['retention.ms'] = retentionConfig.retentionMs;
    config.configs['cleanup.policy'] = retentionConfig.cleanupPolicy;

    if (retentionConfig.segmentMs) {
      config.configs['segment.ms'] = retentionConfig.segmentMs;
    }

    if (retentionConfig.minCleanableDirtyRatio) {
      config.configs['min.cleanable.dirty.ratio'] = retentionConfig.minCleanableDirtyRatio;
    }

    if (retentionConfig.minCompactionLagMs) {
      config.configs['min.compaction.lag.ms'] = retentionConfig.minCompactionLagMs;
    }

    if (retentionConfig.maxCompactionLagMs) {
      config.configs['max.compaction.lag.ms'] = retentionConfig.maxCompactionLagMs;
    }

    if (retentionConfig.deleteRetentionMs) {
      config.configs['delete.retention.ms'] = retentionConfig.deleteRetentionMs;
    }
  } else {
    // Usar configurações padrão
    config.configs['retention.ms'] = defaultConfig.configs['retention.ms'];
    config.configs['cleanup.policy'] = defaultConfig.configs['cleanup.policy'];
  }

  return config;
}

// Configuração de tópicos por domínio
const topicConfigurations: TopicConfig[] = [
  // Domínio: Pagamentos
  getTopicConfig('payment.transaction.created'),
  getTopicConfig('payment.transaction.updated'),
  getTopicConfig('payment.transaction.failed'),
  getTopicConfig('payment.refund.requested'),
  getTopicConfig('payment.refund.processed'),
  getTopicConfig('payment.invoice.created'),
  getTopicConfig('payment.invoice.paid'),
  getTopicConfig('payment.webhook.received'),

  // Domínio: Pedidos
  getTopicConfig('order.created'),
  getTopicConfig('order.updated'),
  getTopicConfig('order.status.changed'),
  getTopicConfig('order.cancelled'),
  getTopicConfig('order.fulfilled'),

  // Domínio: Usuários
  getTopicConfig('user.registered'),
  getTopicConfig('user.profile.updated'),
  getTopicConfig('user.subscription.changed'),

  // Domínio: Notificações
  getTopicConfig('notification.email.queued'),
  getTopicConfig('notification.email.sent'),
  getTopicConfig('notification.email.failed'),
  getTopicConfig('notification.alert.created'),
  getTopicConfig('notification.alert.resolved'),

  // Domínio: Analytics
  getTopicConfig('analytics.page.viewed'),
  getTopicConfig('analytics.product.viewed'),
  getTopicConfig('analytics.search.performed'),
  getTopicConfig('analytics.cart.abandoned'),
  getTopicConfig('analytics.conversion.completed'),
];

/**
 * Configura os tópicos Kafka
 */
async function setupKafkaTopics() {
  const admin = kafka.admin();

  try {
    logger.info('Conectando ao cluster Kafka...');
    await admin.connect();
    logger.info('Conexão estabelecida com sucesso');

    // Listar tópicos existentes
    logger.info('Obtendo lista de tópicos existentes...');
    const existingTopics = await admin.listTopics();
    logger.info(`Tópicos existentes: ${existingTopics.length}`);

    // Filtrar tópicos que precisam ser criados
    const topicsToCreate = topicConfigurations.filter(
      (topic) => !existingTopics.includes(topic.name)
    );

    if (topicsToCreate.length === 0) {
      logger.info('Todos os tópicos já existem. Nenhum tópico novo para criar.');
    } else {
      logger.info(`Criando ${topicsToCreate.length} tópicos...`);

      // Criar tópicos
      await admin.createTopics({
        topics: topicsToCreate.map((topic) => ({
          topic: topic.name,
          numPartitions: topic.numPartitions,
          replicationFactor: topic.replicationFactor,
          configEntries: Object.entries(topic.configs).map(([key, value]) => ({
            name: key,
            value: value.toString(),
          })),
        })),
      });

      logger.info('Tópicos criados com sucesso');
    }

    // Verificar configurações dos tópicos existentes
    logger.info('Verificando configurações dos tópicos existentes...');

    for (const topicConfig of topicConfigurations) {
      if (existingTopics.includes(topicConfig.name)) {
        // Obter configurações atuais do tópico
        const topicMetadata = await admin.fetchTopicMetadata({
          topics: [topicConfig.name],
        });

        if (topicMetadata.topics.length > 0) {
          const topic = topicMetadata.topics[0];

          // Verificar número de partições
          if (topic.partitions.length !== topicConfig.numPartitions) {
            logger.warn(
              `Tópico ${topicConfig.name} tem ${topic.partitions.length} partições, mas deveria ter ${topicConfig.numPartitions}`
            );
          }

          // Obter configurações do tópico
          const configResource = await admin.describeConfigs({
            resources: [
              {
                type: 2, // TOPIC
                name: topicConfig.name,
              },
            ],
          });

          if (configResource.resources.length > 0) {
            const config = configResource.resources[0];

            // Verificar configurações
            for (const [key, value] of Object.entries(topicConfig.configs)) {
              const configEntry = config.configEntries.find((entry) => entry.name === key);

              if (!configEntry || configEntry.value !== value.toString()) {
                logger.warn(
                  `Configuração ${key} do tópico ${topicConfig.name} é ${
                    configEntry ? configEntry.value : 'não definida'
                  }, mas deveria ser ${value}`
                );
              }
            }
          }
        }
      }
    }

    logger.info('Verificação de configurações concluída');
  } catch (error) {
    logger.error('Erro ao configurar tópicos Kafka:', error);
    throw error;
  } finally {
    await admin.disconnect();
    logger.info('Desconectado do cluster Kafka');
  }
}

// Executar script
if (require.main === module) {
  setupKafkaTopics()
    .then(() => {
      logger.info('Configuração de tópicos Kafka concluída com sucesso');
      process.exit(0);
    })
    .catch((error) => {
      logger.error('Falha na configuração de tópicos Kafka:', error);
      process.exit(1);
    });
}

export { setupKafkaTopics };
