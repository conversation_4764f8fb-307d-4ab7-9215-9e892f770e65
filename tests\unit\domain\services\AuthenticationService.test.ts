/**
 * Testes para AuthenticationService
 *
 * Este arquivo contém testes unitários para o serviço de autenticação.
 * Parte da implementação da tarefa 9.1.1 - Testes unitários
 */

import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { User } from '../../../../src/domain/entities/User';
import { UserRepository } from '../../../../src/domain/repositories/UserRepository';
import { AuthenticationService } from '../../../../src/domain/services/AuthenticationService';
import { TokenService } from '../../../../src/domain/services/TokenService';
import { AuthenticationError } from '../../../../src/domain/services/errors/AuthenticationError';

// Mock dos repositórios e serviços
vi.mock('../../../../src/domain/repositories/UserRepository');
vi.mock('../../../../src/domain/services/TokenService');

describe('AuthenticationService', () => {
  let authService: AuthenticationService;
  let userRepository: UserRepository;
  let tokenService: TokenService;

  // Dados de teste
  const testUser: User = {
    id: '1',
    name: 'Test User',
    email: '<EMAIL>',
    password: 'hashed_password',
    role: 'user',
    createdAt: new Date(),
    updatedAt: new Date(),
    isActive: true,
  };

  const testCredentials = {
    email: '<EMAIL>',
    password: 'password123',
  };

  const testToken = {
    accessToken: 'test_access_token',
    refreshToken: 'test_refresh_token',
    expiresIn: 3600,
  };

  beforeEach(() => {
    // Configurar mocks
    userRepository = {
      findByEmail: vi.fn(),
      findById: vi.fn(),
      create: vi.fn(),
      update: vi.fn(),
      delete: vi.fn(),
      list: vi.fn(),
    } as unknown as UserRepository;

    tokenService = {
      generateToken: vi.fn(),
      verifyToken: vi.fn(),
      refreshToken: vi.fn(),
      revokeToken: vi.fn(),
    } as unknown as TokenService;

    // Instanciar o serviço de autenticação com os mocks
    authService = new AuthenticationService(userRepository, tokenService);
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('login', () => {
    it('should authenticate a user with valid credentials', async () => {
      // Configurar mocks
      (userRepository.findByEmail as any).mockResolvedValue(testUser);
      (tokenService.generateToken as any).mockResolvedValue(testToken);

      // Configurar mock para verificação de senha
      vi.spyOn(authService as any, 'verifyPassword').mockResolvedValue(true);

      // Executar o método de login
      const result = await authService.login(testCredentials.email, testCredentials.password);

      // Verificar se os métodos foram chamados corretamente
      expect(userRepository.findByEmail).toHaveBeenCalledWith(testCredentials.email);
      expect(authService.verifyPassword).toHaveBeenCalledWith(
        testCredentials.password,
        testUser.password
      );
      expect(tokenService.generateToken).toHaveBeenCalledWith({
        userId: testUser.id,
        role: testUser.role,
      });

      // Verificar o resultado
      expect(result).toEqual({
        user: {
          id: testUser.id,
          name: testUser.name,
          email: testUser.email,
          role: testUser.role,
        },
        ...testToken,
      });
    });

    it('should throw an error when user is not found', async () => {
      // Configurar mock para retornar null (usuário não encontrado)
      (userRepository.findByEmail as any).mockResolvedValue(null);

      // Verificar se o método lança a exceção esperada
      await expect(
        authService.login(testCredentials.email, testCredentials.password)
      ).rejects.toThrow(AuthenticationError);

      // Verificar se a mensagem de erro é a esperada
      await expect(
        authService.login(testCredentials.email, testCredentials.password)
      ).rejects.toThrow('Credenciais inválidas');

      // Verificar se o método foi chamado corretamente
      expect(userRepository.findByEmail).toHaveBeenCalledWith(testCredentials.email);
    });

    it('should throw an error when password is incorrect', async () => {
      // Configurar mocks
      (userRepository.findByEmail as any).mockResolvedValue(testUser);

      // Configurar mock para verificação de senha (retorna falso)
      vi.spyOn(authService as any, 'verifyPassword').mockResolvedValue(false);

      // Verificar se o método lança a exceção esperada
      await expect(
        authService.login(testCredentials.email, testCredentials.password)
      ).rejects.toThrow(AuthenticationError);

      // Verificar se a mensagem de erro é a esperada
      await expect(
        authService.login(testCredentials.email, testCredentials.password)
      ).rejects.toThrow('Credenciais inválidas');

      // Verificar se os métodos foram chamados corretamente
      expect(userRepository.findByEmail).toHaveBeenCalledWith(testCredentials.email);
      expect(authService.verifyPassword).toHaveBeenCalledWith(
        testCredentials.password,
        testUser.password
      );
    });

    it('should throw an error when user is inactive', async () => {
      // Criar um usuário inativo
      const inactiveUser = { ...testUser, isActive: false };

      // Configurar mocks
      (userRepository.findByEmail as any).mockResolvedValue(inactiveUser);

      // Configurar mock para verificação de senha
      vi.spyOn(authService as any, 'verifyPassword').mockResolvedValue(true);

      // Verificar se o método lança a exceção esperada
      await expect(
        authService.login(testCredentials.email, testCredentials.password)
      ).rejects.toThrow(AuthenticationError);

      // Verificar se a mensagem de erro é a esperada
      await expect(
        authService.login(testCredentials.email, testCredentials.password)
      ).rejects.toThrow('Conta desativada');

      // Verificar se os métodos foram chamados corretamente
      expect(userRepository.findByEmail).toHaveBeenCalledWith(testCredentials.email);
      expect(authService.verifyPassword).toHaveBeenCalledWith(
        testCredentials.password,
        inactiveUser.password
      );
    });
  });

  describe('refreshToken', () => {
    it('should refresh a valid token', async () => {
      // Configurar mocks
      const tokenPayload = { userId: testUser.id, role: testUser.role };
      (tokenService.verifyToken as any).mockResolvedValue(tokenPayload);
      (userRepository.findById as any).mockResolvedValue(testUser);
      (tokenService.refreshToken as any).mockResolvedValue(testToken);

      // Executar o método de refresh token
      const result = await authService.refreshToken('valid_refresh_token');

      // Verificar se os métodos foram chamados corretamente
      expect(tokenService.verifyToken).toHaveBeenCalledWith('valid_refresh_token');
      expect(userRepository.findById).toHaveBeenCalledWith(testUser.id);
      expect(tokenService.refreshToken).toHaveBeenCalledWith('valid_refresh_token', {
        userId: testUser.id,
        role: testUser.role,
      });

      // Verificar o resultado
      expect(result).toEqual(testToken);
    });

    it('should throw an error when token is invalid', async () => {
      // Configurar mock para lançar erro ao verificar token
      (tokenService.verifyToken as any).mockRejectedValue(new Error('Token inválido'));

      // Verificar se o método lança a exceção esperada
      await expect(authService.refreshToken('invalid_token')).rejects.toThrow(AuthenticationError);

      // Verificar se a mensagem de erro é a esperada
      await expect(authService.refreshToken('invalid_token')).rejects.toThrow(
        'Token inválido ou expirado'
      );

      // Verificar se o método foi chamado corretamente
      expect(tokenService.verifyToken).toHaveBeenCalledWith('invalid_token');
    });

    it('should throw an error when user is not found', async () => {
      // Configurar mocks
      const tokenPayload = { userId: 'non_existent_id', role: 'user' };
      (tokenService.verifyToken as any).mockResolvedValue(tokenPayload);
      (userRepository.findById as any).mockResolvedValue(null);

      // Verificar se o método lança a exceção esperada
      await expect(authService.refreshToken('valid_token')).rejects.toThrow(AuthenticationError);

      // Verificar se a mensagem de erro é a esperada
      await expect(authService.refreshToken('valid_token')).rejects.toThrow(
        'Usuário não encontrado'
      );

      // Verificar se os métodos foram chamados corretamente
      expect(tokenService.verifyToken).toHaveBeenCalledWith('valid_token');
      expect(userRepository.findById).toHaveBeenCalledWith('non_existent_id');
    });

    it('should throw an error when user is inactive', async () => {
      // Criar um usuário inativo
      const inactiveUser = { ...testUser, isActive: false };

      // Configurar mocks
      const tokenPayload = { userId: inactiveUser.id, role: inactiveUser.role };
      (tokenService.verifyToken as any).mockResolvedValue(tokenPayload);
      (userRepository.findById as any).mockResolvedValue(inactiveUser);

      // Verificar se o método lança a exceção esperada
      await expect(authService.refreshToken('valid_token')).rejects.toThrow(AuthenticationError);

      // Verificar se a mensagem de erro é a esperada
      await expect(authService.refreshToken('valid_token')).rejects.toThrow('Conta desativada');

      // Verificar se os métodos foram chamados corretamente
      expect(tokenService.verifyToken).toHaveBeenCalledWith('valid_token');
      expect(userRepository.findById).toHaveBeenCalledWith(inactiveUser.id);
    });
  });

  describe('logout', () => {
    it('should revoke a token', async () => {
      // Configurar mock
      (tokenService.revokeToken as any).mockResolvedValue(true);

      // Executar o método de logout
      await authService.logout('valid_refresh_token');

      // Verificar se o método foi chamado corretamente
      expect(tokenService.revokeToken).toHaveBeenCalledWith('valid_refresh_token');
    });
  });
});
