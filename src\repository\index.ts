/**
 * Camada de compatibilidade para repositórios antigos
 *
 * Este arquivo mantém a compatibilidade com o código existente que
 * utiliza a estrutura antiga de repositórios. Ele re-exporta as
 * implementações da nova estrutura no formato antigo.
 *
 * @deprecated Use a nova estrutura de repositórios em src/repositories
 */

// Importar repositórios da camada de compatibilidade
import {
  productRepository,
  userRepository,
  categoryRepository,
  orderRepository,
  orderItemRepository,
  invoiceRepository,
  invoiceItemRepository,
  paymentRepository,
} from '../repositories/compatibility';

// Importar repositórios que ainda não foram migrados
import { informationSchemaColumnRepository } from './informationSchemaColumnRepository';
import { paymentTypeRepository } from './paymentTypeRepository';
import { postRepository } from './postRepository';
import { schoolTypeRepository } from './schoolTypeRepository';
import { statusRepository } from './statusRepository';
import { userTypeRepository } from './userTypeRepository';

export const database = {
  // Repositórios migrados para a camada de compatibilidade
  productRepository,
  userRepository,
  categoryRepository,
  orderRepository,
  orderItemRepository,
  invoiceRepository,
  invoiceItemRepository,
  paymentRepository,

  // Repositórios que ainda não foram migrados
  informationSchemaColumnRepository,
  paymentTypeRepository,
  postRepository,
  schoolTypeRepository,
  statusRepository,
  userTypeRepository,
};

// Aviso de depreciação
console.warn(
  'DEPRECATED: A estrutura antiga de repositórios em src/repository está depreciada. ' +
    'Use a nova estrutura em src/repositories.'
);
