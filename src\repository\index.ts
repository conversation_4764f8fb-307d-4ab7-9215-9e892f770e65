/**
 * Camada de compatibilidade para repositórios antigos
 *
 * Este arquivo mantém a compatibilidade com o código existente que
 * utiliza a estrutura antiga de repositórios. Ele re-exporta as
 * implementações da nova estrutura no formato antigo.
 *
 * @deprecated Use a nova estrutura de repositórios em src/repositories
 */

import { categoryRepository } from './categoryRepository';
import { informationSchemaColumnRepository } from './informationSchemaColumnRepository';
import { invoiceRepository } from './invoiceRepository';
import { invoiceItemRepository } from './invoiceItemRepository';
import { orderRepository } from './orderRepository';
import { orderItemRepository } from './orderItemRepository';
import { paymentRepository } from './paymentRepository';
import { paymentTypeRepository } from './paymentTypeRepository';
import { postRepository } from './postRepository';
import { productRepository } from '../repositories/compatibility/productRepository'; // Usando a nova implementação
import { schoolTypeRepository } from './schoolTypeRepository';
import { statusRepository } from './statusRepository';
import { userRepository } from './userRepository';
import { userTypeRepository } from './userTypeRepository';

export const database = {
  categoryRepository,
  informationSchemaColumnRepository,
  invoiceRepository,
  invoiceItemRepository,
  orderRepository,
  orderItemRepository,
  paymentRepository,
  paymentTypeRepository,
  postRepository,
  productRepository,
  schoolTypeRepository,
  statusRepository,
  userRepository,
  userTypeRepository,
};

// Aviso de depreciação
console.warn(
  'DEPRECATED: A estrutura antiga de repositórios em src/repository está depreciada. ' +
    'Use a nova estrutura em src/repositories.'
);
