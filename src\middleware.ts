import { defineMiddleware } from 'astro:middleware';
import type { MiddlewareResponseHandler } from 'astro';

/**
 * Middleware for Astro
 * Handles authentication, session management, and other global concerns
 */
export const onRequest: MiddlewareResponseHandler = defineMiddleware(
  async ({ request, cookies, locals }, next) => {
    // Start timing for performance monitoring
    const startTime = performance.now();

    // Get session from cookies
    const sessionId = cookies.get('session')?.value;

    // If session exists, load user data
    if (sessionId) {
      try {
        // In a real implementation, this would validate the session and load user data
        // For demonstration purposes, we'll use a mock user
        if (sessionId) {
          locals.user = {
            id: '1',
            name: 'Admin User',
            email: '<EMAIL>',
            role: 'admin',
          };
        }
      } catch (error) {
        console.error('Error loading user data:', error);
        // Clear invalid session
        cookies.delete('session', { path: '/' });
      }
    }

    // Add request information to locals for logging
    locals.requestInfo = {
      url: request.url,
      method: request.method,
      userAgent: request.headers.get('user-agent'),
      timestamp: new Date().toISOString(),
    };

    // Continue to the endpoint
    const response = await next();

    // Calculate response time
    const responseTime = performance.now() - startTime;

    // Add Server-Timing header for performance monitoring
    response.headers.append('Server-Timing', `app;dur=${responseTime}`);

    // Add security headers
    response.headers.append('X-Content-Type-Options', 'nosniff');
    response.headers.append('X-Frame-Options', 'SAMEORIGIN');
    response.headers.append('X-XSS-Protection', '1; mode=block');

    // Log request (in a real implementation, this would use a proper logging system)
    console.log(
      `${request.method} ${new URL(request.url).pathname} - ${response.status} (${responseTime.toFixed(2)}ms)`
    );

    return response;
  }
);
