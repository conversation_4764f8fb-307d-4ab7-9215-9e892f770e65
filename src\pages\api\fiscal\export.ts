/**
 * API de Exportação de Relatórios Fiscais
 *
 * Endpoint para exportar relatórios fiscais em diferentes formatos.
 * Parte da implementação da tarefa 8.7.2 - Gestão de documentos fiscais
 */

import type { APIRoute } from 'astro';
import { FiscalDocumentStatus, FiscalDocumentType } from '../../../domain/entities/FiscalDocument';
import { FiscalDocumentRepository } from '../../../domain/repositories/FiscalDocumentRepository';
import {
  ExportFiscalReportUseCase,
  ReportFormat,
} from '../../../domain/usecases/fiscal/ExportFiscalReportUseCase';
import { PostgresFiscalDocumentRepository } from '../../../infrastructure/database/repositories/PostgresFiscalDocumentRepository';

// Inicializar repositório
const fiscalDocumentRepository: FiscalDocumentRepository = new PostgresFiscalDocumentRepository();

// Inicializar caso de uso
const exportFiscalReportUseCase = new ExportFiscalReportUseCase(fiscalDocumentRepository);

export const POST: APIRoute = async ({ request }) => {
  try {
    // Obter dados da requisição
    const body = await request.json();
    const { filter, format, includeItems } = body;

    // Validar formato
    if (!format || !['csv', 'xlsx', 'pdf'].includes(format)) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Formato de relatório inválido. Formatos suportados: csv, xlsx, pdf.',
        }),
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    // Processar datas se fornecidas
    if (filter?.startDate) {
      filter.startDate = new Date(filter.startDate);
    }

    if (filter?.endDate) {
      filter.endDate = new Date(filter.endDate);
    }

    // Exportar relatório
    const result = await exportFiscalReportUseCase.execute({
      filter,
      format: format as ReportFormat,
      includeItems: includeItems || false,
    });

    if (result.success && result.data) {
      // Retornar o arquivo para download
      return new Response(result.data.content, {
        status: 200,
        headers: {
          'Content-Type': result.data.contentType,
          'Content-Disposition': `attachment; filename="${result.data.filename}"`,
        },
      });
    }
    return new Response(
      JSON.stringify({
        success: false,
        error: result.error || 'Erro desconhecido ao exportar relatório fiscal.',
      }),
      {
        status: 400,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  } catch (error) {
    console.error('Erro ao processar exportação de relatório fiscal:', error);

    return new Response(
      JSON.stringify({
        success: false,
        error:
          'Ocorreu um erro ao processar a exportação do relatório fiscal. Por favor, tente novamente.',
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  }
};
