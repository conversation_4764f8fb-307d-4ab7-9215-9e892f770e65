import { defineAction } from 'astro:actions';
import type { AstroGlobal } from 'astro';
import * as argon2 from 'argon2';
import { userRepository } from '@repository/userRepository';
import { userTypeRepository } from '@repository/userTypeRepository';
import { tokenRepository } from '@repository/tokenRepository';
import { handleError } from '@helpers/errorUtils';
import { csrfHelper } from '@helpers/csrfHelper';
import { logger } from '@utils/logger';
import { emailService } from '@services/emailService';
import { queryHelper } from '@db/queryHelper';
import { jwtService } from '@services/jwtService';

type ActionContext = {
  locals: AstroGlobal['locals'];
  session?: AstroGlobal['session'];
  request: Request;
};

// Interface para dados do usuário
interface UserData {
  ulid_user: string;
  name: string;
  email: string;
  is_teacher: boolean;
  ulid_user_type: string;
  ulid_school_type: string;
  password: string;
  state: string;
  county: string;
}

interface SignInInput {
  email: string;
  password: string;
  remember?: string;
  redirect?: string;
  csrf_token: string;
}

interface SignUpInput {
  name: string;
  email: string;
  password: string;
  password_confirm: string;
  ulid_school_type: string;
  state: string;
  county: string;
  is_teacher: string;
  terms: string;
  csrf_token: string;
}

// Contador de tentativas de login por IP
const loginAttempts = new Map<string, { count: number; lastAttempt: number }>();

// Limite de tentativas de login
const MAX_LOGIN_ATTEMPTS = 5;
// Tempo de bloqueio em milissegundos (15 minutos)
const BLOCK_DURATION = 15 * 60 * 1000;

export const signin = defineAction({
  async handler(data: SignInInput, context: ActionContext) {
    try {
      const { email, password } = data;
      const { session, request } = context;

      // Validar token CSRF
      const csrfToken = data.csrf_token;
      const isValidCsrf = await csrfHelper.validateToken(context, csrfToken);

      if (!isValidCsrf) {
        return {
          success: false,
          error:
            'Token de segurança inválido. Por favor, recarregue a página e tente novamente.',
        };
      }

      // Obter IP do cliente para controle de tentativas
      const clientIp =
        request.headers.get('x-forwarded-for') ||
        request.headers.get('x-real-ip') ||
        'unknown';

      // Verificar se o IP está bloqueado por muitas tentativas
      const ipAttempts = loginAttempts.get(clientIp);
      if (ipAttempts && ipAttempts.count >= MAX_LOGIN_ATTEMPTS) {
        const timeSinceLastAttempt = Date.now() - ipAttempts.lastAttempt;

        if (timeSinceLastAttempt < BLOCK_DURATION) {
          const minutesLeft = Math.ceil(
            (BLOCK_DURATION - timeSinceLastAttempt) / 60000
          );

          // Registrar tentativa de login durante bloqueio
          await logUserEvent('blocked', 'login_blocked', {
            email: email,
            ip: clientIp,
            userAgent: request.headers.get('user-agent') || 'unknown',
            reason: 'too_many_attempts',
          });

          return {
            success: false,
            error: `Muitas tentativas de login. Tente novamente em ${minutesLeft} minutos.`,
          };
        }

        // Resetar contador após o período de bloqueio
        loginAttempts.delete(clientIp);
      }

      // Validar dados de entrada
      if (!email || !validateEmail(email)) {
        return {
          success: false,
          error: 'Email inválido',
        };
      }

      if (!password) {
        return {
          success: false,
          error: 'Senha é obrigatória',
        };
      }

      // Buscar usuário pelo email
      const userResult = await userRepository.read(
        undefined,
        undefined,
        undefined,
        email,
        undefined,
        undefined,
        undefined,
        true
      );

      // Verificar se o usuário existe
      if (userResult.rowCount === 0) {
        // Incrementar contador de tentativas
        incrementLoginAttempts(clientIp);

        // Registrar falha de login
        await logUserEvent('blocked', 'login_failed', {
          email: email,
          ip: clientIp,
          userAgent: request.headers.get('user-agent') || 'unknown',
          reason: 'user_not_found',
        });

        return {
          success: false,
          error: 'Email ou senha inválidos',
        };
      }

      const user = userResult.rows[0] as UserData;

      // Verificar senha com argon2
      const validPassword = await argon2.verify(user.password, password);
      if (!validPassword) {
        // Incrementar contador de tentativas
        incrementLoginAttempts(clientIp);

        // Registrar falha de login
        await logUserEvent(user.ulid_user, 'login_failed', {
          email: email,
          ip: clientIp,
          userAgent: request.headers.get('user-agent') || 'unknown',
          reason: 'invalid_password',
        });

        return {
          success: false,
          error: 'Email ou senha inválidos',
        };
      }

      // Verificar se a conta está ativa
      if (user.is_active === false) {
        // Registrar tentativa em conta inativa
        await logUserEvent(user.ulid_user, 'login_failed', {
          email: email,
          ip: clientIp,
          userAgent: request.headers.get('user-agent') || 'unknown',
          reason: 'account_inactive',
        });

        return {
          success: false,
          error: 'Conta desativada. Entre em contato com o suporte.',
        };
      }

      // Resetar contador de tentativas após login bem-sucedido
      loginAttempts.delete(clientIp);

      // Registrar login bem-sucedido
      await logUserEvent(user.ulid_user, 'login_success', {
        email: email,
        ip: clientIp,
        userAgent: request.headers.get('user-agent') || 'unknown',
      });

      // Atualizar último login do usuário
      await userRepository.updateLastLogin(user.ulid_user);

      // Armazenar dados do usuário na sessão
      await session?.set('user', {
        ulid_user: user.ulid_user,
        name: user.name,
        email: user.email,
        is_teacher: user.is_teacher,
        ulid_user_type: user.ulid_user_type,
        ulid_school_type: user.ulid_school_type,
        state: user.state,
        county: user.county,
      });

      // Definir duração da sessão com base na opção "lembrar-me"
      const sessionDuration =
        data.remember === 'on' ? 30 * 24 * 60 * 60 : 24 * 60 * 60; // 30 dias ou 1 dia
      await session?.set(
        'session_expires',
        Date.now() + sessionDuration * 1000
      );

      await session?.set('isAuthenticated', true);

      // Rotacionar token CSRF após login bem-sucedido
      await csrfHelper.rotateToken(context);

      return {
        success: true,
        result: {
          user: {
            ulid_user: user.ulid_user,
            name: user.name,
            email: user.email,
            is_teacher: user.is_teacher,
            ulid_user_type: user.ulid_user_type,
            ulid_school_type: user.ulid_school_type,
            state: user.state,
            county: user.county,
          },
          redirect: data.redirect || '/dashboard',
        },
      };
    } catch (error) {
      logger.error('Erro ao realizar login:', error);
      return {
        success: false,
        error: 'Erro ao realizar login. Por favor, tente novamente mais tarde.',
      };
    }
  },
});

/**
 * Incrementa o contador de tentativas de login para um IP
 * @param ip - Endereço IP do cliente
 */
function incrementLoginAttempts(ip: string): void {
  const now = Date.now();
  const attempts = loginAttempts.get(ip);

  if (attempts) {
    attempts.count += 1;
    attempts.lastAttempt = now;
  } else {
    loginAttempts.set(ip, { count: 1, lastAttempt: now });
  }

  // Limpar entradas antigas periodicamente
  if (loginAttempts.size > 1000) {
    cleanupLoginAttempts();
  }
}

/**
 * Limpa entradas antigas do contador de tentativas
 */
function cleanupLoginAttempts(): void {
  const now = Date.now();

  for (const [ip, attempts] of loginAttempts.entries()) {
    if (now - attempts.lastAttempt > BLOCK_DURATION) {
      loginAttempts.delete(ip);
    }
  }
}

// Função para validar email
function validateEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

// Função para validar senha
function validatePassword(password: string): boolean {
  // Pelo menos 8 caracteres, uma letra maiúscula, uma minúscula e um número
  const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).{8,}$/;
  return passwordRegex.test(password);
}

// Função para registrar eventos de usuário
async function logUserEvent(
  userId: string | 'blocked',
  eventType: string,
  metadata: Record<string, unknown>
): Promise<void> {
  try {
    if (userId === 'blocked') {
      // Registrar evento sem usuário específico
      await queryHelper.query(
        `INSERT INTO tab_user_event (
          ulid_user_event, event_type, event_data, created_at
        ) VALUES (
          gen_ulid(), $1, $2, NOW()
        )`,
        [eventType, JSON.stringify(metadata)]
      );
    } else {
      // Registrar evento para usuário específico
      await userRepository.logEvent(userId, eventType, metadata);
    }
  } catch (error) {
    logger.error(`Erro ao registrar evento de usuário ${eventType}:`, error);
  }
}

export const signup = defineAction({
  async handler(data: SignUpInput, context: ActionContext) {
    try {
      const { session, request } = context;

      // Validar token CSRF
      const csrfToken = data.csrf_token;
      const isValidCsrf = await csrfHelper.validateToken(context, csrfToken);

      if (!isValidCsrf) {
        return {
          success: false,
          error:
            'Token de segurança inválido. Por favor, recarregue a página e tente novamente.',
        };
      }

      // Validar dados de entrada
      if (!data.name || data.name.trim().length < 3) {
        return {
          success: false,
          error: 'Nome deve ter pelo menos 3 caracteres',
        };
      }

      if (!data.email || !validateEmail(data.email)) {
        return {
          success: false,
          error: 'Email inválido',
        };
      }

      if (!data.password || !validatePassword(data.password)) {
        return {
          success: false,
          error:
            'A senha deve ter pelo menos 8 caracteres, uma letra maiúscula, uma minúscula e um número',
        };
      }

      if (data.password !== data.password_confirm) {
        return {
          success: false,
          error: 'As senhas não coincidem',
        };
      }

      // Verificar se o email já está em uso
      const existingUser = await userRepository.read(
        undefined,
        undefined,
        undefined,
        data.email,
        undefined,
        undefined,
        undefined
      );

      if (existingUser.rows.length > 0) {
        return {
          success: false,
          error: 'Este email já está em uso',
        };
      }

      // Determinar o tipo de usuário e escola
      const is_teacher = data.is_teacher === 'true';
      const userType = await userTypeRepository.read(
        undefined,
        is_teacher ? 'Professor' : 'Usuário',
        undefined
      );

      // Buscar ulid_user_type baseado no nome
      const ulid_user_type = userType.rows[0].ulid_user_type;

      // Verificar se o tipo de escola é válido
      let school_type = data.ulid_school_type;
      if (is_teacher) {
        school_type = 'none';
      } else if (!school_type || school_type === 'none') {
        return {
          success: false,
          error: 'Tipo de escola é obrigatório para usuários não professores',
        };
      }

      // Hash da senha com configurações seguras
      const hashedPassword = await argon2.hash(data.password, {
        type: argon2.argon2id, // Algoritmo mais seguro
        memoryCost: 2 ** 16, // 64 MiB
        timeCost: 3, // 3 iterações
        parallelism: 1, // 1 thread
      });

      // Criar o usuário
      const result = await userRepository.create(
        ulid_user_type,
        school_type,
        data.email,
        hashedPassword,
        is_teacher,
        data.name,
        data.state,
        data.county
      );

      const newUser = result.rows[0] as UserData;

      // Registrar evento de registro
      await logUserEvent(newUser.ulid_user, 'user_registered', {
        ip:
          request.headers.get('x-forwarded-for') ||
          request.headers.get('x-real-ip') ||
          'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown',
      });

      // Armazenar dados do usuário na sessão
      await session?.set('user', {
        ulid_user: newUser.ulid_user,
        name: newUser.name,
        email: newUser.email,
        is_teacher: newUser.is_teacher,
        ulid_user_type: newUser.ulid_user_type,
        ulid_school_type: newUser.ulid_school_type,
        state: newUser.state,
        county: newUser.county,
      });

      await session?.set('isAuthenticated', true);

      // Rotacionar token CSRF após registro bem-sucedido
      await csrfHelper.rotateToken(context);

      // Enviar email de boas-vindas
      try {
        await emailService.sendEmail({
          to: newUser.email,
          subject: 'Bem-vindo à Estação Alfabetização',
          html: `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
              <h2>Bem-vindo à Estação Alfabetização!</h2>
              <p>Olá ${newUser.name},</p>
              <p>Sua conta foi criada com sucesso!</p>
              <p>Agora você pode acessar todos os recursos da nossa plataforma.</p>
              <p>Atenciosamente,<br>Equipe Estação Alfabetização</p>
            </div>
          `,
        });
      } catch (emailError) {
        logger.error('Erro ao enviar email de boas-vindas:', emailError);
        // Não falhar o registro se o email falhar
      }

      return {
        success: true,
        result: {
          user: {
            ulid_user: newUser.ulid_user,
            name: newUser.name,
            email: newUser.email,
            is_teacher: newUser.is_teacher,
            ulid_user_type: newUser.ulid_user_type,
            ulid_school_type: newUser.ulid_school_type,
            state: newUser.state,
            county: newUser.county,
          },
        },
      };
    } catch (error) {
      logger.error('Erro ao registrar usuário:', error);
      return {
        success: false,
        error:
          'Erro ao registrar usuário. Por favor, tente novamente mais tarde.',
      };
    }
  },
});

export const signout = defineAction({
  async handler(_: unknown, context: ActionContext) {
    try {
      const { session, locals, request } = context;

      // Obter usuário atual para registro de evento
      const user = await session?.get('user');

      if (user) {
        // Registrar evento de logout
        await logUserEvent(user.ulid_user, 'user_logout', {
          ip:
            request.headers.get('x-forwarded-for') ||
            request.headers.get('x-real-ip') ||
            'unknown',
          userAgent: request.headers.get('user-agent') || 'unknown',
        });
      }

      // Destruir a sessão
      await session?.destroy();

      // Limpar o usuário dos locals
      locals.user = undefined;

      return {
        success: true,
        redirect: '/signin',
      };
    } catch (error) {
      logger.error('Erro ao realizar logout:', error);
      return {
        success: false,
        error: 'Erro ao realizar logout. Por favor, tente novamente.',
      };
    }
  },
});

/**
 * Interface para solicitação de recuperação de senha
 */
interface ForgotPasswordInput {
  email: string;
  csrf_token: string;
}

/**
 * Interface para redefinição de senha
 */
interface ResetPasswordInput {
  token: string;
  password: string;
  confirmPassword: string;
  csrf_token: string;
}

/**
 * Action para solicitar recuperação de senha
 */
export const forgotPassword = defineAction({
  async handler(data: ForgotPasswordInput, context: ActionContext) {
    try {
      const { email, csrf_token } = data;

      // Validar token CSRF
      const isValidCsrf = await csrfHelper.validateToken(context, csrf_token);
      if (!isValidCsrf) {
        return {
          success: false,
          error:
            'Token de segurança inválido. Por favor, recarregue a página e tente novamente.',
        };
      }

      // Validar email
      if (!email || !validateEmail(email)) {
        return {
          success: false,
          error: 'Email inválido',
        };
      }

      // Buscar usuário pelo email
      const userResult = await userRepository.read(
        undefined,
        undefined,
        undefined,
        email,
        undefined,
        undefined,
        undefined,
        true
      );

      // Se o usuário não existir, retornar sucesso de qualquer forma
      // (por segurança, não informamos se o email existe ou não)
      if (userResult.rowCount === 0) {
        // Registrar tentativa para email inexistente
        await logUserEvent('blocked', 'password_reset_request', {
          email,
          ip: context.request.headers.get('x-forwarded-for') || 'unknown',
          userAgent: context.request.headers.get('user-agent') || 'unknown',
          status: 'email_not_found',
        });

        // Simular um pequeno atraso para evitar timing attacks
        await new Promise(resolve =>
          setTimeout(resolve, 500 + Math.random() * 500)
        );

        return {
          success: true,
          message:
            'Se o email estiver cadastrado, você receberá instruções para redefinir sua senha.',
        };
      }

      const user = userResult.rows[0] as UserData;

      // Gerar token de recuperação de senha (válido por 1 hora)
      const resetToken = jwtService.generateToken(
        {
          sub: user.ulid_user,
          email: user.email,
          type: 'reset',
        },
        '1h'
      );

      // Salvar token no banco de dados
      const expiresAt = new Date();
      expiresAt.setHours(expiresAt.getHours() + 1);

      await tokenRepository.saveToken(
        user.ulid_user,
        resetToken,
        'reset',
        expiresAt
      );

      // Enviar email de recuperação de senha
      const resetLink = `${process.env.APP_URL || 'http://localhost:4321'}/reset-password?token=${resetToken}`;

      await emailService.sendEmail({
        to: user.email,
        subject: 'Recuperação de Senha - Estação Alfabetização',
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2>Recuperação de Senha</h2>
            <p>Olá ${user.name},</p>
            <p>Você solicitou a recuperação de sua senha.</p>
            <p>Clique no link abaixo para redefinir sua senha:</p>
            <p><a href="${resetLink}" style="background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Redefinir Senha</a></p>
            <p>Este link expira em 1 hora.</p>
            <p>Se você não solicitou esta recuperação, ignore este email.</p>
            <p>Atenciosamente,<br>Equipe Estação Alfabetização</p>
          </div>
        `,
      });

      // Registrar evento de solicitação de recuperação de senha
      await logUserEvent(user.ulid_user, 'password_reset_request', {
        ip: context.request.headers.get('x-forwarded-for') || 'unknown',
        userAgent: context.request.headers.get('user-agent') || 'unknown',
        status: 'email_sent',
      });

      return {
        success: true,
        message: 'Email de recuperação enviado com sucesso.',
      };
    } catch (error) {
      logger.error('Erro ao solicitar recuperação de senha:', error);
      return {
        success: false,
        error:
          'Erro ao processar solicitação. Por favor, tente novamente mais tarde.',
      };
    }
  },
});

/**
 * Action para redefinir senha
 */
export const resetPassword = defineAction({
  async handler(data: ResetPasswordInput, context: ActionContext) {
    try {
      const { token, password, confirmPassword, csrf_token } = data;

      // Validar token CSRF
      const isValidCsrf = await csrfHelper.validateToken(context, csrf_token);
      if (!isValidCsrf) {
        return {
          success: false,
          error:
            'Token de segurança inválido. Por favor, recarregue a página e tente novamente.',
        };
      }

      // Validar token de reset
      const payload = jwtService.verifyToken(token, 'reset');
      if (!payload || payload.type !== 'reset') {
        return {
          success: false,
          error:
            'Token inválido ou expirado. Solicite um novo link de recuperação.',
        };
      }

      // Verificar se o token está no banco e não foi revogado
      const tokenResult = await tokenRepository.findToken(token);
      if (tokenResult.rowCount === 0 || tokenResult.rows[0].is_revoked) {
        return {
          success: false,
          error:
            'Token já utilizado ou revogado. Solicite um novo link de recuperação.',
        };
      }

      // Validar senha
      if (!password || password.length < 8) {
        return {
          success: false,
          error: 'A senha deve ter pelo menos 8 caracteres.',
        };
      }

      // Validar confirmação de senha
      if (password !== confirmPassword) {
        return {
          success: false,
          error: 'As senhas não coincidem.',
        };
      }

      // Buscar usuário pelo ID
      const userResult = await userRepository.read(
        payload.sub,
        undefined,
        undefined,
        undefined,
        undefined,
        undefined,
        undefined,
        true
      );

      if (userResult.rowCount === 0) {
        return {
          success: false,
          error: 'Usuário não encontrado.',
        };
      }

      const user = userResult.rows[0] as UserData;

      // Gerar hash da nova senha
      const hashedPassword = await argon2.hash(password);

      // Atualizar senha do usuário
      await userRepository.updatePassword(user.ulid_user, hashedPassword);

      // Revogar token de reset
      await tokenRepository.revokeToken(token);

      // Revogar todos os tokens de acesso e refresh do usuário
      await tokenRepository.revokeAllUserTokens(user.ulid_user);

      // Registrar evento de redefinição de senha
      await logUserEvent(user.ulid_user, 'password_reset_success', {
        ip: context.request.headers.get('x-forwarded-for') || 'unknown',
        userAgent: context.request.headers.get('user-agent') || 'unknown',
      });

      // Enviar email de confirmação
      await emailService.sendEmail({
        to: user.email,
        subject: 'Senha Alterada - Estação Alfabetização',
        template: 'password-changed',
        data: {
          userName: user.name,
          timestamp: new Date().toLocaleString('pt-BR'),
        },
      });

      return {
        success: true,
        message:
          'Senha redefinida com sucesso. Você já pode fazer login com sua nova senha.',
      };
    } catch (error) {
      logger.error('Erro ao redefinir senha:', error);
      return {
        success: false,
        error:
          'Erro ao processar solicitação. Por favor, tente novamente mais tarde.',
      };
    }
  },
});

export const authAction = {
  signin,
  signup,
  signout,
  forgotPassword,
  resetPassword,
};
