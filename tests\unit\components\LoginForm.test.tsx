/**
 * Testes para o componente LoginForm
 * 
 * Este arquivo contém testes unitários para o componente de formulário de login.
 * Parte da implementação da tarefa 9.1.1 - Testes unitários
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { LoginForm } from '../../../src/components/LoginForm';

// Mock do serviço de autenticação
const mockLogin = vi.fn();
vi.mock('../../../src/domain/services/AuthenticationService', () => ({
  AuthenticationService: vi.fn().mockImplementation(() => ({
    login: mockLogin
  }))
}));

// Mock do hook de navegação
const mockNavigate = vi.fn();
vi.mock('react-router-dom', () => ({
  useNavigate: () => mockNavigate
}));

describe('LoginForm', () => {
  beforeEach(() => {
    // Limpar mocks antes de cada teste
    vi.clearAllMocks();
  });
  
  it('should render the login form correctly', () => {
    render(<LoginForm />);
    
    // Verificar se os elementos do formulário estão presentes
    expect(screen.getByLabelText(/email/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/senha/i)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /entrar/i })).toBeInTheDocument();
    expect(screen.getByText(/esqueceu sua senha/i)).toBeInTheDocument();
    expect(screen.getByText(/criar uma conta/i)).toBeInTheDocument();
  });
  
  it('should show validation errors for empty fields', async () => {
    render(<LoginForm />);
    
    // Clicar no botão de login sem preencher os campos
    fireEvent.click(screen.getByRole('button', { name: /entrar/i }));
    
    // Verificar se as mensagens de erro são exibidas
    await waitFor(() => {
      expect(screen.getByText(/email é obrigatório/i)).toBeInTheDocument();
      expect(screen.getByText(/senha é obrigatória/i)).toBeInTheDocument();
    });
  });
  
  it('should show validation error for invalid email', async () => {
    render(<LoginForm />);
    
    // Preencher o campo de email com um valor inválido
    fireEvent.change(screen.getByLabelText(/email/i), {
      target: { value: 'invalid-email' }
    });
    
    // Preencher o campo de senha
    fireEvent.change(screen.getByLabelText(/senha/i), {
      target: { value: 'password123' }
    });
    
    // Clicar no botão de login
    fireEvent.click(screen.getByRole('button', { name: /entrar/i }));
    
    // Verificar se a mensagem de erro é exibida
    await waitFor(() => {
      expect(screen.getByText(/email inválido/i)).toBeInTheDocument();
    });
  });
  
  it('should call login service and navigate on successful login', async () => {
    // Configurar mock para retornar sucesso
    mockLogin.mockResolvedValue({
      user: {
        id: '1',
        name: 'Test User',
        email: '<EMAIL>',
        role: 'user'
      },
      accessToken: 'test_token',
      refreshToken: 'test_refresh_token',
      expiresIn: 3600
    });
    
    render(<LoginForm />);
    
    // Preencher o formulário
    fireEvent.change(screen.getByLabelText(/email/i), {
      target: { value: '<EMAIL>' }
    });
    
    fireEvent.change(screen.getByLabelText(/senha/i), {
      target: { value: 'password123' }
    });
    
    // Clicar no botão de login
    fireEvent.click(screen.getByRole('button', { name: /entrar/i }));
    
    // Verificar se o serviço de login foi chamado com os parâmetros corretos
    await waitFor(() => {
      expect(mockLogin).toHaveBeenCalledWith('<EMAIL>', 'password123');
    });
    
    // Verificar se o usuário foi redirecionado
    await waitFor(() => {
      expect(mockNavigate).toHaveBeenCalledWith('/dashboard');
    });
  });
  
  it('should show error message on login failure', async () => {
    // Configurar mock para retornar erro
    mockLogin.mockRejectedValue(new Error('Credenciais inválidas'));
    
    render(<LoginForm />);
    
    // Preencher o formulário
    fireEvent.change(screen.getByLabelText(/email/i), {
      target: { value: '<EMAIL>' }
    });
    
    fireEvent.change(screen.getByLabelText(/senha/i), {
      target: { value: 'wrong_password' }
    });
    
    // Clicar no botão de login
    fireEvent.click(screen.getByRole('button', { name: /entrar/i }));
    
    // Verificar se a mensagem de erro é exibida
    await waitFor(() => {
      expect(screen.getByText(/credenciais inválidas/i)).toBeInTheDocument();
    });
    
    // Verificar se o usuário não foi redirecionado
    expect(mockNavigate).not.toHaveBeenCalled();
  });
  
  it('should disable the login button while submitting', async () => {
    // Configurar mock para demorar um pouco para resolver
    mockLogin.mockImplementation(() => new Promise(resolve => {
      setTimeout(() => {
        resolve({
          user: {
            id: '1',
            name: 'Test User',
            email: '<EMAIL>',
            role: 'user'
          },
          accessToken: 'test_token',
          refreshToken: 'test_refresh_token',
          expiresIn: 3600
        });
      }, 100);
    }));
    
    render(<LoginForm />);
    
    // Preencher o formulário
    fireEvent.change(screen.getByLabelText(/email/i), {
      target: { value: '<EMAIL>' }
    });
    
    fireEvent.change(screen.getByLabelText(/senha/i), {
      target: { value: 'password123' }
    });
    
    // Clicar no botão de login
    fireEvent.click(screen.getByRole('button', { name: /entrar/i }));
    
    // Verificar se o botão está desabilitado durante o envio
    expect(screen.getByRole('button', { name: /entrar/i })).toBeDisabled();
    
    // Verificar se o botão é habilitado novamente após o login
    await waitFor(() => {
      expect(screen.getByRole('button', { name: /entrar/i })).not.toBeDisabled();
    });
  });
  
  it('should navigate to forgot password page when clicking on forgot password link', () => {
    render(<LoginForm />);
    
    // Clicar no link de esqueceu a senha
    fireEvent.click(screen.getByText(/esqueceu sua senha/i));
    
    // Verificar se o usuário foi redirecionado
    expect(mockNavigate).toHaveBeenCalledWith('/forgot-password');
  });
  
  it('should navigate to register page when clicking on create account link', () => {
    render(<LoginForm />);
    
    // Clicar no link de criar conta
    fireEvent.click(screen.getByText(/criar uma conta/i));
    
    // Verificar se o usuário foi redirecionado
    expect(mockNavigate).toHaveBeenCalledWith('/register');
  });
});
