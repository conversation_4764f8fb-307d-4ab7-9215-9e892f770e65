/**
 * Implementação de conexão com PostgreSQL
 *
 * Esta classe implementa a interface DatabaseConnection usando PostgreSQL.
 * Seguindo a Clean Architecture, esta é uma implementação concreta na
 * camada de frameworks e drivers.
 */

import { Pool, PoolClient } from 'pg';
import { config } from '../../config';
import { Logger } from '../../domain/interfaces/Logger';
import { DatabaseConnection, QueryResult, TransactionClient } from './DatabaseConnection';

/**
 * Cliente de transação PostgreSQL
 */
class PostgresTransactionClient implements TransactionClient {
  /**
   * Cria uma nova instância de PostgresTransactionClient
   * @param client - Cliente PostgreSQL
   */
  constructor(private readonly client: PoolClient) {}

  /**
   * Executa uma consulta SQL dentro da transação
   * @param text - Texto da consulta
   * @param params - Parâmetros da consulta
   * @returns Resultado da consulta
   */
  async query(text: string, params?: any[]): Promise<QueryResult> {
    const result = await this.client.query(text, params);
    return {
      rows: result.rows,
      rowCount: result.rowCount,
    };
  }
}

/**
 * Implementação de conexão com PostgreSQL
 */
export class PostgresDatabaseConnection implements DatabaseConnection {
  /**
   * Instância singleton
   */
  private static instance: PostgresDatabaseConnection;

  /**
   * Pool de conexões
   */
  private readonly pool: Pool;

  /**
   * Cria uma nova instância de PostgresDatabaseConnection
   * @param logger - Serviço de logging
   */
  private constructor(private readonly logger: Logger) {
    this.pool = new Pool({
      host: config.database.host,
      port: config.database.port,
      user: config.database.user,
      password: config.database.password,
      database: config.database.name,
      max: config.database.poolSize || 20,
      idleTimeoutMillis: 30000,
    });

    this.pool.on('error', (err) => {
      this.logger.error('Unexpected error on idle client', {
        error: err.message,
        stack: err.stack,
      });
    });

    this.logger.info('PostgreSQL connection pool created', {
      host: config.database.host,
      database: config.database.name,
      poolSize: config.database.poolSize || 20,
    });
  }

  /**
   * Obtém a instância singleton
   * @param logger - Serviço de logging
   * @returns Instância de PostgresDatabaseConnection
   */
  public static getInstance(logger: Logger): PostgresDatabaseConnection {
    if (!PostgresDatabaseConnection.instance) {
      PostgresDatabaseConnection.instance = new PostgresDatabaseConnection(logger);
    }
    return PostgresDatabaseConnection.instance;
  }

  /**
   * Executa uma consulta SQL
   * @param text - Texto da consulta
   * @param params - Parâmetros da consulta
   * @returns Resultado da consulta
   */
  async query(text: string, params?: any[]): Promise<QueryResult> {
    const client = await this.pool.connect();
    try {
      const result = await client.query(text, params);
      return {
        rows: result.rows,
        rowCount: result.rowCount,
      };
    } catch (error) {
      this.logger.error('Error executing query', {
        query: text,
        params,
        error: error instanceof Error ? error.message : String(error),
      });
      throw error;
    } finally {
      client.release();
    }
  }

  /**
   * Executa uma consulta SQL dentro de uma transação
   * @param callback - Função que recebe um cliente de transação
   * @returns Resultado da função de callback
   */
  async transaction<T>(callback: (client: TransactionClient) => Promise<T>): Promise<T> {
    const client = await this.pool.connect();
    try {
      await client.query('BEGIN');

      const transactionClient = new PostgresTransactionClient(client);
      const result = await callback(transactionClient);

      await client.query('COMMIT');
      return result;
    } catch (error) {
      await client.query('ROLLBACK');

      this.logger.error('Transaction failed', {
        error: error instanceof Error ? error.message : String(error),
      });

      throw error;
    } finally {
      client.release();
    }
  }

  /**
   * Fecha a conexão com o banco de dados
   */
  async close(): Promise<void> {
    await this.pool.end();
    this.logger.info('PostgreSQL connection pool closed');
  }
}
