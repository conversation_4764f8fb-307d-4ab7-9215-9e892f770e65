---
/**
 * Base layout component
 * Provides the basic HTML structure for all pages
 * Integrates View Transitions API for smooth page transitions
 */
import { ThemeProvider } from '../layouts/themes';
import '../layouts/themes/themes.css';
import '../styles/transitions.css';
import { ViewTransitions } from 'astro:transitions';
import PWAInstaller from '../components/PWAInstaller.astro';
import AnalyticsScript from '../components/analytics/AnalyticsScript.astro';
import RemarketingPixel from '../components/marketing/RemarketingPixel.astro';
import WebVitalsMonitor from '../components/performance/WebVitalsMonitor.astro';
import { supportsViewTransitions } from '../utils/viewTransitionUtils';

interface Props {
  title?: string;
  description?: string;
  theme?: string;
  disableWebVitals?: boolean;
  /**
   * Habilitar View Transitions
   * @default true
   */
  enableViewTransitions?: boolean;
  /**
   * Tipo de transição padrão
   * @default "fade"
   */
  transitionType?: 'fade' | 'slide' | 'scale' | 'flip' | 'none';
  /**
   * Duração da transição
   * @default "0.3s"
   */
  transitionDuration?: string;
}

const {
  title = 'Estação da Alfabetização',
  description = 'Estação da Alfabetização - Plataforma educacional para alfabetização',
  theme = 'light',
  disableWebVitals = false,
  enableViewTransitions = true,
  transitionType = 'fade',
  transitionDuration = '0.3s',
} = Astro.props;

// Get user from locals (set by middleware)
const user = Astro.locals.user;

// Verificar se o navegador suporta View Transitions
const hasViewTransitionsSupport = supportsViewTransitions();
---

<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="description" content={description}>
  <meta name="generator" content={Astro.generator} />

  {/* Meta tags para PWA */}
  <meta name="theme-color" content="#0078d7">
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="default">
  <meta name="apple-mobile-web-app-title" content="Estação Alfabetização">
  <meta name="application-name" content="Estação Alfabetização">
  <meta name="format-detection" content="telephone=no">
  <meta name="mobile-web-app-capable" content="yes">

  {/* Links para PWA */}
  <link rel="manifest" href="/manifest.json">
  <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
  <link rel="apple-touch-icon" href="/icons/apple-touch-icon.png">
  <link rel="apple-touch-startup-image" href="/icons/apple-splash-2048-2732.jpg" media="(device-width: 1024px) and (device-height: 1366px) and (-webkit-device-pixel-ratio: 2) and (orientation: portrait)">
  <link rel="apple-touch-startup-image" href="/icons/apple-splash-1668-2388.jpg" media="(device-width: 834px) and (device-height: 1194px) and (-webkit-device-pixel-ratio: 2) and (orientation: portrait)">
  <link rel="apple-touch-startup-image" href="/icons/apple-splash-1536-2048.jpg" media="(device-width: 768px) and (device-height: 1024px) and (-webkit-device-pixel-ratio: 2) and (orientation: portrait)">
  <link rel="apple-touch-startup-image" href="/icons/apple-splash-1125-2436.jpg" media="(device-width: 375px) and (device-height: 812px) and (-webkit-device-pixel-ratio: 3) and (orientation: portrait)">
  <link rel="apple-touch-startup-image" href="/icons/apple-splash-1242-2688.jpg" media="(device-width: 414px) and (device-height: 896px) and (-webkit-device-pixel-ratio: 3) and (orientation: portrait)">
  <link rel="apple-touch-startup-image" href="/icons/apple-splash-828-1792.jpg" media="(device-width: 414px) and (device-height: 896px) and (-webkit-device-pixel-ratio: 2) and (orientation: portrait)">
  <link rel="apple-touch-startup-image" href="/icons/apple-splash-750-1334.jpg" media="(device-width: 375px) and (device-height: 667px) and (-webkit-device-pixel-ratio: 2) and (orientation: portrait)">
  <link rel="apple-touch-startup-image" href="/icons/apple-splash-640-1136.jpg" media="(device-width: 320px) and (device-height: 568px) and (-webkit-device-pixel-ratio: 2) and (orientation: portrait)">

  <link href="https://fonts.googleapis.com/css2?family=Coiny&family=Delius+Swash+Caps&family=Gwendolyn:wght@700&family=Proxima+Nova:wght@400;700&display=swap" rel="stylesheet">
  <title>{title}</title>

  {/* Adicionar View Transitions se habilitado */}
  {enableViewTransitions && <ViewTransitions />}

  <!-- Critical CSS -->
  <style is:inline>
    /* Critical CSS for initial render */
    :root {
      --color-primary: #4a6cf7;
      --color-primary-dark: #3a5ce5;
      --color-text: #334155;
      --color-text-light: #64748b;
      --color-text-dark: #1e293b;
      --color-bg: #f8fafc;
      --color-bg-dark: #f1f5f9;
    }

    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }

    body {
      font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      line-height: 1.6;
      color: var(--color-text);
      background-color: var(--color-bg);
    }

    .layout {
      display: flex;
      flex-direction: column;
      min-height: 100vh;
    }

    .main-content {
      flex: 1;
    }
  </style>
</head>
<body>
  <ThemeProvider defaultTheme={theme} enableSystem={true}>
    <div class="l-body"
      data-transition-type={transitionType}
      data-transition-duration={transitionDuration}
      data-astro-transition-scope
    >
      <div id="page-content" class={`page-transition pt-${transitionType}`}>
        <slot />
      </div>
    </div>
  </ThemeProvider>

  {/* Monitoramento de Core Web Vitals */}
  {!disableWebVitals && <WebVitalsMonitor pageId={Astro.url.pathname} />}

  {/* Analytics Script */}
  <AnalyticsScript
    trackingId={import.meta.env.GOOGLE_ANALYTICS_ID}
    anonymizeIp={true}
    sendUserId={false}
    customDimensions={{
      '1': 'visitor',
      '2': 'anonymous'
    }}
  />

  {/* Remarketing Pixels */}
  <RemarketingPixel
    pixelIds={{
      facebook: import.meta.env.FACEBOOK_PIXEL_ID,
      google: import.meta.env.GOOGLE_TAG_MANAGER_ID,
      tiktok: import.meta.env.TIKTOK_PIXEL_ID
    }}
    trackPageView={true}
    pageData={{
      path: Astro.url.pathname,
      title: title
    }}
  />

  {/* Componente PWA Installer */}
  <PWAInstaller
    swPath="/service-worker.js"
    showInstallButton={true}
    showUpdateNotification={true}
  />

  {/* Script para detecção de suporte a View Transitions */}
  <script>
    // Verificar suporte à View Transitions API
    if (!document.startViewTransition) {
      document.documentElement.classList.add('no-view-transition');
    } else {
      document.documentElement.classList.add('has-view-transition');
    }

    // Configurar manipuladores de eventos para transições
    document.addEventListener('astro:page-load', () => {
      // Inicializar elementos com transição
      const pageContent = document.getElementById('page-content');
      if (pageContent) {
        const transitionType = document.querySelector('.l-body')?.getAttribute('data-transition-type') || 'fade';
        const transitionDuration = document.querySelector('.l-body')?.getAttribute('data-transition-duration') || '0.3s';

        // Aplicar classes de transição
        pageContent.style.setProperty('--transition-duration', transitionDuration);
        pageContent.classList.add('transition-loaded');
      }
    });

    // Manipulador para início de transição
    document.addEventListener('astro:before-preparation', () => {
      const pageContent = document.getElementById('page-content');
      if (pageContent) {
        pageContent.classList.add('transition-exit');
      }
    });
  </script>
</body>
</html>
<style is:global lang="scss">
// Azul     - rgba( 30, 144, 255, 1.0)
// Vermelho - rgba(255,  69,   0, 1.0)
// Amarelo  - rgba(255, 215,   0, 1.0)
// Verde    - rgba( 50, 205,  50, 1.0)
// Rosa     - rgba(255, 105, 180, 1.0)
// Laranja  - rgba(255, 165,   0, 1.0)
//  Roxo     - rgba(128,   0, 128, 1.0)
:root {
		--header-height               : 80px;
		--color-accent-20             : rgba(255, 165,   0, 0.2);
		--color-accent-40             : rgba(255, 165,   0, 0.4);
		--color-accent-70             : rgba(255, 165,   0, 0.7);
		--color-accent-100            : rgba(255, 165,   0, 1.0);
		--color-base-50               : rgba(255, 255, 255, 0.5);
		--color-base-100              : rgba(255, 255, 255, 1.0);
		--color-error-20              : rgba(255,  69,   0, 0.2);
		--color-error-50              : rgba(255,  69,   0, 0.5);
		--color-error-100             : rgba(255,  69,   0, 1.0);
		--color-gray-20               : rgba(128, 128, 128, 0.2);
		--color-gray-100              : rgba(128, 128, 128, 1.0);
		--color-info-100              : rgba( 30, 144, 255, 1.0);
		--color-neutral-70            : rgba(  0,   0,   0, 0.7);
		--color-neutral-100           : rgba(  0,   0,   0, 1.0);

		--color-opposite-accent-100   : rgba(  0,  90, 255, 1.0);
		--color-opposite-error-100    : rgba(  0, 186, 255, 1.0);
		--color-opposite-info-100     : rgba(225, 111,   0, 1.0);
		--color-opposite-primary-100  : rgba(225, 111,   0, 1.0);
		--color-opposite-secondary-100: rgba(  0, 150,  75, 1.0);
		--color-opposite-success-100  : rgba(205,  50, 205, 1.0);
		--color-opposite-warning-100  : rgba(  0,  40, 255, 1.0);

		--color-primary-20            : rgba( 30, 144, 255, 0.2);
		--color-primary-70            : rgba( 30, 144, 255, 0.7);
		--color-primary-100           : rgba( 30, 144, 255, 1.0);
		--color-royal-blue-70         : rgba(  0,   0, 204, 0.7);
		--color-royal-blue-100        : rgba(  0,   0, 204, 1.0);
		--color-secondary-100         : rgba(255, 105, 180, 1.0);
		--color-success-20            : rgba( 50, 205,  50, 0.2);
		--color-success-40            : rgba( 50, 205,  50, 0.4);
		--color-success-100           : rgba( 50, 205,  50, 1.0);
		--color-transparent           : rgba(  0,   0,   0, 0.0);
		--color-warning-100           : rgba(255, 215,   0, 1.0);
		--font-name                   : 'Delius Swash Caps', cursive;
		--image-width                 : 545px;
		--image-min-width             : 320px;
		--menu-width                  : 285px;
	}

	* {
		box-sizing: border-box;
	}

	body {
		margin  : 0;
		padding : 0;
	}

	html {
		font-size: 16px;
	}

	a, button, p, select, span, .s-highlighted {
    text-shadow: -1px -1px 0px var(--color-base-100),
                 -1px  0px 0px var(--color-base-100),
                 -1px  1px 0px var(--color-base-100),
                  0px -1px 0px var(--color-base-100),
                  0px  1px 0px var(--color-base-100),
                  1px -1px 0px var(--color-base-100),
                  1px  0px 0px var(--color-base-100),
                  1px  1px 0px var(--color-base-100);
	}

	img {
		border-radius: 5px;
	}

	input, option, select {
		font-weight: bold;
	}

	option {
		color: var(--color-neutral-100);
	}

	p {
		margin: 5px;
	}

	.l-body {
		background     : linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 1) 13%, rgba(255, 255, 255, 0.8) 13%), url('/bg.jpg');
		background-size: 200px;
		height         : 100vh;
		width          : 100vw;
	}

	/* Estilos para transições de página */
	.page-transition {
	  position: relative;
	  width: 100%;
	  transition-property: opacity, transform;
	  transition-duration: var(--transition-duration, 0.3s);
	  transition-timing-function: ease-in-out;
	  will-change: opacity, transform;
	}

	/* Fade transition */
	.pt-fade {
	  opacity: 0;
	}

	.pt-fade.transition-loaded {
	  opacity: 1;
	}

	.pt-fade.transition-exit {
	  opacity: 0;
	}

	/* Slide transition */
	.pt-slide {
	  transform: translateX(30px);
	  opacity: 0;
	}

	.pt-slide.transition-loaded {
	  transform: translateX(0);
	  opacity: 1;
	}

	.pt-slide.transition-exit {
	  transform: translateX(-30px);
	  opacity: 0;
	}

	/* Scale transition */
	.pt-scale {
	  transform: scale(0.95);
	  opacity: 0;
	}

	.pt-scale.transition-loaded {
	  transform: scale(1);
	  opacity: 1;
	}

	.pt-scale.transition-exit {
	  transform: scale(1.05);
	  opacity: 0;
	}

	/* Flip transition */
	.pt-flip {
	  transform: rotateY(90deg);
	  opacity: 0;
	  transform-style: preserve-3d;
	  perspective: 1000px;
	}

	.pt-flip.transition-loaded {
	  transform: rotateY(0deg);
	  opacity: 1;
	}

	.pt-flip.transition-exit {
	  transform: rotateY(-90deg);
	  opacity: 0;
	}

	.c-back-header {
		background-image : url('/bg3.jpg');
		background-size  : 300px;
		background-repeat: repeat;
		border-bottom    : 2px solid var(--color-warning-100);
		height           : var(--header-height);
		opacity          : 1;
		position         : fixed;
		top              : 0px;
		width            : 100%;
		z-index          : 1;
	}

  .c-btn-confirm {
    margin: 8px 0px 0px 0px;
  }

	.c-button-default {
		border-radius: 25px;
    padding      : 8px;
  }

	.c-email {
		margin: 0px 0px 4px 0px;
		width : 100%;
	}

	.c-front-header {
		background     : linear-gradient(90deg, var(--color-royal-blue-70) 0%, var(--color-primary-70) 50%, var(--color-primary-20) 100%);
		border-bottom  : 1px solid var(--color-warning-100);
		height         : var(--header-height);
		justify-content: center;
		opacity        : 1;
		padding-left   : 1rem;
		position       : fixed;
		top            : 0px;
		width          : 100%;
		z-index        : 1;
	}

	.c-password {
		width: 100%;
	}

	.c-shadowed {
		box-shadow: 2px 2px 4px 0px var(--color-neutral-70);
	}

	@mixin text-shadow($colorShadow, $fontColor) {
		color      : $fontColor;
		text-shadow: -1px -1px 0px $colorShadow,
                 -1px  0px 0px $colorShadow,
                 -1px  1px 0px $colorShadow,
                  0px -1px 0px $colorShadow,
                  0px  1px 0px $colorShadow,
                  1px -1px 0px $colorShadow,
                  1px  0px 0px $colorShadow,
                  1px  1px 0px $colorShadow;
	}

	.c-text-shadow-sign-link {
		@include text-shadow(var(--color-base-100), var(--color-secondary-100));
	}

	.c-text-shadow-sign-span {
		@include text-shadow(var(--color-base-100), var(--color-primary-100));
	}

	.c-text-shadow-payment {
		@include text-shadow(var(--color-base-100), var(--color-neutral-100));
	}

	.c-title-adds {
		background-color: var(--color-primary-100);
		border          : 2px solid var(--color-base-100);
		border-radius   : 8px;
		justify-content : center;
		padding         : 4px;
		width           : 100%;
	}

	.c-title-font {
		color      : var(--color-neutral-100);
		font-family: 'Gwendolyn', cursive;
		font-size  : 3rem;
	}

	.c-title-train {
		/* background-color: var(--color-transparent);
		border: 2px solid var(--color-base-100);
		border-radius: 25px;
		padding: 6px; */
		width: 85px;
	}

	.c-tooltip-custom-props {
		border       : 3px solid var(--color-base-100);
		border-radius: 26px;
		color        : black;
		font-family  : var(--font-name);
		font-weight  : bold;
		margin       : 8px;
	}
</style>