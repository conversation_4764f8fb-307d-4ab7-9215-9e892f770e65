/**
 * Default Social Sharing Service
 *
 * Implementação padrão do serviço de compartilhamento em redes sociais.
 * Parte da implementação da tarefa 8.9.2 - Integração com redes sociais
 */

import {
  ShareOptions,
  SocialSharingService,
  SocialWidgetOptions,
} from '../../domain/services/SocialSharingService';

export class DefaultSocialSharingService implements SocialSharingService {
  private baseUrl: string;
  private siteName: string;
  private twitterHandle: string;

  constructor(baseUrl: string, siteName: string, twitterHandle: string) {
    this.baseUrl = baseUrl;
    this.siteName = siteName;
    this.twitterHandle = twitterHandle;
  }

  /**
   * Gera URL para compartilhamento em uma rede social específica
   */
  generateShareUrl(
    platform: 'facebook' | 'twitter' | 'linkedin' | 'pinterest' | 'whatsapp' | 'telegram' | 'email',
    options: ShareOptions
  ): string {
    // Normalizar URL
    const url = this.normalizeUrl(options.url);

    // Gerar URL de compartilhamento de acordo com a plataforma
    switch (platform) {
      case 'facebook':
        return `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`;

      case 'twitter': {
        let twitterUrl = `https://twitter.com/intent/tweet?url=${encodeURIComponent(url)}`;

        if (options.title) {
          twitterUrl += `&text=${encodeURIComponent(options.title)}`;
        }

        if (options.hashtags && options.hashtags.length > 0) {
          twitterUrl += `&hashtags=${options.hashtags.join(',')}`;
        }

        if (options.via || this.twitterHandle) {
          twitterUrl += `&via=${encodeURIComponent(options.via || this.twitterHandle.replace('@', ''))}`;
        }

        return twitterUrl;
      }

      case 'linkedin': {
        let linkedinUrl = `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(url)}`;

        if (options.title) {
          linkedinUrl += `&title=${encodeURIComponent(options.title)}`;
        }

        if (options.description) {
          linkedinUrl += `&summary=${encodeURIComponent(options.description)}`;
        }

        return linkedinUrl;
      }

      case 'pinterest': {
        let pinterestUrl = `https://pinterest.com/pin/create/button/?url=${encodeURIComponent(url)}`;

        if (options.image) {
          pinterestUrl += `&media=${encodeURIComponent(options.image)}`;
        }

        if (options.description) {
          pinterestUrl += `&description=${encodeURIComponent(options.description)}`;
        }

        return pinterestUrl;
      }

      case 'whatsapp': {
        const whatsappText = options.title ? `${options.title} ${url}` : url;
        return `https://wa.me/?text=${encodeURIComponent(whatsappText)}`;
      }

      case 'telegram': {
        const telegramText = options.title ? `${options.title} ${url}` : url;
        return `https://t.me/share/url?url=${encodeURIComponent(url)}&text=${encodeURIComponent(options.title || '')}`;
      }

      case 'email': {
        const subject = options.title || `Compartilhando: ${this.siteName}`;
        let body = `${options.title || 'Confira este link'}: ${url}`;

        if (options.description) {
          body += `\n\n${options.description}`;
        }

        return `mailto:?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;
      }

      default:
        return url;
    }
  }

  /**
   * Gera código HTML para botões de compartilhamento
   */
  generateShareButtons(
    options: ShareOptions,
    platforms?: Array<
      'facebook' | 'twitter' | 'linkedin' | 'pinterest' | 'whatsapp' | 'telegram' | 'email'
    >,
    buttonStyle: 'icon' | 'text' | 'icon-text' = 'icon-text'
  ): string {
    // Definir plataformas padrão se não fornecidas
    const defaultPlatforms: Array<
      'facebook' | 'twitter' | 'linkedin' | 'pinterest' | 'whatsapp' | 'telegram' | 'email'
    > = ['facebook', 'twitter', 'whatsapp', 'telegram', 'email'];

    const sharePlatforms = platforms || defaultPlatforms;

    // Gerar HTML para os botões
    let html = '<div class="social-share-buttons">';

    sharePlatforms.forEach((platform) => {
      const shareUrl = this.generateShareUrl(platform, options);
      const platformName = this.getPlatformName(platform);
      const iconClass = this.getPlatformIconClass(platform);

      let buttonContent = '';

      switch (buttonStyle) {
        case 'icon':
          buttonContent = `<i class="${iconClass}" aria-hidden="true"></i><span class="sr-only">Compartilhar no ${platformName}</span>`;
          break;
        case 'text':
          buttonContent = `Compartilhar no ${platformName}`;
          break;
        case 'icon-text':
          buttonContent = `<i class="${iconClass}" aria-hidden="true"></i> ${platformName}`;
          break;
      }

      html += `
        <a href="${shareUrl}" 
           class="social-share-button social-share-${platform}" 
           target="_blank" 
           rel="noopener noreferrer" 
           aria-label="Compartilhar no ${platformName}">
          ${buttonContent}
        </a>
      `;
    });

    html += '</div>';

    return html;
  }

  /**
   * Gera código HTML para botão de curtir do Facebook
   */
  generateFacebookLikeButton(pageUrl: string, options?: SocialWidgetOptions): string {
    const url = this.normalizeUrl(pageUrl);
    const layout = options?.layout || 'standard';
    const showFaces = options?.showFaces !== undefined ? options.showFaces : false;
    const width = options?.width || (layout === 'standard' ? 450 : 80);
    const height = options?.height || (layout === 'standard' ? 80 : 20);

    return `
      <div id="fb-root"></div>
      <div class="fb-like" 
        data-href="${url}" 
        data-width="${width}" 
        data-layout="${layout}" 
        data-action="like" 
        data-size="${options?.size || 'small'}" 
        data-share="true" 
        data-show-faces="${showFaces}">
      </div>
    `;
  }

  /**
   * Gera código HTML para botão de seguir do Twitter
   */
  generateTwitterFollowButton(username: string, options?: SocialWidgetOptions): string {
    // Remover @ se presente
    const twitterUsername = username.startsWith('@') ? username.substring(1) : username;
    const showCount = options?.showCount !== undefined ? options.showCount : true;
    const size = options?.size || 'medium';

    return `
      <a href="https://twitter.com/${twitterUsername}" 
         class="twitter-follow-button" 
         data-show-count="${showCount}" 
         data-size="${size === 'large' ? 'large' : ''}">
        Follow @${twitterUsername}
      </a>
    `;
  }

  /**
   * Gera código HTML para timeline do Twitter
   */
  generateTwitterTimeline(username: string, options?: SocialWidgetOptions): string {
    // Remover @ se presente
    const twitterUsername = username.startsWith('@') ? username.substring(1) : username;
    const height = options?.height || 400;
    const theme = options?.theme || 'light';

    return `
      <a class="twitter-timeline" 
         href="https://twitter.com/${twitterUsername}" 
         data-height="${height}" 
         data-theme="${theme}">
        Tweets by @${twitterUsername}
      </a>
    `;
  }

  /**
   * Gera código HTML para feed do Instagram
   */
  generateInstagramFeed(username: string, limit = 6, options?: SocialWidgetOptions): string {
    // Nota: O Instagram não oferece mais um widget oficial de feed
    // Esta é uma implementação alternativa usando a API pública

    return `
      <div class="instagram-feed" data-username="${username}" data-limit="${limit}">
        <div class="instagram-feed-header">
          <a href="https://www.instagram.com/${username}" target="_blank" rel="noopener noreferrer">
            <h3>@${username}</h3>
          </a>
        </div>
        <div class="instagram-feed-container" id="instagram-feed-${username}">
          <p>Carregando feed do Instagram...</p>
        </div>
        <div class="instagram-feed-footer">
          <a href="https://www.instagram.com/${username}" target="_blank" rel="noopener noreferrer">
            Ver mais no Instagram
          </a>
        </div>
      </div>
      <script>
        document.addEventListener('DOMContentLoaded', function() {
          loadInstagramFeed('${username}', ${limit});
        });
        
        function loadInstagramFeed(username, limit) {
          // Implementação do carregamento do feed via JavaScript
          // Nota: Requer implementação adicional no lado do servidor
          // devido às limitações da API do Instagram
        }
      </script>
    `;
  }

  /**
   * Gera código HTML para feed do Facebook
   */
  generateFacebookFeed(pageUrl: string, options?: SocialWidgetOptions): string {
    const height = options?.height || 500;
    const showFaces = options?.showFaces !== undefined ? options.showFaces : true;
    const width = options?.width || 340;

    return `
      <div id="fb-root"></div>
      <div class="fb-page" 
        data-href="${pageUrl}" 
        data-width="${width}" 
        data-height="${height}" 
        data-tabs="timeline" 
        data-small-header="false" 
        data-adapt-container-width="true" 
        data-hide-cover="false" 
        data-show-facepile="${showFaces}">
        <blockquote cite="${pageUrl}" class="fb-xfbml-parse-ignore">
          <a href="${pageUrl}">Carregando página...</a>
        </blockquote>
      </div>
    `;
  }

  /**
   * Gera código para inicialização dos SDKs das redes sociais
   */
  generateSocialSdkInitCode(
    platforms?: Array<'facebook' | 'twitter' | 'linkedin' | 'pinterest'>
  ): string {
    const defaultPlatforms: Array<'facebook' | 'twitter' | 'linkedin' | 'pinterest'> = [
      'facebook',
      'twitter',
    ];

    const initPlatforms = platforms || defaultPlatforms;
    let code = '';

    // Facebook SDK
    if (initPlatforms.includes('facebook')) {
      code += `
        <script>
          window.fbAsyncInit = function() {
            FB.init({
              appId: '${process.env.FACEBOOK_APP_ID || ''}',
              autoLogAppEvents: true,
              xfbml: true,
              version: 'v16.0'
            });
          };
          
          (function(d, s, id) {
            var js, fjs = d.getElementsByTagName(s)[0];
            if (d.getElementById(id)) return;
            js = d.createElement(s); js.id = id;
            js.src = "https://connect.facebook.net/pt_BR/sdk.js";
            fjs.parentNode.insertBefore(js, fjs);
          }(document, 'script', 'facebook-jssdk'));
        </script>
      `;
    }

    // Twitter SDK
    if (initPlatforms.includes('twitter')) {
      code += `
        <script>
          window.twttr = (function(d, s, id) {
            var js, fjs = d.getElementsByTagName(s)[0],
              t = window.twttr || {};
            if (d.getElementById(id)) return t;
            js = d.createElement(s);
            js.id = id;
            js.src = "https://platform.twitter.com/widgets.js";
            fjs.parentNode.insertBefore(js, fjs);
          
            t._e = [];
            t.ready = function(f) {
              t._e.push(f);
            };
          
            return t;
          }(document, "script", "twitter-wjs"));
        </script>
      `;
    }

    // LinkedIn SDK
    if (initPlatforms.includes('linkedin')) {
      code += `
        <script src="https://platform.linkedin.com/in.js" type="text/javascript">
          lang: pt_BR
        </script>
      `;
    }

    // Pinterest SDK
    if (initPlatforms.includes('pinterest')) {
      code += `
        <script async defer src="//assets.pinterest.com/js/pinit.js"></script>
      `;
    }

    return code;
  }

  /**
   * Obtém código de incorporação para um post específico
   */
  async getEmbedCode(
    platform: 'facebook' | 'twitter' | 'instagram' | 'linkedin' | 'youtube',
    postUrl: string,
    options?: SocialWidgetOptions
  ): Promise<string> {
    // Esta função normalmente faria uma chamada à API de incorporação da plataforma
    // Aqui estamos retornando códigos de incorporação estáticos para demonstração

    switch (platform) {
      case 'facebook':
        return `
          <div id="fb-root"></div>
          <div class="fb-post" 
            data-href="${postUrl}" 
            data-width="${options?.width || 500}" 
            data-show-text="true">
            <blockquote cite="${postUrl}" class="fb-xfbml-parse-ignore"></blockquote>
          </div>
        `;

      case 'twitter':
        return `
          <blockquote class="twitter-tweet" data-theme="${options?.theme || 'light'}">
            <a href="${postUrl}">Carregando tweet...</a>
          </blockquote>
        `;

      case 'instagram':
        return `
          <blockquote class="instagram-media" data-instgrm-permalink="${postUrl}" 
            data-instgrm-version="14" style="width: 100%; max-width: 540px;">
            <a href="${postUrl}">Carregando post do Instagram...</a>
          </blockquote>
          <script async src="//www.instagram.com/embed.js"></script>
        `;

      case 'linkedin':
        return `
          <div class="linkedin-post">
            <iframe src="https://www.linkedin.com/embed/feed/update/${postUrl.split('/').pop()}" 
              height="${options?.height || 500}" width="${options?.width || 500}" frameborder="0" allowfullscreen></iframe>
          </div>
        `;

      case 'youtube': {
        const videoId = this.extractYoutubeVideoId(postUrl);
        return `
          <div class="youtube-video">
            <iframe width="${options?.width || 560}" height="${options?.height || 315}" 
              src="https://www.youtube.com/embed/${videoId}" 
              frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" 
              allowfullscreen></iframe>
          </div>
        `;
      }

      default:
        return `<a href="${postUrl}" target="_blank" rel="noopener noreferrer">Ver post original</a>`;
    }
  }

  /**
   * Gera meta tags para compartilhamento em redes sociais
   */
  generateSocialMetaTags(options: ShareOptions): Record<string, string> {
    const metaTags: Record<string, string> = {};

    // Meta tags básicas
    if (options.title) {
      metaTags.title = options.title;
    }

    if (options.description) {
      metaTags.description = options.description;
    }

    // Open Graph meta tags
    metaTags['og:url'] = options.url;

    if (options.title) {
      metaTags['og:title'] = options.title;
    }

    if (options.description) {
      metaTags['og:description'] = options.description;
    }

    if (options.image) {
      metaTags['og:image'] = options.image;
    }

    metaTags['og:type'] = 'website';
    metaTags['og:site_name'] = this.siteName;

    // Twitter Card meta tags
    metaTags['twitter:card'] = options.image ? 'summary_large_image' : 'summary';

    if (this.twitterHandle) {
      metaTags['twitter:site'] = this.twitterHandle;
    }

    if (options.title) {
      metaTags['twitter:title'] = options.title;
    }

    if (options.description) {
      metaTags['twitter:description'] = options.description;
    }

    if (options.image) {
      metaTags['twitter:image'] = options.image;
    }

    return metaTags;
  }

  /**
   * Normaliza uma URL
   */
  private normalizeUrl(url: string): string {
    // Verificar se a URL já tem o domínio
    if (url.startsWith('http://') || url.startsWith('https://')) {
      return url;
    }

    // Remover barra no início se existir
    const path = url.startsWith('/') ? url.substring(1) : url;

    // Adicionar domínio
    return `${this.baseUrl}/${path}`;
  }

  /**
   * Obtém o nome da plataforma
   */
  private getPlatformName(platform: string): string {
    switch (platform) {
      case 'facebook':
        return 'Facebook';
      case 'twitter':
        return 'Twitter';
      case 'linkedin':
        return 'LinkedIn';
      case 'pinterest':
        return 'Pinterest';
      case 'whatsapp':
        return 'WhatsApp';
      case 'telegram':
        return 'Telegram';
      case 'email':
        return 'Email';
      default:
        return platform.charAt(0).toUpperCase() + platform.slice(1);
    }
  }

  /**
   * Obtém a classe do ícone da plataforma
   */
  private getPlatformIconClass(platform: string): string {
    switch (platform) {
      case 'facebook':
        return 'icon icon-facebook';
      case 'twitter':
        return 'icon icon-twitter';
      case 'linkedin':
        return 'icon icon-linkedin';
      case 'pinterest':
        return 'icon icon-pinterest';
      case 'whatsapp':
        return 'icon icon-whatsapp';
      case 'telegram':
        return 'icon icon-telegram';
      case 'email':
        return 'icon icon-mail';
      default:
        return `icon icon-${platform}`;
    }
  }

  /**
   * Extrai o ID de um vídeo do YouTube a partir da URL
   */
  private extractYoutubeVideoId(url: string): string {
    const regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|&v=)([^#&?]*).*/;
    const match = url.match(regExp);

    return match && match[2].length === 11 ? match[2] : '';
  }
}
