---
/**
 * Componente de Formulário de Login - Astro Nativo
 *
 * Este componente fornece um formulário de login usando apenas
 * tecnologias nativas do Astro.js, sem dependências de React.
 */

interface Props {
  /** URL de redirecionamento após login bem-sucedido */
  redirectTo?: string;
  /** Mostrar campo "Lembrar de mim" */
  showRememberMe?: boolean;
  /** Classe CSS adicional */
  class?: string;
}

const { redirectTo = '/', showRememberMe = true, class: className = '' } = Astro.props;

// Verificar se há mensagens de erro ou sucesso na URL
const url = new URL(Astro.request.url);
const error = url.searchParams.get('error');
const message = url.searchParams.get('message');
---

<div class={`login-form-container ${className}`}>
  <form 
    method="POST" 
    action="/api/auth/login"
    class="login-form"
    id="loginForm"
  >
    <input type="hidden" name="redirectTo" value={redirectTo} />
    
    {error && (
      <div class="alert alert-error">
        <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
        <span>{error}</span>
      </div>
    )}
    
    {message && (
      <div class="alert alert-info">
        <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
        <span>{message}</span>
      </div>
    )}
    
    <div class="form-group">
      <label for="email" class="form-label">
        Email
      </label>
      <input 
        type="email" 
        id="email" 
        name="email" 
        class="form-input"
        required 
        autocomplete="email"
        placeholder="<EMAIL>"
      />
    </div>
    
    <div class="form-group">
      <label for="password" class="form-label">
        Senha
      </label>
      <input 
        type="password" 
        id="password" 
        name="password" 
        class="form-input"
        required 
        autocomplete="current-password"
        placeholder="Sua senha"
      />
    </div>
    
    {showRememberMe && (
      <div class="form-group">
        <label class="form-checkbox">
          <input 
            type="checkbox" 
            name="remember" 
            value="true"
          />
          <span class="checkmark"></span>
          Lembrar de mim
        </label>
      </div>
    )}
    
    <div class="form-actions">
      <button 
        type="submit" 
        class="btn btn-primary btn-block"
        id="submitBtn"
      >
        <span class="btn-text">Entrar</span>
        <span class="btn-loading" style="display: none;">
          <svg class="animate-spin h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          Entrando...
        </span>
      </button>
    </div>
    
    <div class="form-links">
      <a href="/forgot-password" class="link">
        Esqueceu sua senha?
      </a>
      <a href="/register" class="link">
        Criar conta
      </a>
    </div>
  </form>
</div>

<script>
  // Adicionar funcionalidade de loading ao formulário
  const form = document.getElementById('loginForm');
  const submitBtn = document.getElementById('submitBtn');
  const btnText = submitBtn?.querySelector('.btn-text');
  const btnLoading = submitBtn?.querySelector('.btn-loading');
  
  if (form && submitBtn && btnText && btnLoading) {
    form.addEventListener('submit', () => {
      // Mostrar estado de loading
      submitBtn.disabled = true;
      btnText.style.display = 'none';
      btnLoading.style.display = 'flex';
    });
  }
  
  // Validação básica do formulário
  const emailInput = document.getElementById('email');
  const passwordInput = document.getElementById('password');
  
  function validateForm() {
    const email = emailInput?.value;
    const password = passwordInput?.value;
    
    if (!email || !password) {
      return false;
    }
    
    // Validação básica de email
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return false;
    }
    
    return true;
  }
  
  // Habilitar/desabilitar botão baseado na validação
  function updateSubmitButton() {
    if (submitBtn) {
      submitBtn.disabled = !validateForm();
    }
  }
  
  emailInput?.addEventListener('input', updateSubmitButton);
  passwordInput?.addEventListener('input', updateSubmitButton);
  
  // Validação inicial
  updateSubmitButton();
</script>

<style>
  .login-form-container {
    max-width: 400px;
    margin: 0 auto;
    padding: 2rem;
  }
  
  .login-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
  }
  
  .form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .form-label {
    font-weight: 600;
    color: #374151;
    font-size: 0.875rem;
  }
  
  .form-input {
    padding: 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 0.5rem;
    font-size: 1rem;
    transition: border-color 0.2s, box-shadow 0.2s;
  }
  
  .form-input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }
  
  .form-checkbox {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    font-size: 0.875rem;
  }
  
  .form-checkbox input[type="checkbox"] {
    margin: 0;
  }
  
  .form-actions {
    margin-top: 1rem;
  }
  
  .btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 0.5rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s;
    text-decoration: none;
  }
  
  .btn-primary {
    background-color: #3b82f6;
    color: white;
  }
  
  .btn-primary:hover:not(:disabled) {
    background-color: #2563eb;
  }
  
  .btn-primary:disabled {
    background-color: #9ca3af;
    cursor: not-allowed;
  }
  
  .btn-block {
    width: 100%;
  }
  
  .btn-loading {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }
  
  .form-links {
    display: flex;
    justify-content: space-between;
    margin-top: 1rem;
    font-size: 0.875rem;
  }
  
  .link {
    color: #3b82f6;
    text-decoration: none;
  }
  
  .link:hover {
    text-decoration: underline;
  }
  
  .alert {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
    border-radius: 0.5rem;
    margin-bottom: 1rem;
  }
  
  .alert-error {
    background-color: #fef2f2;
    border: 1px solid #fca5a5;
    color: #dc2626;
  }
  
  .alert-info {
    background-color: #eff6ff;
    border: 1px solid #93c5fd;
    color: #2563eb;
  }
  
  .alert svg {
    width: 1.5rem;
    height: 1.5rem;
    flex-shrink: 0;
  }
  
  .animate-spin {
    animation: spin 1s linear infinite;
  }
  
  @keyframes spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }
</style>
