---
import DaisyButton from '../../components/ui/DaisyButton.astro';
import DaisyCard from '../../components/ui/DaisyCard.astro';
import Container from '../../components/ui/layout/Container.astro';
import Divider from '../../components/ui/layout/Divider.astro';
import Grid from '../../components/ui/layout/Grid.astro';
import Section from '../../components/ui/layout/Section.astro';
import Breadcrumbs from '../../components/ui/navigation/Breadcrumbs.astro';
import Tabs from '../../components/ui/navigation/Tabs.astro';
import BaseLayout from '../../layouts/BaseLayout.astro';

// Importar tokens
import {
  animations,
  borders,
  breakpoints,
  colors,
  shadows,
  spacing,
  typography,
} from '../../tokens';

const title = 'Design Tokens';

// Breadcrumbs para a página atual
const breadcrumbItems = [
  { href: '/', label: 'In<PERSON>cio' },
  { href: '/docs', label: 'Documentação' },
  { label: 'Design Tokens' },
];

// Tabs para as categorias de tokens
const tokenTabs = [
  { id: 'colors', label: 'Cores', isActive: true },
  { id: 'typography', label: 'Tipografia' },
  { id: 'spacing', label: 'Espaçamento' },
  { id: 'borders', label: 'Bordas' },
  { id: 'shadows', label: 'Sombras' },
  { id: 'animations', label: 'Animações' },
  { id: 'breakpoints', label: 'Breakpoints' },
];

// Função para formatar objetos como JSON
function formatObject(obj) {
  return JSON.stringify(obj, null, 2);
}
---

<BaseLayout title={title}>
  <Container>
    <Breadcrumbs items={breadcrumbItems} class="my-4" />
    
    <Section title={title} subtitle="Sistema de tokens de design para consistência visual">
      <div class="mb-8">
        <p>
          Design tokens são valores reutilizáveis que representam as decisões de design do projeto, como cores, tipografia, espaçamento, etc.
          Eles são a fonte única de verdade para o design do projeto e garantem consistência visual em toda a aplicação.
        </p>
        
        <p class="mt-4">
          Este sistema de tokens foi desenvolvido para o projeto Estação da Alfabetização, com foco em flexibilidade, acessibilidade e adaptação para o público infantil.
        </p>
      </div>
      
      <Tabs tabs={tokenTabs}>
        <!-- Cores -->
        <div slot="colors" class="py-4">
          <h2 class="text-2xl font-bold mb-4">Paleta de Cores</h2>
          
          <Grid cols={{ sm: 1, md: 2, lg: 4 }} gap={6} class="mb-8">
            {Object.entries(colors.palette).map(([colorName, colorShades]) => (
              <DaisyCard title={colorName.charAt(0).toUpperCase() + colorName.slice(1)}>
                <div class="space-y-2">
                  {Object.entries(colorShades).map(([shade, value]) => (
                    <div class="flex items-center">
                      <div 
                        class="w-8 h-8 rounded mr-2" 
                        style={`background-color: ${value};`}
                      ></div>
                      <div>
                        <div class="text-sm font-medium">{colorName}-{shade}</div>
                        <div class="text-xs opacity-70">{value}</div>
                      </div>
                    </div>
                  ))}
                </div>
              </DaisyCard>
            ))}
          </Grid>
          
          <h2 class="text-2xl font-bold mb-4">Cores Semânticas</h2>
          
          <Grid cols={{ sm: 1, md: 2, lg: 3 }} gap={6} class="mb-8">
            <DaisyCard title="Cores Primárias">
              <div class="space-y-2">
                {['primary', 'secondary', 'accent', 'neutral'].map(color => (
                  <div class="flex items-center">
                    <div 
                      class="w-8 h-8 rounded mr-2" 
                      style={`background-color: ${colors.semantic[color]};`}
                    ></div>
                    <div>
                      <div class="text-sm font-medium">{color}</div>
                      <div class="text-xs opacity-70">{colors.semantic[color]}</div>
                    </div>
                  </div>
                ))}
              </div>
            </DaisyCard>
            
            <DaisyCard title="Cores de Estado">
              <div class="space-y-2">
                {['info', 'success', 'warning', 'error'].map(color => (
                  <div class="flex items-center">
                    <div 
                      class="w-8 h-8 rounded mr-2" 
                      style={`background-color: ${colors.semantic[color]};`}
                    ></div>
                    <div>
                      <div class="text-sm font-medium">{color}</div>
                      <div class="text-xs opacity-70">{colors.semantic[color]}</div>
                    </div>
                  </div>
                ))}
              </div>
            </DaisyCard>
            
            <DaisyCard title="Cores de Fundo">
              <div class="space-y-2">
                {['base-100', 'base-200', 'base-300', 'base-content'].map(color => (
                  <div class="flex items-center">
                    <div 
                      class="w-8 h-8 rounded mr-2" 
                      style={`background-color: ${colors.semantic[color]};`}
                    ></div>
                    <div>
                      <div class="text-sm font-medium">{color}</div>
                      <div class="text-xs opacity-70">{colors.semantic[color]}</div>
                    </div>
                  </div>
                ))}
              </div>
            </DaisyCard>
          </Grid>
          
          <div class="bg-base-200 p-6 rounded-box">
            <h3 class="text-xl font-bold mb-4">Exemplo de Uso</h3>
            
            <pre class="bg-base-300 p-4 rounded-box overflow-x-auto text-sm"><code>// Em JavaScript
import { colors } from '../tokens';

const primaryColor = colors.palette.blue[500];
const buttonColor = colors.semantic.primary;

// Em CSS
.button {
  background-color: var(--colors-semantic-primary);
  color: var(--colors-semantic-primary-content);
}</code></pre>
          </div>
        </div>
        
        <!-- Tipografia -->
        <div slot="typography" class="py-4">
          <h2 class="text-2xl font-bold mb-4">Famílias de Fontes</h2>
          
          <Grid cols={{ sm: 1, md: 2 }} gap={6} class="mb-8">
            {Object.entries(typography.fontFamily).map(([name, value]) => (
              <div class="card p-4">
                <h3 class="font-bold mb-2">{name}</h3>
                <p class="text-sm opacity-70 mb-2">{value}</p>
                <p style={`font-family: ${value}`} class="text-xl">
                  O rápido cachorro marrom pula sobre o cão preguiçoso.
                </p>
              </div>
            ))}
          </Grid>
          
          <h2 class="text-2xl font-bold mb-4">Tamanhos de Fonte</h2>
          
          <div class="card p-4 mb-8">
            <div class="space-y-4">
              {Object.entries(typography.fontSize).map(([name, value]) => (
                <div class="flex items-center">
                  <div class="w-16 text-sm">{name}</div>
                  <div class="w-16 text-sm opacity-70">{value}</div>
                  <div style={`font-size: ${value}`}>Texto de exemplo</div>
                </div>
              ))}
            </div>
          </div>
          
          <h2 class="text-2xl font-bold mb-4">Estilos de Texto</h2>
          
          <Grid cols={{ sm: 1, md: 2 }} gap={6} class="mb-8">
            {['h1', 'h2', 'h3', 'body', 'button', 'kids-title'].map(style => (
              <div class="card p-4">
                <h3 class="font-bold mb-2">{style}</h3>
                <div 
                  style={`
                    font-family: ${typography.textStyle[style].fontFamily};
                    font-size: ${typography.textStyle[style].fontSize};
                    font-weight: ${typography.textStyle[style].fontWeight};
                    line-height: ${typography.textStyle[style].lineHeight};
                    letter-spacing: ${typography.textStyle[style].letterSpacing};
                  `}
                >
                  Exemplo de texto com estilo {style}
                </div>
              </div>
            ))}
          </Grid>
          
          <div class="bg-base-200 p-6 rounded-box">
            <h3 class="text-xl font-bold mb-4">Exemplo de Uso</h3>
            
            <pre class="bg-base-300 p-4 rounded-box overflow-x-auto text-sm"><code>// Em JavaScript
import { typography } from '../tokens';

const headingFont = typography.fontFamily.display;
const bodyStyle = typography.textStyle.body;

// Em CSS
.heading {
  font-family: var(--typography-fontFamily-display);
  font-size: var(--typography-fontSize-4xl);
  font-weight: var(--typography-fontWeight-bold);
}</code></pre>
          </div>
        </div>
        
        <!-- Espaçamento -->
        <div slot="spacing" class="py-4">
          <h2 class="text-2xl font-bold mb-4">Escala de Espaçamento</h2>
          
          <div class="card p-4 mb-8">
            <div class="space-y-4">
              {[0, 1, 2, 4, 6, 8, 12, 16, 24].map(size => (
                <div class="flex items-center">
                  <div class="w-16 text-sm">{size}</div>
                  <div class="w-24 text-sm opacity-70">{spacing.scale[size]}</div>
                  <div class="bg-primary h-4" style={`width: ${spacing.scale[size]}`}></div>
                </div>
              ))}
            </div>
          </div>
          
          <h2 class="text-2xl font-bold mb-4">Espaçamento Semântico</h2>
          
          <Grid cols={{ sm: 1, md: 2 }} gap={6} class="mb-8">
            {Object.entries(spacing.semantic).map(([category, values]) => (
              <DaisyCard title={category}>
                <div class="space-y-2">
                  {Object.entries(values).map(([size, value]) => (
                    <div class="flex items-center">
                      <div class="w-12 text-sm">{size}</div>
                      <div class="w-16 text-sm opacity-70">{value}</div>
                      <div class="bg-primary h-4" style={`width: ${value}`}></div>
                    </div>
                  ))}
                </div>
              </DaisyCard>
            ))}
          </Grid>
          
          <div class="bg-base-200 p-6 rounded-box">
            <h3 class="text-xl font-bold mb-4">Exemplo de Uso</h3>
            
            <pre class="bg-base-300 p-4 rounded-box overflow-x-auto text-sm"><code>// Em JavaScript
import { spacing } from '../tokens';

const padding = spacing.semantic.padding.md;
const margin = spacing.semantic.margin.lg;

// Em CSS
.card {
  padding: var(--spacing-semantic-padding-md);
  margin-bottom: var(--spacing-semantic-margin-lg);
}</code></pre>
          </div>
        </div>
        
        <!-- Bordas -->
        <div slot="borders" class="py-4">
          <h2 class="text-2xl font-bold mb-4">Raios de Borda</h2>
          
          <Grid cols={{ sm: 2, md: 3, lg: 4 }} gap={6} class="mb-8">
            {Object.entries(borders.radius).map(([name, value]) => (
              <div class="flex flex-col items-center">
                <div 
                  class="w-16 h-16 bg-primary mb-2" 
                  style={`border-radius: ${value};`}
                ></div>
                <div class="text-sm font-medium">{name}</div>
                <div class="text-xs opacity-70">{value}</div>
              </div>
            ))}
          </Grid>
          
          <h2 class="text-2xl font-bold mb-4">Larguras de Borda</h2>
          
          <div class="card p-4 mb-8">
            <div class="space-y-4">
              {Object.entries(borders.width).map(([name, value]) => (
                <div class="flex items-center">
                  <div class="w-12 text-sm">{name}</div>
                  <div class="w-16 text-sm opacity-70">{value}</div>
                  <div 
                    class="w-32 h-8 bg-base-200" 
                    style={`border: ${value} solid var(--colors-semantic-primary);`}
                  ></div>
                </div>
              ))}
            </div>
          </div>
          
          <h2 class="text-2xl font-bold mb-4">Bordas Semânticas</h2>
          
          <Grid cols={{ sm: 1, md: 2 }} gap={6} class="mb-8">
            {Object.entries(borders.semantic).map(([component, values]) => (
              <DaisyCard title={component}>
                <div 
                  class="w-full h-16 bg-base-200" 
                  style={`
                    border-width: ${values.width};
                    border-style: ${values.style};
                    border-radius: ${values.radius};
                    border-color: var(--colors-semantic-primary);
                  `}
                ></div>
                <div class="mt-2 text-sm">
                  <div>Width: {values.width}</div>
                  <div>Style: {values.style}</div>
                  <div>Radius: {values.radius}</div>
                </div>
              </DaisyCard>
            ))}
          </Grid>
          
          <div class="bg-base-200 p-6 rounded-box">
            <h3 class="text-xl font-bold mb-4">Exemplo de Uso</h3>
            
            <pre class="bg-base-300 p-4 rounded-box overflow-x-auto text-sm"><code>// Em JavaScript
import { borders } from '../tokens';

const buttonBorder = borders.semantic.button;
const cardRadius = borders.radius.xl;

// Em CSS
.button {
  border-width: var(--borders-semantic-button-width);
  border-style: var(--borders-semantic-button-style);
  border-radius: var(--borders-semantic-button-radius);
}</code></pre>
          </div>
        </div>
        
        <!-- Sombras -->
        <div slot="shadows" class="py-4">
          <h2 class="text-2xl font-bold mb-4">Sombras Base</h2>
          
          <Grid cols={{ sm: 1, md: 2, lg: 3 }} gap={6} class="mb-8">
            {Object.entries(shadows.base).map(([name, value]) => (
              <div class="flex flex-col items-center">
                <div 
                  class="w-24 h-24 bg-base-100 mb-2" 
                  style={`box-shadow: ${value};`}
                ></div>
                <div class="text-sm font-medium">{name}</div>
                <div class="text-xs opacity-70 text-center">{value}</div>
              </div>
            ))}
          </Grid>
          
          <h2 class="text-2xl font-bold mb-4">Sombras Semânticas</h2>
          
          <Grid cols={{ sm: 1, md: 2 }} gap={6} class="mb-8">
            {Object.entries(shadows.semantic).filter(([_, value]) => typeof value === 'string').map(([component, value]) => (
              <div class="flex flex-col items-center">
                <div 
                  class="w-32 h-32 bg-base-100 mb-2" 
                  style={`box-shadow: ${value};`}
                ></div>
                <div class="text-sm font-medium">{component}</div>
              </div>
            ))}
          </Grid>
          
          <div class="bg-base-200 p-6 rounded-box">
            <h3 class="text-xl font-bold mb-4">Exemplo de Uso</h3>
            
            <pre class="bg-base-300 p-4 rounded-box overflow-x-auto text-sm"><code>// Em JavaScript
import { shadows } from '../tokens';

const cardShadow = shadows.semantic.card;
const hoverShadow = shadows.base.lg;

// Em CSS
.card {
  box-shadow: var(--shadows-semantic-card);
}
.card:hover {
  box-shadow: var(--shadows-base-lg);
}</code></pre>
          </div>
        </div>
        
        <!-- Animações -->
        <div slot="animations" class="py-4">
          <h2 class="text-2xl font-bold mb-4">Durações</h2>
          
          <div class="card p-4 mb-8">
            <div class="space-y-4">
              {Object.entries(animations.duration).map(([name, value]) => (
                <div class="flex items-center">
                  <div class="w-12 text-sm">{name}</div>
                  <div class="w-16 text-sm opacity-70">{value}</div>
                  <div class="w-full bg-base-300 h-2">
                    <div 
                      class="bg-primary h-2 transition-all duration-1000" 
                      style={`width: ${parseInt(name) / 20}%;`}
                    ></div>
                  </div>
                </div>
              ))}
            </div>
          </div>
          
          <h2 class="text-2xl font-bold mb-4">Funções de Temporização</h2>
          
          <Grid cols={{ sm: 1, md: 2, lg: 3 }} gap={6} class="mb-8">
            {Object.entries(animations.easing).map(([name, value]) => (
              <div class="card p-4">
                <div class="text-sm font-medium mb-2">{name}</div>
                <div class="text-xs opacity-70 mb-4">{value}</div>
                <div class="h-16 relative overflow-hidden">
                  <div 
                    class="absolute top-0 left-0 w-8 h-8 bg-primary rounded-full"
                    style={`animation: move-${name} 2s ${value} infinite alternate;`}
                  ></div>
                </div>
                <style>
                  {`@keyframes move-${name} {
                    from { transform: translateX(0); }
                    to { transform: translateX(calc(100% - 2rem)); }
                  }`}
                </style>
              </div>
            ))}
          </Grid>
          
          <h2 class="text-2xl font-bold mb-4">Animações Semânticas</h2>
          
          <Grid cols={{ sm: 1, md: 2 }} gap={6} class="mb-8">
            <DaisyCard title="Transições de Botão">
              <DaisyButton 
                variant="primary" 
                class="transition-transform hover:scale-105 active:scale-95"
                style={`
                  transition: ${animations.semantic.button.hover};
                `}
              >
                Hover Me
              </DaisyButton>
              <div class="mt-4 text-sm opacity-70">
                <div>Hover: {animations.semantic.button.hover}</div>
                <div>Active: {animations.semantic.button.active}</div>
              </div>
            </DaisyCard>
            
            <DaisyCard title="Animações de Carregamento">
              <div class="flex items-center gap-4">
                <div 
                  class="w-8 h-8 border-4 border-primary border-t-transparent rounded-full"
                  style={`animation: ${animations.semantic.loading.spinner};`}
                ></div>
                <div 
                  class="w-8 h-8 bg-primary rounded-full"
                  style={`animation: ${animations.semantic.loading.pulse}; animation-name: pulse;`}
                ></div>
              </div>
              <div class="mt-4 text-sm opacity-70">
                <div>Spinner: {animations.semantic.loading.spinner}</div>
                <div>Pulse: {animations.semantic.loading.pulse}</div>
              </div>
              <style>
                @keyframes pulse {
                  0%, 100% { transform: scale(1); opacity: 1; }
                  50% { transform: scale(0.8); opacity: 0.5; }
                }
              </style>
            </DaisyCard>
          </Grid>
          
          <div class="bg-base-200 p-6 rounded-box">
            <h3 class="text-xl font-bold mb-4">Exemplo de Uso</h3>
            
            <pre class="bg-base-300 p-4 rounded-box overflow-x-auto text-sm"><code>// Em JavaScript
import { animations } from '../tokens';

const buttonTransition = animations.semantic.button.hover;
const loadingAnimation = animations.semantic.loading.spinner;

// Em CSS
.button {
  transition: var(--animations-semantic-button-hover);
}
.spinner {
  animation: var(--animations-semantic-loading-spinner);
}</code></pre>
          </div>
        </div>
        
        <!-- Breakpoints -->
        <div slot="breakpoints" class="py-4">
          <h2 class="text-2xl font-bold mb-4">Valores de Breakpoint</h2>
          
          <div class="card p-4 mb-8">
            <div class="space-y-4">
              {Object.entries(breakpoints.values).map(([name, value]) => (
                <div class="flex items-center">
                  <div class="w-12 text-sm">{name}</div>
                  <div class="w-16 text-sm opacity-70">{value}px</div>
                  <div class="w-full bg-base-300 h-4">
                    <div 
                      class="bg-primary h-4" 
                      style={`width: ${value / 20}%;`}
                    ></div>
                  </div>
                </div>
              ))}
            </div>
          </div>
          
          <h2 class="text-2xl font-bold mb-4">Media Queries</h2>
          
          <div class="card p-4 mb-8">
            <div class="space-y-4">
              {Object.entries(breakpoints.mediaQueries).slice(0, 8).map(([name, value]) => (
                <div>
                  <div class="text-sm font-medium">{name}</div>
                  <div class="text-xs opacity-70">{value}</div>
                </div>
              ))}
            </div>
          </div>
          
          <div class="bg-base-200 p-6 rounded-box">
            <h3 class="text-xl font-bold mb-4">Exemplo de Uso</h3>
            
            <pre class="bg-base-300 p-4 rounded-box overflow-x-auto text-sm"><code>// Em JavaScript
import { breakpoints } from '../tokens';

const tabletQuery = breakpoints.mediaQueries.tablet;
const lgBreakpoint = breakpoints.values.lg;

// Em CSS
@media (min-width: var(--breakpoints-screens-md)) {
  .container {
    max-width: var(--breakpoints-container-md);
  }
}</code></pre>
          </div>
        </div>
      </Tabs>
      
      <div class="mt-12 p-6 bg-base-200 rounded-box">
        <h2 class="text-2xl font-bold mb-4">Documentação Completa</h2>
        
        <p class="mb-4">
          Para uma documentação completa de todos os tokens de design, consulte o arquivo:
        </p>
        
        <div class="bg-base-300 p-4 rounded-box">
          <code>docs/DESIGN_TOKENS.md</code>
        </div>
        
        <p class="mt-4">
          Este arquivo contém informações detalhadas sobre todos os tokens, exemplos de uso e boas práticas.
        </p>
        
        <div class="mt-6">
          <DaisyButton href="/docs/DESIGN_TOKENS.md" variant="primary">Ver Documentação Completa</DaisyButton>
        </div>
      </div>
    </Section>
  </Container>
</BaseLayout>
