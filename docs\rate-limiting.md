# Sistema de Rate Limiting

## Visão Geral

O sistema de rate limiting implementa controle de taxa de requisições para proteger a aplicação contra abusos, ataques de força bruta e sobrecarga. Utilizando o Valkey (fork do Redis) como backend, o sistema oferece um mecanismo eficiente e escalável para limitar requisições com base em diferentes critérios.

## Arquitetura

O sistema de rate limiting é composto pelos seguintes componentes:

1. **Serviço de Rate Limiting (valkeyRateLimitService)**: Implementa a lógica de controle de taxa utilizando o Valkey.
2. **Middleware de Rate Limiting (valkeyRateLimitMiddleware)**: Intercepta requisições HTTP e aplica os limites de taxa.
3. **Interface de Administração**: Permite visualizar e gerenciar bloqueios e configurações.
4. **Integração com Monitoramento**: Registra métricas e eventos para análise.

## Algoritmo de Sliding Window

O sistema utiliza o algoritmo de "sliding window" (janela deslizante) para contagem precisa de requisições. Este algoritmo oferece as seguintes vantagens:

- **Precisão**: Considera o tempo exato de cada requisição, evitando o problema de "boundary" dos algoritmos de fixed window.
- **Eficiência**: Implementado com scripts Lua no Valkey para operações atômicas e eficientes.
- **Escalabilidade**: Funciona bem em ambientes distribuídos com múltiplas instâncias.

### Funcionamento do Algoritmo

1. Cada requisição é registrada no Valkey com um timestamp.
2. Ao verificar o limite, o sistema remove timestamps mais antigos que a janela de tempo.
3. Se o número de timestamps restantes for menor que o limite, a requisição é permitida.
4. Caso contrário, a requisição é bloqueada.

## Tipos de Limites

O sistema suporta diferentes tipos de limites, cada um com configurações específicas:

| Tipo | Descrição | Limite Padrão | Janela | Bloqueio |
|------|-----------|---------------|--------|----------|
| GLOBAL | Limite global por IP | 1000 req | 1 hora | Sem bloqueio |
| LOGIN | Tentativas de login | 5 req | 5 minutos | 15 minutos |
| SIGNUP | Criação de conta | 3 req | 1 hora | 24 horas |
| CONTACT | Formulário de contato | 5 req | 1 hora | 3 horas |
| API | API pública | 100 req | 1 minuto | 5 minutos |
| UPLOAD | Upload de arquivos | 10 req | 10 minutos | 30 minutos |
| PAYMENT | Requisições de pagamento | 10 req | 10 minutos | Sem bloqueio |

## Implementação Técnica

### Serviço de Rate Limiting

O serviço `valkeyRateLimitService` implementa as seguintes funcionalidades:

- **Verificação de Limites**: Verifica se uma requisição deve ser limitada.
- **Bloqueio de IPs**: Bloqueia IPs que excedem limites por um período configurável.
- **Gerenciamento de Bloqueios**: Permite listar, verificar e remover bloqueios.
- **Métricas**: Registra métricas para análise e monitoramento.

### Scripts Lua

O sistema utiliza scripts Lua para operações atômicas no Valkey:

```lua
-- Script para sliding window rate limiting
local key = KEYS[1]
local now = tonumber(ARGV[1])
local window = tonumber(ARGV[2])
local limit = tonumber(ARGV[3])

-- Remove timestamps older than the window
redis.call('ZREMRANGEBYSCORE', key, 0, now - window)

-- Count current requests in window
local count = redis.call('ZCARD', key)

-- Check if limit exceeded
if count >= limit then
  return {1, count, limit}
end

-- Add current timestamp
redis.call('ZADD', key, now, now .. ':' .. math.random())

-- Set expiration
redis.call('EXPIRE', key, window)

-- Return result: not limited, current count, limit
return {0, count + 1, limit}
```

### Middleware

O middleware `valkeyRateLimitMiddleware` intercepta requisições HTTP e:

1. Verifica se o caminho deve ser limitado.
2. Obtém o identificador (geralmente IP) da requisição.
3. Determina o tipo de limite com base no caminho.
4. Verifica o limite usando o serviço de rate limiting.
5. Retorna erro 429 (Too Many Requests) se o limite for excedido.
6. Adiciona cabeçalhos de rate limiting à resposta.

### Cabeçalhos HTTP

O sistema adiciona os seguintes cabeçalhos às respostas HTTP:

- `X-RateLimit-Limit`: Número máximo de requisições permitidas.
- `X-RateLimit-Remaining`: Número de requisições restantes.
- `X-RateLimit-Reset`: Tempo em segundos até o reset do contador.
- `Retry-After`: Tempo em segundos até que novas requisições sejam permitidas (apenas para respostas 429).

## Interface de Administração

A interface de administração (`/admin/rate-limiting`) permite:

- Visualizar IPs bloqueados e tempo restante de bloqueio.
- Desbloquear IPs manualmente.
- Visualizar estatísticas de rate limiting.
- Consultar configurações de limites.

## Integração com Monitoramento

O sistema integra-se com o serviço de monitoramento da aplicação para registrar:

- **Contadores**: Número de requisições limitadas, bloqueadas, etc.
- **Métricas de Performance**: Tempo de verificação de limites.
- **Alertas**: Notificações para padrões suspeitos de tráfego.

## Configuração

A configuração do sistema é feita através de variáveis de ambiente:

- `ENABLE_RATE_LIMITING`: Habilita/desabilita o rate limiting (default: true).
- `RATE_LIMIT_WHITELIST`: Lista de IPs separados por vírgula que devem ser ignorados.

## Boas Práticas

1. **Ajuste os Limites**: Configure limites apropriados para cada tipo de rota com base no tráfego esperado.
2. **Monitore Bloqueios**: Verifique regularmente os IPs bloqueados para identificar padrões de abuso.
3. **Analise Métricas**: Use as métricas para ajustar os limites e identificar problemas.
4. **Whitelist IPs Confiáveis**: Adicione IPs de serviços confiáveis à whitelist.
5. **Teste sob Carga**: Verifique se o sistema funciona corretamente sob carga elevada.

## Troubleshooting

### Problemas Comuns

1. **Falsos Positivos**: Usuários legítimos sendo bloqueados.
   - Solução: Ajuste os limites ou adicione IPs à whitelist.

2. **Impacto na Performance**: Rate limiting afetando a performance da aplicação.
   - Solução: Otimize scripts Lua ou ajuste a configuração do Valkey.

3. **Bypass de Limites**: Atacantes contornando os limites.
   - Solução: Implemente identificação mais robusta (além do IP) ou adicione camadas adicionais de proteção.

### Logs

O sistema registra eventos importantes nos logs:

- Inicialização do serviço
- Bloqueios de IPs
- Erros de verificação
- Ações administrativas (desbloqueios manuais)

## Referências

- [OWASP Rate Limiting](https://cheatsheetseries.owasp.org/cheatsheets/Denial_of_Service_Cheat_Sheet.html)
- [Valkey Documentation](https://valkey.io/docs/)
- [Rate Limiting Algorithms](https://konghq.com/blog/how-to-design-a-scalable-rate-limiting-algorithm/)
