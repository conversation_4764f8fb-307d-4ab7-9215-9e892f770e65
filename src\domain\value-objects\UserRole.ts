/**
 * Pap<PERSON>is de usuário no sistema
 *
 * Este objeto de valor representa os papéis possíveis de um usuário no sistema.
 */

export enum UserRole {
  ADMIN = 'admin',
  TEACHER = 'teacher',
  STUDENT = 'student',
  PARENT = 'parent',
}

/**
 * Verifica se um valor é um papel de usuário válido
 *
 * @param value Valor a ser verificado
 * @returns Verdadeiro se o valor for um papel de usuário válido
 */
export function isValidUserRole(value: string): value is UserRole {
  return Object.values(UserRole).includes(value as UserRole);
}

/**
 * Obtém a descrição de um papel de usuário
 *
 * @param role Papel de usuário
 * @returns Descrição do papel de usuário
 */
export function getUserRoleDescription(role: UserRole): string {
  const descriptions: Record<UserRole, string> = {
    [UserRole.ADMIN]: 'Administrador',
    [UserRole.TEACHER]: 'Professor',
    [UserRole.STUDENT]: 'Estudante',
    [UserRole.PARENT]: 'Pai/Responsável',
  };

  return descriptions[role] || 'Desconhecido';
}

/**
 * Obtém as permissões associadas a um papel de usuário
 *
 * @param role Papel de usuário
 * @returns Array de permissões
 */
export function getUserRolePermissions(role: UserRole): string[] {
  const permissions: Record<UserRole, string[]> = {
    [UserRole.ADMIN]: [
      'manage:users',
      'manage:content',
      'manage:settings',
      'view:analytics',
      'manage:payments',
      'manage:roles',
    ],
    [UserRole.TEACHER]: [
      'create:content',
      'edit:own_content',
      'view:student_progress',
      'create:assignments',
      'grade:assignments',
    ],
    [UserRole.STUDENT]: [
      'view:content',
      'submit:assignments',
      'view:own_progress',
      'participate:discussions',
    ],
    [UserRole.PARENT]: ['view:child_progress', 'view:content', 'manage:child_account'],
  };

  return permissions[role] || [];
}

/**
 * Verifica se um papel de usuário tem uma permissão específica
 *
 * @param role Papel de usuário
 * @param permission Permissão a ser verificada
 * @returns Verdadeiro se o papel tiver a permissão
 */
export function hasPermission(role: UserRole, permission: string): boolean {
  const permissions = getUserRolePermissions(role);
  return permissions.includes(permission);
}

/**
 * Obtém a cor associada a um papel de usuário
 *
 * @param role Papel de usuário
 * @returns Código de cor para o papel
 */
export function getUserRoleColor(role: UserRole): string {
  const colors: Record<UserRole, string> = {
    [UserRole.ADMIN]: 'red',
    [UserRole.TEACHER]: 'blue',
    [UserRole.STUDENT]: 'green',
    [UserRole.PARENT]: 'purple',
  };

  return colors[role] || 'gray';
}
