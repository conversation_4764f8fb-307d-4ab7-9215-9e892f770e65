/**
 * Sistema de Grid
 *
 * Este arquivo define a configuração do sistema de grid para o projeto Estação da Alfabetização.
 * O sistema é baseado no TailwindCSS e CSS Grid, com suporte a diferentes breakpoints.
 */

// Importar tokens de breakpoints
import { breakpoints } from '../../tokens';

// Configuração de colunas do grid
const columns = {
  1: 'grid-cols-1',
  2: 'grid-cols-2',
  3: 'grid-cols-3',
  4: 'grid-cols-4',
  5: 'grid-cols-5',
  6: 'grid-cols-6',
  7: 'grid-cols-7',
  8: 'grid-cols-8',
  9: 'grid-cols-9',
  10: 'grid-cols-10',
  11: 'grid-cols-11',
  12: 'grid-cols-12',
};

// Configuração de colunas responsivas
const responsiveColumns = {
  sm: {
    1: 'sm:grid-cols-1',
    2: 'sm:grid-cols-2',
    3: 'sm:grid-cols-3',
    4: 'sm:grid-cols-4',
    5: 'sm:grid-cols-5',
    6: 'sm:grid-cols-6',
    7: 'sm:grid-cols-7',
    8: 'sm:grid-cols-8',
    9: 'sm:grid-cols-9',
    10: 'sm:grid-cols-10',
    11: 'sm:grid-cols-11',
    12: 'sm:grid-cols-12',
  },
  md: {
    1: 'md:grid-cols-1',
    2: 'md:grid-cols-2',
    3: 'md:grid-cols-3',
    4: 'md:grid-cols-4',
    5: 'md:grid-cols-5',
    6: 'md:grid-cols-6',
    7: 'md:grid-cols-7',
    8: 'md:grid-cols-8',
    9: 'md:grid-cols-9',
    10: 'md:grid-cols-10',
    11: 'md:grid-cols-11',
    12: 'md:grid-cols-12',
  },
  lg: {
    1: 'lg:grid-cols-1',
    2: 'lg:grid-cols-2',
    3: 'lg:grid-cols-3',
    4: 'lg:grid-cols-4',
    5: 'lg:grid-cols-5',
    6: 'lg:grid-cols-6',
    7: 'lg:grid-cols-7',
    8: 'lg:grid-cols-8',
    9: 'lg:grid-cols-9',
    10: 'lg:grid-cols-10',
    11: 'lg:grid-cols-11',
    12: 'lg:grid-cols-12',
  },
  xl: {
    1: 'xl:grid-cols-1',
    2: 'xl:grid-cols-2',
    3: 'xl:grid-cols-3',
    4: 'xl:grid-cols-4',
    5: 'xl:grid-cols-5',
    6: 'xl:grid-cols-6',
    7: 'xl:grid-cols-7',
    8: 'xl:grid-cols-8',
    9: 'xl:grid-cols-9',
    10: 'xl:grid-cols-10',
    11: 'xl:grid-cols-11',
    12: 'xl:grid-cols-12',
  },
  '2xl': {
    1: '2xl:grid-cols-1',
    2: '2xl:grid-cols-2',
    3: '2xl:grid-cols-3',
    4: '2xl:grid-cols-4',
    5: '2xl:grid-cols-5',
    6: '2xl:grid-cols-6',
    7: '2xl:grid-cols-7',
    8: '2xl:grid-cols-8',
    9: '2xl:grid-cols-9',
    10: '2xl:grid-cols-10',
    11: '2xl:grid-cols-11',
    12: '2xl:grid-cols-12',
  },
};

// Configuração de gaps
const gaps = {
  0: 'gap-0',
  1: 'gap-1',
  2: 'gap-2',
  3: 'gap-3',
  4: 'gap-4',
  5: 'gap-5',
  6: 'gap-6',
  8: 'gap-8',
  10: 'gap-10',
  12: 'gap-12',
  16: 'gap-16',
  20: 'gap-20',
  24: 'gap-24',
  32: 'gap-32',
};

// Configuração de gaps responsivos
const responsiveGaps = {
  sm: {
    0: 'sm:gap-0',
    1: 'sm:gap-1',
    2: 'sm:gap-2',
    3: 'sm:gap-3',
    4: 'sm:gap-4',
    5: 'sm:gap-5',
    6: 'sm:gap-6',
    8: 'sm:gap-8',
    10: 'sm:gap-10',
    12: 'sm:gap-12',
    16: 'sm:gap-16',
    20: 'sm:gap-20',
    24: 'sm:gap-24',
    32: 'sm:gap-32',
  },
  md: {
    0: 'md:gap-0',
    1: 'md:gap-1',
    2: 'md:gap-2',
    3: 'md:gap-3',
    4: 'md:gap-4',
    5: 'md:gap-5',
    6: 'md:gap-6',
    8: 'md:gap-8',
    10: 'md:gap-10',
    12: 'md:gap-12',
    16: 'md:gap-16',
    20: 'md:gap-20',
    24: 'md:gap-24',
    32: 'md:gap-32',
  },
  lg: {
    0: 'lg:gap-0',
    1: 'lg:gap-1',
    2: 'lg:gap-2',
    3: 'lg:gap-3',
    4: 'lg:gap-4',
    5: 'lg:gap-5',
    6: 'lg:gap-6',
    8: 'lg:gap-8',
    10: 'lg:gap-10',
    12: 'lg:gap-12',
    16: 'lg:gap-16',
    20: 'lg:gap-20',
    24: 'lg:gap-24',
    32: 'lg:gap-32',
  },
  xl: {
    0: 'xl:gap-0',
    1: 'xl:gap-1',
    2: 'xl:gap-2',
    3: 'xl:gap-3',
    4: 'xl:gap-4',
    5: 'xl:gap-5',
    6: 'xl:gap-6',
    8: 'xl:gap-8',
    10: 'xl:gap-10',
    12: 'xl:gap-12',
    16: 'xl:gap-16',
    20: 'xl:gap-20',
    24: 'xl:gap-24',
    32: 'xl:gap-32',
  },
  '2xl': {
    0: '2xl:gap-0',
    1: '2xl:gap-1',
    2: '2xl:gap-2',
    3: '2xl:gap-3',
    4: '2xl:gap-4',
    5: '2xl:gap-5',
    6: '2xl:gap-6',
    8: '2xl:gap-8',
    10: '2xl:gap-10',
    12: '2xl:gap-12',
    16: '2xl:gap-16',
    20: '2xl:gap-20',
    24: '2xl:gap-24',
    32: '2xl:gap-32',
  },
};

// Configuração de alinhamento
const alignment = {
  start: 'justify-start',
  center: 'justify-center',
  end: 'justify-end',
  between: 'justify-between',
  around: 'justify-around',
  evenly: 'justify-evenly',
};

// Configuração de alinhamento vertical
const verticalAlignment = {
  start: 'items-start',
  center: 'items-center',
  end: 'items-end',
  stretch: 'items-stretch',
  baseline: 'items-baseline',
};

// Configuração de containers
const containers = {
  none: '',
  sm: 'max-w-screen-sm mx-auto',
  md: 'max-w-screen-md mx-auto',
  lg: 'max-w-screen-lg mx-auto',
  xl: 'max-w-screen-xl mx-auto',
  '2xl': 'max-w-screen-2xl mx-auto',
  full: 'max-w-full',
};

// Configuração de padding
const padding = {
  0: 'p-0',
  1: 'p-1',
  2: 'p-2',
  3: 'p-3',
  4: 'p-4',
  5: 'p-5',
  6: 'p-6',
  8: 'p-8',
  10: 'p-10',
  12: 'p-12',
  16: 'p-16',
  20: 'p-20',
  24: 'p-24',
  32: 'p-32',
};

// Configuração de padding horizontal
const paddingX = {
  0: 'px-0',
  1: 'px-1',
  2: 'px-2',
  3: 'px-3',
  4: 'px-4',
  5: 'px-5',
  6: 'px-6',
  8: 'px-8',
  10: 'px-10',
  12: 'px-12',
  16: 'px-16',
  20: 'px-20',
  24: 'px-24',
  32: 'px-32',
};

// Configuração de padding vertical
const paddingY = {
  0: 'py-0',
  1: 'py-1',
  2: 'py-2',
  3: 'py-3',
  4: 'py-4',
  5: 'py-5',
  6: 'py-6',
  8: 'py-8',
  10: 'py-10',
  12: 'py-12',
  16: 'py-16',
  20: 'py-20',
  24: 'py-24',
  32: 'py-32',
};

// Exportar configuração do sistema de grid
const GridSystem = {
  columns,
  responsiveColumns,
  gaps,
  responsiveGaps,
  alignment,
  verticalAlignment,
  containers,
  padding,
  paddingX,
  paddingY,
  breakpoints: breakpoints.values,
};

export default GridSystem;
