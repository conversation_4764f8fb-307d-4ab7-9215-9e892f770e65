/**
 * Bcrypt Password Service
 *
 * Implementação do serviço de senhas usando bcrypt.
 * Parte da implementação da tarefa 8.8.2 - Gest<PERSON> de usuários
 */

import bcrypt from 'bcrypt';
import { PasswordService } from '../../domain/services/PasswordService';

export class BcryptPasswordService implements PasswordService {
  private readonly saltRounds: number;

  constructor(saltRounds = 10) {
    this.saltRounds = saltRounds;
  }

  /**
   * Gera um hash para uma senha
   */
  async hashPassword(password: string): Promise<string> {
    return bcrypt.hash(password, this.saltRounds);
  }

  /**
   * Compara uma senha com um hash
   */
  async comparePassword(password: string, hash: string): Promise<boolean> {
    return bcrypt.compare(password, hash);
  }

  /**
   * Gera uma senha aleatória
   */
  generateRandomPassword(length = 12): string {
    const uppercaseChars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    const lowercaseChars = 'abcdefghijklmnopqrstuvwxyz';
    const numberChars = '0123456789';
    const specialChars = '!@#$%^&*()_+~`|}{[]:;?><,./-=';

    const allChars = uppercaseChars + lowercaseChars + numberChars + specialChars;

    // Garantir que a senha tenha pelo menos um caractere de cada tipo
    let password = '';
    password += uppercaseChars.charAt(Math.floor(Math.random() * uppercaseChars.length));
    password += lowercaseChars.charAt(Math.floor(Math.random() * lowercaseChars.length));
    password += numberChars.charAt(Math.floor(Math.random() * numberChars.length));
    password += specialChars.charAt(Math.floor(Math.random() * specialChars.length));

    // Preencher o restante da senha com caracteres aleatórios
    for (let i = password.length; i < length; i++) {
      password += allChars.charAt(Math.floor(Math.random() * allChars.length));
    }

    // Embaralhar a senha
    return password
      .split('')
      .sort(() => 0.5 - Math.random())
      .join('');
  }

  /**
   * Verifica a força de uma senha
   */
  checkPasswordStrength(password: string): {
    score: number;
    isStrong: boolean;
    hasMinLength: boolean;
    hasUpperCase: boolean;
    hasLowerCase: boolean;
    hasNumbers: boolean;
    hasSpecialChars: boolean;
    feedback: string[];
  } {
    const minLength = 8;
    const hasMinLength = password.length >= minLength;
    const hasUpperCase = /[A-Z]/.test(password);
    const hasLowerCase = /[a-z]/.test(password);
    const hasNumbers = /[0-9]/.test(password);
    const hasSpecialChars = /[^A-Za-z0-9]/.test(password);

    // Calcular pontuação
    let score = 0;

    if (hasMinLength) score += 1;
    if (hasUpperCase) score += 1;
    if (hasLowerCase) score += 1;
    if (hasNumbers) score += 1;
    if (hasSpecialChars) score += 1;

    // Adicionar pontos extras para senhas mais longas
    if (password.length >= 12) score += 1;
    if (password.length >= 16) score += 1;

    // Verificar se a senha é forte
    const isStrong = score >= 4;

    // Gerar feedback
    const feedback: string[] = [];

    if (!hasMinLength) {
      feedback.push(`A senha deve ter pelo menos ${minLength} caracteres.`);
    }

    if (!hasUpperCase) {
      feedback.push('A senha deve conter pelo menos uma letra maiúscula.');
    }

    if (!hasLowerCase) {
      feedback.push('A senha deve conter pelo menos uma letra minúscula.');
    }

    if (!hasNumbers) {
      feedback.push('A senha deve conter pelo menos um número.');
    }

    if (!hasSpecialChars) {
      feedback.push('A senha deve conter pelo menos um caractere especial.');
    }

    return {
      score,
      isStrong,
      hasMinLength,
      hasUpperCase,
      hasLowerCase,
      hasNumbers,
      hasSpecialChars,
      feedback,
    };
  }
}
