/**
 * Serviço para gerenciamento de métricas de performance
 *
 * Este serviço é responsável por salvar, recuperar e analisar
 * métricas de performance coletadas dos usuários.
 */

import { ValkeyCacheService } from '../infrastructure/cache/ValkeyCacheService';
import { sendEmail } from './email-service';

/**
 * Interface para métricas de performance
 */
export interface PerformanceMetric {
  name: string;
  value: number;
  id: string;
  page?: string;
  userAgent?: string;
  timestamp?: number;
}

/**
 * Interface para estatísticas de métricas
 */
export interface MetricStats {
  name: string;
  avg: number;
  median: number;
  p75: number;
  p95: number;
  min: number;
  max: number;
  count: number;
}

/**
 * Limites para alertas de performance
 */
const PERFORMANCE_THRESHOLDS: Record<string, number> = {
  LCP: 2500, // 2.5 segundos
  FID: 100, // 100ms
  CLS: 0.1, // 0.1
  FCP: 1800, // 1.8 segundos
  TTFB: 600, // 600ms
  APP_STARTUP: 3000, // 3 segundos
};

/**
 * Serviço de cache para armazenamento de métricas
 */
const cacheService = new ValkeyCacheService(process.env.REDIS_URL || 'redis://localhost:6379');

/**
 * Salva uma métrica de performance
 *
 * @param metric Métrica a ser salva
 */
export async function saveMetric(metric: PerformanceMetric): Promise<void> {
  try {
    // Validar métrica
    if (!metric.name || metric.value === undefined || !metric.id) {
      console.error('Métrica inválida:', metric);
      return;
    }

    // Adicionar timestamp se não existir
    if (!metric.timestamp) {
      metric.timestamp = Date.now();
    }

    // Chave para a lista de métricas
    const metricsKey = `metrics:${metric.name}`;

    // Obter lista atual de métricas
    const currentMetrics = (await cacheService.get<PerformanceMetric[]>(metricsKey)) || [];

    // Adicionar nova métrica
    currentMetrics.push(metric);

    // Limitar tamanho da lista (manter últimas 1000 métricas)
    const limitedMetrics = currentMetrics.slice(-1000);

    // Salvar lista atualizada
    await cacheService.set(metricsKey, limitedMetrics, 60 * 60 * 24 * 30); // 30 dias

    // Verificar se métrica excede limite
    await checkMetricAlert(metric);
  } catch (error) {
    console.error('Erro ao salvar métrica:', error);
  }
}

/**
 * Recupera métricas de performance
 *
 * @param days Número de dias para recuperar (padrão: 7)
 * @param metricName Nome da métrica (opcional)
 * @returns Lista de métricas
 */
export async function getPerformanceMetrics(
  days = 7,
  metricName?: string
): Promise<PerformanceMetric[]> {
  try {
    // Calcular timestamp mínimo
    const minTimestamp = Date.now() - days * 24 * 60 * 60 * 1000;

    // Se um nome específico de métrica for fornecido
    if (metricName) {
      const metricsKey = `metrics:${metricName}`;
      const metrics = (await cacheService.get<PerformanceMetric[]>(metricsKey)) || [];

      // Filtrar por timestamp
      return metrics.filter((m) => (m.timestamp || 0) >= minTimestamp);
    }

    // Caso contrário, recuperar todas as métricas
    const allMetrics: PerformanceMetric[] = [];

    // Obter todas as chaves de métricas
    const metricKeys = await cacheService.keys('metrics:*');

    // Recuperar métricas de cada chave
    for (const key of metricKeys) {
      const metrics = (await cacheService.get<PerformanceMetric[]>(key)) || [];

      // Filtrar por timestamp e adicionar à lista
      allMetrics.push(...metrics.filter((m) => (m.timestamp || 0) >= minTimestamp));
    }

    return allMetrics;
  } catch (error) {
    console.error('Erro ao recuperar métricas:', error);
    return [];
  }
}

/**
 * Calcula estatísticas para uma métrica específica
 *
 * @param metricName Nome da métrica
 * @param days Número de dias para calcular (padrão: 7)
 * @returns Estatísticas da métrica
 */
export async function getMetricStats(metricName: string, days = 7): Promise<MetricStats | null> {
  try {
    // Recuperar métricas
    const metrics = await getPerformanceMetrics(days, metricName);

    // Se não houver métricas, retornar null
    if (metrics.length === 0) {
      return null;
    }

    // Extrair valores
    const values = metrics.map((m) => m.value).sort((a, b) => a - b);
    const count = values.length;

    // Calcular estatísticas
    const min = values[0];
    const max = values[count - 1];
    const avg = values.reduce((sum, val) => sum + val, 0) / count;
    const median =
      count % 2 === 0
        ? (values[count / 2 - 1] + values[count / 2]) / 2
        : values[Math.floor(count / 2)];
    const p75 = values[Math.floor(count * 0.75)];
    const p95 = values[Math.floor(count * 0.95)];

    return {
      name: metricName,
      avg,
      median,
      p75,
      p95,
      min,
      max,
      count,
    };
  } catch (error) {
    console.error(`Erro ao calcular estatísticas para ${metricName}:`, error);
    return null;
  }
}

/**
 * Verifica se uma métrica excede o limite e envia alerta
 *
 * @param metric Métrica a ser verificada
 */
async function checkMetricAlert(metric: PerformanceMetric): Promise<void> {
  try {
    // Verificar se há um limite definido para esta métrica
    const threshold = PERFORMANCE_THRESHOLDS[metric.name];

    // Se não houver limite ou valor não exceder limite, ignorar
    if (!threshold || metric.value <= threshold) {
      return;
    }

    // Calcular percentual acima do limite
    const percentage = Math.round((metric.value / threshold - 1) * 100);

    // Enviar alerta se exceder em mais de 20%
    if (percentage > 20) {
      await sendPerformanceAlert(metric, percentage, threshold);
    }
  } catch (error) {
    console.error('Erro ao verificar alerta de métrica:', error);
  }
}

/**
 * Envia alerta de performance
 *
 * @param metric Métrica que excedeu o limite
 * @param percentage Percentual acima do limite
 * @param threshold Valor limite
 */
async function sendPerformanceAlert(
  metric: PerformanceMetric,
  percentage: number,
  threshold: number
): Promise<void> {
  try {
    // Verificar se estamos em ambiente de produção
    const isProduction = process.env.NODE_ENV === 'production';

    // Em desenvolvimento, apenas logar no console
    if (!isProduction) {
      console.warn(`[ALERTA] Métrica ${metric.name} excedeu limite em ${percentage}%`);
      console.warn(`Valor: ${metric.value}, Limite: ${threshold}`);
      console.warn(
        `Página: ${metric.page}, Timestamp: ${new Date(metric.timestamp || 0).toLocaleString()}`
      );
      return;
    }

    // Chave para controle de alertas (evitar spam)
    const alertKey = `alert:${metric.name}:${metric.page || 'global'}`;

    // Verificar se já enviamos um alerta recentemente
    const lastAlert = await cacheService.get<number>(alertKey);

    // Se já enviamos um alerta nas últimas 6 horas, ignorar
    if (lastAlert && Date.now() - lastAlert < 6 * 60 * 60 * 1000) {
      return;
    }

    // Preparar mensagem de alerta
    const subject = `[ALERTA] Métrica de performance ${metric.name} excedeu limite`;
    const message = `
      A métrica ${metric.name} excedeu o limite em ${percentage}%.
      
      Valor: ${metric.value}
      Limite: ${threshold}
      Página: ${metric.page || 'N/A'}
      User Agent: ${metric.userAgent || 'N/A'}
      Data: ${new Date(metric.timestamp || Date.now()).toLocaleString()}
      
      Por favor, verifique a performance do site.
    `;

    // Enviar email para equipe
    await sendEmail(
      process.env.ALERT_EMAIL || '<EMAIL>',
      subject,
      message
    );

    // Registrar envio de alerta
    await cacheService.set(alertKey, Date.now(), 60 * 60 * 6); // 6 horas

    console.warn(`Alerta de performance enviado: ${metric.name} excedeu limite em ${percentage}%`);
  } catch (error) {
    console.error('Erro ao enviar alerta de performance:', error);
  }
}
