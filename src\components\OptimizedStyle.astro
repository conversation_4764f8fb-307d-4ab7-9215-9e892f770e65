---
/**
 * Componente OptimizedStyle
 *
 * Este componente implementa carregamento otimizado de estilos CSS com:
 * - Carregamento assíncrono
 * - Preload para estilos críticos
 * - Inline para estilos críticos
 * - Carregamento diferido para estilos não críticos
 */

interface Props {
  /**
   * Caminho do arquivo CSS
   */
  href?: string;

  /**
   * Conteúdo inline do CSS
   */
  content?: string;

  /**
   * Se o estilo é crítico para a renderização inicial
   * @default false
   */
  critical?: boolean;

  /**
   * Media query para o estilo
   * @default "all"
   */
  media?: string;

  /**
   * ID do elemento
   */
  id?: string;

  /**
   * Estratégia de carregamento
   * @default "default"
   */
  strategy?: 'default' | 'async' | 'defer' | 'lazy';

  /**
   * Atributos adicionais
   */
  attributes?: Record<string, string>;
}

// Props com valores padrão
const {
  href,
  content,
  critical = false,
  media = 'all',
  id,
  strategy = 'default',
  attributes = {},
} = Astro.props;

// Determinar se o estilo é inline ou externo
const isInline = !!content;

// Determinar se deve fazer preload
const shouldPreload = critical && !isInline && strategy !== 'lazy';

// Gerar ID único para o estilo
const styleId = id || `style-${Math.random().toString(36).substring(2, 11)}`;

// Determinar atributos do estilo
const styleAttributes = {
  ...attributes,
  media,
  ...(id ? { id } : {}),
};

// Converter atributos para string
const attributesString = Object.entries(styleAttributes)
  .map(([key, value]) => (value === '' ? key : `${key}="${value}"`))
  .join(' ');

// Função para gerar o código de carregamento assíncrono
function generateAsyncLoadCode(href: string, id: string, media: string, attributesString: string) {
  return `
    const link = document.createElement('link');
    link.rel = 'stylesheet';
    link.href = "${href}";
    link.id = "${id}";
    link.media = "${media}";
    ${
      attributesString
        ? `
      ${Object.entries(styleAttributes)
        .map(([key, value]) =>
          value === ''
            ? `link.setAttribute("${key}", "");`
            : `link.setAttribute("${key}", "${value}");`
        )
        .join('\n')}
    `
        : ''
    }
    
    // Adicionar um listener para saber quando o CSS foi carregado
    link.addEventListener('load', function() {
      link.media = 'all';
    });
    
    // Fallback para navegadores que não suportam o evento load em links
    setTimeout(function() {
      link.media = 'all';
    }, 3000);
    
    document.head.appendChild(link);
  `;
}
---

{/* Preload para estilos críticos */}
{shouldPreload && (
  <link 
    rel="preload" 
    href={href} 
    as="style"
    {...styleAttributes}
  />
)}

{/* Estilo inline */}
{isInline && (
  <style 
    id={styleId}
    media={media}
    {...attributes}
    set:html={content}
  />
)}

{/* Estilo externo com carregamento padrão */}
{!isInline && strategy === 'default' && (
  <link 
    rel="stylesheet" 
    href={href}
    id={styleId}
    {...styleAttributes}
  />
)}

{/* Estilo externo com carregamento assíncrono */}
{!isInline && strategy === 'async' && (
  <>
    <link 
      rel="stylesheet" 
      href={href}
      id={styleId}
      media="print"
      onload="this.media='all'"
      {...attributes}
    />
    <noscript>
      <link 
        rel="stylesheet" 
        href={href}
        {...styleAttributes}
      />
    </noscript>
  </>
)}

{/* Estilo externo com carregamento diferido */}
{!isInline && strategy === 'defer' && (
  <script set:html={`
    document.addEventListener('DOMContentLoaded', function() {
      ${generateAsyncLoadCode(href!, styleId, media, attributesString)}
    });
  `} />
)}

{/* Estilo externo com carregamento lazy */}
{!isInline && strategy === 'lazy' && (
  <script set:html={`
    if ('IntersectionObserver' in window) {
      const loadStyle = () => {
        ${generateAsyncLoadCode(href!, styleId, media, attributesString)}
      };

      const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            loadStyle();
            observer.disconnect();
          }
        });
      }, { rootMargin: '200px' });

      // Observar o final da página
      const footer = document.querySelector('footer') || document.body;
      observer.observe(footer);

      // Carregar de qualquer forma após 3 segundos
      setTimeout(() => {
        if (!document.getElementById("${styleId}")) {
          loadStyle();
          observer.disconnect();
        }
      }, 3000);
    } else {
      // Fallback para navegadores sem suporte a Intersection Observer
      window.addEventListener('load', () => {
        setTimeout(() => {
          ${generateAsyncLoadCode(href!, styleId, media, attributesString)}
        }, 200);
      });
    }
  `} />
)}
