---
import { actions } from 'astro:actions';
import ControlButtons from '@components/form/ControlButtons.astro';
import FormBase from '@components/form/FormBase.astro';
import InputHidden from '@components/form/InputHidden.astro';
import InputText from '@components/form/InputText.astro';
import BaseLayout from '@layouts/BaseLayout.astro';
import type { UserTypeData } from 'src/database/interfacesHelper';

const formValidation = `
  const typeInput = form.querySelector('input[name="type"]');
  if (!typeInput || !typeInput.value.trim()) {
      alert('O tipo de usuário é obrigatório');
      return false;
  }
  return true;
`;
---
<BaseLayout title="Formulário de Tipo de Usuário">
  <div class="container mx-auto p-4">Novo Tipo de Usuário</div>

  <FormBase
    action={actions.userTypeAction.create}
    formType="userType"
    onSubmitValidation={formValidation}
  >
    <InputHidden field="ulid_user_type" id="ulid_user_type" />
    <InputText 
      label="Tipo" 
      name="type" 
      value="" 
      required={true}
    />
    <ControlButtons 
      saveLabel="Salvar"
      cancelHref="/admin/register/user/type"
    />
  </FormBase>
</BaseLayout>