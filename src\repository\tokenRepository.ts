/**
 * Repositório para gerenciamento de tokens JWT no banco de dados
 * Implementa funções para armazenar, verificar e invalidar tokens
 *
 * Este repositório gerencia tokens JWT e fornece funcionalidades para:
 * - Armazenar tokens emitidos
 * - Consultar tokens por ID, usuário ou JTI
 * - Revogar tokens (blacklisting)
 * - Limpar tokens expirados
 * - Rastrear uso de tokens para detecção de anomalias
 */

import type { QueryResult } from 'pg';
import { pgHelper } from './pgHelper';

// Interface para dados de token
export interface TokenData {
  ulid_token: string;
  ulid_user: string;
  token: string;
  jti: string; // JWT ID para identificação única
  type: 'access' | 'refresh' | 'reset' | 'verification' | 'api';
  is_revoked: boolean;
  revoked_reason?: string;
  revoked_at?: Date;
  device_id?: string;
  device_name?: string;
  device_type?: string;
  ip_address?: string;
  user_agent?: string;
  last_used_at?: Date;
  expires_at: Date;
  created_at: Date;
  updated_at: Date;
  revoked_at?: Date;
}

// Interface para informações do dispositivo
export interface DeviceInfo {
  deviceId?: string;
  deviceName?: string;
  deviceType?: string;
  ipAddress?: string;
  userAgent?: string;
}

// Repositório de tokens
export const tokenRepository = {
  /**
   * Cria uma nova tabela de tokens se não existir
   * Esta função deve ser chamada durante a inicialização do aplicativo
   */
  async createTokenTable(): Promise<void> {
    await pgHelper.query(`
      CREATE TABLE IF NOT EXISTS tab_token (
        ulid_token UUID PRIMARY KEY,
        ulid_user UUID NOT NULL,
        token TEXT NOT NULL,
        type VARCHAR(10) NOT NULL,
        is_revoked BOOLEAN DEFAULT FALSE,
        expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT fk_user FOREIGN KEY (ulid_user) REFERENCES tab_user(ulid_user)
      )
    `);
  },

  /**
   * Salva um token no banco de dados
   * @param ulid_user - ID do usuário
   * @param token - Token JWT
   * @param type - Tipo de token
   * @param jti - JWT ID único
   * @param expiresAt - Data de expiração
   * @param deviceInfo - Informações do dispositivo (opcional)
   * @returns Resultado da query
   */
  async saveToken(
    ulid_user: string,
    token: string,
    type: TokenData['type'],
    jti: string,
    expiresAt: Date,
    deviceInfo?: DeviceInfo
  ): Promise<QueryResult> {
    const params = [
      pgHelper.generateULID(),
      ulid_user,
      token,
      jti,
      type,
      false, // is_revoked
      deviceInfo?.deviceId || null,
      deviceInfo?.deviceName || null,
      deviceInfo?.deviceType || null,
      deviceInfo?.ipAddress || null,
      deviceInfo?.userAgent || null,
      deviceInfo ? new Date() : null, // last_used_at
      expiresAt,
    ];

    return await pgHelper.query(
      `INSERT INTO tab_token (
        ulid_token,
        ulid_user,
        token,
        jti,
        type,
        is_revoked,
        device_id,
        device_name,
        device_type,
        ip_address,
        user_agent,
        last_used_at,
        expires_at,
        created_at,
        updated_at
      ) VALUES (
        $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
      ) RETURNING *`,
      params
    );
  },

  /**
   * Busca um token pelo valor
   * @param token - Token JWT
   * @returns Resultado da query
   */
  async findToken(token: string): Promise<QueryResult> {
    return await pgHelper.query('SELECT * FROM tab_token WHERE token = $1', [token]);
  },

  /**
   * Busca um token pelo seu JTI (JWT ID)
   * @param jti - ID único do token JWT
   * @returns Resultado da query
   */
  async findTokenByJti(jti: string): Promise<QueryResult> {
    return await pgHelper.query('SELECT * FROM tab_token WHERE jti = $1', [jti]);
  },

  /**
   * Atualiza a data de último uso de um token
   * @param token - Token JWT
   * @returns Resultado da query
   */
  async updateLastUsed(token: string): Promise<QueryResult> {
    return await pgHelper.query(
      'UPDATE tab_token SET last_used_at = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP WHERE token = $1 RETURNING *',
      [token]
    );
  },

  /**
   * Busca todos os tokens ativos de um usuário
   * @param ulid_user - ID do usuário
   * @param type - Tipo de token (opcional)
   * @returns Resultado da query
   */
  async findActiveTokensByUser(ulid_user: string, type?: TokenData['type']): Promise<QueryResult> {
    let query = 'SELECT * FROM tab_token WHERE ulid_user = $1 AND is_revoked = false';
    const params = [ulid_user];

    if (type) {
      query += ' AND type = $2';
      params.push(type);
    }

    query += ' ORDER BY created_at DESC';

    return await pgHelper.query(query, params);
  },

  /**
   * Revoga um token específico
   * @param token - Token JWT ou JTI
   * @param reason - Motivo da revogação (opcional)
   * @param byJti - Se true, busca pelo JTI em vez do token completo
   * @returns Resultado da query
   */
  async revokeToken(tokenOrJti: string, reason?: string, byJti = false): Promise<QueryResult> {
    const field = byJti ? 'jti' : 'token';
    const query = `
      UPDATE tab_token
      SET
        is_revoked = true,
        revoked_at = CURRENT_TIMESTAMP,
        revoked_reason = $2,
        updated_at = CURRENT_TIMESTAMP
      WHERE ${field} = $1
      RETURNING *
    `;

    return await pgHelper.query(query, [tokenOrJti, reason || 'Revogado manualmente']);
  },

  /**
   * Revoga todos os tokens de um usuário
   * @param ulid_user - ID do usuário
   * @param exceptToken - Token a ser mantido (opcional)
   * @param tokenTypes - Tipos de token a serem revogados (opcional)
   * @returns Resultado da query
   */
  async revokeAllUserTokens(
    ulid_user: string,
    exceptToken?: string,
    tokenTypes?: string[]
  ): Promise<QueryResult> {
    let query =
      'UPDATE tab_token SET is_revoked = true, revoked_at = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP WHERE ulid_user = $1';
    const params = [ulid_user];

    // Adicionar exceção para token específico
    if (exceptToken) {
      query += ` AND token != $${params.length + 1}`;
      params.push(exceptToken);
    }

    // Adicionar filtro por tipos de token
    if (tokenTypes && tokenTypes.length > 0) {
      query += ` AND type IN (${tokenTypes.map((_, i) => `$${params.length + i + 1}`).join(', ')})`;
      params.push(...tokenTypes);
    }

    query += ' RETURNING *';

    return await pgHelper.query(query, params);
  },

  /**
   * Remove tokens expirados do banco de dados
   * @returns Resultado da query
   */
  async cleanupExpiredTokens(): Promise<QueryResult> {
    return await pgHelper.query(
      'DELETE FROM tab_token WHERE expires_at < CURRENT_TIMESTAMP RETURNING *',
      []
    );
  },

  /**
   * Obtém todos os dispositivos ativos de um usuário
   * @param ulid_user - ID do usuário
   * @returns Resultado da query com dispositivos ativos
   */
  async getUserDevices(ulid_user: string): Promise<QueryResult> {
    return await pgHelper.query(
      `SELECT
        device_id,
        device_name,
        device_type,
        ip_address,
        MAX(last_used_at) as last_used_at,
        COUNT(*) as active_tokens,
        MIN(created_at) as first_seen
      FROM tab_token
      WHERE ulid_user = $1
        AND is_revoked = false
        AND device_id IS NOT NULL
      GROUP BY device_id, device_name, device_type, ip_address
      ORDER BY MAX(last_used_at) DESC`,
      [ulid_user]
    );
  },

  /**
   * Revoga todos os tokens de um dispositivo específico
   * @param ulid_user - ID do usuário
   * @param deviceId - ID do dispositivo
   * @returns Resultado da query
   */
  async revokeDeviceTokens(ulid_user: string, deviceId: string): Promise<QueryResult> {
    return await pgHelper.query(
      'UPDATE tab_token SET is_revoked = true, revoked_at = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP WHERE ulid_user = $1 AND device_id = $2 RETURNING *',
      [ulid_user, deviceId]
    );
  },

  /**
   * Revoga todos os tokens de todos os dispositivos exceto o atual
   * @param ulid_user - ID do usuário
   * @param currentDeviceId - ID do dispositivo atual
   * @returns Resultado da query
   */
  async revokeAllDevicesExceptCurrent(
    ulid_user: string,
    currentDeviceId: string
  ): Promise<QueryResult> {
    return await pgHelper.query(
      'UPDATE tab_token SET is_revoked = true, revoked_at = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP WHERE ulid_user = $1 AND device_id != $2 RETURNING *',
      [ulid_user, currentDeviceId]
    );
  },
};
