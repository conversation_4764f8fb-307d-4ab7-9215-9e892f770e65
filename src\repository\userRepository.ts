import type { QueryResult } from 'pg';
import { pg<PERSON><PERSON>per } from './pgHelper';

async function create(
  ulid_user_type: string,
  ulid_school_type: string,
  email: string,
  password: string,
  is_teacher: boolean,
  name: string,
  state: string,
  county: string
): Promise<QueryResult> {
  return await pgHelper.query(
    `INSERT INTO tab_user (
      ulid_user_type,
      ulid_school_type,
      email,
      password,
      is_teacher,
      name,
      state,
      county,
      active,
      created_at,
      updated_at
    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
    RETURNING *`,
    [ulid_user_type, ulid_school_type, email, password, is_teacher, name, state, county]
  );
}

async function read(
  ulid_user?: string,
  ulid_user_type?: string,
  ulid_school_type?: string,
  email?: string,
  is_teacher?: boolean,
  state?: string,
  county?: string,
  active?: boolean
): Promise<QueryResult> {
  const conditions = [];
  const values = [];
  let paramCount = 1;

  if (ulid_user !== undefined) {
    conditions.push(`ulid_user = $${paramCount++}`);
    values.push(ulid_user);
  }
  if (ulid_user_type !== undefined) {
    conditions.push(`ulid_user_type = $${paramCount++}`);
    values.push(ulid_user_type);
  }
  if (ulid_school_type !== undefined) {
    conditions.push(`ulid_school_type = $${paramCount++}`);
    values.push(ulid_school_type);
  }
  if (email !== undefined) {
    conditions.push(`email = $${paramCount++}`);
    values.push(email);
  }
  if (is_teacher !== undefined) {
    conditions.push(`is_teacher = $${paramCount++}`);
    values.push(is_teacher.toString());
  }
  if (state !== undefined) {
    conditions.push(`state = $${paramCount++}`);
    values.push(state);
  }
  if (county !== undefined) {
    conditions.push(`county = $${paramCount++}`);
    values.push(county);
  }
  if (active !== undefined) {
    conditions.push(`active = $${paramCount++}`);
    values.push(active.toString());
  }

  const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';

  return await pgHelper.query(`SELECT * FROM tab_user ${whereClause}`, values);
}

async function update(
  ulid_user: string,
  ulid_user_type?: string,
  ulid_school_type?: string,
  email?: string,
  password?: string,
  is_teacher?: boolean,
  name?: string,
  state?: string,
  county?: string,
  active?: boolean
): Promise<QueryResult> {
  const updates = [];
  const values = [ulid_user];
  let paramCount = 2;

  if (ulid_user_type !== undefined) {
    updates.push(`ulid_user_type = $${paramCount++}`);
    values.push(ulid_user_type);
  }
  if (ulid_school_type !== undefined) {
    updates.push(`ulid_school_type = $${paramCount++}`);
    values.push(ulid_school_type);
  }
  if (email !== undefined) {
    updates.push(`email = $${paramCount++}`);
    values.push(email);
  }
  if (password !== undefined) {
    updates.push(`password = $${paramCount++}`);
    values.push(password);
  }
  if (is_teacher !== undefined) {
    updates.push(`is_teacher = $${paramCount++}`);
    values.push(is_teacher.toString());
  }
  if (name !== undefined) {
    updates.push(`name = $${paramCount++}`);
    values.push(name);
  }
  if (state !== undefined) {
    updates.push(`state = $${paramCount++}`);
    values.push(state);
  }
  if (county !== undefined) {
    updates.push(`county = $${paramCount++}`);
    values.push(county);
  }
  if (active !== undefined) {
    updates.push(`active = $${paramCount++}`);
    values.push(active.toString());
  }

  updates.push('updated_at = CURRENT_TIMESTAMP');

  return await pgHelper.query(
    `UPDATE tab_user 
     SET ${updates.join(', ')} 
     WHERE ulid_user = $1 
     RETURNING *`,
    values
  );
}

async function deleteByUlid(ulid_user: string): Promise<QueryResult> {
  return await pgHelper.query(
    `DELETE FROM tab_user 
      WHERE ulid_user = $1 
     RETURNING *`,
    [ulid_user]
  );
}

async function inactivate(ulid_user: string): Promise<QueryResult> {
  return await pgHelper.query(
    `UPDATE tab_user 
        SET active = false, 
            updated_at = CURRENT_TIMESTAMP 
      WHERE ulid_user = $1 
     RETURNING *`,
    [ulid_user]
  );
}

export const userRepository = {
  create,
  read,
  update,
  deleteByUlid,
  inactivate,
};
