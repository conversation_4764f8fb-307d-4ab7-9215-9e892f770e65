/**
 * Interfaces para o sistema RBAC (Role-Based Access Control)
 */

/**
 * Interface para dados de papel (role)
 */
export interface RoleData {
  ulid_role: string;
  name: string;
  description?: string;
  is_system: boolean;
  active: boolean;
  created_at: Date;
  updated_at: Date;
}

/**
 * Interface para dados de recurso (resource)
 */
export interface ResourceData {
  ulid_resource: string;
  name: string;
  description?: string;
  active: boolean;
  created_at: Date;
  updated_at: Date;
}

/**
 * Interface para dados de permissão (permission)
 */
export interface PermissionData {
  ulid_permission: string;
  name: string;
  description?: string;
  action: string;
  active: boolean;
  created_at: Date;
  updated_at: Date;
}

/**
 * Interface para associação entre recurso e permissão
 */
export interface ResourcePermissionData {
  ulid_resource_permission: string;
  ulid_resource: string;
  ulid_permission: string;
  created_at: Date;

  // Campos expandidos (não estão no banco)
  resource_name?: string;
  permission_name?: string;
  permission_action?: string;
}

/**
 * Interface para associação entre papel e permissão de recurso
 */
export interface RoleResourcePermissionData {
  ulid_role_resource_permission: string;
  ulid_role: string;
  ulid_resource_permission: string;
  created_at: Date;

  // Campos expandidos (não estão no banco)
  role_name?: string;
  resource_name?: string;
  permission_name?: string;
  permission_action?: string;
}

/**
 * Interface para associação entre usuário e papel
 */
export interface UserRoleData {
  ulid_user_role: string;
  ulid_user: string;
  ulid_role: string;
  created_at: Date;

  // Campos expandidos (não estão no banco)
  user_name?: string;
  user_email?: string;
  role_name?: string;
}

/**
 * Interface para hierarquia de papéis
 */
export interface RoleHierarchyData {
  ulid_role_hierarchy: string;
  ulid_parent_role: string;
  ulid_child_role: string;
  created_at: Date;

  // Campos expandidos (não estão no banco)
  parent_role_name?: string;
  child_role_name?: string;
}

/**
 * Interface para permissão completa com informações de recurso
 */
export interface FullPermissionData extends PermissionData {
  resource_name: string;
  resource_description?: string;
}

/**
 * Interface para papel completo com todas as permissões
 */
export interface FullRoleData extends RoleData {
  permissions: FullPermissionData[];
  parent_roles?: string[];
  child_roles?: string[];
}

/**
 * Interface para verificação de permissão
 */
export interface PermissionCheckData {
  resource: string;
  action: string;
}

/**
 * Interface para resultado de verificação de permissão
 */
export interface PermissionCheckResult {
  granted: boolean;
  reason?: string;
}
