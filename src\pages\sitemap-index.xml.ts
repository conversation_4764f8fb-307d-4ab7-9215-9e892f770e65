/**
 * Sitemap Index XML Endpoint
 *
 * Endpoint para gerar o índice de sitemaps XML do site.
 * Parte da implementação da tarefa 8.9.1 - Otimização para buscadores
 */

import path from 'node:path';
import type { APIRoute } from 'astro';
import { ContentRepository } from '../domain/repositories/ContentRepository';
import { SitemapRepository } from '../domain/repositories/SitemapRepository';
import { SitemapService } from '../domain/services/SitemapService';
import { FileSitemapRepository } from '../infrastructure/database/repositories/FileSitemapRepository';
import { PostgresContentRepository } from '../infrastructure/database/repositories/PostgresContentRepository';
import { DefaultSitemapService } from '../infrastructure/services/DefaultSitemapService';

// Inicializar repositórios
const sitemapRepository: SitemapRepository = new FileSitemapRepository(
  path.join(process.cwd(), 'public', 'sitemaps')
);
const contentRepository: ContentRepository = new PostgresContentRepository();

// Inicializar serviço
const baseUrl = import.meta.env.BASE_URL || 'https://example.com';
const sitemapService: SitemapService = new DefaultSitemapService(
  sitemapRepository,
  contentRepository,
  baseUrl
);

export const GET: APIRoute = async () => {
  try {
    // Inicializar repositório de sitemaps
    if ('initialize' in sitemapRepository) {
      await (sitemapRepository as FileSitemapRepository).initialize();
    }

    // Gerar índice de sitemaps
    const xml = await sitemapService.generateSitemapIndex();

    return new Response(xml, {
      status: 200,
      headers: {
        'Content-Type': 'application/xml',
        'Cache-Control': 'public, max-age=3600',
      },
    });
  } catch (error) {
    console.error('Erro ao gerar índice de sitemaps:', error);

    return new Response('Erro ao gerar índice de sitemaps', {
      status: 500,
      headers: {
        'Content-Type': 'text/plain',
      },
    });
  }
};
