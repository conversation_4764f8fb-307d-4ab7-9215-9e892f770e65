/**
 * Tipos de conteúdo educacional
 *
 * Este objeto de valor representa os tipos possíveis de conteúdo educacional no sistema.
 */

export enum ContentType {
  ARTICLE = 'article',
  VIDEO = 'video',
  ACTIVITY = 'activity',
  GAME = 'game',
  QUIZ = 'quiz',
  LESSON = 'lesson',
  COURSE = 'course',
  WORKSHEET = 'worksheet',
}

/**
 * Verifica se um valor é um tipo de conteúdo válido
 *
 * @param value Valor a ser verificado
 * @returns Verdadeiro se o valor for um tipo de conteúdo válido
 */
export function isValidContentType(value: string): value is ContentType {
  return Object.values(ContentType).includes(value as ContentType);
}

/**
 * Obtém a descrição de um tipo de conteúdo
 *
 * @param type Tipo de conteúdo
 * @returns Descrição do tipo de conteúdo
 */
export function getContentTypeDescription(type: ContentType): string {
  const descriptions: Record<ContentType, string> = {
    [ContentType.ARTICLE]: 'Artigo',
    [ContentType.VIDEO]: 'Vídeo',
    [ContentType.ACTIVITY]: 'Atividade',
    [ContentType.GAME]: 'Jogo',
    [ContentType.QUIZ]: 'Quiz',
    [ContentType.LESSON]: 'Lição',
    [ContentType.COURSE]: 'Curso',
    [ContentType.WORKSHEET]: 'Folha de Exercícios',
  };

  return descriptions[type] || 'Desconhecido';
}

/**
 * Obtém o ícone de um tipo de conteúdo
 *
 * @param type Tipo de conteúdo
 * @returns Nome do ícone para o tipo de conteúdo
 */
export function getContentTypeIcon(type: ContentType): string {
  const icons: Record<ContentType, string> = {
    [ContentType.ARTICLE]: 'file-text',
    [ContentType.VIDEO]: 'video',
    [ContentType.ACTIVITY]: 'activity',
    [ContentType.GAME]: 'gamepad',
    [ContentType.QUIZ]: 'help-circle',
    [ContentType.LESSON]: 'book-open',
    [ContentType.COURSE]: 'book',
    [ContentType.WORKSHEET]: 'file',
  };

  return icons[type] || 'help-circle';
}
