---
import NotificationList from '../../components/notification/NotificationList.astro';
import { PageTransition } from '../../components/transitions';
import DaisyTabs from '../../components/ui/DaisyTabs.astro';
import Breadcrumbs from '../../components/ui/navigation/Breadcrumbs.astro';
/**
 * Página de Notificações
 *
 * Interface para visualizar e gerenciar notificações.
 * Parte da implementação da tarefa 8.5.1 - Notificações internas
 */
import BaseLayout from '../../layouts/BaseLayout.astro';
import { Container, Section } from '../../layouts/grid';

// Título da página
const title = 'Notificações';

// Breadcrumbs para a página atual
const breadcrumbItems = [{ href: '/', label: 'Início' }, { label: 'Notificações' }];

// Obter parâmetros de consulta
const view = Astro.url.searchParams.get('view') || 'all';
const page = Astro.url.searchParams.get('page')
  ? Number.parseInt(Astro.url.searchParams.get('page') as string)
  : 1;
const type = Astro.url.searchParams.get('type');

// Em um cenário real, buscaríamos as notificações do repositório
// Por enquanto, usaremos dados de exemplo
const notifications = [
  {
    id: 'notif-001',
    title: 'Bem-vindo ao sistema',
    content:
      'Obrigado por se cadastrar! Explore nossos recursos e descubra tudo o que podemos oferecer para ajudar no desenvolvimento educacional.',
    type: 'info',
    isRead: false,
    isArchived: false,
    createdAt: new Date(Date.now() - 1000 * 60 * 5), // 5 minutos atrás
    actions: [
      {
        label: 'Explorar recursos',
        url: '/resources',
        primary: true,
      },
    ],
  },
  {
    id: 'notif-002',
    title: 'Pagamento confirmado',
    content:
      'Seu pagamento para o pedido #12345 foi processado com sucesso. Você já pode acessar os materiais adquiridos em sua biblioteca.',
    type: 'success',
    isRead: false,
    isArchived: false,
    createdAt: new Date(Date.now() - 1000 * 60 * 30), // 30 minutos atrás
    actions: [
      {
        label: 'Ver pedido',
        url: '/orders/12345',
      },
      {
        label: 'Acessar biblioteca',
        url: '/library',
        primary: true,
      },
    ],
  },
  {
    id: 'notif-003',
    title: 'Novo conteúdo disponível',
    content:
      'Confira nosso novo material sobre alfabetização. Este conteúdo foi desenvolvido por especialistas e está disponível em sua biblioteca.',
    type: 'content',
    isRead: true,
    isArchived: false,
    createdAt: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2 horas atrás
    actions: [
      {
        label: 'Ver conteúdo',
        url: '/content/alfabetizacao',
        primary: true,
      },
    ],
  },
  {
    id: 'notif-004',
    title: 'Promoção especial',
    content:
      'Aproveite nossa promoção especial de 20% de desconto em todos os materiais educacionais. Use o cupom EDUCA20 no checkout.',
    type: 'product',
    isRead: true,
    isArchived: false,
    createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24), // 1 dia atrás
    actions: [
      {
        label: 'Ver produtos',
        url: '/products',
        primary: true,
      },
    ],
  },
  {
    id: 'notif-005',
    title: 'Atualização do sistema',
    content:
      'O sistema será atualizado no dia 15/06/2023 às 22h. O serviço ficará indisponível por aproximadamente 30 minutos.',
    type: 'system',
    isRead: false,
    isArchived: false,
    createdAt: new Date(Date.now() - 1000 * 60 * 60 * 48), // 2 dias atrás
  },
  {
    id: 'notif-006',
    title: 'Lembrete de evento',
    content:
      'O webinar sobre "Técnicas de Ensino Inovadoras" acontecerá amanhã às 19h. Não se esqueça de participar!',
    type: 'info',
    isRead: true,
    isArchived: true,
    createdAt: new Date(Date.now() - 1000 * 60 * 60 * 72), // 3 dias atrás
    actions: [
      {
        label: 'Adicionar ao calendário',
        url: '/calendar/add?event=webinar-123',
        primary: true,
      },
    ],
  },
];

// Filtrar notificações com base nos parâmetros
let filteredNotifications = [...notifications];

if (view === 'unread') {
  filteredNotifications = notifications.filter((notification) => !notification.isRead);
} else if (view === 'archived') {
  filteredNotifications = notifications.filter((notification) => notification.isArchived);
} else if (view === 'all') {
  filteredNotifications = notifications.filter((notification) => !notification.isArchived);
}

if (type) {
  filteredNotifications = filteredNotifications.filter(
    (notification) => notification.type === type
  );
}

// Estatísticas para as abas
const stats = {
  all: notifications.filter((n) => !n.isArchived).length,
  unread: notifications.filter((n) => !n.isRead).length,
  archived: notifications.filter((n) => n.isArchived).length,
};

// Abas
const tabs = [
  { id: 'all', label: `Todas (${stats.all})`, content: '' },
  { id: 'unread', label: `Não lidas (${stats.unread})`, content: '' },
  { id: 'archived', label: `Arquivadas (${stats.archived})`, content: '' },
];

// Usuário atual (em um cenário real, seria obtido da sessão)
const currentUserId = 'user-123';
---

<BaseLayout title={title} enableViewTransitions={true} transitionType="fade">
  <Container>
    <PageTransition type="fade" duration="0.5s">
      <Section>
        <Breadcrumbs items={breadcrumbItems} class="mb-6" />
        
        <div class="flex justify-between items-center mb-6">
          <h1 class="text-3xl font-bold">{title}</h1>
          
          <a href="/notifications/preferences" class="btn btn-outline">
            <i class="icon icon-settings mr-2"></i>
            Preferências
          </a>
        </div>
        
        <div class="tabs-container">
          <div class="tabs tabs-boxed mb-6">
            {tabs.map(t => (
              <a 
                href={`/notifications?view=${t.id}`} 
                class={`tab ${t.id === view ? 'tab-active' : ''}`}
              >
                {t.label}
              </a>
            ))}
          </div>
          
          <div class="tab-content">
            <NotificationList
              userId={currentUserId}
              notifications={filteredNotifications}
              viewType={view as 'all' | 'unread' | 'archived'}
              layout="default"
              showBulkActions={true}
              showFilters={true}
              showPagination={true}
              currentPage={page}
              totalPages={1}
              paginationBaseUrl="/notifications"
            />
          </div>
        </div>
      </Section>
    </PageTransition>
  </Container>
</BaseLayout>

<script>
  // Script para atualizar a aba ativa quando o usuário muda de aba
  document.addEventListener('DOMContentLoaded', () => {
    const tabLinks = document.querySelectorAll('.tabs .tab');
    
    tabLinks.forEach(link => {
      link.addEventListener('click', (e) => {
        // Atualizar URL sem recarregar a página
        const url = new URL(link.getAttribute('href') || '', window.location.origin);
        const view = url.searchParams.get('view');
        
        // Atualizar classes
        tabLinks.forEach(tab => tab.classList.remove('tab-active'));
        link.classList.add('tab-active');
        
        // Atualizar histórico do navegador
        window.history.pushState({}, '', url.toString());
        
        // Atualizar conteúdo (em um cenário real, seria feito via AJAX)
        // Por enquanto, apenas recarregamos a página
        window.location.href = url.toString();
        
        e.preventDefault();
      });
    });
  });
</script>
