---
import InputText from '@components/form/InputText.astro';
import AdminLayout from '@layouts/AdminLayout.astro';
---
<AdminLayout title="Cadastro de Tipo de Escola">
  <template>
    <div class="search-container flex justify-between mb-4">
      <InputText id="search-input" label="Pesquisar" name="search" placeholder="Pesquisar tipos de escola..." />
      <div>
        <button class="btn btn-primary" id="handleSearch">Pesquisar</button>
        <button class="btn btn-secondary ml-2" id="navigateToNewSchoolType">Novo</button>
      </div>
    </div>

    <ul class="school-type-list list-disc pl-5">
    </ul>
  </template>

  <style>
    .search-container {
      display: flex;
      justify-content: space-between;
      margin-bottom: 20px;
    }
    .school-type-list {
      list-style: none;
      padding: 0;
    }
    .school-type-list li {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10px;
    }
  </style>
</AdminLayout>

<script>
  import { actions } from "astro:actions";

  interface SchoolType {
    ulid_school_type: string;
    type: string;
  }

  let schoolTypes: SchoolType[] = []; // Declare and initialize the 'schoolTypes' variable

  async function handleSearch() {
    const searchInputElement = document.getElementById('search-input') as HTMLInputElement;
    if (searchInputElement) {
      const searchInput = searchInputElement.value;
      if (searchInput !== null && searchInput !== undefined) {
        // Lógica para buscar tipos de escola com base no input
        const param = { filter: "type", type: searchInput };
        loadSchoolTypes(param);
      } else {
        console.error('O valor do input de pesquisa é nulo ou indefinido.');
      }
    } else {
      console.error('Elemento de input de pesquisa não encontrado.');
    }
  }

  function navigateToNewSchoolType() {
    // Lógica para redirecionar para o novo tipo de escola
    window.location.href = '/admin/register/user/school-type/new';
  }

  async function handleEdit(ulid_school_type: string) {
    // Lógica para redirecionar para o tipo de escola selecionado
    window.location.href = `/admin/register/user/school-type/${ulid_school_type}`;
  }

  async function handleDelete(ulid_school_type: string) {
    // Lógica para excluir o tipo de escola selecionado
    if (!confirm('Tem certeza que deseja excluir este tipo de escola?')) return;
    await actions.schoolTypeAction.delete({ ulid_school_type });
    await loadSchoolTypes({ filter: "all" });
  }

  async function loadSchoolTypes(param: any) {
    const result = await actions.schoolTypeAction.read(param);
    schoolTypes = result.data || [];

    // Apagar a lista existente
    const schoolTypeList = document.querySelector('.school-type-list');

    if (schoolTypeList) {
      schoolTypeList.innerHTML = '';

      // Inserir o cabeçalho
      const titleRow = document.createElement('li');
      titleRow.innerHTML = `
          <strong>Nome</strong>
          <strong>Ações</strong>
      `;
      schoolTypeList.appendChild(titleRow);

      if (schoolTypes.length === 0) {
        const noDataMessage = document.createElement('li');
        noDataMessage.textContent = 'Nenhum tipo de escola encontrado.';
        schoolTypeList.appendChild(noDataMessage);
        return;
      }

      // Inserir novos tipos de escola
      schoolTypes.forEach((schoolType: SchoolType) => {
          const listItem = document.createElement('li');
          listItem.innerHTML = `
              <span>${schoolType.type}</span>
              <div>
                  <button type="button" class="btn btn-warning mr-2" onclick="handleEdit('${schoolType.ulid_school_type}')">Alterar</button>
                  <button type="button" class="btn btn-error" onclick="handleDelete('${schoolType.ulid_school_type}')">Excluir</button>
              </div>
          `;
          schoolTypeList.appendChild(listItem);
      });
    }
  }

  loadSchoolTypes({ filter: "all" });
</script>