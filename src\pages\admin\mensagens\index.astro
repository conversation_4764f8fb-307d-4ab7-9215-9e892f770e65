---
import { PageTransition } from '../../../components/transitions';
import DaisyButton from '../../../components/ui/DaisyButton.astro';
import DaisyCard from '../../../components/ui/DaisyCard.astro';
import DaisyTabs from '../../../components/ui/DaisyTabs.astro';
import Breadcrumbs from '../../../components/ui/navigation/Breadcrumbs.astro';
import { GetContactMessageStatsUseCase } from '../../../domain/usecases/contact/GetContactMessageStatsUseCase';
import { GetContactMessagesUseCase } from '../../../domain/usecases/contact/GetContactMessagesUseCase';
import { PostgresContactMessageRepository } from '../../../infrastructure/database/repositories/PostgresContactMessageRepository';
/**
 * Página de Gerenciamento de Mensagens
 *
 * Interface para gerenciar mensagens de contato.
 * Parte da implementação da tarefa 8.6.2 - Gestão de mensagens
 */
import BaseLayout from '../../../layouts/BaseLayout.astro';
import { Container, Section } from '../../../layouts/grid';

// Título da página
const title = 'Gerenciamento de Mensagens';

// Breadcrumbs para a página atual
const breadcrumbItems = [
  { href: '/', label: 'Início' },
  { href: '/admin', label: 'Administração' },
  { label: 'Mensagens' },
];

// Obter parâmetros de consulta
const tab = Astro.url.searchParams.get('tab') || 'all';
const page = Number.parseInt(Astro.url.searchParams.get('page') || '1');
const limit = Number.parseInt(Astro.url.searchParams.get('limit') || '10');
const search = Astro.url.searchParams.get('search') || '';
const status = Astro.url.searchParams.get('status') || '';
const category = Astro.url.searchParams.get('category') || '';
const priority = Astro.url.searchParams.get('priority') || '';

// Inicializar repositório e casos de uso
const contactMessageRepository = new PostgresContactMessageRepository();
const getContactMessagesUseCase = new GetContactMessagesUseCase(contactMessageRepository);
const getContactMessageStatsUseCase = new GetContactMessageStatsUseCase(contactMessageRepository);

// Obter estatísticas
const statsResult = await getContactMessageStatsUseCase.execute();
const stats = statsResult.success
  ? statsResult.data
  : {
      total: 0,
      pending: 0,
      read: 0,
      replied: 0,
      archived: 0,
      spam: 0,
      avgResponseTime: 0,
      categoryDistribution: {},
    };

// Construir filtro
const filter: any = {};

if (search) {
  filter.search = search;
}

if (status) {
  filter.status = status;
}

if (category) {
  filter.category = category;
}

if (priority) {
  filter.priority = priority;
}

// Determinar o tipo de consulta com base na aba
let type: 'all' | 'pending' | 'unassigned' | 'assigned' | 'highPriority' = 'all';

switch (tab) {
  case 'pending':
    type = 'pending';
    break;
  case 'unassigned':
    type = 'unassigned';
    break;
  case 'assigned':
    type = 'assigned';
    break;
  case 'highPriority':
    type = 'highPriority';
    break;
  default:
    type = 'all';
    break;
}

// Obter mensagens
const messagesResult = await getContactMessagesUseCase.execute({
  filter,
  pagination: { page, limit },
  type,
  userId: 'current-user-id', // Em um cenário real, seria o ID do usuário atual
});

const messages = messagesResult.success ? messagesResult.data?.messages || [] : [];
const totalMessages = messagesResult.success ? messagesResult.data?.total || 0 : 0;
const totalPages = messagesResult.success ? messagesResult.data?.totalPages || 1 : 1;

// Categorias disponíveis
const categories = [
  { value: 'general', label: 'Geral' },
  { value: 'support', label: 'Suporte' },
  { value: 'sales', label: 'Vendas' },
  { value: 'billing', label: 'Faturamento' },
  { value: 'technical', label: 'Técnico' },
  { value: 'partnership', label: 'Parcerias' },
  { value: 'feedback', label: 'Feedback' },
  { value: 'other', label: 'Outro' },
];

// Status disponíveis
const statuses = [
  { value: 'pending', label: 'Pendente', color: 'badge-warning' },
  { value: 'read', label: 'Lida', color: 'badge-info' },
  { value: 'replied', label: 'Respondida', color: 'badge-success' },
  { value: 'archived', label: 'Arquivada', color: 'badge-neutral' },
  { value: 'spam', label: 'Spam', color: 'badge-error' },
];

// Prioridades disponíveis
const priorities = [
  { value: 'low', label: 'Baixa', color: 'text-success' },
  { value: 'medium', label: 'Média', color: 'text-info' },
  { value: 'high', label: 'Alta', color: 'text-warning' },
  { value: 'urgent', label: 'Urgente', color: 'text-error' },
];

// Função para formatar data relativa
const formatRelativeTime = (date: Date): string => {
  const now = new Date();
  const diffMs = now.getTime() - date.getTime();
  const diffSec = Math.floor(diffMs / 1000);
  const diffMin = Math.floor(diffSec / 60);
  const diffHour = Math.floor(diffMin / 60);
  const diffDay = Math.floor(diffHour / 24);

  if (diffSec < 60) {
    return 'agora';
  }
  if (diffMin < 60) {
    return `${diffMin} min atrás`;
  }
  if (diffHour < 24) {
    return `${diffHour} h atrás`;
  }
  if (diffDay < 7) {
    return `${diffDay} d atrás`;
  }
  return date.toLocaleDateString('pt-BR');
};

// Função para truncar texto
const truncateText = (text: string, maxLength: number): string => {
  if (text.length <= maxLength) {
    return text;
  }

  return `${text.substring(0, maxLength)}...`;
};

// Abas
const tabs = [
  { id: 'all', label: 'Todas', count: stats?.total || 0 },
  { id: 'pending', label: 'Pendentes', count: stats?.pending || 0 },
  { id: 'unassigned', label: 'Não atribuídas', count: 0 }, // Seria calculado em um cenário real
  { id: 'assigned', label: 'Minhas mensagens', count: 0 }, // Seria calculado em um cenário real
  { id: 'highPriority', label: 'Alta prioridade', count: 0 }, // Seria calculado em um cenário real
];
---

<BaseLayout title={title} enableViewTransitions={true} transitionType="fade">
  <Container>
    <PageTransition type="fade" duration="0.5s">
      <Section>
        <Breadcrumbs items={breadcrumbItems} class="mb-6" />
        
        <div class="flex justify-between items-center mb-6">
          <h1 class="text-3xl font-bold">{title}</h1>
          
          <div class="flex gap-2">
            <DaisyButton 
              href="/admin/mensagens/estatisticas" 
              variant="outline" 
              icon="bar-chart-2"
              text="Estatísticas"
            />
          </div>
        </div>
        
        <div class="stats shadow mb-6 w-full">
          <div class="stat">
            <div class="stat-figure text-primary">
              <i class="icon icon-mail text-3xl"></i>
            </div>
            <div class="stat-title">Total</div>
            <div class="stat-value">{stats?.total || 0}</div>
          </div>
          
          <div class="stat">
            <div class="stat-figure text-warning">
              <i class="icon icon-clock text-3xl"></i>
            </div>
            <div class="stat-title">Pendentes</div>
            <div class="stat-value">{stats?.pending || 0}</div>
          </div>
          
          <div class="stat">
            <div class="stat-figure text-success">
              <i class="icon icon-check-circle text-3xl"></i>
            </div>
            <div class="stat-title">Respondidas</div>
            <div class="stat-value">{stats?.replied || 0}</div>
          </div>
          
          <div class="stat">
            <div class="stat-figure text-info">
              <i class="icon icon-clock text-3xl"></i>
            </div>
            <div class="stat-title">Tempo médio de resposta</div>
            <div class="stat-value">{stats?.avgResponseTime?.toFixed(1) || 0}h</div>
          </div>
        </div>
        
        <div class="tabs-container">
          <div class="tabs tabs-boxed mb-6">
            {tabs.map(t => (
              <a 
                href={`/admin/mensagens?tab=${t.id}`} 
                class={`tab ${t.id === tab ? 'tab-active' : ''}`}
              >
                {t.label} {t.count > 0 && <span class="badge badge-sm ml-1">{t.count}</span>}
              </a>
            ))}
          </div>
          
          <div class="flex flex-col md:flex-row gap-4 mb-6">
            <div class="form-control flex-1">
              <div class="input-group">
                <input 
                  type="text" 
                  placeholder="Buscar mensagens..." 
                  class="input input-bordered w-full" 
                  value={search}
                  id="search-input"
                />
                <button class="btn btn-square" id="search-button">
                  <i class="icon icon-search"></i>
                </button>
              </div>
            </div>
            
            <div class="flex gap-2">
              <select class="select select-bordered" id="status-filter">
                <option value="">Status</option>
                {statuses.map(s => (
                  <option value={s.value} selected={status === s.value}>{s.label}</option>
                ))}
              </select>
              
              <select class="select select-bordered" id="category-filter">
                <option value="">Categoria</option>
                {categories.map(c => (
                  <option value={c.value} selected={category === c.value}>{c.label}</option>
                ))}
              </select>
              
              <select class="select select-bordered" id="priority-filter">
                <option value="">Prioridade</option>
                {priorities.map(p => (
                  <option value={p.value} selected={priority === p.value}>{p.label}</option>
                ))}
              </select>
              
              <button class="btn btn-outline" id="clear-filters">
                <i class="icon icon-x"></i>
              </button>
            </div>
          </div>
          
          <div class="overflow-x-auto">
            <table class="table w-full">
              <thead>
                <tr>
                  <th>Status</th>
                  <th>Remetente</th>
                  <th>Assunto</th>
                  <th>Categoria</th>
                  <th>Prioridade</th>
                  <th>Data</th>
                  <th>Ações</th>
                </tr>
              </thead>
              <tbody>
                {messages.length === 0 ? (
                  <tr>
                    <td colspan="7" class="text-center py-8">
                      <div class="flex flex-col items-center">
                        <i class="icon icon-inbox text-4xl text-gray-400 mb-2"></i>
                        <p class="text-gray-500">Nenhuma mensagem encontrada</p>
                      </div>
                    </td>
                  </tr>
                ) : (
                  messages.map(message => (
                    <tr class="hover">
                      <td>
                        <span class={`badge ${statuses.find(s => s.value === message.status)?.color || 'badge-ghost'}`}>
                          {statuses.find(s => s.value === message.status)?.label || message.status}
                        </span>
                      </td>
                      <td>
                        <div class="font-bold">{message.name}</div>
                        <div class="text-sm opacity-50">{message.email}</div>
                      </td>
                      <td>{truncateText(message.subject, 30)}</td>
                      <td>
                        {categories.find(c => c.value === message.category)?.label || message.category}
                      </td>
                      <td>
                        <span class={priorities.find(p => p.value === message.priority)?.color || ''}>
                          {priorities.find(p => p.value === message.priority)?.label || message.priority}
                        </span>
                      </td>
                      <td>
                        <div class="tooltip" data-tip={message.createdAt.toLocaleString('pt-BR')}>
                          {formatRelativeTime(message.createdAt)}
                        </div>
                      </td>
                      <td>
                        <div class="flex gap-1">
                          <a 
                            href={`/admin/mensagens/${message.id}`} 
                            class="btn btn-sm btn-ghost"
                          >
                            <i class="icon icon-eye"></i>
                          </a>
                          
                          <button 
                            class="btn btn-sm btn-ghost text-success"
                            data-action="reply"
                            data-id={message.id}
                          >
                            <i class="icon icon-message-circle"></i>
                          </button>
                          
                          <div class="dropdown dropdown-end">
                            <button class="btn btn-sm btn-ghost">
                              <i class="icon icon-more-vertical"></i>
                            </button>
                            <ul class="dropdown-content z-[1] menu p-2 shadow bg-base-100 rounded-box w-52">
                              <li>
                                <button data-action="markAsRead" data-id={message.id}>
                                  <i class="icon icon-check"></i> Marcar como lida
                                </button>
                              </li>
                              <li>
                                <button data-action="archive" data-id={message.id}>
                                  <i class="icon icon-archive"></i> Arquivar
                                </button>
                              </li>
                              <li>
                                <button data-action="markAsSpam" data-id={message.id}>
                                  <i class="icon icon-alert-triangle"></i> Marcar como spam
                                </button>
                              </li>
                              <li>
                                <button data-action="delete" data-id={message.id} class="text-error">
                                  <i class="icon icon-trash"></i> Excluir
                                </button>
                              </li>
                            </ul>
                          </div>
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
          
          {totalPages > 1 && (
            <div class="flex justify-center mt-6">
              <div class="join">
                {Array.from({ length: totalPages }, (_, i) => i + 1).map(p => (
                  <a 
                    href={`/admin/mensagens?tab=${tab}&page=${p}&limit=${limit}${search ? `&search=${search}` : ''}${status ? `&status=${status}` : ''}${category ? `&category=${category}` : ''}${priority ? `&priority=${priority}` : ''}`} 
                    class={`join-item btn ${p === page ? 'btn-active' : ''}`}
                  >
                    {p}
                  </a>
                ))}
              </div>
            </div>
          )}
        </div>
      </Section>
    </PageTransition>
  </Container>
</BaseLayout>

<script>
  // Script para funcionalidade da página de mensagens
  document.addEventListener('DOMContentLoaded', () => {
    // Busca
    const searchInput = document.getElementById('search-input') as HTMLInputElement;
    const searchButton = document.getElementById('search-button');
    
    if (searchInput && searchButton) {
      searchButton.addEventListener('click', () => {
        applyFilters();
      });
      
      searchInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
          applyFilters();
        }
      });
    }
    
    // Filtros
    const statusFilter = document.getElementById('status-filter') as HTMLSelectElement;
    const categoryFilter = document.getElementById('category-filter') as HTMLSelectElement;
    const priorityFilter = document.getElementById('priority-filter') as HTMLSelectElement;
    const clearFiltersButton = document.getElementById('clear-filters');
    
    if (statusFilter && categoryFilter && priorityFilter) {
      statusFilter.addEventListener('change', applyFilters);
      categoryFilter.addEventListener('change', applyFilters);
      priorityFilter.addEventListener('change', applyFilters);
    }
    
    if (clearFiltersButton) {
      clearFiltersButton.addEventListener('click', clearFilters);
    }
    
    // Ações de mensagem
    const actionButtons = document.querySelectorAll('[data-action]');
    
    actionButtons.forEach(button => {
      button.addEventListener('click', async () => {
        const action = button.getAttribute('data-action');
        const messageId = button.getAttribute('data-id');
        
        if (!action || !messageId) {
          return;
        }
        
        switch (action) {
          case 'reply':
            window.location.href = `/admin/mensagens/${messageId}?reply=true`;
            break;
            
          case 'markAsRead':
            if (await confirmAction('Marcar como lida', 'Tem certeza que deseja marcar esta mensagem como lida?')) {
              // Em um cenário real, aqui seria feita uma chamada para a API
              console.log(`Marcando mensagem ${messageId} como lida`);
              window.location.reload();
            }
            break;
            
          case 'archive':
            if (await confirmAction('Arquivar', 'Tem certeza que deseja arquivar esta mensagem?')) {
              // Em um cenário real, aqui seria feita uma chamada para a API
              console.log(`Arquivando mensagem ${messageId}`);
              window.location.reload();
            }
            break;
            
          case 'markAsSpam':
            if (await confirmAction('Marcar como spam', 'Tem certeza que deseja marcar esta mensagem como spam?')) {
              // Em um cenário real, aqui seria feita uma chamada para a API
              console.log(`Marcando mensagem ${messageId} como spam`);
              window.location.reload();
            }
            break;
            
          case 'delete':
            if (await confirmAction('Excluir', 'Tem certeza que deseja excluir esta mensagem? Esta ação não pode ser desfeita.')) {
              // Em um cenário real, aqui seria feita uma chamada para a API
              console.log(`Excluindo mensagem ${messageId}`);
              window.location.reload();
            }
            break;
        }
      });
    });
    
    // Função para aplicar filtros
    function applyFilters() {
      const currentUrl = new URL(window.location.href);
      const tab = currentUrl.searchParams.get('tab') || 'all';
      
      const search = searchInput?.value || '';
      const status = statusFilter?.value || '';
      const category = categoryFilter?.value || '';
      const priority = priorityFilter?.value || '';
      
      const newUrl = new URL('/admin/mensagens', window.location.origin);
      newUrl.searchParams.set('tab', tab);
      
      if (search) {
        newUrl.searchParams.set('search', search);
      }
      
      if (status) {
        newUrl.searchParams.set('status', status);
      }
      
      if (category) {
        newUrl.searchParams.set('category', category);
      }
      
      if (priority) {
        newUrl.searchParams.set('priority', priority);
      }
      
      window.location.href = newUrl.toString();
    }
    
    // Função para limpar filtros
    function clearFilters() {
      const currentUrl = new URL(window.location.href);
      const tab = currentUrl.searchParams.get('tab') || 'all';
      
      const newUrl = new URL('/admin/mensagens', window.location.origin);
      newUrl.searchParams.set('tab', tab);
      
      window.location.href = newUrl.toString();
    }
    
    // Função para confirmar ação
    async function confirmAction(title: string, message: string): Promise<boolean> {
      return confirm(message);
    }
  });
</script>
