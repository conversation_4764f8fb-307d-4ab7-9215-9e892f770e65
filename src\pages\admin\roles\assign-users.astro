---
/**
 * Página para atribuir usuários a um papel
 *
 * Esta página permite atribuir múltiplos usuários a um papel específico.
 */

import Notification from '@components/admin/Notification.astro';
import UserRoleManager from '@components/admin/UserRoleManager.astro';
import PermissionGate from '@components/auth/PermissionGate.astro';
// Importações
import MainLayout from '@layouts/MainLayout.astro';
import { roleRepository } from '@repository/roleRepository';
import { getCurrentUser } from '@utils/authUtils';

// Obter parâmetros da rota
const roleId = Astro.url.searchParams.get('role');

// Verificar autenticação
const user = await getCurrentUser(Astro.cookies);

// Redirecionar se não estiver autenticado
if (!user) {
  return Astro.redirect('/signin?redirect=/admin/roles/assign-users');
}

// Verificar se o ID do papel foi fornecido
if (!roleId) {
  return Astro.redirect('/admin/permissions?error=missing-role-id');
}

// Buscar dados do papel
const roleResult = await roleRepository.read(roleId);

// Verificar se o papel existe
if (roleResult.rowCount === 0) {
  return Astro.redirect('/admin/permissions?error=role-not-found');
}

const role = roleResult.rows[0];

// Obter parâmetros de consulta para mensagens
const success = Astro.url.searchParams.get('success');
const error = Astro.url.searchParams.get('error');

// Título da página
const title = `Atribuir Usuários: ${role.name}`;
---

<MainLayout title={title}>
  <main class="container mx-auto px-4 py-8">
    <PermissionGate resource="roles" action="assign">
      <div class="flex items-center justify-between mb-6">
        <h1 class="text-3xl font-bold">{title}</h1>
        
        <div class="flex space-x-2">
          <a 
            href={`/admin/roles/${roleId}`}
            class="bg-gray-200 text-gray-800 px-4 py-2 rounded hover:bg-gray-300 transition"
          >
            Voltar
          </a>
        </div>
      </div>
      
      {success && (
        <div class="mb-6">
          {success === 'users-assigned' && (
            <Notification 
              type="success" 
              title="Usuários atribuídos" 
              message="Os usuários foram atribuídos ao papel com sucesso."
            />
          )}
        </div>
      )}
      
      {error && (
        <div class="mb-6">
          {error === 'assign-failed' && (
            <Notification 
              type="error" 
              title="Erro ao atribuir" 
              message="Ocorreu um erro ao atribuir usuários ao papel. Tente novamente."
            />
          )}
        </div>
      )}
      
      <div class="bg-white rounded-lg shadow p-6 mb-6">
        <h2 class="text-xl font-semibold mb-2">Detalhes do Papel</h2>
        
        <div class="flex items-center">
          <div>
            <p class="text-gray-700">
              <span class="font-medium">Nome:</span> {role.name}
            </p>
            {role.description && (
              <p class="text-gray-600 mt-1">
                <span class="font-medium">Descrição:</span> {role.description}
              </p>
            )}
          </div>
          
          {role.is_system && (
            <span class="ml-4 px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
              Papel do Sistema
            </span>
          )}
        </div>
      </div>
      
      <div class="bg-white rounded-lg shadow overflow-hidden">
        <div class="p-6 bg-gray-50 border-b">
          <h2 class="text-xl font-semibold">Gerenciar Usuários</h2>
          <p class="mt-1 text-gray-600">
            Atribua ou remova usuários deste papel.
          </p>
        </div>
        
        <div class="p-6">
          <UserRoleManager roleId={roleId} />
        </div>
      </div>
      
      <slot name="fallback">
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mt-6">
          <p>Você não tem permissão para atribuir usuários a papéis.</p>
          <p class="mt-2">
            <a href={`/admin/roles/${roleId}`} class="text-red-700 font-medium hover:underline">Voltar para detalhes do papel</a>
          </p>
        </div>
      </slot>
    </PermissionGate>
  </main>
</MainLayout>

<script>
  // Script para interatividade no cliente
  document.addEventListener('DOMContentLoaded', () => {
    // Implementar funcionalidades interativas aqui
  });
</script>

<style>
  /* Estilos específicos da página */
</style>
