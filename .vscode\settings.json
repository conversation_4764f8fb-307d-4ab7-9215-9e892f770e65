{"editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": true, "source.organizeImports": true}, "editor.defaultFormatter": "esbenp.prettier-vscode", "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[astro]": {"editor.defaultFormatter": "astro-build.astro-vscode"}, "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact", "astro"], "typescript.tsdk": "node_modules/typescript/lib", "files.associations": {"*.astro": "astro"}, "search.exclude": {"**/node_modules": true, "**/dist": true, "**/.astro": true}, "files.exclude": {"**/.git": true, "**/.DS_Store": true, "**/node_modules": false, "**/dist": false}, "typescript.preferences.importModuleSpecifier": "non-relative", "javascript.preferences.importModuleSpecifier": "non-relative", "prettier.documentSelectors": ["**/*.astro"], "eslint.workingDirectories": [{"mode": "auto"}]}