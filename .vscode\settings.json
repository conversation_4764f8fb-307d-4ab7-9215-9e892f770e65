{"editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.organizeImports.biome": "explicit", "source.fixAll.biome": "explicit"}, "editor.defaultFormatter": "biomejs.biome", "[typescript]": {"editor.defaultFormatter": "biomejs.biome"}, "[javascript]": {"editor.defaultFormatter": "biomejs.biome"}, "[json]": {"editor.defaultFormatter": "biomejs.biome"}, "[jsonc]": {"editor.defaultFormatter": "biomejs.biome"}, "[astro]": {"editor.defaultFormatter": "astro-build.astro-vscode"}, "typescript.tsdk": "node_modules/typescript/lib", "files.associations": {"*.astro": "astro"}, "search.exclude": {"**/node_modules": true, "**/dist": true, "**/.astro": true}, "files.exclude": {"**/.git": true, "**/.DS_Store": true, "**/node_modules": false, "**/dist": false}, "typescript.preferences.importModuleSpecifier": "non-relative", "javascript.preferences.importModuleSpecifier": "non-relative", "biome.lspBin": "node_modules/.bin/biome", "biome.enabled": true}