/**
 * Create Promotional Banner Use Case
 *
 * Caso de uso para criar um novo banner promocional.
 * Parte da implementação da tarefa 8.4.3 - Promoções
 */

import {
  BannerAction,
  BannerDisplay,
  BannerPosition,
  BannerSize,
  PromotionalBanner,
} from '../../entities/PromotionalBanner';
import { PromotionalBannerRepository } from '../../repositories/PromotionalBannerRepository';

export interface CreateBannerRequest {
  title: string;
  subtitle?: string;
  content?: string;
  display: {
    position: BannerPosition;
    size: BannerSize;
    order?: number;
    mobileImage?: string;
    desktopImage?: string;
    backgroundColor?: string;
    textColor?: string;
  };
  action?: {
    type: 'link' | 'product' | 'category' | 'collection' | 'coupon' | 'promotion';
    targetId?: string;
    url?: string;
    couponCode?: string;
  };
  startDate?: Date;
  endDate?: Date;
  isActive?: boolean;
  userGroups?: string[];
  createdBy?: string;
}

export interface CreateBannerResponse {
  success: boolean;
  banner?: PromotionalBanner;
  error?: string;
}

export class CreatePromotionalBannerUseCase {
  constructor(private bannerRepository: PromotionalBannerRepository) {}

  async execute(request: CreateBannerRequest): Promise<CreateBannerResponse> {
    try {
      // Validar os dados de entrada
      if (!this.validateRequest(request)) {
        return {
          success: false,
          error: 'Dados inválidos para criação de banner promocional.',
        };
      }

      // Determinar a ordem do banner, se não fornecida
      let order = request.display.order;

      if (order === undefined) {
        // Buscar banners existentes na mesma posição para determinar a próxima ordem
        const existingBanners = await this.bannerRepository.getActiveBannersForPosition(
          request.display.position
        );

        // Definir a ordem como a maior ordem existente + 1, ou 0 se não houver banners
        order =
          existingBanners.length > 0
            ? Math.max(...existingBanners.map((b) => b.display.order)) + 1
            : 0;
      }

      // Criar o objeto de exibição
      const display: BannerDisplay = {
        position: request.display.position,
        size: request.display.size,
        order,
        mobileImage: request.display.mobileImage,
        desktopImage: request.display.desktopImage,
        backgroundColor: request.display.backgroundColor,
        textColor: request.display.textColor,
      };

      // Criar o objeto de ação, se fornecido
      let action: BannerAction | undefined;

      if (request.action) {
        action = request.action as BannerAction;
      }

      // Criar o banner
      const banner = new PromotionalBanner({
        id: crypto.randomUUID(),
        title: request.title,
        subtitle: request.subtitle,
        content: request.content,
        display,
        action,
        startDate: request.startDate,
        endDate: request.endDate,
        isActive: request.isActive !== undefined ? request.isActive : true,
        userGroups: request.userGroups,
        createdBy: request.createdBy,
      });

      // Salvar o banner
      const savedBanner = await this.bannerRepository.create(banner);

      return {
        success: true,
        banner: savedBanner,
      };
    } catch (error) {
      console.error('Erro ao criar banner promocional:', error);
      return {
        success: false,
        error: 'Ocorreu um erro ao criar o banner promocional.',
      };
    }
  }

  private validateRequest(request: CreateBannerRequest): boolean {
    // Validar título
    if (!request.title || request.title.trim().length === 0) {
      return false;
    }

    // Validar exibição
    if (!request.display) {
      return false;
    }

    // Validar posição
    if (
      ![
        'home_hero',
        'home_featured',
        'category_top',
        'product_sidebar',
        'checkout',
        'popup',
      ].includes(request.display.position)
    ) {
      return false;
    }

    // Validar tamanho
    if (!['small', 'medium', 'large', 'full_width'].includes(request.display.size)) {
      return false;
    }

    // Validar ação, se fornecida
    if (request.action) {
      // Validar tipo de ação
      if (
        !['link', 'product', 'category', 'collection', 'coupon', 'promotion'].includes(
          request.action.type
        )
      ) {
        return false;
      }

      // Validar dados específicos por tipo
      switch (request.action.type) {
        case 'link':
          if (!request.action.url) {
            return false;
          }
          break;
        case 'product':
        case 'category':
        case 'collection':
        case 'promotion':
          if (!request.action.targetId) {
            return false;
          }
          break;
        case 'coupon':
          if (!request.action.couponCode) {
            return false;
          }
          break;
      }
    }

    // Validar datas
    if (request.startDate && request.endDate && request.startDate > request.endDate) {
      return false;
    }

    return true;
  }
}
