/**
 * Get Product Catalog Use Case
 *
 * Caso de uso para obter o catálogo de produtos.
 * Parte da implementação da tarefa 8.3.3 - Catálogo de produtos
 */

import { Product } from '../../entities/Product';
import { ProductCategory } from '../../entities/ProductCategory';
import {
  CategoryFilter,
  ProductCategoryRepository,
} from '../../repositories/ProductCategoryRepository';
import {
  PaginatedProducts,
  ProductFilter,
  ProductPaginationOptions,
  ProductRepository,
  ProductSortOptions,
} from '../../repositories/ProductRepository';

export interface GetProductCatalogRequest {
  categoryId?: string;
  categorySlug?: string;
  includeSubcategories?: boolean;
  tags?: string[];
  search?: string;
  priceMin?: number;
  priceMax?: number;
  featured?: boolean;
  sortBy?: 'name' | 'price' | 'createdAt' | 'updatedAt';
  sortDirection?: 'asc' | 'desc';
  page?: number;
  limit?: number;
}

export interface GetProductCatalogResponse {
  success: boolean;
  products?: PaginatedProducts;
  category?: ProductCategory;
  categories?: ProductCategory[];
  breadcrumb?: ProductCategory[];
  error?: string;
}

export class GetProductCatalogUseCase {
  constructor(
    private productRepository: ProductRepository,
    private categoryRepository: ProductCategoryRepository
  ) {}

  async execute(request: GetProductCatalogRequest): Promise<GetProductCatalogResponse> {
    try {
      // Construir filtro de produtos
      const filter: ProductFilter = {
        isActive: true,
        isVisible: true,
      };

      // Adicionar filtros opcionais
      if (request.tags && request.tags.length > 0) {
        filter.tags = request.tags;
      }

      if (request.priceMin !== undefined) {
        filter.priceMin = request.priceMin;
      }

      if (request.priceMax !== undefined) {
        filter.priceMax = request.priceMax;
      }

      if (request.featured !== undefined) {
        filter.isFeatured = request.featured;
      }

      // Configurar ordenação
      const sort: ProductSortOptions = {
        field: request.sortBy || 'name',
        direction: request.sortDirection || 'asc',
      };

      // Configurar paginação
      const pagination: ProductPaginationOptions = {
        page: request.page || 1,
        limit: request.limit || 12,
      };

      // Variáveis para armazenar informações de categoria
      let category: ProductCategory | null = null;
      let categories: ProductCategory[] = [];
      let breadcrumb: ProductCategory[] = [];

      // Se foi fornecido um ID ou slug de categoria, buscar produtos por categoria
      if (request.categoryId || request.categorySlug) {
        // Buscar a categoria
        if (request.categoryId) {
          category = await this.categoryRepository.getById(request.categoryId);
        } else if (request.categorySlug) {
          category = await this.categoryRepository.getBySlug(request.categorySlug);
        }

        if (!category) {
          return {
            success: false,
            error: 'Categoria não encontrada.',
          };
        }

        // Verificar se a categoria está ativa e visível
        if (!category.isActive || !category.isVisible) {
          return {
            success: false,
            error: 'Categoria não disponível.',
          };
        }

        // Adicionar categoria ao filtro
        filter.categories = [category.id];

        // Se incluir subcategorias, buscar todas as subcategorias
        if (request.includeSubcategories) {
          const subcategories = await this.categoryRepository.getSubcategories(category.id, true);

          // Adicionar IDs das subcategorias ao filtro
          if (subcategories.categories.length > 0) {
            filter.categories = [
              ...filter.categories,
              ...subcategories.categories.map((c) => c.id),
            ];
          }
        }

        // Buscar o caminho da categoria (breadcrumb)
        breadcrumb = await this.categoryRepository.getCategoryPath(category.id);

        // Buscar subcategorias para exibição
        const subcategories = await this.categoryRepository.getSubcategories(category.id, true);

        categories = subcategories.categories;
      } else {
        // Se não foi fornecida categoria, buscar categorias raiz
        const rootCategories = await this.categoryRepository.getRootCategories(true);
        categories = rootCategories.categories;
      }

      // Se foi fornecido um termo de busca, realizar busca
      let products: PaginatedProducts;

      if (request.search) {
        products = await this.productRepository.search(request.search, pagination);

        // Aplicar filtros adicionais aos resultados da busca
        // Nota: Em uma implementação real, a busca já incluiria os filtros
        if (
          filter.categories ||
          filter.tags ||
          filter.priceMin ||
          filter.priceMax ||
          filter.isFeatured
        ) {
          products = await this.productRepository.find(filter, sort, pagination);
        }
      } else {
        // Buscar produtos com os filtros configurados
        products = await this.productRepository.find(filter, sort, pagination);
      }

      return {
        success: true,
        products,
        category: category || undefined,
        categories,
        breadcrumb: breadcrumb.length > 0 ? breadcrumb : undefined,
      };
    } catch (error) {
      console.error('Erro ao obter catálogo de produtos:', error);
      return {
        success: false,
        error: 'Ocorreu um erro ao processar a solicitação do catálogo.',
      };
    }
  }
}
