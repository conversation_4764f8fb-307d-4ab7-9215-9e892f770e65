---
/**
 * Página de listagem de conteúdos educacionais
 *
 * Esta página implementa a listagem de conteúdos educacionais com filtros server-side,
 * seguindo a arquitetura Zero-JS.
 */

import MainNavigation from '../../components/navigation/MainNavigation.astro';
import { ContentType } from '../../domain/value-objects/ContentType';
import BaseLayout from '../../layouts/BaseLayout.astro';
import { getContentByAgeRange, getContentByType } from '../../services/DataService';

// Obter parâmetros da URL
const { searchParams } = Astro.url;
const type = (searchParams.get('tipo') as ContentType) || null;
const minAge = Number.parseInt(searchParams.get('idade_min') || '0', 10);
const maxAge = Number.parseInt(searchParams.get('idade_max') || '12', 10);
const page = Number.parseInt(searchParams.get('pagina') || '1', 10);
const limit = 12;

// Obter conteúdos com base nos filtros
let contents = [];
if (type) {
  contents = await getContentByType(type, limit);
} else if (minAge > 0 || maxAge < 12) {
  contents = await getContentByAgeRange(minAge, maxAge, limit);
} else {
  // Obter todos os tipos de conteúdo
  const atividades = await getContentByType(ContentType.ATIVIDADE, 4);
  const jogos = await getContentByType(ContentType.JOGO, 4);
  const materiais = await getContentByType(ContentType.MATERIAL, 4);

  // Combinar resultados
  contents = [...atividades, ...jogos, ...materiais];
}

// Tipos de conteúdo para filtro
const contentTypes = [
  { value: ContentType.ATIVIDADE, label: 'Atividades' },
  { value: ContentType.JOGO, label: 'Jogos' },
  { value: ContentType.MATERIAL, label: 'Materiais' },
  { value: ContentType.VIDEO, label: 'Vídeos' },
  { value: ContentType.AUDIO, label: 'Áudios' },
];

// Faixas etárias para filtro
const ageRanges = [
  { min: 3, max: 5, label: '3 a 5 anos' },
  { min: 6, max: 7, label: '6 a 7 anos' },
  { min: 8, max: 9, label: '8 a 9 anos' },
  { min: 10, max: 12, label: '10 a 12 anos' },
];

// Título e descrição da página
const title = type
  ? `${contentTypes.find((t) => t.value === type)?.label || 'Conteúdos'} - Estação Alfabetização`
  : 'Conteúdos Educacionais - Estação Alfabetização';

const description =
  'Explore nossa biblioteca de conteúdos educacionais para alfabetização. Atividades, jogos, materiais e muito mais para auxiliar no processo de aprendizagem.';
---

<BaseLayout title={title} description={description}>
  <MainNavigation currentPath="/conteudos" />
  
  <main class="contents-page">
    <div class="page-header">
      <div class="container">
        <h1>Conteúdos Educacionais</h1>
        <p class="lead">Explore nossa biblioteca de recursos para alfabetização</p>
      </div>
    </div>
    
    <div class="container">
      <div class="content-layout">
        <aside class="filters-sidebar">
          <div class="filter-section">
            <h2>Filtros</h2>
            
            <form method="get" action="/conteudos" class="filter-form">
              <div class="filter-group">
                <h3>Tipo de Conteúdo</h3>
                <div class="filter-options">
                  <div class="filter-option">
                    <input 
                      type="radio" 
                      id="tipo-todos" 
                      name="tipo" 
                      value="" 
                      checked={!type}
                    >
                    <label for="tipo-todos">Todos</label>
                  </div>
                  
                  {contentTypes.map(contentType => (
                    <div class="filter-option">
                      <input 
                        type="radio" 
                        id={`tipo-${contentType.value}`} 
                        name="tipo" 
                        value={contentType.value}
                        checked={type === contentType.value}
                      >
                      <label for={`tipo-${contentType.value}`}>{contentType.label}</label>
                    </div>
                  ))}
                </div>
              </div>
              
              <div class="filter-group">
                <h3>Faixa Etária</h3>
                <div class="filter-options">
                  <div class="filter-option">
                    <input 
                      type="radio" 
                      id="idade-todos" 
                      name="idade" 
                      value="todos"
                      checked={minAge === 0 && maxAge === 12}
                    >
                    <label for="idade-todos">Todas as idades</label>
                  </div>
                  
                  {ageRanges.map(range => (
                    <div class="filter-option">
                      <input 
                        type="radio" 
                        id={`idade-${range.min}-${range.max}`} 
                        name="idade" 
                        value={`${range.min}-${range.max}`}
                        checked={minAge === range.min && maxAge === range.max}
                      >
                      <label for={`idade-${range.min}-${range.max}`}>{range.label}</label>
                    </div>
                  ))}
                </div>
              </div>
              
              <button type="submit" class="btn btn-primary filter-submit">Aplicar Filtros</button>
            </form>
          </div>
        </aside>
        
        <div class="content-main">
          {type && (
            <div class="active-filters">
              <h2>{contentTypes.find(t => t.value === type)?.label || 'Conteúdos'}</h2>
              <a href="/conteudos" class="clear-filters">Limpar filtros</a>
            </div>
          )}
          
          {minAge > 0 || maxAge < 12 && (
            <div class="active-filters">
              <h2>Faixa etária: {minAge} a {maxAge} anos</h2>
              <a href="/conteudos" class="clear-filters">Limpar filtros</a>
            </div>
          )}
          
          {contents.length > 0 ? (
            <div class="content-grid">
              {contents.map(content => (
                <a href={`/conteudos/${content.id}`} class="content-card">
                  <div class="content-image">
                    <img 
                      src={content.featuredImageUrl || '/images/placeholder.jpg'} 
                      alt={content.title}
                      width="300"
                      height="200"
                      loading="lazy"
                    >
                  </div>
                  <div class="content-info">
                    <span class="content-type">{contentTypes.find(t => t.value === content.type)?.label}</span>
                    <h3 class="content-title">{content.title}</h3>
                    <p class="content-description">{content.description}</p>
                    <div class="content-meta">
                      <span class="content-age">Idade: {content.ageRange[0]}-{content.ageRange[1]} anos</span>
                    </div>
                  </div>
                </a>
              ))}
            </div>
          ) : (
            <div class="no-results">
              <p>Nenhum conteúdo encontrado com os filtros selecionados.</p>
              <a href="/conteudos" class="btn btn-outline">Ver todos os conteúdos</a>
            </div>
          )}
          
          <!-- Paginação -->
          <div class="pagination">
            <a href={`/conteudos?pagina=${page > 1 ? page - 1 : 1}${type ? `&tipo=${type}` : ''}${minAge > 0 ? `&idade_min=${minAge}` : ''}${maxAge < 12 ? `&idade_max=${maxAge}` : ''}`} class={`pagination-link ${page <= 1 ? 'disabled' : ''}`}>Anterior</a>
            
            <span class="pagination-info">Página {page}</span>
            
            <a href={`/conteudos?pagina=${page + 1}${type ? `&tipo=${type}` : ''}${minAge > 0 ? `&idade_min=${minAge}` : ''}${maxAge < 12 ? `&idade_max=${maxAge}` : ''}`} class="pagination-link">Próxima</a>
          </div>
        </div>
      </div>
    </div>
  </main>
</BaseLayout>

<style>
  .page-header {
    background-color: var(--color-primary-light, #f0f4ff);
    padding: 3rem 0;
    margin-bottom: 2rem;
    text-align: center;
  }
  
  .page-header h1 {
    font-size: 2.5rem;
    color: var(--color-text-dark, #111827);
    margin-bottom: 1rem;
  }
  
  .lead {
    font-size: 1.25rem;
    color: var(--color-text, #374151);
    max-width: 700px;
    margin: 0 auto;
  }
  
  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
  }
  
  .content-layout {
    display: grid;
    grid-template-columns: 250px 1fr;
    gap: 2rem;
    margin-bottom: 3rem;
  }
  
  .filters-sidebar {
    background-color: white;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    padding: 1.5rem;
    align-self: start;
    position: sticky;
    top: 90px;
  }
  
  .filter-section h2 {
    font-size: 1.25rem;
    color: var(--color-text-dark, #111827);
    margin-bottom: 1.5rem;
  }
  
  .filter-group {
    margin-bottom: 1.5rem;
  }
  
  .filter-group h3 {
    font-size: 1rem;
    color: var(--color-text-dark, #111827);
    margin-bottom: 0.75rem;
  }
  
  .filter-options {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .filter-option {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }
  
  .filter-option label {
    font-size: 0.875rem;
    color: var(--color-text, #374151);
    cursor: pointer;
  }
  
  .filter-submit {
    width: 100%;
    margin-top: 1rem;
  }
  
  .active-filters {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
  }
  
  .active-filters h2 {
    font-size: 1.5rem;
    color: var(--color-text-dark, #111827);
    margin: 0;
  }
  
  .clear-filters {
    color: var(--color-primary, #4a6cf7);
    text-decoration: none;
    font-size: 0.875rem;
  }
  
  .clear-filters:hover {
    text-decoration: underline;
  }
  
  .content-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
  }
  
  .content-card {
    background-color: white;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: transform 0.2s, box-shadow 0.2s;
    text-decoration: none;
    color: inherit;
    display: flex;
    flex-direction: column;
  }
  
  .content-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  }
  
  .content-image {
    height: 180px;
    overflow: hidden;
  }
  
  .content-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  
  .content-info {
    padding: 1.25rem;
    flex: 1;
    display: flex;
    flex-direction: column;
  }
  
  .content-type {
    display: inline-block;
    font-size: 0.75rem;
    font-weight: 500;
    color: var(--color-primary, #4a6cf7);
    background-color: var(--color-primary-light, #f0f4ff);
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    margin-bottom: 0.75rem;
  }
  
  .content-title {
    font-size: 1.125rem;
    color: var(--color-text-dark, #111827);
    margin-bottom: 0.5rem;
  }
  
  .content-description {
    font-size: 0.875rem;
    color: var(--color-text, #374151);
    margin-bottom: 1rem;
    flex: 1;
  }
  
  .content-meta {
    font-size: 0.75rem;
    color: var(--color-text-light, #6b7280);
  }
  
  .no-results {
    text-align: center;
    padding: 3rem 0;
  }
  
  .no-results p {
    font-size: 1.125rem;
    color: var(--color-text, #374151);
    margin-bottom: 1.5rem;
  }
  
  .pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 1rem;
    margin-top: 2rem;
  }
  
  .pagination-link {
    padding: 0.5rem 1rem;
    border: 1px solid var(--color-border, #e5e7eb);
    border-radius: 0.375rem;
    color: var(--color-text, #374151);
    text-decoration: none;
    transition: background-color 0.2s;
  }
  
  .pagination-link:hover {
    background-color: var(--color-bg-light, #f3f4f6);
  }
  
  .pagination-link.disabled {
    opacity: 0.5;
    pointer-events: none;
  }
  
  .pagination-info {
    font-size: 0.875rem;
    color: var(--color-text, #374151);
  }
  
  .btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.625rem 1.25rem;
    font-weight: 500;
    border-radius: 0.375rem;
    border: none;
    cursor: pointer;
    transition: all 0.2s;
    font-size: 1rem;
    line-height: 1.5;
    text-decoration: none;
  }
  
  .btn-primary {
    background-color: var(--color-primary, #4a6cf7);
    color: white;
  }
  
  .btn-primary:hover {
    background-color: var(--color-primary-dark, #3b5bd9);
  }
  
  .btn-outline {
    background-color: transparent;
    border: 1px solid var(--color-border, #e5e7eb);
    color: var(--color-text, #374151);
  }
  
  .btn-outline:hover {
    background-color: var(--color-bg-light, #f3f4f6);
  }
  
  @media (max-width: 768px) {
    .content-layout {
      grid-template-columns: 1fr;
    }
    
    .filters-sidebar {
      position: static;
      margin-bottom: 2rem;
    }
    
    .page-header {
      padding: 2rem 0;
    }
    
    .page-header h1 {
      font-size: 2rem;
    }
    
    .lead {
      font-size: 1.125rem;
    }
  }
  
  @media (prefers-reduced-motion: reduce) {
    .content-card,
    .btn,
    .pagination-link {
      transition: none;
    }
  }
</style>

<script>
  // Script para processar os filtros de idade
  document.addEventListener('DOMContentLoaded', () => {
    const form = document.querySelector('.filter-form');
    
    if (form) {
      form.addEventListener('submit', (e) => {
        e.preventDefault();
        
        const formData = new FormData(form);
        const tipo = formData.get('tipo');
        const idade = formData.get('idade');
        
        let url = '/conteudos?';
        const params = [];
        
        if (tipo) {
          params.push(`tipo=${tipo}`);
        }
        
        if (idade && idade !== 'todos') {
          const [min, max] = idade.split('-');
          params.push(`idade_min=${min}`);
          params.push(`idade_max=${max}`);
        }
        
        url += params.join('&');
        window.location.href = url;
      });
    }
  });
</script>
