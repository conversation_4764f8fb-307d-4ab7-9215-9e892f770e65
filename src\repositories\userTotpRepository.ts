/**
 * Repositório para gerenciamento de configurações TOTP dos usuários
 *
 * Este repositório implementa operações para armazenar e recuperar configurações
 * de autenticação de dois fatores (TOTP) dos usuários.
 *
 * Parte da implementação da tarefa 6.1.2 - Implementação de 2FA
 */

import { nanoid } from 'nanoid';
import { pool } from '../infrastructure/database/connection';
import { logger } from '../utils/logger';

// Interface para configuração TOTP de usuário
export interface UserTotp {
  id: string;
  user_id: string;
  secret: string;
  backup_codes: string[];
  enabled: boolean;
  verified: boolean;
  created_at: Date;
  updated_at: Date;
  last_used_at: Date | null;
}

// Interface para criação de configuração TOTP
export interface CreateUserTotp {
  user_id: string;
  secret: string;
  backup_codes: string[];
  enabled?: boolean;
  verified?: boolean;
}

// Interface para atualização de configuração TOTP
export interface UpdateUserTotp {
  secret?: string;
  backup_codes?: string[];
  enabled?: boolean;
  verified?: boolean;
  last_used_at?: Date;
}

/**
 * Repositório para gerenciamento de configurações TOTP
 */
export const userTotpRepository = {
  /**
   * Cria uma nova configuração TOTP para um usuário
   * @param data - Dados da configuração TOTP
   * @returns Configuração TOTP criada
   */
  async create(data: CreateUserTotp): Promise<UserTotp> {
    try {
      const id = uuidv4();
      const now = new Date();

      const query = `
        INSERT INTO user_totp (
          id, user_id, secret, backup_codes, enabled, verified, created_at, updated_at
        ) VALUES (
          $1, $2, $3, $4, $5, $6, $7, $8
        ) RETURNING *
      `;

      const values = [
        id,
        data.user_id,
        data.secret,
        JSON.stringify(data.backup_codes),
        data.enabled ?? false,
        data.verified ?? false,
        now,
        now,
      ];

      const result = await pool.query(query, values);

      // Converter backup_codes de JSON para array
      const userTotp = result.rows[0];
      userTotp.backup_codes = JSON.parse(userTotp.backup_codes);

      return userTotp;
    } catch (error) {
      logger.error('Erro ao criar configuração TOTP:', error);
      throw new Error('Falha ao criar configuração TOTP');
    }
  },

  /**
   * Busca a configuração TOTP de um usuário
   * @param userId - ID do usuário
   * @returns Configuração TOTP ou null se não existir
   */
  async findByUserId(userId: string): Promise<UserTotp | null> {
    try {
      const query = `
        SELECT * FROM user_totp
        WHERE user_id = $1
      `;

      const result = await pool.query(query, [userId]);

      if (result.rows.length === 0) {
        return null;
      }

      // Converter backup_codes de JSON para array
      const userTotp = result.rows[0];
      userTotp.backup_codes = JSON.parse(userTotp.backup_codes);

      return userTotp;
    } catch (error) {
      logger.error('Erro ao buscar configuração TOTP:', error);
      throw new Error('Falha ao buscar configuração TOTP');
    }
  },

  /**
   * Atualiza a configuração TOTP de um usuário
   * @param userId - ID do usuário
   * @param data - Dados a serem atualizados
   * @returns Configuração TOTP atualizada
   */
  async update(userId: string, data: UpdateUserTotp): Promise<UserTotp> {
    try {
      // Construir query de atualização dinamicamente
      const updates: string[] = [];
      const values: any[] = [userId];
      let paramIndex = 2;

      // Adicionar campos a serem atualizados
      if (data.secret !== undefined) {
        updates.push(`secret = $${paramIndex++}`);
        values.push(data.secret);
      }

      if (data.backup_codes !== undefined) {
        updates.push(`backup_codes = $${paramIndex++}`);
        values.push(JSON.stringify(data.backup_codes));
      }

      if (data.enabled !== undefined) {
        updates.push(`enabled = $${paramIndex++}`);
        values.push(data.enabled);
      }

      if (data.verified !== undefined) {
        updates.push(`verified = $${paramIndex++}`);
        values.push(data.verified);
      }

      if (data.last_used_at !== undefined) {
        updates.push(`last_used_at = $${paramIndex++}`);
        values.push(data.last_used_at);
      }

      // Sempre atualizar updated_at
      updates.push(`updated_at = $${paramIndex++}`);
      values.push(new Date());

      // Executar query
      const query = `
        UPDATE user_totp
        SET ${updates.join(', ')}
        WHERE user_id = $1
        RETURNING *
      `;

      const result = await pool.query(query, values);

      if (result.rows.length === 0) {
        throw new Error('Configuração TOTP não encontrada');
      }

      // Converter backup_codes de JSON para array
      const userTotp = result.rows[0];
      userTotp.backup_codes = JSON.parse(userTotp.backup_codes);

      return userTotp;
    } catch (error) {
      logger.error('Erro ao atualizar configuração TOTP:', error);
      throw new Error('Falha ao atualizar configuração TOTP');
    }
  },

  /**
   * Remove a configuração TOTP de um usuário
   * @param userId - ID do usuário
   * @returns Verdadeiro se removido com sucesso
   */
  async delete(userId: string): Promise<boolean> {
    try {
      const query = `
        DELETE FROM user_totp
        WHERE user_id = $1
        RETURNING id
      `;

      const result = await pool.query(query, [userId]);

      return result.rows.length > 0;
    } catch (error) {
      logger.error('Erro ao remover configuração TOTP:', error);
      throw new Error('Falha ao remover configuração TOTP');
    }
  },

  /**
   * Verifica se um código de backup é válido e o consome
   * @param userId - ID do usuário
   * @param backupCode - Código de backup a ser verificado
   * @returns Verdadeiro se o código for válido
   */
  async verifyAndConsumeBackupCode(userId: string, backupCode: string): Promise<boolean> {
    try {
      // Buscar configuração TOTP do usuário
      const userTotp = await this.findByUserId(userId);

      if (!userTotp || !userTotp.enabled || !userTotp.verified) {
        return false;
      }

      // Verificar se o código existe na lista
      const backupCodes = userTotp.backup_codes;
      const index = backupCodes.indexOf(backupCode);

      if (index === -1) {
        return false;
      }

      // Remover o código usado
      backupCodes.splice(index, 1);

      // Atualizar a lista de códigos
      await this.update(userId, {
        backup_codes: backupCodes,
        last_used_at: new Date(),
      });

      return true;
    } catch (error) {
      logger.error('Erro ao verificar código de backup:', error);
      return false;
    }
  },
};

export default userTotpRepository;
