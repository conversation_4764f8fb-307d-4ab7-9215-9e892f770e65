/**
 * Serviço de serialização de mensagens
 *
 * Este serviço fornece funcionalidades para serialização e deserialização de mensagens
 * em diferentes formatos, com suporte para versionamento e compatibilidade.
 */

import { promisify } from 'node:util';
import { gzip, ungzip } from 'node:zlib';
import { cacheService } from '@services/cacheService';
import { messageTransformationService } from '@services/messageTransformationService';
import { schemaValidationService } from '@services/schemaValidationService';
import { logger } from '@utils/logger';
import { v4 as uuidv4 } from 'uuid';

// Promisificar funções de compressão
const gzipAsync = promisify(gzip);
const ungzipAsync = promisify(ungzip);

/**
 * Formatos de serialização suportados
 */
export enum SerializationFormat {
  JSON = 'json',
  AVRO = 'avro',
  PROTOBUF = 'protobuf',
  BINARY = 'binary',
}

/**
 * Opções de serialização
 */
export interface SerializationOptions {
  /**
   * Formato de serialização
   */
  format?: SerializationFormat;

  /**
   * Versão do esquema
   */
  schemaVersion?: number;

  /**
   * Nome do esquema
   */
  schemaName?: string;

  /**
   * Se deve comprimir os dados
   */
  compress?: boolean;

  /**
   * Limiar de tamanho para compressão (bytes)
   */
  compressionThreshold?: number;

  /**
   * Metadados adicionais
   */
  metadata?: Record<string, string>;

  /**
   * Se deve validar o esquema
   */
  validateSchema?: boolean;
}

/**
 * Resultado de serialização
 */
export interface SerializationResult {
  /**
   * Dados serializados
   */
  data: Buffer | string;

  /**
   * Formato de serialização
   */
  format: SerializationFormat;

  /**
   * Versão do esquema
   */
  schemaVersion?: number;

  /**
   * Nome do esquema
   */
  schemaName?: string;

  /**
   * Se os dados estão comprimidos
   */
  compressed: boolean;

  /**
   * Metadados adicionais
   */
  metadata: Record<string, string>;

  /**
   * Tamanho original dos dados (bytes)
   */
  originalSize: number;

  /**
   * Tamanho serializado dos dados (bytes)
   */
  serializedSize: number;
}

/**
 * Resultado de deserialização
 */
export interface DeserializationResult<T = Record<string, unknown>> {
  /**
   * Dados deserializados
   */
  data: T;

  /**
   * Formato de serialização
   */
  format: SerializationFormat;

  /**
   * Versão do esquema
   */
  schemaVersion?: number;

  /**
   * Nome do esquema
   */
  schemaName?: string;

  /**
   * Metadados adicionais
   */
  metadata: Record<string, string>;

  /**
   * Se os dados estavam comprimidos
   */
  wasCompressed: boolean;
}

/**
 * Serviço de serialização de mensagens
 */
export const messageSerializationService = {
  /**
   * Configurações padrão
   */
  defaultOptions: {
    format: SerializationFormat.JSON,
    compress: false,
    compressionThreshold: 1024, // 1KB
    validateSchema: true,
  } as SerializationOptions,

  /**
   * Serializa uma mensagem
   * @param message - Mensagem a ser serializada
   * @param options - Opções de serialização
   * @returns Resultado da serialização
   */
  async serialize<T extends Record<string, unknown>>(
    message: T,
    options: SerializationOptions = {}
  ): Promise<SerializationResult> {
    try {
      // Mesclar opções com padrões
      const mergedOptions = {
        ...this.defaultOptions,
        ...options,
      };

      // Validar esquema se necessário
      if (mergedOptions.validateSchema && mergedOptions.schemaName) {
        const validationResult = await schemaValidationService.validateMessage(
          message,
          mergedOptions.schemaName
        );

        if (!validationResult.isValid) {
          throw new Error(`Erro de validação de esquema: ${validationResult.errors.join(', ')}`);
        }
      }

      // Adicionar metadados padrão
      const metadata = {
        'message-id': uuidv4(),
        timestamp: Date.now().toString(),
        'content-type': `application/${mergedOptions.format}`,
        ...(mergedOptions.schemaName ? { 'schema-name': mergedOptions.schemaName } : {}),
        ...(mergedOptions.schemaVersion
          ? { 'schema-version': mergedOptions.schemaVersion.toString() }
          : {}),
        ...(mergedOptions.metadata || {}),
      };

      // Serializar dados de acordo com o formato
      let serializedData: string | Buffer;
      let originalSize: number;

      switch (mergedOptions.format) {
        case SerializationFormat.JSON:
          serializedData = JSON.stringify(message);
          originalSize = serializedData.length;
          break;

        case SerializationFormat.AVRO:
          // Implementação de AVRO será adicionada posteriormente
          throw new Error('Formato AVRO não implementado');

        case SerializationFormat.PROTOBUF:
          // Implementação de PROTOBUF será adicionada posteriormente
          throw new Error('Formato PROTOBUF não implementado');

        case SerializationFormat.BINARY:
          // Implementação de BINARY será adicionada posteriormente
          throw new Error('Formato BINARY não implementado');

        default:
          throw new Error(`Formato de serialização não suportado: ${mergedOptions.format}`);
      }

      // Comprimir dados se necessário
      let compressed = false;
      let finalData: string | Buffer = serializedData;

      if (mergedOptions.compress && originalSize > (mergedOptions.compressionThreshold || 1024)) {
        if (typeof serializedData === 'string') {
          finalData = await gzipAsync(Buffer.from(serializedData));
        } else {
          finalData = await gzipAsync(serializedData);
        }

        compressed = true;

        // Adicionar metadado de compressão
        metadata['content-encoding'] = 'gzip';
      }

      return {
        data: finalData,
        format: mergedOptions.format,
        schemaName: mergedOptions.schemaName,
        schemaVersion: mergedOptions.schemaVersion,
        compressed,
        metadata,
        originalSize,
        serializedSize: typeof finalData === 'string' ? finalData.length : finalData.byteLength,
      };
    } catch (error) {
      logger.error('Erro ao serializar mensagem:', error);
      throw error;
    }
  },

  /**
   * Deserializa uma mensagem
   * @param data - Dados serializados
   * @param metadata - Metadados da mensagem
   * @returns Resultado da deserialização
   */
  async deserialize<T = Record<string, unknown>>(
    data: Buffer | string,
    metadata: Record<string, string> = {}
  ): Promise<DeserializationResult<T>> {
    try {
      // Determinar formato de serialização
      const contentType = metadata['content-type'] || 'application/json';
      let format = SerializationFormat.JSON;

      if (contentType.includes('avro')) {
        format = SerializationFormat.AVRO;
      } else if (contentType.includes('protobuf')) {
        format = SerializationFormat.PROTOBUF;
      } else if (contentType.includes('binary')) {
        format = SerializationFormat.BINARY;
      }

      // Verificar se os dados estão comprimidos
      const contentEncoding = metadata['content-encoding'] || '';
      const isCompressed = contentEncoding.includes('gzip');

      // Descomprimir dados se necessário
      let decompressedData: Buffer | string = data;

      if (isCompressed) {
        if (typeof data === 'string') {
          decompressedData = await ungzipAsync(Buffer.from(data, 'base64'));
        } else {
          decompressedData = await ungzipAsync(data);
        }

        // Converter Buffer para string se o formato for JSON
        if (format === SerializationFormat.JSON) {
          decompressedData = decompressedData.toString('utf-8');
        }
      }

      // Deserializar dados de acordo com o formato
      let deserializedData: T;

      switch (format) {
        case SerializationFormat.JSON:
          if (typeof decompressedData !== 'string') {
            decompressedData = decompressedData.toString('utf-8');
          }
          deserializedData = JSON.parse(decompressedData) as T;
          break;

        case SerializationFormat.AVRO:
          // Implementação de AVRO será adicionada posteriormente
          throw new Error('Formato AVRO não implementado');

        case SerializationFormat.PROTOBUF:
          // Implementação de PROTOBUF será adicionada posteriormente
          throw new Error('Formato PROTOBUF não implementado');

        case SerializationFormat.BINARY:
          // Implementação de BINARY será adicionada posteriormente
          throw new Error('Formato BINARY não implementado');

        default:
          throw new Error(`Formato de serialização não suportado: ${format}`);
      }

      // Obter informações do esquema
      const schemaName = metadata['schema-name'];
      const schemaVersion = metadata['schema-version']
        ? Number.parseInt(metadata['schema-version'], 10)
        : undefined;

      // Transformar dados se necessário
      if (schemaName && metadata['transform-to']) {
        const transformedData = await messageTransformationService.transformMessage(
          deserializedData as Record<string, unknown>,
          metadata['transform-to']
        );

        if (transformedData) {
          deserializedData = transformedData as T;
        }
      }

      return {
        data: deserializedData,
        format,
        schemaName,
        schemaVersion,
        metadata,
        wasCompressed: isCompressed,
      };
    } catch (error) {
      logger.error('Erro ao deserializar mensagem:', error);
      throw error;
    }
  },
};
