/**
 * Repositório para gerenciamento de permissões no sistema RBAC
 */

import { logger } from '@utils/logger';
import type { QueryResult } from 'pg';
import type { PermissionData } from './interfaces/rbacInterfaces';
import { pgHelper } from './pgHelper';

/**
 * Repositório para gerenciamento de permissões
 */
export const permissionRepository = {
  /**
   * Cria uma nova permissão
   * @param name - Nome da permissão
   * @param description - Descrição da permissão (opcional)
   * @param action - Ação da permissão (create, read, update, delete, etc.)
   * @returns Resultado da query
   */
  async create(name: string, description: string | null, action: string): Promise<QueryResult> {
    try {
      return await pgHelper.query(
        `INSERT INTO tab_permission (
          ulid_permission,
          name,
          description,
          action,
          active,
          created_at,
          updated_at
        ) VALUES ($1, $2, $3, $4, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
        RETURNING *`,
        [pgHelper.generateULID(), name, description, action]
      );
    } catch (error) {
      logger.error('Erro ao criar permissão:', error);
      throw error;
    }
  },

  /**
   * Busca permissões com filtros opcionais
   * @param ulid_permission - ID da permissão (opcional)
   * @param name - Nome da permissão (opcional)
   * @param action - Ação da permissão (opcional)
   * @param active - Se a permissão está ativa (opcional)
   * @returns Resultado da query
   */
  async read(
    ulid_permission?: string,
    name?: string,
    action?: string,
    active = true
  ): Promise<QueryResult> {
    try {
      let query = 'SELECT * FROM tab_permission WHERE 1=1';
      const params: any[] = [];
      let paramIndex = 1;

      if (ulid_permission) {
        query += ` AND ulid_permission = $${paramIndex++}`;
        params.push(ulid_permission);
      }

      if (name) {
        query += ` AND name ILIKE $${paramIndex++}`;
        params.push(`%${name}%`);
      }

      if (action) {
        query += ` AND action = $${paramIndex++}`;
        params.push(action);
      }

      if (active !== undefined) {
        query += ` AND active = $${paramIndex++}`;
        params.push(active);
      }

      query += ' ORDER BY name ASC';

      return await pgHelper.query(query, params);
    } catch (error) {
      logger.error('Erro ao buscar permissões:', error);
      throw error;
    }
  },

  /**
   * Atualiza uma permissão existente
   * @param ulid_permission - ID da permissão
   * @param name - Nome da permissão
   * @param description - Descrição da permissão (opcional)
   * @param action - Ação da permissão
   * @param active - Se a permissão está ativa
   * @returns Resultado da query
   */
  async update(
    ulid_permission: string,
    name: string,
    description: string | null,
    action: string,
    active = true
  ): Promise<QueryResult> {
    try {
      return await pgHelper.query(
        `UPDATE tab_permission
         SET name = $1,
             description = $2,
             action = $3,
             active = $4,
             updated_at = CURRENT_TIMESTAMP
         WHERE ulid_permission = $5
         RETURNING *`,
        [name, description, action, active, ulid_permission]
      );
    } catch (error) {
      logger.error('Erro ao atualizar permissão:', error);
      throw error;
    }
  },

  /**
   * Remove uma permissão (desativa)
   * @param ulid_permission - ID da permissão
   * @returns Resultado da query
   */
  async delete(ulid_permission: string): Promise<QueryResult> {
    try {
      return await pgHelper.query(
        `UPDATE tab_permission
         SET active = false,
             updated_at = CURRENT_TIMESTAMP
         WHERE ulid_permission = $1
         RETURNING *`,
        [ulid_permission]
      );
    } catch (error) {
      logger.error('Erro ao remover permissão:', error);
      throw error;
    }
  },

  /**
   * Associa uma permissão a um recurso
   * @param ulid_resource - ID do recurso
   * @param ulid_permission - ID da permissão
   * @returns Resultado da query
   */
  async associateWithResource(
    ulid_resource: string,
    ulid_permission: string
  ): Promise<QueryResult> {
    try {
      return await pgHelper.query(
        `INSERT INTO tab_resource_permission (
          ulid_resource_permission,
          ulid_resource,
          ulid_permission,
          created_at
        ) VALUES ($1, $2, $3, CURRENT_TIMESTAMP)
        ON CONFLICT (ulid_resource, ulid_permission) DO NOTHING
        RETURNING *`,
        [pgHelper.generateULID(), ulid_resource, ulid_permission]
      );
    } catch (error) {
      logger.error('Erro ao associar permissão ao recurso:', error);
      throw error;
    }
  },

  /**
   * Remove a associação de uma permissão a um recurso
   * @param ulid_resource - ID do recurso
   * @param ulid_permission - ID da permissão
   * @returns Resultado da query
   */
  async dissociateFromResource(
    ulid_resource: string,
    ulid_permission: string
  ): Promise<QueryResult> {
    try {
      return await pgHelper.query(
        `DELETE FROM tab_resource_permission
         WHERE ulid_resource = $1
         AND ulid_permission = $2
         RETURNING *`,
        [ulid_resource, ulid_permission]
      );
    } catch (error) {
      logger.error('Erro ao remover associação de permissão ao recurso:', error);
      throw error;
    }
  },

  /**
   * Busca permissões associadas a um recurso
   * @param ulid_resource - ID do recurso
   * @returns Resultado da query
   */
  async getResourcePermissions(ulid_resource: string): Promise<QueryResult> {
    try {
      return await pgHelper.query(
        `SELECT p.*
         FROM tab_permission p
         JOIN tab_resource_permission rp ON p.ulid_permission = rp.ulid_permission
         WHERE rp.ulid_resource = $1
         AND p.active = true
         ORDER BY p.name ASC`,
        [ulid_resource]
      );
    } catch (error) {
      logger.error('Erro ao buscar permissões do recurso:', error);
      throw error;
    }
  },

  /**
   * Busca permissões associadas a um papel
   * @param ulid_role - ID do papel
   * @returns Resultado da query
   */
  async getRolePermissions(ulid_role: string): Promise<QueryResult> {
    try {
      return await pgHelper.query(
        `SELECT p.*, r.name as resource_name
         FROM tab_permission p
         JOIN tab_resource_permission rp ON p.ulid_permission = rp.ulid_permission
         JOIN tab_resource r ON rp.ulid_resource = r.ulid_resource
         JOIN tab_role_resource_permission rrp ON rp.ulid_resource_permission = rrp.ulid_resource_permission
         WHERE rrp.ulid_role = $1
         AND p.active = true
         ORDER BY r.name ASC, p.name ASC`,
        [ulid_role]
      );
    } catch (error) {
      logger.error('Erro ao buscar permissões do papel:', error);
      throw error;
    }
  },

  /**
   * Busca permissões de um usuário (incluindo permissões herdadas de papéis)
   * @param ulid_user - ID do usuário
   * @returns Resultado da query
   */
  async getUserPermissions(ulid_user: string): Promise<QueryResult> {
    try {
      return await pgHelper.query(
        `WITH RECURSIVE role_hierarchy AS (
          -- Papéis diretamente atribuídos ao usuário
          SELECT r.ulid_role, r.name as role_name
          FROM tab_role r
          JOIN tab_user_role ur ON r.ulid_role = ur.ulid_role
          WHERE ur.ulid_user = $1
          AND r.active = true
          
          UNION
          
          -- Papéis herdados (ancestrais)
          SELECT r.ulid_role, r.name as role_name
          FROM tab_role r
          JOIN tab_role_hierarchy rh ON r.ulid_role = rh.ulid_parent_role
          JOIN role_hierarchy h ON rh.ulid_child_role = h.ulid_role
          WHERE r.active = true
        )
        SELECT DISTINCT p.*, r.name as resource_name
        FROM tab_permission p
        JOIN tab_resource_permission rp ON p.ulid_permission = rp.ulid_permission
        JOIN tab_resource r ON rp.ulid_resource = r.ulid_resource
        JOIN tab_role_resource_permission rrp ON rp.ulid_resource_permission = rrp.ulid_resource_permission
        JOIN role_hierarchy rh ON rrp.ulid_role = rh.ulid_role
        WHERE p.active = true
        ORDER BY r.name ASC, p.name ASC`,
        [ulid_user]
      );
    } catch (error) {
      logger.error('Erro ao buscar permissões do usuário:', error);
      throw error;
    }
  },

  /**
   * Verifica se um usuário tem uma permissão específica
   * @param ulid_user - ID do usuário
   * @param resourceName - Nome do recurso
   * @param action - Ação da permissão
   * @returns Resultado da query (true se tem permissão)
   */
  async checkUserPermission(
    ulid_user: string,
    resourceName: string,
    action: string
  ): Promise<boolean> {
    try {
      const result = await pgHelper.query(
        `WITH RECURSIVE role_hierarchy AS (
          -- Papéis diretamente atribuídos ao usuário
          SELECT r.ulid_role
          FROM tab_role r
          JOIN tab_user_role ur ON r.ulid_role = ur.ulid_role
          WHERE ur.ulid_user = $1
          AND r.active = true
          
          UNION
          
          -- Papéis herdados (ancestrais)
          SELECT r.ulid_role
          FROM tab_role r
          JOIN tab_role_hierarchy rh ON r.ulid_role = rh.ulid_parent_role
          JOIN role_hierarchy h ON rh.ulid_child_role = h.ulid_role
          WHERE r.active = true
        )
        SELECT EXISTS (
          SELECT 1
          FROM tab_permission p
          JOIN tab_resource_permission rp ON p.ulid_permission = rp.ulid_permission
          JOIN tab_resource r ON rp.ulid_resource = r.ulid_resource
          JOIN tab_role_resource_permission rrp ON rp.ulid_resource_permission = rrp.ulid_resource_permission
          JOIN role_hierarchy rh ON rrp.ulid_role = rh.ulid_role
          WHERE p.active = true
          AND r.name = $2
          AND p.action = $3
        ) as has_permission`,
        [ulid_user, resourceName, action]
      );

      return result.rows[0]?.has_permission || false;
    } catch (error) {
      logger.error('Erro ao verificar permissão do usuário:', error);
      return false;
    }
  },
};
