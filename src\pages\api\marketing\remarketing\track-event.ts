/**
 * Remarketing Track Event API
 *
 * Endpoint para rastrear eventos de remarketing no lado do servidor.
 * Parte da implementação da tarefa 8.9.4 - Marketing digital
 */

import type { APIRoute } from 'astro';
import { RemarketingService } from '../../../../domain/services/RemarketingService';
import { DefaultRemarketingService } from '../../../../infrastructure/services/DefaultRemarketingService';

// Inicializar serviço de remarketing
const remarketingService: RemarketingService = new DefaultRemarketingService();

// Configurar serviço com pixels do ambiente
await remarketingService.initialize([
  {
    id: process.env.FACEBOOK_PIXEL_ID || '',
    type: 'facebook',
    enabled: !!process.env.FACEBOOK_PIXEL_ID,
  },
  {
    id: process.env.GOOGLE_TAG_MANAGER_ID || '',
    type: 'google',
    enabled: !!process.env.GOOGLE_TAG_MANAGER_ID,
  },
  {
    id: process.env.TIKTOK_PIXEL_ID || '',
    type: 'tiktok',
    enabled: !!process.env.TIKTOK_PIXEL_ID,
  },
]);

export const POST: APIRoute = async ({ request }) => {
  try {
    // Obter dados do corpo da requisição
    const body = await request.json();

    // Validar dados
    if (!body.name) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Parâmetro obrigatório: name',
        }),
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    // Rastrear evento
    const success = await remarketingService.trackEvent({
      name: body.name,
      params: body.params,
      pixelTypes: body.pixelTypes,
    });

    if (!success) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Erro ao rastrear evento de remarketing',
        }),
        {
          status: 500,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    return new Response(
      JSON.stringify({
        success: true,
        message: 'Evento rastreado com sucesso',
      }),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  } catch (error) {
    console.error('Erro ao processar requisição de evento de remarketing:', error);

    return new Response(
      JSON.stringify({
        success: false,
        error: 'Erro ao processar requisição',
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  }
};
