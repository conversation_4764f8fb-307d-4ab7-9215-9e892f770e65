/**
 * Camada de compatibilidade para o repositório de usuários
 *
 * Este arquivo mantém a compatibilidade com o código existente que
 * utiliza a estrutura antiga do repositório de usuários.
 *
 * @deprecated Use a nova estrutura de repositórios em src/repositories
 */

import type { QueryResult } from 'pg';
import { pgHelper } from '../../repository/pgHelper';
import { repositories } from '../index';

/**
 * Cria um novo usuário
 *
 * @param ulid_user_type Tipo de usuário
 * @param ulid_school_type Tipo de escola
 * @param email Email do usuário
 * @param password Senha do usuário
 * @param is_teacher Se é professor
 * @param name Nome do usuário
 * @param state Estado
 * @param county Município
 * @returns Resultado da consulta
 */
async function create(
  ulid_user_type: string,
  ulid_school_type: string,
  email: string,
  password: string,
  is_teacher: boolean,
  name: string,
  state: string,
  county: string
): Promise<QueryResult> {
  try {
    // Usar a nova implementação
    const user = await repositories.userRepository.create({
      userType: ulid_user_type,
      schoolType: ulid_school_type,
      email,
      password,
      isTeacher: is_teacher,
      name,
      state,
      county,
    });

    // Converter para o formato esperado pelo código antigo
    return {
      rows: [user],
      rowCount: 1,
      command: 'INSERT',
      oid: 0,
      fields: [],
    };
  } catch (error) {
    console.error('Error in compatibility layer (userRepository.create):', error);

    // Fallback para a implementação antiga em caso de erro
    return pgHelper.query(
      `INSERT INTO tab_user (
        ulid_user_type,
        ulid_school_type,
        email,
        password,
        is_teacher,
        name,
        state,
        county,
        active,
        created_at,
        updated_at
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
      RETURNING *`,
      [ulid_user_type, ulid_school_type, email, password, is_teacher, name, state, county]
    );
  }
}

/**
 * Busca usuários
 *
 * @param ulid_user ID do usuário (opcional)
 * @param ulid_user_type Tipo de usuário (opcional)
 * @param ulid_school_type Tipo de escola (opcional)
 * @param email Email do usuário (opcional)
 * @param is_teacher Se é professor (opcional)
 * @param state Estado (opcional)
 * @param county Município (opcional)
 * @param active Status de ativação (opcional)
 * @returns Resultado da consulta
 */
async function read(
  ulid_user?: string,
  ulid_user_type?: string,
  ulid_school_type?: string,
  email?: string,
  is_teacher?: boolean,
  state?: string,
  county?: string,
  active?: boolean
): Promise<QueryResult> {
  try {
    // Usar a nova implementação
    const users = await repositories.userRepository.findAll({
      id: ulid_user,
      userType: ulid_user_type,
      schoolType: ulid_school_type,
      email,
      isTeacher: is_teacher,
      state,
      county,
      active,
    });

    // Converter para o formato esperado pelo código antigo
    return {
      rows: users,
      rowCount: users.length,
      command: 'SELECT',
      oid: 0,
      fields: [],
    };
  } catch (error) {
    console.error('Error in compatibility layer (userRepository.read):', error);

    // Fallback para a implementação antiga em caso de erro
    const conditions = [];
    const values = [];
    let paramCount = 1;

    if (ulid_user !== undefined) {
      conditions.push(`ulid_user = $${paramCount++}`);
      values.push(ulid_user);
    }
    if (ulid_user_type !== undefined) {
      conditions.push(`ulid_user_type = $${paramCount++}`);
      values.push(ulid_user_type);
    }
    if (ulid_school_type !== undefined) {
      conditions.push(`ulid_school_type = $${paramCount++}`);
      values.push(ulid_school_type);
    }
    if (email !== undefined) {
      conditions.push(`email = $${paramCount++}`);
      values.push(email);
    }
    if (is_teacher !== undefined) {
      conditions.push(`is_teacher = $${paramCount++}`);
      values.push(is_teacher.toString());
    }
    if (state !== undefined) {
      conditions.push(`state = $${paramCount++}`);
      values.push(state);
    }
    if (county !== undefined) {
      conditions.push(`county = $${paramCount++}`);
      values.push(county);
    }
    if (active !== undefined) {
      conditions.push(`active = $${paramCount++}`);
      values.push(active.toString());
    }

    const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';

    return pgHelper.query(`SELECT * FROM tab_user ${whereClause}`, values);
  }
}

/**
 * Atualiza um usuário
 *
 * @param ulid_user ID do usuário
 * @param ulid_user_type Tipo de usuário (opcional)
 * @param ulid_school_type Tipo de escola (opcional)
 * @param email Email do usuário (opcional)
 * @param password Senha do usuário (opcional)
 * @param is_teacher Se é professor (opcional)
 * @param name Nome do usuário (opcional)
 * @param state Estado (opcional)
 * @param county Município (opcional)
 * @param active Status de ativação (opcional)
 * @returns Resultado da consulta
 */
async function update(
  ulid_user: string,
  ulid_user_type?: string,
  ulid_school_type?: string,
  email?: string,
  password?: string,
  is_teacher?: boolean,
  name?: string,
  state?: string,
  county?: string,
  active?: boolean
): Promise<QueryResult> {
  try {
    // Usar a nova implementação
    const user = await repositories.userRepository.update(ulid_user, {
      userType: ulid_user_type,
      schoolType: ulid_school_type,
      email,
      password,
      isTeacher: is_teacher,
      name,
      state,
      county,
      active,
    });

    // Converter para o formato esperado pelo código antigo
    return {
      rows: [user],
      rowCount: 1,
      command: 'UPDATE',
      oid: 0,
      fields: [],
    };
  } catch (error) {
    console.error('Error in compatibility layer (userRepository.update):', error);

    // Fallback para a implementação antiga em caso de erro
    const updates = [];
    const values = [ulid_user];
    let paramCount = 2;

    if (ulid_user_type !== undefined) {
      updates.push(`ulid_user_type = $${paramCount++}`);
      values.push(ulid_user_type);
    }
    if (ulid_school_type !== undefined) {
      updates.push(`ulid_school_type = $${paramCount++}`);
      values.push(ulid_school_type);
    }
    if (email !== undefined) {
      updates.push(`email = $${paramCount++}`);
      values.push(email);
    }
    if (password !== undefined) {
      updates.push(`password = $${paramCount++}`);
      values.push(password);
    }
    if (is_teacher !== undefined) {
      updates.push(`is_teacher = $${paramCount++}`);
      values.push(is_teacher.toString());
    }
    if (name !== undefined) {
      updates.push(`name = $${paramCount++}`);
      values.push(name);
    }
    if (state !== undefined) {
      updates.push(`state = $${paramCount++}`);
      values.push(state);
    }
    if (county !== undefined) {
      updates.push(`county = $${paramCount++}`);
      values.push(county);
    }
    if (active !== undefined) {
      updates.push(`active = $${paramCount++}`);
      values.push(active.toString());
    }

    updates.push('updated_at = CURRENT_TIMESTAMP');

    return pgHelper.query(
      `UPDATE tab_user 
       SET ${updates.join(', ')} 
       WHERE ulid_user = $1 
       RETURNING *`,
      values
    );
  }
}

/**
 * Remove um usuário
 *
 * @param ulid_user ID do usuário
 * @returns Resultado da consulta
 */
async function deleteByUlid(ulid_user: string): Promise<QueryResult> {
  try {
    // Usar a nova implementação
    const success = await repositories.userRepository.delete(ulid_user);

    // Converter para o formato esperado pelo código antigo
    return {
      rows: success ? [{ ulid_user }] : [],
      rowCount: success ? 1 : 0,
      command: 'DELETE',
      oid: 0,
      fields: [],
    };
  } catch (error) {
    console.error('Error in compatibility layer (userRepository.deleteByUlid):', error);

    // Fallback para a implementação antiga em caso de erro
    return pgHelper.query(
      `DELETE FROM tab_user 
        WHERE ulid_user = $1 
       RETURNING *`,
      [ulid_user]
    );
  }
}

/**
 * Inativa um usuário
 *
 * @param ulid_user ID do usuário
 * @returns Resultado da consulta
 */
async function inactivate(ulid_user: string): Promise<QueryResult> {
  try {
    // Usar a nova implementação
    const user = await repositories.userRepository.update(ulid_user, {
      active: false,
    });

    // Converter para o formato esperado pelo código antigo
    return {
      rows: [user],
      rowCount: 1,
      command: 'UPDATE',
      oid: 0,
      fields: [],
    };
  } catch (error) {
    console.error('Error in compatibility layer (userRepository.inactivate):', error);

    // Fallback para a implementação antiga em caso de erro
    return pgHelper.query(
      `UPDATE tab_user 
          SET active = false, 
              updated_at = CURRENT_TIMESTAMP 
        WHERE ulid_user = $1 
       RETURNING *`,
      [ulid_user]
    );
  }
}

export const userRepository = {
  create,
  read,
  update,
  deleteByUlid,
  inactivate,
};
