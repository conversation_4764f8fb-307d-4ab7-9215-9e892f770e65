// src/services/paymentEventConsumer.ts
import { consumer } from '@config/kafka';
import { queryHelper } from '@db/queryHelper';
import { logger } from '@utils/logger';
import { emailService } from './emailService';
import { PaymentStatus } from './paymentService';

/**
 * Interface para evento de pagamento
 */
interface PaymentEvent {
  paymentId: string;
  status: PaymentStatus;
  webhookData?: any;
  timestamp: string;
}

/**
 * Interface para evento de reembolso
 */
interface RefundEvent {
  paymentId: string;
  status: PaymentStatus;
  refundData: any;
  timestamp: string;
}

/**
 * Serviço de consumidor de eventos de pagamento
 */
export const paymentEventConsumer = {
  /**
   * Inicializa o consumidor de eventos de pagamento
   */
  async init(): Promise<void> {
    try {
      await consumer.connect();

      // Inscrever-se nos tópicos
      await consumer.subscribe({
        topics: ['payment-events', 'refund-events'],
        fromBeginning: true,
      });

      await consumer.run({
        eachMessage: async ({ topic, partition, message }) => {
          try {
            if (!message.value) {
              return;
            }

            const event = JSON.parse(message.value.toString());

            logger.info(`Processando evento de ${topic}:`, {
              topic,
              partition,
              offset: message.offset,
              key: message.key?.toString(),
              paymentId: event.paymentId,
              status: event.status,
            });

            // Processar evento de acordo com o tópico
            if (topic === 'payment-events') {
              await this.processPaymentEvent(event);
            } else if (topic === 'refund-events') {
              await this.processRefundEvent(event);
            }
          } catch (error) {
            logger.error('Erro ao processar mensagem:', error);
          }
        },
      });

      logger.info('Consumidor de eventos de pagamento inicializado');
    } catch (error) {
      logger.error('Erro ao inicializar consumidor de eventos de pagamento:', error);
      throw error;
    }
  },

  /**
   * Processa um evento de pagamento
   * @param event - Evento de pagamento
   */
  async processPaymentEvent(event: PaymentEvent): Promise<void> {
    const { paymentId, status } = event;

    // Buscar dados do pagamento
    const payment = await queryHelper.queryOne(
      `SELECT p.*, pt.type as payment_type, o.ulid_order, o.total as order_total,
              u.name as user_name, u.email as user_email, u.ulid_user
       FROM tab_payment p
       JOIN tab_payment_type pt ON p.ulid_payment_type = pt.ulid_payment_type
       LEFT JOIN tab_order o ON p.ulid_order = o.ulid_order
       LEFT JOIN tab_user u ON o.ulid_user = u.ulid_user
       WHERE p.ulid_payment = $1`,
      [paymentId]
    );

    if (!payment) {
      logger.error(`Pagamento não encontrado: ${paymentId}`);
      return;
    }

    // Verificar se o status já foi processado
    if (payment.cod_status === status) {
      logger.info(`Status já processado para pagamento ${paymentId}: ${status}`);
      return;
    }

    // Atualizar status do pagamento
    await queryHelper.query(
      'UPDATE tab_payment SET cod_status = $1, updated_at = NOW() WHERE ulid_payment = $2',
      [status, paymentId]
    );

    // Registrar evento de pagamento
    await this.logPaymentEvent(payment.ulid_payment, status, event.webhookData);

    // Processar de acordo com o status
    if (status === PaymentStatus.APPROVED) {
      await this.handleApprovedPayment(payment);
    } else if (status === PaymentStatus.REJECTED || status === PaymentStatus.CANCELLED) {
      await this.handleRejectedPayment(payment, status);
    } else if (status === PaymentStatus.REFUNDED) {
      await this.handleRefundedPayment(payment);
    }
  },

  /**
   * Processa um evento de reembolso
   * @param event - Evento de reembolso
   */
  async processRefundEvent(event: RefundEvent): Promise<void> {
    const { paymentId, status, refundData } = event;

    // Buscar dados do pagamento
    const payment = await queryHelper.queryOne(
      `SELECT p.*, pt.type as payment_type, o.ulid_order, o.total as order_total,
              u.name as user_name, u.email as user_email, u.ulid_user
       FROM tab_payment p
       JOIN tab_payment_type pt ON p.ulid_payment_type = pt.ulid_payment_type
       LEFT JOIN tab_order o ON p.ulid_order = o.ulid_order
       LEFT JOIN tab_user u ON o.ulid_user = u.ulid_user
       WHERE p.ulid_payment = $1`,
      [paymentId]
    );

    if (!payment) {
      logger.error(`Pagamento não encontrado para reembolso: ${paymentId}`);
      return;
    }

    // Atualizar status do pagamento para reembolsado
    await queryHelper.query(
      'UPDATE tab_payment SET cod_status = $1, updated_at = NOW() WHERE ulid_payment = $2',
      [PaymentStatus.REFUNDED, paymentId]
    );

    // Registrar evento de reembolso
    await this.logRefundEvent(payment.ulid_payment, refundData);

    // Atualizar status do pedido se existir
    if (payment.ulid_order) {
      await queryHelper.query(
        'UPDATE tab_order SET cod_status = 5, updated_at = NOW() WHERE ulid_order = $1',
        [payment.ulid_order]
      );
    }

    // Enviar email de confirmação de reembolso
    if (payment.user_email) {
      await emailService.sendRefundConfirmationEmail(payment);
    }
  },

  /**
   * Processa um pagamento aprovado
   * @param payment - Dados do pagamento
   */
  async handleApprovedPayment(payment: any): Promise<void> {
    // Atualizar status do pedido se existir
    if (payment.ulid_order) {
      await queryHelper.query(
        'UPDATE tab_order SET cod_status = 2, updated_at = NOW() WHERE ulid_order = $1',
        [payment.ulid_order]
      );
    }

    // Enviar email de confirmação
    if (payment.user_email) {
      await emailService.sendPaymentConfirmationEmail(payment);
    }

    // Verificar se há produtos digitais para liberar acesso
    if (payment.ulid_order) {
      await this.processDigitalProducts(payment.ulid_order, payment.ulid_user);
    }
  },

  /**
   * Processa um pagamento rejeitado ou cancelado
   * @param payment - Dados do pagamento
   * @param status - Status do pagamento
   */
  async handleRejectedPayment(payment: any, status: PaymentStatus): Promise<void> {
    // Atualizar status do pedido se existir
    if (payment.ulid_order) {
      await queryHelper.query(
        'UPDATE tab_order SET cod_status = 4, updated_at = NOW() WHERE ulid_order = $1',
        [payment.ulid_order]
      );
    }

    // Enviar email de falha no pagamento
    if (payment.user_email) {
      await emailService.sendPaymentFailureEmail(payment, status);
    }
  },

  /**
   * Processa um pagamento reembolsado
   * @param payment - Dados do pagamento
   */
  async handleRefundedPayment(payment: any): Promise<void> {
    // Atualizar status do pedido se existir
    if (payment.ulid_order) {
      await queryHelper.query(
        'UPDATE tab_order SET cod_status = 5, updated_at = NOW() WHERE ulid_order = $1',
        [payment.ulid_order]
      );
    }

    // Enviar email de confirmação de reembolso
    if (payment.user_email) {
      await emailService.sendRefundConfirmationEmail(payment);
    }

    // Revogar acesso a produtos digitais se necessário
    if (payment.ulid_order) {
      await this.revokeDigitalProductsAccess(payment.ulid_order, payment.ulid_user);
    }
  },

  /**
   * Processa produtos digitais após pagamento aprovado
   * @param orderId - ID do pedido
   * @param userId - ID do usuário
   */
  async processDigitalProducts(orderId: string, userId: string): Promise<void> {
    try {
      // Buscar produtos digitais do pedido
      const digitalProducts = await queryHelper.queryAll(
        `SELECT p.*
         FROM tab_order_item oi
         JOIN tab_product p ON oi.ulid_product = p.ulid_product
         WHERE oi.ulid_order = $1 AND p.is_digital = true`,
        [orderId]
      );

      if (digitalProducts.length === 0) {
        return;
      }

      logger.info(
        `Processando ${digitalProducts.length} produtos digitais para o pedido ${orderId}`
      );

      // Conceder acesso a cada produto digital
      for (const product of digitalProducts) {
        // Verificar se já existe acesso
        const existingAccess = await queryHelper.queryOne(
          `SELECT * FROM tab_user_product
           WHERE ulid_user = $1 AND ulid_product = $2`,
          [userId, product.ulid_product]
        );

        if (existingAccess) {
          // Atualizar acesso existente
          await queryHelper.query(
            `UPDATE tab_user_product
             SET active = true, updated_at = NOW()
             WHERE ulid_user = $1 AND ulid_product = $2`,
            [userId, product.ulid_product]
          );
        } else {
          // Criar novo acesso
          await queryHelper.query(
            `INSERT INTO tab_user_product (
              ulid_user_product, ulid_user, ulid_product,
              active, created_at, updated_at
            ) VALUES (
              $1, $2, $3, true, NOW(), NOW()
            )`,
            [crypto.randomUUID(), userId, product.ulid_product]
          );
        }
      }
    } catch (error) {
      logger.error('Erro ao processar produtos digitais:', error);
    }
  },

  /**
   * Revoga acesso a produtos digitais após reembolso
   * @param orderId - ID do pedido
   * @param userId - ID do usuário
   */
  async revokeDigitalProductsAccess(orderId: string, userId: string): Promise<void> {
    try {
      // Buscar produtos digitais do pedido
      const digitalProducts = await queryHelper.queryAll(
        `SELECT p.*
         FROM tab_order_item oi
         JOIN tab_product p ON oi.ulid_product = p.ulid_product
         WHERE oi.ulid_order = $1 AND p.is_digital = true`,
        [orderId]
      );

      if (digitalProducts.length === 0) {
        return;
      }

      logger.info(
        `Revogando acesso a ${digitalProducts.length} produtos digitais para o pedido ${orderId}`
      );

      // Revogar acesso a cada produto digital
      for (const product of digitalProducts) {
        await queryHelper.query(
          `UPDATE tab_user_product
           SET active = false, updated_at = NOW()
           WHERE ulid_user = $1 AND ulid_product = $2`,
          [userId, product.ulid_product]
        );
      }
    } catch (error) {
      logger.error('Erro ao revogar acesso a produtos digitais:', error);
    }
  },

  /**
   * Registra um evento de pagamento no log
   * @param paymentId - ID do pagamento
   * @param status - Status do pagamento
   * @param data - Dados do evento
   */
  async logPaymentEvent(paymentId: string, status: PaymentStatus, data: any): Promise<void> {
    try {
      await queryHelper.query(
        `INSERT INTO tab_payment_log (
          ulid_payment_log, ulid_payment, cod_status,
          event_data, created_at
        ) VALUES (
          $1, $2, $3, $4, NOW()
        )`,
        [crypto.randomUUID(), paymentId, status, JSON.stringify(data)]
      );
    } catch (error) {
      logger.error('Erro ao registrar evento de pagamento:', error);
    }
  },

  /**
   * Registra um evento de reembolso no log
   * @param paymentId - ID do pagamento
   * @param data - Dados do reembolso
   */
  async logRefundEvent(paymentId: string, data: any): Promise<void> {
    try {
      await queryHelper.query(
        `INSERT INTO tab_refund_log (
          ulid_refund_log, ulid_payment,
          refund_id, refund_data, created_at
        ) VALUES (
          $1, $2, $3, $4, NOW()
        )`,
        [crypto.randomUUID(), paymentId, data.id, JSON.stringify(data)]
      );
    } catch (error) {
      logger.error('Erro ao registrar evento de reembolso:', error);
    }
  },
};

/**
 * Inicializa o consumidor de eventos de pagamento
 */
export async function initPaymentEventConsumer(): Promise<void> {
  await paymentEventConsumer.init();
}
