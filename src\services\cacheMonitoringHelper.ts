/**
 * Helper para Monitoramento de Cache
 *
 * Este módulo implementa monitoramento para operações de cache,
 * coletando métricas de performance e uso para o serviço de monitoramento.
 */

import { performance } from 'node:perf_hooks';
import { monitoringEvents } from '@services/applicationMonitoringService';
import { logger } from '@utils/logger';

/**
 * Interface para dados de operação de cache
 */
interface CacheOperationData {
  id: string;
  key: string;
  operation: 'get' | 'set' | 'del' | 'exists' | 'expire' | 'keys' | 'other';
  startTime: number;
}

/**
 * Mapa de operações de cache em andamento
 */
const activeOperations = new Map<string, CacheOperationData>();

/**
 * Gera um ID único para a operação
 * @returns ID único
 */
function generateOperationId(): string {
  return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
}

/**
 * Monitora uma operação de cache
 * @param operation Tipo de operação
 * @param key Chave de cache
 * @param fn Função a ser monitorada
 * @returns Resultado da função
 */
export async function monitorCacheOperation<T>(
  operation: 'get' | 'set' | 'del' | 'exists' | 'expire' | 'keys' | 'other',
  key: string,
  fn: () => Promise<T>
): Promise<T> {
  // Gerar ID único para a operação
  const operationId = generateOperationId();

  // Registrar dados da operação
  const operationData: CacheOperationData = {
    id: operationId,
    key,
    operation,
    startTime: performance.now(),
  };

  // Armazenar operação ativa
  activeOperations.set(operationId, operationData);

  // Emitir evento de início de operação
  monitoringEvents.emit('cache_operation_start', {
    id: operationId,
    key,
    operation,
  });

  try {
    // Executar operação
    const result = await fn();

    // Obter dados da operação
    const data = activeOperations.get(operationId);

    if (data) {
      // Calcular duração
      const duration = performance.now() - data.startTime;

      // Emitir evento de fim de operação
      monitoringEvents.emit('cache_operation_end', {
        id: operationId,
        key: data.key,
        operation: data.operation,
        duration,
        result: operation === 'get' ? result !== null : undefined,
      });

      // Emitir eventos específicos para get
      if (operation === 'get') {
        if (result !== null && result !== undefined) {
          monitoringEvents.emit('cache_hit', {
            key: data.key,
          });
        } else {
          monitoringEvents.emit('cache_miss', {
            key: data.key,
          });
        }
      }

      // Remover operação do mapa de ativas
      activeOperations.delete(operationId);
    }

    return result;
  } catch (error) {
    // Obter dados da operação
    const data = activeOperations.get(operationId);

    if (data) {
      // Calcular duração
      const duration = performance.now() - data.startTime;

      // Emitir evento de erro de operação
      monitoringEvents.emit('cache_error', {
        id: operationId,
        key: data.key,
        operation: data.operation,
        error: error.message,
        duration,
      });

      // Registrar erro no log
      logger.error(`Erro em operação de cache (${data.operation}): ${error.message}`, {
        key: data.key,
        operation: data.operation,
        error: error.message,
        stack: error.stack,
      });

      // Remover operação do mapa de ativas
      activeOperations.delete(operationId);
    }

    throw error;
  }
}

/**
 * Wrapper para monitorar serviço de cache
 * @param cacheService Serviço de cache original
 * @returns Serviço de cache com monitoramento
 */
export function monitorCacheService<T extends Record<string, any>>(cacheService: T): T {
  const monitoredService = { ...cacheService };

  // Monitorar método get
  if (typeof cacheService.get === 'function') {
    monitoredService.get = async <V>(key: string): Promise<V | null> =>
      monitorCacheOperation('get', key, () => cacheService.get<V>(key));
  }

  // Monitorar método set
  if (typeof cacheService.set === 'function') {
    monitoredService.set = async <V>(key: string, value: V, ttl?: number): Promise<boolean> =>
      monitorCacheOperation('set', key, () => cacheService.set(key, value, ttl));
  }

  // Monitorar método del
  if (typeof cacheService.del === 'function') {
    monitoredService.del = async (...keys: string[]): Promise<number> =>
      monitorCacheOperation('del', keys.join(','), () => cacheService.del(...keys));
  }

  // Monitorar método exists
  if (typeof cacheService.exists === 'function') {
    monitoredService.exists = async (key: string): Promise<number> =>
      monitorCacheOperation('exists', key, () => cacheService.exists(key));
  }

  // Monitorar método expire
  if (typeof cacheService.expire === 'function') {
    monitoredService.expire = async (key: string, ttl: number): Promise<boolean> =>
      monitorCacheOperation('expire', key, () => cacheService.expire(key, ttl));
  }

  // Monitorar método keys
  if (typeof cacheService.keys === 'function') {
    monitoredService.keys = async (pattern: string): Promise<string[]> =>
      monitorCacheOperation('keys', pattern, () => cacheService.keys(pattern));
  }

  return monitoredService;
}

/**
 * Obtém estatísticas de operações de cache ativas
 * @returns Estatísticas de operações
 */
export function getCacheOperationStats(): Record<string, any> {
  const stats = {
    activeOperations: activeOperations.size,
    operationsByType: {} as Record<string, number>,
  };

  // Agrupar operações por tipo
  for (const [_, data] of activeOperations.entries()) {
    if (!stats.operationsByType[data.operation]) {
      stats.operationsByType[data.operation] = 0;
    }
    stats.operationsByType[data.operation]++;
  }

  return stats;
}
