/**
 * Implementação do serviço de token usando JWT
 * 
 * Este serviço implementa a interface TokenService usando JWT.
 * Parte da implementação da tarefa 9.1.1 - Testes unitários
 */

import jwt from 'jsonwebtoken';
import { TokenService, TokenPayload, TokenResponse } from './TokenService';

/**
 * Implementação do serviço de token usando JWT
 */
export class JwtTokenService implements TokenService {
  // Lista de tokens revogados (blacklist)
  private tokenBlacklist: Set<string> = new Set();
  
  /**
   * Cria uma nova instância do serviço de token JWT
   * @param secretKey Chave secreta para assinatura dos tokens de acesso
   * @param refreshSecretKey Chave secreta para assinatura dos refresh tokens
   */
  constructor(
    private readonly secretKey: string,
    private readonly refreshSecretKey: string
  ) {}

  /**
   * Gera um novo token de acesso e refresh token
   * @param payload Dados a serem incluídos no token
   * @returns Token de acesso, refresh token e tempo de expiração
   */
  async generateToken(payload: TokenPayload): Promise<TokenResponse> {
    // Gerar token de acesso (expira em 1 hora)
    const accessToken = jwt.sign(
      { ...payload, timestamp: Date.now() },
      this.secretKey,
      { expiresIn: '1h' }
    );
    
    // Gerar refresh token (expira em 7 dias)
    const refreshToken = jwt.sign(
      { userId: payload.userId, timestamp: Date.now() },
      this.refreshSecretKey,
      { expiresIn: '7d' }
    );
    
    return {
      accessToken,
      refreshToken,
      expiresIn: 3600 // 1 hora em segundos
    };
  }

  /**
   * Verifica se um token é válido
   * @param token Token a ser verificado
   * @returns Payload do token se for válido
   * @throws Error se o token for inválido
   */
  async verifyToken(token: string): Promise<TokenPayload> {
    try {
      // Verificar se o token está na blacklist
      if (this.isTokenRevoked(token)) {
        throw new Error('Token revogado');
      }
      
      // Verificar e decodificar o token
      const payload = jwt.verify(token, this.secretKey) as TokenPayload;
      return payload;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Atualiza um token de acesso usando um refresh token
   * @param refreshToken Refresh token
   * @param payload Dados a serem incluídos no novo token
   * @returns Novo token de acesso, refresh token e tempo de expiração
   * @throws Error se o refresh token for inválido
   */
  async refreshToken(refreshToken: string, payload: TokenPayload): Promise<TokenResponse> {
    try {
      // Verificar se o refresh token está na blacklist
      if (this.isTokenRevoked(refreshToken)) {
        throw new Error('Refresh token revogado');
      }
      
      // Verificar e decodificar o refresh token
      const decodedRefreshToken = jwt.verify(refreshToken, this.refreshSecretKey) as { userId: string };
      
      // Verificar se o userId do refresh token corresponde ao userId do payload
      if (decodedRefreshToken.userId !== payload.userId) {
        throw new Error('Refresh token inválido');
      }
      
      // Adicionar o refresh token antigo à blacklist
      await this.addToBlacklist(refreshToken);
      
      // Gerar novos tokens
      return this.generateToken(payload);
    } catch (error) {
      throw error;
    }
  }

  /**
   * Revoga um refresh token
   * @param refreshToken Refresh token a ser revogado
   * @returns true se o token foi revogado com sucesso
   */
  async revokeToken(refreshToken: string): Promise<boolean> {
    return this.addToBlacklist(refreshToken);
  }

  /**
   * Verifica se um token está revogado
   * @param token Token a ser verificado
   * @returns true se o token estiver revogado, false caso contrário
   */
  private isTokenRevoked(token: string): boolean {
    return this.tokenBlacklist.has(token);
  }

  /**
   * Adiciona um token à blacklist
   * @param token Token a ser adicionado à blacklist
   * @returns true se o token foi adicionado com sucesso
   */
  private async addToBlacklist(token: string): Promise<boolean> {
    this.tokenBlacklist.add(token);
    return true;
  }
}
