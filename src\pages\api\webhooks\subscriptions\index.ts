import { isAuthenticated } from '@middleware/authMiddleware';
import { WebhookEvent, WebhookStatus } from '@models/WebhookSubscription';
import { webhookRepository } from '@repositories/webhookRepository';
import { generateRandomString } from '@utils/crypto';
import { validateSchema } from '@utils/validation';
// src/pages/api/webhooks/subscriptions/index.ts
import type { APIRoute } from 'astro';
import { z } from 'zod';

// Schema para validação de criação de assinatura
const createSubscriptionSchema = z.object({
  name: z.string().min(3).max(100),
  url: z.string().url(),
  events: z.array(z.nativeEnum(WebhookEvent)).min(1),
  secretKey: z.string().min(16).max(64).optional(),
  description: z.string().max(500).optional(),
  headers: z.record(z.string()).optional(),
});

// Schema para validação de filtros
const filtersSchema = z.object({
  status: z.nativeEnum(WebhookStatus).optional(),
  event: z.nativeEnum(WebhookEvent).optional(),
});

/**
 * Endpoint para listar assinaturas de webhook
 */
export const GET: APIRoute = async ({ request, cookies, url }) => {
  try {
    // Verificar autenticação
    const authResult = await isAuthenticated({ request, cookies } as any);

    if (!authResult.isAuthenticated) {
      return new Response(JSON.stringify({ error: 'Não autorizado' }), {
        status: 401,
        headers: {
          'Content-Type': 'application/json',
        },
      });
    }

    // Extrair parâmetros de consulta
    const status = url.searchParams.get('status') as WebhookStatus | null;
    const event = url.searchParams.get('event') as WebhookEvent | null;

    // Validar filtros
    const filters = {};

    if (status) {
      Object.assign(filters, { status });
    }

    if (event) {
      Object.assign(filters, { event });
    }

    // Validar filtros
    const validationResult = validateSchema(filtersSchema, filters);

    if (!validationResult.success) {
      return new Response(
        JSON.stringify({
          error: 'Parâmetros de consulta inválidos',
          details: validationResult.errors,
        }),
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    // Obter assinaturas
    const subscriptions = await webhookRepository.listSubscriptions(filters);

    // Remover chaves secretas da resposta
    const sanitizedSubscriptions = subscriptions.map((subscription) => {
      const { secretKey, ...rest } = subscription;
      return rest;
    });

    // Retornar resposta
    return new Response(JSON.stringify({ subscriptions: sanitizedSubscriptions }), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  } catch (error) {
    console.error('Erro ao listar assinaturas de webhook:', error);

    return new Response(
      JSON.stringify({
        error: error instanceof Error ? error.message : 'Erro interno',
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  }
};

/**
 * Endpoint para criar assinatura de webhook
 */
export const POST: APIRoute = async ({ request, cookies }) => {
  try {
    // Verificar autenticação
    const authResult = await isAuthenticated({ request, cookies } as any);

    if (!authResult.isAuthenticated) {
      return new Response(JSON.stringify({ error: 'Não autorizado' }), {
        status: 401,
        headers: {
          'Content-Type': 'application/json',
        },
      });
    }

    // Obter corpo da requisição
    const body = await request.json();

    // Validar dados
    const validationResult = validateSchema(createSubscriptionSchema, body);

    if (!validationResult.success) {
      return new Response(
        JSON.stringify({
          error: 'Dados inválidos',
          details: validationResult.errors,
        }),
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    // Gerar chave secreta se não fornecida
    const secretKey = body.secretKey || generateRandomString(32);

    // Criar assinatura
    const subscription = await webhookRepository.createSubscription({
      name: body.name,
      url: body.url,
      events: body.events,
      secretKey,
      description: body.description,
      headers: body.headers,
      createdBy: authResult.user?.id,
    });

    // Retornar resposta
    return new Response(
      JSON.stringify({
        message: 'Assinatura de webhook criada com sucesso',
        subscription: {
          ...subscription,
          // Incluir a chave secreta apenas na resposta de criação
          secretKey: subscription.secretKey,
        },
      }),
      {
        status: 201,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  } catch (error) {
    console.error('Erro ao criar assinatura de webhook:', error);

    return new Response(
      JSON.stringify({
        error: error instanceof Error ? error.message : 'Erro interno',
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  }
};
