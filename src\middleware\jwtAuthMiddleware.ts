/**
 * Middleware para autenticação JWT
 * Verifica tokens JWT em requisições e gerencia sessões
 *
 * Este middleware implementa:
 * - Verificação de tokens JWT
 * - Renovação automática de tokens expirados
 * - Rotação de refresh tokens
 * - Verificação de blacklist
 * - Proteção contra roubo de tokens
 */

import { defineMiddleware } from 'astro:middleware';
import { authJwtService } from '@services/authJwtService';
import { jwtService } from '@services/jwtService';
import { logger } from '@utils/logger';
import type { MiddlewareHandler } from 'astro';

// Rotas que não precisam de autenticação
const publicRoutes = [
  '/signin',
  '/signup',
  '/api/auth',
  '/reset-password',
  '/api/public',
  '/assets',
  '/favicon.ico',
  '/robots.txt',
];

// Middleware de autenticação JWT
export const onRequest = defineMiddleware(async (context, next) => {
  const { request, url, cookies, locals } = context;

  // Extrair informações do cliente para segurança e auditoria
  const ipAddress =
    request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown';
  const userAgent = request.headers.get('user-agent') || 'unknown';

  // Criar objeto de informações do dispositivo
  const deviceInfo = {
    ipAddress,
    userAgent,
    // Gerar um ID de dispositivo baseado em fingerprinting
    deviceId: await generateDeviceId(ipAddress, userAgent),
  };

  // Verificar se a rota é pública
  const isPublicRoute = publicRoutes.some((route) => url.pathname.startsWith(route));

  // Se for uma rota pública, continuar sem verificar autenticação
  if (isPublicRoute) {
    return next();
  }

  // Obter token do cabeçalho Authorization ou do cookie
  let token = request.headers.get('Authorization')?.replace('Bearer ', '');

  if (!token) {
    token = cookies.get('access_token')?.value;
  }

  // Se não houver token, verificar se há refresh token
  if (!token) {
    const refreshToken = cookies.get('refresh_token')?.value;

    if (refreshToken) {
      try {
        // Tentar renovar o token com rotação
        const result = await authJwtService.refreshTokenWithRotation(refreshToken, deviceInfo);

        if (result) {
          // Definir novo access token no cookie
          cookies.set('access_token', result.accessToken, {
            path: '/',
            httpOnly: true,
            secure: process.env.NODE_ENV === 'production',
            maxAge: 60 * 15, // 15 minutos
          });

          // Se houver um novo refresh token, atualizar também
          if (result.refreshToken) {
            cookies.set('refresh_token', result.refreshToken, {
              path: '/',
              httpOnly: true,
              secure: process.env.NODE_ENV === 'production',
              maxAge: 60 * 60 * 24 * 7, // 7 dias
            });

            logger.debug('Refresh token rotacionado durante autenticação');
          }

          token = result.accessToken;
        }
      } catch (error) {
        logger.error('Erro ao renovar token:', error);
      }
    }
  }

  // Se ainda não houver token, redirecionar para login
  if (!token) {
    return context.redirect('/signin?reason=no_token');
  }

  // Verificar se o token está na blacklist
  const isRevoked = await jwtService.isTokenRevoked(token);
  if (isRevoked) {
    // Limpar cookies
    cookies.delete('access_token');
    cookies.delete('refresh_token');

    logger.warn('Tentativa de uso de token revogado', {
      ipAddress,
      userAgent,
      path: url.pathname,
    });

    return context.redirect('/signin?reason=revoked_token');
  }

  // Verificar token e obter dados do usuário
  const user = await authJwtService.verifyToken(token, true);

  // Se o token for inválido, redirecionar para login
  if (!user) {
    // Limpar cookies
    cookies.delete('access_token');
    cookies.delete('refresh_token');

    return context.redirect('/signin?reason=invalid_token');
  }

  // Verificar se o token foi emitido para este dispositivo (proteção contra roubo de tokens)
  if (user.device && user.device !== deviceInfo.deviceId) {
    // Revogar o token potencialmente comprometido
    await jwtService.revokeToken(token, 'Possível roubo de token - dispositivo diferente');

    // Limpar cookies
    cookies.delete('access_token');
    cookies.delete('refresh_token');

    logger.warn('Possível roubo de token detectado - dispositivo diferente', {
      userId: user.ulid_user,
      expectedDevice: user.device,
      actualDevice: deviceInfo.deviceId,
      ipAddress,
      userAgent,
    });

    return context.redirect('/signin?reason=security_violation');
  }

  // Disponibilizar o usuário para todos os componentes
  locals.user = user;

  // Registrar acesso bem-sucedido para auditoria
  logger.debug('Acesso autenticado', {
    userId: user.ulid_user,
    path: url.pathname,
    method: request.method,
    ipAddress,
    userAgent,
  });

  return next();
}) satisfies MiddlewareHandler;

/**
 * Gera um ID de dispositivo baseado em fingerprinting
 * @param ipAddress - Endereço IP
 * @param userAgent - User-Agent
 * @returns ID do dispositivo
 */
async function generateDeviceId(ipAddress: string, userAgent: string): Promise<string> {
  // Importar crypto de forma dinâmica para evitar problemas no lado do cliente
  const { createHash } = await import('node:crypto');

  // Criar um hash baseado no IP e User-Agent
  // Em uma implementação mais robusta, poderia incluir mais fatores
  const fingerprint = `${ipAddress}|${userAgent}`;
  return createHash('sha256').update(fingerprint).digest('hex');
}
