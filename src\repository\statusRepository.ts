import type { QueryResult } from 'pg';
import { pgHelper } from './pgHelper';

async function create(status: string): Promise<QueryResult> {
  return await pgHelper.query(
    `INSERT INTO tab_status (
      status
    ) VALUES ($1)
    RETURNING *`,
    [status]
  );
}

async function read(ulid_status?: string, status?: string): Promise<QueryResult> {
  return await pgHelper.query(
    `SELECT * 
       FROM tab_status 
      WHERE TRUE 
        ${ulid_status ? 'AND ulid_status = $1' : ''}
        ${status ? 'AND status = $2' : ''}
   ORDER BY created_at DESC`,
    [ulid_status, status]
  );
}

async function update(ulid_status: string, status: string): Promise<QueryResult> {
  return await pgHelper.query(
    `UPDATE tab_status 
        SET status = $1 
      WHERE ulid_status = $2
     RETURNING *`,
    [status, ulid_status]
  );
}

async function deleteByUlid(ulid_status: string): Promise<QueryResult> {
  return await pgHelper.query(
    `DELETE FROM tab_status 
      WHERE ulid_status = $1
     RETURNING *`,
    [ulid_status]
  );
}

export const statusRepository = {
  create,
  read,
  update,
  deleteByUlid,
};
