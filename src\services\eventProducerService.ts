/**
 * Serviço de produção de eventos para o Kafka
 *
 * Este serviço fornece uma interface unificada para produção de eventos
 * para diferentes domínios de negócio, com suporte a retry e dead letter queue.
 */

import { getTopicName, producer, sendMessage } from '@config/kafka';
import { logger } from '@utils/logger';
import { kafkaDLQService } from './kafka-dlq.service';
import { kafkaLoggingService } from './kafka-logging.service';
import { kafkaProducerRetryService } from './kafka-producer-retry.service';
import {
  type SerializationOptions,
  messageSerializationService,
} from './messageSerializationService';

/**
 * Interface para evento base
 */
export interface BaseEvent {
  id: string;
  timestamp: string;
  version: string;
  [key: string]: unknown;
}

/**
 * Interface para evento de pagamento
 */
export interface PaymentEvent extends BaseEvent {
  paymentId: string;
  orderId?: string;
  userId?: string;
  value: number;
  paymentType: string;
  status: string;
  externalId?: string;
}

/**
 * Interface para evento de pedido
 */
export interface OrderEvent extends BaseEvent {
  orderId: string;
  userId: string;
  status: string;
  total: number;
  items?: Array<{
    productId: string;
    quantity: number;
    price: number;
  }>;
}

/**
 * Interface para evento de usuário
 */
export interface UserEvent extends BaseEvent {
  userId: string;
  email?: string;
  action: string;
}

/**
 * Interface para evento de produto
 */
export interface ProductEvent extends BaseEvent {
  productId: string;
  name?: string;
  price?: number;
  stock?: number;
  action: string;
}

/**
 * Interface para evento de notificação
 */
export interface NotificationEvent extends BaseEvent {
  notificationId: string;
  type: string;
  recipientId?: string;
  recipientEmail?: string;
  subject?: string;
  content?: string;
  status: string;
}

/**
 * Serviço de produção de eventos
 */
export const eventProducerService = {
  /**
   * Inicializa o produtor de eventos
   */
  async init(): Promise<void> {
    try {
      if (producer.isConnected()) {
        return;
      }

      await producer.connect();

      // Inicializar serviço de DLQ
      await kafkaDLQService.initConsumer();
      await kafkaDLQService.startProcessing();

      logger.info('Produtor de eventos Kafka conectado');
      kafkaLoggingService.info(
        'eventProducer',
        'Produtor de eventos Kafka inicializado com suporte a retry e DLQ'
      );
    } catch (error) {
      logger.error('Erro ao inicializar produtor de eventos Kafka:', error);
      kafkaLoggingService.error(
        'eventProducer',
        'Erro ao inicializar produtor de eventos Kafka',
        error
      );
      throw error;
    }
  },

  /**
   * Finaliza o produtor de eventos
   */
  async shutdown(): Promise<void> {
    try {
      // Parar serviço de DLQ
      await kafkaDLQService.shutdown();

      if (!producer.isConnected()) {
        return;
      }

      await producer.disconnect();
      logger.info('Produtor de eventos Kafka desconectado');
      kafkaLoggingService.info('eventProducer', 'Produtor de eventos Kafka finalizado');
    } catch (error) {
      logger.error('Erro ao finalizar produtor de eventos Kafka:', error);
      kafkaLoggingService.error(
        'eventProducer',
        'Erro ao finalizar produtor de eventos Kafka',
        error
      );
    }
  },

  /**
   * Cria um evento base
   * @param id - ID do evento
   * @param data - Dados adicionais do evento
   * @returns Evento base
   */
  createBaseEvent(id: string, data: Record<string, unknown> = {}): BaseEvent {
    return {
      id,
      timestamp: new Date().toISOString(),
      version: '1.0',
      ...data,
    };
  },

  /**
   * Envia um evento para um tópico específico
   * @param topic - Nome do tópico
   * @param event - Evento a ser enviado
   * @param key - Chave do evento
   * @param options - Opções de serialização
   */
  async sendEvent(
    topic: string,
    event: BaseEvent,
    key: string,
    options: SerializationOptions = {}
  ): Promise<void> {
    try {
      await this.init();

      // Extrair informações do tópico para determinar o esquema
      const topicParts = topic.split('.');
      const domain = topicParts[0];
      const entity = topicParts[1];
      const eventType = topicParts[2];

      // Definir nome do esquema se não fornecido
      if (!options.schemaName) {
        options.schemaName = `${domain}.${entity}`;
      }

      // Serializar evento
      const serialized = await messageSerializationService.serialize(event, {
        ...options,
        metadata: {
          'event-id': event.id,
          'event-timestamp': event.timestamp,
          'event-version': event.version,
          'event-type': eventType,
          domain: domain,
          entity: entity,
          ...(options.metadata || {}),
        },
      });

      // Preparar cabeçalhos
      const headers: Record<string, Buffer> = {};

      // Converter metadados para formato de cabeçalhos Kafka
      for (const [headerKey, value] of Object.entries(serialized.metadata)) {
        headers[headerKey] = Buffer.from(value);
      }

      // Adicionar informações de serialização
      headers['content-type'] = Buffer.from(`application/${serialized.format}`);

      if (serialized.compressed) {
        headers['content-encoding'] = Buffer.from('gzip');
      }

      // Preparar mensagem
      const message = {
        key,
        value: typeof serialized.data === 'string' ? Buffer.from(serialized.data) : serialized.data,
        headers,
      };

      // Enviar mensagem com retry
      const result = await kafkaProducerRetryService.sendWithRetry(topic, message, producer);

      if (result.success) {
        logger.debug(`Evento enviado para tópico ${topic}`, {
          eventId: event.id,
          key,
          format: serialized.format,
          compressed: serialized.compressed,
          originalSize: serialized.originalSize,
          serializedSize: serialized.serializedSize,
          attempts: result.attempts,
          totalTimeMs: result.totalTimeMs,
        });
      } else {
        const errorMessage = `Falha ao enviar evento para tópico ${topic} após ${result.attempts} tentativas`;

        kafkaLoggingService.error('eventProducer', errorMessage, {
          eventId: event.id,
          key,
          error: result.error?.message,
          sentToDLQ: result.sentToDLQ,
          attempts: result.attempts,
          totalTimeMs: result.totalTimeMs,
        });

        throw new Error(errorMessage);
      }
    } catch (error) {
      logger.error(`Erro ao enviar evento para tópico ${topic}:`, error);
      throw error;
    }
  },

  /**
   * Envia um evento para um domínio específico
   * @param domain - Domínio do evento
   * @param entity - Entidade do evento
   * @param eventType - Tipo do evento
   * @param data - Dados do evento
   * @param key - Chave do evento
   */
  async sendDomainEvent(
    domain: string,
    entity: string,
    eventType: string,
    data: Record<string, unknown>,
    key: string
  ): Promise<void> {
    try {
      await this.init();

      const event = this.createBaseEvent(
        `${domain}-${entity}-${Date.now()}-${Math.random().toString(36).substring(2, 10)}`,
        data
      );

      await sendMessage(domain, entity, eventType, key, event);

      logger.debug(`Evento de domínio enviado: ${domain}.${entity}.${eventType}`, {
        eventId: event.id,
        key,
      });
    } catch (error) {
      logger.error(`Erro ao enviar evento de domínio ${domain}.${entity}.${eventType}:`, error);
      throw error;
    }
  },

  /**
   * Envia um evento de pagamento
   * @param eventType - Tipo do evento (created, updated, failed, etc.)
   * @param data - Dados do evento
   */
  async sendPaymentEvent(eventType: string, data: PaymentEvent): Promise<void> {
    const key = data.paymentId;
    await this.sendDomainEvent('payment', 'transaction', eventType, data, key);
  },

  /**
   * Envia um evento de reembolso
   * @param eventType - Tipo do evento (requested, processed, etc.)
   * @param data - Dados do evento
   */
  async sendRefundEvent(
    eventType: string,
    data: PaymentEvent & { refundId: string }
  ): Promise<void> {
    const key = data.refundId;
    await this.sendDomainEvent('payment', 'refund', eventType, data, key);
  },

  /**
   * Envia um evento de pedido
   * @param eventType - Tipo do evento (created, updated, cancelled, etc.)
   * @param data - Dados do evento
   */
  async sendOrderEvent(eventType: string, data: OrderEvent): Promise<void> {
    const key = data.orderId;
    await this.sendDomainEvent(
      'order',
      eventType === 'status.changed' ? 'status' : '',
      eventType,
      data,
      key
    );
  },

  /**
   * Envia um evento de usuário
   * @param eventType - Tipo do evento (registered, profile.updated, etc.)
   * @param data - Dados do evento
   */
  async sendUserEvent(eventType: string, data: UserEvent): Promise<void> {
    const key = data.userId;

    // Determinar entidade com base no tipo de evento
    let entity = '';
    let finalEventType = eventType;

    if (eventType.includes('.')) {
      const parts = eventType.split('.');
      entity = parts[0];
      finalEventType = parts[1];
    }

    await this.sendDomainEvent('user', entity, finalEventType, data, key);
  },

  /**
   * Envia um evento de produto
   * @param eventType - Tipo do evento (created, updated, inventory.updated, etc.)
   * @param data - Dados do evento
   */
  async sendProductEvent(eventType: string, data: ProductEvent): Promise<void> {
    const key = data.productId;

    // Determinar entidade com base no tipo de evento
    let entity = '';
    let finalEventType = eventType;

    if (eventType.includes('.')) {
      const parts = eventType.split('.');
      entity = parts[0];
      finalEventType = parts[1];
    }

    await this.sendDomainEvent('product', entity, finalEventType, data, key);
  },

  /**
   * Envia um evento de notificação
   * @param eventType - Tipo do evento (email.queued, email.sent, etc.)
   * @param data - Dados do evento
   */
  async sendNotificationEvent(eventType: string, data: NotificationEvent): Promise<void> {
    const key = data.notificationId;

    // Determinar entidade com base no tipo de evento
    let entity = '';
    let finalEventType = eventType;

    if (eventType.includes('.')) {
      const parts = eventType.split('.');
      entity = parts[0];
      finalEventType = parts[1];
    }

    await this.sendDomainEvent('notification', entity, finalEventType, data, key);
  },
};
