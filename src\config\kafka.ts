import pkg from 'kafkajs';
const { Kafka, Producer, Consumer } = pkg;
import { kafkaConfig } from './kafka.config';
import { logger } from '@utils/logger';
import {
  kafkaPartitioningConfig,
  PartitioningStrategy,
  type PartitionKey,
} from './kafka-partitioning.config';
import { kafkaProducerRetryConfig } from './kafka-retry.config';

// Criar instância do Kafka com as configurações
const kafka = new Kafka(kafkaConfig.connection);

// Criar produtor com configurações
export const producer = kafka.producer(kafkaConfig.producer);

// Criar consumidor com configurações
export const consumer = kafka.consumer(kafkaConfig.consumer);

// Função para obter nome do tópico a partir da configuração
export function getTopicName(
  domain: string,
  entity: string,
  event: string
): string {
  try {
    // @ts-ignore - <PERSON><PERSON><PERSON> propriedades aninhadas dinamicamente
    return kafkaConfig.topics[domain][entity][event];
  } catch (error) {
    logger.error(
      `Tópico não encontrado para ${domain}.${entity}.${event}`,
      error
    );
    throw new Error(`Tópico não encontrado para ${domain}.${entity}.${event}`);
  }
}

// Interface para mensagem Kafka para particionamento
export interface PartitionerMessage {
  key: string;
  value: string | Record<string, unknown>;
  headers?: Record<string, string>;
}

// Tipo para função de particionamento
export type PartitionerFunction = (
  key: string,
  numPartitions: number,
  message?: PartitionerMessage
) => number;

// Função para obter estratégia de particionamento
export function getPartitioner(topicName: string): PartitionerFunction {
  // Obter configuração de particionamento para o tópico
  const partitioningConfig =
    kafkaPartitioningConfig.getTopicPartitioningConfig(topicName);

  // Se não houver configuração específica, usar estratégia padrão
  if (!partitioningConfig) {
    return defaultPartitioner;
  }

  // Selecionar estratégia com base na configuração
  switch (partitioningConfig.strategy) {
    case PartitioningStrategy.RANDOM:
      return randomPartitioner;

    case PartitioningStrategy.ROUND_ROBIN:
      return roundRobinPartitioner;

    case PartitioningStrategy.KEY_BASED: {
      return (
        key: string,
        numPartitions: number,
        message?: PartitionerMessage
      ) =>
        keyBasedPartitioner(
          key,
          numPartitions,
          message,
          partitioningConfig.keyField
        );
    }

    case PartitioningStrategy.CUSTOM: {
      // Obter particionador personalizado pelo nome
      const customPartitionerName = partitioningConfig.customPartitioner || '';
      const customPartitioner = customPartitioners[customPartitionerName];
      return customPartitioner || defaultPartitioner;
    }

    default:
      return defaultPartitioner;
  }
}

// Contador para particionamento round-robin
let roundRobinCounter = 0;

// Particionador padrão baseado em hash da chave
export function defaultPartitioner(key: string, numPartitions: number): number {
  const crypto = require('node:crypto');
  const hash = crypto.createHash('md5').update(key).digest('hex');

  return Number.parseInt(hash.substring(0, 8), 16) % numPartitions;
}

// Particionador aleatório
export function randomPartitioner(_key: string, numPartitions: number): number {
  return Math.floor(Math.random() * numPartitions);
}

// Particionador round-robin
export function roundRobinPartitioner(
  _key: string,
  numPartitions: number
): number {
  const partition = roundRobinCounter % numPartitions;
  roundRobinCounter = (roundRobinCounter + 1) % numPartitions;
  return partition;
}

// Particionador baseado em campo específico da mensagem
export function keyBasedPartitioner(
  key: string,
  numPartitions: number,
  message?: PartitionerMessage,
  keyField?: PartitionKey
): number {
  // Se não houver mensagem ou campo de chave, usar particionador padrão
  if (!message || !message.value || !keyField) {
    return defaultPartitioner(key, numPartitions);
  }

  try {
    // Tentar extrair o valor do campo de chave da mensagem
    const value =
      typeof message.value === 'string'
        ? JSON.parse(message.value)
        : message.value;

    // Se o campo existir, usar como chave para particionamento
    if (value[keyField]) {
      const fieldValue = value[keyField].toString();
      return defaultPartitioner(fieldValue, numPartitions);
    }
  } catch (error) {
    logger.warn(
      `Erro ao extrair campo ${keyField} para particionamento:`,
      error
    );
  }

  // Fallback para o particionador padrão
  return defaultPartitioner(key, numPartitions);
}

// Registro de particionadores personalizados
const customPartitioners: Record<string, PartitionerFunction> = {
  // Exemplo de particionador personalizado
  defaultCustomPartitioner: defaultPartitioner,
};

// Interface para mensagem Kafka
export interface KafkaMessage {
  key: string;
  value: Record<string, unknown>;
  headers?: Record<string, string>;
}

// Função para enviar mensagem com configurações adequadas
export async function sendMessage(
  domain: string,
  entity: string,
  event: string,
  key: string,
  value: Record<string, unknown>
): Promise<void> {
  try {
    const topicName = getTopicName(domain, entity, event);

    // Preparar mensagem
    const message = {
      key,
      value: JSON.stringify(value),
      headers: {
        'message-type': `${domain}.${entity}.${event}`,
        timestamp: Date.now().toString(),
        'content-type': 'application/json',
      },
    };

    // Importar serviço de retry dinamicamente para evitar dependência circular
    const { kafkaProducerRetryService } = await import(
      '../services/kafka-producer-retry.service'
    );

    // Enviar mensagem com retry
    const result = await kafkaProducerRetryService.sendWithRetry(
      topicName,
      message,
      producer
    );

    if (result.success) {
      logger.debug(`Mensagem enviada para ${topicName}`, {
        key,
        attempts: result.attempts,
        totalTimeMs: result.totalTimeMs,
      });
    } else {
      const errorMessage = `Falha ao enviar mensagem para ${topicName} após ${result.attempts} tentativas`;

      // Importar serviço de logging dinamicamente para evitar dependência circular
      const { kafkaLoggingService } = await import(
        '../services/kafka-logging.service'
      );

      kafkaLoggingService.error('kafka', errorMessage, {
        domain,
        entity,
        event,
        key,
        error: result.error?.message,
        sentToDLQ: result.sentToDLQ,
        attempts: result.attempts,
        totalTimeMs: result.totalTimeMs,
      });

      throw new Error(errorMessage);
    }
  } catch (error) {
    logger.error(
      `Erro ao enviar mensagem para ${domain}.${entity}.${event}`,
      error
    );
    throw error;
  }
}

export default kafka;
