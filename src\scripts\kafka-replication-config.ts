/**
 * Script para configuração de replicação e particionamento do Kafka
 *
 * Este script configura os parâmetros de replicação e particionamento
 * para o cluster Kafka, garantindo alta disponibilidade e escalabilidade.
 */

import kafka from '../config/kafka';
import { kafkaReplicationConfig } from '../config/kafka-replication.config';
import { logger } from '../utils/logger';

/**
 * Configura replicação e particionamento para tópicos Kafka
 */
async function configureReplicationAndPartitioning(): Promise<void> {
  const admin = kafka.admin();

  try {
    logger.info('Conectando ao cluster Kafka para configurar replicação e particionamento...');
    await admin.connect();
    logger.info('Conexão estabelecida com sucesso');

    // Listar tópicos existentes
    logger.info('Obtendo lista de tópicos existentes...');
    const existingTopics = await admin.listTopics();
    logger.info(`Tópicos existentes: ${existingTopics.length}`);

    // Verificar configurações dos tópicos existentes
    logger.info('Verificando configurações de replicação e particionamento...');

    for (const topicName of existingTopics) {
      // Obter configurações atuais do tópico
      const topicMetadata = await admin.fetchTopicMetadata({
        topics: [topicName],
      });

      if (topicMetadata.topics.length === 0) {
        logger.warn(`Não foi possível obter metadados para o tópico ${topicName}`);
        continue;
      }

      const topic = topicMetadata.topics[0];

      // Verificar configuração de replicação
      const configResource = await admin.describeConfigs({
        resources: [
          {
            type: 2, // TOPIC
            name: topicName,
          },
        ],
      });

      if (configResource.resources.length === 0) {
        logger.warn(`Não foi possível obter configurações para o tópico ${topicName}`);
        continue;
      }

      const config = configResource.resources[0];

      // Obter configuração desejada para o tópico
      const desiredConfig = kafkaReplicationConfig.getTopicConfig(topicName);

      if (!desiredConfig) {
        logger.info(`Tópico ${topicName} não tem configuração específica definida, usando padrões`);
        continue;
      }

      // Verificar fator de replicação
      const partitions = topic.partitions;
      const currentReplicationFactor = partitions.length > 0 ? partitions[0].replicas.length : 0;

      if (currentReplicationFactor !== desiredConfig.replicationFactor) {
        logger.warn(
          `Tópico ${topicName} tem fator de replicação ${currentReplicationFactor}, ` +
            `mas deveria ter ${desiredConfig.replicationFactor}`
        );

        // Registrar necessidade de alteração do fator de replicação
        // Nota: Alterar o fator de replicação requer recriar o tópico
        logger.info(
          `Para alterar o fator de replicação, o tópico ${topicName} precisa ser recriado`
        );
      }

      // Verificar número de partições
      if (partitions.length !== desiredConfig.numPartitions) {
        logger.warn(
          `Tópico ${topicName} tem ${partitions.length} partições, ` +
            `mas deveria ter ${desiredConfig.numPartitions}`
        );

        // Se o número atual de partições for menor que o desejado, podemos aumentar
        if (partitions.length < desiredConfig.numPartitions) {
          logger.info(`Aumentando número de partições para o tópico ${topicName}`);

          try {
            await admin.createPartitions({
              topicPartitions: [
                {
                  topic: topicName,
                  count: desiredConfig.numPartitions,
                },
              ],
            });

            logger.info(
              `Número de partições para o tópico ${topicName} aumentado para ${desiredConfig.numPartitions}`
            );
          } catch (error) {
            logger.error(`Erro ao aumentar partições para o tópico ${topicName}:`, error);
          }
        } else {
          // Reduzir o número de partições não é suportado pelo Kafka
          logger.warn(
            `Reduzir o número de partições não é suportado. O tópico ${topicName} precisa ser recriado para ter menos partições.`
          );
        }
      }

      // Verificar configurações de replicação
      const minInsyncReplicasConfig = config.configEntries.find(
        (entry) => entry.name === 'min.insync.replicas'
      );

      const currentMinInsyncReplicas = minInsyncReplicasConfig
        ? Number.parseInt(minInsyncReplicasConfig.value, 10)
        : 1;

      if (currentMinInsyncReplicas !== desiredConfig.minInsyncReplicas) {
        logger.warn(
          `Tópico ${topicName} tem min.insync.replicas=${currentMinInsyncReplicas}, ` +
            `mas deveria ter ${desiredConfig.minInsyncReplicas}`
        );

        // Atualizar configuração de min.insync.replicas
        try {
          await admin.alterConfigs({
            resources: [
              {
                type: 2, // TOPIC
                name: topicName,
                configEntries: [
                  {
                    name: 'min.insync.replicas',
                    value: desiredConfig.minInsyncReplicas.toString(),
                  },
                ],
              },
            ],
          });

          logger.info(
            `Configuração min.insync.replicas para o tópico ${topicName} ` +
              `atualizada para ${desiredConfig.minInsyncReplicas}`
          );
        } catch (error) {
          logger.error(`Erro ao atualizar min.insync.replicas para o tópico ${topicName}:`, error);
        }
      }

      // Verificar outras configurações importantes
      const cleanupPolicyConfig = config.configEntries.find(
        (entry) => entry.name === 'cleanup.policy'
      );

      if (
        cleanupPolicyConfig?.value !== desiredConfig.cleanupPolicy &&
        desiredConfig.cleanupPolicy
      ) {
        logger.warn(
          `Tópico ${topicName} tem cleanup.policy=${cleanupPolicyConfig?.value}, ` +
            `mas deveria ter ${desiredConfig.cleanupPolicy}`
        );

        // Atualizar configuração de cleanup.policy
        try {
          await admin.alterConfigs({
            resources: [
              {
                type: 2, // TOPIC
                name: topicName,
                configEntries: [
                  {
                    name: 'cleanup.policy',
                    value: desiredConfig.cleanupPolicy,
                  },
                ],
              },
            ],
          });

          logger.info(
            `Configuração cleanup.policy para o tópico ${topicName} ` +
              `atualizada para ${desiredConfig.cleanupPolicy}`
          );
        } catch (error) {
          logger.error(`Erro ao atualizar cleanup.policy para o tópico ${topicName}:`, error);
        }
      }
    }

    logger.info('Verificação de configurações de replicação e particionamento concluída');

    // Verificar balanceamento de partições entre brokers
    logger.info('Verificando balanceamento de partições entre brokers...');

    const clusterInfo = await admin.describeCluster();
    const brokers = clusterInfo.brokers;

    logger.info(`Cluster tem ${brokers.length} brokers ativos`);

    if (brokers.length > 1) {
      // Obter distribuição de partições por broker
      const brokerPartitionCounts: Record<string, number> = {};

      for (const topicName of existingTopics) {
        const topicMetadata = await admin.fetchTopicMetadata({
          topics: [topicName],
        });

        if (topicMetadata.topics.length === 0) continue;

        const topic = topicMetadata.topics[0];

        for (const partition of topic.partitions) {
          const leaderId = partition.leader;

          if (!brokerPartitionCounts[leaderId]) {
            brokerPartitionCounts[leaderId] = 0;
          }

          brokerPartitionCounts[leaderId]++;
        }
      }

      // Calcular estatísticas de balanceamento
      const partitionCounts = Object.values(brokerPartitionCounts);
      const totalPartitions = partitionCounts.reduce((sum, count) => sum + count, 0);
      const avgPartitionsPerBroker = totalPartitions / brokers.length;
      const maxPartitions = Math.max(...partitionCounts);
      const minPartitions = Math.min(...partitionCounts);
      const imbalancePercentage = ((maxPartitions - minPartitions) / avgPartitionsPerBroker) * 100;

      logger.info('Estatísticas de balanceamento de partições:');
      logger.info(`- Total de partições: ${totalPartitions}`);
      logger.info(`- Média de partições por broker: ${avgPartitionsPerBroker.toFixed(2)}`);
      logger.info(`- Partições no broker mais carregado: ${maxPartitions}`);
      logger.info(`- Partições no broker menos carregado: ${minPartitions}`);
      logger.info(`- Percentual de desbalanceamento: ${imbalancePercentage.toFixed(2)}%`);

      if (imbalancePercentage > 20) {
        logger.warn(
          `Desbalanceamento de partições (${imbalancePercentage.toFixed(2)}%) excede o limite recomendado de 20%. Considere rebalancear o cluster.`
        );
      } else {
        logger.info('Balanceamento de partições está dentro dos limites aceitáveis');
      }
    }
  } catch (error) {
    logger.error('Erro ao configurar replicação e particionamento:', error);
    throw error;
  } finally {
    await admin.disconnect();
    logger.info('Desconectado do cluster Kafka');
  }
}

// Executar script
if (require.main === module) {
  configureReplicationAndPartitioning()
    .then(() => {
      logger.info('Configuração de replicação e particionamento concluída com sucesso');
      process.exit(0);
    })
    .catch((error) => {
      logger.error('Falha na configuração de replicação e particionamento:', error);
      process.exit(1);
    });
}

export { configureReplicationAndPartitioning };
