/**
 * API de Conteúdo
 *
 * Endpoint para gerenciamento de conteúdo.
 * Parte da implementação da tarefa 8.8.3 - Gestão de conteúdo
 */

import type { APIRoute } from 'astro';
import { ContentStatus, ContentType } from '../../../../domain/entities/Content';
import { ContentCategoryRepository } from '../../../../domain/repositories/ContentCategoryRepository';
import { ContentRepository } from '../../../../domain/repositories/ContentRepository';
import { ContentTagRepository } from '../../../../domain/repositories/ContentTagRepository';
import { SlugService } from '../../../../domain/services/SlugService';
import { TokenService } from '../../../../domain/services/TokenService';
import { CreateContentUseCase } from '../../../../domain/usecases/content/CreateContentUseCase';
import { PostgresContentCategoryRepository } from '../../../../infrastructure/database/repositories/PostgresContentCategoryRepository';
import { PostgresContentRepository } from '../../../../infrastructure/database/repositories/PostgresContentRepository';
import { PostgresContentTagRepository } from '../../../../infrastructure/database/repositories/PostgresContentTagRepository';
import { DefaultSlugService } from '../../../../infrastructure/services/DefaultSlugService';
import { JwtTokenService } from '../../../../infrastructure/services/JwtTokenService';

// Inicializar repositórios
const contentRepository: ContentRepository = new PostgresContentRepository();
const categoryRepository: ContentCategoryRepository = new PostgresContentCategoryRepository();
const tagRepository: ContentTagRepository = new PostgresContentTagRepository();

// Inicializar serviços
const slugService: SlugService = new DefaultSlugService();
const tokenService: TokenService = new JwtTokenService(
  process.env.JWT_SECRET || 'default-secret-key',
  process.env.PASSWORD_RESET_SECRET || 'password-reset-secret-key',
  process.env.EMAIL_VERIFICATION_SECRET || 'email-verification-secret-key'
);

// Inicializar casos de uso
const createContentUseCase = new CreateContentUseCase(
  contentRepository,
  categoryRepository,
  tagRepository,
  slugService
);

// Verificar autenticação e permissões
const checkAuth = (request: Request): { userId: string; isAuthorized: boolean } | null => {
  const authHeader = request.headers.get('Authorization');

  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null;
  }

  const token = authHeader.substring(7);
  const payload = tokenService.verifyToken(token);

  if (!payload) {
    return null;
  }

  // Verificar permissões
  const isAuthorized = payload.role === 'admin' || payload.permissions?.includes('content:read');

  return {
    userId: payload.id,
    isAuthorized,
  };
};

export const GET: APIRoute = async ({ request }) => {
  try {
    // Verificar autenticação
    const auth = checkAuth(request);

    if (!auth) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Não autorizado. Faça login para continuar.',
        }),
        {
          status: 401,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    if (!auth.isAuthorized) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Você não tem permissão para acessar este recurso.',
        }),
        {
          status: 403,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    // Obter parâmetros de consulta
    const url = new URL(request.url);
    const type = url.searchParams.get('type');
    const status = url.searchParams.get('status');
    const categoryId = url.searchParams.get('categoryId');
    const tagId = url.searchParams.get('tagId');
    const search = url.searchParams.get('search');
    const page = url.searchParams.get('page');
    const limit = url.searchParams.get('limit');
    const sortField = url.searchParams.get('sortField');
    const sortDirection = url.searchParams.get('sortDirection');

    // Construir filtro
    const filter: any = {};

    if (type) {
      filter.types = [type as ContentType];
    }

    if (status) {
      filter.statuses = [status as ContentStatus];
    }

    if (categoryId) {
      filter.categoryIds = [categoryId];
    }

    if (tagId) {
      filter.tagIds = [tagId];
    }

    if (search) {
      filter.search = search;
    }

    // Construir ordenação
    const sort =
      sortField && sortDirection
        ? {
            field: sortField as 'title' | 'createdAt' | 'updatedAt' | 'publishedAt',
            direction: sortDirection as 'asc' | 'desc',
          }
        : undefined;

    // Construir paginação
    const pagination =
      page && limit
        ? {
            page: Number.parseInt(page),
            limit: Number.parseInt(limit),
          }
        : undefined;

    // Buscar conteúdos
    const result = await contentRepository.find(filter, sort, pagination);

    return new Response(
      JSON.stringify({
        success: true,
        data: result,
      }),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  } catch (error) {
    console.error('Erro ao processar busca de conteúdo:', error);

    return new Response(
      JSON.stringify({
        success: false,
        error: 'Ocorreu um erro ao processar a busca de conteúdo. Por favor, tente novamente.',
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  }
};

export const POST: APIRoute = async ({ request }) => {
  try {
    // Verificar autenticação
    const auth = checkAuth(request);

    if (!auth) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Não autorizado. Faça login para continuar.',
        }),
        {
          status: 401,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    // Verificar permissão específica para criar conteúdo
    const payload = tokenService.verifyToken(request.headers.get('Authorization')!.substring(7));

    if (!payload || !(payload.role === 'admin' || payload.permissions?.includes('content:write'))) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Você não tem permissão para criar conteúdo.',
        }),
        {
          status: 403,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    // Obter dados do corpo da requisição
    const body = await request.json();

    // Validar dados
    if (!body.title || !body.body || !body.type) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Dados incompletos. Verifique os campos obrigatórios.',
        }),
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    // Executar caso de uso
    const result = await createContentUseCase.execute({
      title: body.title,
      body: body.body,
      type: body.type as ContentType,
      status: body.status as ContentStatus,
      meta: body.meta,
      seo: body.seo,
      categoryIds: body.categoryIds,
      tagIds: body.tagIds,
      slug: body.slug,
      scheduledAt: body.scheduledAt ? new Date(body.scheduledAt) : undefined,
      userId: auth.userId,
    });

    if (result.success && result.data) {
      return new Response(
        JSON.stringify({
          success: true,
          data: result.data,
        }),
        {
          status: 201,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }
    return new Response(
      JSON.stringify({
        success: false,
        error: result.error || 'Erro desconhecido ao criar conteúdo.',
      }),
      {
        status: 400,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  } catch (error) {
    console.error('Erro ao processar criação de conteúdo:', error);

    return new Response(
      JSON.stringify({
        success: false,
        error: 'Ocorreu um erro ao processar a criação do conteúdo. Por favor, tente novamente.',
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  }
};
