/**
 * Serviço de notificação
 *
 * Este serviço orquestra o envio de notificações através de diferentes canais,
 * utilizando as interfaces segregadas para cada tipo de canal.
 */

import { nanoid } from 'nanoid';
import { NotificationMessage } from '../../domain/interfaces/NotificationChannels';
import { logger } from '../../utils/logger';
import { NotificationServiceFactory } from './NotificationServiceFactory';

/**
 * Tipo de canal de notificação
 */
export type NotificationChannel = 'email' | 'sms' | 'push' | 'inapp' | 'webhook';

/**
 * Opções para envio de notificação
 */
export interface SendNotificationOptions {
  /**
   * Canais para enviar a notificação
   */
  channels: NotificationChannel[];

  /**
   * Endereço de e-mail (para canal 'email')
   */
  emailAddress?: string;

  /**
   * Número de telefone (para canal 'sms')
   */
  phoneNumber?: string;

  /**
   * Token do dispositivo (para canal 'push')
   */
  deviceToken?: string;

  /**
   * URL do webhook (para canal 'webhook')
   */
  webhookUrl?: string;

  /**
   * Opções específicas para e-mail
   */
  emailOptions?: {
    from?: string;
    subject?: string;
    template?: string;
    attachments?: Array<{
      filename: string;
      content: Buffer | string;
      contentType?: string;
    }>;
    cc?: string[];
    bcc?: string[];
  };

  /**
   * Opções específicas para SMS
   */
  smsOptions?: {
    from?: string;
    priority?: 'normal' | 'high';
    requestDeliveryReceipt?: boolean;
  };

  /**
   * Opções específicas para push
   */
  pushOptions?: {
    actionUrl?: string;
    imageUrl?: string;
    sound?: string;
    badge?: number;
    data?: Record<string, unknown>;
    ttl?: number;
  };

  /**
   * Opções específicas para in-app
   */
  inAppOptions?: {
    type?: 'info' | 'success' | 'warning' | 'error';
    action?: {
      label: string;
      url: string;
    };
    duration?: number;
    position?: 'top' | 'bottom' | 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';
    dismissible?: boolean;
  };

  /**
   * Opções específicas para webhook
   */
  webhookOptions?: {
    method?: 'POST' | 'PUT';
    headers?: Record<string, string>;
    format?: 'json' | 'xml' | 'form';
    timeout?: number;
    retries?: number;
  };
}

/**
 * Resultado do envio de notificação
 */
export interface NotificationResult {
  /**
   * ID da notificação
   */
  id: string;

  /**
   * Resultados por canal
   */
  channelResults: Record<
    NotificationChannel,
    {
      /**
       * Se o envio foi bem-sucedido
       */
      success: boolean;

      /**
       * ID da notificação no canal
       */
      notificationId?: string;

      /**
       * Mensagem de erro (se houver)
       */
      error?: string;
    }
  >;
}

/**
 * Serviço de notificação
 */
export class NotificationService {
  /**
   * Envia uma notificação através de múltiplos canais
   * @param userId - ID do usuário destinatário
   * @param title - Título da notificação
   * @param body - Corpo da mensagem
   * @param options - Opções de envio
   * @returns Resultado do envio
   */
  public async sendNotification(
    userId: string,
    title: string,
    body: string,
    options: SendNotificationOptions
  ): Promise<NotificationResult> {
    // Gerar ID para a notificação
    const notificationId = uuidv4();

    // Criar mensagem base
    const message: NotificationMessage = {
      id: notificationId,
      userId,
      title,
      body,
      createdAt: new Date(),
    };

    // Inicializar resultado
    const result: NotificationResult = {
      id: notificationId,
      channelResults: {} as Record<NotificationChannel, any>,
    };

    // Enviar para cada canal solicitado
    await Promise.all(
      options.channels.map(async (channel) => {
        try {
          switch (channel) {
            case 'email': {
              if (!options.emailAddress) {
                throw new Error('Email address is required for email channel');
              }

              const emailService = NotificationServiceFactory.getEmailService();
              const emailId = await emailService.sendEmail(
                message,
                options.emailAddress,
                options.emailOptions
              );

              result.channelResults[channel] = {
                success: true,
                notificationId: emailId,
              };
              break;
            }

            case 'push': {
              if (!options.deviceToken) {
                throw new Error('Device token is required for push channel');
              }

              const pushService = NotificationServiceFactory.getPushService();
              const pushId = await pushService.sendPush(
                message,
                options.deviceToken,
                options.pushOptions
              );

              result.channelResults[channel] = {
                success: true,
                notificationId: pushId,
              };
              break;
            }

            case 'sms':
              if (!options.phoneNumber) {
                throw new Error('Phone number is required for SMS channel');
              }

              try {
                const smsService = NotificationServiceFactory.getSmsService();
                const smsId = await smsService.sendSms(
                  message,
                  options.phoneNumber,
                  options.smsOptions
                );

                result.channelResults[channel] = {
                  success: true,
                  notificationId: smsId,
                };
              } catch (error) {
                // Serviço não implementado ainda
                result.channelResults[channel] = {
                  success: false,
                  error: error instanceof Error ? error.message : String(error),
                };
              }
              break;

            case 'inapp':
              try {
                const inAppService = NotificationServiceFactory.getInAppService();
                const inAppId = await inAppService.sendInApp(message, options.inAppOptions);

                result.channelResults[channel] = {
                  success: true,
                  notificationId: inAppId,
                };
              } catch (error) {
                // Serviço não implementado ainda
                result.channelResults[channel] = {
                  success: false,
                  error: error instanceof Error ? error.message : String(error),
                };
              }
              break;

            case 'webhook':
              if (!options.webhookUrl) {
                throw new Error('Webhook URL is required for webhook channel');
              }

              try {
                const webhookService = NotificationServiceFactory.getWebhookService();
                const webhookId = await webhookService.sendWebhook(
                  message,
                  options.webhookUrl,
                  options.webhookOptions
                );

                result.channelResults[channel] = {
                  success: true,
                  notificationId: webhookId,
                };
              } catch (error) {
                // Serviço não implementado ainda
                result.channelResults[channel] = {
                  success: false,
                  error: error instanceof Error ? error.message : String(error),
                };
              }
              break;
          }
        } catch (error) {
          // Registrar erro
          logger.error(`Failed to send notification via ${channel}`, {
            error: error instanceof Error ? error.message : String(error),
            userId,
            notificationId,
          });

          // Adicionar ao resultado
          result.channelResults[channel] = {
            success: false,
            error: error instanceof Error ? error.message : String(error),
          };
        }
      })
    );

    return result;
  }
}
