import { producer } from '@config/kafka';
import { queryHelper } from '@db/queryHelper';
import { logger } from '@utils/logger';
// src/services/refundService.ts
import { efiPayService } from './efiPayService';
import { PaymentStatus } from './paymentService';

/**
 * Enum para status de reembolso
 */
export enum RefundStatus {
  PENDING = 1,
  COMPLETED = 2,
  REJECTED = 3,
}

/**
 * Interface para solicitação de reembolso
 */
export interface RefundRequest {
  paymentId: string;
  value?: number; // Se não fornecido, reembolsa o valor total
  reason: string;
  requestedBy: string;
}

/**
 * Serviço para gerenciamento de reembolsos
 */
export const refundService = {
  /**
   * Solicita um reembolso PIX
   * @param request - Dados da solicitação
   * @returns Resultado da solicitação
   */
  async requestPixRefund(request: RefundRequest): Promise<any> {
    try {
      // Buscar dados do pagamento
      const payment = await queryHelper.queryOne(
        `SELECT p.*, pt.type as payment_type
         FROM tab_payment p
         JOIN tab_payment_type pt ON p.ulid_payment_type = pt.ulid_payment_type
         WHERE p.ulid_payment = $1`,
        [request.paymentId]
      );

      if (!payment) {
        throw new Error(`Pagamento não encontrado: ${request.paymentId}`);
      }

      // Verificar se o pagamento é do tipo PIX
      if (payment.payment_type.toLowerCase() !== 'pix') {
        throw new Error(`Tipo de pagamento não suportado para reembolso: ${payment.payment_type}`);
      }

      // Verificar se o pagamento está aprovado
      if (payment.cod_status !== PaymentStatus.APPROVED) {
        throw new Error(`Status de pagamento não permite reembolso: ${payment.cod_status}`);
      }

      // Verificar se o pagamento tem ID externo (endToEndId)
      if (!payment.external_id) {
        throw new Error('Pagamento não possui ID externo para reembolso');
      }

      // Determinar valor do reembolso
      const refundValue = request.value || payment.value;

      // Verificar se o valor do reembolso é válido
      if (refundValue <= 0 || refundValue > payment.value) {
        throw new Error(`Valor de reembolso inválido: ${refundValue}`);
      }

      // Criar registro de reembolso
      const refundId = await this.createRefundRecord(
        payment.ulid_payment,
        refundValue,
        request.reason,
        request.requestedBy,
        RefundStatus.PENDING
      );

      // Solicitar reembolso na Efí Pay
      const refundResult = await this.processPixRefund(payment.external_id, refundValue, refundId);

      // Atualizar registro de reembolso com ID externo
      await queryHelper.query(
        `UPDATE tab_refund 
         SET external_id = $1, updated_at = NOW() 
         WHERE ulid_refund = $2`,
        [refundResult.id, refundId]
      );

      // Se o reembolso for processado imediatamente
      if (refundResult.status === 'DEVOLVIDO') {
        // Atualizar status do reembolso
        await queryHelper.query(
          `UPDATE tab_refund 
           SET cod_status = $1, updated_at = NOW() 
           WHERE ulid_refund = $2`,
          [RefundStatus.COMPLETED, refundId]
        );

        // Atualizar status do pagamento
        await queryHelper.query(
          `UPDATE tab_payment 
           SET cod_status = $1, updated_at = NOW() 
           WHERE ulid_payment = $2`,
          [PaymentStatus.REFUNDED, payment.ulid_payment]
        );

        // Enviar evento para o Kafka
        await this.sendRefundEvent(refundId, RefundStatus.COMPLETED, refundResult);
      }

      return {
        refundId,
        paymentId: payment.ulid_payment,
        value: refundValue,
        status: refundResult.status,
        result: refundResult,
      };
    } catch (error) {
      logger.error('Erro ao solicitar reembolso PIX:', error);
      throw error;
    }
  },

  /**
   * Cria um registro de reembolso no banco de dados
   * @param paymentId - ID do pagamento
   * @param value - Valor do reembolso
   * @param reason - Motivo do reembolso
   * @param requestedBy - ID do usuário que solicitou o reembolso
   * @param status - Status do reembolso
   * @returns ID do reembolso criado
   */
  async createRefundRecord(
    paymentId: string,
    value: number,
    reason: string,
    requestedBy: string,
    status: RefundStatus
  ): Promise<string> {
    const refundId = crypto.randomUUID();

    await queryHelper.query(
      `INSERT INTO tab_refund (
        ulid_refund,
        ulid_payment,
        value,
        reason,
        requested_by,
        cod_status,
        created_at,
        updated_at
      ) VALUES (
        $1, $2, $3, $4, $5, $6, NOW(), NOW()
      )`,
      [refundId, paymentId, value, reason, requestedBy, status]
    );

    return refundId;
  },

  /**
   * Processa um reembolso PIX na Efí Pay
   * @param e2eId - ID end-to-end do pagamento PIX
   * @param value - Valor do reembolso
   * @param refundId - ID do reembolso
   * @returns Resultado do processamento
   */
  async processPixRefund(e2eId: string, value: number, refundId: string): Promise<any> {
    try {
      const efi = efiPayService.getInstance();

      const params = {
        e2eId,
        id: refundId,
      };

      const body = {
        valor: value.toFixed(2),
      };

      return await efi.pixDevolution(params, body);
    } catch (error) {
      logger.error('Erro ao processar reembolso PIX:', error);
      throw error;
    }
  },

  /**
   * Consulta o status de um reembolso PIX
   * @param e2eId - ID end-to-end do pagamento PIX
   * @param refundId - ID do reembolso
   * @returns Status do reembolso
   */
  async getPixRefundStatus(e2eId: string, refundId: string): Promise<any> {
    try {
      const efi = efiPayService.getInstance();

      const params = {
        e2eId,
        id: refundId,
      };

      return await efi.pixDetailDevolution(params);
    } catch (error) {
      logger.error('Erro ao consultar status de reembolso PIX:', error);
      throw error;
    }
  },

  /**
   * Envia um evento de reembolso para o Kafka
   * @param refundId - ID do reembolso
   * @param status - Status do reembolso
   * @param data - Dados do reembolso
   */
  async sendRefundEvent(refundId: string, status: RefundStatus, data: any): Promise<void> {
    try {
      await producer.connect();

      await producer.send({
        topic: 'refund-events',
        messages: [
          {
            key: refundId,
            value: JSON.stringify({
              refundId,
              status,
              refundData: data,
              timestamp: new Date().toISOString(),
            }),
          },
        ],
      });
    } catch (error) {
      logger.error('Erro ao enviar evento de reembolso:', error);
    } finally {
      await producer.disconnect();
    }
  },

  /**
   * Lista reembolsos de um pagamento
   * @param paymentId - ID do pagamento
   * @returns Lista de reembolsos
   */
  async listRefundsByPayment(paymentId: string): Promise<any[]> {
    try {
      return await queryHelper.queryAll(
        `SELECT r.*, u.name as requested_by_name
         FROM tab_refund r
         LEFT JOIN tab_user u ON r.requested_by = u.ulid_user
         WHERE r.ulid_payment = $1
         ORDER BY r.created_at DESC`,
        [paymentId]
      );
    } catch (error) {
      logger.error('Erro ao listar reembolsos por pagamento:', error);
      throw error;
    }
  },
};
