# Camada de Compatibilidade para Repositórios

Esta pasta contém implementações de compatibilidade para manter a retrocompatibilidade com o código existente que utiliza a estrutura antiga de repositórios.

## Migração de `repository` para `repositories`

Como parte da reorganização do projeto, os arquivos do diretório `src/repository` foram movidos para `src/repositories/compatibility` para manter a compatibilidade com o código existente enquanto migramos para a nova estrutura.

## Estrutura

- `src/repositories/interfaces/`: Interfaces que definem os contratos dos repositórios
- `src/repositories/implementations/`: Implementações concretas dos repositórios
- `src/repositories/compatibility/`: Implementações de compatibilidade para manter a retrocompatibilidade

## Repositórios Migrados

Os seguintes repositórios já foram migrados para a camada de compatibilidade:

- `productRepository.ts`: Repositório de produtos
- `userRepository.ts`: Repositório de usuários
- `categoryRepository.ts`: Repositório de categorias
- `orderRepository.ts`: Repositório de pedidos
- `orderItemRepository.ts`: Repositório de itens de pedido
- `invoiceRepository.ts`: Repositório de faturas
- `invoiceItemRepository.ts`: Repositório de itens de fatura
- `paymentRepository.ts`: Repositório de pagamentos

## Uso

Novos códigos devem usar as interfaces e implementações da nova estrutura em `src/repositories/interfaces/` e `src/repositories/implementations/`.

O código existente que ainda usa a estrutura antiga continuará funcionando através da camada de compatibilidade, mas deve ser migrado para a nova estrutura quando possível.

## Como Funciona

Cada repositório na camada de compatibilidade:

1. Tenta usar a nova implementação do repositório correspondente
2. Converte o resultado para o formato esperado pelo código antigo
3. Em caso de erro, faz fallback para a implementação antiga

Isso garante que o código existente continue funcionando enquanto migramos para a nova estrutura.

## Depreciação

A estrutura antiga em `src/repository` está depreciada e será removida em uma versão futura. Todo o código novo deve usar a nova estrutura em `src/repositories`.

## Próximos Passos

Os seguintes repositórios ainda precisam ser migrados:

- `paymentTypeRepository.ts`
- `postRepository.ts`
- `schoolTypeRepository.ts`
- `statusRepository.ts`
- `userTypeRepository.ts`
- `auditRepository.ts`
- `informationSchemaColumnRepository.ts`
- `permissionRepository.ts`
- `resourceRepository.ts`
- `roleRepository.ts`
- `tokenRepository.ts`
