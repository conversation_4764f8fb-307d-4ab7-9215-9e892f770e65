# Camada de Compatibilidade para Repositórios

Esta pasta contém implementações de compatibilidade para manter a retrocompatibilidade com o código existente que utiliza a estrutura antiga de repositórios.

## Migração de `repository` para `repositories`

Como parte da reorganização do projeto, os arquivos do diretório `src/repository` foram movidos para `src/repositories/compatibility` para manter a compatibilidade com o código existente enquanto migramos para a nova estrutura.

## Estrutura

- `src/repositories/interfaces/`: Interfaces que definem os contratos dos repositórios
- `src/repositories/implementations/`: Implementações concretas dos repositórios
- `src/repositories/compatibility/`: Implementações de compatibilidade para manter a retrocompatibilidade

## Uso

Novos códigos devem usar as interfaces e implementações da nova estrutura em `src/repositories/interfaces/` e `src/repositories/implementations/`.

O código existente que ainda usa a estrutura antiga continuará funcionando através da camada de compatibilidade, mas deve ser migrado para a nova estrutura quando possível.

## Depreciação

A estrutura antiga em `src/repository` está depreciada e será removida em uma versão futura. Todo o código novo deve usar a nova estrutura em `src/repositories`.
