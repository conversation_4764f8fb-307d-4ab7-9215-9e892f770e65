/**
 * Serviço de auditoria
 *
 * Este serviço é responsável por registrar ações sensíveis no sistema,
 * como tentativas de login, alterações de permissões e acesso a recursos protegidos.
 */

import * as fs from 'node:fs';
import * as path from 'node:path';
import { pipeline } from 'node:stream';
import { promisify } from 'node:util';
import { createGzip } from 'node:zlib';
import { pgHelper } from '@repository/pgHelper';
import { cacheService } from '@services/cacheService';
import { logger } from '@utils/logger';
import { ulid } from 'ulid';

// Promisificar pipeline
const pipelineAsync = promisify(pipeline);

/**
 * Tipos de eventos de auditoria
 */
export enum AuditEventType {
  // Eventos de autenticação
  LOGIN_SUCCESS = 'auth.login.success',
  LOGIN_FAILED = 'auth.login.failed',
  LOGOUT = 'auth.logout',
  PASSWORD_CHANGED = 'auth.password.changed',
  PASSWORD_RESET_REQUESTED = 'auth.password.reset.requested',
  PASSWORD_RESET_COMPLETED = 'auth.password.reset.completed',
  EMAIL_CHANGED = 'auth.email.changed',
  PROFILE_UPDATED = 'auth.profile.updated',
  TWO_FACTOR_ENABLED = 'auth.2fa.enabled',
  TWO_FACTOR_DISABLED = 'auth.2fa.disabled',
  TWO_FACTOR_VERIFICATION_SUCCESS = 'auth.2fa.verification.success',
  TWO_FACTOR_VERIFICATION_FAILED = 'auth.2fa.verification.failed',

  // Eventos de autorização
  PERMISSION_CHANGED = 'auth.permission.changed',
  PERMISSION_GRANTED = 'auth.permission.granted',
  PERMISSION_DENIED = 'auth.permission.denied',
  ROLE_ASSIGNED = 'auth.role.assigned',
  ROLE_REMOVED = 'auth.role.removed',

  // Eventos de administração
  USER_CREATED = 'admin.user.created',
  USER_UPDATED = 'admin.user.updated',
  USER_DELETED = 'admin.user.deleted',
  USER_BLOCKED = 'admin.user.blocked',
  USER_UNBLOCKED = 'admin.user.unblocked',
  ROLE_CREATED = 'admin.role.created',
  ROLE_UPDATED = 'admin.role.updated',
  ROLE_DELETED = 'admin.role.deleted',
  PERMISSION_CREATED = 'admin.permission.created',
  PERMISSION_UPDATED = 'admin.permission.updated',
  PERMISSION_DELETED = 'admin.permission.deleted',
  ADMIN_ACTION = 'admin.action',
  SETTINGS_CHANGED = 'admin.settings.changed',

  // Eventos de acesso a recursos
  RESOURCE_ACCESSED = 'resource.accessed',
  RESOURCE_CREATED = 'resource.created',
  RESOURCE_UPDATED = 'resource.updated',
  RESOURCE_DELETED = 'resource.deleted',
  SENSITIVE_DATA_ACCESSED = 'resource.sensitive.accessed',
  SENSITIVE_DATA_EXPORTED = 'resource.sensitive.exported',

  // Eventos de sistema
  SYSTEM_STARTED = 'system.started',
  SYSTEM_STOPPED = 'system.stopped',
  SYSTEM_ERROR = 'system.error',
  SYSTEM_WARNING = 'system.warning',
  CONFIG_CHANGED = 'system.config.changed',
  MAINTENANCE_MODE = 'system.maintenance',
  BACKUP_CREATED = 'system.backup.created',
  BACKUP_RESTORED = 'system.backup.restored',

  // Eventos de conteúdo
  CONTENT_VIEWED = 'content.viewed',
  CONTENT_CREATED = 'content.created',
  CONTENT_UPDATED = 'content.updated',
  CONTENT_DELETED = 'content.deleted',
  CONTENT_PUBLISHED = 'content.published',
  CONTENT_UNPUBLISHED = 'content.unpublished',

  // Eventos de relatórios
  REPORT_GENERATED = 'report.generated',
  EXPORT_CREATED = 'report.export.created',
  IMPORT_COMPLETED = 'report.import.completed',

  // Eventos de auditoria
  AUDIT_VIEWED = 'audit.viewed',
  AUDIT_EXPORTED = 'audit.exported',
  AUDIT_SETTINGS_CHANGED = 'audit.settings.changed',
  AUDIT_ARCHIVED = 'audit.archived',
  AUDIT_PURGED = 'audit.purged',
}

/**
 * Níveis de severidade para eventos de auditoria
 */
export enum AuditSeverity {
  INFO = 'info',
  WARNING = 'warning',
  ERROR = 'error',
  CRITICAL = 'critical',
}

/**
 * Interface para dados de evento de auditoria
 */
export interface AuditEventData {
  /**
   * Tipo do evento
   */
  eventType: AuditEventType;

  /**
   * ID do usuário que realizou a ação (se disponível)
   */
  userId?: string;

  /**
   * Nome do usuário que realizou a ação (se disponível)
   */
  userName?: string;

  /**
   * Endereço IP de origem
   */
  ipAddress?: string;

  /**
   * User-Agent do navegador
   */
  userAgent?: string;

  /**
   * Recurso afetado pela ação
   */
  resource?: string;

  /**
   * ID do recurso afetado
   */
  resourceId?: string;

  /**
   * Ação realizada
   */
  action?: string;

  /**
   * Resultado da ação
   */
  result?: string;

  /**
   * Severidade do evento
   */
  severity?: AuditSeverity;

  /**
   * Dados adicionais específicos do evento (serão armazenados como JSON)
   */
  metadata?: Record<string, unknown>;
}

/**
 * Interface para evento de auditoria
 */
export interface AuditEvent {
  /**
   * ID do evento
   */
  id: string;

  /**
   * Tipo do evento
   */
  eventType: AuditEventType;

  /**
   * ID do usuário que realizou a ação
   */
  userId?: string;

  /**
   * Nome do usuário que realizou a ação
   */
  userName?: string;

  /**
   * Endereço IP de origem
   */
  ipAddress?: string;

  /**
   * User-Agent do navegador
   */
  userAgent?: string;

  /**
   * Recurso afetado pela ação
   */
  resource?: string;

  /**
   * ID do recurso afetado
   */
  resourceId?: string;

  /**
   * Ação realizada
   */
  action?: string;

  /**
   * Resultado da ação
   */
  result?: string;

  /**
   * Severidade do evento
   */
  severity: AuditSeverity;

  /**
   * Dados adicionais específicos do evento
   */
  metadata?: Record<string, unknown>;

  /**
   * Timestamp do evento
   */
  timestamp: string;

  /**
   * Se o evento foi arquivado
   */
  archived?: boolean;

  /**
   * ID do arquivo de arquivamento
   */
  archiveId?: string;
}

/**
 * Serviço de auditoria
 */
export const auditService = {
  /**
   * Registra um evento de auditoria
   * @param eventData - Dados do evento
   * @returns ID do evento registrado ou null em caso de erro
   */
  async logEvent(eventData: AuditEventData): Promise<string | null> {
    try {
      // Gerar ID único para o evento
      const eventId = ulid();

      // Definir timestamp atual
      const timestamp = new Date();

      // Definir severidade padrão se não fornecida
      const severity = eventData.severity || AuditSeverity.INFO;

      // Preparar metadados para armazenamento
      const metadata = eventData.metadata ? JSON.stringify(eventData.metadata) : null;

      // Inserir evento no banco de dados
      const query = `
        INSERT INTO tab_audit_log (
          ulid_audit_log,
          event_type,
          ulid_user,
          user_name,
          ip_address,
          user_agent,
          resource,
          resource_id,
          action,
          result,
          severity,
          metadata,
          created_at
        ) VALUES (
          $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13
        )
        RETURNING ulid_audit_log
      `;

      const values = [
        eventId,
        eventData.eventType,
        eventData.userId || null,
        eventData.userName || null,
        eventData.ipAddress || null,
        eventData.userAgent || null,
        eventData.resource || null,
        eventData.resourceId || null,
        eventData.action || null,
        eventData.result || null,
        severity,
        metadata,
        timestamp,
      ];

      const result = await pgHelper.query(query, values);

      // Armazenar evento em cache para acesso rápido a eventos recentes
      if (result.rowCount > 0) {
        await this.cacheRecentEvent(eventId, eventData, timestamp);
        return eventId;
      }

      return null;
    } catch (error) {
      logger.error('Erro ao registrar evento de auditoria:', error);

      // Tentar registrar em log local em caso de falha no banco de dados
      try {
        logger.warn(
          'Registrando evento de auditoria em log local devido a falha no banco de dados',
          {
            eventType: eventData.eventType,
            userId: eventData.userId,
            resource: eventData.resource,
            action: eventData.action,
            result: eventData.result,
            timestamp: new Date().toISOString(),
          }
        );
      } catch (logError) {
        // Silenciar erro de fallback para evitar loops
      }

      return null;
    }
  },

  /**
   * Armazena evento recente em cache para acesso rápido
   * @param eventId - ID do evento
   * @param eventData - Dados do evento
   * @param timestamp - Timestamp do evento
   */
  async cacheRecentEvent(
    eventId: string,
    eventData: AuditEventData,
    timestamp: Date
  ): Promise<void> {
    try {
      // Chave para lista de eventos recentes
      const recentEventsKey = 'audit:recent_events';

      // Chave para detalhes do evento específico
      const eventKey = `audit:event:${eventId}`;

      // Armazenar detalhes do evento (TTL de 24 horas)
      await cacheService.set(
        eventKey,
        JSON.stringify({
          id: eventId,
          ...eventData,
          timestamp: timestamp.toISOString(),
        }),
        86400 // 24 horas em segundos
      );

      // Adicionar à lista de eventos recentes (limitada a 100 eventos)
      await cacheService.zadd(recentEventsKey, timestamp.getTime(), eventId);

      // Manter apenas os 100 eventos mais recentes
      await cacheService.zremrangebyrank(recentEventsKey, 0, -101);

      // Definir TTL para a lista de eventos recentes (24 horas)
      await cacheService.expire(recentEventsKey, 86400);
    } catch (error) {
      logger.error('Erro ao armazenar evento de auditoria em cache:', error);
      // Não propagar erro, pois o cache é apenas uma otimização
    }
  },

  /**
   * Obtém eventos recentes de auditoria
   * @param limit - Número máximo de eventos a retornar
   * @returns Lista de eventos recentes
   */
  async getRecentEvents(limit = 50): Promise<AuditEvent[]> {
    try {
      // Chave para lista de eventos recentes
      const recentEventsKey = 'audit:recent_events';

      // Obter IDs dos eventos recentes (do mais recente para o mais antigo)
      const eventIds = await cacheService.zrevrange(recentEventsKey, 0, limit - 1);

      if (!eventIds || eventIds.length === 0) {
        // Se não há eventos em cache, buscar do banco de dados
        return this.getEventsFromDatabase(limit);
      }

      // Obter detalhes de cada evento do cache
      const events: AuditEvent[] = [];

      for (const eventId of eventIds) {
        const eventKey = `audit:event:${eventId}`;
        const eventData = await cacheService.get(eventKey);

        if (eventData) {
          events.push(JSON.parse(eventData));
        }
      }

      return events;
    } catch (error) {
      logger.error('Erro ao obter eventos recentes de auditoria:', error);

      // Em caso de erro no cache, buscar do banco de dados
      return this.getEventsFromDatabase(limit);
    }
  },

  /**
   * Obtém eventos de auditoria diretamente do banco de dados
   * @param limit - Número máximo de eventos a retornar
   * @returns Lista de eventos
   */
  async getEventsFromDatabase(limit = 50): Promise<AuditEvent[]> {
    try {
      const query = `
        SELECT
          ulid_audit_log as id,
          event_type as "eventType",
          ulid_user as "userId",
          user_name as "userName",
          ip_address as "ipAddress",
          user_agent as "userAgent",
          resource,
          resource_id as "resourceId",
          action,
          result,
          severity,
          metadata,
          created_at as "timestamp"
        FROM tab_audit_log
        ORDER BY created_at DESC
        LIMIT $1
      `;

      const result = await pgHelper.query(query, [limit]);

      return result.rows.map((row) => ({
        ...row,
        metadata: row.metadata ? JSON.parse(row.metadata) : null,
      }));
    } catch (error) {
      logger.error('Erro ao obter eventos de auditoria do banco de dados:', error);
      return [];
    }
  },

  /**
   * Busca eventos de auditoria com filtros
   * @param filters - Filtros para a busca
   * @param page - Número da página (começando em 1)
   * @param pageSize - Tamanho da página
   * @returns Eventos filtrados e informações de paginação
   */
  async searchEvents(
    filters: {
      eventType?: AuditEventType | AuditEventType[];
      userId?: string;
      resource?: string;
      action?: string;
      severity?: AuditSeverity | AuditSeverity[];
      startDate?: Date;
      endDate?: Date;
      archived?: boolean;
    },
    page = 1,
    pageSize = 20
  ): Promise<{
    events: AuditEvent[];
    total: number;
    page: number;
    pageSize: number;
    totalPages: number;
  }> {
    try {
      // Construir consulta com filtros
      let query = `
        SELECT
          ulid_audit_log as id,
          event_type as "eventType",
          ulid_user as "userId",
          user_name as "userName",
          ip_address as "ipAddress",
          user_agent as "userAgent",
          resource,
          resource_id as "resourceId",
          action,
          result,
          severity,
          metadata,
          created_at as "timestamp"
        FROM tab_audit_log
        WHERE 1=1
      `;

      // Construir consulta de contagem
      let countQuery = `
        SELECT COUNT(*) as total
        FROM tab_audit_log
        WHERE 1=1
      `;

      const queryParams: any[] = [];
      const conditions: string[] = [];

      // Adicionar filtros à consulta
      if (filters.eventType) {
        if (Array.isArray(filters.eventType)) {
          conditions.push(`event_type = ANY($${queryParams.length + 1})`);
          queryParams.push(filters.eventType);
        } else {
          conditions.push(`event_type = $${queryParams.length + 1}`);
          queryParams.push(filters.eventType);
        }
      }

      if (filters.userId) {
        conditions.push(`ulid_user = $${queryParams.length + 1}`);
        queryParams.push(filters.userId);
      }

      if (filters.resource) {
        conditions.push(`resource = $${queryParams.length + 1}`);
        queryParams.push(filters.resource);
      }

      if (filters.action) {
        conditions.push(`action = $${queryParams.length + 1}`);
        queryParams.push(filters.action);
      }

      if (filters.severity) {
        if (Array.isArray(filters.severity)) {
          conditions.push(`severity = ANY($${queryParams.length + 1})`);
          queryParams.push(filters.severity);
        } else {
          conditions.push(`severity = $${queryParams.length + 1}`);
          queryParams.push(filters.severity);
        }
      }

      if (filters.startDate) {
        conditions.push(`created_at >= $${queryParams.length + 1}`);
        queryParams.push(filters.startDate);
      }

      if (filters.endDate) {
        conditions.push(`created_at <= $${queryParams.length + 1}`);
        queryParams.push(filters.endDate);
      }

      // Adicionar condições às consultas
      if (conditions.length > 0) {
        const whereClause = conditions.join(' AND ');
        query += ` AND ${whereClause}`;
        countQuery += ` AND ${whereClause}`;
      }

      // Adicionar ordenação e paginação
      query += ` ORDER BY created_at DESC LIMIT $${queryParams.length + 1} OFFSET $${queryParams.length + 2}`;

      // Calcular offset para paginação
      const offset = (page - 1) * pageSize;

      // Adicionar parâmetros de paginação
      const paginationParams = [...queryParams, pageSize, offset];

      // Executar consulta de contagem
      const countResult = await pgHelper.query(countQuery, queryParams);
      const total = Number.parseInt(countResult.rows[0].total, 10);

      // Executar consulta principal
      const result = await pgHelper.query(query, paginationParams);

      // Calcular total de páginas
      const totalPages = Math.ceil(total / pageSize);

      // Processar resultados
      const events = result.rows.map((row) => ({
        ...row,
        metadata: row.metadata ? JSON.parse(row.metadata) : null,
      }));

      return {
        events,
        total,
        page,
        pageSize,
        totalPages,
      };
    } catch (error) {
      logger.error('Erro ao buscar eventos de auditoria:', error);
      return {
        events: [],
        total: 0,
        page,
        pageSize,
        totalPages: 0,
      };
    }
  },

  /**
   * Exporta eventos de auditoria para um arquivo
   * @param filters - Filtros para a exportação
   * @param format - Formato do arquivo (json ou csv)
   * @param compress - Se deve comprimir o arquivo
   * @returns Caminho do arquivo gerado ou null em caso de erro
   */
  async exportEvents(
    filters: {
      eventType?: AuditEventType | AuditEventType[];
      userId?: string;
      resource?: string;
      action?: string;
      severity?: AuditSeverity | AuditSeverity[];
      startDate?: Date;
      endDate?: Date;
    },
    format: 'json' | 'csv' = 'json',
    compress = true
  ): Promise<string | null> {
    try {
      logger.info('Iniciando exportação de eventos de auditoria');

      // Buscar eventos (sem paginação)
      let query = `
        SELECT
          ulid_audit_log as id,
          event_type as "eventType",
          ulid_user as "userId",
          user_name as "userName",
          ip_address as "ipAddress",
          user_agent as "userAgent",
          resource,
          resource_id as "resourceId",
          action,
          result,
          severity,
          metadata,
          created_at as "timestamp"
        FROM tab_audit_log
        WHERE 1=1
      `;

      const queryParams: any[] = [];
      const conditions: string[] = [];

      // Adicionar filtros à consulta
      if (filters.eventType) {
        if (Array.isArray(filters.eventType)) {
          conditions.push(`event_type = ANY($${queryParams.length + 1})`);
          queryParams.push(filters.eventType);
        } else {
          conditions.push(`event_type = $${queryParams.length + 1}`);
          queryParams.push(filters.eventType);
        }
      }

      if (filters.userId) {
        conditions.push(`ulid_user = $${queryParams.length + 1}`);
        queryParams.push(filters.userId);
      }

      if (filters.resource) {
        conditions.push(`resource = $${queryParams.length + 1}`);
        queryParams.push(filters.resource);
      }

      if (filters.action) {
        conditions.push(`action = $${queryParams.length + 1}`);
        queryParams.push(filters.action);
      }

      if (filters.severity) {
        if (Array.isArray(filters.severity)) {
          conditions.push(`severity = ANY($${queryParams.length + 1})`);
          queryParams.push(filters.severity);
        } else {
          conditions.push(`severity = $${queryParams.length + 1}`);
          queryParams.push(filters.severity);
        }
      }

      if (filters.startDate) {
        conditions.push(`created_at >= $${queryParams.length + 1}`);
        queryParams.push(filters.startDate);
      }

      if (filters.endDate) {
        conditions.push(`created_at <= $${queryParams.length + 1}`);
        queryParams.push(filters.endDate);
      }

      // Adicionar condições à consulta
      if (conditions.length > 0) {
        const whereClause = conditions.join(' AND ');
        query += ` AND ${whereClause}`;
      }

      // Adicionar ordenação
      query += ' ORDER BY created_at DESC';

      // Executar consulta
      const result = await pgHelper.query(query, queryParams);

      // Processar resultados
      const events = result.rows.map((row) => ({
        ...row,
        metadata: row.metadata ? JSON.parse(row.metadata) : null,
      }));

      if (events.length === 0) {
        logger.warn('Nenhum evento encontrado para exportação');
        return null;
      }

      // Criar diretório de exportação se não existir
      const exportDir = path.join(process.cwd(), 'exports', 'audit');
      await fs.promises.mkdir(exportDir, { recursive: true });

      // Gerar nome do arquivo
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const fileName = `audit_export_${timestamp}.${format}`;
      const filePath = path.join(exportDir, fileName);

      // Converter eventos para o formato desejado
      let content = '';

      if (format === 'json') {
        content = JSON.stringify(events, null, 2);
      } else {
        // Formato CSV
        const headers = Object.keys(events[0]).join(',');
        const rows = events.map((event) => {
          return Object.values(event)
            .map((value) => {
              if (typeof value === 'object') {
                return `"${JSON.stringify(value).replace(/"/g, '""')}"`;
              }
              return `"${String(value).replace(/"/g, '""')}"`;
            })
            .join(',');
        });

        content = [headers, ...rows].join('\n');
      }

      // Salvar arquivo
      if (compress) {
        const gzip = createGzip();
        const source = fs.createReadStream(filePath);
        const destination = fs.createWriteStream(`${filePath}.gz`);

        await fs.promises.writeFile(filePath, content);
        await pipelineAsync(source, gzip, destination);

        // Remover arquivo original
        fs.unlinkSync(filePath);

        logger.info(`Eventos exportados para ${filePath}.gz (${events.length} eventos)`);

        // Registrar evento de exportação
        await this.logEvent({
          eventType: AuditEventType.AUDIT_EXPORTED,
          action: 'export',
          result: 'success',
          resource: 'audit_log',
          metadata: {
            format,
            compressed: true,
            eventCount: events.length,
            filePath: `${filePath}.gz`,
            filters,
          },
          severity: AuditSeverity.INFO,
        });

        return `${filePath}.gz`;
      }
      await fs.promises.writeFile(filePath, content);

      logger.info(`Eventos exportados para ${filePath} (${events.length} eventos)`);

      // Registrar evento de exportação
      await this.logEvent({
        eventType: AuditEventType.AUDIT_EXPORTED,
        action: 'export',
        result: 'success',
        resource: 'audit_log',
        metadata: {
          format,
          compressed: false,
          eventCount: events.length,
          filePath,
          filters,
        },
        severity: AuditSeverity.INFO,
      });

      return filePath;
    } catch (error) {
      logger.error('Erro ao exportar eventos de auditoria:', error);

      // Registrar evento de erro
      await this.logEvent({
        eventType: AuditEventType.AUDIT_EXPORTED,
        action: 'export',
        result: 'error',
        resource: 'audit_log',
        metadata: {
          error: error instanceof Error ? error.message : String(error),
          filters,
        },
        severity: AuditSeverity.ERROR,
      });

      return null;
    }
  },

  /**
   * Obtém estatísticas de eventos de auditoria
   * @param startDate - Data de início
   * @param endDate - Data de fim
   * @returns Estatísticas de eventos
   */
  async getStatistics(
    startDate?: Date,
    endDate?: Date
  ): Promise<{
    totalEvents: number;
    eventsByType: Record<string, number>;
    eventsBySeverity: Record<string, number>;
    eventsByDay: Record<string, number>;
    topUsers: Array<{ userId: string; userName: string; count: number }>;
    topResources: Array<{ resource: string; count: number }>;
  }> {
    try {
      // Definir período padrão (últimos 30 dias)
      if (!endDate) {
        endDate = new Date();
      }

      if (!startDate) {
        startDate = new Date();
        startDate.setDate(startDate.getDate() - 30);
      }

      // Consulta para total de eventos
      const totalQuery = `
        SELECT COUNT(*) as total
        FROM tab_audit_log
        WHERE created_at BETWEEN $1 AND $2
      `;

      // Consulta para eventos por tipo
      const typeQuery = `
        SELECT event_type, COUNT(*) as count
        FROM tab_audit_log
        WHERE created_at BETWEEN $1 AND $2
        GROUP BY event_type
        ORDER BY count DESC
      `;

      // Consulta para eventos por severidade
      const severityQuery = `
        SELECT severity, COUNT(*) as count
        FROM tab_audit_log
        WHERE created_at BETWEEN $1 AND $2
        GROUP BY severity
        ORDER BY count DESC
      `;

      // Consulta para eventos por dia
      const dayQuery = `
        SELECT DATE(created_at) as day, COUNT(*) as count
        FROM tab_audit_log
        WHERE created_at BETWEEN $1 AND $2
        GROUP BY day
        ORDER BY day
      `;

      // Consulta para top usuários
      const userQuery = `
        SELECT ulid_user as "userId", user_name as "userName", COUNT(*) as count
        FROM tab_audit_log
        WHERE created_at BETWEEN $1 AND $2 AND ulid_user IS NOT NULL
        GROUP BY ulid_user, user_name
        ORDER BY count DESC
        LIMIT 10
      `;

      // Consulta para top recursos
      const resourceQuery = `
        SELECT resource, COUNT(*) as count
        FROM tab_audit_log
        WHERE created_at BETWEEN $1 AND $2 AND resource IS NOT NULL
        GROUP BY resource
        ORDER BY count DESC
        LIMIT 10
      `;

      // Executar consultas
      const [totalResult, typeResult, severityResult, dayResult, userResult, resourceResult] =
        await Promise.all([
          pgHelper.query(totalQuery, [startDate, endDate]),
          pgHelper.query(typeQuery, [startDate, endDate]),
          pgHelper.query(severityQuery, [startDate, endDate]),
          pgHelper.query(dayQuery, [startDate, endDate]),
          pgHelper.query(userQuery, [startDate, endDate]),
          pgHelper.query(resourceQuery, [startDate, endDate]),
        ]);

      // Processar resultados
      const totalEvents = Number.parseInt(totalResult.rows[0]?.total || '0', 10);

      const eventsByType: Record<string, number> = {};
      typeResult.rows.forEach((row) => {
        eventsByType[row.event_type] = Number.parseInt(row.count, 10);
      });

      const eventsBySeverity: Record<string, number> = {};
      severityResult.rows.forEach((row) => {
        eventsBySeverity[row.severity] = Number.parseInt(row.count, 10);
      });

      const eventsByDay: Record<string, number> = {};
      dayResult.rows.forEach((row) => {
        const day = new Date(row.day).toISOString().split('T')[0];
        eventsByDay[day] = Number.parseInt(row.count, 10);
      });

      const topUsers = userResult.rows.map((row) => ({
        userId: row.userId,
        userName: row.userName,
        count: Number.parseInt(row.count, 10),
      }));

      const topResources = resourceResult.rows.map((row) => ({
        resource: row.resource,
        count: Number.parseInt(row.count, 10),
      }));

      return {
        totalEvents,
        eventsByType,
        eventsBySeverity,
        eventsByDay,
        topUsers,
        topResources,
      };
    } catch (error) {
      logger.error('Erro ao obter estatísticas de auditoria:', error);

      // Retornar estatísticas vazias em caso de erro
      return {
        totalEvents: 0,
        eventsByType: {},
        eventsBySeverity: {},
        eventsByDay: {},
        topUsers: [],
        topResources: [],
      };
    }
  },
};
