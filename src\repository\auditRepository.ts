/**
 * Repositório para acesso aos logs de auditoria
 */

import { pgHelper } from '@repository/pgHelper';
import { logger } from '@utils/logger';
import type { QueryResult } from 'pg';
import { ulid } from 'ulid';

/**
 * Repositório para logs de auditoria
 */
export const auditRepository = {
  /**
   * Cria um novo registro de auditoria
   * @param eventType - Tipo do evento
   * @param userId - ID do usuário (opcional)
   * @param userName - Nome do usuário (opcional)
   * @param ipAddress - Endereço IP (opcional)
   * @param userAgent - User-Agent (opcional)
   * @param resource - Recurso afetado (opcional)
   * @param resourceId - ID do recurso (opcional)
   * @param action - Ação realizada (opcional)
   * @param result - Resultado da ação (opcional)
   * @param severity - Severidade do evento
   * @param metadata - Metadados adicionais (opcional)
   * @returns Resultado da query
   */
  async create(
    eventType: string,
    userId?: string,
    userName?: string,
    ipAddress?: string,
    userAgent?: string,
    resource?: string,
    resourceId?: string,
    action?: string,
    result?: string,
    severity = 'info',
    metadata?: Record<string, any>
  ): Promise<QueryResult> {
    try {
      const id = ulid();
      const timestamp = new Date();
      const metadataJson = metadata ? JSON.stringify(metadata) : null;

      const query = `
        INSERT INTO tab_audit_log (
          ulid_audit_log,
          event_type,
          ulid_user,
          user_name,
          ip_address,
          user_agent,
          resource,
          resource_id,
          action,
          result,
          severity,
          metadata,
          created_at
        ) VALUES (
          $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13
        )
        RETURNING *
      `;

      const values = [
        id,
        eventType,
        userId || null,
        userName || null,
        ipAddress || null,
        userAgent || null,
        resource || null,
        resourceId || null,
        action || null,
        result || null,
        severity,
        metadataJson,
        timestamp,
      ];

      return await pgHelper.query(query, values);
    } catch (error) {
      logger.error('Erro ao criar registro de auditoria:', error);
      throw error;
    }
  },

  /**
   * Busca registros de auditoria
   * @param id - ID do registro (opcional)
   * @param eventType - Tipo do evento (opcional)
   * @param userId - ID do usuário (opcional)
   * @param resource - Recurso afetado (opcional)
   * @param action - Ação realizada (opcional)
   * @param severity - Severidade do evento (opcional)
   * @param startDate - Data inicial (opcional)
   * @param endDate - Data final (opcional)
   * @param limit - Limite de registros (opcional)
   * @param offset - Offset para paginação (opcional)
   * @returns Resultado da query
   */
  async read(
    id?: string,
    eventType?: string | string[],
    userId?: string,
    resource?: string,
    action?: string,
    severity?: string | string[],
    startDate?: Date,
    endDate?: Date,
    limit = 50,
    offset = 0
  ): Promise<QueryResult> {
    try {
      let query = `
        SELECT 
          ulid_audit_log as id,
          event_type,
          ulid_user,
          user_name,
          ip_address,
          user_agent,
          resource,
          resource_id,
          action,
          result,
          severity,
          metadata,
          created_at
        FROM tab_audit_log
        WHERE 1=1
      `;

      const queryParams: any[] = [];
      const conditions: string[] = [];

      // Adicionar filtros à consulta
      if (id) {
        conditions.push(`ulid_audit_log = $${queryParams.length + 1}`);
        queryParams.push(id);
      }

      if (eventType) {
        if (Array.isArray(eventType)) {
          conditions.push(`event_type = ANY($${queryParams.length + 1})`);
          queryParams.push(eventType);
        } else {
          conditions.push(`event_type = $${queryParams.length + 1}`);
          queryParams.push(eventType);
        }
      }

      if (userId) {
        conditions.push(`ulid_user = $${queryParams.length + 1}`);
        queryParams.push(userId);
      }

      if (resource) {
        conditions.push(`resource = $${queryParams.length + 1}`);
        queryParams.push(resource);
      }

      if (action) {
        conditions.push(`action = $${queryParams.length + 1}`);
        queryParams.push(action);
      }

      if (severity) {
        if (Array.isArray(severity)) {
          conditions.push(`severity = ANY($${queryParams.length + 1})`);
          queryParams.push(severity);
        } else {
          conditions.push(`severity = $${queryParams.length + 1}`);
          queryParams.push(severity);
        }
      }

      if (startDate) {
        conditions.push(`created_at >= $${queryParams.length + 1}`);
        queryParams.push(startDate);
      }

      if (endDate) {
        conditions.push(`created_at <= $${queryParams.length + 1}`);
        queryParams.push(endDate);
      }

      // Adicionar condições à consulta
      if (conditions.length > 0) {
        query += ` AND ${conditions.join(' AND ')}`;
      }

      // Adicionar ordenação e paginação
      query += ` ORDER BY created_at DESC LIMIT $${queryParams.length + 1} OFFSET $${queryParams.length + 2}`;
      queryParams.push(limit, offset);

      return await pgHelper.query(query, queryParams);
    } catch (error) {
      logger.error('Erro ao buscar registros de auditoria:', error);
      throw error;
    }
  },

  /**
   * Conta o número total de registros de auditoria com os filtros especificados
   * @param eventType - Tipo do evento (opcional)
   * @param userId - ID do usuário (opcional)
   * @param resource - Recurso afetado (opcional)
   * @param action - Ação realizada (opcional)
   * @param severity - Severidade do evento (opcional)
   * @param startDate - Data inicial (opcional)
   * @param endDate - Data final (opcional)
   * @returns Resultado da query
   */
  async count(
    eventType?: string | string[],
    userId?: string,
    resource?: string,
    action?: string,
    severity?: string | string[],
    startDate?: Date,
    endDate?: Date
  ): Promise<number> {
    try {
      let query = `
        SELECT COUNT(*) as total
        FROM tab_audit_log
        WHERE 1=1
      `;

      const queryParams: any[] = [];
      const conditions: string[] = [];

      // Adicionar filtros à consulta
      if (eventType) {
        if (Array.isArray(eventType)) {
          conditions.push(`event_type = ANY($${queryParams.length + 1})`);
          queryParams.push(eventType);
        } else {
          conditions.push(`event_type = $${queryParams.length + 1}`);
          queryParams.push(eventType);
        }
      }

      if (userId) {
        conditions.push(`ulid_user = $${queryParams.length + 1}`);
        queryParams.push(userId);
      }

      if (resource) {
        conditions.push(`resource = $${queryParams.length + 1}`);
        queryParams.push(resource);
      }

      if (action) {
        conditions.push(`action = $${queryParams.length + 1}`);
        queryParams.push(action);
      }

      if (severity) {
        if (Array.isArray(severity)) {
          conditions.push(`severity = ANY($${queryParams.length + 1})`);
          queryParams.push(severity);
        } else {
          conditions.push(`severity = $${queryParams.length + 1}`);
          queryParams.push(severity);
        }
      }

      if (startDate) {
        conditions.push(`created_at >= $${queryParams.length + 1}`);
        queryParams.push(startDate);
      }

      if (endDate) {
        conditions.push(`created_at <= $${queryParams.length + 1}`);
        queryParams.push(endDate);
      }

      // Adicionar condições à consulta
      if (conditions.length > 0) {
        query += ` AND ${conditions.join(' AND ')}`;
      }

      const result = await pgHelper.query(query, queryParams);
      return Number.parseInt(result.rows[0].total, 10);
    } catch (error) {
      logger.error('Erro ao contar registros de auditoria:', error);
      throw error;
    }
  },

  /**
   * Executa a limpeza de logs antigos com base nas configurações de retenção
   * @returns Número de registros excluídos
   */
  async cleanupOldLogs(): Promise<number> {
    try {
      const query = 'SELECT fn_cleanup_audit_logs() as deleted_count';
      const result = await pgHelper.query(query);
      return Number.parseInt(result.rows[0].deleted_count, 10);
    } catch (error) {
      logger.error('Erro ao limpar logs antigos:', error);
      throw error;
    }
  },

  /**
   * Obtém as configurações de auditoria
   * @param eventType - Tipo do evento (opcional)
   * @returns Resultado da query
   */
  async getConfig(eventType?: string): Promise<QueryResult> {
    try {
      let query = `
        SELECT 
          ulid_audit_config as id,
          event_type,
          is_enabled,
          retention_days,
          description,
          created_at,
          updated_at
        FROM tab_audit_config
      `;

      const queryParams: any[] = [];

      if (eventType) {
        query += ' WHERE event_type = $1';
        queryParams.push(eventType);
      }

      query += ' ORDER BY event_type ASC';

      return await pgHelper.query(query, queryParams);
    } catch (error) {
      logger.error('Erro ao obter configurações de auditoria:', error);
      throw error;
    }
  },

  /**
   * Atualiza a configuração de auditoria
   * @param id - ID da configuração
   * @param isEnabled - Se o registro está habilitado
   * @param retentionDays - Dias de retenção
   * @param description - Descrição
   * @returns Resultado da query
   */
  async updateConfig(
    id: string,
    isEnabled?: boolean,
    retentionDays?: number,
    description?: string
  ): Promise<QueryResult> {
    try {
      let query = `
        UPDATE tab_audit_config
        SET updated_at = NOW()
      `;

      const queryParams: any[] = [id];
      const updates: string[] = [];

      if (isEnabled !== undefined) {
        updates.push(`is_enabled = $${queryParams.length + 1}`);
        queryParams.push(isEnabled);
      }

      if (retentionDays !== undefined) {
        updates.push(`retention_days = $${queryParams.length + 1}`);
        queryParams.push(retentionDays);
      }

      if (description !== undefined) {
        updates.push(`description = $${queryParams.length + 1}`);
        queryParams.push(description);
      }

      if (updates.length === 0) {
        throw new Error('Nenhum campo para atualizar');
      }

      query += `, ${updates.join(', ')}`;
      query += ' WHERE ulid_audit_config = $1 RETURNING *';

      return await pgHelper.query(query, queryParams);
    } catch (error) {
      logger.error('Erro ao atualizar configuração de auditoria:', error);
      throw error;
    }
  },
};
