/**
 * Utilitários Responsivos
 *
 * Este arquivo fornece utilitários para trabalhar com layouts responsivos.
 */

import breakpoints from './breakpoints';

/**
 * Retorna um valor com base no breakpoint atual
 * @param {Object} values - Objeto com valores para cada breakpoint
 * @param {string} defaultValue - Valor padrão caso nenhum breakpoint corresponda
 * @returns {*} - Valor correspondente ao breakpoint atual
 */
export function getResponsiveValue(values, defaultValue = null) {
  if (typeof window === 'undefined') {
    // No SSR, retornar o valor para desktop ou o valor padrão
    return values.lg || values.md || defaultValue;
  }

  const currentBreakpoint = breakpoints.getCurrentBreakpoint();

  // Verificar se temos um valor para o breakpoint atual
  if (values[currentBreakpoint]) {
    return values[currentBreakpoint];
  }

  // Caso contrário, procurar o breakpoint mais próximo
  const breakpointOrder = ['xs', 'sm', 'md', 'lg', 'xl', '2xl'];
  const currentIndex = breakpointOrder.indexOf(currentBreakpoint);

  // Procurar um breakpoint menor
  for (let i = currentIndex - 1; i >= 0; i--) {
    const breakpoint = breakpointOrder[i];
    if (values[breakpoint]) {
      return values[breakpoint];
    }
  }

  // Procurar um breakpoint maior
  for (let i = currentIndex + 1; i < breakpointOrder.length; i++) {
    const breakpoint = breakpointOrder[i];
    if (values[breakpoint]) {
      return values[breakpoint];
    }
  }

  // Se nenhum breakpoint corresponder, retornar o valor padrão
  return defaultValue;
}

/**
 * Retorna classes CSS com base no breakpoint atual
 * @param {Object} classes - Objeto com classes para cada breakpoint
 * @returns {string} - Classes CSS para o breakpoint atual
 */
export function getResponsiveClasses(classes) {
  if (!classes) return '';

  // Se classes for uma string, retorná-la diretamente
  if (typeof classes === 'string') {
    return classes;
  }

  // Se classes for um objeto, processar os breakpoints
  const result = [];

  // Adicionar classes base (sem prefixo de breakpoint)
  if (classes.base) {
    result.push(classes.base);
  }

  // Adicionar classes para cada breakpoint
  Object.entries(classes).forEach(([breakpoint, value]) => {
    if (breakpoint !== 'base') {
      result.push(`${breakpoint}:${value}`);
    }
  });

  return result.join(' ');
}

/**
 * Hook para detectar mudanças de breakpoint (para uso no cliente)
 * @param {Function} callback - Função a ser chamada quando o breakpoint mudar
 * @returns {Function} - Função para remover o listener
 */
export function onBreakpointChange(callback) {
  if (typeof window === 'undefined') return () => {};

  const breakpointValues = Object.values(breakpoints.screens);
  const mediaQueries = breakpointValues.map((value) => window.matchMedia(`(min-width: ${value})`));

  const handleChange = () => {
    const currentBreakpoint = breakpoints.getCurrentBreakpoint();
    callback(currentBreakpoint);
  };

  // Adicionar listeners para cada media query
  mediaQueries.forEach((mq) => {
    mq.addEventListener('change', handleChange);
  });

  // Retornar função para remover os listeners
  return () => {
    mediaQueries.forEach((mq) => {
      mq.removeEventListener('change', handleChange);
    });
  };
}

/**
 * Gera estilos CSS para diferentes breakpoints
 * @param {Object} styles - Objeto com estilos para cada breakpoint
 * @returns {Object} - Objeto com estilos CSS
 */
export function getResponsiveStyles(styles) {
  if (!styles) return {};

  // Se styles for um objeto simples (não tem breakpoints), retorná-lo diretamente
  if (
    !styles.base &&
    !styles.xs &&
    !styles.sm &&
    !styles.md &&
    !styles.lg &&
    !styles.xl &&
    !styles['2xl']
  ) {
    return styles;
  }

  // Inicializar com estilos base
  const result = { ...styles.base };

  // Adicionar media queries para cada breakpoint
  Object.entries(styles).forEach(([breakpoint, value]) => {
    if (breakpoint !== 'base' && breakpoints.mediaQueries[breakpoint]) {
      result[breakpoints.mediaQueries[breakpoint]] = value;
    }
  });

  return result;
}

// Exportar utilitários
const ResponsiveUtils = {
  getResponsiveValue,
  getResponsiveClasses,
  onBreakpointChange,
  getResponsiveStyles,
};

export default ResponsiveUtils;
