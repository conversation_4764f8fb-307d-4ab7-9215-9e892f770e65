---
import { actions } from 'astro:actions';
import ControlButtons from '@components/form/ControlButtons.astro';
import InputCheckbox from '@components/form/InputCheckbox.astro';
import InputText from '@components/form/InputText.astro';
import AdminLayout from '@layouts/AdminLayout.astro';
import type { CategoryData } from 'src/database/interfacesHelper';

// Buscar categorias para parent
const categories = await actions.categoryAction.read({
  active: true,
});

const result = Astro.getActionResult(actions.categoryAction.update);
---
<AdminLayout title="Nova Categoria">
  <div class="container mx-auto p-4">
    <h1 class="text-2xl font-bold mb-4">Nova Categoria</h1>
    <div class="toast toast-top toast-center">
      {result?.error && <div class="alert alert-error">
        <span>{result.error}</span>
      </div>}
      {result?.data?.success && <div class="alert alert-success">
        <span>Categoria criada com sucesso!!</span>
      </div>}
    </div>
    <form
      action={actions.categoryAction.update}
      class="space-y-4"
      method="post"
      on:submit={() => {
        result = Astro.getActionResult(actions.categoryAction.update);
      }}
    >
      <InputText 
        label="Nome" 
        name="name" 
        value="" 
        required={true}
      />

      <InputCheckbox 
        label="Ativo" 
        name="active" 
        checked={true}
      />

      <div class="form-control">
        <label class="label">
          <span class="label-text">Categoria Pai</span>
        </label>
        <select 
          name="ulid_parent"
          class="select select-bordered w-full"
        >
          <option value="">Selecione uma categoria pai</option>
          {categories.data?.result?.map((cat: CategoryData) => (
            <option value={cat.ulid_category}>
              {cat.name}
            </option>
          ))}
        </select>
      </div>

      <ControlButtons 
        saveLabel="Criar"
        cancelHref="./"
      />
    </form>
  </div>
</AdminLayout>