/**
 * Repositório para gerenciamento de papéis (roles) no sistema RBAC
 */

import { logger } from '@utils/logger';
import type { QueryResult } from 'pg';
import type { RoleData } from './interfaces/rbacInterfaces';
import { pgHelper } from './pgHelper';

/**
 * Repositório para gerenciamento de papéis (roles)
 */
export const roleRepository = {
  /**
   * Cria um novo papel
   * @param name - Nome do papel
   * @param description - Descrição do papel (opcional)
   * @param isSystem - Se é um papel do sistema (opcional)
   * @returns Resultado da query
   */
  async create(name: string, description?: string, isSystem = false): Promise<QueryResult> {
    try {
      return await pgHelper.query(
        `INSERT INTO tab_role (
          ulid_role,
          name,
          description,
          is_system,
          active,
          created_at,
          updated_at
        ) VALUES ($1, $2, $3, $4, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
        RETURNING *`,
        [pgHelper.generateULID(), name, description, isSystem]
      );
    } catch (error) {
      logger.error('Erro ao criar papel:', error);
      throw error;
    }
  },

  /**
   * Busca papéis com filtros opcionais
   * @param ulid_role - ID do papel (opcional)
   * @param name - Nome do papel (opcional)
   * @param isSystem - Se é um papel do sistema (opcional)
   * @param active - Se o papel está ativo (opcional)
   * @returns Resultado da query
   */
  async read(
    ulid_role?: string,
    name?: string,
    isSystem?: boolean,
    active = true
  ): Promise<QueryResult> {
    try {
      let query = 'SELECT * FROM tab_role WHERE 1=1';
      const params: any[] = [];
      let paramIndex = 1;

      if (ulid_role) {
        query += ` AND ulid_role = $${paramIndex++}`;
        params.push(ulid_role);
      }

      if (name) {
        query += ` AND name ILIKE $${paramIndex++}`;
        params.push(`%${name}%`);
      }

      if (isSystem !== undefined) {
        query += ` AND is_system = $${paramIndex++}`;
        params.push(isSystem);
      }

      if (active !== undefined) {
        query += ` AND active = $${paramIndex++}`;
        params.push(active);
      }

      query += ' ORDER BY name ASC';

      return await pgHelper.query(query, params);
    } catch (error) {
      logger.error('Erro ao buscar papéis:', error);
      throw error;
    }
  },

  /**
   * Atualiza um papel existente
   * @param ulid_role - ID do papel
   * @param name - Nome do papel
   * @param description - Descrição do papel (opcional)
   * @param active - Se o papel está ativo
   * @returns Resultado da query
   */
  async update(
    ulid_role: string,
    name: string,
    description?: string,
    active = true
  ): Promise<QueryResult> {
    try {
      return await pgHelper.query(
        `UPDATE tab_role
         SET name = $1,
             description = $2,
             active = $3,
             updated_at = CURRENT_TIMESTAMP
         WHERE ulid_role = $4
         RETURNING *`,
        [name, description, active, ulid_role]
      );
    } catch (error) {
      logger.error('Erro ao atualizar papel:', error);
      throw error;
    }
  },

  /**
   * Remove um papel (desativa)
   * @param ulid_role - ID do papel
   * @returns Resultado da query
   */
  async delete(ulid_role: string): Promise<QueryResult> {
    try {
      return await pgHelper.query(
        `UPDATE tab_role
         SET active = false,
             updated_at = CURRENT_TIMESTAMP
         WHERE ulid_role = $1
         RETURNING *`,
        [ulid_role]
      );
    } catch (error) {
      logger.error('Erro ao remover papel:', error);
      throw error;
    }
  },

  /**
   * Busca papéis de um usuário
   * @param ulid_user - ID do usuário
   * @param active - Se o papel está ativo (opcional)
   * @returns Resultado da query
   */
  async getUserRoles(ulid_user: string, active = true): Promise<QueryResult> {
    try {
      return await pgHelper.query(
        `SELECT r.*
         FROM tab_role r
         JOIN tab_user_role ur ON r.ulid_role = ur.ulid_role
         WHERE ur.ulid_user = $1
         AND r.active = $2
         ORDER BY r.name ASC`,
        [ulid_user, active]
      );
    } catch (error) {
      logger.error('Erro ao buscar papéis do usuário:', error);
      throw error;
    }
  },

  /**
   * Atribui um papel a um usuário
   * @param ulid_user - ID do usuário
   * @param ulid_role - ID do papel
   * @returns Resultado da query
   */
  async assignRoleToUser(ulid_user: string, ulid_role: string): Promise<QueryResult> {
    try {
      return await pgHelper.query(
        `INSERT INTO tab_user_role (
          ulid_user_role,
          ulid_user,
          ulid_role,
          created_at
        ) VALUES ($1, $2, $3, CURRENT_TIMESTAMP)
        ON CONFLICT (ulid_user, ulid_role) DO NOTHING
        RETURNING *`,
        [pgHelper.generateULID(), ulid_user, ulid_role]
      );
    } catch (error) {
      logger.error('Erro ao atribuir papel ao usuário:', error);
      throw error;
    }
  },

  /**
   * Remove um papel de um usuário
   * @param ulid_user - ID do usuário
   * @param ulid_role - ID do papel
   * @returns Resultado da query
   */
  async removeRoleFromUser(ulid_user: string, ulid_role: string): Promise<QueryResult> {
    try {
      return await pgHelper.query(
        `DELETE FROM tab_user_role
         WHERE ulid_user = $1
         AND ulid_role = $2
         RETURNING *`,
        [ulid_user, ulid_role]
      );
    } catch (error) {
      logger.error('Erro ao remover papel do usuário:', error);
      throw error;
    }
  },

  /**
   * Adiciona uma relação de hierarquia entre papéis
   * @param parentRoleId - ID do papel pai
   * @param childRoleId - ID do papel filho
   * @returns Resultado da query
   */
  async addRoleHierarchy(parentRoleId: string, childRoleId: string): Promise<QueryResult> {
    try {
      return await pgHelper.query(
        `INSERT INTO tab_role_hierarchy (
          ulid_role_hierarchy,
          ulid_parent_role,
          ulid_child_role,
          created_at
        ) VALUES ($1, $2, $3, CURRENT_TIMESTAMP)
        ON CONFLICT (ulid_parent_role, ulid_child_role) DO NOTHING
        RETURNING *`,
        [pgHelper.generateULID(), parentRoleId, childRoleId]
      );
    } catch (error) {
      logger.error('Erro ao adicionar hierarquia de papéis:', error);
      throw error;
    }
  },

  /**
   * Remove uma relação de hierarquia entre papéis
   * @param parentRoleId - ID do papel pai
   * @param childRoleId - ID do papel filho
   * @returns Resultado da query
   */
  async removeRoleHierarchy(parentRoleId: string, childRoleId: string): Promise<QueryResult> {
    try {
      return await pgHelper.query(
        `DELETE FROM tab_role_hierarchy
         WHERE ulid_parent_role = $1
         AND ulid_child_role = $2
         RETURNING *`,
        [parentRoleId, childRoleId]
      );
    } catch (error) {
      logger.error('Erro ao remover hierarquia de papéis:', error);
      throw error;
    }
  },

  /**
   * Busca papéis filhos de um papel
   * @param parentRoleId - ID do papel pai
   * @returns Resultado da query
   */
  async getChildRoles(parentRoleId: string): Promise<QueryResult> {
    try {
      return await pgHelper.query(
        `SELECT r.*
         FROM tab_role r
         JOIN tab_role_hierarchy rh ON r.ulid_role = rh.ulid_child_role
         WHERE rh.ulid_parent_role = $1
         AND r.active = true
         ORDER BY r.name ASC`,
        [parentRoleId]
      );
    } catch (error) {
      logger.error('Erro ao buscar papéis filhos:', error);
      throw error;
    }
  },

  /**
   * Busca papéis pais de um papel
   * @param childRoleId - ID do papel filho
   * @returns Resultado da query
   */
  async getParentRoles(childRoleId: string): Promise<QueryResult> {
    try {
      return await pgHelper.query(
        `SELECT r.*
         FROM tab_role r
         JOIN tab_role_hierarchy rh ON r.ulid_role = rh.ulid_parent_role
         WHERE rh.ulid_child_role = $1
         AND r.active = true
         ORDER BY r.name ASC`,
        [childRoleId]
      );
    } catch (error) {
      logger.error('Erro ao buscar papéis pais:', error);
      throw error;
    }
  },

  /**
   * Busca usuários com um papel específico
   * @param roleId - ID do papel
   * @returns Resultado da query
   */
  async getUsersWithRole(roleId: string): Promise<QueryResult> {
    try {
      return await pgHelper.query(
        `SELECT u.ulid_user, u.name, u.email
         FROM tab_user u
         JOIN tab_user_role ur ON u.ulid_user = ur.ulid_user
         WHERE ur.ulid_role = $1
         AND u.active = true
         ORDER BY u.name ASC`,
        [roleId]
      );
    } catch (error) {
      logger.error('Erro ao buscar usuários com papel:', error);
      throw error;
    }
  },
};
