/**
 * Serviço de dados
 *
 * Este serviço fornece métodos para interagir com dados persistentes,
 * seguindo a arquitetura sem API tradicional.
 */

import { createClient } from '@valkey/client';
import type { ActionResult } from '../utils/server-actions';

// Tipos de dados
export interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  imageUrl: string;
  category: string;
  featured: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface User {
  id: string;
  name: string;
  email: string;
  role: 'admin' | 'customer';
  createdAt: Date;
}

export interface Order {
  id: string;
  userId: string;
  products: Array<{
    productId: string;
    quantity: number;
    price: number;
  }>;
  total: number;
  status: 'pending' | 'paid' | 'shipped' | 'delivered' | 'cancelled';
  createdAt: Date;
  updatedAt: Date;
}

// Configuração do cliente de cache
let cacheClient: any;

/**
 * Inicializa o cliente de cache
 */
export async function initializeCache() {
  if (!cacheClient) {
    cacheClient = createClient({
      url: process.env.CACHE_URL || 'redis://localhost:6379',
    });

    await cacheClient.connect();
  }

  return cacheClient;
}

/**
 * Obtém um item do cache
 *
 * @param key Chave do cache
 * @returns Valor do cache ou null se não existir
 */
export async function getCacheItem<T>(key: string): Promise<T | null> {
  const client = await initializeCache();
  const value = await client.get(key);

  if (!value) {
    return null;
  }

  try {
    return JSON.parse(value);
  } catch (error) {
    console.error('Erro ao parsear item do cache:', error);
    return null;
  }
}

/**
 * Define um item no cache
 *
 * @param key Chave do cache
 * @param value Valor a ser armazenado
 * @param ttl Tempo de vida em segundos (opcional)
 */
export async function setCacheItem<T>(key: string, value: T, ttl?: number): Promise<void> {
  const client = await initializeCache();
  const stringValue = JSON.stringify(value);

  if (ttl) {
    await client.set(key, stringValue, { EX: ttl });
  } else {
    await client.set(key, stringValue);
  }
}

/**
 * Remove um item do cache
 *
 * @param key Chave do cache
 */
export async function removeCacheItem(key: string): Promise<void> {
  const client = await initializeCache();
  await client.del(key);
}

/**
 * Invalida cache por padrão
 *
 * @param pattern Padrão de chaves a serem invalidadas
 */
export async function invalidateCache(pattern: string): Promise<void> {
  const client = await initializeCache();
  const keys = await client.keys(pattern);

  if (keys.length > 0) {
    await client.del(keys);
  }
}

// Serviço de produtos

/**
 * Obtém todos os produtos
 *
 * @param options Opções de filtragem e paginação
 * @returns Lista de produtos e metadados
 */
export async function getProducts(
  options: {
    page?: number;
    limit?: number;
    category?: string;
    featured?: boolean;
    sortBy?: string;
  } = {}
): Promise<{ items: Product[]; total: number; pages: number }> {
  const { page = 1, limit = 10, category, featured, sortBy = 'createdAt' } = options;

  // Construir chave de cache
  const cacheKey = `products:${page}:${limit}:${category || 'all'}:${featured}:${sortBy}`;

  // Tentar obter do cache
  const cachedData = await getCacheItem<{ items: Product[]; total: number; pages: number }>(
    cacheKey
  );

  if (cachedData) {
    return cachedData;
  }

  // Simulação de busca no banco de dados
  // Em uma implementação real, isso seria substituído por uma consulta ao banco
  const allProducts = await getMockProducts();

  // Aplicar filtros
  let filteredProducts = [...allProducts];

  if (category) {
    filteredProducts = filteredProducts.filter((p) => p.category === category);
  }

  if (featured !== undefined) {
    filteredProducts = filteredProducts.filter((p) => p.featured === featured);
  }

  // Aplicar ordenação
  filteredProducts.sort((a, b) => {
    if (sortBy === 'price') {
      return a.price - b.price;
    }
    if (sortBy === 'name') {
      return a.name.localeCompare(b.name);
    }
    // Padrão: createdAt (mais recente primeiro)
    return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
  });

  // Aplicar paginação
  const total = filteredProducts.length;
  const pages = Math.ceil(total / limit);
  const startIndex = (page - 1) * limit;
  const endIndex = startIndex + limit;
  const paginatedProducts = filteredProducts.slice(startIndex, endIndex);

  // Resultado
  const result = {
    items: paginatedProducts,
    total,
    pages,
  };

  // Armazenar no cache por 5 minutos
  await setCacheItem(cacheKey, result, 300);

  return result;
}

/**
 * Obtém um produto pelo ID
 *
 * @param id ID do produto
 * @returns Produto ou null se não encontrado
 */
export async function getProductById(id: string): Promise<Product | null> {
  // Tentar obter do cache
  const cacheKey = `product:${id}`;
  const cachedProduct = await getCacheItem<Product>(cacheKey);

  if (cachedProduct) {
    return cachedProduct;
  }

  // Simulação de busca no banco de dados
  const allProducts = await getMockProducts();
  const product = allProducts.find((p) => p.id === id) || null;

  // Armazenar no cache por 1 hora
  if (product) {
    await setCacheItem(cacheKey, product, 3600);
  }

  return product;
}

/**
 * Cria um novo produto
 *
 * @param data Dados do produto
 * @returns Produto criado
 */
export async function createProduct(
  data: Omit<Product, 'id' | 'createdAt' | 'updatedAt'>
): Promise<Product> {
  // Simulação de criação no banco de dados
  const newProduct: Product = {
    ...data,
    id: `prod_${Date.now()}`,
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  // Em uma implementação real, isso seria uma inserção no banco
  // Aqui apenas simulamos o processo

  // Invalidar cache de produtos
  await invalidateCache('products:*');

  return newProduct;
}

/**
 * Atualiza um produto existente
 *
 * @param id ID do produto
 * @param data Dados para atualização
 * @returns Produto atualizado ou null se não encontrado
 */
export async function updateProduct(
  id: string,
  data: Partial<Omit<Product, 'id' | 'createdAt' | 'updatedAt'>>
): Promise<Product | null> {
  // Em uma implementação real, isso seria uma atualização no banco
  // Aqui apenas simulamos o processo

  // Buscar produto existente
  const product = await getProductById(id);

  if (!product) {
    return null;
  }

  // Atualizar dados
  const updatedProduct: Product = {
    ...product,
    ...data,
    updatedAt: new Date(),
  };

  // Invalidar cache
  await removeCacheItem(`product:${id}`);
  await invalidateCache('products:*');

  return updatedProduct;
}

/**
 * Exclui um produto
 *
 * @param id ID do produto
 * @returns true se excluído com sucesso, false caso contrário
 */
export async function deleteProduct(id: string): Promise<boolean> {
  // Em uma implementação real, isso seria uma exclusão no banco
  // Aqui apenas simulamos o processo

  // Verificar se produto existe
  const product = await getProductById(id);

  if (!product) {
    return false;
  }

  // Invalidar cache
  await removeCacheItem(`product:${id}`);
  await invalidateCache('products:*');

  return true;
}

// Funções auxiliares

/**
 * Obtém produtos mockados para demonstração
 *
 * @returns Lista de produtos
 */
async function getMockProducts(): Promise<Product[]> {
  // Em uma implementação real, isso seria uma consulta ao banco
  return [
    {
      id: 'prod_1',
      name: 'Cartilha de Alfabetização',
      description: 'Cartilha completa para alfabetização de crianças',
      price: 29.9,
      imageUrl: '/images/products/cartilha.jpg',
      category: 'material',
      featured: true,
      createdAt: new Date('2023-01-15'),
      updatedAt: new Date('2023-01-15'),
    },
    {
      id: 'prod_2',
      name: 'Curso de Alfabetização',
      description: 'Curso online completo para alfabetização',
      price: 199.9,
      imageUrl: '/images/products/curso.jpg',
      category: 'curso',
      featured: true,
      createdAt: new Date('2023-02-10'),
      updatedAt: new Date('2023-02-10'),
    },
    {
      id: 'prod_3',
      name: 'Kit de Letras Magnéticas',
      description: 'Kit com letras magnéticas para atividades lúdicas',
      price: 59.9,
      imageUrl: '/images/products/letras.jpg',
      category: 'material',
      featured: false,
      createdAt: new Date('2023-03-05'),
      updatedAt: new Date('2023-03-05'),
    },
  ];
}
