-- Migração para criar índices otimizados
-- Parte da implementação da tarefa 7.2.3 - Otimização de banco de dados

-- Função para verificar se um índice existe
CREATE OR REPLACE FUNCTION index_exists(idx_name TEXT) RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1
    FROM pg_indexes
    WHERE indexname = idx_name
  );
END;
$$ LANGUAGE plpgsql;

-- Índices para tabela de usuários
DO $$
BEGIN
  -- Índice para busca por email (login)
  IF NOT index_exists('idx_user_email') THEN
    CREATE INDEX idx_user_email ON tab_user(email);
    RAISE NOTICE 'Índice idx_user_email criado';
  END IF;

  -- Índice para busca por tipo de usuário
  IF NOT index_exists('idx_user_type') THEN
    CREATE INDEX idx_user_type ON tab_user(ulid_user_type);
    RAISE NOTICE 'Índice idx_user_type criado';
  END IF;

  -- Índice para busca por tipo de escola
  IF NOT index_exists('idx_user_school_type') THEN
    CREATE INDEX idx_user_school_type ON tab_user(ulid_school_type);
    RAISE NOTICE 'Índice idx_user_school_type criado';
  END IF;

  -- Índice para busca por estado e município
  IF NOT index_exists('idx_user_location') THEN
    CREATE INDEX idx_user_location ON tab_user(state, county);
    RAISE NOTICE 'Índice idx_user_location criado';
  END IF;

  -- Índice para busca por data de último login
  IF NOT index_exists('idx_user_last_login') THEN
    CREATE INDEX idx_user_last_login ON tab_user(last_login);
    RAISE NOTICE 'Índice idx_user_last_login criado';
  END IF;
END $$;

-- Índices para tabela de produtos
DO $$
BEGIN
  -- Índice para busca por categoria
  IF NOT index_exists('idx_product_category') THEN
    CREATE INDEX idx_product_category ON tab_product(ulid_category);
    RAISE NOTICE 'Índice idx_product_category criado';
  END IF;

  -- Índice para busca por nome (usando gin para busca parcial)
  IF NOT index_exists('idx_product_name_gin') THEN
    CREATE INDEX idx_product_name_gin ON tab_product USING gin(name gin_trgm_ops);
    RAISE NOTICE 'Índice idx_product_name_gin criado';
  EXCEPTION
    WHEN undefined_object THEN
      -- Criar extensão se não existir
      CREATE EXTENSION IF NOT EXISTS pg_trgm;
      -- Tentar criar o índice novamente
      CREATE INDEX idx_product_name_gin ON tab_product USING gin(name gin_trgm_ops);
      RAISE NOTICE 'Extensão pg_trgm e índice idx_product_name_gin criados';
  END;

  -- Índice para busca por produtos ativos
  IF NOT index_exists('idx_product_active') THEN
    CREATE INDEX idx_product_active ON tab_product(active);
    RAISE NOTICE 'Índice idx_product_active criado';
  END IF;
END $$;

-- Índices para tabela de pedidos
DO $$
BEGIN
  -- Índice para busca por usuário
  IF NOT index_exists('idx_order_user') THEN
    CREATE INDEX idx_order_user ON tab_order(ulid_user);
    RAISE NOTICE 'Índice idx_order_user criado';
  END IF;

  -- Índice para busca por status
  IF NOT index_exists('idx_order_status') THEN
    CREATE INDEX idx_order_status ON tab_order(cod_status);
    RAISE NOTICE 'Índice idx_order_status criado';
  END IF;

  -- Índice para busca por data de criação
  IF NOT index_exists('idx_order_created_at') THEN
    CREATE INDEX idx_order_created_at ON tab_order(created_at);
    RAISE NOTICE 'Índice idx_order_created_at criado';
  END IF;
END $$;

-- Índices para tabela de itens de pedido
DO $$
BEGIN
  -- Índice para busca por pedido
  IF NOT index_exists('idx_order_item_order') THEN
    CREATE INDEX idx_order_item_order ON tab_order_item(ulid_order);
    RAISE NOTICE 'Índice idx_order_item_order criado';
  END IF;

  -- Índice para busca por produto
  IF NOT index_exists('idx_order_item_product') THEN
    CREATE INDEX idx_order_item_product ON tab_order_item(ulid_product);
    RAISE NOTICE 'Índice idx_order_item_product criado';
  END IF;
END $$;

-- Índices para tabela de pagamentos
DO $$
BEGIN
  -- Índice para busca por pedido
  IF NOT index_exists('idx_payment_order') THEN
    CREATE INDEX idx_payment_order ON tab_payment(ulid_order);
    RAISE NOTICE 'Índice idx_payment_order criado';
  END IF;

  -- Índice para busca por tipo de pagamento
  IF NOT index_exists('idx_payment_type') THEN
    CREATE INDEX idx_payment_type ON tab_payment(ulid_payment_type);
    RAISE NOTICE 'Índice idx_payment_type criado';
  END IF;

  -- Índice para busca por status
  IF NOT index_exists('idx_payment_status') THEN
    CREATE INDEX idx_payment_status ON tab_payment(cod_status);
    RAISE NOTICE 'Índice idx_payment_status criado';
  END IF;

  -- Índice para busca por data de criação
  IF NOT index_exists('idx_payment_created_at') THEN
    CREATE INDEX idx_payment_created_at ON tab_payment(created_at);
    RAISE NOTICE 'Índice idx_payment_created_at criado';
  END IF;
END $$;

-- Índices para tabela de posts
DO $$
BEGIN
  -- Índice para busca por usuário
  IF NOT index_exists('idx_post_user') THEN
    CREATE INDEX idx_post_user ON tab_post(ulid_user);
    RAISE NOTICE 'Índice idx_post_user criado';
  END IF;

  -- Índice para busca por produto
  IF NOT index_exists('idx_post_product') THEN
    CREATE INDEX idx_post_product ON tab_post(ulid_product);
    RAISE NOTICE 'Índice idx_post_product criado';
  END IF;

  -- Índice para busca por post pai (comentários)
  IF NOT index_exists('idx_post_parent') THEN
    CREATE INDEX idx_post_parent ON tab_post(ulid_parent);
    RAISE NOTICE 'Índice idx_post_parent criado';
  END IF;

  -- Índice para busca por publicados
  IF NOT index_exists('idx_post_published') THEN
    CREATE INDEX idx_post_published ON tab_post(published);
    RAISE NOTICE 'Índice idx_post_published criado';
  END IF;

  -- Índice para busca por avaliação
  IF NOT index_exists('idx_post_rating') THEN
    CREATE INDEX idx_post_rating ON tab_post(rating_value);
    RAISE NOTICE 'Índice idx_post_rating criado';
  END IF;
END $$;

-- Índices para tabela de tokens
DO $$
BEGIN
  -- Índice para busca por usuário
  IF NOT index_exists('idx_token_user') THEN
    CREATE INDEX idx_token_user ON tab_token(ulid_user);
    RAISE NOTICE 'Índice idx_token_user criado';
  END IF;

  -- Índice para busca por token (hash)
  IF NOT index_exists('idx_token_hash') THEN
    CREATE INDEX idx_token_hash ON tab_token(md5(token));
    RAISE NOTICE 'Índice idx_token_hash criado';
  END IF;

  -- Índice para busca por tipo
  IF NOT index_exists('idx_token_type') THEN
    CREATE INDEX idx_token_type ON tab_token(type);
    RAISE NOTICE 'Índice idx_token_type criado';
  END IF;

  -- Índice para busca por expiração
  IF NOT index_exists('idx_token_expires') THEN
    CREATE INDEX idx_token_expires ON tab_token(expires_at);
    RAISE NOTICE 'Índice idx_token_expires criado';
  END IF;

  -- Índice para busca por revogados
  IF NOT index_exists('idx_token_revoked') THEN
    CREATE INDEX idx_token_revoked ON tab_token(is_revoked);
    RAISE NOTICE 'Índice idx_token_revoked criado';
  END IF;
END $$;

-- Índices para tabela de mensagens de contato
DO $$
BEGIN
  -- Índice para busca por status
  IF NOT index_exists('idx_contact_status') THEN
    CREATE INDEX idx_contact_status ON contact_messages(status);
    RAISE NOTICE 'Índice idx_contact_status criado';
  END IF;

  -- Índice para busca por categoria
  IF NOT index_exists('idx_contact_category') THEN
    CREATE INDEX idx_contact_category ON contact_messages(category);
    RAISE NOTICE 'Índice idx_contact_category criado';
  END IF;

  -- Índice para busca por prioridade
  IF NOT index_exists('idx_contact_priority') THEN
    CREATE INDEX idx_contact_priority ON contact_messages(priority);
    RAISE NOTICE 'Índice idx_contact_priority criado';
  END IF;

  -- Índice para busca por email
  IF NOT index_exists('idx_contact_email') THEN
    CREATE INDEX idx_contact_email ON contact_messages(email);
    RAISE NOTICE 'Índice idx_contact_email criado';
  END IF;
END $$;

-- Índices para tabela de documentos
DO $$
BEGIN
  -- Índice para busca por proprietário
  IF NOT index_exists('idx_document_owner') THEN
    CREATE INDEX idx_document_owner ON documents(owner_id);
    RAISE NOTICE 'Índice idx_document_owner criado';
  END IF;

  -- Índice para busca por visibilidade
  IF NOT index_exists('idx_document_public') THEN
    CREATE INDEX idx_document_public ON documents(is_public);
    RAISE NOTICE 'Índice idx_document_public criado';
  END IF;

  -- Índice para busca por título (usando gin para busca parcial)
  IF NOT index_exists('idx_document_title_gin') THEN
    CREATE INDEX idx_document_title_gin ON documents USING gin(title gin_trgm_ops);
    RAISE NOTICE 'Índice idx_document_title_gin criado';
  EXCEPTION
    WHEN undefined_object THEN
      -- Criar extensão se não existir
      CREATE EXTENSION IF NOT EXISTS pg_trgm;
      -- Tentar criar o índice novamente
      CREATE INDEX idx_document_title_gin ON documents USING gin(title gin_trgm_ops);
      RAISE NOTICE 'Extensão pg_trgm e índice idx_document_title_gin criados';
  END;
END $$;

-- Índices para tabela de notificações
DO $$
BEGIN
  -- Índice para busca por usuário
  IF NOT index_exists('idx_notification_user') THEN
    CREATE INDEX idx_notification_user ON notifications(user_id);
    RAISE NOTICE 'Índice idx_notification_user criado';
  END IF;

  -- Índice para busca por tipo
  IF NOT index_exists('idx_notification_type') THEN
    CREATE INDEX idx_notification_type ON notifications(type);
    RAISE NOTICE 'Índice idx_notification_type criado';
  END IF;

  -- Índice para busca por lidas
  IF NOT index_exists('idx_notification_read') THEN
    CREATE INDEX idx_notification_read ON notifications(is_read);
    RAISE NOTICE 'Índice idx_notification_read criado';
  END IF;

  -- Índice para busca por arquivadas
  IF NOT index_exists('idx_notification_archived') THEN
    CREATE INDEX idx_notification_archived ON notifications(is_archived);
    RAISE NOTICE 'Índice idx_notification_archived criado';
  END IF;
END $$;

-- Índices para tabela de alertas de segurança
DO $$
BEGIN
  -- Índice para busca por tipo de alerta
  IF NOT index_exists('idx_security_alert_type') THEN
    CREATE INDEX idx_security_alert_type ON tab_security_alerts(alert_type);
    RAISE NOTICE 'Índice idx_security_alert_type criado';
  END IF;

  -- Índice para busca por severidade
  IF NOT index_exists('idx_security_alert_severity') THEN
    CREATE INDEX idx_security_alert_severity ON tab_security_alerts(severity);
    RAISE NOTICE 'Índice idx_security_alert_severity criado';
  END IF;

  -- Índice para busca por usuário
  IF NOT index_exists('idx_security_alert_user') THEN
    CREATE INDEX idx_security_alert_user ON tab_security_alerts(ulid_user);
    RAISE NOTICE 'Índice idx_security_alert_user criado';
  END IF;

  -- Índice para busca por resolvidos
  IF NOT index_exists('idx_security_alert_resolved') THEN
    CREATE INDEX idx_security_alert_resolved ON tab_security_alerts(is_resolved);
    RAISE NOTICE 'Índice idx_security_alert_resolved criado';
  END IF;
END $$;

-- Criar estatísticas estendidas para melhorar o planejador de consultas
DO $$
BEGIN
  -- Estatísticas para correlação entre estado e município
  EXECUTE 'CREATE STATISTICS IF NOT EXISTS stat_user_location ON state, county FROM tab_user';
  RAISE NOTICE 'Estatísticas estendidas para localização de usuários criadas';
  
  -- Estatísticas para correlação entre produto e categoria
  EXECUTE 'CREATE STATISTICS IF NOT EXISTS stat_product_category ON ulid_product, ulid_category FROM tab_product';
  RAISE NOTICE 'Estatísticas estendidas para produtos e categorias criadas';
  
  -- Estatísticas para correlação entre pedido e status
  EXECUTE 'CREATE STATISTICS IF NOT EXISTS stat_order_status ON ulid_order, cod_status FROM tab_order';
  RAISE NOTICE 'Estatísticas estendidas para pedidos e status criadas';
EXCEPTION
  WHEN undefined_function THEN
    RAISE NOTICE 'Estatísticas estendidas não suportadas nesta versão do PostgreSQL';
END $$;

-- Atualizar estatísticas do banco de dados
ANALYZE;
