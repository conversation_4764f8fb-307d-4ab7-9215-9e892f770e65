/**
 * A/B Testing Track Conversion API
 *
 * Endpoint para rastrear conversões em testes A/B.
 * Parte da implementação da tarefa 8.9.4 - Marketing digital
 */

import type { APIRoute } from 'astro';
import { ABTestingService } from '../../../../domain/services/ABTestingService';
import { SimpleABTestingService } from '../../../../infrastructure/services/SimpleABTestingService';

// Inicializar serviço de testes A/B
const abTestingService: ABTestingService = new SimpleABTestingService();
await abTestingService.initialize();

export const POST: APIRoute = async ({ request }) => {
  try {
    // Obter dados do corpo da requisição
    const body = await request.json();

    // Validar dados
    if (!body.goalId) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Parâmetro obrigatório: goalId',
        }),
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    // Rastrear conversão
    const success = await abTestingService.trackConversion(body.goalId, body.userId, body.value);

    if (!success) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Erro ao rastrear conversão',
        }),
        {
          status: 500,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    return new Response(
      JSON.stringify({
        success: true,
        message: 'Conversão rastreada com sucesso',
      }),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  } catch (error) {
    console.error('Erro ao processar requisição de conversão de teste A/B:', error);

    return new Response(
      JSON.stringify({
        success: false,
        error: 'Erro ao processar requisição',
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  }
};
