# build output
dist/
# generated types
.astro/

# dependencies
node_modules/

# logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# environment variables
.env
.env.production

# macOS-specific files
.DS_Store

# editor settings
.idea/
.vscode/*
!.vscode/extensions.json
!.vscode/settings.json
!.vscode/launch.json

# linting and formatting
.eslintcache
.stylelintcache
.prettierignore
.prettierrc.backup

# testing
coverage/
.nyc_output/
