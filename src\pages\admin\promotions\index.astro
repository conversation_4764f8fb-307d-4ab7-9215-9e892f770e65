---
import { PageTransition } from '../../../components/transitions';
import DaisyButton from '../../../components/ui/DaisyButton.astro';
import DaisyCard from '../../../components/ui/DaisyCard.astro';
import DaisyPagination from '../../../components/ui/DaisyPagination.astro';
import DaisyTabs from '../../../components/ui/DaisyTabs.astro';
import Breadcrumbs from '../../../components/ui/navigation/Breadcrumbs.astro';
/**
 * Página de Gerenciamento de Promoções
 *
 * Interface para gerenciar promoções.
 * Parte da implementação da tarefa 8.4.3 - Promoções
 */
import BaseLayout from '../../../layouts/BaseLayout.astro';
import { Container, Section } from '../../../layouts/grid';

// Título da página
const title = 'Gerenciamento de Promoções';

// Breadcrumbs para a página atual
const breadcrumbItems = [
  { href: '/', label: 'Início' },
  { href: '/admin', label: 'Administração' },
  { label: 'Promoções' },
];

// Obter parâmetros de consulta
const tab = Astro.url.searchParams.get('tab') || 'active';
const page = Astro.url.searchParams.get('page')
  ? Number.parseInt(Astro.url.searchParams.get('page') as string)
  : 1;
const limit = Astro.url.searchParams.get('limit')
  ? Number.parseInt(Astro.url.searchParams.get('limit') as string)
  : 10;
const search = Astro.url.searchParams.get('search') || '';

// Em um cenário real, buscaríamos as promoções do repositório
// Por enquanto, usaremos dados de exemplo
const promotions = [
  {
    id: 'promo-001',
    name: 'Desconto de Verão',
    description: 'Desconto especial para produtos de verão',
    rules: [
      {
        type: 'percentage',
        value: 15,
        target: {
          categoryIds: ['cat-001', 'cat-003'],
        },
        stackable: true,
        priority: 1,
      },
    ],
    startDate: new Date('2023-12-01'),
    endDate: new Date('2024-02-28'),
    isActive: true,
    usageCount: 45,
  },
  {
    id: 'promo-002',
    name: 'Compre 2 Leve 3',
    description: 'Compre 2 produtos e leve 3',
    rules: [
      {
        type: 'buy_x_get_y',
        value: 2,
        secondaryValue: 1,
        target: {
          productIds: ['prod-001', 'prod-002', 'prod-003'],
        },
        stackable: false,
        priority: 2,
      },
    ],
    startDate: new Date('2023-11-15'),
    endDate: new Date('2023-12-15'),
    isActive: true,
    usageCount: 12,
  },
  {
    id: 'promo-003',
    name: 'Frete Grátis',
    description: 'Frete grátis para compras acima de R$ 100',
    rules: [
      {
        type: 'free_shipping',
        value: 0,
        target: {
          minPurchaseAmount: 100,
        },
        stackable: true,
        priority: 0,
      },
    ],
    startDate: new Date('2023-01-01'),
    endDate: new Date('2023-12-31'),
    isActive: true,
    usageCount: 156,
  },
  {
    id: 'promo-004',
    name: 'Pacote Educacional',
    description: 'Desconto especial para o pacote de produtos educacionais',
    rules: [
      {
        type: 'bundle',
        value: 20,
        target: {
          productIds: ['prod-001', 'prod-002', 'prod-004'],
        },
        stackable: false,
        priority: 3,
      },
    ],
    startDate: new Date('2024-01-01'),
    endDate: new Date('2024-03-31'),
    isActive: false,
    usageCount: 0,
  },
  {
    id: 'promo-005',
    name: 'Desconto para Novos Clientes',
    description: 'Desconto de 10% para novos clientes',
    rules: [
      {
        type: 'percentage',
        value: 10,
        target: {},
        stackable: true,
        priority: 1,
      },
    ],
    restrictions: {
      newCustomersOnly: true,
    },
    startDate: new Date('2023-01-01'),
    endDate: null,
    isActive: true,
    usageCount: 78,
  },
];

// Filtrar promoções com base na aba selecionada
const now = new Date();
let filteredPromotions = [...promotions];

switch (tab) {
  case 'active':
    filteredPromotions = promotions.filter(
      (promo) =>
        promo.isActive &&
        (!promo.startDate || promo.startDate <= now) &&
        (!promo.endDate || promo.endDate >= now)
    );
    break;
  case 'upcoming':
    filteredPromotions = promotions.filter(
      (promo) => promo.isActive && promo.startDate && promo.startDate > now
    );
    break;
  case 'expired':
    filteredPromotions = promotions.filter((promo) => promo.endDate && promo.endDate < now);
    break;
  case 'inactive':
    filteredPromotions = promotions.filter((promo) => !promo.isActive);
    break;
  case 'all':
    // Todos as promoções
    break;
}

// Filtrar por busca, se fornecida
if (search) {
  const searchLower = search.toLowerCase();
  filteredPromotions = filteredPromotions.filter(
    (promo) =>
      promo.name.toLowerCase().includes(searchLower) ||
      promo.description?.toLowerCase().includes(searchLower)
  );
}

// Paginação
const totalPromotions = filteredPromotions.length;
const totalPages = Math.ceil(totalPromotions / limit);
const startIndex = (page - 1) * limit;
const endIndex = startIndex + limit;
const paginatedPromotions = filteredPromotions.slice(startIndex, endIndex);

// Estatísticas
const stats = {
  total: promotions.length,
  active: promotions.filter(
    (promo) =>
      promo.isActive &&
      (!promo.startDate || promo.startDate <= now) &&
      (!promo.endDate || promo.endDate >= now)
  ).length,
  upcoming: promotions.filter((promo) => promo.isActive && promo.startDate && promo.startDate > now)
    .length,
  expired: promotions.filter((promo) => promo.endDate && promo.endDate < now).length,
  inactive: promotions.filter((promo) => !promo.isActive).length,
};

// Abas
const tabs = [
  { id: 'active', label: `Ativas (${stats.active})`, content: '' },
  { id: 'upcoming', label: `Agendadas (${stats.upcoming})`, content: '' },
  { id: 'expired', label: `Expiradas (${stats.expired})`, content: '' },
  { id: 'inactive', label: `Inativas (${stats.inactive})`, content: '' },
  { id: 'all', label: `Todas (${stats.total})`, content: '' },
];

// Função para formatar data
const formatDate = (date: Date | null): string => {
  if (!date) return 'Sem data';
  return date.toLocaleDateString('pt-BR');
};

// Função para obter texto do tipo de promoção
const getPromotionTypeText = (type: string): string => {
  switch (type) {
    case 'percentage':
      return 'Percentual';
    case 'fixed_amount':
      return 'Valor Fixo';
    case 'buy_x_get_y':
      return 'Compre X Leve Y';
    case 'bundle':
      return 'Pacote';
    case 'free_shipping':
      return 'Frete Grátis';
    default:
      return type;
  }
};
---

<BaseLayout title={title} enableViewTransitions={true} transitionType="fade">
  <Container>
    <PageTransition type="fade" duration="0.5s">
      <Section>
        <Breadcrumbs items={breadcrumbItems} class="mb-6" />
        
        <div class="flex justify-between items-center mb-6">
          <h1 class="text-3xl font-bold">{title}</h1>
          
          <div class="flex gap-2">
            <DaisyButton 
              href="/admin/promotions/create" 
              variant="primary" 
              icon="plus"
              text="Nova Promoção"
            />
            
            <DaisyButton 
              href="/admin/banners" 
              variant="outline" 
              icon="image"
              text="Banners Promocionais"
            />
          </div>
        </div>
        
        <DaisyCard class="mb-8">
          <div class="p-4">
            <h2 class="text-xl font-bold mb-4">Filtros</h2>
            
            <form class="flex flex-col md:flex-row gap-4">
              <div class="form-control flex-1">
                <label class="label">
                  <span class="label-text">Buscar</span>
                </label>
                <div class="relative">
                  <input 
                    type="text" 
                    name="search" 
                    placeholder="Nome ou descrição..." 
                    class="input input-bordered w-full pr-10" 
                    value={search}
                  />
                  <button type="submit" class="absolute right-2 top-1/2 -translate-y-1/2 btn btn-ghost btn-sm btn-circle">
                    <i class="icon icon-search"></i>
                  </button>
                </div>
              </div>
              
              <input type="hidden" name="tab" value={tab} />
              
              <div class="form-control md:w-auto mt-8">
                <button type="submit" class="btn btn-primary">
                  <i class="icon icon-filter mr-2"></i>
                  Filtrar
                </button>
              </div>
            </form>
          </div>
        </DaisyCard>
        
        <div class="tabs-container">
          <div class="tabs tabs-boxed mb-6">
            {tabs.map(t => (
              <a 
                href={`/admin/promotions?tab=${t.id}${search ? `&search=${search}` : ''}`} 
                class={`tab ${t.id === tab ? 'tab-active' : ''}`}
              >
                {t.label}
              </a>
            ))}
          </div>
          
          <div class="tab-content">
            {paginatedPromotions.length > 0 ? (
              <div class="overflow-x-auto">
                <table class="table table-zebra w-full">
                  <thead>
                    <tr>
                      <th>Nome</th>
                      <th>Tipo</th>
                      <th>Valor</th>
                      <th>Período</th>
                      <th>Usos</th>
                      <th>Status</th>
                      <th>Ações</th>
                    </tr>
                  </thead>
                  <tbody>
                    {paginatedPromotions.map(promo => (
                      <tr>
                        <td>
                          <div class="font-bold">{promo.name}</div>
                          {promo.description && (
                            <div class="text-sm text-gray-500">{promo.description}</div>
                          )}
                        </td>
                        <td>
                          {promo.rules.map((rule, index) => (
                            <div class={index > 0 ? 'mt-1' : ''}>
                              {getPromotionTypeText(rule.type)}
                            </div>
                          ))}
                        </td>
                        <td>
                          {promo.rules.map((rule, index) => (
                            <div class={index > 0 ? 'mt-1' : ''}>
                              {rule.type === 'percentage' ? `${rule.value}%` : 
                               rule.type === 'fixed_amount' ? `R$ ${rule.value.toFixed(2)}` :
                               rule.type === 'buy_x_get_y' ? `${rule.value}+${rule.secondaryValue || 1}` :
                               rule.type === 'bundle' ? `${rule.value}%` :
                               'Grátis'}
                            </div>
                          ))}
                        </td>
                        <td>
                          <div>
                            <span class="font-medium">Início:</span> {formatDate(promo.startDate)}
                          </div>
                          <div>
                            <span class="font-medium">Fim:</span> {formatDate(promo.endDate)}
                          </div>
                        </td>
                        <td class="text-center">{promo.usageCount}</td>
                        <td>
                          {!promo.isActive ? (
                            <span class="badge badge-ghost">Inativa</span>
                          ) : promo.startDate && promo.startDate > now ? (
                            <span class="badge badge-warning">Agendada</span>
                          ) : promo.endDate && promo.endDate < now ? (
                            <span class="badge badge-error">Expirada</span>
                          ) : (
                            <span class="badge badge-success">Ativa</span>
                          )}
                        </td>
                        <td>
                          <div class="flex gap-1">
                            <a href={`/admin/promotions/edit/${promo.id}`} class="btn btn-sm btn-ghost btn-square">
                              <i class="icon icon-edit"></i>
                            </a>
                            
                            {promo.isActive ? (
                              <a href={`/admin/promotions/deactivate/${promo.id}`} class="btn btn-sm btn-ghost btn-square text-error">
                                <i class="icon icon-x-circle"></i>
                              </a>
                            ) : (
                              <a href={`/admin/promotions/activate/${promo.id}`} class="btn btn-sm btn-ghost btn-square text-success">
                                <i class="icon icon-check-circle"></i>
                              </a>
                            )}
                            
                            <a href={`/admin/promotions/delete/${promo.id}`} class="btn btn-sm btn-ghost btn-square text-error">
                              <i class="icon icon-trash"></i>
                            </a>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <div class="text-center py-12 bg-base-200 rounded-lg">
                <div class="text-4xl text-gray-300 mb-4">
                  <i class="icon icon-tag"></i>
                </div>
                <p class="text-gray-500">Nenhuma promoção encontrada nesta categoria.</p>
                <a href="/admin/promotions/create" class="btn btn-primary mt-4">
                  Criar Nova Promoção
                </a>
              </div>
            )}
            
            {totalPages > 1 && (
              <div class="flex justify-center mt-8">
                <DaisyPagination
                  currentPage={page}
                  totalPages={totalPages}
                  baseUrl="/admin/promotions"
                  queryParams={{ tab, search }}
                />
              </div>
            )}
          </div>
        </div>
      </Section>
    </PageTransition>
  </Container>
</BaseLayout>

<script>
  // Script para atualizar a aba ativa quando o usuário muda de aba
  document.addEventListener('DOMContentLoaded', () => {
    const tabLinks = document.querySelectorAll('.tabs .tab');
    const tabInput = document.querySelector('input[name="tab"]') as HTMLInputElement;
    
    tabLinks.forEach(link => {
      link.addEventListener('click', () => {
        const url = new URL(link.getAttribute('href') || '', window.location.origin);
        tabInput.value = url.searchParams.get('tab') || 'active';
      });
    });
  });
</script>
