---
import { processPaymentAction } from '@actions/processPaymentAction';
import FormBase from '@components/form/FormBase.astro';
import { queryHelper } from '@db/queryHelper';
import Layout from '@layouts/Layout.astro';
import { PaymentType } from '@services/paymentService';

// Obter ID do pedido da URL
const { ulid_order } = Astro.params;

// Verificar se o ID do pedido foi fornecido
if (!ulid_order) {
  return Astro.redirect('/404');
}

// Buscar dados do pedido
const orderResult = await queryHelper.queryOne(
  `SELECT o.*, u.name as user_name, u.email as user_email
   FROM tab_order o
   JOIN tab_user u ON o.ulid_user = u.ulid_user
   WHERE o.ulid_order = $1`,
  [ulid_order]
);

// Verificar se o pedido existe
if (!orderResult) {
  return Astro.redirect('/404');
}

// Buscar itens do pedido
const orderItems = await queryHelper.queryAll(
  `SELECT oi.*, p.name as product_name, p.description as product_description
   FROM tab_order_item oi
   JOIN tab_product p ON oi.ulid_product = p.ulid_product
   WHERE oi.ulid_order = $1`,
  [ulid_order]
);

// Buscar tipos de pagamento disponíveis
const paymentTypes = await queryHelper.queryAll(
  'SELECT * FROM tab_payment_type WHERE active = true ORDER BY type',
  []
);

// Obter resultado da action (se houver)
const actionResult = Astro.getActionResult();
---

<Layout title="Checkout">
  <div class="container mx-auto p-4">
    <h1 class="text-2xl font-bold mb-4">Checkout</h1>
    
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
      <!-- Resumo do pedido -->
      <div class="md:col-span-2">
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
          <h2 class="text-xl font-semibold mb-4">Resumo do Pedido</h2>
          
          <div class="overflow-x-auto">
            <table class="table w-full">
              <thead>
                <tr>
                  <th>Produto</th>
                  <th>Quantidade</th>
                  <th>Preço</th>
                  <th>Subtotal</th>
                </tr>
              </thead>
              <tbody>
                {orderItems.map((item) => (
                  <tr>
                    <td>{item.product_name}</td>
                    <td>{item.qty}</td>
                    <td>R$ {item.price.toFixed(2)}</td>
                    <td>R$ {(item.qty * item.price).toFixed(2)}</td>
                  </tr>
                ))}
              </tbody>
              <tfoot>
                <tr>
                  <td colspan="3" class="text-right font-bold">Total:</td>
                  <td class="font-bold">R$ {orderResult.total.toFixed(2)}</td>
                </tr>
              </tfoot>
            </table>
          </div>
        </div>
      </div>
      
      <!-- Formulário de pagamento -->
      <div class="md:col-span-1">
        <div class="bg-white rounded-lg shadow-md p-6">
          <h2 class="text-xl font-semibold mb-4">Pagamento</h2>
          
          {actionResult && actionResult.success && (
            <div class="alert alert-success mb-4">
              <span>Pagamento processado com sucesso!</span>
            </div>
          )}
          
          {actionResult && !actionResult.success && (
            <div class="alert alert-error mb-4">
              <span>{actionResult.error.message}</span>
            </div>
          )}
          
          <FormBase action={processPaymentAction} csrfProtection={true}>
            <!-- Campos ocultos -->
            <input type="hidden" name="orderId" value={ulid_order} />
            <input type="hidden" name="userId" value={orderResult.ulid_user} />
            <input type="hidden" name="value" value={orderResult.total} />
            
            <!-- Tipo de pagamento -->
            <div class="form-control mb-4">
              <label class="label" for="paymentType">
                <span class="label-text">Forma de Pagamento</span>
              </label>
              <select
                id="paymentType"
                name="paymentType"
                class="select select-bordered w-full"
                required
              >
                <option value="" disabled selected>Selecione uma forma de pagamento</option>
                {paymentTypes.map((type) => (
                  <option value={type.type.toLowerCase()}>{type.type}</option>
                ))}
              </select>
            </div>
            
            <!-- Seção PIX (exibida apenas quando PIX for selecionado) -->
            <div id="pixSection" class="hidden">
              <div class="alert alert-info mb-4">
                <span>Ao confirmar, você receberá um QR Code para pagamento via PIX.</span>
              </div>
            </div>
            
            <!-- Seção Cartão de Crédito (exibida apenas quando Cartão for selecionado) -->
            <div id="creditCardSection" class="hidden">
              <div class="form-control mb-4">
                <label class="label" for="customerName">
                  <span class="label-text">Nome no Cartão</span>
                </label>
                <input
                  type="text"
                  id="customerName"
                  name="customerName"
                  class="input input-bordered"
                />
              </div>
              
              <div class="form-control mb-4">
                <label class="label" for="customerCpf">
                  <span class="label-text">CPF</span>
                </label>
                <input
                  type="text"
                  id="customerCpf"
                  name="customerCpf"
                  class="input input-bordered"
                />
              </div>
              
              <div class="form-control mb-4">
                <label class="label" for="customerEmail">
                  <span class="label-text">Email</span>
                </label>
                <input
                  type="email"
                  id="customerEmail"
                  name="customerEmail"
                  class="input input-bordered"
                  value={orderResult.user_email}
                />
              </div>
              
              <div class="form-control mb-4">
                <label class="label" for="customerBirthDate">
                  <span class="label-text">Data de Nascimento</span>
                </label>
                <input
                  type="date"
                  id="customerBirthDate"
                  name="customerBirthDate"
                  class="input input-bordered"
                />
              </div>
              
              <div class="form-control mb-4">
                <label class="label" for="customerPhone">
                  <span class="label-text">Telefone</span>
                </label>
                <input
                  type="tel"
                  id="customerPhone"
                  name="customerPhone"
                  class="input input-bordered"
                />
              </div>
              
              <div class="form-control mb-4">
                <label class="label" for="cardNumber">
                  <span class="label-text">Número do Cartão</span>
                </label>
                <input
                  type="text"
                  id="cardNumber"
                  class="input input-bordered"
                />
              </div>
              
              <div class="grid grid-cols-2 gap-4">
                <div class="form-control mb-4">
                  <label class="label" for="cardExpiry">
                    <span class="label-text">Validade</span>
                  </label>
                  <input
                    type="text"
                    id="cardExpiry"
                    placeholder="MM/AA"
                    class="input input-bordered"
                  />
                </div>
                
                <div class="form-control mb-4">
                  <label class="label" for="cardCvc">
                    <span class="label-text">CVC</span>
                  </label>
                  <input
                    type="text"
                    id="cardCvc"
                    class="input input-bordered"
                  />
                </div>
              </div>
              
              <input type="hidden" id="paymentToken" name="paymentToken" />
              
              <div class="form-control mb-4">
                <label class="label" for="installments">
                  <span class="label-text">Parcelas</span>
                </label>
                <select
                  id="installments"
                  name="installments"
                  class="select select-bordered w-full"
                >
                  <option value="1">1x de R$ {orderResult.total.toFixed(2)}</option>
                  <option value="2">2x de R$ {(orderResult.total / 2).toFixed(2)}</option>
                  <option value="3">3x de R$ {(orderResult.total / 3).toFixed(2)}</option>
                </select>
              </div>
            </div>
            
            <!-- Seção Boleto (exibida apenas quando Boleto for selecionado) -->
            <div id="billetSection" class="hidden">
              <div class="form-control mb-4">
                <label class="label" for="customerName">
                  <span class="label-text">Nome Completo</span>
                </label>
                <input
                  type="text"
                  id="customerName"
                  name="customerName"
                  class="input input-bordered"
                  value={orderResult.user_name}
                />
              </div>
              
              <div class="form-control mb-4">
                <label class="label" for="customerCpf">
                  <span class="label-text">CPF</span>
                </label>
                <input
                  type="text"
                  id="customerCpf"
                  name="customerCpf"
                  class="input input-bordered"
                />
              </div>
              
              <div class="form-control mb-4">
                <label class="label" for="customerEmail">
                  <span class="label-text">Email</span>
                </label>
                <input
                  type="email"
                  id="customerEmail"
                  name="customerEmail"
                  class="input input-bordered"
                  value={orderResult.user_email}
                />
              </div>
              
              <div class="form-control mb-4">
                <label class="label" for="expirationDate">
                  <span class="label-text">Data de Vencimento</span>
                </label>
                <input
                  type="date"
                  id="expirationDate"
                  name="expirationDate"
                  class="input input-bordered"
                />
              </div>
              
              <div class="alert alert-info mb-4">
                <span>O boleto será enviado para o seu email após a confirmação.</span>
              </div>
            </div>
            
            <button type="submit" class="btn btn-primary w-full">Finalizar Pagamento</button>
          </FormBase>
        </div>
      </div>
    </div>
  </div>
</Layout>

<script>
  // Controlar exibição das seções de pagamento
  const paymentTypeSelect = document.getElementById('paymentType');
  const pixSection = document.getElementById('pixSection');
  const creditCardSection = document.getElementById('creditCardSection');
  const billetSection = document.getElementById('billetSection');
  
  paymentTypeSelect?.addEventListener('change', (e) => {
    const target = e.target as HTMLSelectElement;
    const value = target.value;
    
    // Esconder todas as seções
    pixSection?.classList.add('hidden');
    creditCardSection?.classList.add('hidden');
    billetSection?.classList.add('hidden');
    
    // Exibir seção correspondente
    if (value === 'pix') {
      pixSection?.classList.remove('hidden');
    } else if (value === 'credit_card') {
      creditCardSection?.classList.remove('hidden');
    } else if (value === 'billet') {
      billetSection?.classList.remove('hidden');
    }
  });
  
  // Simulação de tokenização de cartão
  // Em produção, isso seria feito com a SDK do gateway de pagamento
  const cardNumberInput = document.getElementById('cardNumber');
  const cardExpiryInput = document.getElementById('cardExpiry');
  const cardCvcInput = document.getElementById('cardCvc');
  const paymentTokenInput = document.getElementById('paymentToken') as HTMLInputElement;
  
  [cardNumberInput, cardExpiryInput, cardCvcInput].forEach(input => {
    input?.addEventListener('change', () => {
      if (cardNumberInput?.value && cardExpiryInput?.value && cardCvcInput?.value) {
        // Simulação de token (em produção, seria gerado pelo gateway)
        const fakeToken = `tok_${Math.random().toString(36).substring(2, 15)}`;
        paymentTokenInput.value = fakeToken;
      }
    });
  });
</script>
