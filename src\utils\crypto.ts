// src/utils/crypto.ts
import crypto from 'crypto';

/**
 * Gera uma string aleatória com o comprimento especificado
 * @param length - Comprimento da string
 * @returns String aleatória
 */
export function generateRandomString(length: number): string {
  return crypto.randomBytes(Math.ceil(length / 2))
    .toString('hex')
    .slice(0, length);
}

/**
 * Gera um hash HMAC SHA-256 para um payload
 * @param payload - Payload para assinar
 * @param secret - Chave secreta
 * @returns Assinatura HMAC SHA-256 em formato hexadecimal
 */
export function generateHmacSignature(payload: string, secret: string): string {
  return crypto
    .createHmac('sha256', secret)
    .update(payload)
    .digest('hex');
}

/**
 * Verifica se uma assinatura HMAC SHA-256 é válida
 * @param payload - Payload original
 * @param signature - Assinatura a verificar
 * @param secret - Chave secreta
 * @returns true se a assinatura for válida
 */
export function verifyHmacSignature(
  payload: string,
  signature: string,
  secret: string
): boolean {
  const expectedSignature = generateHmacSignature(payload, secret);
  return crypto.timingSafeEqual(
    Buffer.from(signature, 'hex'),
    Buffer.from(expectedSignature, 'hex')
  );
}

/**
 * Gera um timestamp atual em segundos
 * @returns Timestamp atual em segundos
 */
export function getCurrentTimestamp(): number {
  return Math.floor(Date.now() / 1000);
}

/**
 * Verifica se um timestamp está dentro de uma janela de tempo válida
 * @param timestamp - Timestamp a verificar (em segundos)
 * @param maxAgeSeconds - Idade máxima permitida em segundos
 * @returns true se o timestamp for válido
 */
export function isTimestampValid(
  timestamp: number,
  maxAgeSeconds: number = 300 // 5 minutos por padrão
): boolean {
  const currentTime = getCurrentTimestamp();
  return currentTime - timestamp <= maxAgeSeconds;
}
