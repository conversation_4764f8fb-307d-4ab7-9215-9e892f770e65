/**
 * Script de inicialização de cache
 *
 * Este script inicializa o cache e executa o warm-up inicial.
 */

import { cacheWarmupService } from '@services/cacheWarmupService';
import { logger } from '@utils/logger';

/**
 * Inicializa o cache
 */
export async function initializeCache(): Promise<void> {
  try {
    logger.info('Iniciando inicialização de cache');

    // Configurar e inicializar serviço de warm-up
    cacheWarmupService.initialize({
      enabled: true,
      refreshInterval: 60, // 1 hora
      cacheTTL: 3600, // 1 hora
      maxItemsPerType: 100,
    });

    logger.info('Inicialização de cache concluída');
  } catch (error) {
    logger.error('Erro durante inicialização de cache:', error);
  }
}

// Executar inicialização se este arquivo for executado diretamente
if (require.main === module) {
  initializeCache()
    .then(() => {
      logger.info('Script de inicialização de cache concluído');
      process.exit(0);
    })
    .catch((error) => {
      logger.error('Erro no script de inicialização de cache:', error);
      process.exit(1);
    });
}
