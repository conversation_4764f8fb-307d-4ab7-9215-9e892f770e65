/**
 * Camada de compatibilidade para o repositório de produtos
 *
 * Este arquivo mantém a compatibilidade com o código existente que
 * utiliza a estrutura antiga do repositório de produtos.
 *
 * @deprecated Use a nova estrutura de repositórios em src/repositories
 */

import type { QueryResult } from 'pg';
import { pgHelper } from '../../repository/pgHelper';
import { PgProductRepository } from '../implementations/postgres/PgProductRepository';

// Criar instância do novo repositório
const pgProductRepository = new PgProductRepository();

/**
 * Cria um novo produto
 *
 * @param ulid_category ID da categoria
 * @param name Nome do produto
 * @param description Descrição do produto
 * @param file Arquivo/imagem do produto
 * @param price Preço do produto
 * @returns Resultado da consulta
 */
async function create(
  ulid_category: string,
  name: string,
  description: string,
  file: Buffer,
  price: number
): Promise<QueryResult> {
  const result = await read(undefined, ulid_category, name);
  if (result.rows.length > 0) {
    return result;
  }

  // Usar o novo repositório, mas retornar no formato antigo
  const product = await pgProductRepository.save({
    id: pgHelper.generateULID(),
    name,
    description,
    price,
    category: ulid_category,
    imageUrl: '', // Não temos esse campo no formato antigo
    stock: 0, // Não temos esse campo no formato antigo
    featured: false,
    active: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  });

  // Simular o resultado da consulta para manter compatibilidade
  return {
    rows: [
      {
        ulid_product: product.id,
        ulid_category: product.category,
        name: product.name,
        description: product.description,
        file,
        price: product.price,
        active: product.active,
        created_at: product.createdAt,
        updated_at: product.updatedAt,
      },
    ],
    rowCount: 1,
    command: 'INSERT',
    oid: 0,
    fields: [],
  };
}

/**
 * Busca produtos
 *
 * @param ulid_product ID do produto (opcional)
 * @param ulid_category ID da categoria (opcional)
 * @param name Nome do produto (opcional)
 * @param active Status de ativação (padrão: true)
 * @returns Resultado da consulta
 */
async function read(
  ulid_product?: string,
  ulid_category?: string,
  name?: string,
  active = true
): Promise<QueryResult> {
  // Usar o novo repositório, mas retornar no formato antigo
  let products = [];

  if (ulid_product) {
    const product = await pgProductRepository.findById(ulid_product);
    if (product && product.active === active) {
      products = [product];
    }
  } else {
    const result = await pgProductRepository.findAll({
      category: ulid_category,
      search: name,
      active,
      sortBy: 'createdAt',
      sortOrder: 'desc',
    });
    products = result.items;
  }

  // Mapear para o formato antigo
  const rows = products.map((product) => ({
    ulid_product: product.id,
    ulid_category: product.category,
    name: product.name,
    description: product.description,
    file: Buffer.from([]), // Não temos esse campo no novo formato
    price: product.price,
    active: product.active,
    created_at: product.createdAt,
    updated_at: product.updatedAt,
  }));

  // Simular o resultado da consulta para manter compatibilidade
  return {
    rows,
    rowCount: rows.length,
    command: 'SELECT',
    oid: 0,
    fields: [],
  };
}

/**
 * Atualiza um produto
 *
 * @param ulid_product ID do produto
 * @param ulid_category ID da categoria
 * @param active Status de ativação
 * @param name Nome do produto
 * @param description Descrição do produto
 * @param file Arquivo/imagem do produto
 * @param price Preço do produto
 * @returns Resultado da consulta
 */
async function update(
  ulid_product: string,
  ulid_category: string,
  active: boolean,
  name: string,
  description: string,
  file: Buffer,
  price: number
): Promise<QueryResult> {
  // Usar o novo repositório, mas retornar no formato antigo
  const product = await pgProductRepository.save({
    id: ulid_product,
    name,
    description,
    price,
    category: ulid_category,
    imageUrl: '', // Não temos esse campo no formato antigo
    stock: 0, // Não temos esse campo no formato antigo
    featured: false,
    active,
    createdAt: new Date(),
    updatedAt: new Date(),
  });

  // Simular o resultado da consulta para manter compatibilidade
  return {
    rows: [
      {
        ulid_product: product.id,
        ulid_category: product.category,
        name: product.name,
        description: product.description,
        file,
        price: product.price,
        active: product.active,
        created_at: product.createdAt,
        updated_at: product.updatedAt,
      },
    ],
    rowCount: 1,
    command: 'UPDATE',
    oid: 0,
    fields: [],
  };
}

/**
 * Remove um produto
 *
 * @param ulid_product ID do produto
 * @returns Resultado da consulta
 */
async function deleteByUlid(ulid_product: string): Promise<QueryResult> {
  // Buscar o produto antes de remover para retornar seus dados
  const product = await pgProductRepository.findById(ulid_product);

  if (!product) {
    return {
      rows: [],
      rowCount: 0,
      command: 'DELETE',
      oid: 0,
      fields: [],
    };
  }

  // Remover o produto
  const result = await pgProductRepository.remove(ulid_product);

  // Simular o resultado da consulta para manter compatibilidade
  return {
    rows: result
      ? [
          {
            ulid_product: product.id,
            ulid_category: product.category,
            name: product.name,
            description: product.description,
            file: Buffer.from([]), // Não temos esse campo no novo formato
            price: product.price,
            active: false,
            created_at: product.createdAt,
            updated_at: new Date(),
          },
        ]
      : [],
    rowCount: result ? 1 : 0,
    command: 'DELETE',
    oid: 0,
    fields: [],
  };
}

/**
 * Inativa um produto
 *
 * @param ulid_product ID do produto
 * @returns Resultado da consulta
 */
async function inactivate(ulid_product: string): Promise<QueryResult> {
  // Buscar o produto antes de inativar para retornar seus dados
  const product = await pgProductRepository.findById(ulid_product);

  if (!product) {
    return {
      rows: [],
      rowCount: 0,
      command: 'UPDATE',
      oid: 0,
      fields: [],
    };
  }

  // Inativar o produto (usando o método remove que faz soft delete)
  const result = await pgProductRepository.remove(ulid_product);

  // Simular o resultado da consulta para manter compatibilidade
  return {
    rows: result
      ? [
          {
            ulid_product: product.id,
            ulid_category: product.category,
            name: product.name,
            description: product.description,
            file: Buffer.from([]), // Não temos esse campo no novo formato
            price: product.price,
            active: false,
            created_at: product.createdAt,
            updated_at: new Date(),
          },
        ]
      : [],
    rowCount: result ? 1 : 0,
    command: 'UPDATE',
    oid: 0,
    fields: [],
  };
}

export const productRepository = {
  create,
  read,
  update,
  deleteByUlid,
  inactivate,
};
