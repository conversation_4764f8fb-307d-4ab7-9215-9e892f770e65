/**
 * Script para gerar ícones PWA a partir de um ícone base
 * 
 * Este script utiliza o sharp para redimensionar um ícone base
 * e gerar todos os tamanhos necessários para o PWA.
 * 
 * Uso:
 * node scripts/generate-pwa-icons.js
 */

const fs = require('fs');
const path = require('path');
const sharp = require('sharp');

// Configurações
const SOURCE_ICON = path.join(__dirname, '../src/assets/logo.png');
const OUTPUT_DIR = path.join(__dirname, '../public/icons');
const APPLE_SPLASH_DIR = path.join(OUTPUT_DIR, 'apple-splash');

// Taman<PERSON> de ícones PWA
const PWA_ICON_SIZES = [72, 96, 128, 144, 152, 192, 384, 512];

// Tamanhos de ícones Apple
const APPLE_ICON_SIZES = [180];

// Configurações para Apple Splash Screens
const APPLE_SPLASH_SCREENS = [
  { width: 2048, height: 2732 }, // iPad Pro 12.9"
  { width: 1668, height: 2388 }, // iPad Pro 11"
  { width: 1536, height: 2048 }, // iPad Air 10.5"
  { width: 1242, height: 2688 }, // iPhone XS Max
  { width: 1125, height: 2436 }, // iPhone X/XS
  { width: 828, height: 1792 },  // iPhone XR
  { width: 750, height: 1334 },  // iPhone 8/7/6s/6
  { width: 640, height: 1136 },  // iPhone SE
];

// Cores de fundo para splash screens
const SPLASH_BACKGROUND_COLOR = '#ffffff';
const ICON_PADDING_PERCENT = 30; // Porcentagem de padding ao redor do ícone no splash screen

// Função principal
async function generateIcons() {
  try {
    // Verificar se o ícone fonte existe
    if (!fs.existsSync(SOURCE_ICON)) {
      console.error(`Erro: Ícone fonte não encontrado em ${SOURCE_ICON}`);
      process.exit(1);
    }

    // Criar diretórios de saída se não existirem
    if (!fs.existsSync(OUTPUT_DIR)) {
      fs.mkdirSync(OUTPUT_DIR, { recursive: true });
      console.log(`Diretório criado: ${OUTPUT_DIR}`);
    }

    if (!fs.existsSync(APPLE_SPLASH_DIR)) {
      fs.mkdirSync(APPLE_SPLASH_DIR, { recursive: true });
      console.log(`Diretório criado: ${APPLE_SPLASH_DIR}`);
    }

    // Gerar ícones PWA
    console.log('Gerando ícones PWA...');
    for (const size of PWA_ICON_SIZES) {
      await sharp(SOURCE_ICON)
        .resize(size, size)
        .toFile(path.join(OUTPUT_DIR, `icon-${size}x${size}.png`));
      console.log(`Ícone gerado: icon-${size}x${size}.png`);
    }

    // Gerar ícone Apple Touch
    console.log('Gerando ícones Apple Touch...');
    for (const size of APPLE_ICON_SIZES) {
      await sharp(SOURCE_ICON)
        .resize(size, size)
        .toFile(path.join(OUTPUT_DIR, `apple-touch-icon.png`));
      console.log(`Ícone gerado: apple-touch-icon.png (${size}x${size})`);
    }

    // Gerar favicon
    console.log('Gerando favicon...');
    await sharp(SOURCE_ICON)
      .resize(32, 32)
      .toFile(path.join(OUTPUT_DIR, 'favicon.ico'));
    console.log('Favicon gerado: favicon.ico');

    // Gerar splash screens para Apple
    console.log('Gerando splash screens para Apple...');
    
    // Primeiro, preparar o ícone redimensionado para splash screens
    const iconBuffer = await sharp(SOURCE_ICON)
      .toBuffer();
    
    for (const screen of APPLE_SPLASH_SCREENS) {
      const { width, height } = screen;
      
      // Calcular o tamanho do ícone para o splash screen (com padding)
      const iconSize = Math.min(width, height) * (1 - ICON_PADDING_PERCENT / 100);
      
      // Criar uma imagem com fundo branco
      const splashScreen = sharp({
        create: {
          width,
          height,
          channels: 4,
          background: SPLASH_BACKGROUND_COLOR
        }
      });
      
      // Redimensionar o ícone e centralizá-lo
      const resizedIcon = await sharp(iconBuffer)
        .resize(Math.round(iconSize), Math.round(iconSize))
        .toBuffer();
      
      // Compor a imagem final
      await splashScreen
        .composite([
          {
            input: resizedIcon,
            gravity: 'center'
          }
        ])
        .toFile(path.join(APPLE_SPLASH_DIR, `apple-splash-${width}-${height}.jpg`));
      
      console.log(`Splash screen gerado: apple-splash-${width}-${height}.jpg`);
    }

    console.log('Todos os ícones foram gerados com sucesso!');
  } catch (error) {
    console.error('Erro ao gerar ícones:', error);
    process.exit(1);
  }
}

// Executar a função principal
generateIcons();
