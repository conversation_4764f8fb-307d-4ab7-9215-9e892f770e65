---
import { actions } from 'astro:actions';
import AdminLayout from '@layouts/AdminLayout.astro';
import type { CategoryData } from 'src/database/interfacesHelper';

const name = Astro.url.searchParams.get('query') || undefined;
const result = await actions.categoryAction.read({
  name,
  active: true,
});
---

<AdminLayout title="Categorias">
  <div class="container mx-auto p-4">
    <div class="flex justify-between items-center mb-6">
      <h1 class="text-2xl font-bold">Categorias</h1>
      <a href="./category/new" class="btn btn-primary">Nova Categoria</a>
    </div>

    <form method="get" class="mb-6">
      <div class="flex gap-4">
        <input 
          type="text"
          name="query"
          value={name}
          class="input input-bordered flex-1"
          placeholder="Pesquisar por nome..."
        />
        <button type="submit" class="btn btn-primary">Buscar</button>
      </div>
    </form>

    <div class="overflow-x-auto">
      <table class="table w-full">
        <thead>
          <tr>
            <th>Nome</th>
            <th>Categoria Pai</th>
            <th>Status</th>
            <th>Ações</th>
          </tr>
        </thead>
        <tbody>
          {result.data?.data?.map((category: CategoryData) => (
            <tr>
              <td>{category.name}</td>
              <td>
                {category.owner_name ? (
                  <span class="text-sm">{category.owner_name}</span>
                ) : (
                  <span class="text-sm text-gray-400">Categoria Raiz</span>
                )}
              </td>
              <td>
                <span class={`badge ${category.active ? 'badge-success' : 'badge-error'}`}>
                  {category.active ? 'Ativo' : 'Inativo'}
                </span>
              </td>
              <td class="flex gap-2">
                <a href={`./${category.ulid_category}`} class="btn btn-sm btn-info">
                  Editar
                </a>
                <button 
                  data-id={category.ulid_category}
                  class="btn btn-sm btn-error delete-btn">
                  Excluir
                </button>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  </div>
</AdminLayout>

<script>
  import { actions } from "astro:actions";
  
  document.querySelectorAll('.delete-btn').forEach(btn => {
    btn.addEventListener('click', async (e) => {
      const id = (e.target as HTMLButtonElement).dataset.id;
      if (!id || !confirm('Confirma exclusão?')) return;
      
      try {
        const response = await actions.categoryAction.delete({
          method: "delete",
          ulid_category: id
        });

        if (response.data?.success) {
          window.location.reload();
        } else {
          throw new Error(String(response.data?.error || 'Erro desconhecido'));
        }
      } catch (error) {
        console.error('Erro:', error);
        alert(error instanceof Error ? error.message : 'Erro ao excluir');
      }
    });
  });
</script>