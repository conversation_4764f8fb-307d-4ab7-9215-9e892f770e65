/**
 * Serviço para gerenciamento de autenticação de dois fatores
 *
 * Este serviço implementa a lógica de negócio para configuração, ativação
 * e verificação de autenticação de dois fatores (2FA).
 *
 * Parte da implementação da tarefa 6.1.2 - Implementação de 2FA
 */

import { userRepository } from '../repositories/userRepository';
import { userTotpRepository } from '../repositories/userTotpRepository';
import { logger } from '../utils/logger';
import { AuditEventType, AuditSeverity, auditService } from './auditService';
import { totpService } from './totpService';

// Interface para resultado de operações
interface Result<T = void> {
  success: boolean;
  data?: T;
  error?: string;
}

// Interface para configuração de 2FA
interface TwoFactorConfig {
  secret: string;
  uri: string;
  backupCodes: string[];
}

/**
 * Serviço para gerenciamento de autenticação de dois fatores
 */
export const twoFactorAuthService = {
  /**
   * Inicia a configuração de 2FA para um usuário
   * @param userId - ID do usuário
   * @returns Configuração inicial de 2FA
   */
  async setupTwoFactor(userId: string): Promise<Result<TwoFactorConfig>> {
    try {
      // Verificar se o usuário existe
      const user = await userRepository.findById(userId);
      if (!user) {
        return { success: false, error: 'Usuário não encontrado' };
      }

      // Verificar se o usuário já tem 2FA configurado
      const existingConfig = await userTotpRepository.findByUserId(userId);
      if (existingConfig?.verified) {
        return { success: false, error: 'Autenticação de dois fatores já está configurada' };
      }

      // Gerar novo segredo TOTP
      const secret = totpService.generateSecret();

      // Gerar URI para QR code
      const uri = totpService.generateUri(secret, user.email, {
        issuer: 'Efi Educacional',
      });

      // Gerar códigos de backup
      const backupCodes = totpService.generateBackupCodes();

      // Se já existe uma configuração não verificada, atualizá-la
      if (existingConfig) {
        await userTotpRepository.update(userId, {
          secret,
          backup_codes: backupCodes,
          verified: false,
          enabled: false,
        });
      } else {
        // Criar nova configuração
        await userTotpRepository.create({
          user_id: userId,
          secret,
          backup_codes: backupCodes,
          verified: false,
          enabled: false,
        });
      }

      // Registrar evento de auditoria
      await auditService.logEvent({
        eventType: AuditEventType.TWO_FACTOR_SETUP,
        userId,
        userName: user.name,
        resource: 'auth',
        action: 'setup_2fa',
        result: 'success',
        severity: AuditSeverity.INFO,
      });

      return {
        success: true,
        data: {
          secret,
          uri,
          backupCodes,
        },
      };
    } catch (error) {
      logger.error('Erro ao configurar 2FA:', error);
      return { success: false, error: 'Falha ao configurar autenticação de dois fatores' };
    }
  },

  /**
   * Verifica e ativa a configuração de 2FA
   * @param userId - ID do usuário
   * @param token - Código TOTP fornecido pelo usuário
   * @returns Resultado da verificação
   */
  async verifyAndEnableTwoFactor(userId: string, token: string): Promise<Result> {
    try {
      // Buscar configuração TOTP do usuário
      const userTotp = await userTotpRepository.findByUserId(userId);

      if (!userTotp) {
        return { success: false, error: 'Configuração de 2FA não encontrada' };
      }

      if (userTotp.verified) {
        return { success: false, error: 'Autenticação de dois fatores já está ativada' };
      }

      // Verificar o código TOTP
      const verifyResult = totpService.verifyCode(token, userTotp.secret);

      if (!verifyResult.success) {
        // Registrar falha de verificação
        await auditService.logEvent({
          eventType: AuditEventType.TWO_FACTOR_VERIFICATION_FAILED,
          userId,
          resource: 'auth',
          action: 'verify_2fa',
          result: 'failure',
          severity: AuditSeverity.WARNING,
        });

        return { success: false, error: 'Código inválido' };
      }

      // Atualizar configuração como verificada e ativada
      await userTotpRepository.update(userId, {
        verified: true,
        enabled: true,
        last_used_at: new Date(),
      });

      // Registrar evento de auditoria
      await auditService.logEvent({
        eventType: AuditEventType.TWO_FACTOR_ENABLED,
        userId,
        resource: 'auth',
        action: 'enable_2fa',
        result: 'success',
        severity: AuditSeverity.INFO,
      });

      return { success: true };
    } catch (error) {
      logger.error('Erro ao verificar e ativar 2FA:', error);
      return { success: false, error: 'Falha ao verificar e ativar autenticação de dois fatores' };
    }
  },

  /**
   * Verifica um código TOTP durante o login
   * @param userId - ID do usuário
   * @param token - Código TOTP ou código de backup
   * @returns Resultado da verificação
   */
  async verifyTwoFactorToken(userId: string, token: string): Promise<Result> {
    try {
      // Buscar configuração TOTP do usuário
      const userTotp = await userTotpRepository.findByUserId(userId);

      if (!userTotp || !userTotp.enabled || !userTotp.verified) {
        return { success: false, error: 'Autenticação de dois fatores não está ativada' };
      }

      // Verificar se é um código TOTP
      const verifyResult = totpService.verifyCode(token, userTotp.secret);

      if (verifyResult.success) {
        // Atualizar último uso
        await userTotpRepository.update(userId, {
          last_used_at: new Date(),
        });

        // Registrar evento de auditoria
        await auditService.logEvent({
          eventType: AuditEventType.TWO_FACTOR_VERIFICATION_SUCCESS,
          userId,
          resource: 'auth',
          action: 'verify_2fa',
          result: 'success',
          severity: AuditSeverity.INFO,
        });

        return { success: true };
      }

      // Verificar se é um código de backup
      const isValidBackupCode = await userTotpRepository.verifyAndConsumeBackupCode(userId, token);

      if (isValidBackupCode) {
        // Registrar evento de auditoria
        await auditService.logEvent({
          eventType: AuditEventType.TWO_FACTOR_BACKUP_CODE_USED,
          userId,
          resource: 'auth',
          action: 'use_backup_code',
          result: 'success',
          severity: AuditSeverity.WARNING,
        });

        return { success: true };
      }

      // Registrar falha de verificação
      await auditService.logEvent({
        eventType: AuditEventType.TWO_FACTOR_VERIFICATION_FAILED,
        userId,
        resource: 'auth',
        action: 'verify_2fa',
        result: 'failure',
        severity: AuditSeverity.WARNING,
      });

      return { success: false, error: 'Código inválido' };
    } catch (error) {
      logger.error('Erro ao verificar token 2FA:', error);
      return { success: false, error: 'Falha ao verificar código de autenticação' };
    }
  },

  /**
   * Desativa a autenticação de dois fatores
   * @param userId - ID do usuário
   * @param token - Código TOTP para confirmar a desativação
   * @returns Resultado da operação
   */
  async disableTwoFactor(userId: string, token: string): Promise<Result> {
    try {
      // Verificar o token primeiro
      const verifyResult = await this.verifyTwoFactorToken(userId, token);

      if (!verifyResult.success) {
        return verifyResult;
      }

      // Desativar 2FA
      await userTotpRepository.update(userId, {
        enabled: false,
      });

      // Registrar evento de auditoria
      await auditService.logEvent({
        eventType: AuditEventType.TWO_FACTOR_DISABLED,
        userId,
        resource: 'auth',
        action: 'disable_2fa',
        result: 'success',
        severity: AuditSeverity.WARNING,
      });

      return { success: true };
    } catch (error) {
      logger.error('Erro ao desativar 2FA:', error);
      return { success: false, error: 'Falha ao desativar autenticação de dois fatores' };
    }
  },

  /**
   * Gera novos códigos de backup
   * @param userId - ID do usuário
   * @param token - Código TOTP para confirmar a operação
   * @returns Novos códigos de backup
   */
  async regenerateBackupCodes(userId: string, token: string): Promise<Result<string[]>> {
    try {
      // Verificar o token primeiro
      const verifyResult = await this.verifyTwoFactorToken(userId, token);

      if (!verifyResult.success) {
        return verifyResult;
      }

      // Gerar novos códigos de backup
      const backupCodes = totpService.generateBackupCodes();

      // Atualizar códigos de backup
      await userTotpRepository.update(userId, {
        backup_codes: backupCodes,
      });

      // Registrar evento de auditoria
      await auditService.logEvent({
        eventType: AuditEventType.TWO_FACTOR_BACKUP_CODES_REGENERATED,
        userId,
        resource: 'auth',
        action: 'regenerate_backup_codes',
        result: 'success',
        severity: AuditSeverity.INFO,
      });

      return {
        success: true,
        data: backupCodes,
      };
    } catch (error) {
      logger.error('Erro ao regenerar códigos de backup:', error);
      return { success: false, error: 'Falha ao regenerar códigos de backup' };
    }
  },

  /**
   * Verifica se um usuário tem 2FA ativado
   * @param userId - ID do usuário
   * @returns Verdadeiro se o usuário tem 2FA ativado
   */
  async isTwoFactorEnabled(userId: string): Promise<boolean> {
    try {
      const userTotp = await userTotpRepository.findByUserId(userId);
      return !!(userTotp?.enabled && userTotp.verified);
    } catch (error) {
      logger.error('Erro ao verificar status de 2FA:', error);
      return false;
    }
  },
};

export default twoFactorAuthService;
