/**
 * Testes para TokenService
 *
 * Este arquivo contém testes unitários para o serviço de tokens.
 * Parte da implementação da tarefa 9.1.1 - Testes unitários
 */

import jwt from 'jsonwebtoken';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { JwtTokenService } from '../../../../src/domain/services/JwtTokenService';

// Mock do módulo jsonwebtoken
vi.mock('jsonwebtoken', () => ({
  default: {
    sign: vi.fn(),
    verify: vi.fn(),
  },
}));

describe('JwtTokenService', () => {
  let tokenService: JwtTokenService;
  const secretKey = 'test_secret_key';
  const refreshSecretKey = 'test_refresh_secret_key';

  // Dados de teste
  const testPayload = {
    userId: '1',
    role: 'user',
  };

  const testToken = 'test_token';
  const testRefreshToken = 'test_refresh_token';

  beforeEach(() => {
    // Instanciar o serviço de token com as chaves de teste
    tokenService = new JwtTokenService(secretKey, refreshSecretKey);
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('generateToken', () => {
    it('should generate access and refresh tokens', async () => {
      // Configurar mocks
      (jwt.sign as any).mockImplementation((payload, secret, options) => {
        if (secret === secretKey) {
          return testToken;
        }
        if (secret === refreshSecretKey) {
          return testRefreshToken;
        }
        return null;
      });

      // Executar o método de geração de token
      const result = await tokenService.generateToken(testPayload);

      // Verificar se os métodos foram chamados corretamente
      expect(jwt.sign).toHaveBeenCalledTimes(2);
      expect(jwt.sign).toHaveBeenCalledWith(
        expect.objectContaining(testPayload),
        secretKey,
        expect.objectContaining({ expiresIn: '1h' })
      );
      expect(jwt.sign).toHaveBeenCalledWith(
        expect.objectContaining({ userId: testPayload.userId }),
        refreshSecretKey,
        expect.objectContaining({ expiresIn: '7d' })
      );

      // Verificar o resultado
      expect(result).toEqual({
        accessToken: testToken,
        refreshToken: testRefreshToken,
        expiresIn: 3600,
      });
    });
  });

  describe('verifyToken', () => {
    it('should verify and return the payload of a valid token', async () => {
      // Configurar mock
      (jwt.verify as any).mockReturnValue(testPayload);

      // Executar o método de verificação de token
      const result = await tokenService.verifyToken(testToken);

      // Verificar se o método foi chamado corretamente
      expect(jwt.verify).toHaveBeenCalledWith(testToken, secretKey);

      // Verificar o resultado
      expect(result).toEqual(testPayload);
    });

    it('should throw an error for an invalid token', async () => {
      // Configurar mock para lançar erro
      (jwt.verify as any).mockImplementation(() => {
        throw new Error('Invalid token');
      });

      // Verificar se o método lança a exceção esperada
      await expect(tokenService.verifyToken(testToken)).rejects.toThrow('Invalid token');

      // Verificar se o método foi chamado corretamente
      expect(jwt.verify).toHaveBeenCalledWith(testToken, secretKey);
    });
  });

  describe('refreshToken', () => {
    it('should verify refresh token and generate new tokens', async () => {
      // Configurar mocks
      (jwt.verify as any).mockReturnValue({ userId: testPayload.userId });
      (jwt.sign as any).mockImplementation((payload, secret, options) => {
        if (secret === secretKey) {
          return 'new_access_token';
        }
        if (secret === refreshSecretKey) {
          return 'new_refresh_token';
        }
        return null;
      });

      // Executar o método de refresh token
      const result = await tokenService.refreshToken(testRefreshToken, testPayload);

      // Verificar se os métodos foram chamados corretamente
      expect(jwt.verify).toHaveBeenCalledWith(testRefreshToken, refreshSecretKey);
      expect(jwt.sign).toHaveBeenCalledTimes(2);

      // Verificar o resultado
      expect(result).toEqual({
        accessToken: 'new_access_token',
        refreshToken: 'new_refresh_token',
        expiresIn: 3600,
      });
    });

    it('should throw an error for an invalid refresh token', async () => {
      // Configurar mock para lançar erro
      (jwt.verify as any).mockImplementation(() => {
        throw new Error('Invalid refresh token');
      });

      // Verificar se o método lança a exceção esperada
      await expect(tokenService.refreshToken(testRefreshToken, testPayload)).rejects.toThrow(
        'Invalid refresh token'
      );

      // Verificar se o método foi chamado corretamente
      expect(jwt.verify).toHaveBeenCalledWith(testRefreshToken, refreshSecretKey);
    });
  });

  describe('revokeToken', () => {
    it('should revoke a refresh token', async () => {
      // Configurar mock para o método interno de revogação
      vi.spyOn(tokenService as any, 'addToBlacklist').mockResolvedValue(true);

      // Executar o método de revogação de token
      const result = await tokenService.revokeToken(testRefreshToken);

      // Verificar se o método interno foi chamado corretamente
      expect(tokenService.addToBlacklist).toHaveBeenCalledWith(testRefreshToken);

      // Verificar o resultado
      expect(result).toBe(true);
    });
  });
});
