/**
 * Download Fiscal Document Use Case
 *
 * Caso de uso para download de documentos fiscais (XML ou PDF).
 * Parte da implementação da tarefa 8.7.3 - Portal do cliente para documentos fiscais
 */

import { FiscalDocumentRepository } from '../../repositories/FiscalDocumentRepository';
import { FiscalProviderService } from '../../services/FiscalProviderService';

export type DocumentFormat = 'xml' | 'pdf';

export interface DownloadFiscalDocumentRequest {
  documentId: string;
  customerId: string;
  format: DocumentFormat;
}

export interface DownloadFiscalDocumentResponse {
  success: boolean;
  data?: {
    content: string | Buffer;
    filename: string;
    contentType: string;
  };
  error?: string;
}

export class DownloadFiscalDocumentUseCase {
  constructor(
    private fiscalDocumentRepository: FiscalDocumentRepository,
    private fiscalProviderService: FiscalProviderService
  ) {}

  async execute(request: DownloadFiscalDocumentRequest): Promise<DownloadFiscalDocumentResponse> {
    try {
      // Validar os dados de entrada
      if (!request.documentId) {
        return {
          success: false,
          error: 'ID do documento é obrigatório.',
        };
      }

      if (!request.customerId) {
        return {
          success: false,
          error: 'ID do cliente é obrigatório.',
        };
      }

      if (!request.format || !['xml', 'pdf'].includes(request.format)) {
        return {
          success: false,
          error: 'Formato inválido. Formatos suportados: xml, pdf.',
        };
      }

      // Obter o documento fiscal
      const document = await this.fiscalDocumentRepository.getById(request.documentId);

      if (!document) {
        return {
          success: false,
          error: `Documento fiscal com ID ${request.documentId} não encontrado.`,
        };
      }

      // Verificar se o documento pertence ao cliente
      if (document.customer.documentNumber !== request.customerId) {
        return {
          success: false,
          error: 'Acesso negado. Este documento não pertence ao cliente informado.',
        };
      }

      // Verificar se o documento está em um estado que permite download
      if (!['ISSUED', 'CANCELLED'].includes(document.status)) {
        return {
          success: false,
          error: `Documento fiscal com status ${document.status} não está disponível para download.`,
        };
      }

      // Obter o conteúdo do documento de acordo com o formato
      let content: string | Buffer | null = null;
      let contentType: string;
      let filename: string;

      if (request.format === 'xml') {
        // Verificar se o documento tem XML armazenado
        if (document.xmlContent) {
          content = document.xmlContent;
        } else if (document.number && document.series) {
          // Tentar obter o XML do provedor fiscal
          content = await this.fiscalProviderService.getDocumentXml(
            document.type,
            document.number,
            document.series
          );

          // Se obteve o XML, atualizar o documento
          if (content) {
            const updatedDocument = { ...document, xmlContent: content };
            await this.fiscalDocumentRepository.update(updatedDocument);
          }
        }

        contentType = 'application/xml';
        filename = `documento-fiscal-${document.number || document.id}.xml`;
      } else {
        // Verificar se o documento tem URL do PDF
        if (document.pdfUrl) {
          // Retornar a URL do PDF para redirecionamento
          return {
            success: true,
            data: {
              content: document.pdfUrl,
              filename: `documento-fiscal-${document.number || document.id}.pdf`,
              contentType: 'application/pdf',
            },
          };
        }
        if (document.number && document.series) {
          // Tentar obter o PDF do provedor fiscal
          content = await this.fiscalProviderService.getDocumentPdf(
            document.type,
            document.number,
            document.series
          );
        }

        contentType = 'application/pdf';
        filename = `documento-fiscal-${document.number || document.id}.pdf`;
      }

      if (!content) {
        return {
          success: false,
          error: `Não foi possível obter o documento fiscal no formato ${request.format}.`,
        };
      }

      return {
        success: true,
        data: {
          content,
          filename,
          contentType,
        },
      };
    } catch (error) {
      console.error('Erro ao fazer download do documento fiscal:', error);

      return {
        success: false,
        error:
          error instanceof Error
            ? error.message
            : 'Erro desconhecido ao fazer download do documento fiscal.',
      };
    }
  }
}
