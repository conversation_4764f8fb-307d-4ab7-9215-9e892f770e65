/**
 * Notify Fiscal Document Emission Use Case
 *
 * Caso de uso para notificar o cliente sobre a emissão de um documento fiscal.
 * Parte da implementação da tarefa 8.7.3 - Portal do cliente para documentos fiscais
 */

import { FiscalDocument } from '../../entities/FiscalDocument';
import { FiscalDocumentRepository } from '../../repositories/FiscalDocumentRepository';
import { NotificationService } from '../../services/NotificationService';

export interface NotifyFiscalDocumentEmissionRequest {
  documentId: string;
}

export interface NotifyFiscalDocumentEmissionResponse {
  success: boolean;
  data?: {
    notificationSent: boolean;
    notificationType: string;
    recipient: string;
  };
  error?: string;
}

export class NotifyFiscalDocumentEmissionUseCase {
  constructor(
    private fiscalDocumentRepository: FiscalDocumentRepository,
    private notificationService: NotificationService
  ) {}

  async execute(
    request: NotifyFiscalDocumentEmissionRequest
  ): Promise<NotifyFiscalDocumentEmissionResponse> {
    try {
      // Validar os dados de entrada
      if (!request.documentId) {
        return {
          success: false,
          error: 'ID do documento é obrigatório.',
        };
      }

      // Obter o documento fiscal
      const document = await this.fiscalDocumentRepository.getById(request.documentId);

      if (!document) {
        return {
          success: false,
          error: `Documento fiscal com ID ${request.documentId} não encontrado.`,
        };
      }

      // Verificar se o documento está emitido
      if (document.status !== 'ISSUED') {
        return {
          success: false,
          error: `Documento fiscal com status ${document.status} não pode ser notificado.`,
        };
      }

      // Verificar se o cliente tem email
      if (!document.customer.email) {
        return {
          success: false,
          error: 'Cliente não possui email para notificação.',
        };
      }

      // Enviar notificação por email
      const notificationResult = await this.sendEmailNotification(document);

      if (!notificationResult.success) {
        return {
          success: false,
          error: notificationResult.error || 'Erro ao enviar notificação.',
        };
      }

      return {
        success: true,
        data: {
          notificationSent: true,
          notificationType: 'email',
          recipient: document.customer.email,
        },
      };
    } catch (error) {
      console.error('Erro ao notificar emissão de documento fiscal:', error);

      return {
        success: false,
        error:
          error instanceof Error
            ? error.message
            : 'Erro desconhecido ao notificar emissão de documento fiscal.',
      };
    }
  }

  /**
   * Envia notificação por email
   */
  private async sendEmailNotification(
    document: FiscalDocument
  ): Promise<{ success: boolean; error?: string }> {
    try {
      if (!document.customer.email) {
        return {
          success: false,
          error: 'Cliente não possui email para notificação.',
        };
      }

      const documentType = this.getDocumentTypeLabel(document.type);
      const documentNumber = document.number || document.id;

      const subject = `Documento Fiscal Emitido: ${documentType} ${documentNumber}`;

      const body = `
        <h2>Documento Fiscal Emitido</h2>
        <p>Prezado(a) ${document.customer.name},</p>
        <p>Informamos que foi emitido um documento fiscal em seu nome:</p>
        <ul>
          <li><strong>Tipo:</strong> ${documentType}</li>
          <li><strong>Número:</strong> ${documentNumber}</li>
          <li><strong>Série:</strong> ${document.series || 'N/A'}</li>
          <li><strong>Data de Emissão:</strong> ${document.issueDate ? new Date(document.issueDate).toLocaleDateString('pt-BR') : 'N/A'}</li>
          <li><strong>Valor:</strong> ${document.finalValue.toLocaleString('pt-BR', { style: 'currency', currency: 'BRL' })}</li>
        </ul>
        <p>Você pode acessar este documento a qualquer momento através do portal do cliente:</p>
        <p><a href="https://estacao.alfabetizacao.com.br/cliente/documentos-fiscais/${document.id}">Acessar Documento</a></p>
        <p>Em caso de dúvidas, entre em contato com nosso suporte.</p>
        <p>Atenciosamente,<br>Equipe Estação da Alfabetização</p>
      `;

      // Enviar email
      await this.notificationService.sendEmail({
        to: document.customer.email,
        subject,
        body,
        attachments: [],
      });

      return { success: true };
    } catch (error) {
      console.error('Erro ao enviar notificação por email:', error);

      return {
        success: false,
        error:
          error instanceof Error
            ? error.message
            : 'Erro desconhecido ao enviar notificação por email.',
      };
    }
  }

  /**
   * Obtém o label do tipo de documento
   */
  private getDocumentTypeLabel(type: string): string {
    const typeLabels: Record<string, string> = {
      NFE: 'Nota Fiscal Eletrônica',
      NFSE: 'Nota Fiscal de Serviços Eletrônica',
      NFCE: 'Nota Fiscal de Consumidor Eletrônica',
    };

    return typeLabels[type] || type;
  }
}
