---
import CouponCard from '../../../components/coupon/CouponCard.astro';
import { PageTransition } from '../../../components/transitions';
import DaisyButton from '../../../components/ui/DaisyButton.astro';
import DaisyCard from '../../../components/ui/DaisyCard.astro';
import DaisyPagination from '../../../components/ui/DaisyPagination.astro';
import DaisyTabs from '../../../components/ui/DaisyTabs.astro';
import Breadcrumbs from '../../../components/ui/navigation/Breadcrumbs.astro';
/**
 * Página de Gerenciamento de Cupons
 *
 * Interface para gerenciar cupons de desconto.
 * Parte da implementação da tarefa 8.4.1 - Sistema de cupons
 */
import BaseLayout from '../../../layouts/BaseLayout.astro';
import { Container, Section } from '../../../layouts/grid';

// Tí<PERSON>lo da página
const title = 'Gerenciamento de Cupons';

// Breadcrumbs para a página atual
const breadcrumbItems = [
  { href: '/', label: 'Início' },
  { href: '/admin', label: 'Administração' },
  { label: 'Cupons' },
];

// Obter parâmetros de consulta
const tab = Astro.url.searchParams.get('tab') || 'active';
const page = Astro.url.searchParams.get('page')
  ? Number.parseInt(Astro.url.searchParams.get('page') as string)
  : 1;
const limit = Astro.url.searchParams.get('limit')
  ? Number.parseInt(Astro.url.searchParams.get('limit') as string)
  : 12;
const search = Astro.url.searchParams.get('search') || '';

// Em um cenário real, buscaríamos os cupons do repositório
// Por enquanto, usaremos dados de exemplo
const coupons = [
  {
    id: 'coupon-001',
    code: 'WELCOME20',
    type: 'percentage',
    value: 20,
    description: 'Cupom de boas-vindas com 20% de desconto',
    isActive: true,
    usageLimit: 100,
    usageCount: 45,
    startDate: new Date('2023-01-01'),
    endDate: new Date('2023-12-31'),
    minPurchaseAmount: 50,
    maxDiscountAmount: 100,
  },
  {
    id: 'coupon-002',
    code: 'FREESHIP',
    type: 'free_shipping',
    value: 0,
    description: 'Frete grátis para qualquer pedido',
    isActive: true,
    usageLimit: 50,
    usageCount: 50,
    startDate: new Date('2023-01-01'),
    endDate: new Date('2023-06-30'),
  },
  {
    id: 'coupon-003',
    code: 'SUMMER10',
    type: 'fixed_amount',
    value: 10,
    description: 'R$ 10 de desconto na compra',
    isActive: true,
    startDate: new Date('2023-06-01'),
    endDate: new Date('2023-08-31'),
    minPurchaseAmount: 30,
  },
  {
    id: 'coupon-004',
    code: 'BLACKFRIDAY',
    type: 'percentage',
    value: 30,
    description: 'Desconto de 30% na Black Friday',
    isActive: false,
    startDate: new Date('2023-11-24'),
    endDate: new Date('2023-11-26'),
    maxDiscountAmount: 200,
  },
  {
    id: 'coupon-005',
    code: 'NEWCUSTOMER',
    type: 'percentage',
    value: 15,
    description: 'Desconto de 15% para novos clientes',
    isActive: true,
    usageLimit: 1,
    usageCount: 0,
  },
  {
    id: 'coupon-006',
    code: 'FLASH25',
    type: 'percentage',
    value: 25,
    description: 'Promoção relâmpago de 25% de desconto',
    isActive: true,
    startDate: new Date(Date.now() + 86400000), // Amanhã
    endDate: new Date(Date.now() + 86400000 * 3), // Daqui a 3 dias
  },
];

// Filtrar cupons com base na aba selecionada
const now = new Date();
let filteredCoupons = [...coupons];

switch (tab) {
  case 'active':
    filteredCoupons = coupons.filter(
      (coupon) =>
        coupon.isActive &&
        (!coupon.startDate || coupon.startDate <= now) &&
        (!coupon.endDate || coupon.endDate >= now) &&
        (!coupon.usageLimit || coupon.usageCount < coupon.usageLimit)
    );
    break;
  case 'upcoming':
    filteredCoupons = coupons.filter(
      (coupon) => coupon.isActive && coupon.startDate && coupon.startDate > now
    );
    break;
  case 'expired':
    filteredCoupons = coupons.filter(
      (coupon) =>
        (coupon.endDate && coupon.endDate < now) ||
        (coupon.usageLimit && coupon.usageCount >= coupon.usageLimit)
    );
    break;
  case 'inactive':
    filteredCoupons = coupons.filter((coupon) => !coupon.isActive);
    break;
  case 'all':
    // Todos os cupons
    break;
}

// Filtrar por busca, se fornecida
if (search) {
  const searchLower = search.toLowerCase();
  filteredCoupons = filteredCoupons.filter(
    (coupon) =>
      coupon.code.toLowerCase().includes(searchLower) ||
      coupon.description?.toLowerCase().includes(searchLower)
  );
}

// Paginação
const totalCoupons = filteredCoupons.length;
const totalPages = Math.ceil(totalCoupons / limit);
const startIndex = (page - 1) * limit;
const endIndex = startIndex + limit;
const paginatedCoupons = filteredCoupons.slice(startIndex, endIndex);

// Estatísticas
const stats = {
  total: coupons.length,
  active: coupons.filter(
    (coupon) =>
      coupon.isActive &&
      (!coupon.startDate || coupon.startDate <= now) &&
      (!coupon.endDate || coupon.endDate >= now) &&
      (!coupon.usageLimit || coupon.usageCount < coupon.usageLimit)
  ).length,
  upcoming: coupons.filter(
    (coupon) => coupon.isActive && coupon.startDate && coupon.startDate > now
  ).length,
  expired: coupons.filter(
    (coupon) =>
      (coupon.endDate && coupon.endDate < now) ||
      (coupon.usageLimit && coupon.usageCount >= coupon.usageLimit)
  ).length,
  inactive: coupons.filter((coupon) => !coupon.isActive).length,
};

// Abas
const tabs = [
  { id: 'active', label: `Ativos (${stats.active})`, content: '' },
  { id: 'upcoming', label: `Agendados (${stats.upcoming})`, content: '' },
  { id: 'expired', label: `Expirados (${stats.expired})`, content: '' },
  { id: 'inactive', label: `Inativos (${stats.inactive})`, content: '' },
  { id: 'all', label: `Todos (${stats.total})`, content: '' },
];
---

<BaseLayout title={title} enableViewTransitions={true} transitionType="fade">
  <Container>
    <PageTransition type="fade" duration="0.5s">
      <Section>
        <Breadcrumbs items={breadcrumbItems} class="mb-6" />
        
        <div class="flex justify-between items-center mb-6">
          <h1 class="text-3xl font-bold">{title}</h1>
          
          <DaisyButton 
            href="/admin/coupons/create" 
            variant="primary" 
            icon="plus"
            text="Novo Cupom"
          />
        </div>
        
        <DaisyCard class="mb-8">
          <div class="p-4">
            <h2 class="text-xl font-bold mb-4">Filtros</h2>
            
            <form class="flex flex-col md:flex-row gap-4">
              <div class="form-control flex-1">
                <label class="label">
                  <span class="label-text">Buscar</span>
                </label>
                <div class="relative">
                  <input 
                    type="text" 
                    name="search" 
                    placeholder="Código ou descrição..." 
                    class="input input-bordered w-full pr-10" 
                    value={search}
                  />
                  <button type="submit" class="absolute right-2 top-1/2 -translate-y-1/2 btn btn-ghost btn-sm btn-circle">
                    <i class="icon icon-search"></i>
                  </button>
                </div>
              </div>
              
              <input type="hidden" name="tab" value={tab} />
              
              <div class="form-control md:w-auto mt-8">
                <button type="submit" class="btn btn-primary">
                  <i class="icon icon-filter mr-2"></i>
                  Filtrar
                </button>
              </div>
            </form>
          </div>
        </DaisyCard>
        
        <div class="tabs-container">
          <div class="tabs tabs-boxed mb-6">
            {tabs.map(t => (
              <a 
                href={`/admin/coupons?tab=${t.id}${search ? `&search=${search}` : ''}`} 
                class={`tab ${t.id === tab ? 'tab-active' : ''}`}
              >
                {t.label}
              </a>
            ))}
          </div>
          
          <div class="tab-content">
            {paginatedCoupons.length > 0 ? (
              <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {paginatedCoupons.map(coupon => (
                  <CouponCard
                    id={coupon.id}
                    code={coupon.code}
                    type={coupon.type as any}
                    value={coupon.value}
                    description={coupon.description}
                    isActive={coupon.isActive}
                    usageLimit={coupon.usageLimit}
                    usageCount={coupon.usageCount}
                    startDate={coupon.startDate}
                    endDate={coupon.endDate}
                    minPurchaseAmount={coupon.minPurchaseAmount}
                    maxDiscountAmount={coupon.maxDiscountAmount}
                    showActions={true}
                    showCopyButton={true}
                  />
                ))}
              </div>
            ) : (
              <div class="text-center py-12 bg-base-200 rounded-lg">
                <div class="text-4xl text-gray-300 mb-4">
                  <i class="icon icon-ticket"></i>
                </div>
                <p class="text-gray-500">Nenhum cupom encontrado nesta categoria.</p>
                <a href="/admin/coupons/create" class="btn btn-primary mt-4">
                  Criar Novo Cupom
                </a>
              </div>
            )}
            
            {totalPages > 1 && (
              <div class="flex justify-center mt-8">
                <DaisyPagination
                  currentPage={page}
                  totalPages={totalPages}
                  baseUrl="/admin/coupons"
                  queryParams={{ tab, search }}
                />
              </div>
            )}
          </div>
        </div>
      </Section>
    </PageTransition>
  </Container>
</BaseLayout>

<script>
  // Script para atualizar a aba ativa quando o usuário muda de aba
  document.addEventListener('DOMContentLoaded', () => {
    const tabLinks = document.querySelectorAll('.tabs .tab');
    const tabInput = document.querySelector('input[name="tab"]') as HTMLInputElement;
    
    tabLinks.forEach(link => {
      link.addEventListener('click', () => {
        const url = new URL(link.getAttribute('href') || '', window.location.origin);
        tabInput.value = url.searchParams.get('tab') || 'active';
      });
    });
  });
</script>
