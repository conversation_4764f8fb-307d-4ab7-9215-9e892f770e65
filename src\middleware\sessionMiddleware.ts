/**
 * Middleware para gerenciamento de sessões com Valkey
 *
 * Este middleware gerencia sessões de usuários utilizando
 * o Valkey como armazenamento, oferecendo alta performance,
 * persistência e escalabilidade.
 *
 * Características:
 * - Armazenamento de sessões com TTL configurável
 * - Renovação automática de sessões
 * - Suporte para autenticação via JWT
 * - Proteção contra roubo de sessão
 * - Integração com sistema de monitoramento
 */

import { defineMiddleware } from 'astro:middleware';
import { applicationMonitoringService } from '@services/applicationMonitoringService';
import { authJwtService } from '@services/authJwtService';
import { valkeySessionService } from '@services/valkeySessionService';
import { logger } from '@utils/logger';
import type { MiddlewareHandler } from 'astro';

/**
 * Nome do cookie de sessão
 */
const SESSION_COOKIE_NAME = 'session_id';

/**
 * Tempo de vida do cookie de sessão em segundos (24 horas)
 */
const SESSION_COOKIE_TTL = 24 * 60 * 60;

export const onRequest = defineMiddleware(async (context, next) => {
  const { cookies, request } = context;

  try {
    // Obter ID de sessão do cookie
    let sessionId = cookies.get(SESSION_COOKIE_NAME)?.value;
    let session = null;

    // Obter informações da requisição
    const ipAddress =
      request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown';

    const userAgent = request.headers.get('user-agent') || 'unknown';

    // Iniciar timer para métricas de performance
    const startTime = performance.now();

    // Verificar se há uma sessão válida
    if (sessionId) {
      session = await valkeySessionService.get(sessionId);

      if (session) {
        // Verificar se a sessão expirou
        const now = Date.now();
        const isExpired = session.expiresAt && session.expiresAt < now;

        if (isExpired) {
          // Sessão expirada, remover cookie
          cookies.delete(SESSION_COOKIE_NAME, {
            path: '/',
          });
          sessionId = null;
          session = null;

          logger.info('Sessão expirada', {
            ipAddress,
            userAgent: userAgent.substring(0, 100),
          });

          // Registrar métrica
          applicationMonitoringService.incrementCounter('session_expired');
        } else {
          // Verificar se a sessão precisa ser renovada
          if (valkeySessionService.shouldRenewSession(session)) {
            // Renovar sessão
            const newSession = await valkeySessionService.renewSession(
              sessionId,
              ipAddress,
              userAgent
            );

            if (newSession) {
              // Atualizar ID de sessão e dados
              sessionId = newSession.id;
              session = newSession;

              // Atualizar cookie com nova sessão
              cookies.set(SESSION_COOKIE_NAME, sessionId, {
                path: '/',
                httpOnly: true,
                secure: import.meta.env.PROD,
                sameSite: 'strict',
                maxAge: SESSION_COOKIE_TTL,
              });

              logger.info('Sessão renovada automaticamente', {
                userId: session.userId,
                ipAddress,
              });

              // Registrar métrica
              applicationMonitoringService.incrementCounter('session_renewed_auto');
            }
          } else {
            // Apenas atualizar timestamp de última atividade
            await valkeySessionService.touch(sessionId, ipAddress);
          }

          // Verificar segurança da sessão
          if (session.ipAddress && session.ipAddress !== ipAddress) {
            // Possível roubo de sessão - IP diferente
            logger.warn('Possível roubo de sessão detectado - IP diferente', {
              sessionId,
              originalIp: session.ipAddress,
              currentIp: ipAddress,
              userId: session.userId,
            });

            // Registrar métrica
            applicationMonitoringService.incrementCounter('session_security_ip_mismatch');

            // Invalidar sessão em caso de suspeita de roubo
            if (process.env.INVALIDATE_SUSPICIOUS_SESSIONS === 'true') {
              await valkeySessionService.delete(sessionId);
              cookies.delete(SESSION_COOKIE_NAME, { path: '/' });
              sessionId = null;
              session = null;
            }
          }
        }
      } else {
        // Sessão inválida, remover cookie
        cookies.delete(SESSION_COOKIE_NAME, {
          path: '/',
        });
        sessionId = null;

        // Registrar métrica
        applicationMonitoringService.incrementCounter('session_invalid');
      }
    }

    // Verificar se há um token JWT válido, mas sem sessão
    if (!session) {
      const token = cookies.get('access_token')?.value;

      if (token) {
        // Verificar token e obter dados do usuário
        const user = await authJwtService.verifyToken(token, true);

        if (user) {
          // Criar nova sessão
          session = await valkeySessionService.create(
            user.ulid_user,
            user.name,
            user.email,
            {
              isAuthenticated: true,
              roles: user.roles || [],
              permissions: user.permissions || [],
            },
            ipAddress,
            userAgent,
            {
              ttl: SESSION_COOKIE_TTL,
              validateIp: true,
              validateUserAgent: true,
            }
          );

          // Definir cookie de sessão
          cookies.set(SESSION_COOKIE_NAME, session.id, {
            path: '/',
            httpOnly: true,
            secure: import.meta.env.PROD,
            sameSite: 'strict',
            maxAge: SESSION_COOKIE_TTL,
          });

          sessionId = session.id;

          // Registrar métrica
          applicationMonitoringService.incrementCounter('session_created_from_jwt');

          logger.info('Sessão criada a partir de JWT', {
            userId: user.ulid_user,
            ipAddress,
          });
        }
      }
    }

    // Registrar duração do processamento de sessão
    const processingTime = performance.now() - startTime;
    applicationMonitoringService.recordMetric('session_processing_time', processingTime);

    // Disponibilizar sessão para o contexto
    context.locals.session = {
      id: sessionId,
      data: session?.data || {},

      // Método para obter valor da sessão
      get: async (key: string) => {
        if (!sessionId) return null;

        const currentSession = await valkeySessionService.get(sessionId);
        return currentSession?.data?.[key] || null;
      },

      // Método para definir valor na sessão
      set: async <T>(key: string, value: T) => {
        if (!sessionId) {
          // Criar sessão anônima
          const anonSession = await valkeySessionService.create(
            'anonymous',
            'Anonymous',
            '<EMAIL>',
            { [key]: value },
            ipAddress,
            userAgent,
            {
              ttl: SESSION_COOKIE_TTL,
              validateIp: false, // Sessões anônimas não validam IP
            }
          );

          // Definir cookie de sessão
          cookies.set(SESSION_COOKIE_NAME, anonSession.id, {
            path: '/',
            httpOnly: true,
            secure: import.meta.env.PROD,
            sameSite: 'strict',
            maxAge: SESSION_COOKIE_TTL,
          });

          sessionId = anonSession.id;

          // Registrar métrica
          applicationMonitoringService.incrementCounter('session_created_anonymous');

          return true;
        }

        // Atualizar sessão existente
        const updatedData = { [key]: value };
        const success = await valkeySessionService.update(
          sessionId,
          updatedData,
          ipAddress,
          userAgent
        );

        return success;
      },

      // Método para remover valor da sessão
      unset: async (key: string) => {
        if (!sessionId) return false;

        const currentSession = await valkeySessionService.get(sessionId);

        if (!currentSession) return false;

        // Criar novo objeto de dados sem a chave
        const newData = { ...currentSession.data };
        delete newData[key];

        // Atualizar sessão
        const success = await valkeySessionService.update(sessionId, newData, ipAddress, userAgent);

        return success;
      },

      // Método para destruir a sessão
      destroy: async () => {
        if (!sessionId) return false;

        // Excluir sessão
        const success = await valkeySessionService.delete(sessionId);

        // Remover cookie
        cookies.delete(SESSION_COOKIE_NAME, {
          path: '/',
        });

        // Registrar métrica
        applicationMonitoringService.incrementCounter('session_destroyed');

        return success;
      },

      // Método para renovar a sessão manualmente
      renew: async () => {
        if (!sessionId) return false;

        const newSession = await valkeySessionService.renewSession(
          sessionId,
          ipAddress,
          userAgent,
          { ttl: SESSION_COOKIE_TTL }
        );

        if (!newSession) return false;

        // Atualizar cookie com nova sessão
        cookies.set(SESSION_COOKIE_NAME, newSession.id, {
          path: '/',
          httpOnly: true,
          secure: import.meta.env.PROD,
          sameSite: 'strict',
          maxAge: SESSION_COOKIE_TTL,
        });

        sessionId = newSession.id;

        // Registrar métrica
        applicationMonitoringService.incrementCounter('session_renewed_manual');

        return true;
      },
    };

    return next();
  } catch (error) {
    logger.error('Erro no middleware de sessão:', error);

    // Continuar para o próximo middleware ou rota
    return next();
  }
}) satisfies MiddlewareHandler;
