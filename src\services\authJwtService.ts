/**
 * Serviço para autenticação com JWT
 * Implementa funções para login, registro e gerenciamento de sessões
 */

import { type DeviceInfo, tokenRepository } from '@repository/tokenRepository';
import { userRepository } from '@repository/userRepository';
import { logger } from '@utils/logger';
import * as argon2 from 'argon2';
import type { AstroGlobal } from 'astro';
import { type JwtPayload, jwtService } from './jwtService';

// Interface para dados do usuário
interface UserData {
  ulid_user: string;
  name: string;
  email: string;
  is_teacher: boolean;
  ulid_user_type: string;
  ulid_school_type: string;
  password: string;
  state: string;
  county: string;
}

// Serviço de autenticação JWT
export const authJwtService = {
  /**
   * Autentica um usuário e gera tokens JWT
   * @param email - Email do usuário
   * @param password - Senha do usuário
   * @returns Objeto com tokens e dados do usuário ou null
   */
  async authenticate(
    email: string,
    password: string,
    deviceInfo?: DeviceInfo
  ): Promise<{
    accessToken: string;
    refreshToken: string;
    user: Omit<UserData, 'password'>;
  } | null> {
    try {
      // Buscar usuário pelo email
      const userResult = await userRepository.read(
        undefined,
        undefined,
        undefined,
        email,
        undefined,
        undefined,
        undefined,
        true
      );

      // Verificar se o usuário existe
      if (userResult.rowCount === 0) {
        logger.warn(`Tentativa de login com email não cadastrado: ${email}`, {
          email,
          ipAddress: deviceInfo?.ipAddress,
          userAgent: deviceInfo?.userAgent,
        });
        return null;
      }

      const user = userResult.rows[0] as UserData;

      // Verificar senha com argon2
      const validPassword = await argon2.verify(user.password, password);
      if (!validPassword) {
        logger.warn(`Senha incorreta para usuário: ${user.email}`, {
          userId: user.ulid_user,
          ipAddress: deviceInfo?.ipAddress,
          userAgent: deviceInfo?.userAgent,
        });
        return null;
      }

      // Gerar tokens JWT
      const accessToken = jwtService.generateToken({
        sub: user.ulid_user,
        name: user.name,
        email: user.email,
        role: user.ulid_user_type,
        type: 'access',
        device: deviceInfo?.deviceId,
      });

      const refreshToken = jwtService.generateToken({
        sub: user.ulid_user,
        type: 'refresh',
        device: deviceInfo?.deviceId,
      });

      // Calcular data de expiração do refresh token (7 dias)
      const refreshExpires = new Date();
      refreshExpires.setDate(refreshExpires.getDate() + 7);

      // Salvar refresh token no banco de dados
      await tokenRepository.saveToken(
        user.ulid_user,
        refreshToken,
        'refresh',
        refreshExpires,
        deviceInfo
      );

      // Registrar login bem-sucedido
      logger.info(`Login bem-sucedido para usuário: ${user.email}`, {
        userId: user.ulid_user,
        deviceId: deviceInfo?.deviceId,
        ipAddress: deviceInfo?.ipAddress,
        userAgent: deviceInfo?.userAgent,
      });

      // Retornar tokens e dados do usuário (sem a senha)
      const { password: _, ...userWithoutPassword } = user;

      return {
        accessToken,
        refreshToken,
        user: userWithoutPassword,
      };
    } catch (error) {
      console.error('Erro na autenticação JWT:', error);
      return null;
    }
  },

  /**
   * Verifica um token JWT e retorna os dados do usuário
   * @param token - Token JWT
   * @returns Dados do usuário ou null
   */
  async verifyToken(
    token: string,
    updateLastUsed = false
  ): Promise<Omit<UserData, 'password'> | null> {
    try {
      // Verificar token
      const payload = jwtService.verifyToken(token, 'access');
      if (!payload) {
        return null;
      }

      // Verificar se o token está na blacklist
      const tokenResult = await tokenRepository.findToken(token);
      if (tokenResult.rowCount > 0 && tokenResult.rows[0].is_revoked) {
        return null;
      }

      // Atualizar data de último uso se solicitado
      if (updateLastUsed && tokenResult.rowCount > 0) {
        await tokenRepository.updateLastUsed(token);
      }

      // Buscar usuário pelo ID
      const userResult = await userRepository.read(
        payload.sub,
        undefined,
        undefined,
        undefined,
        undefined,
        undefined,
        undefined,
        true
      );

      // Verificar se o usuário existe
      if (userResult.rowCount === 0) {
        return null;
      }

      const user = userResult.rows[0] as UserData;
      const { password: _, ...userWithoutPassword } = user;

      return userWithoutPassword;
    } catch (error) {
      logger.error('Erro ao verificar token JWT:', error);
      return null;
    }
  },

  /**
   * Renova o token de acesso usando um refresh token
   * @param refreshToken - Refresh token
   * @returns Novo access token ou null
   * @deprecated Use refreshTokenWithRotation instead
   */
  async refreshToken(refreshToken: string): Promise<string | null> {
    try {
      // Verificar refresh token
      const payload = jwtService.verifyToken(refreshToken, 'refresh');
      if (!payload) {
        return null;
      }

      // Verificar se o token está no banco e não foi revogado
      const tokenResult = await tokenRepository.findToken(refreshToken);
      if (tokenResult.rowCount === 0 || tokenResult.rows[0].is_revoked) {
        return null;
      }

      // Gerar novo access token
      return jwtService.generateToken({
        sub: payload.sub,
        name: payload.name,
        email: payload.email,
        role: payload.role,
        type: 'access',
      });
    } catch (error) {
      logger.error('Erro ao renovar token JWT:', error);
      return null;
    }
  },

  /**
   * Renova o token de acesso usando um refresh token com rotação de tokens
   * @param refreshToken - Refresh token
   * @returns Objeto com novo access token e opcionalmente novo refresh token
   */
  async refreshTokenWithRotation(
    refreshToken: string,
    deviceInfo?: DeviceInfo
  ): Promise<{ accessToken: string; refreshToken?: string } | null> {
    try {
      // Verificar refresh token
      const payload = jwtService.verifyToken(refreshToken, 'refresh');
      if (!payload) {
        logger.warn('Refresh token inválido ou expirado');
        return null;
      }

      // Verificar se o token está no banco e não foi revogado
      const tokenResult = await tokenRepository.findToken(refreshToken);
      if (tokenResult.rowCount === 0 || tokenResult.rows[0].is_revoked) {
        logger.warn('Refresh token não encontrado ou revogado');
        return null;
      }

      const tokenData = tokenResult.rows[0];

      // Verificar se o token está próximo da expiração
      const expiresAt = new Date(tokenData.expires_at);
      const now = new Date();
      const timeUntilExpiry = expiresAt.getTime() - now.getTime();
      const halfLifeThreshold = 1000 * 60 * 60 * 24 * 3.5; // 3.5 dias (metade da vida útil de 7 dias)

      let newRefreshToken: string | undefined;

      // Se o token estiver na segunda metade de sua vida útil, gerar um novo
      if (timeUntilExpiry < halfLifeThreshold) {
        // Gerar novo refresh token
        newRefreshToken = jwtService.generateToken({
          sub: payload.sub,
          type: 'refresh',
        });

        // Calcular data de expiração do novo refresh token (7 dias)
        const newExpiresAt = new Date();
        newExpiresAt.setDate(newExpiresAt.getDate() + 7);

        // Salvar novo refresh token no banco de dados
        await tokenRepository.saveToken(
          payload.sub,
          newRefreshToken,
          'refresh',
          newExpiresAt,
          deviceInfo || {
            deviceId: payload.device,
            ipAddress: tokenData.ip_address,
            userAgent: tokenData.user_agent,
          }
        );

        // Revogar o token antigo após gerar o novo
        await tokenRepository.revokeToken(refreshToken);

        logger.debug('Refresh token rotacionado', {
          userId: payload.sub,
          oldTokenExpiry: expiresAt.toISOString(),
          newTokenExpiry: newExpiresAt.toISOString(),
        });
      }

      // Gerar novo access token
      const accessToken = jwtService.generateToken({
        sub: payload.sub,
        name: payload.name,
        email: payload.email,
        role: payload.role,
        type: 'access',
        device: payload.device,
      });

      return {
        accessToken,
        refreshToken: newRefreshToken,
      };
    } catch (error) {
      logger.error('Erro ao renovar token JWT com rotação:', error);
      return null;
    }
  },

  /**
   * Revoga todos os tokens de um usuário (logout)
   * @param ulid_user - ID do usuário
   * @param currentToken - Token atual a ser preservado (opcional)
   */
  async logout(ulid_user: string, currentToken?: string): Promise<void> {
    try {
      await tokenRepository.revokeAllUserTokens(ulid_user, currentToken);
      logger.debug(`Tokens revogados para usuário ${ulid_user}`);
    } catch (error) {
      logger.error('Erro ao fazer logout JWT:', error);
    }
  },

  /**
   * Revoga um token específico
   * @param token - Token a ser revogado
   * @returns Verdadeiro se o token foi revogado com sucesso
   */
  async revokeToken(token: string): Promise<boolean> {
    try {
      const result = await tokenRepository.revokeToken(token);
      return result.rowCount > 0;
    } catch (error) {
      logger.error('Erro ao revogar token:', error);
      return false;
    }
  },

  /**
   * Limpa tokens expirados do banco de dados
   * @returns Número de tokens removidos
   */
  async cleanupExpiredTokens(): Promise<number> {
    try {
      const result = await tokenRepository.cleanupExpiredTokens();
      logger.info(`${result.rowCount} tokens expirados removidos`);
      return result.rowCount;
    } catch (error) {
      logger.error('Erro ao limpar tokens expirados:', error);
      return 0;
    }
  },

  /**
   * Obtém todos os dispositivos ativos de um usuário
   * @param ulid_user - ID do usuário
   * @returns Lista de dispositivos ativos
   */
  async getUserDevices(ulid_user: string): Promise<any[]> {
    try {
      const result = await tokenRepository.getUserDevices(ulid_user);
      return result.rows;
    } catch (error) {
      logger.error('Erro ao obter dispositivos do usuário:', error);
      return [];
    }
  },

  /**
   * Revoga todos os tokens de um dispositivo específico
   * @param ulid_user - ID do usuário
   * @param deviceId - ID do dispositivo
   * @returns Verdadeiro se os tokens foram revogados com sucesso
   */
  async revokeDeviceTokens(ulid_user: string, deviceId: string): Promise<boolean> {
    try {
      const result = await tokenRepository.revokeDeviceTokens(ulid_user, deviceId);
      logger.info(`Tokens do dispositivo ${deviceId} revogados para usuário ${ulid_user}`);
      return result.rowCount > 0;
    } catch (error) {
      logger.error('Erro ao revogar tokens do dispositivo:', error);
      return false;
    }
  },

  /**
   * Revoga todos os tokens de todos os dispositivos exceto o atual
   * @param ulid_user - ID do usuário
   * @param currentDeviceId - ID do dispositivo atual
   * @returns Verdadeiro se os tokens foram revogados com sucesso
   */
  async revokeAllDevicesExceptCurrent(
    ulid_user: string,
    currentDeviceId: string
  ): Promise<boolean> {
    try {
      const result = await tokenRepository.revokeAllDevicesExceptCurrent(
        ulid_user,
        currentDeviceId
      );
      logger.info(
        `Tokens de todos os dispositivos exceto ${currentDeviceId} revogados para usuário ${ulid_user}`
      );
      return result.rowCount > 0;
    } catch (error) {
      logger.error('Erro ao revogar tokens de outros dispositivos:', error);
      return false;
    }
  },
};
