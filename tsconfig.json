{
  "extends": "astro/tsconfigs/strict",
  "compilerOptions": {
    "baseUrl": ".",
    "paths": {
      /* Principais diretórios da aplicação */
      "@actions/*": ["src/actions/*"],
      "@components/*": ["src/components/*"],
      "@config/*": ["src/config/*"],
      "@db/*": ["src/db/*"],
      "@domain/*": ["src/domain/*"],
      "@helpers/*": ["src/helpers/*"],
      "@hooks/*": ["src/hooks/*"],
      "@infrastructure/*": ["src/infrastructure/*"],
      "@layouts/*": ["src/layouts/*"],
      "@middleware/*": ["src/middleware/*"],
      "@middlewares/*": ["src/middlewares/*"],
      "@models/*": ["src/models/*"],
      "@pages/*": ["src/pages/*"],
      "@repositories/*": ["src/repositories/*"],
      "@repository/*": ["src/repository/*"],
      "@resources/*": ["src/resources/*"],
      "@scripts/*": ["src/scripts/*"],
      "@services/*": ["src/services/*"],
      "@styles/*": ["src/styles/*"],
      "@templates/*": ["src/templates/*"],
      "@tests/*": ["src/tests/*"],
      "@themes/*": ["src/themes/*"],
      "@tokens/*": ["src/tokens/*"],
      "@utils/*": ["src/utils/*"]
    },
    "moduleResolution": "bundler"
  }
}
