---
import { actions } from 'astro:actions';
import InputText from '@components/form/InputText.astro';
import AdminLayout from '@layouts/AdminLayout.astro';
import type { OrderData } from 'src/database/interfacesHelper';
---
<AdminLayout title="Vendas">
  <template>
    <div class="search-container flex justify-between mb-4">
      <InputText id="search-input" label="Pesquisar" name="search" placeholder="Pesquisar vendas..." />
      <div>
        <button class="btn btn-primary" id="handleSearch">Pesquisar</button>
        <!-- <button class="btn btn-secondary ml-2" id="navigateToNewOrder">Novo</button> -->
      </div>
    </div>

    <ul class="order-list list-disc pl-5">
    </ul>
  </template>

  <style>
    .search-container {
      display: flex;
      justify-content: space-between;
      margin-bottom: 20px;
    }
    .order-list {
      list-style: none;
      padding: 0;
    }
    .order-list li {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10px;
    }
  </style>
</AdminLayout>

<script>
  import type { OrderData } from "@helpers/database/interfacesHelper";
  import { actions } from "astro:actions";
  let orders: OrderData[] = [];

  async function handleSearch() {
    const searchInputElement = document.getElementById('search-input') as HTMLInputElement;
    if (searchInputElement) {
      const searchInput = searchInputElement.value;
      if (searchInput !== null && searchInput !== undefined) {
        // Lógica para buscar faturas com base no input
        const param = { filter: "value", value: searchInput };
        loadOrders(param);
      } else {
        console.error('O valor do input de pesquisa é nulo ou indefinido.');
      }
    } else {
      console.error('Elemento de input de pesquisa não encontrado.');
    }
  }

  function navigateToNewOrder() {
    // Lógica para redirecionar para o novo pedido
    window.location.href = '/admin/financial/order/new';
  }

  async function loadOrders(param: any) {
    const result = await actions.orderAction.read(param);
    orders = result.data?.data || [];
  }
</script>