/**
 * Camada de compatibilidade para o repositório de categorias
 * 
 * Este arquivo mantém a compatibilidade com o código existente que
 * utiliza a estrutura antiga do repositório de categorias.
 * 
 * @deprecated Use a nova estrutura de repositórios em src/repositories
 */

import type { QueryResult } from "pg";
import { pgHelper } from "../../repository/pgHelper";
import { repositories } from "../index";

/**
 * Cria uma nova categoria
 * 
 * @param name Nome da categoria
 * @param ulid_parent ID da categoria pai (opcional)
 * @returns Resultado da consulta
 */
async function create(
  name: string,
  ulid_parent?: string,
): Promise<QueryResult> {
  try {
    // Usar a nova implementação
    const category = await repositories.categoryRepository.create({
      name,
      parentId: ulid_parent
    });
    
    // Converter para o formato esperado pelo código antigo
    return {
      rows: [category],
      rowCount: 1,
      command: 'INSERT',
      oid: 0,
      fields: []
    };
  } catch (error) {
    console.error('Error in compatibility layer (categoryRepository.create):', error);
    
    // Fallback para a implementação antiga em caso de erro
    const result = await read(undefined, name);

    if (result.rows.length > 0) {
      return result;
    }

    return pgHelper.query(
      `INSERT INTO tab_category (
         ulid_category, 
         ${ulid_parent !== undefined ? "ulid_parent," : ""}
         name) 
       VALUES (
         $1, 
         ${ulid_parent !== undefined ? "$2," : ""}
         $3) 
       RETURNING *`,
      [pgHelper.generateULID(), ulid_parent, name],
    );
  }
}

/**
 * Busca categorias
 * 
 * @param ulid_category ID da categoria (opcional)
 * @param name Nome da categoria (opcional)
 * @param active Status de ativação (padrão: true)
 * @returns Resultado da consulta
 */
async function read(
  ulid_category?: string,
  name?: string,
  active = true,
): Promise<QueryResult> {
  try {
    // Usar a nova implementação
    const categories = await repositories.categoryRepository.findAll({
      id: ulid_category,
      name: name ? name : undefined,
      active
    });
    
    // Converter para o formato esperado pelo código antigo
    return {
      rows: categories,
      rowCount: categories.length,
      command: 'SELECT',
      oid: 0,
      fields: []
    };
  } catch (error) {
    console.error('Error in compatibility layer (categoryRepository.read):', error);
    
    // Fallback para a implementação antiga em caso de erro
    return pgHelper.query(
      `SELECT a.*, 
              b.name AS owner_name 
         FROM tab_category a 
    LEFT JOIN tab_category b 
           ON a.ulid_parent = b.ulid_category 
        WHERE a.active = $1 
          ${ulid_category ? "AND a.ulid_category = $2" : ""}
          ${name ? 'AND a.name ILIKE "%$3%" ' : ""} 
     ORDER BY a.name ASC`,
      [active, ulid_category, name],
    );
  }
}

/**
 * Atualiza uma categoria
 * 
 * @param ulid_category ID da categoria
 * @param active Status de ativação
 * @param name Nome da categoria
 * @param ulid_parent ID da categoria pai (opcional)
 * @returns Resultado da consulta
 */
async function update(
  ulid_category: string,
  active: boolean,
  name: string,
  ulid_parent?: string,
): Promise<QueryResult> {
  try {
    // Usar a nova implementação
    const category = await repositories.categoryRepository.update(ulid_category, {
      active,
      name,
      parentId: ulid_parent
    });
    
    // Converter para o formato esperado pelo código antigo
    return {
      rows: [category],
      rowCount: 1,
      command: 'UPDATE',
      oid: 0,
      fields: []
    };
  } catch (error) {
    console.error('Error in compatibility layer (categoryRepository.update):', error);
    
    // Fallback para a implementação antiga em caso de erro
    const params = [active, name, ulid_category];

    let query = `
      UPDATE tab_category 
         SET active     = $1, 
             name       = $2, 
             updated_at = CURRENT_TIMESTAMP`;

    if (ulid_parent !== undefined) {
      query += ", ulid_parent = $4";
      params.push(ulid_parent);
    }

    query += " WHERE ulid_category = $3 RETURNING *";

    return pgHelper.query(query, params);
  }
}

/**
 * Remove uma categoria
 * 
 * @param ulid_category ID da categoria
 * @returns Resultado da consulta
 */
async function deleteByUlid(ulid_category: string): Promise<QueryResult> {
  try {
    // Usar a nova implementação
    const success = await repositories.categoryRepository.delete(ulid_category);
    
    // Converter para o formato esperado pelo código antigo
    return {
      rows: success ? [{ ulid_category }] : [],
      rowCount: success ? 1 : 0,
      command: 'DELETE',
      oid: 0,
      fields: []
    };
  } catch (error) {
    console.error('Error in compatibility layer (categoryRepository.deleteByUlid):', error);
    
    // Fallback para a implementação antiga em caso de erro
    return pgHelper.query(
      `DELETE FROM tab_category 
        WHERE ulid_category = $1 
       RETURNING *`,
      [ulid_category],
    );
  }
}

/**
 * Inativa uma categoria
 * 
 * @param ulid_category ID da categoria
 * @returns Resultado da consulta
 */
async function inactivate(ulid_category: string): Promise<QueryResult> {
  try {
    // Usar a nova implementação
    const category = await repositories.categoryRepository.update(ulid_category, {
      active: false
    });
    
    // Converter para o formato esperado pelo código antigo
    return {
      rows: [category],
      rowCount: 1,
      command: 'UPDATE',
      oid: 0,
      fields: []
    };
  } catch (error) {
    console.error('Error in compatibility layer (categoryRepository.inactivate):', error);
    
    // Fallback para a implementação antiga em caso de erro
    return pgHelper.query(
      `UPDATE tab_category
          SET active = false
        WHERE ulid_category = $1
       RETURNING *`,
      [ulid_category],
    );
  }
}

export const categoryRepository = {
  create,
  read,
  update,
  deleteByUlid,
  inactivate,
};
