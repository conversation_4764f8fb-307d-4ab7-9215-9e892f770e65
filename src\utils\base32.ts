/**
 * Utilitários para codificação e decodificação Base32
 *
 * Implementa funções para codificar e decodificar dados em Base32,
 * seguindo a especificação RFC 4648.
 *
 * Parte da implementação da tarefa 6.1.2 - Implementação de 2FA
 */

// Alfabeto Base32 padrão (RFC 4648)
const ALPHABET = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ234567';

// Mapa de caracteres para valores
const CHAR_MAP: Record<string, number> = {};
for (let i = 0; i < ALPHABET.length; i++) {
  CHAR_MAP[ALPHABET[i]] = i;
}

/**
 * Codifica um buffer em Base32
 * @param buffer - Buffer a ser codificado
 * @returns String codificada em Base32
 */
export function base32Encode(buffer: Buffer): string {
  let result = '';
  let bits = 0;
  let value = 0;

  for (let i = 0; i < buffer.length; i++) {
    value = (value << 8) | buffer[i];
    bits += 8;

    while (bits >= 5) {
      bits -= 5;
      result += ALPHABET[(value >>> bits) & 31];
    }
  }

  if (bits > 0) {
    result += ALPHABET[(value << (5 - bits)) & 31];
  }

  // Adicionar padding se necessário
  while (result.length % 8 !== 0) {
    result += '=';
  }

  return result;
}

/**
 * Decodifica uma string Base32 em um buffer
 * @param str - String codificada em Base32
 * @returns Buffer decodificado
 */
export function base32Decode(str: string): Buffer {
  // Remover padding e espaços
  const cleanStr = str.replace(/=+$/, '').replace(/\s/g, '').toUpperCase();

  const length = Math.floor((cleanStr.length * 5) / 8);
  const result = Buffer.alloc(length);

  let bits = 0;
  let value = 0;
  let index = 0;

  for (let i = 0; i < cleanStr.length; i++) {
    const char = cleanStr[i];
    if (!(char in CHAR_MAP)) {
      throw new Error(`Caractere inválido na string Base32: ${char}`);
    }

    value = (value << 5) | CHAR_MAP[char];
    bits += 5;

    if (bits >= 8) {
      bits -= 8;
      result[index++] = (value >>> bits) & 255;
    }
  }

  return result;
}

/**
 * Verifica se uma string é uma codificação Base32 válida
 * @param str - String a ser verificada
 * @returns Verdadeiro se a string for Base32 válida
 */
export function isValidBase32(str: string): boolean {
  // Remover padding e espaços
  const cleanStr = str.replace(/=+$/, '').replace(/\s/g, '').toUpperCase();

  // Verificar se todos os caracteres são válidos
  for (let i = 0; i < cleanStr.length; i++) {
    if (!(cleanStr[i] in CHAR_MAP)) {
      return false;
    }
  }

  return true;
}

/**
 * Normaliza uma string Base32 removendo espaços e convertendo para maiúsculas
 * @param str - String Base32 a ser normalizada
 * @returns String Base32 normalizada
 */
export function normalizeBase32(str: string): string {
  return str.replace(/\s/g, '').toUpperCase();
}
