---
import FinancialNav from '@components/navigation/FinancialNav.astro';
import { checkAdmin } from '@helpers/authGuard';
import AdminLayout from '@layouts/AdminLayout.astro';
import { cancellationPolicyService } from '@services/cancellationPolicyService';

// Protege a rota verificando se o usuário é admin
const authRedirect = await checkAdmin(Astro);
if (authRedirect) return authRedirect;

// Obter políticas de cancelamento
const policies = await cancellationPolicyService.getPolicies();

// Formatar percentual
const formatPercentage = (value) => {
  return `${value}%`;
};

// Formatar tempo limite
const formatTimeLimit = (hours) => {
  if (hours < 24) {
    return `${hours} hora${hours !== 1 ? 's' : ''}`;
  }
  const days = Math.floor(hours / 24);
  return `${days} dia${days !== 1 ? 's' : ''}`;
};

// Verificar se há parâmetro de sucesso na URL
const success = Astro.url.searchParams.get('success');
const error = Astro.url.searchParams.get('error');
---

<AdminLayout title="Políticas de Cancelamento">
  <div class="container mx-auto p-4">
    <h1 class="text-2xl font-bold mb-4">Gestão Financeira</h1>
    
    <FinancialNav />
    
    <div class="flex items-center justify-between mb-6">
      <h2 class="text-xl font-bold">Políticas de Cancelamento</h2>
      <a href="/admin/financial/cancellation/policies/new" class="btn btn-primary">
        Nova Política
      </a>
    </div>
    
    {success && (
      <div class="alert alert-success mb-4">
        <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
        <span>Operação realizada com sucesso!</span>
      </div>
    )}
    
    {error && (
      <div class="alert alert-error mb-4">
        <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
        <span>Erro: {error}</span>
      </div>
    )}
    
    <!-- Lista de Políticas -->
    <div class="card bg-base-100 shadow-xl mb-6">
      <div class="card-body">
        <h2 class="card-title">Políticas Disponíveis</h2>
        <p class="text-sm mb-4">Configure as políticas de cancelamento para diferentes tipos de pedidos.</p>
        
        <div class="overflow-x-auto">
          <table class="table table-zebra w-full">
            <thead>
              <tr>
                <th>Nome</th>
                <th>Tipo</th>
                <th>Limite de Tempo</th>
                <th>Reembolso</th>
                <th>Aprovação</th>
                <th>Status</th>
                <th>Ações</th>
              </tr>
            </thead>
            <tbody>
              {policies.length === 0 ? (
                <tr>
                  <td colspan="7" class="text-center py-4">Nenhuma política de cancelamento encontrada</td>
                </tr>
              ) : (
                policies.map(policy => (
                  <tr>
                    <td>
                      <div class="font-bold">{policy.name}</div>
                      <div class="text-xs opacity-60">{policy.description}</div>
                    </td>
                    <td>{policy.type}</td>
                    <td>{formatTimeLimit(policy.time_limit_hours)}</td>
                    <td>{formatPercentage(policy.refund_percentage)}</td>
                    <td>
                      <div class={`badge ${policy.requires_approval ? 'badge-warning' : 'badge-success'}`}>
                        {policy.requires_approval ? 'Requer' : 'Automática'}
                      </div>
                    </td>
                    <td>
                      <div class={`badge ${policy.active ? 'badge-success' : 'badge-error'}`}>
                        {policy.active ? 'Ativa' : 'Inativa'}
                      </div>
                    </td>
                    <td>
                      <div class="flex gap-2">
                        <a href={`/admin/financial/cancellation/policies/edit/${policy.ulid_policy}`} class="btn btn-xs btn-primary">
                          Editar
                        </a>
                        <button 
                          class="btn btn-xs btn-outline btn-error toggle-status"
                          data-policy-id={policy.ulid_policy}
                          data-policy-active={policy.active}
                        >
                          {policy.active ? 'Desativar' : 'Ativar'}
                        </button>
                      </div>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
    
    <!-- Informações sobre Políticas -->
    <div class="card bg-base-100 shadow-xl mb-6">
      <div class="card-body">
        <h2 class="card-title">Sobre as Políticas de Cancelamento</h2>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <h3 class="font-bold mb-2">Tipos de Política</h3>
            <ul class="list-disc list-inside space-y-1">
              <li><strong>Standard:</strong> Para produtos físicos e serviços gerais</li>
              <li><strong>Digital Product:</strong> Para produtos digitais e downloads</li>
              <li><strong>Subscription:</strong> Para assinaturas e serviços recorrentes</li>
              <li><strong>Custom:</strong> Para casos especiais e exceções</li>
            </ul>
          </div>
          
          <div>
            <h3 class="font-bold mb-2">Configurações</h3>
            <ul class="list-disc list-inside space-y-1">
              <li><strong>Limite de Tempo:</strong> Período máximo para solicitar cancelamento</li>
              <li><strong>Reembolso:</strong> Percentual do valor a ser reembolsado</li>
              <li><strong>Aprovação:</strong> Se requer aprovação manual ou é automática</li>
              <li><strong>Reembolso Automático:</strong> Se o reembolso é processado automaticamente</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</AdminLayout>

<script>
  // Inicializar componentes interativos
  document.addEventListener('DOMContentLoaded', () => {
    // Botões de alteração de status
    document.querySelectorAll('.toggle-status').forEach(button => {
      button.addEventListener('click', async (e) => {
        const target = e.currentTarget as HTMLButtonElement;
        const policyId = target.dataset.policyId;
        const isActive = target.dataset.policyActive === 'true';
        
        if (!policyId) return;
        
        // Confirmar ação
        if (!confirm(`Deseja ${isActive ? 'desativar' : 'ativar'} esta política de cancelamento?`)) {
          return;
        }
        
        try {
          // Enviar requisição para alterar status
          const response = await fetch(`/api/cancellation/policies/${policyId}/toggle-status`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              active: !isActive,
            }),
          });
          
          if (!response.ok) {
            throw new Error('Erro ao alterar status da política');
          }
          
          // Recarregar página
          window.location.href = '/admin/financial/cancellation/policies?success=true';
        } catch (error) {
          console.error('Erro:', error);
          alert('Erro ao alterar status da política');
        }
      });
    });
  });
</script>
