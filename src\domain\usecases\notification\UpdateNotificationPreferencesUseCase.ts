/**
 * Update Notification Preferences Use Case
 *
 * Caso de uso para atualizar as preferências de notificação de um usuário.
 * Parte da implementação da tarefa 8.5.1 - Notificações internas
 */

import { NotificationType } from '../../entities/Notification';
import {
  NotificationChannel,
  NotificationPreference,
  TypePreference,
} from '../../entities/NotificationPreference';
import { NotificationPreferenceRepository } from '../../repositories/NotificationPreferenceRepository';

export interface UpdatePreferencesRequest {
  userId: string;
  typePreferences?: {
    type: NotificationType;
    preferences: TypePreference;
  }[];
  channelPreferences?: {
    channel: NotificationChannel;
    enabled: boolean;
    frequency?: 'immediate' | 'daily' | 'weekly' | 'never';
  }[];
  doNotDisturb?: {
    enabled: boolean;
    startTime?: string;
    endTime?: string;
    timezone?: string;
  };
  disableAll?: boolean;
  resetToDefault?: boolean;
}

export interface UpdatePreferencesResponse {
  success: boolean;
  preferences?: NotificationPreference;
  error?: string;
}

export class UpdateNotificationPreferencesUseCase {
  constructor(private preferenceRepository: NotificationPreferenceRepository) {}

  async execute(request: UpdatePreferencesRequest): Promise<UpdatePreferencesResponse> {
    try {
      // Validar os dados de entrada
      if (!this.validateRequest(request)) {
        return {
          success: false,
          error: 'Dados inválidos para atualização de preferências.',
        };
      }

      // Obter as preferências atuais do usuário
      let preferences = await this.preferenceRepository.getByUserId(request.userId);

      // Se não existir, criar novas preferências
      if (!preferences) {
        preferences = new NotificationPreference({
          id: crypto.randomUUID(),
          userId: request.userId,
        });

        await this.preferenceRepository.create(preferences);
      }

      // Resetar para o padrão, se solicitado
      if (request.resetToDefault) {
        await this.preferenceRepository.resetToDefault(request.userId);
        preferences = await this.preferenceRepository.getByUserId(request.userId);

        return {
          success: true,
          preferences: preferences!,
        };
      }

      // Desativar todas as notificações, se solicitado
      if (request.disableAll) {
        await this.preferenceRepository.disableAllNotifications(request.userId);
        preferences = await this.preferenceRepository.getByUserId(request.userId);

        return {
          success: true,
          preferences: preferences!,
        };
      }

      // Atualizar preferências por tipo, se fornecidas
      if (request.typePreferences && request.typePreferences.length > 0) {
        for (const { type, preferences: typePreference } of request.typePreferences) {
          await this.preferenceRepository.updateTypePreference(
            request.userId,
            type,
            typePreference
          );
        }
      }

      // Atualizar preferências por canal, se fornecidas
      if (request.channelPreferences && request.channelPreferences.length > 0) {
        for (const { channel, enabled, frequency } of request.channelPreferences) {
          await this.preferenceRepository.updateChannelPreference(
            request.userId,
            channel,
            enabled,
            frequency
          );
        }
      }

      // Atualizar configuração "Não Perturbe", se fornecida
      if (request.doNotDisturb) {
        await this.preferenceRepository.setDoNotDisturb(
          request.userId,
          request.doNotDisturb.enabled,
          request.doNotDisturb.startTime,
          request.doNotDisturb.endTime,
          request.doNotDisturb.timezone
        );
      }

      // Obter as preferências atualizadas
      const updatedPreferences = await this.preferenceRepository.getByUserId(request.userId);

      return {
        success: true,
        preferences: updatedPreferences!,
      };
    } catch (error) {
      console.error('Erro ao atualizar preferências de notificação:', error);
      return {
        success: false,
        error: 'Ocorreu um erro ao atualizar as preferências de notificação.',
      };
    }
  }

  private validateRequest(request: UpdatePreferencesRequest): boolean {
    // Validar usuário
    if (!request.userId) {
      return false;
    }

    // Validar preferências por tipo, se fornecidas
    if (request.typePreferences) {
      for (const { type, preferences } of request.typePreferences) {
        if (!this.isValidNotificationType(type)) {
          return false;
        }

        // Verificar canais
        for (const channel in preferences) {
          if (!this.isValidNotificationChannel(channel as NotificationChannel)) {
            return false;
          }

          const channelPreference = preferences[channel as NotificationChannel];

          if (channelPreference?.frequency) {
            if (!['immediate', 'daily', 'weekly', 'never'].includes(channelPreference.frequency)) {
              return false;
            }
          }
        }
      }
    }

    // Validar preferências por canal, se fornecidas
    if (request.channelPreferences) {
      for (const { channel, frequency } of request.channelPreferences) {
        if (!this.isValidNotificationChannel(channel)) {
          return false;
        }

        if (frequency && !['immediate', 'daily', 'weekly', 'never'].includes(frequency)) {
          return false;
        }
      }
    }

    // Validar configuração "Não Perturbe", se fornecida
    if (request.doNotDisturb) {
      if (
        request.doNotDisturb.startTime &&
        !this.isValidTimeFormat(request.doNotDisturb.startTime)
      ) {
        return false;
      }

      if (request.doNotDisturb.endTime && !this.isValidTimeFormat(request.doNotDisturb.endTime)) {
        return false;
      }
    }

    return true;
  }

  private isValidNotificationType(type: string): boolean {
    const validTypes: NotificationType[] = [
      'info',
      'success',
      'warning',
      'error',
      'system',
      'payment',
      'order',
      'product',
      'content',
      'message',
    ];

    return validTypes.includes(type as NotificationType);
  }

  private isValidNotificationChannel(channel: string): boolean {
    const validChannels: NotificationChannel[] = ['in_app', 'email', 'push', 'sms'];

    return validChannels.includes(channel as NotificationChannel);
  }

  private isValidTimeFormat(time: string): boolean {
    // Formato esperado: "HH:MM"
    const timeRegex = /^([01]\d|2[0-3]):([0-5]\d)$/;
    return timeRegex.test(time);
  }
}
