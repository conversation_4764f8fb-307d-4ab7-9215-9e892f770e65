/**
 * API para logout de usuários
 */

import { AuditEventType, AuditSeverity, auditService } from '@services/auditService';
import { getCurrentUser } from '@utils/authUtils';
import { logger } from '@utils/logger';
import type { APIRoute } from 'astro';

export const POST: APIRoute = async ({ request, cookies }) => {
  try {
    // Obter usuário atual
    const user = await getCurrentUser(cookies);

    // Obter informações da requisição para auditoria
    const ipAddress =
      request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown';

    const userAgent = request.headers.get('user-agent') || 'unknown';

    // Remover cookie de autenticação
    cookies.delete('access_token', {
      path: '/',
    });

    // Registrar logout
    if (user) {
      await auditService.logEvent({
        eventType: AuditEventType.LOGOUT,
        userId: user.ulid_user,
        userName: user.name,
        ipAddress,
        userAgent,
        resource: 'auth',
        action: 'logout',
        result: 'success',
        severity: AuditSeverity.INFO,
      });
    }

    return new Response(
      JSON.stringify({
        success: true,
        message: 'Logout realizado com sucesso',
      }),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  } catch (error) {
    logger.error('Erro no logout:', error);

    return new Response(
      JSON.stringify({
        success: false,
        error: 'Erro interno do servidor',
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  }
};
