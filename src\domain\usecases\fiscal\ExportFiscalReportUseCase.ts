/**
 * Export Fiscal Report Use Case
 *
 * Caso de uso para exportar relatórios fiscais.
 * Parte da implementação da tarefa 8.7.2 - Gestão de documentos fiscais
 */

import {
  FiscalDocument,
  FiscalDocumentStatus,
  FiscalDocumentType,
} from '../../entities/FiscalDocument';
import {
  FiscalDocumentFilter,
  FiscalDocumentRepository,
} from '../../repositories/FiscalDocumentRepository';

export type ReportFormat = 'csv' | 'xlsx' | 'pdf';

export interface ExportFiscalReportRequest {
  filter?: {
    type?: FiscalDocumentType | FiscalDocumentType[];
    status?: FiscalDocumentStatus | FiscalDocumentStatus[];
    startDate?: Date;
    endDate?: Date;
    customerDocumentNumber?: string;
  };
  format: ReportFormat;
  includeItems?: boolean;
}

export interface ExportFiscalReportResponse {
  success: boolean;
  data?: {
    content: string | Buffer;
    filename: string;
    contentType: string;
  };
  error?: string;
}

export class ExportFiscalReportUseCase {
  constructor(private fiscalDocumentRepository: FiscalDocumentRepository) {}

  async execute(request: ExportFiscalReportRequest): Promise<ExportFiscalReportResponse> {
    try {
      // Validar os dados de entrada
      if (!request.format) {
        return {
          success: false,
          error: 'Formato do relatório é obrigatório.',
        };
      }

      if (!['csv', 'xlsx', 'pdf'].includes(request.format)) {
        return {
          success: false,
          error: `Formato de relatório inválido: ${request.format}. Formatos suportados: csv, xlsx, pdf.`,
        };
      }

      // Mapear filtros
      const filter: FiscalDocumentFilter = request.filter || {};

      // Buscar documentos fiscais
      const result = await this.fiscalDocumentRepository.find(
        filter,
        { field: 'issueDate', direction: 'desc' },
        { page: 1, limit: 1000 } // Limite máximo para exportação
      );

      if (result.documents.length === 0) {
        return {
          success: false,
          error: 'Nenhum documento fiscal encontrado para os filtros informados.',
        };
      }

      // Gerar nome do arquivo
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const filename = `relatorio-fiscal-${timestamp}.${request.format}`;

      // Gerar conteúdo do relatório de acordo com o formato
      let content: string | Buffer;
      let contentType: string;

      switch (request.format) {
        case 'csv':
          content = this.generateCsvReport(result.documents, request.includeItems || false);
          contentType = 'text/csv';
          break;

        case 'xlsx':
          content = await this.generateXlsxReport(result.documents, request.includeItems || false);
          contentType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
          break;

        case 'pdf':
          content = await this.generatePdfReport(result.documents, request.includeItems || false);
          contentType = 'application/pdf';
          break;

        default:
          return {
            success: false,
            error: `Formato de relatório não suportado: ${request.format}`,
          };
      }

      return {
        success: true,
        data: {
          content,
          filename,
          contentType,
        },
      };
    } catch (error) {
      console.error('Erro ao exportar relatório fiscal:', error);

      return {
        success: false,
        error:
          error instanceof Error
            ? error.message
            : 'Erro desconhecido ao exportar relatório fiscal.',
      };
    }
  }

  /**
   * Gera relatório em formato CSV
   */
  private generateCsvReport(documents: FiscalDocument[], includeItems: boolean): string {
    // Cabeçalho do CSV
    let csv =
      'ID,Tipo,Número,Série,Status,Data de Emissão,Cliente,Documento,Valor Total,Valor de Impostos,Valor Final\n';

    // Dados dos documentos
    for (const doc of documents) {
      const row = [
        doc.id,
        doc.type,
        doc.number || '',
        doc.series || '',
        doc.status,
        doc.issueDate ? doc.issueDate.toISOString() : '',
        this.escapeCsvField(doc.customer.name),
        `${doc.customer.documentType} ${doc.customer.documentNumber}`,
        doc.totalValue.toFixed(2).replace('.', ','),
        doc.taxValue.toFixed(2).replace('.', ','),
        doc.finalValue.toFixed(2).replace('.', ','),
      ];

      csv += `${row.join(',')}\n`;

      // Adicionar itens se solicitado
      if (includeItems && doc.items.length > 0) {
        csv +=
          'Item,Código,Descrição,Quantidade,Valor Unitário,Valor Total,Código de Imposto,Valor de Imposto\n';

        doc.items.forEach((item, index) => {
          const itemRow = [
            index + 1,
            this.escapeCsvField(item.productCode),
            this.escapeCsvField(item.description),
            item.quantity.toString().replace('.', ','),
            item.unitValue.toFixed(2).replace('.', ','),
            item.totalValue.toFixed(2).replace('.', ','),
            item.taxCode,
            item.taxValue.toFixed(2).replace('.', ','),
          ];

          csv += `${itemRow.join(',')}\n`;
        });

        csv += '\n'; // Linha em branco entre documentos
      }
    }

    return csv;
  }

  /**
   * Escapa campos para CSV
   */
  private escapeCsvField(field: string): string {
    if (!field) return '';

    // Se o campo contém vírgula, aspas ou quebra de linha, envolve em aspas duplas
    if (field.includes(',') || field.includes('"') || field.includes('\n')) {
      // Substituir aspas duplas por duas aspas duplas
      return `"${field.replace(/"/g, '""')}"`;
    }

    return field;
  }

  /**
   * Gera relatório em formato XLSX
   */
  private async generateXlsxReport(
    documents: FiscalDocument[],
    includeItems: boolean
  ): Promise<Buffer> {
    // Em um cenário real, usaríamos uma biblioteca como ExcelJS
    // Para simplificar, vamos retornar o CSV como Buffer
    const csv = this.generateCsvReport(documents, includeItems);
    return Buffer.from(csv, 'utf-8');
  }

  /**
   * Gera relatório em formato PDF
   */
  private async generatePdfReport(
    documents: FiscalDocument[],
    includeItems: boolean
  ): Promise<Buffer> {
    // Em um cenário real, usaríamos uma biblioteca como PDFKit
    // Para simplificar, vamos retornar o CSV como Buffer
    const csv = this.generateCsvReport(documents, includeItems);
    return Buffer.from(csv, 'utf-8');
  }
}
