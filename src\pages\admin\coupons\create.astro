---
import CouponForm from '../../../components/coupon/CouponForm.astro';
import { PageTransition } from '../../../components/transitions';
import DaisyCard from '../../../components/ui/DaisyCard.astro';
import Breadcrumbs from '../../../components/ui/navigation/Breadcrumbs.astro';
/**
 * Página de Criação de Cupom
 *
 * Interface para criar novos cupons de desconto.
 * Parte da implementação da tarefa 8.4.1 - Sistema de cupons
 */
import BaseLayout from '../../../layouts/BaseLayout.astro';
import { Container, Section } from '../../../layouts/grid';
import { generateRandomCode } from '../../../utils/couponUtils';

// Título da página
const title = 'Criar Novo Cupom';

// Breadcrumbs para a página atual
const breadcrumbItems = [
  { href: '/', label: '<PERSON><PERSON><PERSON>' },
  { href: '/admin', label: 'Administra<PERSON>' },
  { href: '/admin/coupons', label: 'Cupons' },
  { label: 'Criar' },
];

// Processar o formulário de criação
if (Astro.request.method === 'POST') {
  try {
    // Em um cenário real, aqui seria implementada a lógica para:
    // 1. Receber os dados do formulário
    // 2. Validar os dados
    // 3. Criar o cupom no repositório
    // 4. Redirecionar para a página de detalhes ou lista

    // Por enquanto, apenas simularemos o redirecionamento
    return Astro.redirect('/admin/coupons?success=true');
  } catch (error) {
    console.error('Erro ao processar criação de cupom:', error);
    // Em caso de erro, continuamos na página com uma mensagem de erro
  }
}

// Em um cenário real, buscaríamos produtos e categorias do repositório
// Por enquanto, usaremos dados de exemplo
const availableProducts = [
  { id: 'prod-001', name: 'Cartilha de Alfabetização', price: 29.9 },
  { id: 'prod-002', name: 'Kit de Atividades Matemáticas', price: 45.0 },
  { id: 'prod-003', name: 'Livro de Histórias Infantis', price: 35.5 },
  { id: 'prod-004', name: 'Jogo Educativo de Palavras', price: 59.9 },
  { id: 'prod-005', name: 'Curso Online de Alfabetização', price: 149.9 },
];

const availableCategories = [
  { id: 'cat-001', name: 'Alfabetização' },
  { id: 'cat-002', name: 'Matemática' },
  { id: 'cat-003', name: 'Leitura' },
  { id: 'cat-004', name: 'Jogos' },
  { id: 'cat-005', name: 'Cursos' },
];

// Gerar código aleatório para sugestão
const suggestedCode = generateRandomCode();
---

<BaseLayout title={title} enableViewTransitions={true} transitionType="fade">
  <Container>
    <PageTransition type="fade" duration="0.5s">
      <Section>
        <Breadcrumbs items={breadcrumbItems} class="mb-6" />
        
        <h1 class="text-3xl font-bold mb-6">{title}</h1>
        
        <DaisyCard>
          <div class="p-6">
            <CouponForm
              mode="create"
              actionUrl="/admin/coupons/create"
              coupon={{ code: suggestedCode }}
              availableProducts={availableProducts}
              availableCategories={availableCategories}
            />
          </div>
        </DaisyCard>
        
        <div class="mt-8">
          <DaisyCard>
            <div class="p-6">
              <h2 class="text-xl font-bold mb-4">Dicas para Criação de Cupons</h2>
              
              <div class="space-y-4">
                <div>
                  <h3 class="font-bold">Tipos de Cupom</h3>
                  <ul class="list-disc list-inside ml-4 text-sm">
                    <li><strong>Percentual:</strong> Aplica um desconto percentual sobre o valor total.</li>
                    <li><strong>Valor Fixo:</strong> Aplica um desconto de valor fixo sobre o total.</li>
                    <li><strong>Frete Grátis:</strong> Remove o valor do frete do pedido.</li>
                  </ul>
                </div>
                
                <div>
                  <h3 class="font-bold">Boas Práticas</h3>
                  <ul class="list-disc list-inside ml-4 text-sm">
                    <li>Use códigos memoráveis e fáceis de digitar.</li>
                    <li>Defina datas de validade claras para criar senso de urgência.</li>
                    <li>Considere adicionar um valor mínimo de compra para aumentar o ticket médio.</li>
                    <li>Limite o uso por cliente para evitar abusos.</li>
                    <li>Para descontos percentuais grandes, defina um valor máximo de desconto.</li>
                  </ul>
                </div>
                
                <div>
                  <h3 class="font-bold">Estratégias Eficazes</h3>
                  <ul class="list-disc list-inside ml-4 text-sm">
                    <li><strong>Cupons de boas-vindas:</strong> Para novos clientes (ex: 10-15% no primeiro pedido).</li>
                    <li><strong>Cupons sazonais:</strong> Para datas comemorativas ou eventos especiais.</li>
                    <li><strong>Cupons de recuperação:</strong> Para clientes que abandonaram o carrinho.</li>
                    <li><strong>Cupons de fidelidade:</strong> Para recompensar clientes recorrentes.</li>
                    <li><strong>Cupons promocionais:</strong> Para produtos específicos ou categorias.</li>
                  </ul>
                </div>
              </div>
            </div>
          </DaisyCard>
        </div>
      </Section>
    </PageTransition>
  </Container>
</BaseLayout>
