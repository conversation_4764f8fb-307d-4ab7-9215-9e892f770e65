---
/**
 * Página de listagem de recursos
 *
 * Esta página exibe todos os recursos disponíveis no sistema
 * e permite gerenciar suas permissões.
 */

import Notification from '@components/admin/Notification.astro';
import PermissionGate from '@components/auth/PermissionGate.astro';
import PolicyGate from '@components/auth/PolicyGate.astro';
// Importações
import MainLayout from '@layouts/MainLayout.astro';
import { resourceRepository } from '@repository/resourceRepository';
import { getCurrentUser } from '@utils/authUtils';

// Verificar autenticação
const user = await getCurrentUser(Astro.cookies);

// Redirecionar se não estiver autenticado
if (!user) {
  return Astro.redirect('/signin?redirect=/admin/resources');
}

// Buscar recursos
const resourcesResult = await resourceRepository.read();
const resources = resourcesResult.rows;

// Obter parâmetros de consulta para mensagens
const success = Astro.url.searchParams.get('success');
const error = Astro.url.searchParams.get('error');

// Título da página
const title = 'Gerenciamento de Recursos';

// Processar formulário
if (Astro.request.method === 'POST') {
  try {
    const formData = await Astro.request.formData();
    const action = formData.get('action') as string;

    if (action === 'create-resource') {
      // Criar novo recurso
      const name = formData.get('name') as string;
      const description = formData.get('description') as string;

      if (name) {
        const result = await resourceRepository.create(name, description);
        const resourceId = result.rows[0].ulid_resource;

        return Astro.redirect(`/admin/resources/${resourceId}?success=created`);
      }
    }
  } catch (error) {
    console.error('Erro ao processar formulário:', error);
    return Astro.redirect('/admin/resources?error=create-failed');
  }
}
---

<MainLayout title={title}>
  <main class="container mx-auto px-4 py-8">
    <PermissionGate resource="resources" action="read">
      <div class="flex items-center justify-between mb-6">
        <h1 class="text-3xl font-bold">{title}</h1>
        
        <div class="flex space-x-2">
          <a 
            href="/admin/permissions" 
            class="bg-gray-200 text-gray-800 px-4 py-2 rounded hover:bg-gray-300 transition"
          >
            Voltar
          </a>
        </div>
      </div>
      
      {success && (
        <div class="mb-6">
          {success === 'created' && (
            <Notification 
              type="success" 
              title="Recurso criado" 
              message="O novo recurso foi criado com sucesso."
            />
          )}
        </div>
      )}
      
      {error && (
        <div class="mb-6">
          {error === 'create-failed' && (
            <Notification 
              type="error" 
              title="Erro ao criar" 
              message="Ocorreu um erro ao criar o recurso. Tente novamente."
            />
          )}
        </div>
      )}
      
      <div class="grid grid-cols-1 md:grid-cols-12 gap-6">
        <!-- Criar novo recurso -->
        <div class="md:col-span-4">
          <PolicyGate resource="resources" action="create">
            <div class="bg-white rounded-lg shadow p-6">
              <h2 class="text-xl font-semibold mb-4">Criar Novo Recurso</h2>
              
              <form method="POST" class="space-y-4">
                <input type="hidden" name="action" value="create-resource">
                
                <div>
                  <label for="name" class="block text-sm font-medium text-gray-700 mb-1">Nome</label>
                  <input 
                    type="text" 
                    id="name" 
                    name="name" 
                    placeholder="Ex: usuarios"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md"
                    required
                  >
                </div>
                
                <div>
                  <label for="description" class="block text-sm font-medium text-gray-700 mb-1">Descrição</label>
                  <textarea 
                    id="description" 
                    name="description" 
                    placeholder="Ex: Gerenciamento de usuários do sistema"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md"
                    rows="3"
                  ></textarea>
                </div>
                
                <div class="pt-2">
                  <button 
                    type="submit"
                    class="w-full bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition"
                  >
                    Criar Recurso
                  </button>
                </div>
              </form>
            </div>
            
            <slot name="fallback">
              <div class="bg-gray-50 rounded-lg p-6 text-gray-500">
                <p>Você não tem permissão para criar novos recursos.</p>
              </div>
            </slot>
          </PolicyGate>
        </div>
        
        <!-- Lista de recursos -->
        <div class="md:col-span-8">
          <div class="bg-white rounded-lg shadow overflow-hidden">
            <div class="p-6 bg-gray-50 border-b">
              <h2 class="text-xl font-semibold">Recursos Disponíveis</h2>
              <p class="mt-1 text-gray-600">
                Total: {resources.length} recursos
              </p>
            </div>
            
            <div class="p-6">
              {resources.length > 0 ? (
                <div class="grid grid-cols-1 gap-4">
                  {resources.map(resource => (
                    <div class="border rounded-lg p-4 hover:bg-gray-50 transition">
                      <div class="flex items-start justify-between">
                        <div>
                          <h3 class="text-lg font-medium">
                            <a href={`/admin/resources/${resource.ulid_resource}`} class="text-blue-600 hover:underline">
                              {resource.name}
                            </a>
                          </h3>
                          <p class="text-gray-600 mt-1">{resource.description || 'Sem descrição'}</p>
                        </div>
                        <span class={`px-2 py-1 text-xs font-semibold rounded-full ${resource.active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                          {resource.active ? 'Ativo' : 'Inativo'}
                        </span>
                      </div>
                      
                      <div class="mt-4 flex items-center justify-between text-sm">
                        <div class="text-gray-500">
                          Criado em: {new Date(resource.created_at).toLocaleDateString()}
                        </div>
                        <div>
                          <PolicyGate resource="resources" action="update">
                            <a 
                              href={`/admin/resources/${resource.ulid_resource}`}
                              class="text-blue-600 hover:underline"
                            >
                              Gerenciar permissões
                            </a>
                            
                            <slot name="fallback"></slot>
                          </PolicyGate>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p class="text-gray-500">Nenhum recurso encontrado.</p>
              )}
            </div>
          </div>
        </div>
      </div>
      
      <slot name="fallback">
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mt-6">
          <p>Você não tem permissão para acessar esta página.</p>
          <p class="mt-2">
            <a href="/" class="text-red-700 font-medium hover:underline">Voltar para a página inicial</a>
          </p>
        </div>
      </slot>
    </PermissionGate>
  </main>
</MainLayout>

<script>
  // Script para interatividade no cliente
  document.addEventListener('DOMContentLoaded', () => {
    // Implementar funcionalidades interativas aqui
  });
</script>

<style>
  /* Estilos específicos da página */
</style>
