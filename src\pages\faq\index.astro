---
import FaqAccordion from '../../components/faq/FaqAccordion.astro';
import FaqCategoryTabs from '../../components/faq/FaqCategoryTabs.astro';
import FaqSearch from '../../components/faq/FaqSearch.astro';
import { PageTransition } from '../../components/transitions';
import Breadcrumbs from '../../components/ui/navigation/Breadcrumbs.astro';
import { GetFaqItemsUseCase } from '../../domain/usecases/faq/GetFaqItemsUseCase';
import { PostgresFaqRepository } from '../../infrastructure/database/repositories/PostgresFaqRepository';
/**
 * Página de FAQ
 *
 * Página de perguntas frequentes interativa.
 * Parte da implementação da tarefa 8.7.1 - Implementação de FAQ interativo
 */
import BaseLayout from '../../layouts/BaseLayout.astro';
import { Container, Section } from '../../layouts/grid';

// Título da página
const title = 'Perguntas Frequentes';

// Breadcrumbs para a página atual
const breadcrumbItems = [{ href: '/', label: 'Início' }, { label: 'Perguntas Frequentes' }];

// Obter parâmetros de consulta
const category = Astro.url.searchParams.get('category') || '';
const query = Astro.url.searchParams.get('q') || '';
const page = Number.parseInt(Astro.url.searchParams.get('page') || '1');
const limit = Number.parseInt(Astro.url.searchParams.get('limit') || '10');

// Inicializar repositório e caso de uso
const faqRepository = new PostgresFaqRepository();
const getFaqItemsUseCase = new GetFaqItemsUseCase(faqRepository);

// Construir filtro
const filter: any = {
  isPublished: true,
};

if (category) {
  filter.category = category;
}

if (query) {
  filter.search = query;
}

// Obter itens de FAQ
const result = await getFaqItemsUseCase.execute({
  filter,
  sort: { field: 'viewCount', direction: 'desc' },
  pagination: { page, limit },
  onlyPublished: true,
});

const faqItems = result.success ? result.data?.items || [] : [];
const totalItems = result.success ? result.data?.total || 0 : 0;
const totalPages = result.success ? result.data?.totalPages || 1 : 1;

// Obter estatísticas para contagem de categorias
const stats = await faqRepository.getStats();

// Categorias disponíveis com contagem
const categories = [
  { value: '', label: 'Todas as categorias', count: stats.publishedItems },
  { value: 'general', label: 'Geral', count: stats.categoryDistribution?.general || 0 },
  { value: 'account', label: 'Conta', count: stats.categoryDistribution?.account || 0 },
  { value: 'products', label: 'Produtos', count: stats.categoryDistribution?.products || 0 },
  { value: 'payment', label: 'Pagamento', count: stats.categoryDistribution?.payment || 0 },
  { value: 'shipping', label: 'Envio', count: stats.categoryDistribution?.shipping || 0 },
  { value: 'technical', label: 'Técnico', count: stats.categoryDistribution?.technical || 0 },
  {
    value: 'educational',
    label: 'Educacional',
    count: stats.categoryDistribution?.educational || 0,
  },
  { value: 'other', label: 'Outro', count: stats.categoryDistribution?.other || 0 },
].filter((cat) => cat.count > 0);
---

<BaseLayout title={title} enableViewTransitions={true} transitionType="fade">
  <Container>
    <PageTransition type="fade" duration="0.5s">
      <Section>
        <Breadcrumbs items={breadcrumbItems} class="mb-6" />
        
        <div class="text-center mb-8">
          <h1 class="text-4xl font-bold mb-4">{title}</h1>
          <p class="text-xl text-gray-600 max-w-2xl mx-auto">
            Encontre respostas para as perguntas mais comuns sobre nossos produtos e serviços.
          </p>
        </div>
        
        <div class="max-w-3xl mx-auto mb-8">
          <FaqSearch 
            placeholder="O que você está procurando?"
            action="/faq"
            method="GET"
            initialValue={query}
            showCategoryFilter={true}
            initialCategory={category}
          />
        </div>
        
        <div class="mb-8">
          <FaqCategoryTabs 
            categories={categories}
            selectedCategory={category}
            baseUrl="/faq"
            style="tabs-boxed"
          />
        </div>
        
        <div class="mb-8">
          {query && (
            <div class="alert mb-6">
              <div>
                <i class="icon icon-search"></i>
                <span>
                  {totalItems > 0 
                    ? `Encontramos ${totalItems} resultado${totalItems !== 1 ? 's' : ''} para "${query}"` 
                    : `Nenhum resultado encontrado para "${query}"`}
                </span>
              </div>
              <a href="/faq" class="btn btn-sm">Limpar busca</a>
            </div>
          )}
          
          <FaqAccordion 
            items={faqItems}
            style="arrow"
            allowMultiple={true}
            showFeedback={true}
            showTags={true}
          />
        </div>
        
        {totalPages > 1 && (
          <div class="flex justify-center mt-8">
            <div class="join">
              {Array.from({ length: totalPages }, (_, i) => i + 1).map(p => (
                <a 
                  href={`/faq?page=${p}&limit=${limit}${category ? `&category=${category}` : ''}${query ? `&q=${query}` : ''}`} 
                  class={`join-item btn ${p === page ? 'btn-active' : ''}`}
                  aria-current={p === page ? 'page' : undefined}
                  aria-label={`Página ${p}`}
                >
                  {p}
                </a>
              ))}
            </div>
          </div>
        )}
        
        <div class="mt-12 text-center">
          <h2 class="text-2xl font-bold mb-4">Não encontrou o que procurava?</h2>
          <p class="mb-6">Entre em contato conosco e responderemos suas dúvidas o mais breve possível.</p>
          <a href="/contato" class="btn btn-primary">
            <i class="icon icon-mail mr-2"></i>
            Entrar em Contato
          </a>
        </div>
      </Section>
    </PageTransition>
  </Container>
</BaseLayout>

<style>
  /* Estilos específicos para a página de FAQ */
  .icon {
    font-size: 1.25rem;
  }
  
  /* Melhorar acessibilidade de foco */
  a:focus, button:focus, input:focus, textarea:focus, select:focus, [tabindex]:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
  }
</style>
