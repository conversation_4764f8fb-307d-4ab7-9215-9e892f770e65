/**
 * Middleware para aplicação de políticas de acesso
 */

import { authJwtService } from '@services/authJwtService';
import { policyEnforcementService } from '@services/policyEnforcementService';
import { logger } from '@utils/logger';
import type { APIContext, MiddlewareHandler } from 'astro';

/**
 * Opções para o middleware de políticas
 */
export interface PolicyOptions {
  /**
   * Recurso a ser verificado
   */
  resource: string;

  /**
   * Ação a ser verificada
   */
  action: string;

  /**
   * Função para extrair ID do alvo da requisição
   */
  getTargetId?: (context: APIContext) => string | undefined;

  /**
   * Função para extrair metadados adicionais da requisição
   */
  getMetadata?: (context: APIContext) => Record<string, any> | undefined;

  /**
   * Se deve redirecionar para página de acesso negado
   */
  redirect?: boolean;

  /**
   * URL para redirecionamento em caso de acesso negado
   */
  redirectUrl?: string;
}

/**
 * Middleware para aplicação de políticas de acesso
 * @param options - Opções de política
 * @returns Middleware handler
 */
export function enforcePolicy(options: PolicyOptions): MiddlewareHandler {
  return async (context: APIContext, next: () => Promise<Response>) => {
    const { request, cookies, locals } = context;

    try {
      // Obter token do cabeçalho Authorization ou do cookie
      let token = request.headers.get('Authorization')?.replace('Bearer ', '');

      if (!token) {
        token = cookies.get('access_token')?.value;
      }

      // Verificar se o token foi fornecido
      if (!token) {
        if (options.redirect) {
          return new Response(null, {
            status: 302,
            headers: {
              Location: options.redirectUrl || '/signin',
            },
          });
        }

        return new Response(
          JSON.stringify({
            success: false,
            error: 'Não autenticado',
          }),
          {
            status: 401,
            headers: {
              'Content-Type': 'application/json',
            },
          }
        );
      }

      // Verificar token e obter dados do usuário
      const user = await authJwtService.verifyToken(token, true);

      // Verificar se o token é válido
      if (!user) {
        if (options.redirect) {
          return new Response(null, {
            status: 302,
            headers: {
              Location: options.redirectUrl || '/signin',
            },
          });
        }

        return new Response(
          JSON.stringify({
            success: false,
            error: 'Token inválido ou expirado',
          }),
          {
            status: 401,
            headers: {
              'Content-Type': 'application/json',
            },
          }
        );
      }

      // Obter ID do alvo (se aplicável)
      const targetId = options.getTargetId ? options.getTargetId(context) : undefined;

      // Obter metadados adicionais (se aplicável)
      const metadata = options.getMetadata ? options.getMetadata(context) : undefined;

      // Verificar política de acesso
      const policyResult = await policyEnforcementService.enforcePolicy({
        userId: user.ulid_user,
        resource: options.resource,
        action: options.action,
        targetId,
        metadata,
      });

      // Se não tem permissão
      if (!policyResult.granted) {
        logger.warn(
          `Acesso negado por política: ${user.email} tentou ${options.action} em ${options.resource}`,
          {
            userId: user.ulid_user,
            resource: options.resource,
            action: options.action,
            targetId,
            reason: policyResult.reason,
            ip: request.headers.get('x-forwarded-for') || 'unknown',
            userAgent: request.headers.get('user-agent') || 'unknown',
          }
        );

        if (options.redirect) {
          return new Response(null, {
            status: 302,
            headers: {
              Location: options.redirectUrl || '/access-denied',
            },
          });
        }

        return new Response(
          JSON.stringify({
            success: false,
            error: 'Acesso negado',
            reason: policyResult.reason,
          }),
          {
            status: 403,
            headers: {
              'Content-Type': 'application/json',
            },
          }
        );
      }

      // Armazenar usuário e informações de política nos locals para uso posterior
      locals.user = user;
      locals.policy = {
        resource: options.resource,
        action: options.action,
        targetId,
        granted: true,
      };

      // Continuar para o próximo middleware ou rota
      return await next();
    } catch (error) {
      logger.error('Erro no middleware de política:', error);

      if (options.redirect) {
        return new Response(null, {
          status: 302,
          headers: {
            Location: options.redirectUrl || '/error',
          },
        });
      }

      return new Response(
        JSON.stringify({
          success: false,
          error: 'Erro ao verificar política de acesso',
        }),
        {
          status: 500,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }
  };
}

/**
 * Middleware para aplicação de múltiplas políticas (AND)
 * @param policies - Lista de opções de política
 * @returns Middleware handler
 */
export function enforceAllPolicies(policies: PolicyOptions[]): MiddlewareHandler {
  return async (context: APIContext, next: () => Promise<Response>) => {
    const { request, cookies, locals } = context;

    try {
      // Obter token do cabeçalho Authorization ou do cookie
      let token = request.headers.get('Authorization')?.replace('Bearer ', '');

      if (!token) {
        token = cookies.get('access_token')?.value;
      }

      // Verificar se o token foi fornecido
      if (!token) {
        if (policies[0]?.redirect) {
          return new Response(null, {
            status: 302,
            headers: {
              Location: policies[0]?.redirectUrl || '/signin',
            },
          });
        }

        return new Response(
          JSON.stringify({
            success: false,
            error: 'Não autenticado',
          }),
          {
            status: 401,
            headers: {
              'Content-Type': 'application/json',
            },
          }
        );
      }

      // Verificar token e obter dados do usuário
      const user = await authJwtService.verifyToken(token, true);

      // Verificar se o token é válido
      if (!user) {
        if (policies[0]?.redirect) {
          return new Response(null, {
            status: 302,
            headers: {
              Location: policies[0]?.redirectUrl || '/signin',
            },
          });
        }

        return new Response(
          JSON.stringify({
            success: false,
            error: 'Token inválido ou expirado',
          }),
          {
            status: 401,
            headers: {
              'Content-Type': 'application/json',
            },
          }
        );
      }

      // Verificar todas as políticas
      for (const policy of policies) {
        // Obter ID do alvo (se aplicável)
        const targetId = policy.getTargetId ? policy.getTargetId(context) : undefined;

        // Obter metadados adicionais (se aplicável)
        const metadata = policy.getMetadata ? policy.getMetadata(context) : undefined;

        // Verificar política de acesso
        const policyResult = await policyEnforcementService.enforcePolicy({
          userId: user.ulid_user,
          resource: policy.resource,
          action: policy.action,
          targetId,
          metadata,
        });

        // Se não tem permissão
        if (!policyResult.granted) {
          logger.warn(
            `Acesso negado por política: ${user.email} tentou ${policy.action} em ${policy.resource}`,
            {
              userId: user.ulid_user,
              resource: policy.resource,
              action: policy.action,
              targetId,
              reason: policyResult.reason,
              ip: request.headers.get('x-forwarded-for') || 'unknown',
              userAgent: request.headers.get('user-agent') || 'unknown',
            }
          );

          if (policy.redirect) {
            return new Response(null, {
              status: 302,
              headers: {
                Location: policy.redirectUrl || '/access-denied',
              },
            });
          }

          return new Response(
            JSON.stringify({
              success: false,
              error: 'Acesso negado',
              reason: policyResult.reason,
            }),
            {
              status: 403,
              headers: {
                'Content-Type': 'application/json',
              },
            }
          );
        }
      }

      // Armazenar usuário nos locals para uso posterior
      locals.user = user;
      locals.policies = policies.map((policy) => ({
        resource: policy.resource,
        action: policy.action,
        granted: true,
      }));

      // Continuar para o próximo middleware ou rota
      return await next();
    } catch (error) {
      logger.error('Erro no middleware de políticas:', error);

      if (policies[0]?.redirect) {
        return new Response(null, {
          status: 302,
          headers: {
            Location: policies[0]?.redirectUrl || '/error',
          },
        });
      }

      return new Response(
        JSON.stringify({
          success: false,
          error: 'Erro ao verificar políticas de acesso',
        }),
        {
          status: 500,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }
  };
}
