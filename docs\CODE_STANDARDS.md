# Padrões de Código - Estação Alfabetização

Este documento define os padrões de código a serem seguidos no projeto Estação Alfabetização. Estes padrões visam garantir a consistência, legibilidade e manutenibilidade do código.

## Ferramentas de Padronização

O projeto utiliza as seguintes ferramentas para garantir a padronização do código:

- **Biome.js**: Ferramenta unificada para linting, formatação e organização de importações
- **EditorConfig**: Para garantir consistência entre diferentes editores
- **TypeScript**: Para tipagem estática

## Estrutura de Diretórios

O projeto segue a estrutura de diretórios da Clean Architecture:

- `src/domain`: Entidades e regras de negócio
- `src/application`: Casos de uso e interfaces de repositórios
- `src/adapters`: Adaptadores para APIs externas e implementações de interfaces
- `src/infrastructure`: Implementações concretas de banco de dados, cache, etc.
- `src/repositories`: Implementações de repositórios
- `src/components`: Componentes Astro
- `src/layouts`: Layouts Astro
- `src/pages`: Páginas Astro
- `src/middleware`: Middlewares para processamento de requisições
- `src/utils`: Utilitários gerais
- `src/config`: Configurações da aplicação
- `src/services`: Serviços da aplicação

## Convenções de Nomenclatura

### Arquivos e Diretórios

- **Diretórios**: Usar plural para coleções de componentes (ex: `components`, `repositories`)
- **Componentes Astro**: Usar PascalCase (ex: `Button.astro`, `Header.astro`)
- **Interfaces**: Prefixar com `I` (ex: `IUserRepository.ts`)
- **Classes**: Usar PascalCase (ex: `UserService.ts`)
- **Utilitários**: Usar camelCase (ex: `stringUtils.ts`)
- **Constantes**: Usar UPPER_SNAKE_CASE para constantes globais

### Código

- **Variáveis e Funções**: Usar camelCase (ex: `getUserById`, `firstName`)
- **Classes e Interfaces**: Usar PascalCase (ex: `UserService`, `IUserRepository`)
- **Constantes**: Usar UPPER_SNAKE_CASE (ex: `MAX_RETRY_COUNT`)
- **Componentes**: Usar PascalCase (ex: `<UserProfile />`)
- **Tipos e Enums**: Usar PascalCase (ex: `UserRole`, `PaymentStatus`)

## Importações

- Usar aliases de importação definidos no `tsconfig.json` em vez de caminhos relativos
- Organizar importações na seguinte ordem:
  1. Importações de bibliotecas externas
  2. Importações de aliases
  3. Importações relativas
- Agrupar importações por tipo e ordenar alfabeticamente

Exemplo:
```typescript
// Bibliotecas externas
import { useState } from 'react';
import type { QueryResult } from 'pg';

// Aliases
import { User } from '@domain/entities/User';
import { IUserRepository } from '@application/interfaces/repositories/IUserRepository';

// Importações relativas
import { formatDate } from '../utils/dateUtils';
```

## Formatação

- Indentação: 2 espaços
- Comprimento máximo de linha: 100 caracteres
- Usar ponto e vírgula no final das instruções
- Usar aspas simples para strings
- Usar vírgula no final de arrays e objetos multilinhas
- Usar espaços após palavras-chave e antes de chaves
- Usar chaves para todos os blocos, mesmo os de uma linha

## Comentários

- Usar JSDoc para documentar funções, classes e interfaces
- Comentar código complexo ou não óbvio
- Evitar comentários óbvios ou redundantes
- Manter comentários atualizados com o código

Exemplo:
```typescript
/**
 * Busca um usuário pelo ID
 *
 * @param id ID do usuário
 * @returns Usuário encontrado ou null se não existir
 */
async function findUserById(id: string): Promise<User | null> {
  // Implementação
}
```

## Boas Práticas

- Seguir os princípios SOLID
- Evitar funções com mais de 20 linhas
- Evitar arquivos com mais de 300 linhas
- Evitar aninhamento excessivo (máximo 3 níveis)
- Usar tipos explícitos em vez de `any`
- Evitar efeitos colaterais em funções
- Preferir funções puras quando possível
- Usar async/await em vez de callbacks
- Tratar erros adequadamente
- Evitar código duplicado

## Testes

- Escrever testes unitários para funções e componentes
- Escrever testes de integração para fluxos completos
- Seguir a convenção AAA (Arrange, Act, Assert)
- Nomear testes de forma descritiva
- Usar mocks para dependências externas

## Scripts de Padronização

O projeto inclui os seguintes scripts para garantir a padronização do código:

- `npm run lint`: Executa o Biome.js para verificar problemas no código
- `npm run lint:fix`: Corrige automaticamente problemas detectados pelo Biome.js
- `npm run format`: Formata o código usando o Biome.js
- `npm run format:check`: Verifica se o código está formatado corretamente
- `npm run organize`: Verifica a organização de importações
- `npm run organize:fix`: Organiza automaticamente as importações
- `npm run code:check`: Executa lint, format:check e organize
- `npm run code:fix`: Executa lint:fix, format e organize:fix

## Integração com o Editor

O projeto inclui configurações para o VSCode que facilitam a padronização do código:

- Formatação automática ao salvar
- Correção automática de problemas com Biome.js
- Organização automática de importações com Biome.js
- Extensões recomendadas para desenvolvimento, incluindo a extensão oficial do Biome.js

## Verificação Contínua

A padronização do código é verificada continuamente através de:

- Hooks de pre-commit para verificar o código antes de commits
- Pipeline de CI/CD para verificar o código em pull requests
- Revisão de código por pares

## Conclusão

Seguir estes padrões de código é essencial para manter a qualidade e a manutenibilidade do projeto Estação Alfabetização. Todos os desenvolvedores devem se familiarizar com estes padrões e aplicá-los em seu trabalho diário.
