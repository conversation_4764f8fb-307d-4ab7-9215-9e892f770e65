/**
 * Testes para o serviço de retry de produtores Kafka
 */

import {
  describe,
  it,
  expect,
  beforeEach,
  afterEach,
  vi,
  type Mock,
} from 'vitest';
import {
  kafkaProducerRetryService,
  SendWithRetryResult,
} from '../../services/kafka-producer-retry.service';
import {
  RetryStrategy,
  ProducerErrorType,
} from '../../config/kafka-retry.config';
import type { Producer } from 'kafkajs';

// Mock do produtor Kafka
const mockProducer = {
  send: vi.fn(),
  connect: vi.fn(),
  disconnect: vi.fn(),
  isConnected: vi.fn().mockReturnValue(true),
} as unknown as Producer;

// Mock do logger
vi.mock('../../utils/logger', () => ({
  logger: {
    debug: vi.fn(),
    info: vi.fn(),
    warn: vi.fn(),
    error: vi.fn(),
  },
}));

// Mock do serviço de logging
vi.mock('../../services/kafka-logging.service', () => ({
  kafkaLoggingService: {
    info: vi.fn(),
    warn: vi.fn(),
    error: vi.fn(),
    debug: vi.fn(),
  },
}));

// Mock da configuração de retry
vi.mock('../../config/kafka-retry.config', async importOriginal => {
  const originalModule =
    await importOriginal<typeof import('../../config/kafka-retry.config')>();

  return {
    ...originalModule,
    kafkaProducerRetryConfig: {
      strategy: RetryStrategy.EXPONENTIAL_BACKOFF,
      maxRetries: 3,
      initialRetryTimeMs: 10, // Valores baixos para testes mais rápidos
      maxRetryTimeMs: 100,
      multiplier: 2,
      jitter: 0.1,
      deadLetterQueue: {
        enabled: true,
        topic: 'test-dlq',
        includeOriginalMessage: true,
        includeStackTrace: true,
        includeMetadata: true,
      },
      errorTypeConfigs: {
        [ProducerErrorType.CONNECTION]: {
          maxRetries: 5,
          initialRetryTimeMs: 20,
          maxRetryTimeMs: 200,
          multiplier: 1.5,
          jitter: 0.2,
          useDeadLetterQueue: true,
        },
        [ProducerErrorType.SERIALIZATION]: {
          maxRetries: 0,
          initialRetryTimeMs: 0,
          maxRetryTimeMs: 0,
          multiplier: 1,
          jitter: 0,
          useDeadLetterQueue: true,
        },
      },
      topicConfigs: {
        'test-topic': {
          maxRetries: 4,
          initialRetryTimeMs: 15,
          maxRetryTimeMs: 150,
        },
      },
    },
    getTopicRetryConfig: vi.fn().mockImplementation(topic => {
      if (topic === 'test-topic') {
        return {
          strategy: RetryStrategy.EXPONENTIAL_BACKOFF,
          maxRetries: 4,
          initialRetryTimeMs: 15,
          maxRetryTimeMs: 150,
          multiplier: 2,
          jitter: 0.1,
          deadLetterQueue: {
            enabled: true,
            topic: 'test-dlq',
            includeOriginalMessage: true,
            includeStackTrace: true,
            includeMetadata: true,
          },
        };
      }
      return originalModule.kafkaProducerRetryConfig;
    }),
    mapKafkaErrorToProducerErrorType: vi.fn().mockImplementation(error => {
      if (error.message.includes('connection')) {
        return ProducerErrorType.CONNECTION;
      }
      if (error.message.includes('serialization')) {
        return ProducerErrorType.SERIALIZATION;
      }
      return ProducerErrorType.UNKNOWN;
    }),
  };
});

describe('KafkaProducerRetryService', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    vi.useFakeTimers();
  });

  afterEach(() => {
    vi.useRealTimers();
  });

  describe('sendWithRetry', () => {
    it('deve enviar mensagem com sucesso na primeira tentativa', async () => {
      // Configurar mock para sucesso
      (mockProducer.send as Mock).mockResolvedValueOnce(undefined);

      // Mensagem de teste
      const message = {
        key: 'test-key',
        value: Buffer.from('test-value'),
        headers: { 'test-header': 'value' },
      };

      // Enviar mensagem
      const result = await kafkaProducerRetryService.sendWithRetry(
        'test-topic',
        message,
        mockProducer
      );

      // Verificar resultado
      expect(result).toEqual({
        success: true,
        attempts: 1,
        sentToDLQ: false,
        totalTimeMs: 0, // 0 porque estamos usando fake timers
      });

      // Verificar que send foi chamado corretamente
      expect(mockProducer.send).toHaveBeenCalledTimes(1);
      expect(mockProducer.send).toHaveBeenCalledWith({
        topic: 'test-topic',
        messages: [
          expect.objectContaining({
            key: 'test-key',
            value: Buffer.from('test-value'),
            headers: expect.objectContaining({
              'test-header': 'value',
              'retry-attempt-count': '1',
            }),
          }),
        ],
      });
    });

    it('deve tentar novamente após falha e ter sucesso na segunda tentativa', async () => {
      // Configurar mock para falha seguida de sucesso
      (mockProducer.send as Mock)
        .mockRejectedValueOnce(new Error('connection error'))
        .mockResolvedValueOnce(undefined);

      // Mensagem de teste
      const message = {
        key: 'test-key',
        value: Buffer.from('test-value'),
      };

      // Iniciar envio
      const resultPromise = kafkaProducerRetryService.sendWithRetry(
        'test-topic',
        message,
        mockProducer
      );

      // Avançar o tempo para permitir o retry
      await vi.advanceTimersByTimeAsync(50);

      // Obter resultado
      const result = await resultPromise;

      // Verificar resultado
      expect(result).toEqual({
        success: true,
        attempts: 2,
        sentToDLQ: false,
        totalTimeMs: 50,
      });

      // Verificar que send foi chamado duas vezes
      expect(mockProducer.send).toHaveBeenCalledTimes(2);
    });

    it('deve falhar após esgotar todas as tentativas e enviar para DLQ', async () => {
      // Configurar mock para falhar em todas as tentativas
      (mockProducer.send as Mock).mockRejectedValue(
        new Error('connection error')
      );

      // Mensagem de teste
      const message = {
        key: 'test-key',
        value: Buffer.from('test-value'),
      };

      // Iniciar envio
      const resultPromise = kafkaProducerRetryService.sendWithRetry(
        'test-topic',
        message,
        mockProducer
      );

      // Avançar o tempo para permitir todos os retries
      // Retry 1: 0ms (imediato)
      // Retry 2: após ~15ms
      // Retry 3: após ~30ms
      // Retry 4: após ~60ms
      // Total: ~105ms
      await vi.advanceTimersByTimeAsync(200);

      // Obter resultado
      const result = await resultPromise;

      // Verificar resultado
      expect(result).toEqual({
        success: false,
        attempts: 5, // 1 tentativa inicial + 4 retries
        error: expect.any(Error),
        sentToDLQ: true,
        totalTimeMs: 200,
      });

      // Verificar que send foi chamado para cada tentativa + DLQ
      expect(mockProducer.send).toHaveBeenCalledTimes(6); // 5 tentativas + 1 para DLQ

      // Verificar que a última chamada foi para a DLQ
      const lastCall = (mockProducer.send as Mock).mock.calls[
        (mockProducer.send as Mock).mock.calls.length - 1
      ];
      expect(lastCall[0].topic).toBe('test-dlq');
    });

    it('não deve tentar novamente para erros de serialização', async () => {
      // Configurar mock para falha de serialização
      (mockProducer.send as Mock).mockRejectedValue(
        new Error('serialization error')
      );

      // Mensagem de teste
      const message = {
        key: 'test-key',
        value: Buffer.from('test-value'),
      };

      // Enviar mensagem
      const result = await kafkaProducerRetryService.sendWithRetry(
        'test-topic',
        message,
        mockProducer
      );

      // Verificar resultado
      expect(result).toEqual({
        success: false,
        attempts: 1, // Apenas 1 tentativa, sem retry
        error: expect.any(Error),
        sentToDLQ: true,
        totalTimeMs: 0,
      });

      // Verificar que send foi chamado apenas duas vezes (tentativa original + DLQ)
      expect(mockProducer.send).toHaveBeenCalledTimes(2);
    });
  });

  describe('sendBatchWithRetry', () => {
    it('deve processar um lote de mensagens para múltiplos tópicos', async () => {
      // Configurar mock para sucesso e falha
      (mockProducer.send as Mock)
        .mockResolvedValueOnce(undefined) // Sucesso para primeira mensagem
        .mockRejectedValueOnce(new Error('connection error')) // Falha para segunda mensagem
        .mockResolvedValueOnce(undefined); // Sucesso para retry da segunda mensagem

      // Mensagens de teste
      const topicMessages = [
        {
          topic: 'topic-1',
          messages: [
            {
              key: 'key-1',
              value: Buffer.from('value-1'),
            },
          ],
        },
        {
          topic: 'topic-2',
          messages: [
            {
              key: 'key-2',
              value: Buffer.from('value-2'),
            },
          ],
        },
      ];

      // Iniciar envio em lote
      const resultPromise = kafkaProducerRetryService.sendBatchWithRetry(
        topicMessages,
        mockProducer
      );

      // Avançar o tempo para permitir o retry
      await vi.advanceTimersByTimeAsync(50);

      // Obter resultado
      const result = await resultPromise;

      // Verificar resultado
      expect(result).toEqual({
        'topic-1': [
          {
            success: true,
            attempts: 1,
            sentToDLQ: false,
            totalTimeMs: 0,
          },
        ],
        'topic-2': [
          {
            success: true,
            attempts: 2,
            sentToDLQ: false,
            totalTimeMs: 50,
          },
        ],
      });

      // Verificar que send foi chamado três vezes (1 para topic-1, 2 para topic-2)
      expect(mockProducer.send).toHaveBeenCalledTimes(3);
    });
  });
});
