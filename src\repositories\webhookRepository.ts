// src/repositories/webhookRepository.ts
import { queryHelper } from '@db/queryHelper';
import {
  CreateWebhookDeliveryDTO,
  CreateWebhookSubscriptionDTO,
  UpdateWebhookDeliveryDTO,
  UpdateWebhookSubscriptionDTO,
  WebhookDelivery,
  WebhookDeliveryStatus,
  WebhookEvent,
  WebhookStatus,
  WebhookSubscription,
  WebhookSubscriptionFilters,
} from '@models/WebhookSubscription';
import { generateRandomString } from '@utils/crypto';

/**
 * Repositório para gerenciamento de webhooks
 */
export const webhookRepository = {
  /**
   * Cria uma nova assinatura de webhook
   * @param data - Dados da assinatura
   * @returns Assinatura criada
   */
  async createSubscription(data: CreateWebhookSubscriptionDTO): Promise<WebhookSubscription> {
    // Gerar chave secreta se não fornecida
    const secretKey = data.secretKey || generateRandomString(32);

    const result = await queryHelper.queryOne(
      `INSERT INTO webhook_subscriptions 
       (name, url, events, secret_key, description, headers, created_by)
       VALUES ($1, $2, $3, $4, $5, $6, $7)
       RETURNING *`,
      [
        data.name,
        data.url,
        data.events,
        secretKey,
        data.description || null,
        data.headers ? JSON.stringify(data.headers) : null,
        data.createdBy || null,
      ]
    );

    return this.mapSubscriptionFromDb(result);
  },

  /**
   * Atualiza uma assinatura de webhook existente
   * @param id - ID da assinatura
   * @param data - Dados para atualização
   * @returns Assinatura atualizada
   */
  async updateSubscription(
    id: string,
    data: UpdateWebhookSubscriptionDTO
  ): Promise<WebhookSubscription> {
    // Construir query dinâmica com base nos campos fornecidos
    const updates = [];
    const values = [id];
    let paramIndex = 2;

    if (data.name !== undefined) {
      updates.push(`name = $${paramIndex++}`);
      values.push(data.name);
    }

    if (data.url !== undefined) {
      updates.push(`url = $${paramIndex++}`);
      values.push(data.url);
    }

    if (data.events !== undefined) {
      updates.push(`events = $${paramIndex++}`);
      values.push(data.events);
    }

    if (data.status !== undefined) {
      updates.push(`status = $${paramIndex++}`);
      values.push(data.status);
    }

    if (data.secretKey !== undefined) {
      updates.push(`secret_key = $${paramIndex++}`);
      values.push(data.secretKey);
    }

    if (data.description !== undefined) {
      updates.push(`description = $${paramIndex++}`);
      values.push(data.description);
    }

    if (data.headers !== undefined) {
      updates.push(`headers = $${paramIndex++}`);
      values.push(JSON.stringify(data.headers));
    }

    // Sempre atualizar o timestamp
    updates.push('updated_at = NOW()');

    if (updates.length === 0) {
      throw new Error('Nenhum dado fornecido para atualização');
    }

    const result = await queryHelper.queryOne(
      `UPDATE webhook_subscriptions
       SET ${updates.join(', ')}
       WHERE id = $1
       RETURNING *`,
      values
    );

    if (!result) {
      throw new Error(`Assinatura de webhook com ID ${id} não encontrada`);
    }

    return this.mapSubscriptionFromDb(result);
  },

  /**
   * Obtém uma assinatura de webhook por ID
   * @param id - ID da assinatura
   * @returns Assinatura encontrada ou null
   */
  async getSubscriptionById(id: string): Promise<WebhookSubscription | null> {
    const result = await queryHelper.queryOne('SELECT * FROM webhook_subscriptions WHERE id = $1', [
      id,
    ]);

    if (!result) {
      return null;
    }

    return this.mapSubscriptionFromDb(result);
  },

  /**
   * Lista assinaturas de webhook com filtros opcionais
   * @param filters - Filtros para a consulta
   * @returns Lista de assinaturas
   */
  async listSubscriptions(filters?: WebhookSubscriptionFilters): Promise<WebhookSubscription[]> {
    let query = 'SELECT * FROM webhook_subscriptions';
    const conditions = [];
    const values = [];
    let paramIndex = 1;

    if (filters) {
      if (filters.status !== undefined) {
        conditions.push(`status = $${paramIndex++}`);
        values.push(filters.status);
      }

      if (filters.event !== undefined) {
        conditions.push(`$${paramIndex++} = ANY(events)`);
        values.push(filters.event);
      }

      if (filters.createdBy !== undefined) {
        conditions.push(`created_by = $${paramIndex++}`);
        values.push(filters.createdBy);
      }
    }

    if (conditions.length > 0) {
      query += ` WHERE ${conditions.join(' AND ')}`;
    }

    query += ' ORDER BY created_at DESC';

    const results = await queryHelper.query(query, values);

    return results.map(this.mapSubscriptionFromDb);
  },

  /**
   * Exclui uma assinatura de webhook
   * @param id - ID da assinatura
   * @returns true se excluído com sucesso
   */
  async deleteSubscription(id: string): Promise<boolean> {
    const result = await queryHelper.query(
      'DELETE FROM webhook_subscriptions WHERE id = $1 RETURNING id',
      [id]
    );

    return result.length > 0;
  },

  /**
   * Atualiza o timestamp de último uso de uma assinatura
   * @param id - ID da assinatura
   */
  async updateLastUsed(id: string): Promise<void> {
    await queryHelper.query(
      `UPDATE webhook_subscriptions
       SET last_used_at = NOW()
       WHERE id = $1`,
      [id]
    );
  },

  /**
   * Encontra assinaturas ativas para um evento específico
   * @param event - Evento para buscar assinaturas
   * @returns Lista de assinaturas ativas para o evento
   */
  async findActiveSubscriptionsForEvent(event: WebhookEvent): Promise<WebhookSubscription[]> {
    const results = await queryHelper.query(
      `SELECT * FROM webhook_subscriptions
       WHERE status = $1
       AND $2 = ANY(events)
       ORDER BY created_at ASC`,
      [WebhookStatus.ACTIVE, event]
    );

    return results.map(this.mapSubscriptionFromDb);
  },

  /**
   * Cria um registro de entrega de webhook
   * @param data - Dados da entrega
   * @returns Entrega criada
   */
  async createDelivery(data: CreateWebhookDeliveryDTO): Promise<WebhookDelivery> {
    const result = await queryHelper.queryOne(
      `INSERT INTO webhook_deliveries
       (subscription_id, event, payload, status)
       VALUES ($1, $2, $3, $4)
       RETURNING *`,
      [data.subscriptionId, data.event, JSON.stringify(data.payload), WebhookDeliveryStatus.PENDING]
    );

    return this.mapDeliveryFromDb(result);
  },

  /**
   * Atualiza um registro de entrega de webhook
   * @param id - ID da entrega
   * @param data - Dados para atualização
   * @returns Entrega atualizada
   */
  async updateDelivery(id: string, data: UpdateWebhookDeliveryDTO): Promise<WebhookDelivery> {
    const updates = [];
    const values = [id];
    let paramIndex = 2;

    if (data.responseCode !== undefined) {
      updates.push(`response_code = $${paramIndex++}`);
      values.push(data.responseCode);
    }

    if (data.responseBody !== undefined) {
      updates.push(`response_body = $${paramIndex++}`);
      values.push(data.responseBody);
    }

    if (data.status !== undefined) {
      updates.push(`status = $${paramIndex++}`);
      values.push(data.status);
    }

    if (data.errorMessage !== undefined) {
      updates.push(`error_message = $${paramIndex++}`);
      values.push(data.errorMessage);
    }

    if (data.attemptCount !== undefined) {
      updates.push(`attempt_count = $${paramIndex++}`);
      values.push(data.attemptCount);
    }

    if (data.completedAt !== undefined) {
      updates.push(`completed_at = $${paramIndex++}`);
      values.push(data.completedAt);
    }

    if (updates.length === 0) {
      throw new Error('Nenhum dado fornecido para atualização');
    }

    const result = await queryHelper.queryOne(
      `UPDATE webhook_deliveries
       SET ${updates.join(', ')}
       WHERE id = $1
       RETURNING *`,
      values
    );

    if (!result) {
      throw new Error(`Entrega de webhook com ID ${id} não encontrada`);
    }

    return this.mapDeliveryFromDb(result);
  },

  /**
   * Obtém uma entrega de webhook por ID
   * @param id - ID da entrega
   * @returns Entrega encontrada ou null
   */
  async getDeliveryById(id: string): Promise<WebhookDelivery | null> {
    const result = await queryHelper.queryOne('SELECT * FROM webhook_deliveries WHERE id = $1', [
      id,
    ]);

    if (!result) {
      return null;
    }

    return this.mapDeliveryFromDb(result);
  },

  /**
   * Lista entregas de webhook para uma assinatura
   * @param subscriptionId - ID da assinatura
   * @param limit - Limite de resultados
   * @param offset - Deslocamento para paginação
   * @returns Lista de entregas
   */
  async listDeliveriesForSubscription(
    subscriptionId: string,
    limit = 50,
    offset = 0
  ): Promise<WebhookDelivery[]> {
    const results = await queryHelper.query(
      `SELECT * FROM webhook_deliveries
       WHERE subscription_id = $1
       ORDER BY created_at DESC
       LIMIT $2 OFFSET $3`,
      [subscriptionId, limit, offset]
    );

    return results.map(this.mapDeliveryFromDb);
  },

  /**
   * Mapeia um resultado do banco para o modelo WebhookSubscription
   * @param dbResult - Resultado do banco de dados
   * @returns Modelo WebhookSubscription
   */
  mapSubscriptionFromDb(dbResult: any): WebhookSubscription {
    return {
      id: dbResult.id,
      name: dbResult.name,
      url: dbResult.url,
      events: dbResult.events,
      status: dbResult.status,
      secretKey: dbResult.secret_key,
      description: dbResult.description,
      headers: dbResult.headers ? JSON.parse(dbResult.headers) : undefined,
      createdBy: dbResult.created_by,
      createdAt: new Date(dbResult.created_at),
      updatedAt: new Date(dbResult.updated_at),
      lastUsedAt: dbResult.last_used_at ? new Date(dbResult.last_used_at) : undefined,
    };
  },

  /**
   * Mapeia um resultado do banco para o modelo WebhookDelivery
   * @param dbResult - Resultado do banco de dados
   * @returns Modelo WebhookDelivery
   */
  mapDeliveryFromDb(dbResult: any): WebhookDelivery {
    return {
      id: dbResult.id,
      subscriptionId: dbResult.subscription_id,
      event: dbResult.event,
      payload: JSON.parse(dbResult.payload),
      responseCode: dbResult.response_code,
      responseBody: dbResult.response_body,
      status: dbResult.status,
      errorMessage: dbResult.error_message,
      attemptCount: dbResult.attempt_count,
      createdAt: new Date(dbResult.created_at),
      completedAt: dbResult.completed_at ? new Date(dbResult.completed_at) : undefined,
    };
  },
};
