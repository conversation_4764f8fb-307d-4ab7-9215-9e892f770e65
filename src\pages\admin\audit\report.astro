---
/**
 * Página de relatórios de auditoria
 *
 * Esta página gera e exibe relatórios personalizados
 * a partir dos logs de auditoria do sistema.
 */

import AuditReportGenerator from '@components/admin/AuditReportGenerator.astro';
import PermissionGate from '@components/auth/PermissionGate.astro';
// Importações
import MainLayout from '@layouts/MainLayout.astro';
import { auditRepository } from '@repository/auditRepository';
import { AuditEventType, AuditSeverity } from '@services/auditService';
import { getCurrentUser } from '@utils/authUtils';
import { logger } from '@utils/logger';

// Verificar autenticação
const user = await getCurrentUser(Astro.cookies);

// Redirecionar se não estiver autenticado
if (!user) {
  return Astro.redirect('/signin?redirect=/admin/audit/report');
}

// Título da página
const title = 'Relatórios de Auditoria';

// Verificar se há parâmetros para gerar relatório
const reportType = Astro.url.searchParams.get('reportType');
const startDateStr = Astro.url.searchParams.get('startDate');
const endDateStr = Astro.url.searchParams.get('endDate');
const userId = Astro.url.searchParams.get('userId') || undefined;
const resource = Astro.url.searchParams.get('resource') || undefined;
const eventTypesParam = Astro.url.searchParams.getAll('eventTypes');
const severitiesParam = Astro.url.searchParams.getAll('severities');
const format = Astro.url.searchParams.get('format') || 'html';

// Converter strings de data para objetos Date
const startDate = startDateStr ? new Date(startDateStr) : undefined;
const endDate = endDateStr ? new Date(endDateStr) : undefined;

// Determinar tipos de eventos com base no tipo de relatório
let eventTypes: string[] = [];

if (eventTypesParam.length > 0) {
  // Usar tipos de eventos especificados
  eventTypes = eventTypesParam;
} else if (reportType) {
  // Usar tipos de eventos predefinidos com base no tipo de relatório
  switch (reportType) {
    case 'activity':
      eventTypes = [
        AuditEventType.LOGIN_SUCCESS,
        AuditEventType.LOGIN_FAILURE,
        AuditEventType.LOGOUT,
        AuditEventType.RESOURCE_ACCESSED,
      ];
      break;

    case 'security':
      eventTypes = [
        AuditEventType.LOGIN_FAILURE,
        AuditEventType.PERMISSION_DENIED,
        AuditEventType.SYSTEM_ERROR,
        AuditEventType.SYSTEM_WARNING,
      ];
      break;

    case 'access':
      eventTypes = [
        AuditEventType.RESOURCE_ACCESSED,
        AuditEventType.PERMISSION_GRANTED,
        AuditEventType.PERMISSION_DENIED,
      ];
      break;

    case 'admin':
      eventTypes = [
        AuditEventType.USER_CREATED,
        AuditEventType.USER_UPDATED,
        AuditEventType.USER_DELETED,
        AuditEventType.ROLE_CREATED,
        AuditEventType.ROLE_UPDATED,
        AuditEventType.ROLE_DELETED,
        AuditEventType.PERMISSION_CREATED,
        AuditEventType.PERMISSION_UPDATED,
        AuditEventType.PERMISSION_DELETED,
      ];
      break;
  }
}

// Determinar severidades
const severities: string[] =
  severitiesParam.length > 0
    ? severitiesParam
    : [AuditEventType.INFO, AuditEventType.WARNING, AuditEventType.ERROR, AuditEventType.CRITICAL];

// Variáveis para armazenar resultados do relatório
let reportData: any[] = [];
let reportTitle = '';
let reportDescription = '';
let totalEvents = 0;

// Gerar relatório se houver parâmetros suficientes
if (startDate && endDate && (reportType || eventTypes.length > 0)) {
  try {
    // Definir título e descrição do relatório
    switch (reportType) {
      case 'activity':
        reportTitle = 'Relatório de Atividade de Usuários';
        reportDescription = 'Eventos de atividade dos usuários no sistema';
        break;

      case 'security':
        reportTitle = 'Relatório de Eventos de Segurança';
        reportDescription = 'Eventos relacionados à segurança do sistema';
        break;

      case 'access':
        reportTitle = 'Relatório de Acessos a Recursos';
        reportDescription = 'Eventos de acesso a recursos do sistema';
        break;

      case 'admin':
        reportTitle = 'Relatório de Ações Administrativas';
        reportDescription = 'Eventos de ações administrativas no sistema';
        break;

      case 'custom':
        reportTitle = 'Relatório Personalizado';
        reportDescription = 'Relatório personalizado com filtros específicos';
        break;
    }

    // Adicionar período ao título
    reportDescription += ` (${new Date(startDate).toLocaleDateString()} a ${new Date(endDate).toLocaleDateString()})`;

    // Buscar eventos para o relatório
    const limit = 1000; // Limite de eventos para o relatório
    const result = await auditRepository.read(
      undefined,
      eventTypes.length > 0 ? eventTypes : undefined,
      userId,
      resource,
      undefined,
      severities.length > 0 ? severities : undefined,
      startDate,
      endDate,
      limit
    );

    // Processar resultados
    reportData = result.rows.map((row) => ({
      ...row,
      metadata: row.metadata ? JSON.parse(row.metadata) : null,
      created_at: new Date(row.created_at).toLocaleString(),
    }));

    // Contar total de eventos
    totalEvents = await auditRepository.count(
      eventTypes.length > 0 ? eventTypes : undefined,
      userId,
      resource,
      undefined,
      severities.length > 0 ? severities : undefined,
      startDate,
      endDate
    );

    // Se o formato não for HTML, gerar arquivo para download
    if (format !== 'html') {
      // Preparar dados para exportação
      const exportData = reportData.map((event) => ({
        id: event.id,
        timestamp: event.created_at,
        event_type: event.event_type,
        user_name: event.user_name || '',
        user_id: event.ulid_user || '',
        resource: event.resource || '',
        resource_id: event.resource_id || '',
        action: event.action || '',
        result: event.result || '',
        severity: event.severity,
        ip_address: event.ip_address || '',
        metadata: event.metadata ? JSON.stringify(event.metadata) : '',
      }));

      // Gerar arquivo com base no formato solicitado
      switch (format) {
        case 'csv': {
          // Gerar CSV
          const csvHeader = Object.keys(exportData[0]).join(',');
          const csvRows = exportData.map((obj) =>
            Object.values(obj)
              .map((value) =>
                typeof value === 'string' ? `"${value.replace(/"/g, '""')}"` : value
              )
              .join(',')
          );
          const csvContent = [csvHeader, ...csvRows].join('\n');

          return new Response(csvContent, {
            status: 200,
            headers: {
              'Content-Type': 'text/csv',
              'Content-Disposition': `attachment; filename="audit-report-${new Date().toISOString().slice(0, 10)}.csv"`,
            },
          });
        }

        case 'json': {
          // Gerar JSON
          const jsonContent = JSON.stringify(
            {
              title: reportTitle,
              description: reportDescription,
              generated_at: new Date().toISOString(),
              total_events: totalEvents,
              events: exportData,
            },
            null,
            2
          );

          return new Response(jsonContent, {
            status: 200,
            headers: {
              'Content-Type': 'application/json',
              'Content-Disposition': `attachment; filename="audit-report-${new Date().toISOString().slice(0, 10)}.json"`,
            },
          });
        }

        case 'pdf':
          // Para PDF, redirecionamos para uma rota específica que gera o PDF
          return Astro.redirect(`/api/audit/report/pdf?${Astro.url.searchParams.toString()}`);
      }
    }
  } catch (error) {
    logger.error('Erro ao gerar relatório de auditoria:', error);
    reportTitle = 'Erro ao Gerar Relatório';
    reportDescription = 'Ocorreu um erro ao gerar o relatório de auditoria.';
  }
}

// Obter configurações de auditoria para descrições de eventos
const configResult = await auditRepository.getConfig();
const auditConfig = configResult.rows;

// Mapear descrições para tipos de eventos
const eventTypeDescriptions: Record<string, string> = {};
auditConfig.forEach((config) => {
  eventTypeDescriptions[config.event_type] = config.description;
});

// Função para obter classe CSS com base na severidade
function getSeverityClass(severity: string): string {
  switch (severity) {
    case AuditSeverity.INFO:
      return 'bg-blue-100 text-blue-800';
    case AuditSeverity.WARNING:
      return 'bg-yellow-100 text-yellow-800';
    case AuditSeverity.ERROR:
      return 'bg-red-100 text-red-800';
    case AuditSeverity.CRITICAL:
      return 'bg-purple-100 text-purple-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
}

// Função para obter classe CSS com base no resultado
function getResultClass(result: string): string {
  switch (result) {
    case 'success':
      return 'bg-green-100 text-green-800';
    case 'failure':
      return 'bg-red-100 text-red-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
}
---

<MainLayout title={title}>
  <main class="container mx-auto px-4 py-8">
    <PermissionGate resource="audit" action="read">
      <div class="flex items-center justify-between mb-6">
        <h1 class="text-3xl font-bold">{title}</h1>
        
        <div class="flex space-x-2">
          <a 
            href="/admin/audit" 
            class="bg-gray-200 text-gray-800 px-4 py-2 rounded hover:bg-gray-300 transition"
          >
            Voltar para Logs
          </a>
        </div>
      </div>
      
      {!reportType ? (
        <AuditReportGenerator />
      ) : (
        <div class="space-y-6">
          <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center justify-between mb-4">
              <div>
                <h2 class="text-xl font-semibold">{reportTitle}</h2>
                <p class="text-gray-600 mt-1">{reportDescription}</p>
              </div>
              
              <div class="flex space-x-2">
                <a 
                  href={`/admin/audit/report?${Astro.url.searchParams.toString().replace(/format=[^&]+/, 'format=csv')}`}
                  class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50 transition"
                  title="Exportar como CSV"
                >
                  CSV
                </a>
                <a 
                  href={`/admin/audit/report?${Astro.url.searchParams.toString().replace(/format=[^&]+/, 'format=json')}`}
                  class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50 transition"
                  title="Exportar como JSON"
                >
                  JSON
                </a>
                <a 
                  href={`/admin/audit/report?${Astro.url.searchParams.toString().replace(/format=[^&]+/, 'format=pdf')}`}
                  class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50 transition"
                  title="Exportar como PDF"
                >
                  PDF
                </a>
              </div>
            </div>
            
            <div class="text-sm text-gray-600 mb-4">
              <p>Total de eventos: <span class="font-medium">{totalEvents}</span></p>
              {totalEvents > reportData.length && (
                <p class="text-yellow-600">
                  Exibindo apenas os primeiros {reportData.length} eventos. Exporte o relatório para ver todos.
                </p>
              )}
            </div>
            
            {reportData.length > 0 ? (
              <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                  <thead class="bg-gray-50">
                    <tr>
                      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Data/Hora
                      </th>
                      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Evento
                      </th>
                      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Usuário
                      </th>
                      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Recurso
                      </th>
                      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Ação
                      </th>
                      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Resultado
                      </th>
                      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Severidade
                      </th>
                      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Detalhes
                      </th>
                    </tr>
                  </thead>
                  <tbody class="bg-white divide-y divide-gray-200">
                    {reportData.map(event => (
                      <tr>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {event.created_at}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                          <div class="text-sm font-medium text-gray-900">{eventTypeDescriptions[event.event_type] || event.event_type}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                          {event.user_name ? (
                            <div class="text-sm text-gray-900">{event.user_name}</div>
                          ) : (
                            <span class="text-sm text-gray-500">-</span>
                          )}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                          <div class="text-sm text-gray-900">{event.resource || '-'}</div>
                          {event.resource_id && (
                            <div class="text-xs text-gray-500">{event.resource_id}</div>
                          )}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {event.action || '-'}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                          {event.result && (
                            <span class={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getResultClass(event.result)}`}>
                              {event.result}
                            </span>
                          )}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                          <span class={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getSeverityClass(event.severity)}`}>
                            {event.severity}
                          </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          <button 
                            type="button"
                            class="text-blue-600 hover:text-blue-800"
                            data-event-id={event.id}
                          >
                            Ver detalhes
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <div class="p-6 text-center text-gray-500">
                Nenhum evento encontrado para os critérios selecionados.
              </div>
            )}
          </div>
          
          <div class="flex justify-between">
            <a 
              href="/admin/audit/report" 
              class="bg-gray-200 text-gray-800 px-4 py-2 rounded hover:bg-gray-300 transition"
            >
              Novo Relatório
            </a>
            
            <button 
              type="button"
              id="print-report"
              class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition"
            >
              Imprimir Relatório
            </button>
          </div>
        </div>
      )}
      
      <slot name="fallback">
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mt-6">
          <p>Você não tem permissão para acessar os relatórios de auditoria.</p>
          <p class="mt-2">
            <a href="/" class="text-red-700 font-medium hover:underline">Voltar para a página inicial</a>
          </p>
        </div>
      </slot>
    </PermissionGate>
  </main>
  
  <!-- Modal de detalhes -->
  <div id="event-details-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center hidden z-50">
    <div class="bg-white rounded-lg shadow-lg max-w-2xl w-full max-h-[80vh] overflow-y-auto">
      <div class="p-6 border-b">
        <div class="flex items-center justify-between">
          <h3 class="text-xl font-semibold" id="modal-title">Detalhes do Evento</h3>
          <button type="button" class="text-gray-400 hover:text-gray-500" id="close-modal">
            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
      </div>
      
      <div class="p-6" id="modal-content">
        <div class="space-y-4">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <!-- Conteúdo será preenchido via JavaScript -->
          </div>
          
          <div>
            <h4 class="font-medium mb-2">Metadados</h4>
            <pre id="event-metadata" class="bg-gray-50 p-4 rounded-md overflow-x-auto text-sm"></pre>
          </div>
        </div>
      </div>
      
      <div class="p-6 border-t bg-gray-50">
        <button 
          type="button"
          class="w-full bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition"
          id="close-modal-btn"
        >
          Fechar
        </button>
      </div>
    </div>
  </div>
</MainLayout>

<script>
  // Script para interatividade no cliente
  document.addEventListener('DOMContentLoaded', () => {
    // Modal de detalhes
    const modal = document.getElementById('event-details-modal');
    const closeModalBtn = document.getElementById('close-modal-btn');
    const closeModalX = document.getElementById('close-modal');
    const modalTitle = document.getElementById('modal-title');
    const modalContent = document.getElementById('modal-content').querySelector('.grid');
    const eventMetadata = document.getElementById('event-metadata');
    
    // Botões de detalhes
    const detailButtons = document.querySelectorAll('[data-event-id]');
    
    // Função para abrir modal com detalhes do evento
    const openEventDetails = async (eventId) => {
      try {
        // Buscar detalhes do evento
        const response = await fetch(`/api/audit/events/${eventId}`);
        const data = await response.json();
        
        if (data.success && data.event) {
          const event = data.event;
          
          // Atualizar título
          modalTitle.textContent = `Evento: ${event.eventType}`;
          
          // Limpar conteúdo anterior
          modalContent.innerHTML = '';
          
          // Adicionar detalhes do evento
          const details = [
            { label: 'ID', value: event.id },
            { label: 'Tipo', value: event.eventType },
            { label: 'Data/Hora', value: new Date(event.timestamp).toLocaleString() },
            { label: 'Usuário', value: event.userName || '-' },
            { label: 'ID do Usuário', value: event.userId || '-' },
            { label: 'IP', value: event.ipAddress || '-' },
            { label: 'User-Agent', value: event.userAgent || '-' },
            { label: 'Recurso', value: event.resource || '-' },
            { label: 'ID do Recurso', value: event.resourceId || '-' },
            { label: 'Ação', value: event.action || '-' },
            { label: 'Resultado', value: event.result || '-' },
            { label: 'Severidade', value: event.severity || '-' }
          ];
          
          details.forEach(detail => {
            const detailElement = document.createElement('div');
            detailElement.innerHTML = `
              <div class="text-sm font-medium text-gray-500">${detail.label}</div>
              <div class="text-sm text-gray-900">${detail.value}</div>
            `;
            modalContent.appendChild(detailElement);
          });
          
          // Adicionar metadados
          if (event.metadata) {
            eventMetadata.textContent = JSON.stringify(event.metadata, null, 2);
            eventMetadata.parentElement.classList.remove('hidden');
          } else {
            eventMetadata.textContent = '{}';
            eventMetadata.parentElement.classList.add('hidden');
          }
          
          // Exibir modal
          modal.classList.remove('hidden');
        } else {
          alert('Erro ao carregar detalhes do evento.');
        }
      } catch (error) {
        console.error('Erro ao carregar detalhes do evento:', error);
        alert('Erro ao carregar detalhes do evento.');
      }
    };
    
    // Adicionar evento de clique aos botões de detalhes
    detailButtons.forEach(button => {
      button.addEventListener('click', () => {
        const eventId = button.getAttribute('data-event-id');
        openEventDetails(eventId);
      });
    });
    
    // Fechar modal
    const closeModal = () => {
      modal.classList.add('hidden');
    };
    
    closeModalBtn.addEventListener('click', closeModal);
    closeModalX.addEventListener('click', closeModal);
    
    // Fechar modal ao clicar fora
    modal.addEventListener('click', (e) => {
      if (e.target === modal) {
        closeModal();
      }
    });
    
    // Fechar modal com tecla ESC
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape' && !modal.classList.contains('hidden')) {
        closeModal();
      }
    });
    
    // Botão de impressão
    const printReportBtn = document.getElementById('print-report');
    if (printReportBtn) {
      printReportBtn.addEventListener('click', () => {
        window.print();
      });
    }
  });
</script>

<style>
  /* Estilos específicos da página */
  @media print {
    header, footer, button, .flex.space-x-2, .flex.justify-between {
      display: none !important;
    }
    
    body, html {
      width: 100%;
      margin: 0;
      padding: 0;
    }
    
    main {
      width: 100%;
      padding: 0;
    }
    
    table {
      page-break-inside: auto;
    }
    
    tr {
      page-break-inside: avoid;
      page-break-after: auto;
    }
    
    thead {
      display: table-header-group;
    }
    
    tfoot {
      display: table-footer-group;
    }
  }
</style>
