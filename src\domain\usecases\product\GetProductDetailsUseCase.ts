/**
 * Get Product Details Use Case
 *
 * Caso de uso para obter detalhes de um produto.
 * Parte da implementação da tarefa 8.3.3 - Catálogo de produtos
 */

import { Product } from '../../entities/Product';
import { ProductCategory } from '../../entities/ProductCategory';
import { ProductCategoryRepository } from '../../repositories/ProductCategoryRepository';
import { ProductRepository } from '../../repositories/ProductRepository';

export interface GetProductDetailsRequest {
  productId?: string;
  productSlug?: string;
  includeRelated?: boolean;
  relatedLimit?: number;
}

export interface GetProductDetailsResponse {
  success: boolean;
  product?: Product;
  categories?: ProductCategory[];
  breadcrumb?: ProductCategory[];
  relatedProducts?: Product[];
  error?: string;
}

export class GetProductDetailsUseCase {
  constructor(
    private productRepository: ProductRepository,
    private categoryRepository: ProductCategoryRepository
  ) {}

  async execute(request: GetProductDetailsRequest): Promise<GetProductDetailsResponse> {
    try {
      // Verificar se foi fornecido ID ou slug
      if (!request.productId && !request.productSlug) {
        return {
          success: false,
          error: 'ID ou slug do produto não fornecido.',
        };
      }

      // Buscar o produto
      let product: Product | null = null;

      if (request.productId) {
        product = await this.productRepository.getById(request.productId);
      } else if (request.productSlug) {
        product = await this.productRepository.getBySlug(request.productSlug);
      }

      if (!product) {
        return {
          success: false,
          error: 'Produto não encontrado.',
        };
      }

      // Verificar se o produto está ativo e visível
      if (!product.isActive || !product.isVisible) {
        return {
          success: false,
          error: 'Produto não disponível.',
        };
      }

      // Buscar categorias do produto
      const categories: ProductCategory[] = [];
      for (const categoryId of product.categories) {
        const category = await this.categoryRepository.getById(categoryId);
        if (category?.isActive && category.isVisible) {
          categories.push(category);
        }
      }

      // Buscar breadcrumb da primeira categoria (se houver)
      let breadcrumb: ProductCategory[] = [];
      if (product.categories.length > 0) {
        breadcrumb = await this.categoryRepository.getCategoryPath(product.categories[0]);
      }

      // Buscar produtos relacionados se solicitado
      let relatedProducts: Product[] = [];
      if (request.includeRelated) {
        relatedProducts = await this.productRepository.getRelated(
          product.id,
          request.relatedLimit || 4
        );
      }

      return {
        success: true,
        product,
        categories,
        breadcrumb: breadcrumb.length > 0 ? breadcrumb : undefined,
        relatedProducts: relatedProducts.length > 0 ? relatedProducts : undefined,
      };
    } catch (error) {
      console.error('Erro ao obter detalhes do produto:', error);
      return {
        success: false,
        error: 'Ocorreu um erro ao processar a solicitação de detalhes do produto.',
      };
    }
  }
}
