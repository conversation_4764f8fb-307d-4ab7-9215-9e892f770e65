/**
 * Fluxo de processamento de eventos de pagamento
 *
 * Este serviço implementa o fluxo de processamento para eventos de pagamento,
 * incluindo atualização de status de pedidos, notificações e integrações.
 */

import { logger } from '@utils/logger';
import { queryHelper } from '@db/queryHelper';
import { PaymentStatus } from '@services/paymentService';
import { emailService } from '@services/emailService';
import { eventProducerService, type OrderEvent } from '@services/eventProducerService';
import { kafkaLoggingService } from '@services/kafka-logging.service';
import { alertService } from '@services/alertService';
import type { ProcessingContext } from '@services/messageProcessingService';

/**
 * Interface para evento de pagamento processado
 */
export interface ProcessedPaymentEvent {
  paymentId: string;
  orderId?: string;
  userId?: string;
  value: number;
  paymentType: string;
  status: string;
  externalId?: string;
  metadata?: Record<string, unknown>;
  processingTimestamp: string;
}

/**
 * Serviço de fluxo de eventos de pagamento
 */
export const paymentEventFlow = {
  /**
   * Processa um evento de pagamento e executa o fluxo completo
   * @param event - Evento de pagamento
   * @param context - Contexto de processamento
   */
  async processPaymentEvent(
    event: ProcessedPaymentEvent,
    context: ProcessingContext
  ): Promise<void> {
    try {
      logger.info(`Processando fluxo de pagamento para: ${event.paymentId}`, {
        paymentId: event.paymentId,
        status: event.status,
        orderId: event.orderId,
      });

      // Registrar início do processamento
      kafkaLoggingService.info(
        'payment.flow',
        `Iniciando fluxo de processamento para pagamento ${event.paymentId}`,
        {
          paymentId: event.paymentId,
          status: event.status,
          messageId: context.messageId,
        }
      );

      // Verificar se o pagamento existe
      const payment = await this.getPaymentDetails(event.paymentId);

      if (!payment) {
        logger.warn(`Pagamento não encontrado: ${event.paymentId}`);
        return;
      }

      // Processar com base no status
      switch (event.status) {
        case 'approved':
        case 'completed':
          await this.handleApprovedPayment(payment, event, context);
          break;
        case 'pending':
          await this.handlePendingPayment(payment, event, context);
          break;
        case 'rejected':
        case 'failed':
          await this.handleRejectedPayment(payment, event, context);
          break;
        case 'refunded':
          await this.handleRefundedPayment(payment, event, context);
          break;
        default:
          logger.warn(`Status de pagamento não tratado: ${event.status}`);
      }

      // Registrar conclusão do processamento
      kafkaLoggingService.info(
        'payment.flow',
        `Fluxo de processamento para pagamento ${event.paymentId} concluído`,
        {
          paymentId: event.paymentId,
          status: event.status,
          messageId: context.messageId,
        }
      );
    } catch (error) {
      logger.error(
        `Erro ao processar fluxo de pagamento ${event.paymentId}:`,
        error
      );
      kafkaLoggingService.error(
        'payment.flow',
        `Erro no fluxo de processamento para pagamento ${event.paymentId}`,
        error
      );

      // Registrar alerta para erro no processamento
      await alertService.createAlert('payment_processing_error', {
        paymentId: event.paymentId,
        error: error instanceof Error ? error.message : String(error),
        status: event.status,
      });
    }
  },

  /**
   * Obtém detalhes do pagamento
   * @param paymentId - ID do pagamento
   * @returns Detalhes do pagamento
   */
  async getPaymentDetails(paymentId: string): Promise<any> {
    const result = await queryHelper.query(
      `SELECT p.*, pt.type as payment_type, o.ulid_order, o.total as order_total,
              u.name as user_name, u.email as user_email, u.ulid_user
       FROM tab_payment p
       JOIN tab_payment_type pt ON p.ulid_payment_type = pt.ulid_payment_type
       LEFT JOIN tab_order o ON p.ulid_order = o.ulid_order
       LEFT JOIN tab_user u ON o.ulid_user = u.ulid_user
       WHERE p.ulid_payment = $1`,
      [paymentId]
    );

    return result.rows[0];
  },

  /**
   * Processa um pagamento aprovado
   * @param payment - Dados do pagamento
   * @param event - Evento de pagamento
   * @param context - Contexto de processamento
   */
  async handleApprovedPayment(
    payment: any,
    event: ProcessedPaymentEvent,
    context: ProcessingContext
  ): Promise<void> {
    // Atualizar status do pagamento se necessário
    if (payment.cod_status !== PaymentStatus.APPROVED) {
      await queryHelper.query(
        'UPDATE tab_payment SET cod_status = $1, updated_at = NOW() WHERE ulid_payment = $2',
        [PaymentStatus.APPROVED, payment.ulid_payment]
      );
    }

    // Atualizar status do pedido se existir
    if (payment.ulid_order) {
      await queryHelper.query(
        'UPDATE tab_order SET cod_status = 2, updated_at = NOW() WHERE ulid_order = $1',
        [payment.ulid_order]
      );

      // Produzir evento de atualização de status do pedido
      const orderEvent: OrderEvent = {
        ...eventProducerService.createBaseEvent(`order-status-${payment.ulid_order}`),
        orderId: payment.ulid_order,
        userId: payment.ulid_user,
        status: 'paid',
        total: parseFloat(payment.order_total),
      };

      await eventProducerService.sendEvent(
        'order',
        'status',
        'changed',
        payment.ulid_order,
        orderEvent
      );
    }

    // Enviar email de confirmação
    if (payment.user_email) {
      try {
        await emailService.sendPaymentConfirmationEmail({
          email: payment.user_email,
          name: payment.user_name,
          orderId: payment.ulid_order,
          paymentId: payment.ulid_payment,
          amount: payment.value,
          paymentMethod: payment.payment_type,
          date: new Date().toISOString(),
        });
      } catch (error) {
        logger.error(
          `Erro ao enviar email de confirmação para pagamento ${payment.ulid_payment}:`,
          error
        );
      }
    }

    // Verificar se há produtos digitais para liberar acesso
    if (payment.ulid_order) {
      await this.processDigitalProducts(payment.ulid_order, payment.ulid_user);
    }

    // Registrar evento de pagamento aprovado
    kafkaLoggingService.info(
      'payment.flow',
      `Pagamento ${payment.ulid_payment} aprovado e processado`,
      {
        paymentId: payment.ulid_payment,
        orderId: payment.ulid_order,
        userId: payment.ulid_user,
        value: payment.value,
        messageId: context.messageId,
      }
    );
  },

  /**
   * Processa um pagamento pendente
   * @param payment - Dados do pagamento
   * @param event - Evento de pagamento
   * @param context - Contexto de processamento
   */
  async handlePendingPayment(
    payment: any,
    event: ProcessedPaymentEvent,
    context: ProcessingContext
  ): Promise<void> {
    // Atualizar status do pagamento se necessário
    if (payment.cod_status !== PaymentStatus.PENDING) {
      await queryHelper.query(
        'UPDATE tab_payment SET cod_status = $1, updated_at = NOW() WHERE ulid_payment = $2',
        [PaymentStatus.PENDING, payment.ulid_payment]
      );
    }

    // Enviar email de pagamento pendente
    if (payment.user_email) {
      try {
        await emailService.sendPaymentPendingEmail({
          email: payment.user_email,
          name: payment.user_name,
          orderId: payment.ulid_order,
          paymentId: payment.ulid_payment,
          amount: payment.value,
          paymentMethod: payment.payment_type,
          date: new Date().toISOString(),
        });
      } catch (error) {
        logger.error(
          `Erro ao enviar email de pagamento pendente ${payment.ulid_payment}:`,
          error
        );
      }
    }

    // Registrar evento de pagamento pendente
    kafkaLoggingService.info(
      'payment.flow',
      `Pagamento ${payment.ulid_payment} pendente`,
      {
        paymentId: payment.ulid_payment,
        orderId: payment.ulid_order,
        userId: payment.ulid_user,
        value: payment.value,
        messageId: context.messageId,
      }
    );
  },

  /**
   * Processa um pagamento rejeitado
   * @param payment - Dados do pagamento
   * @param event - Evento de pagamento
   * @param context - Contexto de processamento
   */
  async handleRejectedPayment(
    payment: any,
    event: ProcessedPaymentEvent,
    context: ProcessingContext
  ): Promise<void> {
    // Atualizar status do pagamento se necessário
    if (payment.cod_status !== PaymentStatus.REJECTED) {
      await queryHelper.query(
        'UPDATE tab_payment SET cod_status = $1, updated_at = NOW() WHERE ulid_payment = $2',
        [PaymentStatus.REJECTED, payment.ulid_payment]
      );
    }

    // Enviar email de pagamento rejeitado
    if (payment.user_email) {
      try {
        await emailService.sendPaymentRejectedEmail({
          email: payment.user_email,
          name: payment.user_name,
          orderId: payment.ulid_order,
          paymentId: payment.ulid_payment,
          amount: payment.value,
          paymentMethod: payment.payment_type,
          date: new Date().toISOString(),
          reason: event.metadata?.rejectionReason || 'Pagamento não aprovado',
        });
      } catch (error) {
        logger.error(
          `Erro ao enviar email de pagamento rejeitado ${payment.ulid_payment}:`,
          error
        );
      }
    }

    // Registrar alerta para pagamento rejeitado
    await alertService.createAlert('payment_rejected', {
      paymentId: payment.ulid_payment,
      orderId: payment.ulid_order,
      userId: payment.ulid_user,
      value: payment.value,
      reason: event.metadata?.rejectionReason,
    });

    // Registrar evento de pagamento rejeitado
    kafkaLoggingService.info(
      'payment.flow',
      `Pagamento ${payment.ulid_payment} rejeitado`,
      {
        paymentId: payment.ulid_payment,
        orderId: payment.ulid_order,
        userId: payment.ulid_user,
        value: payment.value,
        reason: event.metadata?.rejectionReason,
        messageId: context.messageId,
      }
    );
  },

  /**
   * Processa um pagamento reembolsado
   * @param payment - Dados do pagamento
   * @param event - Evento de pagamento
   * @param context - Contexto de processamento
   */
  async handleRefundedPayment(
    payment: any,
    event: ProcessedPaymentEvent,
    context: ProcessingContext
  ): Promise<void> {
    // Atualizar status do pagamento se necessário
    if (payment.cod_status !== PaymentStatus.REFUNDED) {
      await queryHelper.query(
        'UPDATE tab_payment SET cod_status = $1, updated_at = NOW() WHERE ulid_payment = $2',
        [PaymentStatus.REFUNDED, payment.ulid_payment]
      );
    }

    // Enviar email de reembolso
    if (payment.user_email) {
      try {
        await emailService.sendRefundConfirmationEmail({
          email: payment.user_email,
          name: payment.user_name,
          orderId: payment.ulid_order,
          paymentId: payment.ulid_payment,
          amount: payment.value,
          paymentMethod: payment.payment_type,
          date: new Date().toISOString(),
          reason: event.metadata?.refundReason || 'Reembolso solicitado',
        });
      } catch (error) {
        logger.error(
          `Erro ao enviar email de reembolso ${payment.ulid_payment}:`,
          error
        );
      }
    }

    // Registrar evento de pagamento reembolsado
    kafkaLoggingService.info(
      'payment.flow',
      `Pagamento ${payment.ulid_payment} reembolsado`,
      {
        paymentId: payment.ulid_payment,
        orderId: payment.ulid_order,
        userId: payment.ulid_user,
        value: payment.value,
        reason: event.metadata?.refundReason,
        messageId: context.messageId,
      }
    );
  },

  /**
   * Processa produtos digitais para um pedido
   * @param orderId - ID do pedido
   * @param userId - ID do usuário
   */
  async processDigitalProducts(orderId: string, userId: string): Promise<void> {
    try {
      // Verificar se há produtos digitais no pedido
      const result = await queryHelper.query(
        `SELECT oi.ulid_product, p.name, p.type
         FROM tab_order_item oi
         JOIN tab_product p ON oi.ulid_product = p.ulid_product
         WHERE oi.ulid_order = $1 AND p.type = 'digital'`,
        [orderId]
      );

      if (result.rows.length === 0) {
        return; // Não há produtos digitais
      }

      // Liberar acesso aos produtos digitais
      for (const product of result.rows) {
        await queryHelper.query(
          `INSERT INTO tab_user_product (
             ulid_user_product,
             ulid_user,
             ulid_product,
             ulid_order,
             access_granted_at,
             expires_at,
             created_at,
             updated_at
           ) VALUES (
             $1, $2, $3, $4, NOW(), 
             CASE WHEN $5 = 'subscription' THEN NOW() + INTERVAL '1 month' ELSE NULL END,
             NOW(), NOW()
           )
           ON CONFLICT (ulid_user, ulid_product) DO UPDATE
           SET access_granted_at = NOW(),
               expires_at = CASE WHEN $5 = 'subscription' THEN NOW() + INTERVAL '1 month' ELSE NULL END,
               updated_at = NOW()`,
          [
            crypto.randomUUID(),
            userId,
            product.ulid_product,
            orderId,
            product.type,
          ]
        );

        logger.info(
          `Acesso liberado ao produto digital ${product.name} para usuário ${userId}`
        );
      }
    } catch (error) {
      logger.error(
        `Erro ao processar produtos digitais para pedido ${orderId}:`,
        error
      );
      throw error;
    }
  },
};
