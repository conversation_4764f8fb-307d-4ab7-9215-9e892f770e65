/**
 * Issue Fiscal Document Use Case
 *
 * Caso de uso para emissão de documentos fiscais.
 * Parte da implementação da tarefa 8.7.1 - Integração fiscal
 */

import { FiscalDocument, FiscalDocumentType } from '../../entities/FiscalDocument';
import { FiscalDocumentRepository } from '../../repositories/FiscalDocumentRepository';
import { FiscalProviderService } from '../../services/FiscalProviderService';

export interface IssueFiscalDocumentRequest {
  documentId: string;
}

export interface IssueFiscalDocumentResponse {
  success: boolean;
  document?: FiscalDocument;
  error?: string;
}

export class IssueFiscalDocumentUseCase {
  constructor(
    private fiscalDocumentRepository: FiscalDocumentRepository,
    private fiscalProviderService: FiscalProviderService
  ) {}

  async execute(request: IssueFiscalDocumentRequest): Promise<IssueFiscalDocumentResponse> {
    try {
      // Validar os dados de entrada
      if (!request.documentId) {
        return {
          success: false,
          error: 'ID do documento é obrigatório.',
        };
      }

      // Verificar se o provedor fiscal está pronto
      if (!this.fiscalProviderService.isReady()) {
        return {
          success: false,
          error: 'Serviço de emissão fiscal não está disponível no momento.',
        };
      }

      // Obter o documento fiscal
      const document = await this.fiscalDocumentRepository.getById(request.documentId);

      if (!document) {
        return {
          success: false,
          error: `Documento fiscal com ID ${request.documentId} não encontrado.`,
        };
      }

      // Verificar se o documento pode ser emitido
      if (document.status !== 'DRAFT' && document.status !== 'ERROR') {
        return {
          success: false,
          error: `Documento fiscal com status ${document.status} não pode ser emitido.`,
        };
      }

      // Verificar se o tipo de documento é suportado pelo provedor
      const supportedTypes = this.fiscalProviderService.getSupportedDocumentTypes();
      if (!supportedTypes.includes(document.type)) {
        return {
          success: false,
          error: `Tipo de documento ${document.type} não é suportado pelo provedor fiscal.`,
        };
      }

      // Atualizar status para PROCESSING
      await this.fiscalDocumentRepository.updateStatus(document.id, 'PROCESSING');

      // Emitir documento fiscal
      const issueResult = await this.fiscalProviderService.issueDocument(document);

      if (!issueResult.success) {
        // Atualizar status para ERROR
        await this.fiscalDocumentRepository.updateStatus(
          document.id,
          'ERROR',
          issueResult.errorMessage
        );

        return {
          success: false,
          error: issueResult.errorMessage || 'Erro ao emitir documento fiscal.',
        };
      }

      // Atualizar informações de emissão
      if (issueResult.documentNumber && issueResult.documentSeries && issueResult.issueDate) {
        await this.fiscalDocumentRepository.setIssuedInfo(
          document.id,
          issueResult.documentNumber,
          issueResult.documentSeries,
          issueResult.issueDate,
          issueResult.xmlContent || '',
          issueResult.pdfUrl || ''
        );

        // Obter documento atualizado
        const updatedDocument = await this.fiscalDocumentRepository.getById(request.documentId);

        return {
          success: true,
          document: updatedDocument || undefined,
        };
      }
      // Atualizar status para ERROR
      await this.fiscalDocumentRepository.updateStatus(
        document.id,
        'ERROR',
        'Resposta de emissão incompleta.'
      );

      return {
        success: false,
        error: 'Resposta de emissão incompleta.',
      };
    } catch (error) {
      console.error('Erro ao emitir documento fiscal:', error);

      // Atualizar status para ERROR
      await this.fiscalDocumentRepository.updateStatus(
        request.documentId,
        'ERROR',
        error instanceof Error ? error.message : 'Erro desconhecido ao emitir documento fiscal.'
      );

      return {
        success: false,
        error:
          error instanceof Error ? error.message : 'Erro desconhecido ao emitir documento fiscal.',
      };
    }
  }
}
