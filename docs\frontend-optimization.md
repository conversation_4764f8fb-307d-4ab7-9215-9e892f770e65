# Sistema de Otimização de Frontend

## Visão Geral

O sistema de otimização de frontend implementa as melhores práticas para melhorar a performance, acessibilidade e experiência do usuário no frontend da aplicação. Este sistema visa melhorar as métricas do Core Web Vitals, reduzir o tempo de carregamento e melhorar a eficiência do código frontend.

## Arquitetura

O sistema de otimização de frontend é composto pelos seguintes componentes:

1. **Serviço de Otimização (frontendOptimizationService)**: Implementa a lógica de otimização de assets e configuração de cache.
2. **Componentes Otimizados**: Componentes Astro que implementam carregamento otimizado de recursos.
3. **Estratégias de Carregamento**: Diferentes abordagens para carregamento de recursos com base na criticidade.
4. **Políticas de Cache**: Configurações para cache eficiente de recursos estáticos.

## Componentes Otimizados

### OptimizedImage

O componente `OptimizedImage` implementa carregamento otimizado de imagens com:

- **Formatos Modernos**: Suporte para WebP e AVIF para navegadores modernos com fallback para formatos tradicionais.
- **Imagens Responsivas**: Geração de múltiplas resoluções com `srcset` e `sizes`.
- **Lazy Loading**: Carregamento sob demanda usando Intersection Observer.
- **Placeholders**: Diferentes tipos de placeholders durante o carregamento (cor sólida, versão desfocada, cor dominante).
- **Prevenção de Layout Shift**: Reserva de espaço para imagens antes do carregamento.

Exemplo de uso:

```astro
<OptimizedImage
  src="/images/hero.jpg"
  alt="Imagem de destaque"
  widths={[400, 800, 1200, 1600, 2000]}
  sizes="(max-width: 768px) 100vw, 50vw"
  aspectRatio={16/9}
  placeholderType="blur"
  fadeIn={true}
/>
```

### OptimizedScript

O componente `OptimizedScript` implementa carregamento otimizado de scripts com:

- **Carregamento Assíncrono**: Carregamento não-bloqueante de scripts.
- **Carregamento Diferido**: Carregamento após a renderização inicial.
- **Preload**: Precarregamento para scripts críticos.
- **Estratégias de Carregamento**: Diferentes estratégias baseadas na criticidade do script.

Exemplo de uso:

```astro
<OptimizedScript
  src="/js/analytics.js"
  strategy="lazyOnload"
  async={true}
/>

<OptimizedScript
  src="/js/critical.js"
  critical={true}
  strategy="default"
/>
```

### OptimizedStyle

O componente `OptimizedStyle` implementa carregamento otimizado de estilos CSS com:

- **Carregamento Assíncrono**: Carregamento não-bloqueante de estilos.
- **Preload**: Precarregamento para estilos críticos.
- **Inline**: Inclusão inline para estilos críticos.
- **Carregamento Diferido**: Carregamento após a renderização inicial para estilos não críticos.

Exemplo de uso:

```astro
<OptimizedStyle
  href="/css/critical.css"
  critical={true}
  strategy="default"
/>

<OptimizedStyle
  href="/css/non-critical.css"
  strategy="lazy"
/>
```

## Estratégias de Carregamento

### Imagens

- **Default**: Carregamento padrão com lazy loading nativo.
- **Priority**: Carregamento prioritário para imagens acima da dobra.
- **Lazy**: Carregamento sob demanda usando Intersection Observer.

### Scripts

- **Default**: Carregamento normal com atributos async/defer.
- **AfterInteractive**: Carregamento após a página se tornar interativa.
- **LazyOnload**: Carregamento quando o usuário rola até o final da página.
- **Worker**: Carregamento via Service Worker para reduzir o impacto na thread principal.

### Estilos

- **Default**: Carregamento normal bloqueante para estilos críticos.
- **Async**: Carregamento assíncrono não-bloqueante.
- **Defer**: Carregamento após o evento DOMContentLoaded.
- **Lazy**: Carregamento sob demanda usando Intersection Observer.

## Políticas de Cache

O sistema configura políticas de cache otimizadas para diferentes tipos de recursos:

- **JavaScript**: Cache de longa duração com `Cache-Control: public, max-age=31536000, immutable`.
- **CSS**: Cache de longa duração com `Cache-Control: public, max-age=31536000, immutable`.
- **Imagens**: Cache com revalidação com `Cache-Control: public, max-age=604800, stale-while-revalidate=86400`.
- **Fontes**: Cache de longa duração com `Cache-Control: public, max-age=31536000, immutable`.

## Implementação Técnica

### Otimização de Imagens

O componente `OptimizedImage` implementa as seguintes otimizações:

```typescript
// Determinar classes CSS para efeito de fade-in
const imgClasses = [
  'optimized-image',
  fadeIn ? 'fade-in' : '',
].filter(Boolean).join(' ');

// Determinar estilo para efeito de fade-in
const imgStyle = fadeIn ? `transition: opacity ${fadeInDuration}ms ease-in-out; opacity: 0;` : '';

// Determinar atributo loading
const loadingAttr = nativeLazy ? loading : null;
```

### Otimização de Scripts

O componente `OptimizedScript` implementa carregamento diferido com:

```javascript
document.addEventListener('DOMContentLoaded', function() {
  const script = document.createElement('script');
  script.src = "${src}";
  script.id = "${scriptId}";
  ${isModule ? 'script.type = "module";' : ''}
  document.body.appendChild(script);
});
```

### Otimização de Estilos

O componente `OptimizedStyle` implementa carregamento assíncrono com:

```javascript
const link = document.createElement('link');
link.rel = 'stylesheet';
link.href = "${href}";
link.id = "${id}";
link.media = "print";
link.addEventListener('load', function() {
  link.media = 'all';
});
document.head.appendChild(link);
```

## Boas Práticas

### Imagens

1. **Use Formatos Modernos**: Prefira WebP e AVIF para melhor compressão.
2. **Dimensione Corretamente**: Forneça imagens no tamanho correto para cada dispositivo.
3. **Defina Aspect Ratio**: Sempre defina a proporção para evitar layout shift.
4. **Priorize Imagens Críticas**: Use `priority={true}` para imagens acima da dobra.
5. **Use Placeholders**: Sempre use placeholders para melhorar a percepção de carregamento.

### Scripts

1. **Minimize JavaScript**: Reduza o tamanho dos scripts com minificação e tree-shaking.
2. **Use Módulos ES**: Prefira módulos ES para melhor tree-shaking e carregamento.
3. **Divida o Código**: Divida o código em chunks menores para carregamento sob demanda.
4. **Priorize Scripts Críticos**: Use `critical={true}` para scripts essenciais.
5. **Adie Scripts Não Críticos**: Use `strategy="lazyOnload"` para scripts não essenciais.

### Estilos

1. **Extraia CSS Crítico**: Inline o CSS crítico para renderização inicial.
2. **Minimize CSS**: Reduza o tamanho dos estilos com minificação e purge.
3. **Divida o CSS**: Divida o CSS por rota ou componente para carregamento sob demanda.
4. **Priorize Estilos Críticos**: Use `critical={true}` para estilos essenciais.
5. **Adie Estilos Não Críticos**: Use `strategy="lazy"` para estilos não essenciais.

## Monitoramento e Métricas

O sistema registra as seguintes métricas:

- `frontend_js_size`: Tamanho total de JavaScript em KB.
- `frontend_css_size`: Tamanho total de CSS em KB.
- `frontend_image_size`: Tamanho total de imagens em KB.
- `frontend_total_size`: Tamanho total de assets em KB.
- `frontend_load_time`: Tempo de carregamento da página em ms.

## Troubleshooting

### Problemas Comuns

1. **Layout Shift**: 
   - Verifique se todas as imagens têm dimensões ou aspect ratio definidos.
   - Verifique se fontes personalizadas estão sendo carregadas corretamente.
   - Use a ferramenta Lighthouse para identificar elementos causando CLS.

2. **Tempo de Carregamento Lento**:
   - Verifique o tamanho dos assets (imagens, scripts, estilos).
   - Verifique se os assets estão sendo comprimidos corretamente.
   - Verifique se o cache está configurado corretamente.

3. **JavaScript Bloqueante**:
   - Use `async` ou `defer` para scripts não críticos.
   - Mova scripts não críticos para o final do body.
   - Divida o código em chunks menores.

### Ferramentas de Diagnóstico

- **Lighthouse**: Ferramenta do Google para análise de performance.
- **WebPageTest**: Ferramenta para teste de performance em diferentes dispositivos e localizações.
- **Chrome DevTools**: Ferramenta para análise de performance e depuração.

## Referências

- [Web Vitals](https://web.dev/vitals/)
- [Optimize LCP](https://web.dev/optimize-lcp/)
- [Optimize CLS](https://web.dev/optimize-cls/)
- [Optimize FID](https://web.dev/optimize-fid/)
- [Astro Performance](https://docs.astro.build/en/guides/performance/)
- [Image Optimization](https://web.dev/fast/#optimize-your-images)
- [JavaScript Optimization](https://web.dev/fast/#optimize-your-javascript)
- [CSS Optimization](https://web.dev/fast/#optimize-your-css)
