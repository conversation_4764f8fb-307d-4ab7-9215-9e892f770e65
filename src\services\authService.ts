import { userRepository } from '@repositories/userRepository';
import type { User } from '@repositories/userRepository';
import type { AstroGlobal } from 'astro';
// src/services/authService.ts
import bcrypt from 'bcryptjs';

/**
 * Serviço para autenticação de usuários
 */
export const authService = {
  /**
   * Autentica um usuário com email e senha
   * @param email - Email do usuário
   * @param password - Senha do usuário
   * @returns Usuário autenticado (sem senha) ou null
   */
  async authenticate(email: string, password: string): Promise<Omit<User, 'password_hash'> | null> {
    const user = await userRepository.findByEmail(email);

    if (!user) {
      return null;
    }

    const isPasswordValid = await bcrypt.compare(password, user.password_hash);

    if (!isPasswordValid) {
      return null;
    }

    // Retornar usuário sem a senha
    const { password_hash, ...userWithoutPassword } = user;
    return userWithoutPassword;
  },

  /**
   * Registra um novo usuário
   * @param name - Nome do usuário
   * @param email - Email do usuário
   * @param password - Senha do usuário
   * @returns ID do usuário criado ou null
   */
  async register(name: string, email: string, password: string): Promise<number | null> {
    // Verificar se o email já está em uso
    const existingUser = await userRepository.findByEmail(email);

    if (existingUser) {
      return null;
    }

    // Hash da senha
    const passwordHash = await bcrypt.hash(password, 10);

    // Criar usuário
    return await userRepository.create({
      name,
      email,
      password_hash: passwordHash,
    });
  },

  /**
   * Inicia a sessão do usuário
   * @param Astro - Contexto Astro global
   * @param user - Usuário autenticado
   */
  async login(Astro: AstroGlobal, user: Omit<User, 'password_hash'>): Promise<void> {
    await Astro.session.set('user', user);
    await Astro.session.set('isLoggedIn', true);
    await Astro.session.set('loginTime', new Date().toISOString());
  },

  /**
   * Encerra a sessão do usuário
   * @param Astro - Contexto Astro global
   */
  async logout(Astro: AstroGlobal): Promise<void> {
    await Astro.session.delete('user');
    await Astro.session.delete('isLoggedIn');
    await Astro.session.delete('loginTime');
  },

  /**
   * Verifica se o usuário está autenticado
   * @param Astro - Contexto Astro global
   * @returns Verdadeiro se o usuário estiver autenticado
   */
  async isAuthenticated(Astro: AstroGlobal): Promise<boolean> {
    return (await Astro.session.get('isLoggedIn')) === true;
  },

  /**
   * Obtém o usuário autenticado
   * @param Astro - Contexto Astro global
   * @returns Usuário autenticado ou null
   */
  async getAuthenticatedUser(Astro: AstroGlobal): Promise<Omit<User, 'password_hash'> | null> {
    return await Astro.session.get('user');
  },
};
