/**
 * Cancel Fiscal Document Use Case
 *
 * Caso de uso para cancelamento de documentos fiscais.
 * Parte da implementação da tarefa 8.7.1 - Integração fiscal
 */

import { FiscalDocument } from '../../entities/FiscalDocument';
import { FiscalDocumentRepository } from '../../repositories/FiscalDocumentRepository';
import { FiscalProviderService } from '../../services/FiscalProviderService';

export interface CancelFiscalDocumentRequest {
  documentId: string;
  reason: string;
}

export interface CancelFiscalDocumentResponse {
  success: boolean;
  document?: FiscalDocument;
  error?: string;
}

export class CancelFiscalDocumentUseCase {
  constructor(
    private fiscalDocumentRepository: FiscalDocumentRepository,
    private fiscalProviderService: FiscalProviderService
  ) {}

  async execute(request: CancelFiscalDocumentRequest): Promise<CancelFiscalDocumentResponse> {
    try {
      // Validar os dados de entrada
      if (!request.documentId) {
        return {
          success: false,
          error: 'ID do documento é obrigatório.',
        };
      }

      if (!request.reason || request.reason.trim().length < 5) {
        return {
          success: false,
          error: 'Motivo do cancelamento é obrigatório e deve ter pelo menos 5 caracteres.',
        };
      }

      // Verificar se o provedor fiscal está pronto
      if (!this.fiscalProviderService.isReady()) {
        return {
          success: false,
          error: 'Serviço de emissão fiscal não está disponível no momento.',
        };
      }

      // Obter o documento fiscal
      const document = await this.fiscalDocumentRepository.getById(request.documentId);

      if (!document) {
        return {
          success: false,
          error: `Documento fiscal com ID ${request.documentId} não encontrado.`,
        };
      }

      // Verificar se o documento pode ser cancelado
      if (document.status !== 'ISSUED') {
        return {
          success: false,
          error: `Documento fiscal com status ${document.status} não pode ser cancelado.`,
        };
      }

      // Verificar se o documento tem as informações necessárias
      if (!document.number || !document.series) {
        return {
          success: false,
          error: 'Documento fiscal não possui número ou série.',
        };
      }

      // Cancelar documento fiscal
      const cancelResult = await this.fiscalProviderService.cancelDocument(
        document.type,
        document.number,
        document.series,
        request.reason
      );

      if (!cancelResult.success) {
        return {
          success: false,
          error: cancelResult.errorMessage || 'Erro ao cancelar documento fiscal.',
        };
      }

      // Atualizar status para CANCELLED
      await this.fiscalDocumentRepository.cancel(document.id, request.reason);

      // Obter documento atualizado
      const updatedDocument = await this.fiscalDocumentRepository.getById(request.documentId);

      return {
        success: true,
        document: updatedDocument || undefined,
      };
    } catch (error) {
      console.error('Erro ao cancelar documento fiscal:', error);

      return {
        success: false,
        error:
          error instanceof Error
            ? error.message
            : 'Erro desconhecido ao cancelar documento fiscal.',
      };
    }
  }
}
