---
import ContactForm from '../../components/forms/ContactForm.astro';
import { PageTransition } from '../../components/transitions';
import Breadcrumbs from '../../components/ui/navigation/Breadcrumbs.astro';
/**
 * Página de Contato
 *
 * Página com formulário de contato acessível.
 * Parte da implementação da tarefa 8.6.1 - Implementação de formulário de contato
 */
import BaseLayout from '../../layouts/BaseLayout.astro';
import { Container, Section } from '../../layouts/grid';

// Título da página
const title = 'Entre em Contato';

// Breadcrumbs para a página atual
const breadcrumbItems = [{ href: '/', label: 'Início' }, { label: 'Contato' }];

// Informações de contato
const contactInfo = {
  address: 'Av. Educação, 1234 - Centro, São Paulo - SP, 01234-567',
  phone: '(11) 1234-5678',
  email: '<EMAIL>',
  hours: 'Segunda a Sexta: 9h às 18h',
};

// Perguntas frequentes
const faqs = [
  {
    question: 'Como posso acessar os materiais educacionais?',
    answer:
      'Após a compra, você receberá um e-mail com instruções de acesso. Basta fazer login na plataforma com seu e-mail e senha para acessar todos os materiais adquiridos.',
  },
  {
    question: 'Vocês oferecem suporte para professores?',
    answer:
      "Sim! Temos uma equipe dedicada para auxiliar professores na utilização dos nossos materiais em sala de aula. Entre em contato pelo formulário selecionando a categoria 'Suporte'.",
  },
  {
    question: 'Os materiais são atualizados com frequência?',
    answer:
      'Sim, nossos materiais são constantemente atualizados para seguir as diretrizes educacionais mais recentes e incorporar novas técnicas pedagógicas.',
  },
  {
    question: 'Posso solicitar um material personalizado?',
    answer:
      "Sim, oferecemos serviços de personalização de materiais para escolas e instituições. Entre em contato pelo formulário selecionando a categoria 'Parcerias'.",
  },
];
---

<BaseLayout title={title} enableViewTransitions={true} transitionType="fade">
  <Container>
    <PageTransition type="fade" duration="0.5s">
      <Section>
        <Breadcrumbs items={breadcrumbItems} class="mb-6" />
        
        <h1 class="text-4xl font-bold mb-8 text-center">{title}</h1>
        
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-12">
          <div class="lg:col-span-2">
            <div class="bg-base-100 p-6 rounded-lg shadow-md">
              <h2 class="text-2xl font-bold mb-6">Envie sua mensagem</h2>
              
              <p class="mb-6">
                Preencha o formulário abaixo para entrar em contato conosco. 
                Responderemos o mais breve possível.
              </p>
              
              <ContactForm 
                action="/api/contact"
                showPhone={true}
                showCategory={true}
                showAttachments={true}
                useRecaptcha={true}
              />
            </div>
          </div>
          
          <div>
            <div class="bg-base-100 p-6 rounded-lg shadow-md mb-8">
              <h2 class="text-2xl font-bold mb-6">Informações de Contato</h2>
              
              <div class="space-y-4">
                <div class="flex items-start">
                  <div class="text-primary mr-3 mt-1">
                    <i class="icon icon-map-pin"></i>
                  </div>
                  <div>
                    <h3 class="font-bold">Endereço</h3>
                    <p>{contactInfo.address}</p>
                  </div>
                </div>
                
                <div class="flex items-start">
                  <div class="text-primary mr-3 mt-1">
                    <i class="icon icon-phone"></i>
                  </div>
                  <div>
                    <h3 class="font-bold">Telefone</h3>
                    <p>{contactInfo.phone}</p>
                  </div>
                </div>
                
                <div class="flex items-start">
                  <div class="text-primary mr-3 mt-1">
                    <i class="icon icon-mail"></i>
                  </div>
                  <div>
                    <h3 class="font-bold">E-mail</h3>
                    <p>{contactInfo.email}</p>
                  </div>
                </div>
                
                <div class="flex items-start">
                  <div class="text-primary mr-3 mt-1">
                    <i class="icon icon-clock"></i>
                  </div>
                  <div>
                    <h3 class="font-bold">Horário de Atendimento</h3>
                    <p>{contactInfo.hours}</p>
                  </div>
                </div>
              </div>
            </div>
            
            <div class="bg-base-100 p-6 rounded-lg shadow-md">
              <h2 class="text-2xl font-bold mb-6">Redes Sociais</h2>
              
              <div class="flex justify-center space-x-4">
                <a href="https://facebook.com/estacaodaalfabetizacao" target="_blank" rel="noopener noreferrer" class="btn btn-circle btn-outline">
                  <i class="icon icon-facebook"></i>
                </a>
                
                <a href="https://instagram.com/estacaodaalfabetizacao" target="_blank" rel="noopener noreferrer" class="btn btn-circle btn-outline">
                  <i class="icon icon-instagram"></i>
                </a>
                
                <a href="https://twitter.com/estacaoalfabet" target="_blank" rel="noopener noreferrer" class="btn btn-circle btn-outline">
                  <i class="icon icon-twitter"></i>
                </a>
                
                <a href="https://youtube.com/estacaodaalfabetizacao" target="_blank" rel="noopener noreferrer" class="btn btn-circle btn-outline">
                  <i class="icon icon-youtube"></i>
                </a>
              </div>
            </div>
          </div>
        </div>
        
        <div class="bg-base-100 p-6 rounded-lg shadow-md mb-12">
          <h2 class="text-2xl font-bold mb-6">Perguntas Frequentes</h2>
          
          <div class="space-y-4">
            {faqs.map((faq, index) => (
              <div class="collapse collapse-plus bg-base-200">
                <input type="radio" name="faq-accordion" checked={index === 0} />
                <div class="collapse-title font-medium">
                  {faq.question}
                </div>
                <div class="collapse-content">
                  <p>{faq.answer}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
        
        <div class="bg-base-100 p-6 rounded-lg shadow-md">
          <h2 class="text-2xl font-bold mb-6">Nossa Localização</h2>
          
          <div class="aspect-video w-full">
            <iframe 
              src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3657.1975588201223!2d-46.65429492392006!3d-23.56518066167942!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x94ce59c8da0aa315%3A0xd59f9431f2c9776a!2sAv.%20Paulista%2C%20S%C3%A3o%20Paulo%20-%20SP!5e0!3m2!1spt-BR!2sbr!4v1684597848020!5m2!1spt-BR!2sbr" 
              width="100%" 
              height="100%" 
              style="border:0;" 
              allowfullscreen="" 
              loading="lazy" 
              referrerpolicy="no-referrer-when-downgrade"
              title="Mapa com a localização da Estação da Alfabetização"
            ></iframe>
          </div>
        </div>
      </Section>
    </PageTransition>
  </Container>
</BaseLayout>

<style>
  /* Estilos específicos para a página de contato */
  .icon {
    font-size: 1.25rem;
  }
  
  /* Melhorar acessibilidade de foco */
  a:focus, button:focus, input:focus, textarea:focus, select:focus, [tabindex]:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
  }
  
  /* Animação suave para o accordion */
  .collapse-content {
    transition: all 0.3s ease-in-out;
  }
  
  /* Melhorar contraste para acessibilidade */
  .collapse-title {
    font-weight: 600;
  }
</style>
