import type { QueryResult } from 'pg';
import { pg<PERSON><PERSON><PERSON> } from './pgHelper';

async function create(name: string, ulid_parent?: string): Promise<QueryResult> {
  // TODO: Validate if the category already exists
  const result = await read(name);

  if (result.rows.length > 0) {
    return result;
  }

  return await pgHelper.query(
    `INSERT INTO tab_category (
       ulid_category, 
       ${ulid_parent !== undefined ? 'ulid_parent,' : ''}
       name) 
     VALUES (
       $1, 
       ${ulid_parent !== undefined ? '$2,' : ''}
       $3) 
     RETURNING *`,
    [pgHelper.generateULID(), ulid_parent, name]
  );
}

async function read(ulid_category?: string, name?: string, active = true): Promise<QueryResult> {
  return await pgHelper.query(
    `SELECT a.*, 
            b.name AS owner_name 
       FROM tab_category a 
  LEFT JOIN tab_category b 
         ON a.ulid_parent = b.ulid_category 
      WHERE a.active = $1 
        ${ulid_category ? 'AND a.ulid_category = $2' : ''}
        ${name ? 'AND a.name ILIKE "%$3%" ' : ''} 
   ORDER BY a.name ASC`,
    [active, ulid_category, name]
  );
}

async function update(
  ulid_category: string,
  active: boolean,
  name: string,
  ulid_parent?: string
): Promise<QueryResult> {
  const params = [active, name, ulid_category];

  let query = `
    UPDATE tab_category 
       SET active     = $1, 
           name       = $2, 
           updated_at = CURRENT_TIMESTAMP`;

  if (ulid_parent !== undefined) {
    query += ', ulid_parent = $4';
    params.push(ulid_parent);
  }

  query += ' WHERE ulid_category = $3 RETURNING *';

  return await pgHelper.query(query, params);
}

async function deleteByUlid(ulid_category: string): Promise<QueryResult> {
  return await pgHelper.query(
    `DELETE FROM tab_category 
      WHERE ulid_category = $1 
     RETURNING *`,
    [ulid_category]
  );
}

async function inactivate(ulid_category: string): Promise<QueryResult> {
  return await pgHelper.query(
    `UPDATE tab_category
        SET active = false
      WHERE ulid_category = $1
     RETURNING *`,
    [ulid_category]
  );
}

export const categoryRepository = {
  create,
  read,
  update,
  deleteByUlid,
  inactivate,
};
