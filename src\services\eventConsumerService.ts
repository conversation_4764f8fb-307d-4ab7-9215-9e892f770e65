/**
 * Serviço de consumo de eventos do Kafka
 *
 * Este serviço fornece uma interface unificada para consumo de eventos
 * de diferentes domínios de negócio.
 */

import { consumer } from '@config/kafka';
import { logger } from '@utils/logger';
import { v4 as uuidv4 } from 'uuid';
import type { BaseEvent } from './eventProducerService';
import { type ProcessingContext, messageProcessingService } from './messageProcessingService';
import { messageTransformationService } from './messageTransformationService';
import {
  type CommitStrategy,
  type OffsetManagementConfig,
  offsetManagementService,
} from './offsetManagementService';
import { schemaValidationService } from './schemaValidationService';

/**
 * Interface para handler de eventos
 */
export interface EventHandler<T extends BaseEvent = BaseEvent> {
  /**
   * Processa um evento
   * @param event - Evento a ser processado
   * @param context - Contexto de processamento
   */
  handle(event: T, context: ProcessingContext): Promise<void>;
}

/**
 * Interface para configuração de consumidor
 */
export interface ConsumerConfig {
  /**
   * Tópicos a serem consumidos
   */
  topics: string[];

  /**
   * ID do grupo de consumidores
   */
  groupId?: string;

  /**
   * Se deve consumir mensagens desde o início
   */
  fromBeginning?: boolean;

  /**
   * Número máximo de mensagens a serem processadas em paralelo
   */
  maxParallelMessages?: number;

  /**
   * Tempo máximo de espera para commit de offsets (ms)
   */
  autoCommitInterval?: number;

  /**
   * Se deve habilitar retry automático
   */
  enableAutoRetry?: boolean;

  /**
   * Número máximo de tentativas de retry
   */
  maxRetries?: number;

  /**
   * Tempo de espera entre retries (ms)
   */
  retryDelay?: number;

  /**
   * Configuração de gerenciamento de offsets
   */
  offsetManagement?: Partial<OffsetManagementConfig>;

  /**
   * Estratégia de commit de offsets
   */
  commitStrategy?: CommitStrategy;
}

/**
 * Serviço de consumo de eventos
 */
export const eventConsumerService = {
  /**
   * Handlers registrados por tópico
   */
  handlers: new Map<string, EventHandler[]>(),

  /**
   * Indica se o consumidor está em execução
   */
  isRunning: false,

  /**
   * Configuração padrão do consumidor
   */
  defaultConfig: {
    groupId: 'estacao-alfabetizacao-group',
    fromBeginning: false,
    maxParallelMessages: 10,
    autoCommitInterval: 5000,
    enableAutoRetry: true,
    maxRetries: 3,
    retryDelay: 1000,
    commitStrategy: CommitStrategy.MANUAL_SUCCESS,
    offsetManagement: {
      persistOffsets: true,
      monitorLag: true,
    },
  } as ConsumerConfig,

  /**
   * Inicializa o consumidor de eventos
   * @param config - Configuração do consumidor
   */
  async init(config: Partial<ConsumerConfig> = {}): Promise<void> {
    try {
      if (this.isRunning) {
        logger.warn('Consumidor de eventos já está em execução');
        return;
      }

      // Mesclar configuração padrão com configuração fornecida
      const mergedConfig = {
        ...this.defaultConfig,
        ...config,
      };

      // Verificar se há tópicos para consumir
      if (!mergedConfig.topics || mergedConfig.topics.length === 0) {
        throw new Error('Nenhum tópico especificado para consumo');
      }

      // Conectar ao Kafka
      if (!consumer.isConnected()) {
        await consumer.connect();
        logger.info('Consumidor de eventos Kafka conectado');
      }

      // Inscrever-se nos tópicos
      for (const topic of mergedConfig.topics) {
        await consumer.subscribe({
          topic,
          fromBeginning: mergedConfig.fromBeginning,
        });
        logger.info(`Inscrito no tópico ${topic}`);
      }

      // Configurar gerenciamento de offsets
      const offsetConfig: OffsetManagementConfig = {
        commitStrategy: mergedConfig.commitStrategy || CommitStrategy.MANUAL_SUCCESS,
        ...(mergedConfig.offsetManagement || {}),
      };

      // Inicializar serviço de gerenciamento de offsets
      await offsetManagementService.init(
        consumer,
        mergedConfig.groupId || this.defaultConfig.groupId,
        offsetConfig
      );

      // Iniciar consumo de mensagens
      await consumer.run({
        partitionsConsumedConcurrently: mergedConfig.maxParallelMessages,
        // Desativar autocommit se estiver usando gerenciamento manual de offsets
        autoCommit: offsetConfig.commitStrategy === CommitStrategy.AUTO,
        autoCommitInterval: mergedConfig.autoCommitInterval,
        eachMessage: async (payload) => {
          const { topic, partition, message } = payload;

          try {
            // Verificar se há handlers registrados para o tópico
            const handlers = this.handlers.get(topic) || [];

            if (handlers.length === 0) {
              logger.warn(`Nenhum handler registrado para o tópico ${topic}`);

              // Commit de offset mesmo sem handlers para evitar reprocessamento
              if (offsetConfig.commitStrategy !== CommitStrategy.AUTO) {
                await consumer.commitOffsets([
                  {
                    topic,
                    partition,
                    offset: (BigInt(message.offset) + BigInt(1)).toString(),
                  },
                ]);
              }

              return;
            }

            // Processar mensagem
            if (message.value) {
              const key = message.key?.toString();
              const value = JSON.parse(message.value.toString());
              const offset = message.offset;

              logger.debug(`Mensagem recebida do tópico ${topic}`, {
                partition,
                offset,
                key,
              });

              // Extrair cabeçalhos da mensagem
              const headers: Record<string, string> = {};
              if (message.headers) {
                for (const [headerKey, headerValue] of Object.entries(message.headers)) {
                  if (headerValue !== null) {
                    headers[headerKey] = headerValue.toString();
                  }
                }
              }

              // Criar contexto de processamento
              const messageId = headers['message-id'] || uuidv4();
              const context = messageProcessingService.createContext(
                topic,
                partition,
                offset,
                key,
                {
                  ...headers,
                  'message-id': messageId,
                  timestamp: headers.timestamp || Date.now().toString(),
                }
              );

              // Usar serviço de gerenciamento de offsets para processar mensagem
              await offsetManagementService.processMessage(
                payload,
                consumer,
                mergedConfig.groupId || this.defaultConfig.groupId,
                offsetConfig,
                async (message, topic, partition) => {
                  let success = true;

                  // Executar todos os handlers registrados para o tópico
                  for (const handler of handlers) {
                    try {
                      // Obter informações de esquema e transformação
                      const topicParts = topic.split('.');
                      const schemaName = topicParts.slice(0, 2).join('.');
                      const transformerName = headers.transformer || `${schemaName}.legacy_to_new`;

                      // Obter validador de esquema
                      const validator = await schemaValidationService.getValidator(schemaName);

                      // Obter transformador
                      const transformer =
                        await messageTransformationService.getTransformer(transformerName);

                      // Processar mensagem com validação e transformação
                      await messageProcessingService.processMessage(
                        value,
                        async (processedMessage, processingContext) => {
                          await handler.handle(processedMessage, processingContext);
                        },
                        context,
                        {
                          schemaValidator: validator || undefined,
                          transformer: transformer || undefined,
                          domain: topicParts[0],
                          entity: topicParts[1],
                          eventType: topicParts[2],
                        }
                      );
                    } catch (handlerError) {
                      logger.error(
                        `Erro ao processar mensagem com handler para tópico ${topic}:`,
                        handlerError
                      );

                      success = false;

                      // Implementar retry se habilitado
                      if (mergedConfig.enableAutoRetry) {
                        try {
                          await this.retryHandler(
                            handler,
                            value,
                            context,
                            mergedConfig.maxRetries || 3,
                            mergedConfig.retryDelay || 1000
                          );

                          // Se chegou aqui, o retry foi bem-sucedido
                          success = true;
                        } catch (retryError) {
                          logger.error(`Falha no retry para tópico ${topic}:`, retryError);
                          success = false;
                        }
                      }
                    }
                  }

                  return success;
                }
              );
            }
          } catch (error) {
            logger.error(`Erro ao processar mensagem do tópico ${topic}:`, error);
          }
        },
      });

      this.isRunning = true;
      logger.info('Consumidor de eventos Kafka iniciado');
    } catch (error) {
      logger.error('Erro ao inicializar consumidor de eventos Kafka:', error);
      throw error;
    }
  },

  /**
   * Finaliza o consumidor de eventos
   */
  async shutdown(): Promise<void> {
    try {
      if (!this.isRunning) {
        return;
      }

      await consumer.disconnect();
      this.isRunning = false;
      logger.info('Consumidor de eventos Kafka desconectado');
    } catch (error) {
      logger.error('Erro ao finalizar consumidor de eventos Kafka:', error);
    }
  },

  /**
   * Registra um handler para um tópico
   * @param topic - Tópico a ser consumido
   * @param handler - Handler para processar eventos
   */
  registerHandler(topic: string, handler: EventHandler): void {
    // Obter handlers existentes para o tópico
    const handlers = this.handlers.get(topic) || [];

    // Adicionar novo handler
    handlers.push(handler);

    // Atualizar mapa de handlers
    this.handlers.set(topic, handlers);

    logger.info(`Handler registrado para tópico ${topic}`);
  },

  /**
   * Registra um handler para um domínio específico
   * @param domain - Domínio do evento
   * @param entity - Entidade do evento
   * @param event - Tipo do evento
   * @param handler - Handler para processar eventos
   */
  registerDomainHandler(
    domain: string,
    entity: string,
    event: string,
    handler: EventHandler
  ): void {
    const topic = `${domain}.${entity}.${event}`;
    this.registerHandler(topic, handler);
  },

  /**
   * Tenta executar um handler novamente após falha
   * @param handler - Handler a ser executado
   * @param event - Evento a ser processado
   * @param context - Contexto de processamento
   * @param maxRetries - Número máximo de tentativas
   * @param delay - Tempo de espera entre tentativas (ms)
   */
  async retryHandler(
    handler: EventHandler,
    event: BaseEvent,
    context: ProcessingContext,
    maxRetries: number,
    delay: number
  ): Promise<void> {
    let retries = 0;

    while (retries < maxRetries) {
      retries++;

      try {
        // Aguardar antes de tentar novamente
        await new Promise((resolve) => setTimeout(resolve, delay));

        logger.info(
          `Tentativa ${retries}/${maxRetries} de processar mensagem do tópico ${context.topic}`
        );

        // Incrementar contador de tentativas no contexto
        context.attempt = retries + 1;

        // Tentar processar novamente
        await handler.handle(event, context);

        // Se chegou aqui, o processamento foi bem-sucedido
        logger.info(
          `Mensagem do tópico ${context.topic} processada com sucesso após ${retries} tentativa(s)`
        );
        return;
      } catch (error) {
        logger.error(
          `Erro na tentativa ${retries}/${maxRetries} de processar mensagem do tópico ${context.topic}:`,
          error
        );

        // Se for a última tentativa, desistir
        if (retries >= maxRetries) {
          logger.error(
            `Desistindo após ${maxRetries} tentativas de processar mensagem do tópico ${context.topic}`
          );
        }
      }
    }
  },
};
