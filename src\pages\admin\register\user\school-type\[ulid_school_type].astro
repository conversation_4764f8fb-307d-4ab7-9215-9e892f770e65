---
import { actions } from 'astro:actions';
import ControlButtons from '@components/form/ControlButtons.astro';
import FormBase from '@components/form/FormBase.astro';
import InputHidden from '@components/form/InputHidden.astro';
import InputText from '@components/form/InputText.astro';
import BaseLayout from '@layouts/BaseLayout.astro';
import type { SchoolTypeData } from 'src/database/interfacesHelper';

// Define interfaces for type safety
interface ActionResult {
  data?: SchoolTypeData | SchoolTypeData[];
  error?: string;
}

// Obter parâmetro da URL
const { ulid_school_type = '' } = Astro.params;

// Busca dados da escola
let data: SchoolTypeData = {};
try {
  const result = (await actions.schoolTypeAction.read({
    filter: 'ulid_school_type',
    ulid_school_type,
  })) as ActionResult;
  data = (result.data as SchoolTypeData) || {};
} catch (error) {
  console.error('Erro ao carregar escola:', error);
  return Astro.redirect('/admin/register/user/school-type?error=load');
}

const formValidation = `
  const nameInput = form.querySelector('input[name="name"]');
  if (!nameInput || !nameInput.value.trim()) {
    alert('O nome da escola é obrigatório');
    return false;
  }
  return true;
`;
---
<BaseLayout title="Formulário de Tipo de Escola">
  <div class="container mx-auto p-4">
    <h1 class="text-2xl font-bold mb-4">
      {ulid_school_type ? "Editar" : "Novo"} Tipo de Escola
    </h1>

    <FormBase
      action={actions.schoolTypeAction.update}
      formType="schoolType"
      onSubmitValidation={formValidation}
    >
      <!-- Campos ocultos -->
      <InputHidden ulid={data.ulid_school_type ?? ""} field="ulid_school_type" />

      <!-- Campo nome -->
      <InputText 
        label="Tipo" 
        name="type"
        value={data.type ?? ""}
        required={true}
      />

      <!-- Botões de controle -->
      <ControlButtons 
        saveLabel="Salvar"
        cancelHref="/admin/register/user/school-type"
      />
    </FormBase>
  </div>
</BaseLayout>