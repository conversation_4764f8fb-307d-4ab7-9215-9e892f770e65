/**
 * Camada de compatibilidade para o repositório de pagamentos
 * 
 * Este arquivo mantém a compatibilidade com o código existente que
 * utiliza a estrutura antiga do repositório de pagamentos.
 * 
 * @deprecated Use a nova estrutura de repositórios em src/repositories
 */

import type { QueryResult } from "pg";
import { pgHelper } from "../../repository/pgHelper";
import { repositories } from "../index";

/**
 * Cria um novo pagamento
 * 
 * @param ulid_order ID do pedido
 * @param ulid_payment_type ID do tipo de pagamento
 * @param status Status do pagamento
 * @param value Valor do pagamento
 * @returns Resultado da consulta
 */
async function create(
  ulid_order: string,
  ulid_payment_type: string,
  status: number,
  value: number
): Promise<QueryResult> {
  try {
    // Usar a nova implementação
    const payment = await repositories.paymentRepository.create({
      orderId: ulid_order,
      paymentTypeId: ulid_payment_type,
      status,
      value
    });
    
    // Converter para o formato esperado pelo código antigo
    return {
      rows: [payment],
      rowCount: 1,
      command: 'INSERT',
      oid: 0,
      fields: []
    };
  } catch (error) {
    console.error('Error in compatibility layer (paymentRepository.create):', error);
    
    // Fallback para a implementação antiga em caso de erro
    // Corrigindo o SQL que tinha um erro na query original (aspas simples nos placeholders)
    return pgHelper.query(
      `INSERT INTO tab_payment (
         ulid_order, 
         ulid_payment_type, 
         status, 
         value) 
       VALUES ($1, $2, $3, $4)
       RETURNING *`,
      [ulid_order, ulid_payment_type, status, value]
    );
  }
}

/**
 * Busca pagamentos
 * 
 * @param ulid_payment ID do pagamento (opcional)
 * @returns Resultado da consulta
 */
async function read(
  ulid_payment?: string
):Promise<QueryResult> {
  try {
    // Usar a nova implementação
    if (ulid_payment) {
      const payment = await repositories.paymentRepository.findById(ulid_payment);
      
      // Converter para o formato esperado pelo código antigo
      return {
        rows: payment ? [payment] : [],
        rowCount: payment ? 1 : 0,
        command: 'SELECT',
        oid: 0,
        fields: []
      };
    } else {
      const payments = await repositories.paymentRepository.findAll();
      
      // Converter para o formato esperado pelo código antigo
      return {
        rows: payments,
        rowCount: payments.length,
        command: 'SELECT',
        oid: 0,
        fields: []
      };
    }
  } catch (error) {
    console.error('Error in compatibility layer (paymentRepository.read):', error);
    
    // Fallback para a implementação antiga em caso de erro
    return pgHelper.query(
      `SELECT * 
         FROM tab_payment
         ${ulid_payment ? ` WHERE ulid_payment = $1` : ``}`,
      [ulid_payment]
    );
  }
}

/**
 * Atualiza um pagamento
 * 
 * @param ulid_payment ID do pagamento
 * @param status Status do pagamento
 * @returns Resultado da consulta
 */
async function update(
  ulid_payment: string,
  status: number,
): Promise<QueryResult> {
  try {
    // Usar a nova implementação
    const payment = await repositories.paymentRepository.update(ulid_payment, {
      status
    });
    
    // Converter para o formato esperado pelo código antigo
    return {
      rows: [payment],
      rowCount: 1,
      command: 'UPDATE',
      oid: 0,
      fields: []
    };
  } catch (error) {
    console.error('Error in compatibility layer (paymentRepository.update):', error);
    
    // Fallback para a implementação antiga em caso de erro
    // Corrigindo o SQL que tinha um erro na query original (aspas simples nos placeholders e vírgula no WHERE)
    return pgHelper.query(
      `UPDATE tab_payment 
          SET status      = $2,
              updated_at  = NOW()
        WHERE ulid_payment = $1
       RETURNING *`,
      [ulid_payment, status]
    );
  }
}

/**
 * Remove um pagamento
 * 
 * @param ulid_payment ID do pagamento
 * @returns Resultado da consulta
 */
async function deleteByUlid(
  ulid_payment: string
): Promise<QueryResult> {
  try {
    // Usar a nova implementação
    const success = await repositories.paymentRepository.delete(ulid_payment);
    
    // Converter para o formato esperado pelo código antigo
    return {
      rows: success ? [{ ulid_payment }] : [],
      rowCount: success ? 1 : 0,
      command: 'DELETE',
      oid: 0,
      fields: []
    };
  } catch (error) {
    console.error('Error in compatibility layer (paymentRepository.deleteByUlid):', error);
    
    // Fallback para a implementação antiga em caso de erro
    return pgHelper.query(
      `DELETE FROM tab_payment 
        WHERE ulid_payment = $1 
       RETURNING *`,
      [ulid_payment],
    );
  }
}

export const paymentRepository = {
  create,
  read,
  update,
  deleteByUlid
};
