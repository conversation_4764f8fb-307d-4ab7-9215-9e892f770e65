---
import ThemeSwitcher from '../../components/ui/ThemeSwitcher.astro';
import BaseLayout from '../../layouts/BaseLayout.astro';

import Breadcrumbs from '../../components/ui/navigation/Breadcrumbs.astro';
// Componentes de navegação
import Navbar from '../../components/ui/navigation/Navbar.astro';
import Pagination from '../../components/ui/navigation/Pagination.astro';
import Tabs from '../../components/ui/navigation/Tabs.astro';

// Componentes de layout
import Container from '../../components/ui/layout/Container.astro';
import Divider from '../../components/ui/layout/Divider.astro';
import Grid from '../../components/ui/layout/Grid.astro';
import Section from '../../components/ui/layout/Section.astro';

// Componentes de feedback
import Alert from '../../components/ui/feedback/Alert.astro';
import Progress from '../../components/ui/feedback/Progress.astro';
import Skeleton from '../../components/ui/feedback/Skeleton.astro';
import Toast from '../../components/ui/feedback/Toast.astro';

import Avatar from '../../components/ui/data/Avatar.astro';
import Badge from '../../components/ui/data/Badge.astro';
import Stat from '../../components/ui/data/Stat.astro';
// Componentes de dados
import Table from '../../components/ui/data/Table.astro';

// Outros componentes
import DaisyButton from '../../components/ui/DaisyButton.astro';
import DaisyCard from '../../components/ui/DaisyCard.astro';

const title = 'Biblioteca de Componentes';

// Dados para exemplos
const navLinks = [
  { href: '#', label: 'Início', isActive: true },
  { href: '#', label: 'Componentes' },
  { href: '#', label: 'Documentação' },
  { href: '#', label: 'Sobre' },
];

const breadcrumbItems = [
  { href: '/', label: 'Início' },
  { href: '/examples', label: 'Exemplos' },
  { label: 'Componentes' },
];

const tabs = [
  { id: 'navegacao', label: 'Navegação', isActive: true },
  { id: 'layout', label: 'Layout' },
  { id: 'feedback', label: 'Feedback' },
  { id: 'dados', label: 'Dados' },
];

const tableColumns = [
  { key: 'id', header: 'ID', width: '10%' },
  { key: 'nome', header: 'Nome', width: '30%' },
  { key: 'categoria', header: 'Categoria', width: '20%' },
  {
    key: 'status',
    header: 'Status',
    width: '20%',
    format: (value) => {
      const statusMap = {
        ativo: '<span class="badge badge-success">Ativo</span>',
        inativo: '<span class="badge badge-error">Inativo</span>',
        pendente: '<span class="badge badge-warning">Pendente</span>',
      };
      return statusMap[value] || value;
    },
  },
  { key: 'acoes', header: 'Ações', width: '20%', align: 'center' },
];

const tableData = [
  {
    id: 1,
    nome: 'Componente A',
    categoria: 'Navegação',
    status: 'ativo',
    acoes: '<button class="btn btn-xs">Editar</button>',
  },
  {
    id: 2,
    nome: 'Componente B',
    categoria: 'Layout',
    status: 'inativo',
    acoes: '<button class="btn btn-xs">Editar</button>',
  },
  {
    id: 3,
    nome: 'Componente C',
    categoria: 'Feedback',
    status: 'pendente',
    acoes: '<button class="btn btn-xs">Editar</button>',
  },
];
---

<BaseLayout title={title}>
  <div class="mb-4">
    <Navbar 
      links={navLinks}
      actions={[<ThemeSwitcher position="dropdown" />]}
      sticky
      bordered
    />
  </div>
  
  <Container>
    <Breadcrumbs items={breadcrumbItems} class="mb-4" />
    
    <Section title={title} subtitle="Biblioteca de componentes reutilizáveis para o projeto Estação da Alfabetização">
      <Tabs tabs={tabs}>
        <div slot="navegacao">
          <Grid cols={{ sm: 1, md: 2 }} gap={6} class="mb-8">
            <DaisyCard title="Navbar">
              <p class="mb-4">Barra de navegação responsiva com suporte a logo, links e ações.</p>
              <div class="border rounded-box p-4 bg-base-200">
                <Navbar 
                  links={[
                    { href: "#", label: "Início", isActive: true },
                    { href: "#", label: "Sobre" },
                    { href: "#", label: "Contato" }
                  ]}
                  bordered
                  class="bg-base-100"
                />
              </div>
            </DaisyCard>
            
            <DaisyCard title="Breadcrumbs">
              <p class="mb-4">Navegação estrutural que mostra o caminho atual.</p>
              <div class="border rounded-box p-4 bg-base-200">
                <Breadcrumbs items={breadcrumbItems} />
              </div>
            </DaisyCard>
            
            <DaisyCard title="Tabs">
              <p class="mb-4">Abas para alternar entre diferentes seções de conteúdo.</p>
              <div class="border rounded-box p-4 bg-base-200">
                <Tabs 
                  tabs={[
                    { id: "tab1", label: "Aba 1", isActive: true },
                    { id: "tab2", label: "Aba 2" },
                    { id: "tab3", label: "Aba 3" }
                  ]}
                >
                  <div slot="tab1">Conteúdo da Aba 1</div>
                  <div slot="tab2">Conteúdo da Aba 2</div>
                  <div slot="tab3">Conteúdo da Aba 3</div>
                </Tabs>
              </div>
            </DaisyCard>
            
            <DaisyCard title="Pagination">
              <p class="mb-4">Controles de navegação para conteúdo paginado.</p>
              <div class="border rounded-box p-4 bg-base-200">
                <Pagination currentPage={3} totalPages={10} />
              </div>
            </DaisyCard>
          </Grid>
        </div>
        
        <div slot="layout">
          <Grid cols={{ sm: 1, md: 2 }} gap={6} class="mb-8">
            <DaisyCard title="Container">
              <p class="mb-4">Contêiner responsivo para limitar a largura do conteúdo.</p>
              <div class="border rounded-box p-4 bg-base-200">
                <div class="border border-primary p-4 text-center">
                  Container (largura limitada)
                </div>
              </div>
            </DaisyCard>
            
            <DaisyCard title="Grid">
              <p class="mb-4">Sistema de grid responsivo baseado em CSS Grid.</p>
              <div class="border rounded-box p-4 bg-base-200">
                <Grid cols={{ sm: 2, md: 3 }} gap={2}>
                  <div class="bg-primary text-primary-content p-2 text-center">1</div>
                  <div class="bg-primary text-primary-content p-2 text-center">2</div>
                  <div class="bg-primary text-primary-content p-2 text-center">3</div>
                  <div class="bg-primary text-primary-content p-2 text-center">4</div>
                  <div class="bg-primary text-primary-content p-2 text-center">5</div>
                  <div class="bg-primary text-primary-content p-2 text-center">6</div>
                </Grid>
              </div>
            </DaisyCard>
            
            <DaisyCard title="Section">
              <p class="mb-4">Seção de conteúdo com espaçamento e opções de estilo.</p>
              <div class="border rounded-box p-4 bg-base-200">
                <Section 
                  title="Título da Seção" 
                  subtitle="Subtítulo da seção com descrição"
                  background="primary"
                  container={false}
                  padded
                  spacing="sm"
                >
                  <p class="text-center">Conteúdo da seção</p>
                </Section>
              </div>
            </DaisyCard>
            
            <DaisyCard title="Divider">
              <p class="mb-4">Linha horizontal para separar conteúdo.</p>
              <div class="border rounded-box p-4 bg-base-200">
                <p>Conteúdo acima</p>
                <Divider />
                <p>Conteúdo abaixo</p>
                
                <Divider text="Com texto" class="my-4" />
                
                <div class="flex h-20">
                  <div>Esquerda</div>
                  <Divider vertical />
                  <div>Direita</div>
                </div>
              </div>
            </DaisyCard>
          </Grid>
        </div>
        
        <div slot="feedback">
          <Grid cols={{ sm: 1, md: 2 }} gap={6} class="mb-8">
            <DaisyCard title="Alert">
              <p class="mb-4">Exibe mensagens de feedback ao usuário.</p>
              <div class="border rounded-box p-4 bg-base-200 space-y-4">
                <Alert type="info">Esta é uma mensagem informativa.</Alert>
                <Alert type="success" title="Sucesso!">Operação realizada com sucesso.</Alert>
                <Alert type="warning">Atenção! Verifique os dados antes de continuar.</Alert>
                <Alert type="error" dismissible>Ocorreu um erro ao processar sua solicitação.</Alert>
              </div>
            </DaisyCard>
            
            <DaisyCard title="Toast">
              <p class="mb-4">Exibe mensagens temporárias de feedback.</p>
              <div class="border rounded-box p-4 bg-base-200 space-y-4">
                <div class="relative h-40 border border-dashed border-base-300 rounded-lg">
                  <Toast 
                    type="success" 
                    title="Sucesso!" 
                    message="Operação realizada com sucesso."
                    position="top-center"
                    autoClose={false}
                  />
                </div>
                <div class="flex gap-2">
                  <DaisyButton variant="info" size="sm">Mostrar Info</DaisyButton>
                  <DaisyButton variant="success" size="sm">Mostrar Sucesso</DaisyButton>
                  <DaisyButton variant="error" size="sm">Mostrar Erro</DaisyButton>
                </div>
              </div>
            </DaisyCard>
            
            <DaisyCard title="Progress">
              <p class="mb-4">Exibe uma barra de progresso.</p>
              <div class="border rounded-box p-4 bg-base-200 space-y-4">
                <Progress value={30} max={100} label="Progresso básico" showValue />
                <Progress value={60} max={100} color="primary" label="Progresso primário" showValue />
                <Progress value={45} max={100} color="secondary" label="Progresso secundário" showValue />
                <Progress indeterminate color="accent" label="Carregando..." />
              </div>
            </DaisyCard>
            
            <DaisyCard title="Skeleton">
              <p class="mb-4">Exibe um placeholder animado enquanto o conteúdo está carregando.</p>
              <div class="border rounded-box p-4 bg-base-200 space-y-4">
                <div class="space-y-2">
                  <Skeleton type="title" />
                  <Skeleton type="text" count={3} />
                </div>
                
                <div class="flex gap-4 items-center">
                  <Skeleton type="avatar" />
                  <div class="flex-1">
                    <Skeleton type="text" width="50%" />
                    <Skeleton type="text" width="70%" />
                  </div>
                </div>
                
                <div class="flex gap-2">
                  <Skeleton type="button" />
                  <Skeleton type="button" />
                </div>
              </div>
            </DaisyCard>
          </Grid>
        </div>
        
        <div slot="dados">
          <Grid cols={{ sm: 1, md: 2 }} gap={6} class="mb-8">
            <DaisyCard title="Table">
              <p class="mb-4">Exibe dados em formato tabular.</p>
              <div class="border rounded-box p-4 bg-base-200">
                <Table 
                  columns={tableColumns}
                  data={tableData}
                  zebra
                  bordered
                />
              </div>
            </DaisyCard>
            
            <DaisyCard title="Badge">
              <p class="mb-4">Exibe um pequeno indicador ou rótulo.</p>
              <div class="border rounded-box p-4 bg-base-200">
                <div class="flex flex-wrap gap-2 mb-4">
                  <Badge text="Padrão" />
                  <Badge text="Primário" color="primary" />
                  <Badge text="Secundário" color="secondary" />
                  <Badge text="Acento" color="accent" />
                </div>
                
                <div class="flex flex-wrap gap-2 mb-4">
                  <Badge text="Info" color="info" />
                  <Badge text="Sucesso" color="success" />
                  <Badge text="Alerta" color="warning" />
                  <Badge text="Erro" color="error" />
                </div>
                
                <div class="flex flex-wrap gap-2">
                  <Badge text="Outline" variant="outline" color="primary" />
                  <Badge text="Pequeno" size="sm" color="secondary" />
                  <Badge text="Grande" size="lg" color="accent" />
                </div>
              </div>
            </DaisyCard>
            
            <DaisyCard title="Stat">
              <p class="mb-4">Exibe um valor estatístico com título e descrição.</p>
              <div class="border rounded-box p-4 bg-base-200">
                <div class="stats shadow">
                  <Stat
                    title="Downloads"
                    value="31K"
                    desc="Jan 1 - Fev 1"
                    trend="up"
                    trendValue="4.5%"
                  />
                  
                  <Stat
                    title="Usuários"
                    value="4,200"
                    desc="↘︎ 1.2% (30 dias)"
                    trend="down"
                    trendValue="1.2%"
                  />
                  
                  <Stat
                    title="Novos registros"
                    value="1,200"
                    desc="↗︎ 90 (14%)"
                    trend="up"
                    trendValue="14%"
                  />
                </div>
              </div>
            </DaisyCard>
            
            <DaisyCard title="Avatar">
              <p class="mb-4">Exibe uma imagem de perfil ou iniciais.</p>
              <div class="border rounded-box p-4 bg-base-200">
                <div class="flex flex-wrap gap-4 mb-4">
                  <Avatar 
                    src="https://daisyui.com/images/stock/photo-1534528741775-53994a69daeb.jpg" 
                    alt="Usuário"
                    size="md"
                  />
                  
                  <Avatar 
                    initials="JD"
                    size="md"
                    status="online"
                  />
                  
                  <Avatar 
                    alt="Maria Silva"
                    size="md"
                    status="offline"
                  />
                </div>
                
                <div class="flex flex-wrap gap-4">
                  <Avatar size="xs" initials="XS" />
                  <Avatar size="sm" initials="SM" />
                  <Avatar size="md" initials="MD" />
                  <Avatar size="lg" initials="LG" />
                  <Avatar size="xl" initials="XL" />
                </div>
              </div>
            </DaisyCard>
          </Grid>
        </div>
      </Tabs>
    </Section>
    
    <Section title="Documentação" background="light">
      <p class="mb-4">
        Esta biblioteca de componentes foi desenvolvida para o projeto Estação da Alfabetização,
        utilizando Astro e DaisyUI como base. Os componentes são altamente customizáveis e
        seguem as melhores práticas de acessibilidade.
      </p>
      
      <p class="mb-4">
        Para utilizar os componentes, basta importá-los e utilizá-los em suas páginas Astro.
        Todos os componentes aceitam propriedades para personalização e possuem valores padrão
        sensatos.
      </p>
      
      <div class="flex justify-center mt-8">
        <Pagination currentPage={1} totalPages={1} />
      </div>
    </Section>
  </Container>
</BaseLayout>
