---
import FinancialNav from '@components/navigation/FinancialNav.astro';
import { checkAdmin } from '@helpers/authGuard';
import AdminLayout from '@layouts/AdminLayout.astro';
import { reconciliationService } from '@services/reconciliationService';

// Protege a rota verificando se o usuário é admin
const authRedirect = await checkAdmin(Astro);
if (authRedirect) return authRedirect;

// Obter histórico de reconciliações
const reconciliationHistory = await reconciliationService.getReconciliationHistory(10);

// Obter discrepâncias não resolvidas
const unresolvedDiscrepancies = await reconciliationService.getUnresolvedDiscrepancies(20);

// Formatar valores monetários
const formatCurrency = (value) => {
  return new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: 'BRL',
  }).format(value || 0);
};

// Formatar data
const formatDate = (dateString) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  return `${date.toLocaleDateString('pt-BR')} ${date.toLocaleTimeString('pt-BR')}`;
};

// Formatar tipo de discrepância
const formatDiscrepancyType = (type) => {
  const types = {
    status: 'Status',
    value: 'Valor',
    missing_system: 'Ausente no Sistema',
    missing_provider: 'Ausente no Provedor',
  };
  return types[type] || type;
};

// Verificar se há parâmetro de sucesso na URL
const success = Astro.url.searchParams.get('success');
---

<AdminLayout title="Reconciliação Financeira">
  <div class="container mx-auto p-4">
    <h1 class="text-2xl font-bold mb-4">Gestão Financeira</h1>
    
    <FinancialNav />
    
    <h2 class="text-xl font-bold mb-6">Reconciliação de Transações</h2>
    
    {success && (
      <div class="alert alert-success mb-4">
        <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
        <span>Operação realizada com sucesso!</span>
      </div>
    )}
    
    <!-- Ações de Reconciliação -->
    <div class="card bg-base-100 shadow-xl mb-6">
      <div class="card-body">
        <h2 class="card-title">Ações de Reconciliação</h2>
        <p class="text-sm mb-4">Execute ou agende reconciliações de transações financeiras.</p>
        
        <div class="flex flex-wrap gap-4">
          <form action="/api/reconciliation/run" method="POST">
            <button type="submit" class="btn btn-primary">
              Executar Reconciliação Manual
            </button>
          </form>
          
          <a href="/admin/financial/reconciliation/schedule" class="btn btn-outline">
            Configurar Agendamento
          </a>
          
          <a href="/admin/financial/reconciliation/settings" class="btn btn-outline">
            Configurações
          </a>
        </div>
      </div>
    </div>
    
    <!-- Histórico de Reconciliações -->
    <div class="card bg-base-100 shadow-xl mb-6">
      <div class="card-body">
        <h2 class="card-title">Histórico de Reconciliações</h2>
        <p class="text-sm mb-4">Últimas reconciliações executadas.</p>
        
        <div class="overflow-x-auto">
          <table class="table table-zebra w-full">
            <thead>
              <tr>
                <th>Data</th>
                <th>Total de Transações</th>
                <th>Correspondentes</th>
                <th>Discrepâncias</th>
                <th>Status</th>
                <th>Ações</th>
              </tr>
            </thead>
            <tbody>
              {reconciliationHistory.length === 0 ? (
                <tr>
                  <td colspan="6" class="text-center py-4">Nenhuma reconciliação encontrada</td>
                </tr>
              ) : (
                reconciliationHistory.map(record => (
                  <tr>
                    <td>{formatDate(record.reconciliation_date)}</td>
                    <td>{record.total_transactions}</td>
                    <td>{record.matched_transactions}</td>
                    <td>
                      <div class={`badge ${record.discrepancy_count > 0 ? 'badge-warning' : 'badge-success'}`}>
                        {record.discrepancy_count}
                      </div>
                    </td>
                    <td>
                      <div class={`badge ${record.success ? 'badge-success' : 'badge-error'}`}>
                        {record.success ? 'Sucesso' : 'Falha'}
                      </div>
                    </td>
                    <td>
                      <a href={`/admin/financial/reconciliation/details/${record.ulid_reconciliation_log}`} class="btn btn-xs btn-outline">
                        Detalhes
                      </a>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
        
        <div class="card-actions justify-end mt-4">
          <a href="/admin/financial/reconciliation/history" class="btn btn-outline btn-sm">
            Ver Histórico Completo
          </a>
        </div>
      </div>
    </div>
    
    <!-- Discrepâncias Não Resolvidas -->
    <div class="card bg-base-100 shadow-xl mb-6">
      <div class="card-body">
        <h2 class="card-title">Discrepâncias Não Resolvidas</h2>
        <p class="text-sm mb-4">Discrepâncias que requerem atenção.</p>
        
        <div class="overflow-x-auto">
          <table class="table table-zebra w-full">
            <thead>
              <tr>
                <th>Data</th>
                <th>Tipo</th>
                <th>ID Externo</th>
                <th>Status Sistema</th>
                <th>Status Provedor</th>
                <th>Valor Sistema</th>
                <th>Valor Provedor</th>
                <th>Ações</th>
              </tr>
            </thead>
            <tbody>
              {unresolvedDiscrepancies.length === 0 ? (
                <tr>
                  <td colspan="8" class="text-center py-4">Nenhuma discrepância não resolvida</td>
                </tr>
              ) : (
                unresolvedDiscrepancies.map(discrepancy => (
                  <tr>
                    <td>{formatDate(discrepancy.created_at)}</td>
                    <td>
                      <div class="badge badge-outline">
                        {formatDiscrepancyType(discrepancy.discrepancy_type)}
                      </div>
                    </td>
                    <td>{discrepancy.external_id}</td>
                    <td>{discrepancy.system_status}</td>
                    <td>{discrepancy.provider_status}</td>
                    <td>{formatCurrency(discrepancy.system_value)}</td>
                    <td>{formatCurrency(discrepancy.provider_value)}</td>
                    <td>
                      <div class="flex gap-2">
                        <a href={`/admin/financial/reconciliation/resolve/${discrepancy.ulid_discrepancy}`} class="btn btn-xs btn-primary">
                          Resolver
                        </a>
                        <a href={`/admin/financial/reconciliation/details/${discrepancy.ulid_discrepancy}`} class="btn btn-xs btn-outline">
                          Detalhes
                        </a>
                      </div>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
        
        <div class="card-actions justify-end mt-4">
          <a href="/admin/financial/reconciliation/discrepancies" class="btn btn-outline btn-sm">
            Ver Todas as Discrepâncias
          </a>
        </div>
      </div>
    </div>
  </div>
</AdminLayout>

<script>
  // Inicializar componentes interativos
  document.addEventListener('DOMContentLoaded', () => {
    // Código de inicialização, se necessário
  });
</script>
