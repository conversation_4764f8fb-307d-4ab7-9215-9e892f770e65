import type { QueryResult } from 'pg';
import { pg<PERSON><PERSON><PERSON> } from './pgHelper';

async function create(type: string): Promise<QueryResult> {
  const result = await read(type);

  if (result.rows.length > 0) {
    return result;
  }

  return await pgHelper.query(
    `INSERT INTO tab_user_type (
       ulid_user_type, 
       type) 
     VALUES ($1, $2) 
     RETURNING *`,
    [pgHelper.generateULID(), type]
  );
}

async function read(ulid_user_type?: string, type?: string, active = true): Promise<QueryResult> {
  return await pgHelper.query(
    `SELECT * 
       FROM tab_user_type 
      WHERE active = $1 
     ${ulid_user_type ? 'AND ulid_user_type = $2' : ''}
     ${type ? 'AND type ILIKE "%$2%" ' : ''}
     ORDER BY type ASC`,
    [active, ulid_user_type, type]
  );
}

async function update(ulid_user_type: string, active: boolean, type: string): Promise<QueryResult> {
  return await pgHelper.query(
    `UPDATE tab_user_type 
        SET active     = $2,
            type       = $3, 
            updated_at = CURRENT_TIMESTAMP 
      WHERE ulid_user_type = $1 
     RETURNING *`,
    [ulid_user_type, active, type]
  );
}

async function deleteByUlid(ulid_user_type: string): Promise<QueryResult> {
  return await pgHelper.query(
    `DELETE FROM tab_user_type 
        WHERE ulid_user_type = $1 
       RETURNING *`,
    [ulid_user_type]
  );
}

async function inactivate(ulid_user_type: string): Promise<QueryResult> {
  return await pgHelper.query(
    `UPDATE tab_user_type 
        SET active = false, 
            updated_at = CURRENT_TIMESTAMP 
      WHERE ulid_user_type = $1 
     RETURNING *`,
    [ulid_user_type]
  );
}

export const userTypeRepository = {
  create,
  read,
  update,
  deleteByUlid,
  inactivate,
};
