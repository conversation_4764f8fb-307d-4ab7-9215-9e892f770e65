---
import { actions } from 'astro:actions';
import ControlButtons from '@components/form/ControlButtons.astro';
import FormBase from '@components/form/FormBase.astro';
import InputHidden from '@components/form/InputHidden.astro';
import InputText from '@components/form/InputText.astro';
import BaseLayout from '@layouts/BaseLayout.astro';
import type { SchoolTypeData } from 'src/database/interfacesHelper';

const formValidation = `
  const typeInput = form.querySelector('input[name="type"]');
  if (!typeInput || !typeInput.value.trim()) {
      alert('O tipo de escola é obrigatório');
      return false;
  }
  return true;
`;
---
<BaseLayout title="Formulário de Tipo de Escola">
  <div class="container mx-auto p-4">Novo Tipo de Escola</div>

  <FormBase
    action={actions.schoolTypeAction.create}
    formType="schoolType"
    onSubmitValidation={formValidation}
  >
    <InputHidden field="ulid_school_type" id="ulid_school_type" />
    <InputText 
      label="Tipo" 
      name="type" 
      value="" 
      required={true}
    />
    <ControlButtons 
      saveLabel="Criar"
      cancelHref="/admin/register/user/school-type"
    />
  </FormBase>
</BaseLayout>