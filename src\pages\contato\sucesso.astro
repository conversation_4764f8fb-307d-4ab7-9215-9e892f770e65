---
/**
 * Página de sucesso do formulário de contato
 *
 * Esta página é exibida após o envio bem-sucedido do formulário de contato.
 */

import Layout from '../../layouts/Layout.astro';
---

<Layout title="Mensagem Enviada | Estação da Alfabetização">
  <main class="container mx-auto px-4 py-8">
    <div class="max-w-2xl mx-auto text-center">
      <div class="success-icon mb-6">
        <svg xmlns="http://www.w3.org/2000/svg" width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
          <polyline points="22 4 12 14.01 9 11.01"></polyline>
        </svg>
      </div>
      
      <h1 class="text-3xl font-bold mb-4">Mensagem Enviada com Sucesso!</h1>
      
      <p class="text-lg mb-6">
        Agradecemos pelo seu contato. Nossa equipe irá analisar sua mensagem e responderá o mais breve possível.
      </p>
      
      <div class="info-box p-6 bg-blue-50 rounded-lg mb-8">
        <h2 class="text-xl font-semibold mb-2">O que acontece agora?</h2>
        
        <ul class="text-left list-disc pl-6 space-y-2">
          <li>Sua mensagem foi registrada em nosso sistema</li>
          <li>Um membro da nossa equipe irá analisá-la em até 24 horas úteis</li>
          <li>Você receberá uma resposta no email informado</li>
          <li>Se necessário, entraremos em contato por telefone para mais informações</li>
        </ul>
      </div>
      
      <div class="flex flex-col sm:flex-row gap-4 justify-center">
        <a href="/" class="btn btn-primary">
          Voltar para a Página Inicial
        </a>
        
        <a href="/contato" class="btn btn-secondary">
          Enviar Nova Mensagem
        </a>
      </div>
    </div>
  </main>
</Layout>

<style>
  .success-icon {
    color: var(--primary-green);
    display: flex;
    justify-content: center;
    align-items: center;
  }
  
  .info-box {
    border-left: 4px solid var(--primary-blue);
  }
  
  .btn {
    display: inline-block;
    padding: 0.75rem 1.5rem;
    border-radius: 0.375rem;
    font-weight: 500;
    text-align: center;
    transition: all 0.2s;
  }
  
  .btn-primary {
    background-color: var(--primary-blue);
    color: white;
  }
  
  .btn-primary:hover {
    background-color: var(--primary-blue-dark);
  }
  
  .btn-secondary {
    background-color: white;
    color: var(--primary-blue);
    border: 1px solid var(--primary-blue);
  }
  
  .btn-secondary:hover {
    background-color: var(--primary-blue-light);
    color: var(--primary-blue-dark);
  }
</style>
