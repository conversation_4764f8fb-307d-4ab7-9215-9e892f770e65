/**
 * Helper para facilitar o uso do cache em diferentes contextos
 * Implementa funções de alto nível para operações comuns de cache
 */

import { type CacheableDataType, cacheService } from '@services/cacheService';
import { logger } from '@utils/logger';

/**
 * Interface para opções do decorator de cache
 */
export interface CacheDecoratorOptions {
  type: CacheableDataType;
  ttl?: number;
  keyPrefix?: string;
  keyGenerator?: (...args: any[]) => string;
}

/**
 * Helper para operações de cache
 */
export const cacheHelper = {
  /**
   * Busca um valor do cache, ou executa a função e armazena o resultado
   * @param type - Tipo de dado para cache
   * @param key - Chave para o cache
   * @param fn - Função a ser executada caso o cache não exista
   * @param ttl - Tempo de vida do cache em segundos (opcional)
   * @returns Resultado da função ou valor em cache
   */
  async getOrSet<T>(
    type: CacheableDataType,
    key: string,
    fn: () => Promise<T>,
    ttl?: number
  ): Promise<T> {
    try {
      // Tentar obter do cache
      const cachedValue = await cacheService.get<T>(type, key);

      // Se encontrou no cache, retornar
      if (cachedValue !== null) {
        return cachedValue;
      }

      // Executar função para obter valor
      const value = await fn();

      // Armazenar no cache
      await cacheService.set(type, key, value, { ttl });

      return value;
    } catch (error) {
      logger.error('Erro ao usar getOrSet do cache:', error);

      // Em caso de erro, executar a função diretamente
      return fn();
    }
  },

  /**
   * Decorator para cache de métodos
   * @param options - Opções de configuração do cache
   * @returns Decorator de método
   */
  cached(options: CacheDecoratorOptions) {
    return (target: any, propertyKey: string, descriptor: PropertyDescriptor) => {
      const originalMethod = descriptor.value;

      descriptor.value = async function (...args: any[]) {
        try {
          // Gerar chave de cache
          let cacheKey: string;

          if (options.keyGenerator) {
            // Usar gerador de chave personalizado
            cacheKey = options.keyGenerator(...args);
          } else {
            // Gerar chave baseada nos argumentos
            const argsKey =
              args.length > 0 ? JSON.stringify(args).replace(/[{}"\\]/g, '') : 'noargs';

            cacheKey = `${options.keyPrefix || propertyKey}:${argsKey}`;
          }

          // Usar getOrSet para implementar cache
          return cacheHelper.getOrSet(
            options.type,
            cacheKey,
            () => originalMethod.apply(this, args),
            options.ttl
          );
        } catch (error) {
          logger.error(`Erro no decorator de cache para ${propertyKey}:`, error);

          // Em caso de erro, executar método original
          return originalMethod.apply(this, args);
        }
      };

      return descriptor;
    };
  },

  /**
   * Invalida o cache após a execução de um método
   * @param type - Tipo de dado para invalidar
   * @param keyPattern - Padrão de chave para invalidar (opcional)
   * @returns Decorator de método
   */
  invalidateCache(type: CacheableDataType, keyPattern?: string) {
    return (target: any, propertyKey: string, descriptor: PropertyDescriptor) => {
      const originalMethod = descriptor.value;

      descriptor.value = async function (...args: any[]) {
        try {
          // Executar método original
          const result = await originalMethod.apply(this, args);

          // Invalidar cache
          if (keyPattern) {
            // Invalidar chave específica
            await cacheService.delete(type, keyPattern);
          } else {
            // Invalidar todo o tipo
            await cacheService.invalidateType(type);
          }

          return result;
        } catch (error) {
          logger.error(`Erro no decorator de invalidação de cache para ${propertyKey}:`, error);

          // Em caso de erro, executar método original sem invalidar cache
          return originalMethod.apply(this, args);
        }
      };

      return descriptor;
    };
  },

  /**
   * Gera uma chave de cache para consultas SQL
   * @param sql - Consulta SQL
   * @param params - Parâmetros da consulta
   * @returns Chave de cache
   */
  generateQueryCacheKey(sql: string, params?: any[]): string {
    // Normalizar SQL (remover espaços extras)
    const normalizedSql = sql.replace(/\s+/g, ' ').trim();

    // Gerar hash da consulta com parâmetros
    if (params && params.length > 0) {
      return `${normalizedSql}:${JSON.stringify(params)}`;
    }

    return normalizedSql;
  },

  /**
   * Limpa todo o cache
   * @returns Verdadeiro se o cache foi limpo com sucesso
   */
  async clearAll(): Promise<boolean> {
    return cacheService.clear();
  },

  /**
   * Obtém estatísticas do cache
   * @returns Objeto com estatísticas
   */
  async getStats(): Promise<any> {
    return cacheService.getStats();
  },
};
