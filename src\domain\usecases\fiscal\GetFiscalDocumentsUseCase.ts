/**
 * Get Fiscal Documents Use Case
 *
 * Caso de uso para obter documentos fiscais com filtros, ordenação e paginação.
 * Parte da implementação da tarefa 8.7.2 - Gestão de documentos fiscais
 */

import { FiscalDocumentStatus, FiscalDocumentType } from '../../entities/FiscalDocument';
import {
  FiscalDocumentFilter,
  FiscalDocumentPaginationOptions,
  FiscalDocumentRepository,
  FiscalDocumentSortOptions,
  PaginatedFiscalDocuments,
} from '../../repositories/FiscalDocumentRepository';

export interface GetFiscalDocumentsRequest {
  filter?: {
    ids?: string[];
    type?: FiscalDocumentType | FiscalDocumentType[];
    status?: FiscalDocumentStatus | FiscalDocumentStatus[];
    number?: string;
    customerDocumentNumber?: string;
    customerName?: string;
    startDate?: Date;
    endDate?: Date;
    minValue?: number;
    maxValue?: number;
  };
  sort?: {
    field: 'issueDate' | 'number' | 'customerName' | 'finalValue' | 'createdAt' | 'updatedAt';
    direction: 'asc' | 'desc';
  };
  pagination?: {
    page: number;
    limit: number;
  };
}

export interface GetFiscalDocumentsResponse {
  success: boolean;
  data?: PaginatedFiscalDocuments;
  error?: string;
}

export class GetFiscalDocumentsUseCase {
  constructor(private fiscalDocumentRepository: FiscalDocumentRepository) {}

  async execute(request: GetFiscalDocumentsRequest): Promise<GetFiscalDocumentsResponse> {
    try {
      // Validar os dados de entrada
      if (request.pagination && (request.pagination.page < 1 || request.pagination.limit < 1)) {
        return {
          success: false,
          error: 'Parâmetros de paginação inválidos.',
        };
      }

      // Mapear filtros
      const filter: FiscalDocumentFilter = request.filter || {};

      // Mapear ordenação
      const sort: FiscalDocumentSortOptions | undefined = request.sort
        ? {
            field: request.sort.field,
            direction: request.sort.direction,
          }
        : undefined;

      // Mapear paginação
      const pagination: FiscalDocumentPaginationOptions | undefined = request.pagination
        ? {
            page: request.pagination.page,
            limit: request.pagination.limit,
          }
        : undefined;

      // Buscar documentos fiscais
      const result = await this.fiscalDocumentRepository.find(filter, sort, pagination);

      return {
        success: true,
        data: result,
      };
    } catch (error) {
      console.error('Erro ao obter documentos fiscais:', error);

      return {
        success: false,
        error:
          error instanceof Error ? error.message : 'Erro desconhecido ao obter documentos fiscais.',
      };
    }
  }
}
