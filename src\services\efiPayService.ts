import crypto from 'node:crypto';
import fs from 'node:fs';
import path from 'node:path';
import { paymentConfig } from '@config/payment';
// src/services/efiPayService.ts
import EfiPay from 'sdk-node-apis-efi';

/**
 * Interface para dados do cliente
 */
export interface CustomerData {
  name: string;
  cpf?: string;
  cnpj?: string;
  email: string;
  birthDate?: string;
  phone?: string;
}

/**
 * Interface para dados de endereço
 */
export interface AddressData {
  street: string;
  number: string;
  neighborhood: string;
  zipcode: string;
  city: string;
  state: string;
  complement?: string;
}

/**
 * Serviço para integração com a Efí Pay
 */
export const efiPayService = {
  /**
   * Inicializa a instância da Efí Pay
   * @returns Instância da Efí Pay
   */
  getInstance(): any {
    const options = {
      client_id: paymentConfig.providers.efipay.clientId,
      client_secret: paymentConfig.providers.efipay.clientSecret,
      sandbox: paymentConfig.providers.efipay.sandbox,
      certificate: this.getCertificatePath(),
    };

    return new EfiPay(options);
  },

  /**
   * Obtém o caminho do certificado
   * @returns Caminho do certificado
   */
  getCertificatePath(): string {
    const certPath = paymentConfig.providers.efipay.certificatePath;

    // Verificar se o caminho é absoluto ou relativo
    if (path.isAbsolute(certPath)) {
      return certPath;
    }

    // Se for relativo, resolver a partir do diretório raiz do projeto
    return path.resolve(process.cwd(), certPath);
  },

  /**
   * Cria uma cobrança PIX imediata sem txid
   * @param value - Valor da cobrança em centavos
   * @param description - Descrição da cobrança
   * @param customer - Dados do cliente (opcional)
   * @param expiresIn - Tempo de expiração em segundos (padrão: 3600 = 1 hora)
   * @returns Dados da cobrança PIX
   */
  async createPixCharge(
    value: number,
    description: string,
    customer?: CustomerData,
    expiresIn = 3600
  ): Promise<any> {
    try {
      const efi = this.getInstance();

      const body: any = {
        calendario: {
          expiracao: expiresIn,
        },
        valor: {
          original: (value / 100).toFixed(2),
        },
        chave: paymentConfig.providers.efipay.pixKey,
        solicitacaoPagador: description,
      };

      // Adicionar dados do devedor se fornecidos
      if (customer) {
        body.devedor = {
          nome: customer.name,
        };

        if (customer.cpf) {
          body.devedor.cpf = customer.cpf;
        } else if (customer.cnpj) {
          body.devedor.cnpj = customer.cnpj;
        }
      }

      // Adicionar informações adicionais se necessário
      if (description.length > 140) {
        body.infoAdicionais = [
          {
            nome: 'Descrição completa',
            valor: description,
          },
        ];
        body.solicitacaoPagador = description.substring(0, 140);
      }

      const response = await efi.pixCreateImmediateCharge([], body);

      // Gerar QR Code
      if (response.loc?.id) {
        const qrcode = await efi.pixGenerateQRCode({ id: response.loc.id });
        return { ...response, qrcode };
      }

      return response;
    } catch (error) {
      console.error('Erro ao criar cobrança PIX:', error);
      throw error;
    }
  },

  /**
   * Cria uma cobrança PIX imediata com txid específico
   * @param txid - ID da transação (deve ser único)
   * @param value - Valor da cobrança em centavos
   * @param description - Descrição da cobrança
   * @param customer - Dados do cliente (opcional)
   * @param expiresIn - Tempo de expiração em segundos (padrão: 3600 = 1 hora)
   * @returns Dados da cobrança PIX
   */
  async createPixChargeWithTxid(
    txid: string,
    value: number,
    description: string,
    customer?: CustomerData,
    expiresIn = 3600
  ): Promise<any> {
    try {
      const efi = this.getInstance();

      const body: any = {
        calendario: {
          expiracao: expiresIn,
        },
        valor: {
          original: (value / 100).toFixed(2),
        },
        chave: paymentConfig.providers.efipay.pixKey,
        solicitacaoPagador: description,
      };

      // Adicionar dados do devedor se fornecidos
      if (customer) {
        body.devedor = {
          nome: customer.name,
        };

        if (customer.cpf) {
          body.devedor.cpf = customer.cpf;
        } else if (customer.cnpj) {
          body.devedor.cnpj = customer.cnpj;
        }
      }

      // Adicionar informações adicionais se necessário
      if (description.length > 140) {
        body.infoAdicionais = [
          {
            nome: 'Descrição completa',
            valor: description,
          },
        ];
        body.solicitacaoPagador = description.substring(0, 140);
      }

      const params = {
        txid: txid,
      };

      const response = await efi.pixCreateImmediateCharge(params, body);

      // Gerar QR Code
      if (response.loc?.id) {
        const qrcode = await efi.pixGenerateQRCode({ id: response.loc.id });
        return { ...response, qrcode };
      }

      return response;
    } catch (error) {
      console.error('Erro ao criar cobrança PIX com txid:', error);
      throw error;
    }
  },

  /**
   * Consulta uma cobrança PIX pelo txid
   * @param txid - ID da transação
   * @returns Dados da cobrança
   */
  async getPixCharge(txid: string): Promise<any> {
    try {
      const efi = this.getInstance();

      const params = {
        txid,
      };

      return await efi.pixDetailCharge(params);
    } catch (error) {
      console.error('Erro ao consultar cobrança PIX:', error);
      throw error;
    }
  },

  /**
   * Revisa (atualiza) uma cobrança PIX existente
   * @param txid - ID da transação
   * @param updates - Campos a serem atualizados
   * @returns Dados da cobrança atualizada
   */
  async updatePixCharge(txid: string, updates: any): Promise<any> {
    try {
      const efi = this.getInstance();

      const params = {
        txid,
      };

      return await efi.pixUpdateCharge(params, updates);
    } catch (error) {
      console.error('Erro ao atualizar cobrança PIX:', error);
      throw error;
    }
  },

  /**
   * Consulta uma lista de cobranças PIX
   * @param startDate - Data de início (ISO 8601)
   * @param endDate - Data de fim (ISO 8601)
   * @param cpf - CPF do pagador (opcional)
   * @param cnpj - CNPJ do pagador (opcional)
   * @param status - Status da cobrança (opcional)
   * @returns Lista de cobranças
   */
  async listPixCharges(
    startDate: string,
    endDate: string,
    cpf?: string,
    cnpj?: string,
    status?: string
  ): Promise<any> {
    try {
      const efi = this.getInstance();

      const params: any = {
        inicio: startDate,
        fim: endDate,
      };

      if (cpf) params.cpf = cpf;
      if (cnpj) params.cnpj = cnpj;
      if (status) params.status = status;

      return await efi.pixListCharges(params);
    } catch (error) {
      console.error('Erro ao listar cobranças PIX:', error);
      throw error;
    }
  },

  /**
   * Configura o webhook para receber notificações de pagamentos PIX
   * @param key - Chave PIX para a qual o webhook será configurado
   * @param url - URL do webhook
   * @param skipMtls - Se deve pular a verificação mTLS (padrão: false)
   * @returns Resultado da configuração
   */
  async configureWebhook(key: string, url: string, skipMtls = false): Promise<any> {
    try {
      const efi = this.getInstance();

      const params = {
        chave: key,
      };

      const body = {
        webhookUrl: url,
      };

      const headers: any = {};
      if (skipMtls) {
        headers['x-skip-mtls-checking'] = 'true';
      }

      return await efi.pixConfigWebhook(params, body, headers);
    } catch (error) {
      console.error('Erro ao configurar webhook PIX:', error);
      throw error;
    }
  },

  /**
   * Consulta a configuração do webhook para uma chave PIX
   * @param key - Chave PIX
   * @returns Dados do webhook
   */
  async getWebhook(key: string): Promise<any> {
    try {
      const efi = this.getInstance();

      const params = {
        chave: key,
      };

      return await efi.pixDetailWebhook(params);
    } catch (error) {
      console.error('Erro ao consultar webhook PIX:', error);
      throw error;
    }
  },

  /**
   * Cancela o webhook para uma chave PIX
   * @param key - Chave PIX
   * @returns Resultado do cancelamento
   */
  async cancelWebhook(key: string): Promise<any> {
    try {
      const efi = this.getInstance();

      const params = {
        chave: key,
      };

      return await efi.pixDeleteWebhook(params);
    } catch (error) {
      console.error('Erro ao cancelar webhook PIX:', error);
      throw error;
    }
  },

  /**
   * Solicita o reenvio de webhooks para transações específicas
   * @param type - Tipo de webhook (PIX_RECEBIDO, PIX_ENVIADO, DEVOLUCAO_RECEBIDA, DEVOLUCAO_ENVIADA)
   * @param e2eIds - Lista de IDs end-to-end das transações
   * @returns Resultado da solicitação
   */
  async resendWebhooks(type: string, e2eIds: string[]): Promise<any> {
    try {
      const efi = this.getInstance();

      const body = {
        tipo: type,
        e2eids: e2eIds,
      };

      return await efi.pixResendWebhook([], body);
    } catch (error) {
      console.error('Erro ao solicitar reenvio de webhooks:', error);
      throw error;
    }
  },

  /**
   * Cria uma cobrança com boleto
   * @param value - Valor da cobrança em centavos
   * @param customer - Dados do cliente
   * @param expirationDate - Data de vencimento (YYYY-MM-DD)
   * @returns Dados da cobrança com boleto
   */
  async createBilletCharge(
    value: number,
    customer: CustomerData,
    expirationDate: string
  ): Promise<any> {
    try {
      const efi = this.getInstance();

      if (!customer.cpf && !customer.cnpj) {
        throw new Error('É necessário informar CPF ou CNPJ do cliente');
      }

      const body: any = {
        items: [
          {
            name: 'Produto/Serviço',
            value: value,
            amount: 1,
          },
        ],
        payment: {
          banking_billet: {
            expire_at: expirationDate,
            customer: {
              name: customer.name,
              email: customer.email,
              phone_number: customer.phone,
            },
          },
        },
      };

      // Adicionar CPF ou CNPJ
      if (customer.cpf) {
        body.payment.banking_billet.customer.cpf = customer.cpf;
      } else if (customer.cnpj) {
        body.payment.banking_billet.customer.cnpj = customer.cnpj;
      }

      return await efi.createOneStepCharge([], body);
    } catch (error) {
      console.error('Erro ao criar cobrança com boleto:', error);
      throw error;
    }
  },

  /**
   * Cria uma cobrança com cartão de crédito
   * @param value - Valor da cobrança em centavos
   * @param customer - Dados do cliente
   * @param paymentToken - Token do cartão de crédito
   * @param address - Endereço de cobrança
   * @param installments - Número de parcelas
   * @returns Dados da cobrança com cartão de crédito
   */
  async createCreditCardCharge(
    value: number,
    customer: CustomerData,
    paymentToken: string,
    address: AddressData,
    installments = 1
  ): Promise<any> {
    try {
      const efi = this.getInstance();

      if (!customer.cpf && !customer.cnpj) {
        throw new Error('É necessário informar CPF ou CNPJ do cliente');
      }

      if (!customer.birthDate) {
        throw new Error('É necessário informar a data de nascimento do cliente');
      }

      const body: any = {
        items: [
          {
            name: 'Produto/Serviço',
            value: value,
            amount: 1,
          },
        ],
        payment: {
          credit_card: {
            installments: installments,
            payment_token: paymentToken,
            billing_address: {
              street: address.street,
              number: address.number,
              neighborhood: address.neighborhood,
              zipcode: address.zipcode,
              city: address.city,
              state: address.state,
            },
            customer: {
              name: customer.name,
              email: customer.email,
              birth: customer.birthDate,
              phone_number: customer.phone,
            },
          },
        },
      };

      // Adicionar complemento se fornecido
      if (address.complement) {
        body.payment.credit_card.billing_address.complement = address.complement;
      }

      // Adicionar CPF ou CNPJ
      if (customer.cpf) {
        body.payment.credit_card.customer.cpf = customer.cpf;
      } else if (customer.cnpj) {
        body.payment.credit_card.customer.cnpj = customer.cnpj;
      }

      return await efi.createOneStepCharge([], body);
    } catch (error) {
      console.error('Erro ao criar cobrança com cartão de crédito:', error);
      throw error;
    }
  },

  /**
   * Verifica a assinatura de um webhook recebido
   * @param body - Corpo da requisição
   * @param signature - Assinatura recebida no header X-Efipay-Signature
   * @returns Verdadeiro se a assinatura for válida
   */
  verifyWebhookSignature(body: string, signature: string): boolean {
    try {
      const expectedSignature = crypto
        .createHmac('sha256', paymentConfig.providers.efipay.clientSecret)
        .update(body)
        .digest('hex');

      return signature === expectedSignature;
    } catch (error) {
      console.error('Erro ao verificar assinatura do webhook:', error);
      return false;
    }
  },

  /**
   * Gera um txid único para cobranças PIX
   * @param prefix - Prefixo para o txid (opcional)
   * @returns txid único
   */
  generateTxid(prefix = ''): string {
    const timestamp = Date.now().toString();
    const random = Math.random().toString(36).substring(2, 10);
    const txid = `${prefix}${timestamp}${random}`.substring(0, 32);
    return txid;
  },
};
