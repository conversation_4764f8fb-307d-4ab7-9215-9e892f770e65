---
/**
 * Página de métricas de cache
 *
 * Esta página exibe métricas e estatísticas do sistema de cache.
 */

// Importações
import AdminLayout from '@layouts/AdminLayout.astro';
import { cacheMetricsService } from '@services/cacheMetricsService';
import { cacheService } from '@services/cacheService';
import { getCurrentUser } from '@utils/authUtils';
import { logger } from '@utils/logger';
import { hasPermission } from '@utils/permissionUtils';

// Verificar autenticação e permissão
const user = await getCurrentUser(Astro.cookies);

// Redirecionar se não estiver autenticado ou não tiver permissão
if (!user || !(await hasPermission(user.ulid_user, 'cache:view'))) {
  return Astro.redirect('/admin/login?redirect=/admin/cache-metrics');
}

// Obter métricas atuais
let currentMetrics;
let metricsHistory;
let serverStats;
let error = '';

try {
  // Inicializar serviço de métricas se necessário
  if (!cacheMetricsService.history.length) {
    cacheMetricsService.initialize(15);
  }

  // Coletar métricas atuais
  currentMetrics = await cacheMetricsService.getCurrentMetrics();

  // Obter histórico de métricas
  metricsHistory = cacheMetricsService.getMetricsHistory(10);

  // Obter estatísticas do servidor
  serverStats = await cacheService.getStats();
} catch (e) {
  logger.error('Erro ao obter métricas de cache:', e);
  error = 'Não foi possível carregar as métricas de cache. Tente novamente mais tarde.';
}

// Processar ações
if (Astro.request.method === 'POST') {
  try {
    const formData = await Astro.request.formData();
    const action = formData.get('action');

    if (action === 'reset-metrics') {
      // Verificar permissão
      if (!(await hasPermission(user.ulid_user, 'cache:manage'))) {
        error = 'Você não tem permissão para executar esta ação.';
      } else {
        // Resetar contadores
        cacheMetricsService.resetCounters();

        // Redirecionar para atualizar a página
        return Astro.redirect('/admin/cache-metrics?success=reset');
      }
    } else if (action === 'warmup-cache') {
      // Verificar permissão
      if (!(await hasPermission(user.ulid_user, 'cache:manage'))) {
        error = 'Você não tem permissão para executar esta ação.';
      } else {
        // Importar serviço de warm-up
        const { cacheWarmupService } = await import('@services/cacheWarmupService');

        // Executar warm-up
        await cacheWarmupService.warmupCache();

        // Redirecionar para atualizar a página
        return Astro.redirect('/admin/cache-metrics?success=warmup');
      }
    }
  } catch (e) {
    logger.error('Erro ao processar ação de cache:', e);
    error = 'Ocorreu um erro ao processar sua solicitação. Tente novamente.';
  }
}

// Obter mensagens de sucesso
const success = Astro.url.searchParams.get('success');

let successMessage = '';

if (success === 'reset') {
  successMessage = 'Contadores de métricas resetados com sucesso.';
} else if (success === 'warmup') {
  successMessage = 'Warm-up de cache executado com sucesso.';
}

// Formatar número com 2 casas decimais
function formatNumber(num: number): string {
  return num.toFixed(2);
}

// Formatar porcentagem
function formatPercent(num: number): string {
  return `${(num * 100).toFixed(2)}%`;
}

// Formatar data
function formatDate(timestamp: number): string {
  return new Date(timestamp).toLocaleString();
}

// Título da página
const title = 'Métricas de Cache';
---

<AdminLayout title={title}>
  <main class="container mx-auto px-4 py-8">
    <div class="max-w-6xl mx-auto">
      <h1 class="text-3xl font-bold mb-6">{title}</h1>
      
      {error && (
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
          <p>{error}</p>
        </div>
      )}
      
      {successMessage && (
        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6">
          <p>{successMessage}</p>
        </div>
      )}
      
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        <!-- Resumo de métricas -->
        <div class="bg-white rounded-lg shadow p-6">
          <h2 class="text-xl font-semibold mb-4">Resumo de Métricas</h2>
          
          {currentMetrics ? (
            <div class="grid grid-cols-2 gap-4">
              <div class="bg-blue-50 p-4 rounded">
                <p class="text-sm text-gray-600">Total de Requisições</p>
                <p class="text-2xl font-bold">{currentMetrics.totalRequests}</p>
              </div>
              
              <div class="bg-green-50 p-4 rounded">
                <p class="text-sm text-gray-600">Taxa de Acertos</p>
                <p class="text-2xl font-bold">{formatPercent(currentMetrics.hitRate)}</p>
              </div>
              
              <div class="bg-blue-50 p-4 rounded">
                <p class="text-sm text-gray-600">Acertos (Hits)</p>
                <p class="text-2xl font-bold">{currentMetrics.hits}</p>
              </div>
              
              <div class="bg-red-50 p-4 rounded">
                <p class="text-sm text-gray-600">Erros (Misses)</p>
                <p class="text-2xl font-bold">{currentMetrics.misses}</p>
              </div>
            </div>
          ) : (
            <div class="text-center py-4 text-gray-500">
              <p>Nenhuma métrica disponível.</p>
            </div>
          )}
          
          <div class="mt-6 pt-4 border-t flex justify-between">
            <form method="POST">
              <input type="hidden" name="action" value="reset-metrics">
              
              <button 
                type="submit"
                class="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700 transition"
              >
                Resetar Contadores
              </button>
            </form>
            
            <form method="POST">
              <input type="hidden" name="action" value="warmup-cache">
              
              <button 
                type="submit"
                class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition"
              >
                Executar Warm-up
              </button>
            </form>
          </div>
        </div>
        
        <!-- Estatísticas do servidor -->
        <div class="bg-white rounded-lg shadow p-6">
          <h2 class="text-xl font-semibold mb-4">Estatísticas do Servidor</h2>
          
          {serverStats ? (
            <div class="space-y-2">
              <div class="flex justify-between py-2 border-b">
                <span class="font-medium">Memória Utilizada</span>
                <span>{serverStats.used_memory_human || 'N/A'}</span>
              </div>
              
              <div class="flex justify-between py-2 border-b">
                <span class="font-medium">Pico de Memória</span>
                <span>{serverStats.used_memory_peak_human || 'N/A'}</span>
              </div>
              
              <div class="flex justify-between py-2 border-b">
                <span class="font-medium">Clientes Conectados</span>
                <span>{serverStats.connected_clients || 'N/A'}</span>
              </div>
              
              <div class="flex justify-between py-2 border-b">
                <span class="font-medium">Tempo de Atividade</span>
                <span>{serverStats.uptime_in_days ? `${serverStats.uptime_in_days} dias` : 'N/A'}</span>
              </div>
              
              <div class="flex justify-between py-2 border-b">
                <span class="font-medium">Comandos Processados</span>
                <span>{serverStats.total_commands_processed || 'N/A'}</span>
              </div>
              
              <div class="flex justify-between py-2 border-b">
                <span class="font-medium">Chaves Expiradas</span>
                <span>{serverStats.expired_keys || 'N/A'}</span>
              </div>
              
              <div class="flex justify-between py-2">
                <span class="font-medium">Versão</span>
                <span>{serverStats.redis_version || 'N/A'}</span>
              </div>
            </div>
          ) : (
            <div class="text-center py-4 text-gray-500">
              <p>Estatísticas do servidor não disponíveis.</p>
            </div>
          )}
        </div>
      </div>
      
      <!-- Métricas por tipo -->
      <div class="bg-white rounded-lg shadow p-6 mb-8">
        <h2 class="text-xl font-semibold mb-4">Métricas por Tipo de Dado</h2>
        
        {currentMetrics && Object.keys(currentMetrics.byType).length > 0 ? (
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead>
                <tr>
                  <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Tipo
                  </th>
                  <th class="px-6 py-3 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Requisições
                  </th>
                  <th class="px-6 py-3 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Acertos
                  </th>
                  <th class="px-6 py-3 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Erros
                  </th>
                  <th class="px-6 py-3 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Taxa de Acertos
                  </th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                {Object.entries(currentMetrics.byType).map(([type, metrics]) => (
                  <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {type}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-right">
                      {metrics.requests}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-right">
                      {metrics.hits}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-right">
                      {metrics.misses}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-right">
                      {formatPercent(metrics.hitRate)}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <div class="text-center py-4 text-gray-500">
            <p>Nenhuma métrica por tipo disponível.</p>
          </div>
        )}
      </div>
      
      <!-- Histórico de métricas -->
      <div class="bg-white rounded-lg shadow p-6">
        <h2 class="text-xl font-semibold mb-4">Histórico de Métricas</h2>
        
        {metricsHistory && metricsHistory.length > 0 ? (
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead>
                <tr>
                  <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Data/Hora
                  </th>
                  <th class="px-6 py-3 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Requisições
                  </th>
                  <th class="px-6 py-3 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Acertos
                  </th>
                  <th class="px-6 py-3 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Erros
                  </th>
                  <th class="px-6 py-3 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Taxa de Acertos
                  </th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                {metricsHistory.map((metrics) => (
                  <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {formatDate(metrics.timestamp)}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-right">
                      {metrics.totalRequests}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-right">
                      {metrics.hits}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-right">
                      {metrics.misses}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-right">
                      {formatPercent(metrics.hitRate)}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <div class="text-center py-4 text-gray-500">
            <p>Nenhum histórico de métricas disponível.</p>
          </div>
        )}
      </div>
    </div>
  </main>
</AdminLayout>
