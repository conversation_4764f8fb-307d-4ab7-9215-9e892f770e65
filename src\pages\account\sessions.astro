---
/**
 * Página de gerenciamento de sessões do usuário
 *
 * Esta página permite ao usuário visualizar e gerenciar suas sessões ativas.
 */

// Importações
import MainLayout from '@layouts/MainLayout.astro';
import { sessionService } from '@services/sessionService';
import { getCurrentUser } from '@utils/authUtils';
import { logger } from '@utils/logger';

// Verificar autenticação
const user = await getCurrentUser(Astro.cookies);

// Redirecionar se não estiver autenticado
if (!user) {
  return Astro.redirect('/signin?redirect=/account/sessions');
}

// Obter ID de sessão atual
const currentSessionId = Astro.cookies.get('session_id')?.value;

// Obter todas as sessões do usuário
let sessions = [];
let error = '';

try {
  sessions = await sessionService.getUserSessions(user.ulid_user);

  // Ordenar sessões (atual primeiro, depois por data de última atividade)
  sessions.sort((a, b) => {
    // Sessão atual sempre primeiro
    if (a.id === currentSessionId) return -1;
    if (b.id === currentSessionId) return 1;

    // Depois ordenar por última atividade (mais recente primeiro)
    return b.lastActivity - a.lastActivity;
  });
} catch (e) {
  logger.error('Erro ao obter sessões do usuário:', e);
  error = 'Não foi possível carregar suas sessões. Tente novamente mais tarde.';
}

// Processar formulário para encerrar sessões
if (Astro.request.method === 'POST') {
  try {
    const formData = await Astro.request.formData();
    const action = formData.get('action');

    if (action === 'terminate-session') {
      const sessionId = formData.get('sessionId') as string;

      if (sessionId) {
        // Não permitir encerrar a sessão atual
        if (sessionId === currentSessionId) {
          error = 'Você não pode encerrar sua sessão atual por aqui. Use o botão de logout.';
        } else {
          // Encerrar sessão
          await sessionService.delete(sessionId);

          // Redirecionar para atualizar a lista
          return Astro.redirect('/account/sessions?success=session-terminated');
        }
      }
    } else if (action === 'terminate-all') {
      // Encerrar todas as sessões exceto a atual
      let terminatedCount = 0;

      for (const session of sessions) {
        if (session.id !== currentSessionId) {
          await sessionService.delete(session.id);
          terminatedCount++;
        }
      }

      // Redirecionar para atualizar a lista
      return Astro.redirect(`/account/sessions?success=all-terminated&count=${terminatedCount}`);
    }
  } catch (e) {
    logger.error('Erro ao processar formulário de sessões:', e);
    error = 'Ocorreu um erro ao processar sua solicitação. Tente novamente.';
  }
}

// Obter mensagens de sucesso
const success = Astro.url.searchParams.get('success');
const count = Astro.url.searchParams.get('count');

let successMessage = '';

if (success === 'session-terminated') {
  successMessage = 'Sessão encerrada com sucesso.';
} else if (success === 'all-terminated') {
  successMessage = `${count} sessões encerradas com sucesso.`;
}

// Função para formatar data
function formatDate(timestamp: number): string {
  return new Date(timestamp).toLocaleString();
}

// Função para obter tempo relativo
function getRelativeTime(timestamp: number): string {
  const now = Date.now();
  const diff = now - timestamp;

  // Menos de um minuto
  if (diff < 60 * 1000) {
    return 'agora mesmo';
  }

  // Menos de uma hora
  if (diff < 60 * 60 * 1000) {
    const minutes = Math.floor(diff / (60 * 1000));
    return `há ${minutes} ${minutes === 1 ? 'minuto' : 'minutos'}`;
  }

  // Menos de um dia
  if (diff < 24 * 60 * 60 * 1000) {
    const hours = Math.floor(diff / (60 * 60 * 1000));
    return `há ${hours} ${hours === 1 ? 'hora' : 'horas'}`;
  }

  // Menos de uma semana
  if (diff < 7 * 24 * 60 * 60 * 1000) {
    const days = Math.floor(diff / (24 * 60 * 60 * 1000));
    return `há ${days} ${days === 1 ? 'dia' : 'dias'}`;
  }

  // Mais de uma semana
  return formatDate(timestamp);
}

// Título da página
const title = 'Gerenciar Sessões';
---

<MainLayout title={title}>
  <main class="container mx-auto px-4 py-8">
    <div class="max-w-4xl mx-auto">
      <h1 class="text-3xl font-bold mb-6">{title}</h1>
      
      {error && (
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
          <p>{error}</p>
        </div>
      )}
      
      {successMessage && (
        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6">
          <p>{successMessage}</p>
        </div>
      )}
      
      <div class="bg-white rounded-lg shadow p-6 mb-6">
        <h2 class="text-xl font-semibold mb-4">Suas Sessões Ativas</h2>
        
        <p class="text-gray-600 mb-6">
          Aqui você pode ver e gerenciar todas as suas sessões ativas. Se você não reconhecer alguma sessão, 
          recomendamos encerrá-la imediatamente e alterar sua senha.
        </p>
        
        {sessions.length > 0 ? (
          <div class="space-y-4">
            {sessions.map(session => (
              <div class={`border rounded-lg p-4 ${session.id === currentSessionId ? 'bg-blue-50 border-blue-200' : ''}`}>
                <div class="flex items-start justify-between">
                  <div>
                    <div class="flex items-center">
                      <h3 class="text-lg font-medium">
                        {session.id === currentSessionId ? 'Sessão Atual' : 'Sessão Ativa'}
                      </h3>
                      
                      {session.id === currentSessionId && (
                        <span class="ml-2 px-2 py-1 bg-blue-100 text-blue-800 text-xs font-semibold rounded-full">
                          Atual
                        </span>
                      )}
                    </div>
                    
                    <div class="mt-2 text-sm text-gray-600 space-y-1">
                      <p>
                        <span class="font-medium">Dispositivo:</span> {session.userAgent?.split('(')[0] || 'Desconhecido'}
                      </p>
                      <p>
                        <span class="font-medium">Endereço IP:</span> {session.ipAddress || 'Desconhecido'}
                      </p>
                      <p>
                        <span class="font-medium">Criada em:</span> {formatDate(session.createdAt)}
                      </p>
                      <p>
                        <span class="font-medium">Última atividade:</span> {getRelativeTime(session.lastActivity)}
                      </p>
                    </div>
                  </div>
                  
                  {session.id !== currentSessionId && (
                    <form method="POST">
                      <input type="hidden" name="action" value="terminate-session">
                      <input type="hidden" name="sessionId" value={session.id}>
                      
                      <button 
                        type="submit"
                        class="text-red-600 hover:text-red-800 text-sm font-medium"
                      >
                        Encerrar Sessão
                      </button>
                    </form>
                  )}
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div class="text-center py-4 text-gray-500">
            <p>Nenhuma sessão ativa encontrada.</p>
          </div>
        )}
        
        {sessions.length > 1 && (
          <div class="mt-6 pt-4 border-t">
            <form method="POST">
              <input type="hidden" name="action" value="terminate-all">
              
              <button 
                type="submit"
                class="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700 transition"
              >
                Encerrar Todas as Outras Sessões
              </button>
            </form>
          </div>
        )}
      </div>
      
      <div class="bg-white rounded-lg shadow p-6">
        <h2 class="text-xl font-semibold mb-4">Dicas de Segurança</h2>
        
        <ul class="list-disc pl-5 space-y-2 text-gray-600">
          <li>Sempre encerre suas sessões ao usar dispositivos compartilhados ou públicos.</li>
          <li>Verifique regularmente suas sessões ativas para identificar acessos não autorizados.</li>
          <li>Se você notar uma sessão que não reconhece, encerre-a imediatamente e altere sua senha.</li>
          <li>Mantenha seu navegador e sistema operacional atualizados para maior segurança.</li>
          <li>Utilize uma senha forte e única para sua conta.</li>
        </ul>
      </div>
    </div>
  </main>
</MainLayout>
