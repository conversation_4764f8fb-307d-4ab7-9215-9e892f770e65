/**
 * Modelo de dados para sistema de recompensas
 *
 * Este arquivo define as interfaces e tipos para o sistema de recompensas,
 * badges e conquistas utilizadas na plataforma.
 */

/**
 * Tipos de recompensas disponíveis
 */
export enum RewardType {
  /**
   * Badge/insígnia que o usuário pode colecionar
   */
  BADGE = 'badge',

  /**
   * Pontos que podem ser acumulados
   */
  POINTS = 'points',

  /**
   * Nível de experiência
   */
  LEVEL = 'level',

  /**
   * Item virtual (ex: adesivo, avatar, etc)
   */
  VIRTUAL_ITEM = 'virtual_item',

  /**
   * Desbloqueio de conteúdo
   */
  CONTENT_UNLOCK = 'content_unlock',

  /**
   * Certificado de conclusão
   */
  CERTIFICATE = 'certificate',
}

/**
 * Categorias de badges
 */
export enum BadgeCategory {
  /**
   * Conquistas de alfabetização
   */
  LITERACY = 'literacy',

  /**
   * Conquistas de matemática
   */
  MATH = 'math',

  /**
   * Conquistas de ciências
   */
  SCIENCE = 'science',

  /**
   * Conquistas de artes
   */
  ARTS = 'arts',

  /**
   * Conquistas de geografia
   */
  GEOGRAPHY = 'geography',

  /**
   * Conquistas de história
   */
  HISTORY = 'history',

  /**
   * Conquistas de participação
   */
  PARTICIPATION = 'participation',

  /**
   * Conquistas de assiduidade
   */
  ATTENDANCE = 'attendance',

  /**
   * Conquistas especiais
   */
  SPECIAL = 'special',
}

/**
 * Níveis de raridade de badges
 */
export enum BadgeRarity {
  /**
   * Comum (fácil de obter)
   */
  COMMON = 'common',

  /**
   * Incomum (moderadamente difícil)
   */
  UNCOMMON = 'uncommon',

  /**
   * Raro (difícil de obter)
   */
  RARE = 'rare',

  /**
   * Épico (muito difícil de obter)
   */
  EPIC = 'epic',

  /**
   * Lendário (extremamente difícil de obter)
   */
  LEGENDARY = 'legendary',
}

/**
 * Interface para definição de recompensa
 */
export interface Reward {
  /**
   * ID único da recompensa
   */
  id: string;

  /**
   * Título da recompensa
   */
  title: string;

  /**
   * Descrição da recompensa
   */
  description: string;

  /**
   * Tipo de recompensa
   */
  type: RewardType;

  /**
   * URL da imagem da recompensa
   */
  imageUrl?: string;

  /**
   * Valor da recompensa (ex: quantidade de pontos)
   */
  value?: number;

  /**
   * Categoria da recompensa (para badges)
   */
  category?: BadgeCategory;

  /**
   * Raridade da recompensa (para badges)
   */
  rarity?: BadgeRarity;

  /**
   * Condições para obter a recompensa
   */
  conditions: RewardCondition[];

  /**
   * Se a recompensa está ativa
   */
  isActive: boolean;

  /**
   * Data de criação
   */
  createdAt: Date;

  /**
   * Data de atualização
   */
  updatedAt?: Date;

  /**
   * Metadados adicionais
   */
  metadata?: Record<string, any>;
}

/**
 * Tipos de condições para obter recompensas
 */
export enum ConditionType {
  /**
   * Completar um número específico de atividades
   */
  COMPLETE_ACTIVITIES = 'complete_activities',

  /**
   * Obter uma pontuação mínima em atividades
   */
  ACHIEVE_SCORE = 'achieve_score',

  /**
   * Completar atividades em sequência
   */
  COMPLETE_STREAK = 'complete_streak',

  /**
   * Completar atividades em um tempo específico
   */
  COMPLETE_TIMED = 'complete_timed',

  /**
   * Completar todas as atividades de uma categoria
   */
  COMPLETE_CATEGORY = 'complete_category',

  /**
   * Acessar a plataforma por um número de dias
   */
  LOGIN_DAYS = 'login_days',

  /**
   * Completar um perfil
   */
  COMPLETE_PROFILE = 'complete_profile',

  /**
   * Condição personalizada
   */
  CUSTOM = 'custom',
}

/**
 * Interface para condição de recompensa
 */
export interface RewardCondition {
  /**
   * Tipo de condição
   */
  type: ConditionType;

  /**
   * Parâmetros da condição
   */
  params: Record<string, any>;

  /**
   * Descrição da condição para o usuário
   */
  description: string;
}

/**
 * Interface para recompensa concedida a um usuário
 */
export interface UserReward {
  /**
   * ID único da concessão
   */
  id: string;

  /**
   * ID do usuário
   */
  userId: string;

  /**
   * ID da recompensa
   */
  rewardId: string;

  /**
   * Data de concessão
   */
  awardedAt: Date;

  /**
   * Valor concedido (ex: quantidade de pontos)
   */
  value?: number;

  /**
   * Se a recompensa foi visualizada pelo usuário
   */
  viewed: boolean;

  /**
   * Data de visualização
   */
  viewedAt?: Date;

  /**
   * Metadados adicionais
   */
  metadata?: Record<string, any>;
}

/**
 * Interface para progresso do usuário em direção a uma recompensa
 */
export interface RewardProgress {
  /**
   * ID único do progresso
   */
  id: string;

  /**
   * ID do usuário
   */
  userId: string;

  /**
   * ID da recompensa
   */
  rewardId: string;

  /**
   * Progresso atual (0-100)
   */
  progress: number;

  /**
   * Detalhes do progresso por condição
   */
  conditionProgress: Record<string, any>;

  /**
   * Data da última atualização
   */
  updatedAt: Date;
}

/**
 * Interface para nível de usuário
 */
export interface UserLevel {
  /**
   * ID único do nível
   */
  id: string;

  /**
   * ID do usuário
   */
  userId: string;

  /**
   * Nível atual
   */
  level: number;

  /**
   * Pontos de experiência atuais
   */
  currentXp: number;

  /**
   * Pontos necessários para o próximo nível
   */
  nextLevelXp: number;

  /**
   * Total de pontos acumulados
   */
  totalXp: number;

  /**
   * Data da última atualização
   */
  updatedAt: Date;
}
