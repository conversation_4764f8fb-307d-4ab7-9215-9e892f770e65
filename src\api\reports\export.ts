/**
 * API para exportação de relatórios financeiros
 */

import { checkAdminApi } from '@helpers/authGuard';
import {
  type ExportFormat,
  type ReportFilters,
  type ReportType,
  reportService,
} from '@services/reportService';
import type { APIRoute } from 'astro';

export const POST: APIRoute = async ({ request, redirect }) => {
  try {
    // Verificar se o usuário é admin
    const authResult = await checkAdminApi({ request });
    if (authResult) return authResult;

    // Obter parâmetros da requisição
    const body = await request.json();
    const { reportType, format, filters } = body as {
      reportType: ReportType;
      format: ExportFormat;
      filters: ReportFilters;
    };

    // Validar parâmetros
    if (!reportType || !format) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Tipo de relatório e formato são obrigatórios',
        }),
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    // Gerar dados do relatório
    let reportData: any;
    switch (reportType) {
      case 'sales':
        reportData = await reportService.generateSalesReport(filters);
        break;
      case 'revenue':
        reportData = await reportService.generateRevenueReport(filters);
        break;
      case 'transactions':
        reportData = await reportService.generateTransactionsReport(filters);
        break;
      default:
        return new Response(
          JSON.stringify({
            success: false,
            error: 'Tipo de relatório inválido',
          }),
          {
            status: 400,
            headers: {
              'Content-Type': 'application/json',
            },
          }
        );
    }

    // Converter dados para o formato solicitado
    let content: any;
    let contentType: string;
    let filename: string;

    switch (format) {
      case 'csv':
        content = reportService.convertToCSV(reportData);
        contentType = 'text/csv';
        filename = `relatorio-${reportType}-${new Date().toISOString().split('T')[0]}.csv`;
        break;
      case 'json':
        content = reportService.convertToJSON(reportData);
        contentType = 'application/json';
        filename = `relatorio-${reportType}-${new Date().toISOString().split('T')[0]}.json`;
        break;
      case 'pdf':
        content = await reportService.convertToPDF(reportData, reportType, filters);
        contentType = 'application/pdf';
        filename = `relatorio-${reportType}-${new Date().toISOString().split('T')[0]}.pdf`;
        break;
      default:
        return new Response(
          JSON.stringify({
            success: false,
            error: 'Formato de exportação inválido',
          }),
          {
            status: 400,
            headers: {
              'Content-Type': 'application/json',
            },
          }
        );
    }

    // Retornar arquivo para download
    return new Response(content, {
      status: 200,
      headers: {
        'Content-Type': contentType,
        'Content-Disposition': `attachment; filename="${filename}"`,
      },
    });
  } catch (error) {
    console.error('Erro ao exportar relatório:', error);

    return new Response(
      JSON.stringify({
        success: false,
        error: 'Erro ao processar a solicitação',
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  }
};
