/**
 * PostgreSQL Contact Message Repository
 *
 * Implementação do repositório de mensagens de contato usando PostgreSQL.
 * Parte da implementação da tarefa 8.6.1 - Implementação de formulário de contato
 */

import { Pool } from 'pg';
import {
  ContactMessage,
  ContactMessageCategory,
  ContactMessagePriority,
  ContactMessageStatus,
} from '../../../domain/entities/ContactMessage';
import {
  ContactMessageFilter,
  ContactMessagePaginationOptions,
  ContactMessageRepository,
  ContactMessageSortOptions,
  PaginatedContactMessages,
} from '../../../domain/repositories/ContactMessageRepository';
import { getDbConnection } from '../connection';

export class PostgresContactMessageRepository implements ContactMessageRepository {
  private pool: Pool;

  constructor() {
    this.pool = getDbConnection();
  }

  async create(message: ContactMessage): Promise<ContactMessage> {
    const query = `
      INSERT INTO contact_messages (
        id, name, email, phone, subject, message, category, 
        status, priority, assigned_to, tags, attachments, metadata,
        created_at, updated_at, read_at, replied_at
      )
      VALUES (
        $1, $2, $3, $4, $5, $6, $7, 
        $8, $9, $10, $11, $12, $13,
        $14, $15, $16, $17
      )
      RETURNING *
    `;

    const values = [
      message.id,
      message.name,
      message.email,
      message.phone || null,
      message.subject,
      message.message,
      message.category,
      message.status,
      message.priority,
      message.assignedTo || null,
      message.tags ? JSON.stringify(message.tags) : null,
      message.attachments ? JSON.stringify(message.attachments) : null,
      message.metadata ? JSON.stringify(message.metadata) : null,
      message.createdAt,
      message.updatedAt,
      message.readAt || null,
      message.repliedAt || null,
    ];

    const result = await this.pool.query(query, values);

    return this.mapDbMessageToEntity(result.rows[0]);
  }

  async update(message: ContactMessage): Promise<ContactMessage> {
    const query = `
      UPDATE contact_messages
      SET
        name = $2,
        email = $3,
        phone = $4,
        subject = $5,
        message = $6,
        category = $7,
        status = $8,
        priority = $9,
        assigned_to = $10,
        tags = $11,
        attachments = $12,
        metadata = $13,
        updated_at = $14,
        read_at = $15,
        replied_at = $16
      WHERE id = $1
      RETURNING *
    `;

    const values = [
      message.id,
      message.name,
      message.email,
      message.phone || null,
      message.subject,
      message.message,
      message.category,
      message.status,
      message.priority,
      message.assignedTo || null,
      message.tags ? JSON.stringify(message.tags) : null,
      message.attachments ? JSON.stringify(message.attachments) : null,
      message.metadata ? JSON.stringify(message.metadata) : null,
      message.updatedAt,
      message.readAt || null,
      message.repliedAt || null,
    ];

    const result = await this.pool.query(query, values);

    if (result.rows.length === 0) {
      throw new Error(`Mensagem de contato com ID ${message.id} não encontrada`);
    }

    return this.mapDbMessageToEntity(result.rows[0]);
  }

  async getById(id: string): Promise<ContactMessage | null> {
    const query = `
      SELECT * FROM contact_messages
      WHERE id = $1
    `;

    const result = await this.pool.query(query, [id]);

    if (result.rows.length === 0) {
      return null;
    }

    return this.mapDbMessageToEntity(result.rows[0]);
  }

  async find(
    filter: ContactMessageFilter,
    sort?: ContactMessageSortOptions,
    pagination?: ContactMessagePaginationOptions
  ): Promise<PaginatedContactMessages> {
    // Construir a consulta base
    let query = 'SELECT * FROM contact_messages WHERE 1=1';
    let countQuery = 'SELECT COUNT(*) FROM contact_messages WHERE 1=1';
    const values: any[] = [];
    let paramIndex = 1;

    // Adicionar filtros
    if (filter.ids && filter.ids.length > 0) {
      query += ` AND id = ANY($${paramIndex})`;
      countQuery += ` AND id = ANY($${paramIndex})`;
      values.push(filter.ids);
      paramIndex++;
    }

    if (filter.email) {
      query += ` AND email = $${paramIndex}`;
      countQuery += ` AND email = $${paramIndex}`;
      values.push(filter.email);
      paramIndex++;
    }

    if (filter.status) {
      if (Array.isArray(filter.status)) {
        query += ` AND status = ANY($${paramIndex})`;
        countQuery += ` AND status = ANY($${paramIndex})`;
        values.push(filter.status);
      } else {
        query += ` AND status = $${paramIndex}`;
        countQuery += ` AND status = $${paramIndex}`;
        values.push(filter.status);
      }
      paramIndex++;
    }

    if (filter.priority) {
      if (Array.isArray(filter.priority)) {
        query += ` AND priority = ANY($${paramIndex})`;
        countQuery += ` AND priority = ANY($${paramIndex})`;
        values.push(filter.priority);
      } else {
        query += ` AND priority = $${paramIndex}`;
        countQuery += ` AND priority = $${paramIndex}`;
        values.push(filter.priority);
      }
      paramIndex++;
    }

    if (filter.category) {
      if (Array.isArray(filter.category)) {
        query += ` AND category = ANY($${paramIndex})`;
        countQuery += ` AND category = ANY($${paramIndex})`;
        values.push(filter.category);
      } else {
        query += ` AND category = $${paramIndex}`;
        countQuery += ` AND category = $${paramIndex}`;
        values.push(filter.category);
      }
      paramIndex++;
    }

    if (filter.assignedTo) {
      query += ` AND assigned_to = $${paramIndex}`;
      countQuery += ` AND assigned_to = $${paramIndex}`;
      values.push(filter.assignedTo);
      paramIndex++;
    }

    if (filter.tags && filter.tags.length > 0) {
      // Filtrar por tags usando operador de array
      query += ` AND tags @> $${paramIndex}::jsonb`;
      countQuery += ` AND tags @> $${paramIndex}::jsonb`;
      values.push(JSON.stringify(filter.tags));
      paramIndex++;
    }

    if (filter.createdAfter) {
      query += ` AND created_at >= $${paramIndex}`;
      countQuery += ` AND created_at >= $${paramIndex}`;
      values.push(filter.createdAfter);
      paramIndex++;
    }

    if (filter.createdBefore) {
      query += ` AND created_at <= $${paramIndex}`;
      countQuery += ` AND created_at <= $${paramIndex}`;
      values.push(filter.createdBefore);
      paramIndex++;
    }

    if (filter.search) {
      query += ` AND (
        name ILIKE $${paramIndex} OR
        email ILIKE $${paramIndex} OR
        subject ILIKE $${paramIndex} OR
        message ILIKE $${paramIndex}
      )`;
      countQuery += ` AND (
        name ILIKE $${paramIndex} OR
        email ILIKE $${paramIndex} OR
        subject ILIKE $${paramIndex} OR
        message ILIKE $${paramIndex}
      )`;
      values.push(`%${filter.search}%`);
      paramIndex++;
    }

    // Adicionar ordenação
    if (sort) {
      query += ` ORDER BY ${sort.field} ${sort.direction.toUpperCase()}`;
    } else {
      query += ' ORDER BY created_at DESC';
    }

    // Adicionar paginação
    if (pagination) {
      const limit = pagination.limit || 10;
      const offset = ((pagination.page || 1) - 1) * limit;

      query += ` LIMIT $${paramIndex} OFFSET $${paramIndex + 1}`;
      values.push(limit, offset);
      paramIndex += 2;
    }

    // Executar consultas
    const [messagesResult, countResult] = await Promise.all([
      this.pool.query(query, values),
      this.pool.query(countQuery, values.slice(0, values.length - (pagination ? 2 : 0))),
    ]);

    // Mapear resultados
    const messages = messagesResult.rows.map((row) => this.mapDbMessageToEntity(row));
    const total = Number.parseInt(countResult.rows[0].count, 10);

    return {
      messages,
      total,
      page: pagination?.page || 1,
      limit: pagination?.limit || messages.length,
      totalPages: pagination ? Math.ceil(total / pagination.limit) : 1,
    };
  }

  async markAsRead(id: string): Promise<boolean> {
    const query = `
      UPDATE contact_messages
      SET status = 'read', read_at = NOW(), updated_at = NOW()
      WHERE id = $1 AND status = 'pending'
      RETURNING id
    `;

    const result = await this.pool.query(query, [id]);

    return result.rows.length > 0;
  }

  async markAsReplied(id: string): Promise<boolean> {
    const query = `
      UPDATE contact_messages
      SET status = 'replied', replied_at = NOW(), updated_at = NOW()
      WHERE id = $1
      RETURNING id
    `;

    const result = await this.pool.query(query, [id]);

    return result.rows.length > 0;
  }

  async archive(id: string): Promise<boolean> {
    const query = `
      UPDATE contact_messages
      SET status = 'archived', updated_at = NOW()
      WHERE id = $1
      RETURNING id
    `;

    const result = await this.pool.query(query, [id]);

    return result.rows.length > 0;
  }

  async markAsSpam(id: string): Promise<boolean> {
    const query = `
      UPDATE contact_messages
      SET status = 'spam', updated_at = NOW()
      WHERE id = $1
      RETURNING id
    `;

    const result = await this.pool.query(query, [id]);

    return result.rows.length > 0;
  }

  async assignTo(id: string, userId: string): Promise<boolean> {
    const query = `
      UPDATE contact_messages
      SET assigned_to = $2, updated_at = NOW()
      WHERE id = $1
      RETURNING id
    `;

    const result = await this.pool.query(query, [id, userId]);

    return result.rows.length > 0;
  }

  async setPriority(id: string, priority: ContactMessagePriority): Promise<boolean> {
    const query = `
      UPDATE contact_messages
      SET priority = $2, updated_at = NOW()
      WHERE id = $1
      RETURNING id
    `;

    const result = await this.pool.query(query, [id, priority]);

    return result.rows.length > 0;
  }

  async addTag(id: string, tag: string): Promise<boolean> {
    const query = `
      UPDATE contact_messages
      SET tags = CASE
        WHEN tags IS NULL THEN jsonb_build_array($2)
        WHEN NOT tags @> jsonb_build_array($2) THEN tags || jsonb_build_array($2)
        ELSE tags
      END,
      updated_at = NOW()
      WHERE id = $1
      RETURNING id
    `;

    const result = await this.pool.query(query, [id, tag]);

    return result.rows.length > 0;
  }

  async removeTag(id: string, tag: string): Promise<boolean> {
    const query = `
      UPDATE contact_messages
      SET tags = (
        SELECT jsonb_agg(t) FROM (
          SELECT jsonb_array_elements(tags) AS t
          WHERE t <> $2::jsonb
        ) subquery
      ),
      updated_at = NOW()
      WHERE id = $1 AND tags @> jsonb_build_array($2)
      RETURNING id
    `;

    const result = await this.pool.query(query, [id, tag]);

    return result.rows.length > 0;
  }

  async delete(id: string): Promise<boolean> {
    const query = `
      DELETE FROM contact_messages
      WHERE id = $1
      RETURNING id
    `;

    const result = await this.pool.query(query, [id]);

    return result.rows.length > 0;
  }

  async getStats(): Promise<{
    total: number;
    pending: number;
    read: number;
    replied: number;
    archived: number;
    spam: number;
    avgResponseTime: number;
    categoryDistribution: Record<ContactMessageCategory, number>;
  }> {
    const query = `
      SELECT * FROM contact_message_stats
      ORDER BY updated_at DESC
      LIMIT 1
    `;

    const result = await this.pool.query(query);

    if (result.rows.length === 0) {
      return {
        total: 0,
        pending: 0,
        read: 0,
        replied: 0,
        archived: 0,
        spam: 0,
        avgResponseTime: 0,
        categoryDistribution: {} as Record<ContactMessageCategory, number>,
      };
    }

    const stats = result.rows[0];

    return {
      total: stats.total_messages,
      pending: stats.pending_messages,
      read: stats.read_messages,
      replied: stats.replied_messages,
      archived: stats.archived_messages,
      spam: stats.spam_messages,
      avgResponseTime: stats.avg_response_time_hours,
      categoryDistribution: stats.category_distribution,
    };
  }

  async getPendingMessages(
    pagination?: ContactMessagePaginationOptions
  ): Promise<PaginatedContactMessages> {
    return this.find({ status: 'pending' }, { field: 'createdAt', direction: 'desc' }, pagination);
  }

  async getUnassignedMessages(
    pagination?: ContactMessagePaginationOptions
  ): Promise<PaginatedContactMessages> {
    const filter: ContactMessageFilter = {
      status: ['pending', 'read'],
    };

    // Construir a consulta base
    let query = `
      SELECT * FROM contact_messages
      WHERE (status = 'pending' OR status = 'read')
      AND assigned_to IS NULL
    `;

    const countQuery = `
      SELECT COUNT(*) FROM contact_messages
      WHERE (status = 'pending' OR status = 'read')
      AND assigned_to IS NULL
    `;

    const values: any[] = [];
    let paramIndex = 1;

    // Adicionar ordenação
    query += ' ORDER BY created_at DESC';

    // Adicionar paginação
    if (pagination) {
      const limit = pagination.limit || 10;
      const offset = ((pagination.page || 1) - 1) * limit;

      query += ` LIMIT $${paramIndex} OFFSET $${paramIndex + 1}`;
      values.push(limit, offset);
      paramIndex += 2;
    }

    // Executar consultas
    const [messagesResult, countResult] = await Promise.all([
      this.pool.query(query, values),
      this.pool.query(countQuery),
    ]);

    // Mapear resultados
    const messages = messagesResult.rows.map((row) => this.mapDbMessageToEntity(row));
    const total = Number.parseInt(countResult.rows[0].count, 10);

    return {
      messages,
      total,
      page: pagination?.page || 1,
      limit: pagination?.limit || messages.length,
      totalPages: pagination ? Math.ceil(total / pagination.limit) : 1,
    };
  }

  async getAssignedMessages(
    userId: string,
    pagination?: ContactMessagePaginationOptions
  ): Promise<PaginatedContactMessages> {
    return this.find({ assignedTo: userId }, { field: 'createdAt', direction: 'desc' }, pagination);
  }

  async getHighPriorityMessages(
    pagination?: ContactMessagePaginationOptions
  ): Promise<PaginatedContactMessages> {
    return this.find(
      { priority: ['high', 'urgent'] },
      { field: 'createdAt', direction: 'desc' },
      pagination
    );
  }

  private mapDbMessageToEntity(dbMessage: any): ContactMessage {
    return new ContactMessage({
      id: dbMessage.id,
      name: dbMessage.name,
      email: dbMessage.email,
      phone: dbMessage.phone,
      subject: dbMessage.subject,
      message: dbMessage.message,
      category: dbMessage.category as ContactMessageCategory,
      status: dbMessage.status as ContactMessageStatus,
      priority: dbMessage.priority as ContactMessagePriority,
      assignedTo: dbMessage.assigned_to,
      tags: dbMessage.tags ? dbMessage.tags : undefined,
      attachments: dbMessage.attachments ? dbMessage.attachments : undefined,
      metadata: dbMessage.metadata ? dbMessage.metadata : undefined,
      createdAt: dbMessage.created_at,
      updatedAt: dbMessage.updated_at,
      readAt: dbMessage.read_at,
      repliedAt: dbMessage.replied_at,
    });
  }
}
