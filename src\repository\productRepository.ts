import type { QueryResult } from 'pg';
import { pg<PERSON><PERSON>per } from './pgHelper';

async function create(
  ulid_category: string,
  name: string,
  description: string,
  file: Buffer,
  price: number
): Promise<QueryResult> {
  const result = await read(name);
  if (result.rows.length > 0) {
    return result;
  }

  return pgHelper.query(
    `INSERT INTO tab_product (
       ulid_product, 
       ulid_category, 
       name, 
       description, 
       file, 
       price) 
     VALUES ($1, $2, $3, $4, $5, $6) 
     RETURNING *`,
    [pgHelper.generateULID(), ulid_category, name, description, file, price]
  );
}

async function read(
  ulid_product?: string,
  ulid_category?: string,
  name?: string,
  active = true
): Promise<QueryResult> {
  return pgHelper.query(
    `SELECT * 
       FROM tab_product 
      WHERE active = $1
        ${ulid_product ? 'AND ulid_product = $2' : ''}
        ${ulid_category ? 'AND ulid_category = $3' : ''}
        ${name ? 'AND name ILIKE "%$4%"' : ''}
     ORDER BY created_at DESC`,
    [active, ulid_product, ulid_category, name]
  );
}

async function update(
  ulid_product: string,
  ulid_category: string,
  active: boolean,
  name: string,
  description: string,
  file: Buffer,
  price: number
): Promise<QueryResult> {
  return pgHelper.query(
    `UPDATE tab_product 
        SET ulid_category = $1, 
            active        = $2, 
            name          = $3, 
            description   = $4, 
            file          = $5, 
            price         = $6, 
            updated_at    = CURRENT_TIMESTAMP 
      WHERE ulid_product = $7 
     RETURNING *`,
    [ulid_category, active, name, description, file, price, ulid_product]
  );
}

async function deleteByUlid(ulid_product: string): Promise<QueryResult> {
  return await pgHelper.query(
    `DELETE FROM tab_product 
      WHERE ulid_product = $1 
     RETURNING *`,
    [ulid_product]
  );
}

async function inactivate(ulid_product: string): Promise<QueryResult> {
  return await pgHelper.query(
    `UPDATE tab_product 
        SET active = false, 
            updated_at = CURRENT_TIMESTAMP 
      WHERE ulid_product = $1 
     RETURNING *`,
    [ulid_product]
  );
}

export const productRepository = {
  create,
  read,
  update,
  deleteByUlid,
  inactivate,
};
