import { isAuthenticated } from '@middleware/authMiddleware';
import { refundService } from '@services/refundService';
import { logger } from '@utils/logger';
// src/pages/api/payments/[ulid_payment]/refund.ts
import type { APIRoute } from 'astro';

/**
 * Endpoint para solicitar reembolso de um pagamento
 */
export const POST: APIRoute = async ({ request, params, cookies }) => {
  try {
    // Verificar autenticação
    const authResult = await isAuthenticated({ request, cookies } as any);

    if (!authResult.isAuthenticated) {
      return new Response(JSON.stringify({ error: 'Não autorizado' }), {
        status: 401,
        headers: {
          'Content-Type': 'application/json',
        },
      });
    }

    // Obter ID do pagamento
    const paymentId = params.ulid_payment;

    if (!paymentId) {
      return new Response(JSON.stringify({ error: 'ID do pagamento não fornecido' }), {
        status: 400,
        headers: {
          'Content-Type': 'application/json',
        },
      });
    }

    // Obter dados da requisição
    const body = await request.json();

    // Validar dados da requisição
    if (!body.reason) {
      return new Response(JSON.stringify({ error: 'Motivo do reembolso é obrigatório' }), {
        status: 400,
        headers: {
          'Content-Type': 'application/json',
        },
      });
    }

    // Verificar se o valor é válido (se fornecido)
    if (body.value !== undefined && (Number.isNaN(body.value) || body.value <= 0)) {
      return new Response(JSON.stringify({ error: 'Valor de reembolso inválido' }), {
        status: 400,
        headers: {
          'Content-Type': 'application/json',
        },
      });
    }

    // Solicitar reembolso
    const result = await refundService.requestPixRefund({
      paymentId,
      value: body.value,
      reason: body.reason,
      requestedBy: authResult.user.ulid_user,
    });

    // Registrar solicitação de reembolso
    logger.info('Reembolso solicitado:', {
      paymentId,
      refundId: result.refundId,
      value: result.value,
      requestedBy: authResult.user.ulid_user,
    });

    // Retornar resposta de sucesso
    return new Response(JSON.stringify({ success: true, data: result }), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  } catch (error) {
    // Registrar erro
    logger.error('Erro ao solicitar reembolso:', error);

    // Determinar código de status com base no erro
    let statusCode = 500;
    let errorMessage = 'Erro interno';

    if (error instanceof Error) {
      errorMessage = error.message;

      if (error.message.includes('não encontrado')) {
        statusCode = 404;
      } else if (
        error.message.includes('não suportado') ||
        error.message.includes('não permite') ||
        error.message.includes('inválido')
      ) {
        statusCode = 400;
      }
    }

    // Retornar resposta de erro
    return new Response(JSON.stringify({ error: errorMessage }), {
      status: statusCode,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  }
};

/**
 * Endpoint para listar reembolsos de um pagamento
 */
export const GET: APIRoute = async ({ params, request, cookies }) => {
  try {
    // Verificar autenticação
    const authResult = await isAuthenticated({ request, cookies } as any);

    if (!authResult.isAuthenticated) {
      return new Response(JSON.stringify({ error: 'Não autorizado' }), {
        status: 401,
        headers: {
          'Content-Type': 'application/json',
        },
      });
    }

    // Obter ID do pagamento
    const paymentId = params.ulid_payment;

    if (!paymentId) {
      return new Response(JSON.stringify({ error: 'ID do pagamento não fornecido' }), {
        status: 400,
        headers: {
          'Content-Type': 'application/json',
        },
      });
    }

    // Listar reembolsos
    const refunds = await refundService.listRefundsByPayment(paymentId);

    // Retornar resposta de sucesso
    return new Response(JSON.stringify({ success: true, data: refunds }), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  } catch (error) {
    // Registrar erro
    logger.error('Erro ao listar reembolsos:', error);

    // Retornar resposta de erro
    return new Response(
      JSON.stringify({
        error: error instanceof Error ? error.message : 'Erro interno',
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  }
};
