/**
 * Configuração de alertas para o Kafka
 * 
 * Este arquivo contém as configurações de alertas para o Kafka,
 * incluindo limiares, canais de notificação e regras de alerta.
 */

// Tipos de alertas
export enum KafkaAlertType {
  BROKER_DOWN = 'broker_down',
  CONSUMER_LAG = 'consumer_lag',
  DISK_SPACE = 'disk_space',
  REPLICATION_FAILURE = 'replication_failure',
  PARTITION_REASSIGNMENT = 'partition_reassignment',
  TOPIC_UNAVAILABLE = 'topic_unavailable',
  HIGH_CPU_USAGE = 'high_cpu_usage',
  HIGH_MEMORY_USAGE = 'high_memory_usage',
  UNDER_REPLICATED_PARTITIONS = 'under_replicated_partitions',
  OFFLINE_PARTITIONS = 'offline_partitions',
  PRODUCER_ERROR_RATE = 'producer_error_rate',
  CONSUMER_ERROR_RATE = 'consumer_error_rate',
}

// Níveis de severidade
export enum KafkaAlertSeverity {
  INFO = 'info',
  WARNING = 'warning',
  ERROR = 'error',
  CRITICAL = 'critical',
}

// Canais de notificação
export enum KafkaAlertChannel {
  EMAIL = 'email',
  SLACK = 'slack',
  SMS = 'sms',
  WEBHOOK = 'webhook',
  DASHBOARD = 'dashboard',
  LOG = 'log',
}

// Interface para configuração de alerta
export interface KafkaAlertConfig {
  type: KafkaAlertType;
  enabled: boolean;
  severity: KafkaAlertSeverity;
  thresholds: {
    warning?: number;
    error?: number;
    critical?: number;
  };
  channels: KafkaAlertChannel[];
  cooldown: number; // em segundos
  description: string;
  additionalConfig?: Record<string, any>;
}

// Interface para configuração de canais de notificação
export interface KafkaAlertChannelConfig {
  type: KafkaAlertChannel;
  enabled: boolean;
  config: Record<string, any>;
}

// Configuração padrão de alertas
const defaultAlertConfigs: Record<KafkaAlertType, KafkaAlertConfig> = {
  [KafkaAlertType.BROKER_DOWN]: {
    type: KafkaAlertType.BROKER_DOWN,
    enabled: true,
    severity: KafkaAlertSeverity.CRITICAL,
    thresholds: {},
    channels: [
      KafkaAlertChannel.EMAIL,
      KafkaAlertChannel.SLACK,
      KafkaAlertChannel.DASHBOARD,
      KafkaAlertChannel.LOG,
    ],
    cooldown: 300, // 5 minutos
    description: 'Alerta quando um broker Kafka fica indisponível',
  },
  [KafkaAlertType.CONSUMER_LAG]: {
    type: KafkaAlertType.CONSUMER_LAG,
    enabled: true,
    severity: KafkaAlertSeverity.WARNING,
    thresholds: {
      warning: 1000,
      error: 10000,
      critical: 100000,
    },
    channels: [
      KafkaAlertChannel.DASHBOARD,
      KafkaAlertChannel.LOG,
    ],
    cooldown: 600, // 10 minutos
    description: 'Alerta quando o lag de um consumidor excede o limite configurado',
    additionalConfig: {
      checkInterval: 60, // em segundos
      excludedGroups: [],
      criticalGroups: ['payment-processor', 'order-processor'],
    },
  },
  [KafkaAlertType.DISK_SPACE]: {
    type: KafkaAlertType.DISK_SPACE,
    enabled: true,
    severity: KafkaAlertSeverity.ERROR,
    thresholds: {
      warning: 80, // percentual de uso
      error: 90,
      critical: 95,
    },
    channels: [
      KafkaAlertChannel.EMAIL,
      KafkaAlertChannel.SLACK,
      KafkaAlertChannel.DASHBOARD,
      KafkaAlertChannel.LOG,
    ],
    cooldown: 1800, // 30 minutos
    description: 'Alerta quando o espaço em disco está ficando baixo',
    additionalConfig: {
      checkInterval: 300, // em segundos
    },
  },
  [KafkaAlertType.REPLICATION_FAILURE]: {
    type: KafkaAlertType.REPLICATION_FAILURE,
    enabled: true,
    severity: KafkaAlertSeverity.ERROR,
    thresholds: {},
    channels: [
      KafkaAlertChannel.EMAIL,
      KafkaAlertChannel.SLACK,
      KafkaAlertChannel.DASHBOARD,
      KafkaAlertChannel.LOG,
    ],
    cooldown: 300, // 5 minutos
    description: 'Alerta quando ocorre falha na replicação de partições',
  },
  [KafkaAlertType.PARTITION_REASSIGNMENT]: {
    type: KafkaAlertType.PARTITION_REASSIGNMENT,
    enabled: true,
    severity: KafkaAlertSeverity.INFO,
    thresholds: {},
    channels: [
      KafkaAlertChannel.DASHBOARD,
      KafkaAlertChannel.LOG,
    ],
    cooldown: 60, // 1 minuto
    description: 'Alerta quando ocorre reatribuição de partições',
  },
  [KafkaAlertType.TOPIC_UNAVAILABLE]: {
    type: KafkaAlertType.TOPIC_UNAVAILABLE,
    enabled: true,
    severity: KafkaAlertSeverity.CRITICAL,
    thresholds: {},
    channels: [
      KafkaAlertChannel.EMAIL,
      KafkaAlertChannel.SLACK,
      KafkaAlertChannel.DASHBOARD,
      KafkaAlertChannel.LOG,
    ],
    cooldown: 300, // 5 minutos
    description: 'Alerta quando um tópico fica indisponível',
    additionalConfig: {
      criticalTopics: [
        'payment.transaction.created',
        'payment.transaction.updated',
        'order.created',
        'order.status.changed',
      ],
    },
  },
  [KafkaAlertType.HIGH_CPU_USAGE]: {
    type: KafkaAlertType.HIGH_CPU_USAGE,
    enabled: true,
    severity: KafkaAlertSeverity.WARNING,
    thresholds: {
      warning: 70, // percentual de uso
      error: 85,
      critical: 95,
    },
    channels: [
      KafkaAlertChannel.DASHBOARD,
      KafkaAlertChannel.LOG,
    ],
    cooldown: 600, // 10 minutos
    description: 'Alerta quando o uso de CPU está alto',
    additionalConfig: {
      checkInterval: 60, // em segundos
      duration: 300, // duração em segundos para considerar uso alto
    },
  },
  [KafkaAlertType.HIGH_MEMORY_USAGE]: {
    type: KafkaAlertType.HIGH_MEMORY_USAGE,
    enabled: true,
    severity: KafkaAlertSeverity.WARNING,
    thresholds: {
      warning: 70, // percentual de uso
      error: 85,
      critical: 95,
    },
    channels: [
      KafkaAlertChannel.DASHBOARD,
      KafkaAlertChannel.LOG,
    ],
    cooldown: 600, // 10 minutos
    description: 'Alerta quando o uso de memória está alto',
    additionalConfig: {
      checkInterval: 60, // em segundos
      duration: 300, // duração em segundos para considerar uso alto
    },
  },
  [KafkaAlertType.UNDER_REPLICATED_PARTITIONS]: {
    type: KafkaAlertType.UNDER_REPLICATED_PARTITIONS,
    enabled: true,
    severity: KafkaAlertSeverity.ERROR,
    thresholds: {
      warning: 1,
      error: 5,
      critical: 20,
    },
    channels: [
      KafkaAlertChannel.EMAIL,
      KafkaAlertChannel.SLACK,
      KafkaAlertChannel.DASHBOARD,
      KafkaAlertChannel.LOG,
    ],
    cooldown: 300, // 5 minutos
    description: 'Alerta quando existem partições sub-replicadas',
  },
  [KafkaAlertType.OFFLINE_PARTITIONS]: {
    type: KafkaAlertType.OFFLINE_PARTITIONS,
    enabled: true,
    severity: KafkaAlertSeverity.CRITICAL,
    thresholds: {
      warning: 1,
      error: 1,
      critical: 1,
    },
    channels: [
      KafkaAlertChannel.EMAIL,
      KafkaAlertChannel.SLACK,
      KafkaAlertChannel.DASHBOARD,
      KafkaAlertChannel.LOG,
    ],
    cooldown: 300, // 5 minutos
    description: 'Alerta quando existem partições offline',
  },
  [KafkaAlertType.PRODUCER_ERROR_RATE]: {
    type: KafkaAlertType.PRODUCER_ERROR_RATE,
    enabled: true,
    severity: KafkaAlertSeverity.WARNING,
    thresholds: {
      warning: 0.01, // 1% de erros
      error: 0.05, // 5% de erros
      critical: 0.10, // 10% de erros
    },
    channels: [
      KafkaAlertChannel.DASHBOARD,
      KafkaAlertChannel.LOG,
    ],
    cooldown: 300, // 5 minutos
    description: 'Alerta quando a taxa de erros de produtores excede o limite',
    additionalConfig: {
      windowSize: 60, // em segundos
      minSampleSize: 100, // número mínimo de mensagens para considerar
    },
  },
  [KafkaAlertType.CONSUMER_ERROR_RATE]: {
    type: KafkaAlertType.CONSUMER_ERROR_RATE,
    enabled: true,
    severity: KafkaAlertSeverity.WARNING,
    thresholds: {
      warning: 0.01, // 1% de erros
      error: 0.05, // 5% de erros
      critical: 0.10, // 10% de erros
    },
    channels: [
      KafkaAlertChannel.DASHBOARD,
      KafkaAlertChannel.LOG,
    ],
    cooldown: 300, // 5 minutos
    description: 'Alerta quando a taxa de erros de consumidores excede o limite',
    additionalConfig: {
      windowSize: 60, // em segundos
      minSampleSize: 100, // número mínimo de mensagens para considerar
    },
  },
};

// Configuração de canais de notificação
const alertChannelConfigs: Record<KafkaAlertChannel, KafkaAlertChannelConfig> = {
  [KafkaAlertChannel.EMAIL]: {
    type: KafkaAlertChannel.EMAIL,
    enabled: true,
    config: {
      recipients: process.env.KAFKA_ALERT_EMAIL_RECIPIENTS?.split(',') || ['<EMAIL>'],
      from: process.env.KAFKA_ALERT_EMAIL_FROM || '<EMAIL>',
      subject: '[Kafka Alert] {severity}: {type}',
      includeDetails: true,
    },
  },
  [KafkaAlertChannel.SLACK]: {
    type: KafkaAlertChannel.SLACK,
    enabled: process.env.KAFKA_ALERT_SLACK_ENABLED === 'true',
    config: {
      webhookUrl: process.env.KAFKA_ALERT_SLACK_WEBHOOK_URL || '',
      channel: process.env.KAFKA_ALERT_SLACK_CHANNEL || '#kafka-alerts',
      username: process.env.KAFKA_ALERT_SLACK_USERNAME || 'Kafka Monitor',
      iconEmoji: process.env.KAFKA_ALERT_SLACK_ICON_EMOJI || ':warning:',
    },
  },
  [KafkaAlertChannel.SMS]: {
    type: KafkaAlertChannel.SMS,
    enabled: process.env.KAFKA_ALERT_SMS_ENABLED === 'true',
    config: {
      phoneNumbers: process.env.KAFKA_ALERT_SMS_NUMBERS?.split(',') || [],
      provider: process.env.KAFKA_ALERT_SMS_PROVIDER || 'twilio',
      onlyCritical: true,
    },
  },
  [KafkaAlertChannel.WEBHOOK]: {
    type: KafkaAlertChannel.WEBHOOK,
    enabled: process.env.KAFKA_ALERT_WEBHOOK_ENABLED === 'true',
    config: {
      url: process.env.KAFKA_ALERT_WEBHOOK_URL || '',
      method: process.env.KAFKA_ALERT_WEBHOOK_METHOD || 'POST',
      headers: {},
      includeDetails: true,
    },
  },
  [KafkaAlertChannel.DASHBOARD]: {
    type: KafkaAlertChannel.DASHBOARD,
    enabled: true,
    config: {
      maxAlerts: 100,
      autoRefresh: true,
      refreshInterval: 30, // em segundos
    },
  },
  [KafkaAlertChannel.LOG]: {
    type: KafkaAlertChannel.LOG,
    enabled: true,
    config: {
      logLevel: 'warn',
      includeDetails: true,
    },
  },
};

// Carregar configuração do ambiente
function loadConfigFromEnv(): Partial<Record<KafkaAlertType, Partial<KafkaAlertConfig>>> {
  const config: Partial<Record<KafkaAlertType, Partial<KafkaAlertConfig>>> = {};
  
  // Carregar configurações para cada tipo de alerta
  Object.values(KafkaAlertType).forEach(alertType => {
    const envPrefix = `KAFKA_ALERT_${alertType.toUpperCase()}`;
    
    const enabled = process.env[`${envPrefix}_ENABLED`];
    const severity = process.env[`${envPrefix}_SEVERITY`] as KafkaAlertSeverity;
    const warningThreshold = process.env[`${envPrefix}_WARNING_THRESHOLD`];
    const errorThreshold = process.env[`${envPrefix}_ERROR_THRESHOLD`];
    const criticalThreshold = process.env[`${envPrefix}_CRITICAL_THRESHOLD`];
    const channels = process.env[`${envPrefix}_CHANNELS`];
    const cooldown = process.env[`${envPrefix}_COOLDOWN`];
    
    if (enabled || severity || warningThreshold || errorThreshold || criticalThreshold || channels || cooldown) {
      config[alertType] = {};
      
      if (enabled !== undefined) {
        config[alertType]!.enabled = enabled === 'true';
      }
      
      if (severity) {
        config[alertType]!.severity = severity;
      }
      
      if (warningThreshold || errorThreshold || criticalThreshold) {
        config[alertType]!.thresholds = {};
        
        if (warningThreshold) {
          config[alertType]!.thresholds!.warning = parseFloat(warningThreshold);
        }
        
        if (errorThreshold) {
          config[alertType]!.thresholds!.error = parseFloat(errorThreshold);
        }
        
        if (criticalThreshold) {
          config[alertType]!.thresholds!.critical = parseFloat(criticalThreshold);
        }
      }
      
      if (channels) {
        config[alertType]!.channels = channels.split(',') as KafkaAlertChannel[];
      }
      
      if (cooldown) {
        config[alertType]!.cooldown = parseInt(cooldown, 10);
      }
    }
  });
  
  return config;
}

// Mesclar configuração padrão com configuração do ambiente
const envConfig = loadConfigFromEnv();
const kafkaAlertConfigs: Record<KafkaAlertType, KafkaAlertConfig> = { ...defaultAlertConfigs };

// Aplicar configurações do ambiente
Object.entries(envConfig).forEach(([alertType, config]) => {
  if (config) {
    kafkaAlertConfigs[alertType as KafkaAlertType] = {
      ...kafkaAlertConfigs[alertType as KafkaAlertType],
      ...config,
    };
  }
});

// Exportar configurações
export {
  kafkaAlertConfigs,
  alertChannelConfigs,
};
