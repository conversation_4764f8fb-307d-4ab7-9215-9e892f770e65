/**
 * Cache client using Valkey
 * Provides a centralized interface for caching operations
 */
import { createClient } from '@valkey/client';
import type { RedisClientType } from '@valkey/client';

// Cache configuration
const config = {
  url: process.env.REDIS_URL || 'redis://localhost:6379',
  connectTimeout: 10000,
  reconnectStrategy: (retries: number) => Math.min(retries * 50, 1000)
};

// Cache client singleton
let client: RedisClientType | null = null;

/**
 * Initialize the cache client
 * @returns Redis client instance
 */
async function initializeClient(): Promise<RedisClientType> {
  if (client) return client;

  try {
    client = createClient(config);

    client.on('error', (err) => {
      console.error('Redis client error:', err);
    });

    client.on('reconnecting', () => {
      console.log('Redis client reconnecting...');
    });

    await client.connect();
    console.log('Redis client connected');

    return client;
  } catch (error) {
    console.error('Failed to initialize Redis client:', error);
    throw error;
  }
}

/**
 * Get the cache client
 * @returns Redis client instance
 */
export async function getCacheClient(): Promise<RedisClientType> {
  if (!client) {
    return await initializeClient();
  }

  return client;
}

/**
 * Set a value in the cache
 * @param key Cache key
 * @param value Value to cache
 * @param ttl Time to live in seconds (optional)
 */
export async function setCache(key: string, value: any, ttl?: number): Promise<void> {
  try {
    const cacheClient = await getCacheClient();
    const serializedValue = JSON.stringify(value);

    if (ttl) {
      await cacheClient.set(key, serializedValue, { EX: ttl });
    } else {
      await cacheClient.set(key, serializedValue);
    }
  } catch (error) {
    console.error(`Error setting cache for key ${key}:`, error);
    // Fail gracefully - don't throw error for cache operations
  }
}

/**
 * Get a value from the cache
 * @param key Cache key
 * @returns Cached value or null if not found
 */
export async function getCache<T>(key: string): Promise<T | null> {
  try {
    const cacheClient = await getCacheClient();
    const value = await cacheClient.get(key);

    if (!value) return null;

    return JSON.parse(value) as T;
  } catch (error) {
    console.error(`Error getting cache for key ${key}:`, error);
    return null;
  }
}

/**
 * Delete a value from the cache
 * @param key Cache key
 */
export async function deleteCache(key: string): Promise<void> {
  try {
    const cacheClient = await getCacheClient();
    await cacheClient.del(key);
  } catch (error) {
    console.error(`Error deleting cache for key ${key}:`, error);
  }
}

/**
 * Delete multiple values from the cache using a pattern
 * @param pattern Key pattern to match
 */
export async function deleteCacheByPattern(pattern: string): Promise<void> {
  try {
    const cacheClient = await getCacheClient();
    const keys = await cacheClient.keys(pattern);

    if (keys.length > 0) {
      await cacheClient.del(keys);
      console.log(`Deleted ${keys.length} cache entries matching pattern: ${pattern}`);
    }
  } catch (error) {
    console.error(`Error deleting cache by pattern ${pattern}:`, error);
  }
}

/**
 * Check if a key exists in the cache
 * @param key Cache key
 * @returns True if the key exists, false otherwise
 */
export async function hasCache(key: string): Promise<boolean> {
  try {
    const cacheClient = await getCacheClient();
    return await cacheClient.exists(key) > 0;
  } catch (error) {
    console.error(`Error checking cache for key ${key}:`, error);
    return false;
  }
}

/**
 * Set a value in the cache with hash
 * @param key Hash key
 * @param field Hash field
 * @param value Value to cache
 */
export async function setHashCache(key: string, field: string, value: any): Promise<void> {
  try {
    const cacheClient = await getCacheClient();
    const serializedValue = JSON.stringify(value);
    await cacheClient.hSet(key, field, serializedValue);
  } catch (error) {
    console.error(`Error setting hash cache for key ${key}, field ${field}:`, error);
  }
}

/**
 * Get a value from the cache with hash
 * @param key Hash key
 * @param field Hash field
 * @returns Cached value or null if not found
 */
export async function getHashCache<T>(key: string, field: string): Promise<T | null> {
  try {
    const cacheClient = await getCacheClient();
    const value = await cacheClient.hGet(key, field);

    if (!value) return null;

    return JSON.parse(value) as T;
  } catch (error) {
    console.error(`Error getting hash cache for key ${key}, field ${field}:`, error);
    return null;
  }
}

/**
 * Close the cache client connection
 */
export async function closeCacheConnection(): Promise<void> {
  if (client) {
    await client.quit();
    client = null;
  }
}
