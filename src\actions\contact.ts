/**
 * Ações para o formulário de contato
 *
 * Este arquivo implementa as ações do servidor para o formulário de contato,
 * utilizando Astro Actions para processar os dados sem necessidade de API.
 */

import type { APIContext } from 'astro';
import { z } from 'zod';
import { PostgresConnection } from '../infrastructure/database/PostgresConnection';
import { ConsoleLogger } from '../infrastructure/logging/ConsoleLogger';
import { getDatabaseConfig } from '../infrastructure/config/database.config';
import { LogLevel } from '../application/interfaces/services/Logger';
import { sendEmail } from '../infrastructure/services/EmailService';

// Esquema de validação para o formulário de contato
const contactSchema = z.object({
  name: z.string().min(3, { message: 'O nome deve ter pelo menos 3 caracteres' }),
  email: z.string().email({ message: 'Email inválido' }),
  subject: z.string().min(5, { message: 'O assunto deve ter pelo menos 5 caracteres' }),
  message: z.string().min(10, { message: 'A mensagem deve ter pelo menos 10 caracteres' })
});

// Tipo para os dados do formulário
type ContactFormData = z.infer<typeof contactSchema>;

// Tipo para os erros de validação
type ValidationErrors = Partial<Record<keyof ContactFormData, string>>;

/**
 * Ação para processar o formulário de contato
 *
 * @param context Contexto da requisição
 * @returns Resultado do processamento
 */
export async function processContactForm(context: APIContext) {
  // Inicializar logger
  const logger = new ConsoleLogger('ContactAction', {
    level: LogLevel.INFO,
    useColors: true,
    format: 'text'
  });

  // Inicializar conexão com o banco de dados
  const dbConfig = getDatabaseConfig();
  const dbConnection = new PostgresConnection(dbConfig, logger);

  try {
    // Obter dados do formulário
    const formData = await context.request.formData();
    const data = {
      name: formData.get('name')?.toString() || '',
      email: formData.get('email')?.toString() || '',
      subject: formData.get('subject')?.toString() || '',
      message: formData.get('message')?.toString() || ''
    };

    // Validar dados
    const result = contactSchema.safeParse(data);

    // Se houver erros de validação
    if (!result.success) {
      const errors: ValidationErrors = {};

      // Formatar erros
      result.error.issues.forEach(issue => {
        const path = issue.path[0] as keyof ContactFormData;
        errors[path] = issue.message;
      });

      // Retornar erros
      return {
        success: false,
        errors,
        values: data
      };
    }

    // Dados validados
    const validatedData = result.data;

    // Salvar no banco de dados
    await dbConnection.query(
      `INSERT INTO contact_messages (name, email, subject, message, created_at)
       VALUES ($1, $2, $3, $4, NOW())`,
      [validatedData.name, validatedData.email, validatedData.subject, validatedData.message]
    );

    // Enviar email de notificação
    await sendEmail({
      to: '<EMAIL>',
      subject: `Nova mensagem de contato: ${validatedData.subject}`,
      text: `
        Nome: ${validatedData.name}
        Email: ${validatedData.email}
        Assunto: ${validatedData.subject}

        Mensagem:
        ${validatedData.message}
      `
    });

    // Enviar email de confirmação
    await sendEmail({
      to: validatedData.email,
      subject: 'Recebemos sua mensagem - Estação Alfabetização',
      text: `
        Olá ${validatedData.name},

        Recebemos sua mensagem e retornaremos em breve.

        Atenciosamente,
        Equipe Estação Alfabetização
      `
    });

    // Fechar conexão com o banco de dados
    await dbConnection.disconnect();

    // Retornar sucesso
    return {
      success: true,
      message: 'Mensagem enviada com sucesso! Entraremos em contato em breve.'
    };
  } catch (error) {
    // Registrar erro
    logger.error('Erro ao processar formulário de contato', error as Error);

    // Fechar conexão com o banco de dados
    await dbConnection.disconnect();

    // Retornar erro
    return {
      success: false,
      message: 'Ocorreu um erro ao enviar sua mensagem. Por favor, tente novamente mais tarde.'
    };
  }
}
