/**
 * Configuração do AnimeJS - Estação da Alfabetização
 *
 * Este arquivo configura a biblioteca AnimeJS para uso no projeto,
 * fornecendo helpers e utilitários para animações complexas.
 */

// Importar AnimeJS
import anime from 'animejs/lib/anime.es.js';

// Configurações globais
const defaultConfig = {
  easing: 'easeOutExpo',
  duration: 800,
  delay: 0,
};

// Helpers para animações comuns
const animeHelpers = {
  /**
   * Animação de fade in
   * @param {string|Element|NodeList} targets - Elemento(s) alvo
   * @param {Object} options - Opções adicionais
   * @returns {Object} Instância da animação
   */
  fadeIn: (targets, options = {}) => {
    return anime({
      targets,
      opacity: [0, 1],
      duration: options.duration || defaultConfig.duration,
      easing: options.easing || defaultConfig.easing,
      delay: options.delay || defaultConfig.delay,
      ...options,
    });
  },

  /**
   * Animação de fade out
   * @param {string|Element|NodeList} targets - Elemento(s) alvo
   * @param {Object} options - Opções adicionais
   * @returns {Object} Instância da animação
   */
  fadeOut: (targets, options = {}) => {
    return anime({
      targets,
      opacity: [1, 0],
      duration: options.duration || defaultConfig.duration,
      easing: options.easing || defaultConfig.easing,
      delay: options.delay || defaultConfig.delay,
      ...options,
    });
  },

  /**
   * Animação de slide in (de qualquer direção)
   * @param {string|Element|NodeList} targets - Elemento(s) alvo
   * @param {string} direction - Direção ('left', 'right', 'up', 'down')
   * @param {Object} options - Opções adicionais
   * @returns {Object} Instância da animação
   */
  slideIn: (targets, direction = 'left', options = {}) => {
    const distance = options.distance || '100%';
    let translateX = 0;
    let translateY = 0;

    switch (direction) {
      case 'left':
        translateX = [`-${distance}`, 0];
        break;
      case 'right':
        translateX = [distance, 0];
        break;
      case 'up':
        translateY = [`-${distance}`, 0];
        break;
      case 'down':
        translateY = [distance, 0];
        break;
      default:
        translateX = [`-${distance}`, 0];
    }

    return anime({
      targets,
      translateX,
      translateY,
      opacity: [0, 1],
      duration: options.duration || defaultConfig.duration,
      easing: options.easing || defaultConfig.easing,
      delay: options.delay || defaultConfig.delay,
      ...options,
    });
  },

  /**
   * Animação de slide out (em qualquer direção)
   * @param {string|Element|NodeList} targets - Elemento(s) alvo
   * @param {string} direction - Direção ('left', 'right', 'up', 'down')
   * @param {Object} options - Opções adicionais
   * @returns {Object} Instância da animação
   */
  slideOut: (targets, direction = 'left', options = {}) => {
    const distance = options.distance || '100%';
    let translateX = 0;
    let translateY = 0;

    switch (direction) {
      case 'left':
        translateX = [0, `-${distance}`];
        break;
      case 'right':
        translateX = [0, distance];
        break;
      case 'up':
        translateY = [0, `-${distance}`];
        break;
      case 'down':
        translateY = [0, distance];
        break;
      default:
        translateX = [0, `-${distance}`];
    }

    return anime({
      targets,
      translateX,
      translateY,
      opacity: [1, 0],
      duration: options.duration || defaultConfig.duration,
      easing: options.easing || defaultConfig.easing,
      delay: options.delay || defaultConfig.delay,
      ...options,
    });
  },

  /**
   * Animação de scale in
   * @param {string|Element|NodeList} targets - Elemento(s) alvo
   * @param {Object} options - Opções adicionais
   * @returns {Object} Instância da animação
   */
  scaleIn: (targets, options = {}) => {
    return anime({
      targets,
      scale: [0, 1],
      opacity: [0, 1],
      duration: options.duration || defaultConfig.duration,
      easing: options.easing || defaultConfig.easing,
      delay: options.delay || defaultConfig.delay,
      ...options,
    });
  },

  /**
   * Animação de scale out
   * @param {string|Element|NodeList} targets - Elemento(s) alvo
   * @param {Object} options - Opções adicionais
   * @returns {Object} Instância da animação
   */
  scaleOut: (targets, options = {}) => {
    return anime({
      targets,
      scale: [1, 0],
      opacity: [1, 0],
      duration: options.duration || defaultConfig.duration,
      easing: options.easing || defaultConfig.easing,
      delay: options.delay || defaultConfig.delay,
      ...options,
    });
  },

  /**
   * Animação de rotação
   * @param {string|Element|NodeList} targets - Elemento(s) alvo
   * @param {number} angle - Ângulo de rotação em graus
   * @param {Object} options - Opções adicionais
   * @returns {Object} Instância da animação
   */
  rotate: (targets, angle = 360, options = {}) => {
    return anime({
      targets,
      rotate: angle,
      duration: options.duration || defaultConfig.duration,
      easing: options.easing || defaultConfig.easing,
      delay: options.delay || defaultConfig.delay,
      ...options,
    });
  },

  /**
   * Animação de destaque (pulse)
   * @param {string|Element|NodeList} targets - Elemento(s) alvo
   * @param {Object} options - Opções adicionais
   * @returns {Object} Instância da animação
   */
  pulse: (targets, options = {}) => {
    return anime({
      targets,
      scale: [1, 1.05, 1],
      duration: options.duration || 600,
      easing: 'easeInOutQuad',
      delay: options.delay || defaultConfig.delay,
      ...options,
    });
  },

  /**
   * Animação de shake (tremor)
   * @param {string|Element|NodeList} targets - Elemento(s) alvo
   * @param {Object} options - Opções adicionais
   * @returns {Object} Instância da animação
   */
  shake: (targets, options = {}) => {
    return anime({
      targets,
      translateX: [0, -10, 10, -10, 10, 0],
      duration: options.duration || 500,
      easing: 'easeInOutSine',
      delay: options.delay || defaultConfig.delay,
      ...options,
    });
  },

  /**
   * Animação de bounce (quicar)
   * @param {string|Element|NodeList} targets - Elemento(s) alvo
   * @param {Object} options - Opções adicionais
   * @returns {Object} Instância da animação
   */
  bounce: (targets, options = {}) => {
    return anime({
      targets,
      translateY: [0, -30, 0],
      duration: options.duration || 800,
      easing: 'spring(1, 80, 10, 0)',
      delay: options.delay || defaultConfig.delay,
      ...options,
    });
  },

  /**
   * Animação de texto (letra por letra)
   * @param {string|Element|NodeList} targets - Elemento(s) alvo
   * @param {Object} options - Opções adicionais
   * @returns {Object} Instância da animação
   */
  textAnimation: (targets, options = {}) => {
    // Preparar o elemento para animação letra por letra
    const elements = document.querySelectorAll(targets);
    elements.forEach((el) => {
      const text = el.textContent;
      el.innerHTML = '';

      for (let i = 0; i < text.length; i++) {
        const span = document.createElement('span');
        span.textContent = text[i] === ' ' ? '\u00A0' : text[i];
        span.style.opacity = 0;
        span.style.display = 'inline-block';
        el.appendChild(span);
      }
    });

    // Animar cada letra
    return anime({
      targets: `${targets} span`,
      opacity: [0, 1],
      translateY: [20, 0],
      duration: options.duration || 800,
      easing: options.easing || 'easeOutExpo',
      delay: anime.stagger(50, { start: options.delay || 0 }),
      ...options,
    });
  },
};

// Sistema de sequenciamento de animações
class AnimationSequence {
  constructor() {
    this.timeline = anime.timeline({
      easing: defaultConfig.easing,
      duration: defaultConfig.duration,
    });
  }

  /**
   * Adiciona uma animação à sequência
   * @param {Function} animationFn - Função de animação a ser executada
   * @param {number} offset - Deslocamento em relação à animação anterior
   * @returns {AnimationSequence} A própria instância para encadeamento
   */
  add(animationFn, offset = '+=0') {
    this.timeline.add({
      begin: animationFn,
      offset,
    });
    return this;
  }

  /**
   * Inicia a sequência de animações
   * @returns {Object} A timeline do AnimeJS
   */
  play() {
    return this.timeline.play();
  }

  /**
   * Pausa a sequência de animações
   * @returns {Object} A timeline do AnimeJS
   */
  pause() {
    return this.timeline.pause();
  }

  /**
   * Reinicia a sequência de animações
   * @returns {Object} A timeline do AnimeJS
   */
  restart() {
    return this.timeline.restart();
  }
}

// Exportar a biblioteca configurada e os helpers
export { anime, animeHelpers, AnimationSequence };

// Exportar por padrão
export default anime;
