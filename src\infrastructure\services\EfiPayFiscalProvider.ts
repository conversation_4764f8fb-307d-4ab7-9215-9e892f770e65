/**
 * Efí Pay Fiscal Provider Service
 *
 * Implementação do serviço de integração com o provedor fiscal Efí Pay.
 * Parte da implementação da tarefa 8.7.1 - Integração fiscal
 */

import fs from 'node:fs';
import path from 'node:path';
import axios, { AxiosInstance } from 'axios';
import {
  FiscalCustomer,
  FiscalDocument,
  FiscalDocumentType,
  FiscalItem,
} from '../../domain/entities/FiscalDocument';
import {
  FiscalCancelResponse,
  FiscalIssueResponse,
  FiscalProviderConfig,
  FiscalProviderService,
  FiscalStatusResponse,
} from '../../domain/services/FiscalProviderService';

export class EfiPayFiscalProvider implements FiscalProviderService {
  private apiClient: AxiosInstance | null = null;
  private config: FiscalProviderConfig | null = null;
  private certificateBuffer: Buffer | null = null;
  private isInitialized = false;

  /**
   * Inicializa o serviço com as configurações do provedor
   */
  async initialize(config: FiscalProviderConfig): Promise<boolean> {
    try {
      this.config = config;

      // Carregar certificado digital se fornecido
      if (config.certificatePath) {
        this.certificateBuffer = await fs.promises.readFile(path.resolve(config.certificatePath));
      }

      // Configurar cliente HTTP
      const baseURL =
        config.environment === 'production'
          ? 'https://api-pix.efipay.com.br/v1'
          : 'https://api-pix.sandbox.efipay.com.br/v1';

      this.apiClient = axios.create({
        baseURL,
        timeout: config.timeout || 30000,
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${config.apiKey}`,
        },
      });

      // Verificar conexão com a API
      const response = await this.apiClient.get('/status');

      if (response.status === 200) {
        this.isInitialized = true;
        console.log('Efí Pay Fiscal Provider inicializado com sucesso');
        return true;
      }
      throw new Error(`Falha na conexão com a API: ${response.status}`);
    } catch (error) {
      console.error('Erro ao inicializar Efí Pay Fiscal Provider:', error);
      this.isInitialized = false;
      return false;
    }
  }

  /**
   * Verifica se o serviço está configurado e pronto para uso
   */
  isReady(): boolean {
    return this.isInitialized && !!this.apiClient && !!this.config;
  }

  /**
   * Obtém os tipos de documentos suportados pelo provedor
   */
  getSupportedDocumentTypes(): FiscalDocumentType[] {
    return ['NFE', 'NFSE'];
  }

  /**
   * Emite um documento fiscal
   */
  async issueDocument(document: FiscalDocument): Promise<FiscalIssueResponse> {
    if (!this.isReady()) {
      return {
        success: false,
        errorCode: 'PROVIDER_NOT_READY',
        errorMessage: 'Provedor fiscal não está inicializado',
      };
    }

    try {
      // Validar documento antes da emissão
      const validation = await this.validateDocument(document);

      if (!validation.valid) {
        return {
          success: false,
          errorCode: 'VALIDATION_ERROR',
          errorMessage: validation.errors?.join('; ') || 'Erro de validação',
        };
      }

      // Preparar payload para a API
      const payload = this.mapDocumentToApiPayload(document);

      // Chamar API para emissão
      const response = await this.apiClient!.post(
        `/fiscal/${document.type.toLowerCase()}/issue`,
        payload
      );

      if (response.status === 200 && response.data.success) {
        return {
          success: true,
          documentNumber: response.data.number,
          documentSeries: response.data.series,
          issueDate: new Date(response.data.issueDate),
          xmlContent: response.data.xml,
          pdfUrl: response.data.pdfUrl,
          protocolNumber: response.data.protocol,
        };
      }
      return {
        success: false,
        errorCode: response.data.errorCode || 'ISSUE_ERROR',
        errorMessage: response.data.errorMessage || 'Erro ao emitir documento fiscal',
      };
    } catch (error: any) {
      console.error('Erro ao emitir documento fiscal:', error);

      return {
        success: false,
        errorCode: 'API_ERROR',
        errorMessage: error.message || 'Erro na comunicação com a API',
      };
    }
  }

  /**
   * Cancela um documento fiscal
   */
  async cancelDocument(
    documentType: FiscalDocumentType,
    documentNumber: string,
    documentSeries: string,
    reason: string
  ): Promise<FiscalCancelResponse> {
    if (!this.isReady()) {
      return {
        success: false,
        errorCode: 'PROVIDER_NOT_READY',
        errorMessage: 'Provedor fiscal não está inicializado',
      };
    }

    try {
      const response = await this.apiClient!.post(`/fiscal/${documentType.toLowerCase()}/cancel`, {
        number: documentNumber,
        series: documentSeries,
        reason: reason,
      });

      if (response.status === 200 && response.data.success) {
        return {
          success: true,
          cancellationDate: new Date(response.data.cancellationDate),
          protocolNumber: response.data.protocol,
        };
      }
      return {
        success: false,
        errorCode: response.data.errorCode || 'CANCEL_ERROR',
        errorMessage: response.data.errorMessage || 'Erro ao cancelar documento fiscal',
      };
    } catch (error: any) {
      console.error('Erro ao cancelar documento fiscal:', error);

      return {
        success: false,
        errorCode: 'API_ERROR',
        errorMessage: error.message || 'Erro na comunicação com a API',
      };
    }
  }

  /**
   * Consulta o status de um documento fiscal
   */
  async checkDocumentStatus(
    documentType: FiscalDocumentType,
    documentNumber: string,
    documentSeries: string
  ): Promise<FiscalStatusResponse> {
    if (!this.isReady()) {
      return {
        success: false,
        errorCode: 'PROVIDER_NOT_READY',
        errorMessage: 'Provedor fiscal não está inicializado',
      };
    }

    try {
      const response = await this.apiClient!.get(`/fiscal/${documentType.toLowerCase()}/status`, {
        params: {
          number: documentNumber,
          series: documentSeries,
        },
      });

      if (response.status === 200) {
        return {
          success: true,
          status: response.data.status,
          statusDate: new Date(response.data.statusDate),
          protocolNumber: response.data.protocol,
        };
      }
      return {
        success: false,
        errorCode: response.data.errorCode || 'STATUS_ERROR',
        errorMessage: response.data.errorMessage || 'Erro ao consultar status do documento fiscal',
      };
    } catch (error: any) {
      console.error('Erro ao consultar status do documento fiscal:', error);

      return {
        success: false,
        errorCode: 'API_ERROR',
        errorMessage: error.message || 'Erro na comunicação com a API',
      };
    }
  }

  /**
   * Obtém o PDF de um documento fiscal
   */
  async getDocumentPdf(
    documentType: FiscalDocumentType,
    documentNumber: string,
    documentSeries: string
  ): Promise<string | null> {
    if (!this.isReady()) {
      return null;
    }

    try {
      const response = await this.apiClient!.get(`/fiscal/${documentType.toLowerCase()}/pdf`, {
        params: {
          number: documentNumber,
          series: documentSeries,
        },
        responseType: 'text',
      });

      if (response.status === 200) {
        return response.data;
      }
      console.error('Erro ao obter PDF:', response.status, response.data);
      return null;
    } catch (error) {
      console.error('Erro ao obter PDF do documento fiscal:', error);
      return null;
    }
  }

  /**
   * Obtém o XML de um documento fiscal
   */
  async getDocumentXml(
    documentType: FiscalDocumentType,
    documentNumber: string,
    documentSeries: string
  ): Promise<string | null> {
    if (!this.isReady()) {
      return null;
    }

    try {
      const response = await this.apiClient!.get(`/fiscal/${documentType.toLowerCase()}/xml`, {
        params: {
          number: documentNumber,
          series: documentSeries,
        },
        responseType: 'text',
      });

      if (response.status === 200) {
        return response.data;
      }
      console.error('Erro ao obter XML:', response.status, response.data);
      return null;
    } catch (error) {
      console.error('Erro ao obter XML do documento fiscal:', error);
      return null;
    }
  }

  /**
   * Valida os dados de um documento fiscal antes da emissão
   */
  async validateDocument(document: FiscalDocument): Promise<{
    valid: boolean;
    errors?: string[];
  }> {
    const errors: string[] = [];

    // Validar tipo de documento
    if (!this.getSupportedDocumentTypes().includes(document.type)) {
      errors.push(`Tipo de documento não suportado: ${document.type}`);
    }

    // Validar cliente
    if (!this.validateCustomer(document.customer)) {
      errors.push('Dados do cliente inválidos');
    }

    // Validar itens
    if (!document.items || document.items.length === 0) {
      errors.push('Documento deve ter pelo menos um item');
    } else {
      document.items.forEach((item, index) => {
        if (!this.validateItem(item)) {
          errors.push(`Item ${index + 1} inválido`);
        }
      });
    }

    // Validar valores
    if (document.totalValue <= 0) {
      errors.push('Valor total deve ser maior que zero');
    }

    return {
      valid: errors.length === 0,
      errors: errors.length > 0 ? errors : undefined,
    };
  }

  /**
   * Valida os dados do cliente
   */
  private validateCustomer(customer: FiscalCustomer): boolean {
    // Validar documento
    if (customer.documentType === 'CPF' && !this.validateCpf(customer.documentNumber)) {
      return false;
    }

    if (customer.documentType === 'CNPJ' && !this.validateCnpj(customer.documentNumber)) {
      return false;
    }

    // Validar nome
    if (!customer.name || customer.name.trim().length < 3) {
      return false;
    }

    // Validar endereço
    const address = customer.address;
    if (
      !address.street ||
      !address.number ||
      !address.district ||
      !address.city ||
      !address.state ||
      !address.zipCode
    ) {
      return false;
    }

    return true;
  }

  /**
   * Valida os dados de um item
   */
  private validateItem(item: FiscalItem): boolean {
    return (
      !!item.productCode &&
      !!item.description &&
      item.quantity > 0 &&
      item.unitValue > 0 &&
      item.totalValue > 0 &&
      !!item.taxCode
    );
  }

  /**
   * Valida um CPF
   */
  private validateCpf(cpf: string): boolean {
    cpf = cpf.replace(/[^\d]/g, '');

    if (cpf.length !== 11) return false;

    // Verificar se todos os dígitos são iguais
    if (/^(\d)\1+$/.test(cpf)) return false;

    // Validação do primeiro dígito verificador
    let sum = 0;
    for (let i = 0; i < 9; i++) {
      sum += Number.parseInt(cpf.charAt(i)) * (10 - i);
    }

    let remainder = sum % 11;
    const digit1 = remainder < 2 ? 0 : 11 - remainder;

    if (Number.parseInt(cpf.charAt(9)) !== digit1) return false;

    // Validação do segundo dígito verificador
    sum = 0;
    for (let i = 0; i < 10; i++) {
      sum += Number.parseInt(cpf.charAt(i)) * (11 - i);
    }

    remainder = sum % 11;
    const digit2 = remainder < 2 ? 0 : 11 - remainder;

    return Number.parseInt(cpf.charAt(10)) === digit2;
  }

  /**
   * Valida um CNPJ
   */
  private validateCnpj(cnpj: string): boolean {
    cnpj = cnpj.replace(/[^\d]/g, '');

    if (cnpj.length !== 14) return false;

    // Verificar se todos os dígitos são iguais
    if (/^(\d)\1+$/.test(cnpj)) return false;

    // Validação do primeiro dígito verificador
    let sum = 0;
    let weight = 5;

    for (let i = 0; i < 12; i++) {
      sum += Number.parseInt(cnpj.charAt(i)) * weight;
      weight = weight === 2 ? 9 : weight - 1;
    }

    let remainder = sum % 11;
    const digit1 = remainder < 2 ? 0 : 11 - remainder;

    if (Number.parseInt(cnpj.charAt(12)) !== digit1) return false;

    // Validação do segundo dígito verificador
    sum = 0;
    weight = 6;

    for (let i = 0; i < 13; i++) {
      sum += Number.parseInt(cnpj.charAt(i)) * weight;
      weight = weight === 2 ? 9 : weight - 1;
    }

    remainder = sum % 11;
    const digit2 = remainder < 2 ? 0 : 11 - remainder;

    return Number.parseInt(cnpj.charAt(13)) === digit2;
  }

  /**
   * Mapeia um documento fiscal para o formato da API
   */
  private mapDocumentToApiPayload(document: FiscalDocument): any {
    // Implementação específica para o formato da API Efí Pay
    return {
      document_type: document.type,
      customer: {
        document_type: document.customer.documentType,
        document_number: document.customer.documentNumber.replace(/[^\d]/g, ''),
        name: document.customer.name,
        email: document.customer.email,
        phone: document.customer.phone,
        address: {
          street: document.customer.address.street,
          number: document.customer.address.number,
          complement: document.customer.address.complement,
          district: document.customer.address.district,
          city: document.customer.address.city,
          state: document.customer.address.state,
          zip_code: document.customer.address.zipCode.replace(/[^\d]/g, ''),
          country: document.customer.address.country || 'Brasil',
        },
      },
      items: document.items.map((item) => ({
        product_code: item.productCode,
        description: item.description,
        quantity: item.quantity,
        unit_value: item.unitValue,
        total_value: item.totalValue,
        tax_code: item.taxCode,
        tax_rate: item.taxRate,
        tax_value: item.taxValue,
      })),
      total_value: document.totalValue,
      tax_value: document.taxValue,
      discount_value: document.discountValue,
      shipping_value: document.shippingValue,
      final_value: document.finalValue,
      notes: document.notes,
    };
  }
}
