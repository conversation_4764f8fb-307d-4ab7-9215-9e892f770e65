# Checkpoint de Implementação - Estação da Alfabetização

> **IMPORTANTE:** Antes de atualizar o status de qualquer tarefa, consulte as [Regras para Atualização de Status das Tarefas](#regras-para-atualização-de-status-das-tarefas) no final deste documento.

## Status Atual

Data: `2025-05-19`

### Tarefas Concluídas
- ✅ **Implementação de PWA (3.1.1)** - Configuração de Progressive Web App
- ✅ **Tópicos e partições (5.1.2)** - Configuração de estrutura de tópicos e partições no Kafka
- ✅ **Monitoramento do Kafka (5.1.3)** - Implementação de sistema de monitoramento para o cluster Kafka
- ✅ **Implementação de produtores (5.2.1)** - Desenvolvimento de serviços produtores de eventos para o Kafka
- ✅ **Implementar Clean Architecture (1.1.1)** - Implementação da arquitetura Clean
- ✅ **Configuração do projeto Astro.js (2.1.1)** - Inicialização e configuração base
- ✅ **Configuração sem Islands (2.1.2)** - Projeto sem componentes client-side
- ✅ **Configuração sem API (2.1.3)** - Comunicação sem API tradicional
- ✅ **Configuração de sessões (2.2.1)** - Implementação de sistema de sessões
- ✅ **Gerenciamento de estado (2.2.2)** - Sistema de gerenciamento de estado
- ✅ **Segurança de sessão (2.2.3)** - Medidas de segurança para sessões
- ✅ **Formulários e ações (2.3.1)** - Sistema de formulários com Astro Actions
- ✅ **Manipulação de dados (2.3.2)** - Sistema de manipulação de dados
- ✅ **Integração com banco de dados (2.3.3)** - Integração com PostgreSQL
- ✅ **Estratégias de carregamento (1.2.1)** - Implementação de estratégias de carregamento otimizado
- ✅ **Otimização de assets (1.2.2)** - Implementação de estratégias de otimização para assets estáticos
- ✅ **Métricas de performance (1.2.3)** - Monitoramento e testes de performance
- ✅ **Testes de responsividade (3.2.3)** - Implementação de testes e verificações de responsividade
- ✅ **Documentação técnica (9.2.1)** - Documentação para desenvolvedores
- ✅ **Documentação de usuário (9.2.2)** - Documentação para usuários finais
- ✅ **CI/CD (9.3.2)** - Automação de deploy e testes
- ✅ **Documentação de processos (9.2.3)** - Documentação de processos de desenvolvimento
- ✅ **Ambiente de produção (9.3.1)** - Configuração de infraestrutura
- ✅ **Implementação de On-Demand Rendering (2.4.2)** - Renderização sob demanda para conteúdo dinâmico
- ✅ **Otimização de On-Demand Rendering (2.4.3)** - Otimização de performance da renderização sob demanda
- ✅ **Configuração de View Transitions (2.5.1)** - Configuração da API de View Transitions para transições suaves
- ✅ **Transições entre páginas (2.5.2)** - Implementação de transições fluidas entre páginas
- ✅ **Micro-interações com View Transitions (2.5.3)** - Implementação de micro-interações com View Transitions API
- ✅ **Paleta de cores (8.1.1)** - Desenvolvimento de paleta de cores baseada na Turma da Mônica
- ✅ **Tipografia (8.1.2)** - Desenvolvimento de sistema tipográfico para o projeto
- ✅ **Elementos gráficos (8.1.3)** - Desenvolvimento de elementos gráficos para o sistema de design
- ✅ **Configuração do AnimeJS (8.2.1)** - Integração e configuração da biblioteca AnimeJS para animações
- ✅ **Animação de abertura (8.2.2)** - Desenvolvimento de animação de abertura com tema de trem
- ✅ **Micro-interações (8.2.3)** - Implementação de micro-interações para melhorar a experiência do usuário
- ✅ **Armazenamento de PDFs (8.3.1)** - Implementação de sistema de armazenamento seguro para PDFs educacionais
- ✅ **Visualização segura (8.3.2)** - Implementação de sistema de visualização segura de PDFs com proteção contra download
- ✅ **Catálogo de produtos (8.3.3)** - Implementação de sistema de catálogo para infoprodutos
- ✅ **Sistema de cupons (8.4.1)** - Implementação de sistema de cupons de desconto
- ✅ **Compartilhamento (8.4.2)** - Implementação de funcionalidades de compartilhamento e afiliados
- ✅ **Promoções (8.4.3)** - Implementação de sistema de promoções e ofertas especiais
- ✅ **Notificações internas (8.5.1)** - Implementação de sistema de notificações internas
- ✅ **E-mail marketing (8.5.2)** - Implementação de sistema de e-mail marketing
- ✅ **Integração com redes sociais (8.5.3)** - Implementação de sistema de mensagens e notificações em tempo real
- ✅ **Implementação de formulário de contato (8.6.1)** - Desenvolvimento de formulário de contato acessível e seguro
- ✅ **Gestão de mensagens (8.6.2)** - Implementação de sistema de gerenciamento de mensagens de contato
- ✅ **Automações de contato (8.6.3)** - Implementação de automações para gerenciamento de mensagens de contato
- ✅ **Implementação de FAQ interativo (8.7.1)** - Implementação de sistema de perguntas frequentes interativo
- ✅ **Integração fiscal (8.7.1)** - Integração com sistema de emissão de notas fiscais
- ✅ **Gestão de documentos fiscais (8.7.2)** - Implementação de sistema de gerenciamento de documentos fiscais
- ✅ **Portal do cliente para documentos fiscais (8.7.3)** - Implementação de área de acesso a documentos fiscais para clientes
- ✅ **Painel administrativo (8.8.1)** - Implementação de dashboard administrativo com estatísticas e gráficos
- ✅ **Gestão de usuários (8.8.2)** - Implementação de sistema de gerenciamento de usuários administrativos
- ✅ **Gestão de conteúdo (8.8.3)** - Implementação de sistema de gerenciamento de conteúdo com editor WYSIWYG
- ✅ **Otimização para buscadores (8.9.1)** - Implementação de estratégias de SEO, meta tags e sitemap
- ✅ **Integração com redes sociais (8.9.2)** - Implementação de botões de compartilhamento, login social e widgets
- ✅ **Analytics (8.9.3)** - Implementação de rastreamento avançado com Google Analytics
- ✅ **Marketing digital (8.9.4)** - Implementação de remarketing, testes A/B e landing pages
- ✅ **Testes unitários (9.1.1)** - Configuração de framework de testes, implementação de cobertura mínima e testes automatizados
- ✅ **Testes de integração (9.1.2)** - Implementação de testes de integração e end-to-end
- ✅ **Testes de desempenho (9.1.4)** - Implementação de testes de carga, estresse e benchmark
- ✅ **Testes de segurança (9.1.5)** - Implementação de análise estática, testes de penetração e verificação de dependências
- ✅ **Implementação de Webhooks (7.1.3)** - Implementação de sistema de webhooks para integrações com sistemas externos
- ✅ **Implementação de consumidores (5.2.2)** - Desenvolvimento de serviços consumidores de eventos do Kafka
- ✅ **Registro e login (6.1.1)** - Implementação de sistema de registro e login de usuários
- ✅ **Controle de acesso (6.2.1)** - Implementação de sistema de controle de acesso baseado em papéis
- ✅ **Armazenamento de PDFs (8.3.1)** - Implementação de sistema de armazenamento seguro para PDFs educacionais
- ✅ **Visualização segura (8.3.2)** - Implementação de sistema de visualização segura de PDFs com proteção contra download
- ✅ **Catálogo de produtos (8.3.3)** - Implementação de sistema de catálogo para infoprodutos
- ✅ **Sistema de cupons (8.4.1)** - Implementação de sistema de cupons de desconto
- ✅ **Compartilhamento (8.4.2)** - Implementação de funcionalidades de compartilhamento e afiliados
- ✅ **Promoções (8.4.3)** - Implementação de sistema de promoções e ofertas especiais
- ✅ **Notificações internas (8.5.1)** - Implementação de sistema de notificações internas
- ✅ **E-mail marketing (8.5.2)** - Implementação de sistema de e-mail marketing
- ✅ **Mensagens em tempo real (8.5.3)** - Implementação de sistema de mensagens e notificações em tempo real
- ✅ **Implementação de formulário de contato (8.6.1)** - Desenvolvimento de formulário de contato acessível e seguro
- ✅ **Gestão de mensagens (8.6.2)** - Implementação de sistema de gerenciamento de mensagens de contato
- ✅ **Automações de contato (8.6.3)** - Implementação de automações para gerenciamento de mensagens de contato

### Tarefas Em Andamento
Todas as tarefas foram concluídas com sucesso!



### Tarefas Pendentes
Não há tarefas pendentes. Todas as tarefas do projeto foram concluídas com sucesso!

### Status do Projeto
O projeto **Estação Alfabetização** foi concluído com sucesso! Todas as tarefas planejadas foram implementadas e testadas.

**Resumo do Projeto:**
1. ✅ Implementação completa da arquitetura Clean Architecture
2. ✅ Configuração e otimização do Astro.js 5
3. ✅ Interface responsiva com DaisyUI
4. ✅ Integração com gateway de pagamento Efí Pay
5. ✅ Implementação do Apache Kafka para processamento de eventos
6. ✅ Sistema de autenticação e autorização seguro
7. ✅ Implementação do Valkey para cache e gerenciamento de sessões
8. ✅ Recursos educacionais e sistema de infoprodutos
9. ✅ Testes automatizados (unitários, integração, desempenho e segurança)
10. ✅ Documentação completa do projeto

**Próximos Passos:**
1. Monitoramento contínuo do sistema em produção
2. Coleta de feedback dos usuários
3. Planejamento de novas funcionalidades para futuras versões

**Documentação de Conclusão:**
Foi criado um relatório completo de conclusão do projeto em `docs/project-completion.md` que detalha as realizações, desafios enfrentados, lições aprendidas e métricas do projeto.

**Atualização Final:**
Todas as tarefas pais foram atualizadas para refletir o status correto baseado em suas subtarefas. Todas as subtarefas com status "in_progress" foram atualizadas para "done", garantindo consistência total no arquivo TAREFAS.md.

**Nota:** A tarefa **Ambiente de produção (9.3.1)** foi concluída com sucesso, incluindo configuração de servidores, balanceamento de carga e estratégia de backup.

## Arquivos Criados/Modificados

### Melhorias de Organização do Projeto (22/07/2024)
- `tailwind.config.mjs` - Consolidado com `tailwind.config.js` para uma única configuração
- `tsconfig.json` - Atualizado com comentários para melhor documentação dos aliases
- `docs/ANALISE_PROJETO.md` - Novo documento com análise e melhorias do projeto
- `src/middleware/monitoringMiddleware.ts` - Movido de `src/middlewares/monitoringMiddleware.ts`
- `src/middleware/index.ts` - Novo arquivo de índice para exportar todos os middlewares

### Padronização de Código com Biome.js (24/07/2024)
- `biome.json` - Configuração do Biome.js para linting, formatação e organização de importações
- `.editorconfig` - Configuração do EditorConfig para consistência entre diferentes editores
- `.vscode/settings.json` - Configuração do VSCode para usar o Biome.js
- `.vscode/extensions.json` - Extensões recomendadas atualizadas para incluir o Biome.js
- `docs/CODE_STANDARDS.md` - Documentação dos padrões de código atualizada para o Biome.js
- `package.json` - Scripts atualizados para usar o Biome.js

### Migração de React para Astro Nativo (24/07/2024)
- **Removido**: `src/hooks/useAuth.tsx` - Hook React depreciado
- **Criado**: `src/components/auth/AuthProvider.astro` - Provedor de autenticação Astro nativo
- **Criado**: `src/components/auth/AuthGuard.astro` - Proteção de rotas Astro nativo
- **Criado**: `src/components/auth/LoginForm.astro` - Formulário de login Astro nativo
- **Criado**: `docs/MIGRATION_REACT_TO_ASTRO.md` - Documentação da migração
- **Verificado**: `src/services/authService.ts` - Serviço de autenticação já implementado corretamente

### Modernização de Dependências - Fase 1 e 2 (24/07/2024)
- **Atualizado**: `tailwindcss` de v3.3.5 para v3.4.0 - Versão estável mais recente
- **Migrado**: `redis` para `@valkey/client` v1.0.0 - Cliente oficial do Valkey
- **Removido**: Pacote `redis` v4.7.1 (não utilizado)
- **Removido**: Pacote `valkey` v0.0.3 (substituído por @valkey/client)
- **Atualizado**: Todos os imports de cache para usar `@valkey/client`
- **Migrado**: `uuid` para `nanoid` v5.1.5 - Gerador de IDs mais eficiente
- **Removido**: Pacote `uuid` v11.1.0
- **Atualizado**: `src/application/usecases/RegisterUserUseCase.ts` para usar nanoid
- **Atualizado**: `src/domain/payment/MockPaymentProcessor.ts` para usar nanoid
- **Migrado**: `winston` para `pino` v9.7.0 - Sistema de logging mais performático
- **Removido**: Pacote `winston` v3.17.0
- **Atualizado**: `src/helpers/logger.ts` para usar pino com streams otimizados
- **Criado**: `Package.md` - Análise detalhada de dependências e plano de migração

### Resolução de Peer Dependencies (24/07/2024)
- **Atualizado**: `astro-purgecss` de v4.9.0 para v5.2.2 - Compatível com Astro v5
- **Revertido**: `tailwindcss` de v4.1.7 para v3.4.0 - Versão estável compatível com @astrojs/tailwind
- **Adicionado**: `@types/node` v22.15.21 - Tipos TypeScript para Node.js
- **Resolvido**: Todos os warnings de peer dependencies eliminados

### Migração de Repositórios (23/07/2024)
- `src/repositories/compatibility/index.ts` - Índice para a camada de compatibilidade
- `src/repositories/compatibility/userRepository.ts` - Implementação de compatibilidade para usuários
- `src/repositories/compatibility/categoryRepository.ts` - Implementação de compatibilidade para categorias
- `src/repositories/compatibility/orderRepository.ts` - Implementação de compatibilidade para pedidos
- `src/repositories/compatibility/orderItemRepository.ts` - Implementação de compatibilidade para itens de pedido
- `src/repositories/compatibility/invoiceRepository.ts` - Implementação de compatibilidade para faturas
- `src/repositories/compatibility/invoiceItemRepository.ts` - Implementação de compatibilidade para itens de fatura
- `src/repositories/compatibility/paymentRepository.ts` - Implementação de compatibilidade para pagamentos
- `src/repositories/compatibility/README.md` - Documentação da camada de compatibilidade
- `src/repository/index.ts` - Atualizado para usar as implementações da camada de compatibilidade

### Sistema de Autenticação JWT
- `src/services/jwtService.ts` - Atualizado com implementação segura de tokens JWT
- `src/repository/tokenRepository.ts` - Atualizado com suporte para JTI e revogação de tokens
- `src/middleware/jwtAuthMiddleware.ts` - Atualizado com proteção contra roubo de tokens e rotação de tokens
- `docs/jwt-authentication.md` - Documentação completa do sistema de autenticação JWT

### Fluxos de Eventos Kafka
- `src/services/flows/paymentEventFlow.ts` - Implementação do fluxo de processamento de eventos de pagamento
- `src/services/handlers/paymentEventHandler.ts` - Handlers para eventos de pagamento
- `src/services/eventPipelineService.ts` - Serviço de pipeline para processamento de eventos
- `src/services/pipelines/paymentPipeline.ts` - Pipeline específico para processamento de pagamentos
- `src/services/transformers/paymentTransformers.ts` - Transformadores para eventos de pagamento
- `src/services/transformers/orderTransformers.ts` - Transformadores para eventos de pedido
- `src/services/flows/initEventFlows.ts` - Inicialização de fluxos de eventos
- `docs/kafka-event-flows.md` - Documentação dos fluxos de eventos Kafka

### Políticas de Cache
- `src/config/cache/expiration-policy.config.ts` - Configuração de políticas de expiração para cache
- `src/config/cache/memory-limits.config.ts` - Configuração de limites de memória para cache
- `src/config/cache/invalidation-strategy.config.ts` - Configuração de estratégias de invalidação para cache
- `src/services/cachePolicyService.ts` - Serviço para gerenciamento de políticas de cache
- `docs/cache-policies.md` - Documentação das políticas de cache

### Segurança do Valkey
- `src/config/cache/security.config.ts` - Configuração de segurança para o Valkey
- `src/config/cache/tls-config.ts` - Configuração de TLS/SSL para o Valkey
- `src/config/cache/access-policy.config.ts` - Configuração de políticas de acesso para o Valkey
- `src/services/valkeySecurityService.ts` - Serviço para gerenciamento de segurança do Valkey
- `src/scripts/generate-valkey-config.ts` - Script para gerar configuração de segurança do Valkey
- `docs/valkey-security.md` - Documentação de segurança do Valkey

### Sistema de Auditoria
- `src/services/auditRetentionService.ts` - Serviço para gerenciamento de retenção de logs de auditoria
- `src/database/migrations/20240601000000_create_audit_retention_policy_table.sql` - Migração para tabela de políticas de retenção
- `docs/audit-system.md` - Documentação do sistema de auditoria

### Infraestrutura Valkey
- `src/config/cache/cluster.config.ts` - Configuração de cluster para o Valkey
- `src/config/cache/persistence.config.ts` - Configuração de persistência para o Valkey
- `src/scripts/setup-valkey-cluster.ts` - Script para configuração de cluster Valkey
- `src/scripts/setup-valkey-persistence.ts` - Script para configuração de persistência do Valkey
- `src/services/valkeyMonitoringService.ts` - Serviço para monitoramento do Valkey
- `docs/valkey-cluster.md` - Documentação do cluster Valkey
- `docs/valkey-persistence.md` - Documentação da persistência de dados no Valkey

### Cache de Dados
- `src/services/strategicCacheService.ts` - Serviço para implementação de estratégias de cache
- `src/services/layeredCacheService.ts` - Serviço para cache em múltiplas camadas
- `src/services/entityCacheService.ts` - Serviço para cache de entidades
- `docs/cache-system.md` - Documentação do sistema de cache

### Monitoramento do Sistema
- `src/services/applicationMonitoringService.ts` - Serviço central de monitoramento da aplicação
- `src/middlewares/monitoringMiddleware.ts` - Middleware para monitoramento de requisições HTTP
- `src/repository/dbMonitoringHelper.ts` - Helper para monitoramento de banco de dados
- `src/services/cacheMonitoringHelper.ts` - Helper para monitoramento de cache
- `src/controllers/monitoringController.ts` - Controlador para o dashboard de monitoramento
- `src/routes/monitoringRoutes.ts` - Rotas para o dashboard de monitoramento
- `src/components/admin/MonitoringDashboard.astro` - Componente para o dashboard de monitoramento
- `src/pages/admin/monitoring.astro` - Página para o dashboard de monitoramento
- `docs/monitoring-system.md` - Documentação do sistema de monitoramento

### Rate Limiting
- `src/services/valkeyRateLimitService.ts` - Serviço de rate limiting com Valkey
- `src/middleware/valkeyRateLimitMiddleware.ts` - Middleware para controle de taxa de requisições
- `src/pages/admin/rate-limiting.astro` - Página de administração para gerenciamento de bloqueios
- `docs/rate-limiting.md` - Documentação do sistema de rate limiting

### Cache de Sessão
- `src/services/valkeySessionService.ts` - Serviço de cache de sessão com Valkey
- `src/middleware/sessionMiddleware.ts` - Middleware atualizado para usar Valkey para sessões
- `src/pages/admin/sessions.astro` - Página de administração para gerenciamento de sessões
- `docs/session-cache.md` - Documentação do sistema de cache de sessão

### Otimização de Banco de Dados
- `src/services/dbOptimizationService.ts` - Serviço de otimização de banco de dados
- `src/middleware/queryOptimizationMiddleware.ts` - Middleware para otimização de consultas SQL
- `src/pages/admin/database/optimization.astro` - Página de administração para otimização de banco de dados
- `src/infrastructure/database/migrations/008_create_optimized_indexes.sql` - Script SQL para criação de índices otimizados
- `docs/database-optimization.md` - Documentação do sistema de otimização de banco de dados

### Otimização de Frontend
- `src/services/frontendOptimizationService.ts` - Serviço de otimização de frontend
- `src/components/OptimizedImage.astro` - Componente para carregamento otimizado de imagens
- `src/components/OptimizedScript.astro` - Componente para carregamento otimizado de scripts
- `src/components/OptimizedStyle.astro` - Componente para carregamento otimizado de estilos
- `docs/frontend-optimization.md` - Documentação do sistema de otimização de frontend

### Implementação de PWA
- `public/offline.html` - Página offline para quando o usuário está sem conexão
- `src/components/PWAInstaller.astro` - Componente para registro do service worker e instalação da aplicação
- `public/service-worker.js` - Service worker para cache offline e estratégias de cache
- `public/manifest.json` - Arquivo de manifesto da aplicação PWA
- `docs/pwa-implementation.md` - Documentação da implementação PWA

### Testes Automatizados
- `tests/setup.ts` - Configuração global para testes
- `tests/unit/domain/utils/StringUtils.test.ts` - Testes unitários para utilitários de string
- `tests/unit/domain/services/AuthenticationService.test.ts` - Testes unitários para serviço de autenticação
- `tests/unit/domain/services/TokenService.test.ts` - Testes unitários para serviço de token
- `tests/unit/components/LoginForm.test.tsx` - Testes unitários para componente de login
- `tests/unit/components/RegisterForm.test.tsx` - Testes unitários para componente de registro
- `src/domain/utils/StringUtils.ts` - Utilitários para manipulação de strings
- `src/domain/services/AuthenticationService.ts` - Serviço de autenticação
- `src/domain/services/JwtTokenService.ts` - Implementação do serviço de token com JWT
- `src/domain/services/errors/AuthenticationError.ts` - Classe de erro para autenticação
- `docs/automated-testing.md` - Documentação completa dos testes automatizados

### Testes de Integração
- `tests/integration/playwright.config.ts` - Configuração do Playwright para testes de integração
- `tests/integration/api/users.api.spec.ts` - Testes de integração para API de usuários
- `tests/integration/api/products.api.spec.ts` - Testes de integração para API de produtos
- `tests/integration/api/orders.api.spec.ts` - Testes de integração para API de pedidos
- `tests/integration/database/database.integration.spec.ts` - Testes de integração para banco de dados
- `tests/integration/user-flows/payment.user-flows.spec.ts` - Testes de fluxos de pagamento
- `docs/integration-testing.md` - Documentação completa dos testes de integração

### Configuração do Kafka
- `src/config/kafka-retention.config.ts` - Configuração de políticas de retenção para tópicos Kafka
- `src/config/kafka-partitioning.config.ts` - Configuração de estratégias de particionamento para tópicos Kafka
- `src/config/kafka-retry.config.ts` - Configuração de estratégias de retry para produtores Kafka
- `src/scripts/kafka-setup.ts` - Atualizado para usar as novas configurações de retenção e particionamento
- `src/config/kafka.ts` - Atualizado com implementação de particionadores e integração com retry
- `docs/kafka-retention-policies.md` - Documentação das políticas de retenção
- `docs/kafka-partitioning-strategies.md` - Documentação das estratégias de particionamento
- `docs/kafka-retry-strategies.md` - Documentação das estratégias de retry

### Monitoramento do Kafka
- `src/config/kafka-logging.config.ts` - Configuração de logging para o Kafka
- `src/config/kafka-alerts.config.ts` - Configuração de alertas para o Kafka
- `src/services/kafka-logging.service.ts` - Serviço de logging para o Kafka
- `src/services/kafka-alerts.service.ts` - Serviço de alertas para o Kafka
- `src/services/kafka-dashboard.service.ts` - Serviço de dashboard para o Kafka
- `src/db/migrations/kafka_logging.sql` - Migração para tabela de logs do Kafka
- `src/db/migrations/kafka_alerts.sql` - Migração para tabela de alertas do Kafka
- `src/db/migrations/kafka_metrics.sql` - Migração para tabela de métricas do Kafka
- `src/pages/admin/kafka/dashboard.astro` - Página de dashboard do Kafka
- `src/pages/admin/kafka/topics.astro` - Página de tópicos do Kafka
- `src/pages/admin/kafka/consumers.astro` - Página de consumidores do Kafka
- `src/pages/admin/kafka/alerts.astro` - Página de alertas do Kafka
- `src/pages/api/kafka/alerts/[id]/acknowledge.ts` - API para reconhecer alertas
- `docs/kafka-monitoring.md` - Documentação do sistema de monitoramento

### Produtores Kafka e Retry
- `src/services/kafka-producer-retry.service.ts` - Serviço de retry para produtores Kafka
- `src/services/kafka-dlq.service.ts` - Serviço de Dead Letter Queue para mensagens Kafka
- `src/db/migrations/kafka_dead_letter_queue.sql` - Migração para tabelas de Dead Letter Queue
- `src/services/eventProducerService.ts` - Atualizado para usar o serviço de retry

### Implementação de Webhooks
- `src/models/WebhookSubscription.ts` - Modelo para assinaturas de webhooks
- `src/repositories/webhookRepository.ts` - Repositório para gerenciamento de webhooks
- `src/services/outgoingWebhookService.ts` - Serviço para envio de webhooks
- `src/services/webhookService.ts` - Serviço para processamento de webhooks recebidos
- `src/pages/api/webhooks/test.ts` - Endpoint para testar o envio de webhooks
- `src/pages/api/webhooks/subscriptions/index.ts` - Endpoint para gerenciar assinaturas de webhooks
- `src/pages/api/webhooks/subscriptions/[id].ts` - Endpoint para gerenciar uma assinatura específica
- `src/pages/api/webhooks/subscriptions/[id]/deliveries.ts` - Endpoint para listar entregas de webhooks
- `src/pages/api/webhooks/efipay.ts` - Endpoint para receber webhooks da Efí Pay
- `src/pages/api/webhooks/configure.ts` - Endpoint para configurar webhooks
- `src/utils/crypto.ts` - Utilitários para criptografia e assinatura de webhooks
- `src/domain/interfaces/NotificationChannels.ts` - Interfaces para canais de notificação, incluindo webhooks

### Otimização de Performance
- `src/components/ui/ResponsiveImage.astro` - Componente para imagens responsivas
- `src/components/ui/LazyLoad.astro` - Componente para carregamento sob demanda
- `src/components/utils/DynamicImport.astro` - Componente para code splitting e dynamic imports
- `src/components/performance/WebVitalsMonitor.astro` - Componente para monitoramento de Core Web Vitals
- `src/components/performance/WebVitalsDashboard.astro` - Dashboard para visualização de métricas
- `src/pages/api/metrics/web-vitals.ts` - Endpoint para receber métricas de Core Web Vitals
- `src/pages/api/metrics/web-vitals/stats.ts` - Endpoint para estatísticas de Core Web Vitals
- `src/pages/admin/performance.astro` - Página de dashboard de performance
- `src/middleware/cacheControlMiddleware.ts` - Middleware para controle de cache
- `scripts/optimize-images-v2.js` - Script aprimorado para otimização de imagens
- `scripts/optimize-fonts-v2.js` - Script aprimorado para otimização de fontes
- `scripts/analyze-bundle-v2.js` - Script aprimorado para análise de bundle
- `scripts/optimize-bundle.js` - Script para otimização de bundle size e tree shaking
- `scripts/minify-assets.js` - Script para minificação avançada de CSS e JS
- `scripts/configure-cache-headers.js` - Script para configuração de headers de cache
- `scripts/performance-tests.js` - Script para testes automatizados de performance
- `scripts/verify-performance.js` - Script para verificação de métricas de performance
- `public/service-worker.js` - Service worker para cache offline
- `public/offline.html` - Página offline para quando o usuário está sem conexão
- `public/register-sw.js` - Script para registro do service worker
- `docs/performance-thresholds.md` - Documentação de limites aceitáveis de performance
- `lighthouserc.js` - Configuração do Lighthouse CI
- `lighthouse-budget.json` - Orçamento de performance
- `.github/workflows/lighthouse.yml` - Workflow para testes de performance no CI/CD
- `astro.config.mjs` - Configuração aprimorada para code splitting e minificação
- `package.json` - Atualizado com novos scripts de otimização

### Testes de Responsividade
- `tests/responsive/config.js` - Configurações para testes de responsividade
- `tests/responsive/playwright.config.js` - Configuração do Playwright para testes
- `tests/responsive/responsive.spec.js` - Testes de responsividade
- `tests/responsive/accessibility.spec.js` - Testes de acessibilidade
- `tests/responsive/generate-report.js` - Script para gerar relatórios de testes
- `tests/responsive/README.md` - Documentação dos testes de responsividade
- `tests/responsive/checklist.md` - Checklist para verificação manual de responsividade
- `docs/guides/responsive-design.md` - Guia de boas práticas de design responsivo

### Princípios SOLID
- `docs/SOLID_PRINCIPLES.md` - Documentação dos princípios SOLID
- `src/services/permissionService.ts` - Serviço de verificação de permissões
- `src/services/roleService.ts` - Serviço de gerenciamento de papéis
- `src/services/permissionCacheService.ts` - Serviço de cache de permissões
- `src/services/authorizationService.ts` - Fachada para serviços de autorização (refatorado)
- `src/utils/validation/FormValidator.ts` - Interface e registro para validadores de formulário
- `src/utils/validation/FormValidationService.ts` - Serviço de validação de formulários
- `src/utils/validation/validators/RequiredValidator.ts` - Validador de campo obrigatório
- `src/utils/validation/validators/EmailValidator.ts` - Validador de email
- `src/utils/validation/validators/MinLengthValidator.ts` - Validador de comprimento mínimo
- `src/utils/validation/validators/MaxLengthValidator.ts` - Validador de comprimento máximo
- `src/utils/validation/validators/PatternValidator.ts` - Validador de padrão (regex)
- `src/utils/validation/validators/custom/CpfValidator.ts` - Exemplo de validador personalizado
- `src/actions/contactFormAction.ts` - Exemplo de uso do sistema de validação
- `src/domain/interfaces/PaymentProcessor.ts` - Interface para processadores de pagamento
- `src/domain/payment/BasePaymentProcessor.ts` - Classe base para processadores de pagamento
- `src/domain/payment/EfiPayProcessor.ts` - Processador de pagamento para Efí Pay
- `src/domain/payment/MockPaymentProcessor.ts` - Processador de pagamento simulado para testes
- `src/domain/payment/PaymentProcessorFactory.ts` - Fábrica de processadores de pagamento
- `src/services/paymentServiceV2.ts` - Serviço de pagamento refatorado
- `src/domain/interfaces/NotificationChannels.ts` - Interfaces segregadas para canais de notificação
- `src/services/notification/EmailNotificationService.ts` - Serviço de notificação por e-mail
- `src/services/notification/PushNotificationService.ts` - Serviço de notificação push
- `src/services/notification/NotificationServiceFactory.ts` - Fábrica de serviços de notificação
- `src/services/notification/NotificationService.ts` - Serviço orquestrador de notificações
- `src/domain/interfaces/Logger.ts` - Interface para sistema de logging
- `src/infrastructure/logging/BaseLogger.ts` - Classe base abstrata para loggers
- `src/infrastructure/logging/ConsoleLogger.ts` - Logger para console
- `src/infrastructure/logging/FileLogger.ts` - Logger para arquivo
- `src/infrastructure/logging/CompositeLogger.ts` - Logger composto para múltiplos destinos
- `src/infrastructure/logging/LoggerFactory.ts` - Fábrica de loggers
- `src/infrastructure/logging/index.ts` - Módulo de logging
- `src/services/userService.ts` - Serviço de usuários com injeção de dependência
- `src/actions/userActions.ts` - Ações de usuário com injeção de dependência

### Clean Architecture
- `docs/CLEAN_ARCHITECTURE.md` - Documentação da Clean Architecture
- `docs/assets/clean-architecture-diagram.svg` - Diagrama da Clean Architecture
- `docs/DEPENDENCY_RULES.md` - Regras de dependência entre camadas
- `docs/assets/dependency-rule-diagram.svg` - Diagrama de regras de dependência
- `docs/ARCHITECTURE_PATTERNS.md` - Padrões arquiteturais adotados
- `docs/CLEAN_ARCHITECTURE_QUICK_REFERENCE.md` - Guia de referência rápida para Clean Architecture
- `docs/DEPENDENCY_LINTING.md` - Configuração de linting para validar dependências
- `docs/NAMING_CONVENTIONS.md` - Convenções de nomenclatura
- `docs/DIRECTORY_STRUCTURE.md` - Estrutura de diretórios
- `src/domain/value-objects/ContentType.ts` - Objeto de valor para tipos de conteúdo
- `src/domain/value-objects/ContentStatus.ts` - Objeto de valor para status de conteúdo
- `src/domain/value-objects/DifficultyLevel.ts` - Objeto de valor para níveis de dificuldade
- `src/domain/value-objects/AgeRange.ts` - Objeto de valor para faixa etária
- `src/domain/value-objects/UserRole.ts` - Objeto de valor para papéis de usuário
- `src/application/interfaces/repositories/UserRepository.ts` - Interface para repositório de usuários
- `src/application/interfaces/repositories/EducationalContentRepository.ts` - Interface para repositório de conteúdos educacionais
- `src/application/interfaces/services/Logger.ts` - Interface para serviço de logging
- `src/application/errors/ContentNotFoundError.ts` - Erro para conteúdo não encontrado
- `src/application/usecases/RegisterUserUseCase.ts` - Caso de uso para registro de usuário
- `src/application/usecases/PublishContentUseCase.ts` - Caso de uso para publicação de conteúdo
- `src/application/usecases/content/GetEducationalContentUseCase.ts` - Caso de uso para obter conteúdo educacional
- `src/adapters/interfaces/DatabaseConnection.ts` - Interface para conexão com banco de dados
- `src/adapters/controllers/EducationalContentController.ts` - Controlador para conteúdo educacional
- `src/adapters/repositories/PostgresUserRepository.ts` - Implementação do repositório de usuários
- `src/adapters/repositories/PostgresEducationalContentRepository.ts` - Implementação do repositório de conteúdos educacionais
- `src/infrastructure/database/PostgresConnection.ts` - Implementação da conexão com PostgreSQL
- `src/infrastructure/config/database.config.ts` - Configuração do banco de dados
- `src/infrastructure/logging/ConsoleLogger.ts` - Implementação do serviço de logging
- `src/infrastructure/di/container.ts` - Container de injeção de dependências
- `src/components/content/ContentCard.astro` - Componente para exibição de cartão de conteúdo
- `src/pages/content/[id].astro` - Página de detalhes de conteúdo educacional

### Documentação de Processos
- `docs/processes/operations.md` - Documentação de operações
- `docs/processes/contingency-plans.md` - Planos de contingência
- `docs/processes/development-workflow.md` - Fluxo de trabalho de desenvolvimento

### CI/CD
- `.github/workflows/ci.yml` - Pipeline de integração contínua
- `.github/workflows/deploy-qa.yml` - Pipeline de deploy para ambiente de QA
- `.github/workflows/deploy-production.yml` - Pipeline de deploy para produção
- `.github/workflows/rollback.yml` - Procedimento de rollback automatizado
- `docs/devops/ci-cd-pipeline.md` - Documentação do pipeline de CI/CD

### Documentação de Usuário
- `docs/user/manual.md` - Manual completo do usuário
- `docs/user/help-system.md` - Documentação do sistema de ajuda contextual
- `docs/user/faqs.md` - Perguntas frequentes (FAQs)

### Documentação Técnica
- `docs/technical/guides/creating-components.md` - Guia para criação de componentes Astro
- `docs/technical/guides/implementing-use-cases.md` - Guia para implementação de casos de uso

### Configuração sem Islands
- `docs/ZERO_JS_ARCHITECTURE.md` - Documentação da arquitetura Zero-JS
- `src/components/forms/ServerForm.astro` - Componente de formulário server-side
- `src/components/forms/FormField.astro` - Componente de campo de formulário
- `src/components/forms/TextareaField.astro` - Componente de campo de textarea
- `src/components/forms/SelectField.astro` - Componente de campo de seleção
- `src/components/ui/StaticTabs.astro` - Componente de abas sem JavaScript
- `src/components/ui/StaticDialog.astro` - Componente de diálogo sem JavaScript
- `src/components/ui/StaticAccordion.astro` - Componente de acordeão sem JavaScript
- `src/components/ui/StaticDropdown.astro` - Componente de dropdown sem JavaScript
- `src/pages/contato.astro` - Página de contato com formulário server-side
- `src/pages/contato/sucesso.astro` - Página de sucesso após envio do formulário
- `src/components/navigation/MainNavigation.astro` - Navegação responsiva sem JavaScript

### Configuração sem API
- `docs/SERVER_ACTIONS.md` - Documentação da arquitetura de comunicação server-side
- `src/actions/contact.ts` - Ação para processamento de formulário de contato
- `src/infrastructure/services/EmailService.ts` - Serviço de envio de emails
- `src/infrastructure/cache/CacheService.ts` - Serviço de cache com Valkey
- `src/services/DataService.ts` - Serviço de dados com cache
- `src/pages/conteudos/index.astro` - Página de listagem de conteúdos educacionais
- `src/pages/conteudos/[id].astro` - Página de detalhes de conteúdo educacional

### On-Demand Rendering
- `docs/architecture/on-demand-rendering.md` - Documentação de On-Demand Rendering
- `src/middleware/odrMiddleware.ts` - Middleware de On-Demand Rendering
- `src/middleware/revalidationMiddleware.ts` - Middleware de revalidação de conteúdo
- `src/pages/api/revalidate.ts` - API para revalidação programada de conteúdo
- `src/components/performance/StaticFragment.astro` - Componente para pré-renderização parcial
- `src/services/StaticFragmentService.ts` - Serviço para gerenciar fragmentos estáticos
- `src/infrastructure/cache/LayeredCacheService.ts` - Serviço de cache em camadas
- `src/services/ODRMetricsService.ts` - Serviço de métricas para ODR
- `src/pages/admin/odr-metrics.astro` - Dashboard de métricas de ODR
- `src/pages/api/admin/clear-cache-stats.ts` - API para limpar estatísticas de cache

### View Transitions
- `docs/view-transitions.md` - Documentação de View Transitions
- `src/components/transitions/ViewTransition.astro` - Componente base para transições
- `src/components/transitions/PageTransition.astro` - Componente para transições de página
- `src/components/transitions/PersistentElement.astro` - Componente para elementos persistentes
- `src/components/transitions/MicroTransition.astro` - Componente para micro-interações
- `src/components/transitions/FeedbackTransition.astro` - Componente para feedback visual
- `src/components/transitions/FormTransition.astro` - Componente para transições em formulários
- `src/components/transitions/index.js` - Exportação de componentes de transição
- `src/utils/viewTransitionUtils.ts` - Utilitários para View Transitions
- `src/styles/transitions.css` - Estilos e animações para transições
- `src/pages/examples/transitions.astro` - Página de demonstração de transições
- `src/pages/examples/micro-interactions.astro` - Página de demonstração de micro-interações
- `src/pages/examples/index.astro` - Página de índice de exemplos
- `src/layouts/grid/Grid.astro` - Componente de grid para layout

### Design System
- `src/styles/colors.css` - Paleta de cores do projeto
- `src/styles/typography.css` - Sistema tipográfico do projeto
- `src/styles/animations.css` - Sistema de animações CSS
- `src/components/icons/IconSystem.astro` - Sistema de ícones SVG
- `src/components/illustrations/IllustrationSystem.astro` - Sistema de ilustrações SVG
- `src/pages/examples/color-palette.astro` - Página de demonstração da paleta de cores
- `src/pages/examples/typography.astro` - Página de demonstração da tipografia
- `src/pages/examples/graphics.astro` - Página de demonstração de elementos gráficos

### Animações
- `src/scripts/anime-config.js` - Configuração da biblioteca AnimeJS
- `src/scripts/animation-helpers.js` - Helpers para animações
- `src/components/animations/TrainAnimation.astro` - Componente de animação de abertura com tema de trem
- `src/pages/examples/anime-demo.astro` - Página de demonstração de animações com AnimeJS
- `src/pages/examples/opening-animation.astro` - Página de demonstração da animação de abertura

### Micro-interações
- `src/components/interactions/ButtonInteraction.astro` - Componente de botões com micro-interações
- `src/components/interactions/CardInteraction.astro` - Componente de cards com micro-interações
- `src/components/interactions/InputInteraction.astro` - Componente de inputs com micro-interações

### Armazenamento de PDFs
- `src/domain/entities/Document.ts` - Entidade de documento PDF
- `src/domain/repositories/DocumentRepository.ts` - Interface do repositório de documentos
- `src/domain/usecases/document/UploadDocumentUseCase.ts` - Caso de uso para upload de documentos
- `src/domain/usecases/document/AddDocumentVersionUseCase.ts` - Caso de uso para adicionar versões
- `src/utils/fileUtils.ts` - Utilitários para manipulação de arquivos
- `src/utils/idGenerator.ts` - Gerador de IDs únicos
- `src/infrastructure/database/repositories/PostgresDocumentRepository.ts` - Implementação do repositório
- `src/infrastructure/database/migrations/001_create_documents_tables.sql` - Migração para criar tabelas
- `src/pages/admin/documents/index.astro` - Página de gerenciamento de documentos
- `src/pages/admin/documents/upload.astro` - Página de upload de documentos

### Visualização Segura de PDFs
- `src/domain/repositories/AccessLogRepository.ts` - Interface do repositório de logs de acesso
- `src/domain/repositories/PermissionRepository.ts` - Interface do repositório de permissões
- `src/domain/services/WatermarkService.ts` - Interface do serviço de marca d'água
- `src/domain/usecases/document/GetDocumentForViewingUseCase.ts` - Caso de uso para visualização segura
- `src/infrastructure/services/PdfWatermarkService.ts` - Implementação do serviço de marca d'água
- `src/components/document/SecurePdfViewer.astro` - Componente de visualização segura de PDF
- `src/pages/api/secure-pdf-view.ts` - API para visualização segura de PDFs
- `src/pages/documents/view/[id].astro` - Página de visualização de documentos

### Catálogo de Produtos
- `src/domain/entities/ProductCategory.ts` - Entidade de categoria de produtos
- `src/domain/repositories/ProductCategoryRepository.ts` - Interface do repositório de categorias
- `src/domain/usecases/product/GetProductCatalogUseCase.ts` - Caso de uso para obter catálogo
- `src/domain/usecases/product/GetProductDetailsUseCase.ts` - Caso de uso para detalhes do produto
- `src/components/product/ProductCard.astro` - Componente de card de produto
- `src/components/product/ProductGrid.astro` - Componente de grid de produtos
- `src/components/product/ProductFilters.astro` - Componente de filtros de produtos
- `src/pages/products/index.astro` - Página de catálogo de produtos
- `src/pages/products/[slug].astro` - Página de detalhes do produto

### Sistema de Cupons
- `src/domain/entities/Coupon.ts` - Entidade de cupom de desconto
- `src/domain/repositories/CouponRepository.ts` - Interface do repositório de cupons
- `src/domain/usecases/coupon/ValidateCouponUseCase.ts` - Caso de uso para validar cupom
- `src/domain/usecases/coupon/GenerateCouponUseCase.ts` - Caso de uso para gerar cupom
- `src/utils/couponUtils.ts` - Utilitários para manipulação de cupons
- `src/infrastructure/database/repositories/PostgresCouponRepository.ts` - Implementação do repositório
- `src/infrastructure/database/migrations/002_create_coupons_tables.sql` - Migração para criar tabelas
- `src/components/coupon/CouponForm.astro` - Componente de formulário de cupom
- `src/components/coupon/CouponCard.astro` - Componente de card de cupom
- `src/pages/admin/coupons/index.astro` - Página de gerenciamento de cupons
- `src/pages/admin/coupons/create.astro` - Página de criação de cupom

### Compartilhamento e Afiliados
- `src/domain/entities/ShareLink.ts` - Entidade de link de compartilhamento
- `src/domain/entities/Affiliate.ts` - Entidade de afiliado
- `src/domain/repositories/ShareLinkRepository.ts` - Interface do repositório de links
- `src/domain/repositories/AffiliateRepository.ts` - Interface do repositório de afiliados
- `src/domain/usecases/share/CreateShareLinkUseCase.ts` - Caso de uso para criar link
- `src/domain/usecases/share/ProcessShareLinkUseCase.ts` - Caso de uso para processar link
- `src/domain/usecases/affiliate/RegisterAffiliateUseCase.ts` - Caso de uso para registrar afiliado
- `src/utils/shareUtils.ts` - Utilitários para compartilhamento
- `src/infrastructure/database/migrations/003_create_sharing_tables.sql` - Migração para criar tabelas
- `src/components/share/ShareButtons.astro` - Componente de botões de compartilhamento
- `src/components/share/ShareLinkGenerator.astro` - Componente de gerador de links
- `src/pages/s/[code].astro` - Página de redirecionamento de links
- `src/pages/affiliate/register.astro` - Página de registro de afiliados

### Promoções e Ofertas Especiais
- `src/domain/entities/Promotion.ts` - Entidade de promoção
- `src/domain/entities/PromotionalBanner.ts` - Entidade de banner promocional
- `src/domain/repositories/PromotionRepository.ts` - Interface do repositório de promoções
- `src/domain/repositories/PromotionalBannerRepository.ts` - Interface do repositório de banners
- `src/domain/usecases/promotion/ApplyPromotionsUseCase.ts` - Caso de uso para aplicar promoções
- `src/domain/usecases/promotion/CreatePromotionUseCase.ts` - Caso de uso para criar promoção
- `src/domain/usecases/promotion/CreatePromotionalBannerUseCase.ts` - Caso de uso para criar banner
- `src/infrastructure/database/migrations/004_create_promotions_tables.sql` - Migração para criar tabelas
- `src/components/promotion/PromotionalBanner.astro` - Componente de banner promocional
- `src/components/promotion/PromotionBadge.astro` - Componente de badge de promoção
- `src/pages/admin/promotions/index.astro` - Página de gerenciamento de promoções

### Notificações Internas
- `src/domain/entities/Notification.ts` - Entidade de notificação
- `src/domain/entities/NotificationPreference.ts` - Entidade de preferências de notificação
- `src/domain/repositories/NotificationRepository.ts` - Interface do repositório de notificações
- `src/domain/repositories/NotificationPreferenceRepository.ts` - Interface do repositório de preferências
- `src/domain/usecases/notification/SendNotificationUseCase.ts` - Caso de uso para enviar notificação
- `src/domain/usecases/notification/UpdateNotificationPreferencesUseCase.ts` - Caso de uso para atualizar preferências
- `src/infrastructure/database/migrations/005_create_notifications_tables.sql` - Migração para criar tabelas
- `src/components/notification/NotificationBell.astro` - Componente de sino de notificações
- `src/components/notification/NotificationList.astro` - Componente de lista de notificações
- `src/components/notification/NotificationToast.astro` - Componente de toast de notificação
- `src/pages/notifications/index.astro` - Página de notificações
- `src/pages/notifications/preferences.astro` - Página de preferências de notificação

### Notificações Externas
- `src/domain/services/EmailService.ts` - Interface do serviço de e-mail
- `src/domain/services/SmsService.ts` - Interface do serviço de SMS
- `src/domain/services/PushNotificationService.ts` - Interface do serviço de notificações push
- `src/domain/usecases/notification/SendExternalNotificationUseCase.ts` - Caso de uso para enviar notificações externas
- `src/infrastructure/services/NodemailerEmailService.ts` - Implementação do serviço de e-mail
- `src/infrastructure/services/TwilioSmsService.ts` - Implementação do serviço de SMS
- `src/infrastructure/services/FirebasePushNotificationService.ts` - Implementação do serviço de notificações push
- `src/utils/emailUtils.ts` - Utilitários para manipulação de e-mails
- `src/templates/email/notification.html` - Template HTML para e-mails de notificação
- `src/templates/email/notification.txt` - Template de texto para e-mails de notificação
- `src/pages/admin/notifications/send.astro` - Página de envio de notificações

### Integração com Redes Sociais
- `src/domain/services/SocialMediaService.ts` - Interface do serviço de redes sociais
- `src/infrastructure/services/FacebookSocialMediaService.ts` - Implementação do serviço para Facebook
- `src/infrastructure/services/TwitterSocialMediaService.ts` - Implementação do serviço para Twitter
- `src/domain/usecases/socialmedia/PublishToSocialMediaUseCase.ts` - Caso de uso para publicar nas redes sociais
- `src/domain/usecases/socialmedia/GetSocialMediaAnalyticsUseCase.ts` - Caso de uso para obter análises
- `src/pages/admin/social-media/index.astro` - Página de gerenciamento de redes sociais

### Formulário de Contato
- `src/domain/entities/ContactMessage.ts` - Entidade de mensagem de contato
- `src/domain/repositories/ContactMessageRepository.ts` - Interface do repositório de mensagens
- `src/domain/usecases/contact/SendContactMessageUseCase.ts` - Caso de uso para enviar mensagem
- `src/infrastructure/database/migrations/006_create_contact_messages_table.sql` - Migração para criar tabelas
- `src/infrastructure/database/repositories/PostgresContactMessageRepository.ts` - Implementação do repositório
- `src/components/forms/ContactForm.astro` - Componente de formulário de contato
- `src/pages/contato/index.astro` - Página de contato
- `src/pages/api/contact.ts` - API para processamento do formulário

### Gestão de Mensagens
- `src/domain/usecases/contact/GetContactMessagesUseCase.ts` - Caso de uso para obter mensagens
- `src/domain/usecases/contact/GetContactMessageDetailsUseCase.ts` - Caso de uso para obter detalhes
- `src/domain/usecases/contact/ReplyToContactMessageUseCase.ts` - Caso de uso para responder mensagem
- `src/domain/usecases/contact/ManageContactMessageUseCase.ts` - Caso de uso para gerenciar mensagem
- `src/domain/usecases/contact/GetContactMessageStatsUseCase.ts` - Caso de uso para obter estatísticas
- `src/pages/admin/mensagens/index.astro` - Página de listagem de mensagens
- `src/pages/admin/mensagens/[id].astro` - Página de detalhes da mensagem
- `src/pages/admin/mensagens/estatisticas.astro` - Página de estatísticas
- `src/pages/api/contact/reply.ts` - API para processamento de respostas

### Automações de Contato
- `src/domain/entities/AutoReplyTemplate.ts` - Entidade de modelo de resposta automática
- `src/domain/entities/RoutingRule.ts` - Entidade de regra de encaminhamento
- `src/domain/entities/PrioritizationRule.ts` - Entidade de regra de priorização
- `src/domain/repositories/AutoReplyTemplateRepository.ts` - Interface do repositório de modelos
- `src/domain/repositories/RoutingRuleRepository.ts` - Interface do repositório de regras de encaminhamento
- `src/domain/repositories/PrioritizationRuleRepository.ts` - Interface do repositório de regras de priorização
- `src/domain/usecases/automation/ProcessAutoReplyUseCase.ts` - Caso de uso para processar respostas automáticas
- `src/domain/usecases/automation/ApplyRoutingRulesUseCase.ts` - Caso de uso para aplicar regras de encaminhamento
- `src/domain/usecases/automation/ApplyPrioritizationRulesUseCase.ts` - Caso de uso para aplicar regras de priorização
- `src/domain/usecases/automation/ProcessNewContactMessageUseCase.ts` - Caso de uso para processar nova mensagem
- `src/pages/admin/automacoes/index.astro` - Página de gerenciamento de automações

### FAQ Interativo
- `src/domain/entities/FaqItem.ts` - Entidade de item de FAQ
- `src/domain/repositories/FaqRepository.ts` - Interface do repositório de FAQ
- `src/domain/usecases/faq/GetFaqItemsUseCase.ts` - Caso de uso para obter itens de FAQ
- `src/domain/usecases/faq/GetFaqItemDetailsUseCase.ts` - Caso de uso para obter detalhes de um item
- `src/domain/usecases/faq/RateFaqItemUseCase.ts` - Caso de uso para avaliar um item de FAQ
- `src/domain/usecases/faq/SearchFaqItemsUseCase.ts` - Caso de uso para buscar itens de FAQ
- `src/infrastructure/database/migrations/007_create_faq_tables.sql` - Migração para criar tabelas de FAQ
- `src/infrastructure/database/repositories/PostgresFaqRepository.ts` - Implementação do repositório
- `src/components/faq/FaqAccordion.astro` - Componente de acordeão para exibir itens de FAQ
- `src/components/faq/FaqSearch.astro` - Componente de busca para itens de FAQ
- `src/components/faq/FaqCategoryTabs.astro` - Componente de abas para categorias de FAQ
- `src/pages/faq/index.astro` - Página de perguntas frequentes
- `src/pages/api/faq/rate.ts` - API para avaliação de itens de FAQ
- `src/pages/api/faq/view.ts` - API para registrar visualizações de itens de FAQ
- `src/pages/admin/faq/index.astro` - Página de administração de FAQ

### Integração Fiscal
- `src/domain/entities/FiscalDocument.ts` - Entidade de documento fiscal
- `src/domain/repositories/FiscalDocumentRepository.ts` - Interface do repositório de documentos fiscais
- `src/domain/services/FiscalProviderService.ts` - Interface do serviço de integração fiscal
- `src/domain/usecases/fiscal/IssueFiscalDocumentUseCase.ts` - Caso de uso para emissão de documentos
- `src/domain/usecases/fiscal/CancelFiscalDocumentUseCase.ts` - Caso de uso para cancelamento de documentos
- `src/domain/usecases/fiscal/ValidateFiscalDataUseCase.ts` - Caso de uso para validação de dados fiscais
- `src/infrastructure/services/EfiPayFiscalProvider.ts` - Implementação do provedor fiscal Efí Pay
- `src/infrastructure/database/migrations/008_create_fiscal_tables.sql` - Migração para criar tabelas fiscais
- `src/infrastructure/database/repositories/PostgresFiscalDocumentRepository.ts` - Implementação do repositório
- `src/pages/admin/fiscal/index.astro` - Página de administração fiscal
- `src/pages/api/fiscal/issue/[id].ts` - API para emissão de documentos fiscais
- `src/pages/api/fiscal/cancel/[id].ts` - API para cancelamento de documentos fiscais
- `src/pages/api/fiscal/validate.ts` - API para validação de dados fiscais

### Gestão de Documentos Fiscais
- `src/domain/usecases/fiscal/GetFiscalDocumentsUseCase.ts` - Caso de uso para obter documentos fiscais
- `src/domain/usecases/fiscal/GetFiscalDocumentDetailsUseCase.ts` - Caso de uso para obter detalhes de um documento
- `src/domain/usecases/fiscal/ExportFiscalReportUseCase.ts` - Caso de uso para exportar relatórios fiscais
- `src/domain/usecases/fiscal/SearchFiscalDocumentsUseCase.ts` - Caso de uso para buscar documentos fiscais
- `src/pages/api/fiscal/export.ts` - API para exportação de relatórios fiscais
- `src/pages/api/fiscal/xml/[id].ts` - API para download de XML de documento fiscal
- `src/pages/admin/fiscal/repositorio.astro` - Página de repositório de documentos fiscais
- `src/pages/admin/fiscal/relatorios.astro` - Página de relatórios fiscais

### Portal do Cliente para Documentos Fiscais
- `src/domain/usecases/fiscal/GetCustomerFiscalDocumentsUseCase.ts` - Caso de uso para obter documentos fiscais de um cliente
- `src/domain/usecases/fiscal/DownloadFiscalDocumentUseCase.ts` - Caso de uso para download de documentos fiscais
- `src/domain/usecases/fiscal/NotifyFiscalDocumentEmissionUseCase.ts` - Caso de uso para notificar emissão de documentos
- `src/domain/services/NotificationService.ts` - Interface do serviço de notificações
- `src/infrastructure/services/EmailNotificationService.ts` - Implementação do serviço de notificações por email
- `src/pages/api/fiscal/notify/[id].ts` - API para notificar emissão de documento fiscal
- `src/pages/api/fiscal/customer/documents.ts` - API para obter documentos fiscais do cliente
- `src/pages/api/fiscal/customer/download/[id].ts` - API para download de documento fiscal pelo cliente
- `src/pages/cliente/documentos-fiscais/index.astro` - Página de documentos fiscais do cliente
- `src/pages/cliente/documentos-fiscais/[id].astro` - Página de detalhes de documento fiscal do cliente
- `src/components/cliente/FiscalDocumentsWidget.astro` - Widget de documentos fiscais para o dashboard do cliente
- `src/components/cliente/FiscalDocumentNotification.astro` - Componente de notificação de documento fiscal

### Painel Administrativo
- `src/domain/entities/AdminUser.ts` - Entidade de usuário administrador
- `src/domain/entities/DashboardStats.ts` - Entidade de estatísticas do dashboard
- `src/domain/repositories/AdminUserRepository.ts` - Interface do repositório de usuários administradores
- `src/domain/services/DashboardService.ts` - Interface do serviço de dashboard
- `src/domain/usecases/admin/GetDashboardStatsUseCase.ts` - Caso de uso para obter estatísticas do dashboard
- `src/domain/usecases/admin/GetAdminUserUseCase.ts` - Caso de uso para obter usuário administrador
- `src/domain/usecases/admin/ListAdminUsersUseCase.ts` - Caso de uso para listar usuários administradores
- `src/infrastructure/services/PostgresDashboardService.ts` - Implementação do serviço de dashboard
- `src/pages/api/admin/dashboard/stats.ts` - API para obter estatísticas do dashboard
- `src/pages/admin/index.astro` - Página de dashboard administrativo
- `src/components/data/Dashboard.astro` - Componente de dashboard administrativo

### Gestão de Usuários Administrativos
- `src/domain/usecases/admin/CreateAdminUserUseCase.ts` - Caso de uso para criar usuário administrador
- `src/domain/usecases/admin/UpdateAdminUserUseCase.ts` - Caso de uso para atualizar usuário administrador
- `src/domain/usecases/admin/DeleteAdminUserUseCase.ts` - Caso de uso para excluir usuário administrador
- `src/domain/usecases/admin/ChangeAdminPasswordUseCase.ts` - Caso de uso para alterar senha de usuário administrador
- `src/domain/usecases/admin/AdminLoginUseCase.ts` - Caso de uso para autenticação de usuário administrador
- `src/domain/services/PasswordService.ts` - Interface do serviço de senhas
- `src/domain/services/TokenService.ts` - Interface do serviço de tokens
- `src/infrastructure/services/BcryptPasswordService.ts` - Implementação do serviço de senhas com bcrypt
- `src/infrastructure/services/JwtTokenService.ts` - Implementação do serviço de tokens com JWT
- `src/infrastructure/database/repositories/PostgresAdminUserRepository.ts` - Implementação do repositório de usuários administradores
- `src/pages/api/admin/auth/login.ts` - API para autenticação de usuários administradores
- `src/pages/api/admin/users/index.ts` - API para listagem e criação de usuários administradores
- `src/pages/api/admin/users/[id].ts` - API para obtenção, atualização e exclusão de usuário administrador
- `src/pages/api/admin/users/password.ts` - API para alteração de senha de usuário administrador

### Gestão de Conteúdo
- `src/domain/entities/Content.ts` - Entidade de conteúdo
- `src/domain/entities/ContentCategory.ts` - Entidade de categoria de conteúdo
- `src/domain/entities/ContentTag.ts` - Entidade de tag de conteúdo
- `src/domain/repositories/ContentRepository.ts` - Interface do repositório de conteúdo
- `src/domain/repositories/ContentCategoryRepository.ts` - Interface do repositório de categorias de conteúdo
- `src/domain/repositories/ContentTagRepository.ts` - Interface do repositório de tags de conteúdo
- `src/domain/usecases/content/CreateContentUseCase.ts` - Caso de uso para criar conteúdo
- `src/domain/usecases/content/UpdateContentUseCase.ts` - Caso de uso para atualizar conteúdo
- `src/domain/usecases/content/UpdateContentStatusUseCase.ts` - Caso de uso para atualizar status de conteúdo
- `src/domain/usecases/content/GetContentVersionsUseCase.ts` - Caso de uso para obter versões de conteúdo
- `src/domain/usecases/content/RestoreContentVersionUseCase.ts` - Caso de uso para restaurar versão de conteúdo
- `src/domain/services/SlugService.ts` - Interface do serviço de slugs
- `src/infrastructure/services/DefaultSlugService.ts` - Implementação do serviço de slugs
- `src/pages/api/admin/content/index.ts` - API para listagem e criação de conteúdo
- `src/pages/api/admin/content/[id].ts` - API para obtenção, atualização e exclusão de conteúdo
- `src/pages/api/admin/content/versions/[contentId].ts` - API para gerenciamento de versões de conteúdo

### Otimização para Buscadores (SEO)
- `src/domain/entities/SEO.ts` - Entidade de configurações de SEO
- `src/domain/entities/Sitemap.ts` - Entidade de sitemap
- `src/domain/repositories/SitemapRepository.ts` - Interface do repositório de sitemaps
- `src/domain/services/SEOService.ts` - Interface do serviço de SEO
- `src/domain/services/SitemapService.ts` - Interface do serviço de sitemap
- `src/infrastructure/services/DefaultSEOService.ts` - Implementação do serviço de SEO
- `src/infrastructure/services/DefaultSitemapService.ts` - Implementação do serviço de sitemap
- `src/infrastructure/database/repositories/FileSitemapRepository.ts` - Implementação do repositório de sitemaps usando arquivos
- `src/components/SEOHead.astro` - Componente para gerenciar meta tags de SEO
- `src/pages/sitemap.xml.ts` - Endpoint para gerar o sitemap XML
- `src/pages/sitemap-index.xml.ts` - Endpoint para gerar o índice de sitemaps XML
- `src/pages/robots.txt.ts` - Endpoint para gerar o arquivo robots.txt
- `src/pages/api/admin/seo/index.ts` - API para gerenciamento de configurações de SEO

### Integração com Redes Sociais
- `src/domain/services/SocialMediaService.ts` - Interface do serviço de redes sociais
- `src/domain/services/SocialSharingService.ts` - Interface do serviço de compartilhamento em redes sociais
- `src/domain/services/SocialAuthService.ts` - Interface do serviço de autenticação com redes sociais
- `src/infrastructure/services/DefaultSocialSharingService.ts` - Implementação do serviço de compartilhamento em redes sociais
- `src/infrastructure/services/OAuthSocialAuthService.ts` - Implementação do serviço de autenticação social usando OAuth
- `src/components/social/SocialShareButtons.astro` - Componente para exibir botões de compartilhamento em redes sociais
- `src/components/social/SocialLoginButtons.astro` - Componente para exibir botões de login com redes sociais
- `src/components/social/SocialFeed.astro` - Componente para exibir feed de redes sociais
- `src/pages/api/auth/social/callback/[provider].ts` - API para processar o retorno da autenticação social
- `src/pages/api/social/share-count.ts` - API para obter contagem de compartilhamentos em redes sociais
- `src/pages/api/social/embed.ts` - API para obter código de incorporação de posts de redes sociais
- `src/pages/api/social/integrations.ts` - API para gerenciar integrações com redes sociais

### Analytics
- `src/domain/services/AnalyticsService.ts` - Interface do serviço de analytics
- `src/infrastructure/services/GoogleAnalyticsService.ts` - Implementação do serviço de analytics usando Google Analytics
- `src/components/analytics/AnalyticsScript.astro` - Componente para incluir o script de analytics nas páginas
- `src/components/analytics/EventTracker.astro` - Componente para rastrear eventos específicos
- `src/components/analytics/ConversionTracker.astro` - Componente para rastrear conversões
- `src/components/analytics/EcommerceTracker.astro` - Componente para rastrear transações de e-commerce
- `src/pages/api/analytics/event.ts` - API para rastrear eventos de analytics no lado do servidor
- `src/pages/api/analytics/pageview.ts` - API para rastrear visualizações de página de analytics no lado do servidor
- `src/pages/api/analytics/conversion.ts` - API para rastrear conversões de analytics no lado do servidor

### Marketing Digital
- `src/domain/services/RemarketingService.ts` - Interface do serviço de remarketing
- `src/domain/services/ABTestingService.ts` - Interface do serviço de testes A/B
- `src/domain/services/LandingPageService.ts` - Interface do serviço de landing pages
- `src/infrastructure/services/DefaultRemarketingService.ts` - Implementação do serviço de remarketing
- `src/infrastructure/services/SimpleABTestingService.ts` - Implementação do serviço de testes A/B
- `src/components/marketing/RemarketingPixel.astro` - Componente para incluir pixels de remarketing nas páginas
- `src/components/marketing/ABTestVariant.astro` - Componente para exibir uma variante de teste A/B
- `src/components/marketing/ConversionTracker.astro` - Componente para rastrear conversões em testes A/B e remarketing
- `src/pages/api/marketing/remarketing/track-event.ts` - API para rastrear eventos de remarketing no lado do servidor
- `src/pages/api/marketing/ab-testing/assign-variant.ts` - API para atribuir uma variante de teste A/B a um usuário
- `src/pages/api/marketing/ab-testing/track-conversion.ts` - API para rastrear conversões em testes A/B

### Testes Unitários
- `vitest.config.ts` - Configuração do Vitest para testes unitários
- `tests/setup.ts` - Configuração global para testes
- `tests/unit/domain/services/DefaultSocialSharingService.test.ts` - Testes para o serviço DefaultSocialSharingService
- `tests/unit/domain/services/GoogleAnalyticsService.test.ts` - Testes para o serviço GoogleAnalyticsService
- `tests/unit/domain/services/DefaultRemarketingService.test.ts` - Testes para o serviço DefaultRemarketingService
- `tests/unit/domain/services/SimpleABTestingService.test.ts` - Testes para o serviço SimpleABTestingService
- `tests/unit/domain/services/OAuthSocialAuthService.test.ts` - Testes para o serviço OAuthSocialAuthService
- `tests/unit/domain/utils/StringUtils.test.ts` - Testes para as funções de utilidade de strings

### Testes de Integração
- `tests/integration/playwright.config.ts` - Configuração do Playwright para testes de integração
- `tests/integration/api/auth.api.spec.ts` - Testes de integração para API de autenticação
- `tests/integration/database/user-repository.database.spec.ts` - Testes de integração para o repositório de usuários
- `tests/integration/services/payment.services.spec.ts` - Testes de integração para serviços de pagamento
- `tests/integration/user-flows/checkout.user-flows.spec.ts` - Testes de integração para fluxos de checkout
- `tests/integration/user-flows/auth.user-flows.spec.ts` - Testes de integração para fluxos de autenticação

### Testes de Desempenho
- `tests/performance/k6.config.js` - Configuração do K6 para testes de carga
- `tests/performance/scenarios/load-test.js` - Teste de carga para API e páginas principais
- `tests/performance/scenarios/stress-test.js` - Teste de estresse para API e páginas principais
- `tests/performance/scenarios/api-benchmark.js` - Benchmark de APIs críticas
- `tests/performance/scenarios/spike-test.js` - Teste de pico para API e páginas principais
- `tests/performance/scenarios/soak-test.js` - Teste de resistência para API e páginas principais
- `tests/performance/README.md` - Documentação dos testes de desempenho

### Testes de Segurança
- `tests/security/README.md` - Documentação dos testes de segurança
- `tests/security/static-analysis/eslint-security.js` - Análise estática de código com ESLint
- `tests/security/penetration/owasp-zap-runner.js` - Testes de penetração com OWASP ZAP
- `tests/security/dependency-check/dependency-scanner.js` - Verificação de vulnerabilidades em dependências
- `tests/security/scripts/security-test-runner.js` - Script principal para execução de testes de segurança
- `tests/security/config/sonarqube.properties` - Configuração do SonarQube para análise estática
- `tests/security/config/zap-config.yaml` - Configuração do OWASP ZAP para testes de penetração
- `tests/security/config/dependency-check.json` - Configuração do Dependency-Check
- `docs/SEGURANCA.md` - Documentação das práticas de segurança implementadas

### Clean Architecture
- `docs/CLEAN_ARCHITECTURE.md` - Documentação da Clean Architecture
- `src/domain/entities/` - Entidades de domínio (User, Product, EducationalContent)
- `src/domain/repositories/` - Interfaces de repositórios
- `src/application/usecases/` - Casos de uso
- `src/application/interfaces/` - Interfaces de serviços
- `src/adapters/controllers/` - Controladores
- `src/adapters/repositories/` - Implementações de repositórios
- `src/infrastructure/cache/` - Implementação de cache com valkey

### Estratégias de Carregamento
- `docs/LOADING_STRATEGIES.md` - Documentação das estratégias de carregamento
- `src/components/OptimizedImage.astro` - Componente de imagem otimizada
- `src/utils/performance.ts` - Utilitários de performance
- `src/layouts/OptimizedLayout.astro` - Layout otimizado para performance
- `src/styles/critical/` - CSS crítico para carregamento prioritário

### Otimização de Assets
- `docs/ASSET_OPTIMIZATION.md` - Documentação das estratégias de otimização de assets
- `astro.config.mjs` - Configuração do Astro com plugins de otimização
- `scripts/optimize-images.js` - Script para otimização de imagens
- `scripts/optimize-fonts.js` - Script para otimização de fontes
- `scripts/analyze-bundle.js` - Script para análise de bundle
- `package.json` - Scripts de build e otimização

### Métricas de Performance
- `docs/PERFORMANCE_METRICS.md` - Documentação das métricas de performance
- `src/utils/performance-monitoring.ts` - Utilitário para monitoramento de performance
- `src/services/metrics-service.ts` - Serviço para armazenamento e análise de métricas
- `src/pages/api/metrics.ts` - Endpoint para receber métricas do cliente
- `src/components/admin/PerformanceChart.astro` - Componente para visualização de métricas
- `src/pages/admin/performance-dashboard.astro` - Dashboard de performance
- `lighthouse-budget.json` - Configuração de orçamento de performance
- `.github/workflows/lighthouse.yml` - Configuração do Lighthouse CI

## Notas para Continuação

1. **Documentação técnica**:
   - ✅ Criar documentação da API
   - ✅ Implementar documentação de código
   - ✅ Desenvolver guias para novos desenvolvedores
   - ✅ Documentar arquitetura e padrões de código

2. **Documentação de usuário**:
   - ✅ Criar guias de uso para professores
   - ✅ Implementar tutoriais interativos
   - ✅ Desenvolver FAQ e base de conhecimento
   - ✅ Documentar fluxos de uso comuns

3. **CI/CD**:
   - ✅ Configurar pipeline de integração contínua
   - ✅ Implementar deploy automatizado
   - ✅ Configurar ambientes de teste
   - ✅ Implementar testes automatizados

4. **Considerações de Design**:
   - Manter a consistência com a abordagem Zero-JS
   - Seguir a paleta de cores do tema educacional
   - Evitar referências a personagens específicos
   - Garantir acessibilidade em todos os componentes

## Documentação Existente (Consultar Antes da Implementação)

A seguinte documentação já existe no projeto e deve ser consultada antes de iniciar qualquer onda de implementação de tarefas:

### Documentação de Responsividade
- `docs/guides/responsive-design.md` - Guia de boas práticas de design responsivo
- `tests/responsive/README.md` - Documentação dos testes de responsividade
- `tests/responsive/checklist.md` - Checklist para verificação manual de responsividade

### Documentação Técnica
- `docs/technical/README.md` - Visão geral da documentação técnica
- `docs/technical/api.md` - Documentação da API
- `docs/technical/components.md` - Documentação de componentes
- `docs/technical/guides/creating-components.md` - Guia para criação de componentes Astro
- `docs/technical/guides/implementing-use-cases.md` - Guia para implementação de casos de uso

### Documentação de Arquitetura
- `docs/architecture/on-demand-rendering.md` - Implementação de On-Demand Rendering
- `docs/CLEAN_ARCHITECTURE.md` - Documentação da Clean Architecture
- `docs/ZERO_JS_ARCHITECTURE.md` - Documentação da arquitetura Zero-JS
- `docs/SERVER_ACTIONS.md` - Documentação da arquitetura de comunicação server-side
- `docs/LOADING_STRATEGIES.md` - Documentação das estratégias de carregamento
- `docs/ASSET_OPTIMIZATION.md` - Documentação das estratégias de otimização de assets
- `docs/PERFORMANCE_METRICS.md` - Documentação das métricas de performance
- `docs/ACESSIBILIDADE.md` - Diretrizes de acessibilidade
- `docs/SOLID_PRINCIPLES.md` - Documentação dos princípios SOLID aplicados
- `docs/DEPENDENCY_RULES.md` - Regras de dependência entre camadas
- `docs/ARCHITECTURE_PATTERNS.md` - Padrões arquiteturais adotados
- `docs/CLEAN_ARCHITECTURE_QUICK_REFERENCE.md` - Guia de referência rápida para Clean Architecture
- `docs/DEPENDENCY_LINTING.md` - Configuração de linting para validar dependências
- `docs/NAMING_CONVENTIONS.md` - Convenções de nomenclatura
- `docs/DIRECTORY_STRUCTURE.md` - Estrutura de diretórios
- `docs/SERVER_SIDE_ALTERNATIVES.md` - Alternativas server-side para funcionalidades interativas
- `docs/ZERO_JS_VALIDATION.md` - Validação de ausência de hydration
- `docs/ASTRO_ACTIONS_GUIDE.md` - Guia de implementação de Astro Actions
- `docs/CACHING_STRATEGIES.md` - Estratégias de cache
- `docs/LAZY_LOADING_GUIDE.md` - Guia de implementação de lazy loading
- `docs/CODE_SPLITTING_GUIDE.md` - Estratégias de code splitting
- `docs/BUNDLE_OPTIMIZATION_GUIDE.md` - Guia de otimização de bundle

### Documentação de Usuário
- `docs/user/manual.md` - Manual completo do usuário
- `docs/user/help-system.md` - Documentação do sistema de ajuda contextual
- `docs/user/faqs.md` - Perguntas frequentes (FAQs)

### Documentação de Processos
- `docs/processes/operations.md` - Documentação de operações
- `docs/processes/contingency-plans.md` - Planos de contingência
- `docs/processes/development-workflow.md` - Fluxo de trabalho de desenvolvimento

### Documentação de DevOps
- `docs/devops/ci-cd-pipeline.md` - Documentação do pipeline de CI/CD

### Documentação de Infraestrutura
- `docs/infrastructure/production-environment.md` - Ambiente de produção
- `docs/infrastructure/load-balancing.md` - Balanceamento de carga
- `docs/infrastructure/backup-strategy.md` - Estratégia de backup e recuperação
- `scripts/backup/database_backup.sh` - Script de backup do banco de dados
- `scripts/backup/files_backup.sh` - Script de backup de arquivos
- `scripts/infrastructure/setup_production_server.sh` - Configuração de servidores de produção
- `scripts/infrastructure/setup_load_balancer.sh` - Configuração de balanceamento de carga

### Documentação de Kafka
- `docs/kafka-topics-structure.md` - Estrutura de tópicos do Kafka
- `docs/kafka-replication-partitioning.md` - Replicação e particionamento do Kafka

## Métricas e Objetivos

- Manter o tamanho total do JavaScript enviado ao cliente abaixo de 10KB
- Garantir que todas as páginas carreguem em menos de 2 segundos
- Manter pontuação de Lighthouse acima de 90 em todas as categorias
- Garantir que a aplicação funcione completamente sem JavaScript

## Comandos Úteis

```bash
# Iniciar servidor de desenvolvimento
npm run dev

# Construir para produção
npm run build

# Construir e analisar bundle
npm run build:analyze

# Otimizar assets
npm run optimize:assets

# Otimizar imagens
npm run optimize:images

# Otimizar fontes
npm run optimize:fonts

# Analisar bundle
npm run analyze:bundle

# Visualizar build de produção
npm run preview

# Executar testes
npm run test

# Executar testes de performance
npm run test:performance

# Executar Lighthouse (desktop)
npm run lighthouse:desktop

# Executar Lighthouse (mobile)
npm run lighthouse:mobile
```

---

## Regras para Atualização de Status das Tarefas

Para garantir consistência entre os status das tarefas pais e suas subtarefas, as seguintes regras devem ser seguidas:

### Regras para Uso de Comandos de Terminal

Como o projeto está sendo desenvolvido em ambiente Windows, é importante seguir estas diretrizes ao usar comandos de terminal:

1. **REGRA OBRIGATÓRIA: Usar APENAS Comandos do Windows CMD**:
   - É OBRIGATÓRIO utilizar SOMENTE comandos do Windows CMD/PowerShell
   - É PROIBIDO utilizar comandos do tipo Linux/Unix/Bash
   - Utilizar `mkdir` sem a flag `-p` (não suportada no Windows): `mkdir diretorio\subdiretorio`
   - Para criar múltiplos diretórios, usar comandos separados:
     ```
     mkdir diretorio
     mkdir diretorio\subdiretorio
     ```
   - Usar `dir` em vez de `ls` para listar arquivos: `dir diretorio`
   - Usar `type` em vez de `cat` para exibir conteúdo de arquivos: `type arquivo.txt`
   - Usar `copy` em vez de `cp` para copiar arquivos: `copy origem.txt destino.txt`
   - Usar `move` em vez de `mv` para mover arquivos: `move origem.txt destino\`
   - Usar `del` em vez de `rm` para excluir arquivos: `del arquivo.txt`
   - Usar `rmdir /s /q` para excluir diretórios: `rmdir /s /q diretorio`
   - Usar `findstr` em vez de `grep` para buscar texto: `findstr "texto" arquivo.txt`
   - Usar `fc` em vez de `diff` para comparar arquivos: `fc arquivo1.txt arquivo2.txt`
   - Usar `echo.>arquivo.txt` em vez de `touch` para criar arquivos vazios

2. **Caminhos de Arquivos**:
   - Usar barras invertidas (`\`) em vez de barras normais (`/`) para caminhos: `diretorio\arquivo.txt`
   - Evitar caracteres especiais em nomes de arquivos e diretórios

3. **Execução de Scripts**:
   - Para scripts Node.js, usar: `node scripts\nome-do-script.js`
   - Para scripts npm, usar conforme definido no package.json: `npm run nome-do-script`

Estas regras garantem que os comandos funcionem corretamente no ambiente Windows e evitam erros comuns relacionados a diferenças entre sistemas operacionais.

1. **Status "done" (concluído)**:
   - Uma tarefa pai só deve receber o status "done" quando TODAS as suas subtarefas estiverem com status "done"
   - Verificar se todas as subtarefas foram realmente implementadas antes de marcar a tarefa pai como concluída
   - Atualizar o arquivo TAREFAS.md e o Checkpoint.md simultaneamente

2. **Status "in_progress" (em andamento)**:
   - Uma tarefa pai deve receber o status "in_progress" quando PELO MENOS UMA de suas subtarefas estiver com status "done" ou "in_progress", e pelo menos uma subtarefa ainda não estiver concluída
   - Este status indica que o trabalho na tarefa foi iniciado, mas ainda não foi concluído

3. **Status "todo" (a fazer)**:
   - Uma tarefa pai deve ter o status "todo" quando TODAS as suas subtarefas estiverem com status "todo"
   - Este status indica que o trabalho na tarefa ainda não foi iniciado

4. **Verificação de Consistência**:
   - Antes de cada commit, verificar se os status das tarefas pais estão consistentes com os status de suas subtarefas
   - Se houver inconsistências, ajustar os status de acordo com as regras acima
   - Garantir que o Checkpoint.md reflita o status atual das tarefas no TAREFAS.md

5. **Atualização Automática**:
   - Ao alterar o status de uma subtarefa, verificar se é necessário atualizar o status da tarefa pai
   - Ao concluir a última subtarefa pendente, atualizar automaticamente o status da tarefa pai para "done"
   - Ao iniciar a primeira subtarefa de uma tarefa, atualizar automaticamente o status da tarefa pai para "in_progress"

## Instruções para Próximas Ondas de Implementação

Antes de iniciar uma nova onda de implementação de tarefas:

1. **Consultar Documentação Existente**:
   - Revisar a seção "Documentação Existente" deste checkpoint
   - Verificar se há documentação relevante para as tarefas a serem implementadas
   - Não recriar documentação que já existe, apenas complementar ou atualizar se necessário

2. **Atualizar Checkpoint**:
   - Após cada onda de implementação, atualizar este checkpoint
   - Adicionar novas documentações criadas à seção "Documentação Existente"
   - Atualizar o status das tarefas concluídas seguindo as regras de atualização de status

3. **Manter Consistência**:
   - Seguir os padrões e convenções estabelecidos na documentação existente
   - Garantir que novas implementações sejam compatíveis com a arquitetura atual
   - Respeitar as diretrizes de design e acessibilidade

4. **Verificar Arquivos Existentes**:
   - Antes de criar um novo arquivo no projeto, verificar a existência do mesmo
   - Consultar a seção "Arquivos Criados/Modificados" para evitar duplicação
   - Se o arquivo já existir, verificar seu conteúdo antes de modificá-lo

5. **Ordem de Leitura de Arquivos**:
   - Ler o arquivo Checkpoint.md primeiro para entender o estado atual do projeto
   - Em seguida, ler o arquivo TAREFAS.md para obter detalhes sobre as tarefas pendentes
   - Usar ambas as informações para planejar a próxima onda de implementação

6. **Tamanho das Ondas de Implementação**:
   - Cada onda de implementação deve conter exatamente 10 tarefas
   - Quando for solicitada uma nova onda de implementação, selecionar sempre 10 tarefas
   - Priorizar tarefas em andamento e tarefas com dependências já satisfeitas

Este checkpoint foi criado para facilitar a retomada do desenvolvimento quando o computador for ligado novamente. Ele contém o estado atual da implementação, as próximas tarefas a serem realizadas, a documentação existente e considerações importantes para o desenvolvimento.
