/**
 * Transformadores para eventos de pagamento
 *
 * Este arquivo define transformadores para converter eventos de pagamento
 * entre diferentes formatos e versões.
 */

import { MessageTransformer } from '@services/messageProcessingService';
import { FieldMappingTransformer } from '@services/messageTransformationService';
import { logger } from '@utils/logger';

/**
 * Transformador para converter eventos de pagamento legados para o novo formato
 */
export class LegacyToNewPaymentTransformer implements MessageTransformer {
  /**
   * Transforma um evento de pagamento legado para o novo formato
   * @param message - Evento de pagamento legado
   * @returns Evento de pagamento no novo formato
   */
  transform(message: Record<string, unknown>): Record<string, unknown> {
    try {
      // Mapear campos do formato legado para o novo formato
      return {
        paymentId: message.id || message.payment_id,
        orderId: message.order_id,
        userId: message.user_id,
        value: message.amount || message.value,
        paymentType: message.payment_method || message.payment_type,
        status: this.mapStatus(message.payment_status || message.status),
        externalId: message.external_reference || message.external_id,
        timestamp: message.timestamp || new Date().toISOString(),
        version: '1.0',
        metadata: {
          ...(message.additional_data || message.metadata || {}),
          originalFormat: 'legacy',
        },
      };
    } catch (error) {
      logger.error('Erro ao transformar evento de pagamento legado:', error);
      throw error;
    }
  }

  /**
   * Mapeia status legado para o novo formato
   * @param status - Status legado
   * @returns Status no novo formato
   */
  private mapStatus(status: unknown): string {
    if (!status) return 'unknown';

    const statusStr = String(status).toLowerCase();

    switch (statusStr) {
      case '1':
      case 'pending':
      case 'waiting':
        return 'pending';

      case '2':
      case 'approved':
      case 'paid':
      case 'completed':
        return 'approved';

      case '3':
      case 'rejected':
      case 'denied':
      case 'failed':
        return 'rejected';

      case '4':
      case 'refunded':
      case 'chargeback':
        return 'refunded';

      case '5':
      case 'cancelled':
      case 'canceled':
        return 'cancelled';

      default:
        return statusStr;
    }
  }
}

/**
 * Transformador para converter eventos de pagamento do formato Efí Pay para o formato interno
 */
export class EfiPayToInternalTransformer implements MessageTransformer {
  /**
   * Transforma um evento de pagamento do Efí Pay para o formato interno
   * @param message - Evento de pagamento do Efí Pay
   * @returns Evento de pagamento no formato interno
   */
  transform(message: Record<string, unknown>): Record<string, unknown> {
    try {
      // Extrair dados do webhook
      const webhookData = message.data || {};
      const pixData = (webhookData as any).pix || {};
      const status = this.mapEfiPayStatus((webhookData as any).status);

      // Mapear campos do formato Efí Pay para o formato interno
      return {
        paymentId:
          message.paymentId || (webhookData as any).identifier || (webhookData as any).txid,
        orderId: message.orderId || (webhookData as any).correlationID,
        value: pixData.value || (webhookData as any).value || 0,
        paymentType: 'pix',
        status,
        externalId: (webhookData as any).txid || (webhookData as any).charge_id,
        timestamp: new Date().toISOString(),
        version: '1.0',
        metadata: {
          webhookEvent: message.event,
          webhookData,
          provider: 'efipay',
          receivedAt: message.receivedAt || new Date().toISOString(),
        },
      };
    } catch (error) {
      logger.error('Erro ao transformar evento de pagamento do Efí Pay:', error);
      throw error;
    }
  }

  /**
   * Mapeia status do Efí Pay para o formato interno
   * @param status - Status do Efí Pay
   * @returns Status no formato interno
   */
  private mapEfiPayStatus(status: string): string {
    if (!status) return 'unknown';

    const statusStr = String(status).toUpperCase();

    switch (statusStr) {
      case 'WAITING':
      case 'PENDING':
        return 'pending';

      case 'CONFIRMED':
      case 'COMPLETED':
      case 'APPROVED':
        return 'approved';

      case 'DENIED':
      case 'FAILED':
      case 'MANUAL_REVIEW':
        return 'rejected';

      case 'REFUNDED':
      case 'CHARGEBACK':
        return 'refunded';

      case 'CANCELED':
      case 'CANCELLED':
        return 'cancelled';

      default:
        return 'unknown';
    }
  }
}

/**
 * Transformador para converter eventos de pagamento do formato interno para o formato de notificação
 */
export class InternalToNotificationTransformer implements MessageTransformer {
  /**
   * Transforma um evento de pagamento interno para o formato de notificação
   * @param message - Evento de pagamento interno
   * @returns Evento de pagamento no formato de notificação
   */
  transform(message: Record<string, unknown>): Record<string, unknown> {
    try {
      // Mapear campos do formato interno para o formato de notificação
      return {
        notificationType: 'payment',
        notificationId: crypto.randomUUID(),
        paymentId: message.paymentId,
        orderId: message.orderId,
        userId: message.userId,
        status: message.status,
        statusLabel: this.getStatusLabel(String(message.status)),
        amount: message.value,
        formattedAmount: this.formatCurrency(Number(message.value)),
        paymentMethod: message.paymentType,
        paymentMethodLabel: this.getPaymentMethodLabel(String(message.paymentType)),
        timestamp: message.timestamp || new Date().toISOString(),
        metadata: message.metadata,
      };
    } catch (error) {
      logger.error('Erro ao transformar evento de pagamento para notificação:', error);
      throw error;
    }
  }

  /**
   * Obtém rótulo para status
   * @param status - Status
   * @returns Rótulo do status
   */
  private getStatusLabel(status: string): string {
    switch (status) {
      case 'pending':
        return 'Pendente';
      case 'approved':
        return 'Aprovado';
      case 'rejected':
        return 'Rejeitado';
      case 'refunded':
        return 'Reembolsado';
      case 'cancelled':
        return 'Cancelado';
      default:
        return 'Desconhecido';
    }
  }

  /**
   * Obtém rótulo para método de pagamento
   * @param method - Método de pagamento
   * @returns Rótulo do método de pagamento
   */
  private getPaymentMethodLabel(method: string): string {
    switch (method) {
      case 'pix':
        return 'PIX';
      case 'credit_card':
        return 'Cartão de Crédito';
      case 'billet':
        return 'Boleto';
      default:
        return method;
    }
  }

  /**
   * Formata valor como moeda
   * @param value - Valor
   * @returns Valor formatado
   */
  private formatCurrency(value: number): string {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
    }).format(value);
  }
}

// Registrar transformadores usando o serviço de transformação
import { messageTransformationService } from '@services/messageTransformationService';

// Transformador de campo para formato legado para novo
const legacyToNewFieldMapping = new FieldMappingTransformer({
  id: 'paymentId',
  payment_id: 'paymentId',
  order_id: 'orderId',
  user_id: 'userId',
  amount: 'value',
  payment_method: 'paymentType',
  payment_status: 'status',
  external_reference: 'externalId',
  additional_data: 'metadata',
});

// Registrar transformadores
messageTransformationService.registerTransformer(
  'payment.legacy_to_new',
  new LegacyToNewPaymentTransformer()
);

messageTransformationService.registerTransformer(
  'payment.efipay_to_internal',
  new EfiPayToInternalTransformer()
);

messageTransformationService.registerTransformer(
  'payment.internal_to_notification',
  new InternalToNotificationTransformer()
);

messageTransformationService.registerTransformer(
  'payment.field_mapping.legacy_to_new',
  legacyToNewFieldMapping
);
