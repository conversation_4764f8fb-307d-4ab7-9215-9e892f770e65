---
import ColorPalette from '../../components/ui/ColorPalette.astro';
import DaisyButton from '../../components/ui/DaisyButton.astro';
import ThemeSwitcher from '../../components/ui/ThemeSwitcher.astro';
import BaseLayout from '../../layouts/BaseLayout.astro';

// Importar configuração de temas
import themeConfig from '../../themes/themeConfig';

const title = 'Esquema de Cores';

// Obter temas disponíveis
const themes = Object.keys(themeConfig.themes);
---

<BaseLayout title={title}>
  <div class="container mx-auto px-4 py-8">
    <h1 class="text-4xl font-bold mb-8 text-center">{title}</h1>
    
    <section class="mb-12">
      <div class="flex justify-between items-center mb-4">
        <h2 class="text-2xl font-bold">Paleta de Cores</h2>
        <ThemeSwitcher position="dropdown" />
      </div>
      
      <div class="card p-6 mb-8">
        <p class="mb-4">
          O esquema de cores do projeto Estação da Alfabetização é inspirado nas cores vibrantes e alegres da Turma da Mônica,
          adaptado para o contexto educacional. Utilizamos as cores características, mas sem fazer referência direta aos personagens.
        </p>
        
        <p>
          Selecione diferentes temas no seletor acima para ver como as cores se adaptam a cada tema.
        </p>
      </div>
      
      <ColorPalette />
    </section>
    
    <section class="mb-12">
      <h2 class="text-2xl font-bold mb-4">Uso das Cores</h2>
      
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div class="card p-6">
          <h3 class="text-xl font-bold mb-4">Botões</h3>
          
          <div class="flex flex-wrap gap-2 mb-4">
            <DaisyButton variant="primary">Primário</DaisyButton>
            <DaisyButton variant="secondary">Secundário</DaisyButton>
            <DaisyButton variant="accent">Acento</DaisyButton>
            <DaisyButton variant="neutral">Neutro</DaisyButton>
          </div>
          
          <div class="flex flex-wrap gap-2 mb-4">
            <DaisyButton variant="info">Info</DaisyButton>
            <DaisyButton variant="success">Sucesso</DaisyButton>
            <DaisyButton variant="warning">Alerta</DaisyButton>
            <DaisyButton variant="error">Erro</DaisyButton>
          </div>
          
          <div class="flex flex-wrap gap-2">
            <DaisyButton variant="primary" outline>Outline</DaisyButton>
            <DaisyButton variant="secondary" outline>Outline</DaisyButton>
            <DaisyButton variant="accent" outline>Outline</DaisyButton>
            <DaisyButton variant="neutral" outline>Outline</DaisyButton>
          </div>
        </div>
        
        <div class="card p-6">
          <h3 class="text-xl font-bold mb-4">Badges</h3>
          
          <div class="flex flex-wrap gap-2 mb-4">
            <div class="badge badge-primary">Primário</div>
            <div class="badge badge-secondary">Secundário</div>
            <div class="badge badge-accent">Acento</div>
            <div class="badge badge-neutral">Neutro</div>
          </div>
          
          <div class="flex flex-wrap gap-2 mb-4">
            <div class="badge badge-info">Info</div>
            <div class="badge badge-success">Sucesso</div>
            <div class="badge badge-warning">Alerta</div>
            <div class="badge badge-error">Erro</div>
          </div>
          
          <div class="flex flex-wrap gap-2">
            <div class="badge badge-outline badge-primary">Outline</div>
            <div class="badge badge-outline badge-secondary">Outline</div>
            <div class="badge badge-outline badge-accent">Outline</div>
            <div class="badge badge-outline badge-neutral">Outline</div>
          </div>
        </div>
      </div>
    </section>
    
    <section class="mb-12">
      <h2 class="text-2xl font-bold mb-4">Alertas e Mensagens</h2>
      
      <div class="grid grid-cols-1 gap-4">
        <div class="alert alert-info">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" class="stroke-current shrink-0 w-6 h-6"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>
          <span>Informação: Esta é uma mensagem informativa.</span>
        </div>
        
        <div class="alert alert-success">
          <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
          <span>Sucesso: Sua ação foi concluída com sucesso!</span>
        </div>
        
        <div class="alert alert-warning">
          <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" /></svg>
          <span>Alerta: Atenção, verifique os dados antes de continuar.</span>
        </div>
        
        <div class="alert alert-error">
          <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
          <span>Erro: Ocorreu um erro ao processar sua solicitação.</span>
        </div>
      </div>
    </section>
    
    <section class="mb-12">
      <h2 class="text-2xl font-bold mb-4">Texto e Fundos</h2>
      
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div class="card p-6">
          <h3 class="text-xl font-bold mb-4">Cores de Texto</h3>
          
          <p class="text-base-content mb-2">Texto padrão (base-content)</p>
          <p class="text-primary mb-2">Texto primário</p>
          <p class="text-secondary mb-2">Texto secundário</p>
          <p class="text-accent mb-2">Texto de acento</p>
          <p class="text-neutral mb-2">Texto neutro</p>
          <p class="text-info mb-2">Texto informativo</p>
          <p class="text-success mb-2">Texto de sucesso</p>
          <p class="text-warning mb-2">Texto de alerta</p>
          <p class="text-error">Texto de erro</p>
        </div>
        
        <div class="card p-6">
          <h3 class="text-xl font-bold mb-4">Cores de Fundo</h3>
          
          <div class="p-2 mb-2 rounded bg-base-100 text-base-content">Fundo base-100</div>
          <div class="p-2 mb-2 rounded bg-base-200 text-base-content">Fundo base-200</div>
          <div class="p-2 mb-2 rounded bg-base-300 text-base-content">Fundo base-300</div>
          <div class="p-2 mb-2 rounded bg-primary text-primary-content">Fundo primário</div>
          <div class="p-2 mb-2 rounded bg-secondary text-secondary-content">Fundo secundário</div>
          <div class="p-2 mb-2 rounded bg-accent text-accent-content">Fundo de acento</div>
          <div class="p-2 mb-2 rounded bg-neutral text-neutral-content">Fundo neutro</div>
        </div>
      </div>
    </section>
    
    <section class="mb-12">
      <h2 class="text-2xl font-bold mb-4">Documentação</h2>
      
      <div class="card p-6">
        <p class="mb-4">
          Para mais detalhes sobre o esquema de cores, consulte a documentação completa em:
        </p>
        
        <a href="/docs/COLOR_SCHEME.md" class="link link-primary">COLOR_SCHEME.md</a>
      </div>
    </section>
  </div>
</BaseLayout>
