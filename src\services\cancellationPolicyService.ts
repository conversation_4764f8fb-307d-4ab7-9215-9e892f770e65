/**
 * Serviço para gerenciamento de políticas de cancelamento
 */

import { queryHelper } from '@db/queryHelper';
import { logger } from '@utils/logger';
import { PaymentStatus } from './paymentService';
import { RefundStatus } from './refundService';

/**
 * Enum para tipos de política de cancelamento
 */
export enum CancellationPolicyType {
  STANDARD = 'standard',
  DIGITAL_PRODUCT = 'digital_product',
  SUBSCRIPTION = 'subscription',
  CUSTOM = 'custom',
}

/**
 * Enum para motivos de cancelamento
 */
export enum CancellationReason {
  CUSTOMER_REQUEST = 'customer_request',
  PAYMENT_ISSUE = 'payment_issue',
  PRODUCT_UNAVAILABLE = 'product_unavailable',
  DUPLICATE_ORDER = 'duplicate_order',
  FRAUD_SUSPICION = 'fraud_suspicion',
  OTHER = 'other',
}

/**
 * Interface para política de cancelamento
 */
export interface CancellationPolicy {
  ulid_policy: string;
  type: CancellationPolicyType;
  name: string;
  description: string;
  time_limit_hours: number;
  refund_percentage: number;
  requires_approval: boolean;
  auto_refund: boolean;
  active: boolean;
  created_at: Date;
  updated_at: Date;
}

/**
 * Interface para solicitação de cancelamento
 */
export interface CancellationRequest {
  orderId: string;
  reason: CancellationReason;
  details?: string;
  requestedBy: string;
}

/**
 * Interface para resultado de verificação de elegibilidade
 */
export interface EligibilityResult {
  eligible: boolean;
  policyId?: string;
  policyName?: string;
  refundPercentage?: number;
  timeLimit?: number;
  timeRemaining?: number;
  requiresApproval?: boolean;
  autoRefund?: boolean;
  reason?: string;
}

/**
 * Serviço de políticas de cancelamento
 */
export const cancellationPolicyService = {
  /**
   * Obtém todas as políticas de cancelamento ativas
   * @returns Lista de políticas de cancelamento
   */
  async getPolicies(): Promise<CancellationPolicy[]> {
    try {
      const result = await queryHelper.query(
        'SELECT * FROM tab_cancellation_policy WHERE active = true ORDER BY name'
      );

      return result.rows;
    } catch (error) {
      logger.error('Erro ao obter políticas de cancelamento:', error);
      return [];
    }
  },

  /**
   * Obtém uma política de cancelamento específica
   * @param policyId - ID da política
   * @returns Política de cancelamento ou null se não encontrada
   */
  async getPolicy(policyId: string): Promise<CancellationPolicy | null> {
    try {
      const result = await queryHelper.queryOne(
        'SELECT * FROM tab_cancellation_policy WHERE ulid_policy = $1',
        [policyId]
      );

      return result || null;
    } catch (error) {
      logger.error(`Erro ao obter política de cancelamento ${policyId}:`, error);
      return null;
    }
  },

  /**
   * Cria uma nova política de cancelamento
   * @param policy - Dados da política
   * @returns ID da política criada
   */
  async createPolicy(
    policy: Omit<CancellationPolicy, 'ulid_policy' | 'created_at' | 'updated_at'>
  ): Promise<string> {
    try {
      const result = await queryHelper.query(
        `INSERT INTO tab_cancellation_policy (
          ulid_policy,
          type,
          name,
          description,
          time_limit_hours,
          refund_percentage,
          requires_approval,
          auto_refund,
          active,
          created_at,
          updated_at
        ) VALUES (
          gen_ulid(),
          $1, $2, $3, $4, $5, $6, $7, $8, NOW(), NOW()
        ) RETURNING ulid_policy`,
        [
          policy.type,
          policy.name,
          policy.description,
          policy.time_limit_hours,
          policy.refund_percentage,
          policy.requires_approval,
          policy.auto_refund,
          policy.active,
        ]
      );

      return result.rows[0].ulid_policy;
    } catch (error) {
      logger.error('Erro ao criar política de cancelamento:', error);
      throw error;
    }
  },

  /**
   * Atualiza uma política de cancelamento existente
   * @param policyId - ID da política
   * @param policy - Dados atualizados da política
   * @returns Verdadeiro se a atualização foi bem-sucedida
   */
  async updatePolicy(
    policyId: string,
    policy: Partial<Omit<CancellationPolicy, 'ulid_policy' | 'created_at' | 'updated_at'>>
  ): Promise<boolean> {
    try {
      // Construir query dinâmica com base nos campos fornecidos
      const updateFields: string[] = [];
      const values: any[] = [];
      let paramIndex = 1;

      // Adicionar campos a serem atualizados
      if (policy.type !== undefined) {
        updateFields.push(`type = $${paramIndex++}`);
        values.push(policy.type);
      }

      if (policy.name !== undefined) {
        updateFields.push(`name = $${paramIndex++}`);
        values.push(policy.name);
      }

      if (policy.description !== undefined) {
        updateFields.push(`description = $${paramIndex++}`);
        values.push(policy.description);
      }

      if (policy.time_limit_hours !== undefined) {
        updateFields.push(`time_limit_hours = $${paramIndex++}`);
        values.push(policy.time_limit_hours);
      }

      if (policy.refund_percentage !== undefined) {
        updateFields.push(`refund_percentage = $${paramIndex++}`);
        values.push(policy.refund_percentage);
      }

      if (policy.requires_approval !== undefined) {
        updateFields.push(`requires_approval = $${paramIndex++}`);
        values.push(policy.requires_approval);
      }

      if (policy.auto_refund !== undefined) {
        updateFields.push(`auto_refund = $${paramIndex++}`);
        values.push(policy.auto_refund);
      }

      if (policy.active !== undefined) {
        updateFields.push(`active = $${paramIndex++}`);
        values.push(policy.active);
      }

      // Adicionar updated_at
      updateFields.push('updated_at = NOW()');

      // Se não houver campos para atualizar, retornar verdadeiro
      if (updateFields.length === 1) {
        return true;
      }

      // Adicionar ID da política aos valores
      values.push(policyId);

      // Executar query
      const result = await queryHelper.query(
        `UPDATE tab_cancellation_policy
         SET ${updateFields.join(', ')}
         WHERE ulid_policy = $${paramIndex}`,
        values
      );

      return result.rowCount > 0;
    } catch (error) {
      logger.error(`Erro ao atualizar política de cancelamento ${policyId}:`, error);
      return false;
    }
  },

  /**
   * Verifica a elegibilidade de um pedido para cancelamento
   * @param orderId - ID do pedido
   * @returns Resultado da verificação de elegibilidade
   */
  async checkCancellationEligibility(orderId: string): Promise<EligibilityResult> {
    try {
      // Buscar dados do pedido
      const order = await queryHelper.queryOne(
        `SELECT o.*, s.name as status_name, o.created_at
         FROM tab_order o
         JOIN tab_status s ON o.cod_status = s.cod_status
         WHERE o.ulid_order = $1`,
        [orderId]
      );

      if (!order) {
        return {
          eligible: false,
          reason: 'Pedido não encontrado',
        };
      }

      // Verificar se o pedido já foi cancelado
      if (order.cod_status === 5) {
        // Assumindo que 5 é o código para "Cancelado"
        return {
          eligible: false,
          reason: 'Pedido já cancelado',
        };
      }

      // Verificar se o pedido já foi entregue/concluído
      if (order.cod_status === 4) {
        // Assumindo que 4 é o código para "Concluído"
        return {
          eligible: false,
          reason: 'Pedido já concluído',
        };
      }

      // Buscar pagamentos associados ao pedido
      const payments = await queryHelper.query(
        `SELECT p.*, pt.type as payment_type, s.name as status_name
         FROM tab_payment p
         JOIN tab_payment_type pt ON p.ulid_payment_type = pt.ulid_payment_type
         JOIN tab_status s ON p.cod_status = s.cod_status
         WHERE p.ulid_order = $1`,
        [orderId]
      );

      // Verificar se há pagamentos aprovados
      const hasApprovedPayment = payments.rows.some((p) => p.cod_status === PaymentStatus.APPROVED);

      // Buscar itens do pedido para determinar o tipo de política
      const orderItems = await queryHelper.query(
        `SELECT oi.*, p.type as product_type
         FROM tab_order_item oi
         JOIN tab_product p ON oi.ulid_product = p.ulid_product
         WHERE oi.ulid_order = $1`,
        [orderId]
      );

      // Determinar o tipo de política com base nos produtos
      let policyType = CancellationPolicyType.STANDARD;

      // Se todos os produtos forem digitais, usar política de produto digital
      if (orderItems.rows.every((item) => item.product_type === 'digital')) {
        policyType = CancellationPolicyType.DIGITAL_PRODUCT;
      }

      // Se houver assinaturas, usar política de assinatura
      if (orderItems.rows.some((item) => item.product_type === 'subscription')) {
        policyType = CancellationPolicyType.SUBSCRIPTION;
      }

      // Buscar política aplicável
      const policy = await queryHelper.queryOne(
        `SELECT * FROM tab_cancellation_policy
         WHERE type = $1 AND active = true
         ORDER BY created_at DESC
         LIMIT 1`,
        [policyType]
      );

      if (!policy) {
        return {
          eligible: false,
          reason: 'Nenhuma política de cancelamento aplicável',
        };
      }

      // Calcular tempo decorrido desde a criação do pedido
      const orderCreatedAt = new Date(order.created_at);
      const currentTime = new Date();
      const hoursElapsed = (currentTime.getTime() - orderCreatedAt.getTime()) / (1000 * 60 * 60);

      // Verificar se está dentro do limite de tempo
      if (hoursElapsed > policy.time_limit_hours) {
        return {
          eligible: false,
          policyId: policy.ulid_policy,
          policyName: policy.name,
          timeLimit: policy.time_limit_hours,
          timeRemaining: 0,
          reason: `Prazo para cancelamento expirado (${policy.time_limit_hours} horas)`,
        };
      }

      // Calcular tempo restante
      const timeRemaining = policy.time_limit_hours - hoursElapsed;

      return {
        eligible: true,
        policyId: policy.ulid_policy,
        policyName: policy.name,
        refundPercentage: policy.refund_percentage,
        timeLimit: policy.time_limit_hours,
        timeRemaining,
        requiresApproval: policy.requires_approval,
        autoRefund: policy.auto_refund,
      };
    } catch (error) {
      logger.error(
        `Erro ao verificar elegibilidade para cancelamento do pedido ${orderId}:`,
        error
      );
      return {
        eligible: false,
        reason: 'Erro ao verificar elegibilidade',
      };
    }
  },

  /**
   * Solicita o cancelamento de um pedido
   * @param request - Dados da solicitação
   * @returns Resultado da solicitação
   */
  async requestCancellation(request: CancellationRequest): Promise<any> {
    try {
      // Verificar elegibilidade
      const eligibility = await this.checkCancellationEligibility(request.orderId);

      if (!eligibility.eligible) {
        throw new Error(`Pedido não elegível para cancelamento: ${eligibility.reason}`);
      }

      // Criar registro de solicitação de cancelamento
      const cancellationId = await this.createCancellationRecord(
        request.orderId,
        request.reason,
        request.details || '',
        request.requestedBy,
        eligibility.policyId!,
        eligibility.requiresApproval!
      );

      // Se não requer aprovação, processar cancelamento imediatamente
      if (!eligibility.requiresApproval) {
        await this.processCancellation(cancellationId, 'auto');
      }

      return {
        cancellationId,
        orderId: request.orderId,
        status: eligibility.requiresApproval ? 'pending_approval' : 'approved',
        requiresApproval: eligibility.requiresApproval,
        autoRefund: eligibility.autoRefund,
        refundPercentage: eligibility.refundPercentage,
      };
    } catch (error) {
      logger.error(`Erro ao solicitar cancelamento do pedido ${request.orderId}:`, error);
      throw error;
    }
  },

  /**
   * Cria um registro de solicitação de cancelamento
   * @param orderId - ID do pedido
   * @param reason - Motivo do cancelamento
   * @param details - Detalhes adicionais
   * @param requestedBy - ID do usuário que solicitou o cancelamento
   * @param policyId - ID da política aplicada
   * @param requiresApproval - Se requer aprovação
   * @returns ID da solicitação criada
   */
  async createCancellationRecord(
    orderId: string,
    reason: CancellationReason,
    details: string,
    requestedBy: string,
    policyId: string,
    requiresApproval: boolean
  ): Promise<string> {
    try {
      const result = await queryHelper.query(
        `INSERT INTO tab_cancellation_request (
          ulid_cancellation,
          ulid_order,
          ulid_policy,
          reason,
          details,
          requested_by,
          status,
          created_at,
          updated_at
        ) VALUES (
          gen_ulid(),
          $1, $2, $3, $4, $5, $6, NOW(), NOW()
        ) RETURNING ulid_cancellation`,
        [orderId, policyId, reason, details, requestedBy, requiresApproval ? 'pending' : 'approved']
      );

      return result.rows[0].ulid_cancellation;
    } catch (error) {
      logger.error(`Erro ao criar registro de cancelamento para o pedido ${orderId}:`, error);
      throw error;
    }
  },

  /**
   * Processa um cancelamento aprovado
   * @param cancellationId - ID da solicitação de cancelamento
   * @param approvedBy - ID do usuário que aprovou (ou 'auto' para aprovação automática)
   * @returns Resultado do processamento
   */
  async processCancellation(cancellationId: string, approvedBy: string): Promise<any> {
    try {
      // Buscar dados da solicitação
      const cancellation = await queryHelper.queryOne(
        `SELECT cr.*, cp.auto_refund, cp.refund_percentage
         FROM tab_cancellation_request cr
         JOIN tab_cancellation_policy cp ON cr.ulid_policy = cp.ulid_policy
         WHERE cr.ulid_cancellation = $1`,
        [cancellationId]
      );

      if (!cancellation) {
        throw new Error(`Solicitação de cancelamento não encontrada: ${cancellationId}`);
      }

      // Verificar se já foi processada
      if (cancellation.status === 'approved' || cancellation.status === 'completed') {
        return {
          cancellationId,
          orderId: cancellation.ulid_order,
          status: cancellation.status,
          message: 'Cancelamento já processado',
        };
      }

      // Atualizar status da solicitação
      await queryHelper.query(
        `UPDATE tab_cancellation_request
         SET status = 'approved',
            approved_by = $1,
            approved_at = NOW(),
            updated_at = NOW()
         WHERE ulid_cancellation = $2`,
        [approvedBy, cancellationId]
      );

      // Atualizar status do pedido
      await queryHelper.query(
        `UPDATE tab_order
         SET cod_status = 5, -- Assumindo que 5 é o código para "Cancelado"
             updated_at = NOW()
         WHERE ulid_order = $1`,
        [cancellation.ulid_order]
      );

      // Buscar pagamentos associados ao pedido
      const payments = await queryHelper.query(
        `SELECT p.*, pt.type as payment_type
         FROM tab_payment p
         JOIN tab_payment_type pt ON p.ulid_payment_type = pt.ulid_payment_type
         WHERE p.ulid_order = $1 AND p.cod_status = $2`,
        [cancellation.ulid_order, PaymentStatus.APPROVED]
      );

      // Processar reembolsos se necessário
      const refundResults = [];

      if (cancellation.auto_refund && payments.rows.length > 0) {
        for (const payment of payments.rows) {
          // Calcular valor do reembolso
          const refundValue = payment.value * (cancellation.refund_percentage / 100);

          // Registrar reembolso
          const refundId = await this.createRefundForCancellation(
            payment.ulid_payment,
            refundValue,
            `Cancelamento automático - ${cancellation.reason}`,
            approvedBy === 'auto' ? payment.requested_by : approvedBy,
            cancellationId
          );

          refundResults.push({
            paymentId: payment.ulid_payment,
            refundId,
            value: refundValue,
            percentage: cancellation.refund_percentage,
          });
        }
      }

      // Atualizar status da solicitação para concluída
      await queryHelper.query(
        `UPDATE tab_cancellation_request
         SET status = 'completed',
             completed_at = NOW(),
             updated_at = NOW()
         WHERE ulid_cancellation = $1`,
        [cancellationId]
      );

      return {
        cancellationId,
        orderId: cancellation.ulid_order,
        status: 'completed',
        refunds: refundResults,
      };
    } catch (error) {
      logger.error(`Erro ao processar cancelamento ${cancellationId}:`, error);
      throw error;
    }
  },

  /**
   * Cria um registro de reembolso para um cancelamento
   * @param paymentId - ID do pagamento
   * @param value - Valor do reembolso
   * @param reason - Motivo do reembolso
   * @param requestedBy - ID do usuário que solicitou
   * @param cancellationId - ID da solicitação de cancelamento
   * @returns ID do reembolso criado
   */
  async createRefundForCancellation(
    paymentId: string,
    value: number,
    reason: string,
    requestedBy: string,
    cancellationId: string
  ): Promise<string> {
    try {
      const result = await queryHelper.query(
        `INSERT INTO tab_refund (
          ulid_refund,
          ulid_payment,
          value,
          reason,
          requested_by,
          ulid_cancellation,
          cod_status,
          created_at,
          updated_at
        ) VALUES (
          gen_ulid(),
          $1, $2, $3, $4, $5, $6, NOW(), NOW()
        ) RETURNING ulid_refund`,
        [paymentId, value, reason, requestedBy, cancellationId, RefundStatus.PENDING]
      );

      return result.rows[0].ulid_refund;
    } catch (error) {
      logger.error(`Erro ao criar reembolso para cancelamento ${cancellationId}:`, error);
      throw error;
    }
  },

  /**
   * Rejeita uma solicitação de cancelamento
   * @param cancellationId - ID da solicitação
   * @param rejectedBy - ID do usuário que rejeitou
   * @param reason - Motivo da rejeição
   * @returns Resultado da rejeição
   */
  async rejectCancellation(
    cancellationId: string,
    rejectedBy: string,
    reason: string
  ): Promise<any> {
    try {
      // Buscar dados da solicitação
      const cancellation = await queryHelper.queryOne(
        'SELECT * FROM tab_cancellation_request WHERE ulid_cancellation = $1',
        [cancellationId]
      );

      if (!cancellation) {
        throw new Error(`Solicitação de cancelamento não encontrada: ${cancellationId}`);
      }

      // Verificar se já foi processada
      if (cancellation.status !== 'pending') {
        return {
          cancellationId,
          orderId: cancellation.ulid_order,
          status: cancellation.status,
          message: 'Solicitação já processada',
        };
      }

      // Atualizar status da solicitação
      await queryHelper.query(
        `UPDATE tab_cancellation_request
         SET status = 'rejected',
             rejected_by = $1,
             rejection_reason = $2,
             rejected_at = NOW(),
             updated_at = NOW()
         WHERE ulid_cancellation = $3`,
        [rejectedBy, reason, cancellationId]
      );

      return {
        cancellationId,
        orderId: cancellation.ulid_order,
        status: 'rejected',
        reason,
      };
    } catch (error) {
      logger.error(`Erro ao rejeitar cancelamento ${cancellationId}:`, error);
      throw error;
    }
  },

  /**
   * Obtém o histórico de cancelamentos de um pedido
   * @param orderId - ID do pedido
   * @returns Histórico de cancelamentos
   */
  async getCancellationHistory(orderId: string): Promise<any[]> {
    try {
      const result = await queryHelper.query(
        `SELECT cr.*, cp.name as policy_name, cp.refund_percentage,
                u1.name as requested_by_name,
                u2.name as approved_by_name,
                u3.name as rejected_by_name
         FROM tab_cancellation_request cr
         JOIN tab_cancellation_policy cp ON cr.ulid_policy = cp.ulid_policy
         LEFT JOIN tab_user u1 ON cr.requested_by = u1.ulid_user
         LEFT JOIN tab_user u2 ON cr.approved_by = u2.ulid_user
         LEFT JOIN tab_user u3 ON cr.rejected_by = u3.ulid_user
         WHERE cr.ulid_order = $1
         ORDER BY cr.created_at DESC`,
        [orderId]
      );

      return result.rows;
    } catch (error) {
      logger.error(`Erro ao obter histórico de cancelamentos do pedido ${orderId}:`, error);
      return [];
    }
  },

  /**
   * Obtém solicitações de cancelamento pendentes
   * @param limit - Limite de registros (padrão: 10)
   * @returns Lista de solicitações pendentes
   */
  async getPendingCancellations(limit = 10): Promise<any[]> {
    try {
      const result = await queryHelper.query(
        `SELECT cr.*, cp.name as policy_name, cp.refund_percentage,
                o.total as order_total,
                u.name as customer_name, u.email as customer_email
         FROM tab_cancellation_request cr
         JOIN tab_cancellation_policy cp ON cr.ulid_policy = cp.ulid_policy
         JOIN tab_order o ON cr.ulid_order = o.ulid_order
         JOIN tab_user u ON o.ulid_user = u.ulid_user
         WHERE cr.status = 'pending'
         ORDER BY cr.created_at ASC
         LIMIT $1`,
        [limit]
      );

      return result.rows;
    } catch (error) {
      logger.error('Erro ao obter solicitações de cancelamento pendentes:', error);
      return [];
    }
  },
};
