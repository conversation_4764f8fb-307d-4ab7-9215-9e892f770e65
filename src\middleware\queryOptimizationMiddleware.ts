/**
 * Middleware de Otimização de Consultas SQL
 * 
 * Este middleware intercepta consultas SQL antes de serem executadas,
 * analisa sua estrutura e sugere otimizações quando necessário.
 * 
 * Características:
 * - Detecção de consultas ineficientes
 * - Sugestão de índices
 * - Reescrita automática de consultas
 * - Monitoramento de performance
 */

import { logger } from '@utils/logger';
import { applicationMonitoringService } from '@services/applicationMonitoringService';
import { dbOptimizationService } from '@services/dbOptimizationService';

/**
 * Interface para resultado da análise de consulta
 */
export interface QueryAnalysisResult {
  /**
   * Consulta original
   */
  originalQuery: string;

  /**
   * Consulta otimizada (se aplicável)
   */
  optimizedQuery?: string;

  /**
   * Se a consulta foi otimizada
   */
  wasOptimized: boolean;

  /**
   * Problemas detectados na consulta
   */
  issues: QueryIssue[];

  /**
   * Sugestões de otimização
   */
  suggestions: string[];

  /**
   * Índices sugeridos
   */
  suggestedIndexes: SuggestedIndex[];
}

/**
 * Interface para problema detectado em consulta
 */
export interface QueryIssue {
  /**
   * Tipo de problema
   */
  type: 'performance' | 'security' | 'syntax';

  /**
   * Descrição do problema
   */
  description: string;

  /**
   * Severidade do problema
   */
  severity: 'low' | 'medium' | 'high' | 'critical';
}

/**
 * Interface para índice sugerido
 */
export interface SuggestedIndex {
  /**
   * Tabela para o índice
   */
  table: string;

  /**
   * Colunas para o índice
   */
  columns: string[];

  /**
   * Se o índice deve ser único
   */
  unique: boolean;

  /**
   * SQL para criar o índice
   */
  createSQL: string;
}

/**
 * Middleware de otimização de consultas
 */
export const queryOptimizationMiddleware = {
  /**
   * Padrões de consultas problemáticas
   */
  PROBLEMATIC_PATTERNS: [
    {
      pattern: /SELECT\s+\*\s+FROM/i,
      issue: {
        type: 'performance' as const,
        description: 'Uso de SELECT * pode retornar colunas desnecessárias',
        severity: 'medium' as const,
      },
      suggestion: 'Especifique apenas as colunas necessárias em vez de usar SELECT *',
    },
    {
      pattern: /WHERE\s+[a-zA-Z0-9_]+\s+LIKE\s+'%/i,
      issue: {
        type: 'performance' as const,
        description: 'LIKE com curinga no início não pode usar índices',
        severity: 'high' as const,
      },
      suggestion: 'Evite usar LIKE com % no início do padrão ou considere índices de texto completo',
    },
    {
      pattern: /ORDER\s+BY\s+RANDOM\(\)/i,
      issue: {
        type: 'performance' as const,
        description: 'ORDER BY RANDOM() é extremamente ineficiente para grandes conjuntos de dados',
        severity: 'high' as const,
      },
      suggestion: 'Considere alternativas como TABLESAMPLE ou seleção de IDs aleatórios em uma consulta separada',
    },
    {
      pattern: /NOT\s+IN\s+\(\s*SELECT/i,
      issue: {
        type: 'performance' as const,
        description: 'NOT IN com subconsulta pode ter problemas de performance',
        severity: 'medium' as const,
      },
      suggestion: 'Considere usar NOT EXISTS ou LEFT JOIN / IS NULL em vez de NOT IN',
    },
    {
      pattern: /COUNT\s*\(\s*DISTINCT/i,
      issue: {
        type: 'performance' as const,
        description: 'COUNT(DISTINCT) pode ser lento em grandes conjuntos de dados',
        severity: 'medium' as const,
      },
      suggestion: 'Considere usar subconsultas ou tabelas temporárias para conjuntos de dados grandes',
    },
  ],

  /**
   * Inicializa o middleware
   */
  initialize(): void {
    logger.info('Inicializando middleware de otimização de consultas SQL');
    
    // Configurar monitoramento de métricas
    applicationMonitoringService.registerMetric('query_optimization_count', 'Número de consultas otimizadas');
    applicationMonitoringService.registerMetric('query_issues_detected', 'Número de problemas detectados em consultas');
    
    logger.info('Middleware de otimização de consultas SQL inicializado com sucesso');
  },

  /**
   * Analisa uma consulta SQL e sugere otimizações
   * @param query - Consulta SQL
   * @param params - Parâmetros da consulta
   * @returns Resultado da análise
   */
  analyzeQuery(query: string, params?: unknown[]): QueryAnalysisResult {
    // Inicializar resultado
    const result: QueryAnalysisResult = {
      originalQuery: query,
      wasOptimized: false,
      issues: [],
      suggestions: [],
      suggestedIndexes: [],
    };
    
    try {
      // Verificar padrões problemáticos
      this.checkProblematicPatterns(query, result);
      
      // Verificar uso de índices
      this.checkIndexUsage(query, result);
      
      // Verificar oportunidades de paginação
      this.checkPaginationOpportunities(query, result);
      
      // Tentar otimizar a consulta
      this.optimizeQuery(query, result);
      
      // Registrar métricas
      if (result.issues.length > 0) {
        applicationMonitoringService.incrementCounter('query_issues_detected', result.issues.length);
      }
      
      if (result.wasOptimized) {
        applicationMonitoringService.incrementCounter('query_optimization_count');
      }
      
      return result;
    } catch (error) {
      logger.error('Erro ao analisar consulta SQL:', error);
      
      // Retornar resultado parcial em caso de erro
      return result;
    }
  },

  /**
   * Verifica padrões problemáticos na consulta
   * @param query - Consulta SQL
   * @param result - Resultado da análise
   */
  checkProblematicPatterns(query: string, result: QueryAnalysisResult): void {
    // Verificar cada padrão problemático
    for (const { pattern, issue, suggestion } of this.PROBLEMATIC_PATTERNS) {
      if (pattern.test(query)) {
        result.issues.push(issue);
        result.suggestions.push(suggestion);
      }
    }
  },

  /**
   * Verifica uso de índices na consulta
   * @param query - Consulta SQL
   * @param result - Resultado da análise
   */
  checkIndexUsage(query: string, result: QueryAnalysisResult): void {
    // Extrair tabelas da consulta
    const tables = dbOptimizationService.extractTablesFromQuery(query);
    
    // Verificar cláusulas WHERE para possíveis índices
    const whereClauseMatch = query.match(/WHERE\s+([^;]*)/i);
    
    if (whereClauseMatch && whereClauseMatch[1]) {
      const whereClause = whereClauseMatch[1];
      
      // Extrair condições da cláusula WHERE
      const conditions = whereClause.split(/\s+AND\s+/i);
      
      // Analisar cada condição
      for (const condition of conditions) {
        // Verificar condições de igualdade (mais comuns para índices)
        const equalityMatch = condition.match(/([a-zA-Z0-9_]+)\s*=\s*[^=]/i);
        
        if (equalityMatch && equalityMatch[1]) {
          const column = equalityMatch[1];
          
          // Determinar a tabela para esta coluna
          // Simplificação: assumir que a coluna pertence à primeira tabela
          if (tables.length > 0) {
            const table = tables[0];
            
            // Sugerir índice
            const suggestedIndex: SuggestedIndex = {
              table,
              columns: [column],
              unique: false,
              createSQL: dbOptimizationService.generateCreateIndexSQL(table, [column]),
            };
            
            // Adicionar à lista de índices sugeridos
            result.suggestedIndexes.push(suggestedIndex);
            
            // Adicionar sugestão
            result.suggestions.push(`Considere criar um índice em ${table}.${column} para melhorar a performance da consulta`);
          }
        }
      }
    }
  },

  /**
   * Verifica oportunidades de paginação na consulta
   * @param query - Consulta SQL
   * @param result - Resultado da análise
   */
  checkPaginationOpportunities(query: string, result: QueryAnalysisResult): void {
    // Verificar se é uma consulta SELECT
    if (!query.toUpperCase().startsWith('SELECT')) {
      return;
    }
    
    // Verificar se já tem LIMIT
    const hasLimit = /LIMIT\s+\d+/i.test(query);
    
    // Verificar se tem ORDER BY
    const hasOrderBy = /ORDER\s+BY/i.test(query);
    
    // Verificar se tem OFFSET
    const hasOffset = /OFFSET\s+\d+/i.test(query);
    
    // Se tem ORDER BY e OFFSET, mas não tem LIMIT
    if (hasOrderBy && hasOffset && !hasLimit) {
      result.issues.push({
        type: 'performance',
        description: 'Uso de OFFSET sem LIMIT pode causar problemas de performance',
        severity: 'medium',
      });
      
      result.suggestions.push('Adicione uma cláusula LIMIT para limitar o número de resultados');
    }
    
    // Se tem OFFSET com valor alto
    const offsetMatch = query.match(/OFFSET\s+(\d+)/i);
    if (offsetMatch && parseInt(offsetMatch[1], 10) > 1000) {
      result.issues.push({
        type: 'performance',
        description: `OFFSET com valor alto (${offsetMatch[1]}) pode causar problemas de performance`,
        severity: 'high',
      });
      
      result.suggestions.push('Considere usar paginação baseada em cursor em vez de OFFSET para conjuntos de dados grandes');
    }
  },

  /**
   * Tenta otimizar a consulta
   * @param query - Consulta SQL
   * @param result - Resultado da análise
   */
  optimizeQuery(query: string, result: QueryAnalysisResult): void {
    let optimizedQuery = query;
    
    // Otimização 1: Substituir SELECT * por colunas específicas
    if (/SELECT\s+\*\s+FROM\s+([a-zA-Z0-9_]+)/i.test(query)) {
      // Esta é uma simplificação. Em um cenário real, precisaríamos conhecer o schema da tabela
      // para listar todas as colunas. Aqui, apenas indicamos que a otimização é possível.
      result.wasOptimized = true;
      result.suggestions.push('A consulta poderia ser otimizada substituindo SELECT * por colunas específicas');
    }
    
    // Otimização 2: Adicionar LIMIT para consultas sem limite
    if (
      query.toUpperCase().startsWith('SELECT') &&
      !/LIMIT\s+\d+/i.test(query) &&
      !/FOR\s+UPDATE/i.test(query)
    ) {
      optimizedQuery = `${query} LIMIT 1000`;
      result.wasOptimized = true;
      result.optimizedQuery = optimizedQuery;
      result.suggestions.push('Adicionado LIMIT 1000 para evitar retorno de conjuntos de dados muito grandes');
    }
    
    // Otimização 3: Substituir NOT IN por NOT EXISTS
    if (/NOT\s+IN\s+\(\s*SELECT/i.test(query)) {
      result.wasOptimized = true;
      result.suggestions.push('A consulta poderia ser otimizada substituindo NOT IN por NOT EXISTS');
    }
  },

  /**
   * Intercepta e otimiza uma consulta antes da execução
   * @param query - Consulta SQL
   * @param params - Parâmetros da consulta
   * @returns Consulta otimizada e parâmetros
   */
  interceptQuery(
    query: string,
    params?: unknown[]
  ): { query: string; params: unknown[] } {
    try {
      // Analisar consulta
      const analysis = this.analyzeQuery(query, params);
      
      // Registrar problemas encontrados
      if (analysis.issues.length > 0) {
        const criticalIssues = analysis.issues.filter(issue => issue.severity === 'critical');
        const highIssues = analysis.issues.filter(issue => issue.severity === 'high');
        
        if (criticalIssues.length > 0) {
          logger.error('Problemas críticos detectados na consulta SQL:', {
            query: dbOptimizationService.sanitizeQuery(query),
            issues: criticalIssues.map(i => i.description),
            suggestions: analysis.suggestions,
          });
        } else if (highIssues.length > 0) {
          logger.warn('Problemas importantes detectados na consulta SQL:', {
            query: dbOptimizationService.sanitizeQuery(query),
            issues: highIssues.map(i => i.description),
            suggestions: analysis.suggestions,
          });
        }
      }
      
      // Retornar consulta otimizada se disponível
      if (analysis.wasOptimized && analysis.optimizedQuery) {
        logger.info('Consulta SQL otimizada:', {
          original: dbOptimizationService.sanitizeQuery(query),
          optimized: dbOptimizationService.sanitizeQuery(analysis.optimizedQuery),
        });
        
        return {
          query: analysis.optimizedQuery,
          params: params || [],
        };
      }
      
      // Retornar consulta original
      return {
        query,
        params: params || [],
      };
    } catch (error) {
      logger.error('Erro ao interceptar consulta SQL:', error);
      
      // Em caso de erro, retornar consulta original
      return {
        query,
        params: params || [],
      };
    }
  },
};
