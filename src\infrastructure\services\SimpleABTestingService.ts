/**
 * Simple A/B Testing Service
 *
 * Implementação simples do serviço de testes A/B.
 * Parte da implementação da tarefa 8.9.4 - Marketing digital
 */

import { nanoid } from 'nanoid';
import {
  ABTest,
  ABTestGoal,
  ABTestVariant,
  ABTestingService,
} from '../../domain/services/ABTestingService';

export class SimpleABTestingService implements ABTestingService {
  private tests: ABTest[] = [];
  private goals: ABTestGoal[] = [];
  private userAssignments: Record<string, Record<string, string>> = {}; // testId -> userId -> variantId
  private conversions: Array<{
    goalId: string;
    testId: string;
    variantId: string;
    userId: string;
    timestamp: Date;
    value?: number;
  }> = [];
  private isInitialized = false;

  /**
   * Inicializa o serviço de testes A/B
   */
  async initialize(): Promise<boolean> {
    try {
      // Carregar dados de testes e objetivos (em uma implementação real, isso viria de um banco de dados)
      this.isInitialized = true;
      return true;
    } catch (error) {
      console.error('Erro ao inicializar serviço de testes A/B:', error);
      return false;
    }
  }

  /**
   * Cria um novo teste A/B
   */
  async createTest(test: Omit<ABTest, 'id'>): Promise<ABTest> {
    const newTest: ABTest = {
      ...test,
      id: uuidv4(),
      results: {
        totalParticipants: 0,
        variantResults: test.variants.map((variant) => ({
          variantId: variant.id,
          participants: 0,
          conversions: 0,
          conversionRate: 0,
        })),
        isStatisticallySignificant: false,
      },
    };

    this.tests.push(newTest);
    return newTest;
  }

  /**
   * Atualiza um teste A/B existente
   */
  async updateTest(id: string, updates: Partial<ABTest>): Promise<ABTest | null> {
    const index = this.tests.findIndex((test) => test.id === id);

    if (index === -1) {
      return null;
    }

    this.tests[index] = {
      ...this.tests[index],
      ...updates,
    };

    return this.tests[index];
  }

  /**
   * Remove um teste A/B
   */
  async deleteTest(id: string): Promise<boolean> {
    const initialLength = this.tests.length;
    this.tests = this.tests.filter((test) => test.id !== id);

    return this.tests.length < initialLength;
  }

  /**
   * Obtém um teste A/B pelo ID
   */
  async getTest(id: string): Promise<ABTest | null> {
    const test = this.tests.find((test) => test.id === id);
    return test || null;
  }

  /**
   * Lista todos os testes A/B
   */
  async listTests(filters?: {
    status?: ABTest['status'] | ABTest['status'][];
    search?: string;
  }): Promise<ABTest[]> {
    let filteredTests = [...this.tests];

    if (filters) {
      if (filters.status) {
        const statusArray = Array.isArray(filters.status) ? filters.status : [filters.status];
        filteredTests = filteredTests.filter((test) => statusArray.includes(test.status));
      }

      if (filters.search) {
        const searchLower = filters.search.toLowerCase();
        filteredTests = filteredTests.filter(
          (test) =>
            test.name.toLowerCase().includes(searchLower) ||
            test.description?.toLowerCase().includes(searchLower)
        );
      }
    }

    return filteredTests;
  }

  /**
   * Inicia um teste A/B
   */
  async startTest(id: string): Promise<boolean> {
    const test = await this.getTest(id);

    if (!test) {
      return false;
    }

    const updatedTest = await this.updateTest(id, {
      status: 'running',
      startDate: new Date(),
    });

    return !!updatedTest;
  }

  /**
   * Pausa um teste A/B
   */
  async pauseTest(id: string): Promise<boolean> {
    const test = await this.getTest(id);

    if (!test) {
      return false;
    }

    const updatedTest = await this.updateTest(id, {
      status: 'paused',
    });

    return !!updatedTest;
  }

  /**
   * Encerra um teste A/B
   */
  async completeTest(id: string): Promise<boolean> {
    const test = await this.getTest(id);

    if (!test) {
      return false;
    }

    // Analisar resultados finais
    const results = await this.analyzeResults(id);

    const updatedTest = await this.updateTest(id, {
      status: 'completed',
      endDate: new Date(),
      results,
    });

    return !!updatedTest;
  }

  /**
   * Arquiva um teste A/B
   */
  async archiveTest(id: string): Promise<boolean> {
    const test = await this.getTest(id);

    if (!test) {
      return false;
    }

    const updatedTest = await this.updateTest(id, {
      status: 'archived',
    });

    return !!updatedTest;
  }

  /**
   * Cria um novo objetivo para testes A/B
   */
  async createGoal(goal: Omit<ABTestGoal, 'id'>): Promise<ABTestGoal> {
    const newGoal: ABTestGoal = {
      ...goal,
      id: uuidv4(),
    };

    this.goals.push(newGoal);
    return newGoal;
  }

  /**
   * Atualiza um objetivo existente
   */
  async updateGoal(id: string, updates: Partial<ABTestGoal>): Promise<ABTestGoal | null> {
    const index = this.goals.findIndex((goal) => goal.id === id);

    if (index === -1) {
      return null;
    }

    this.goals[index] = {
      ...this.goals[index],
      ...updates,
    };

    return this.goals[index];
  }

  /**
   * Remove um objetivo
   */
  async deleteGoal(id: string): Promise<boolean> {
    const initialLength = this.goals.length;
    this.goals = this.goals.filter((goal) => goal.id !== id);

    return this.goals.length < initialLength;
  }

  /**
   * Obtém um objetivo pelo ID
   */
  async getGoal(id: string): Promise<ABTestGoal | null> {
    const goal = this.goals.find((goal) => goal.id === id);
    return goal || null;
  }

  /**
   * Lista todos os objetivos
   */
  async listGoals(): Promise<ABTestGoal[]> {
    return this.goals;
  }

  /**
   * Atribui um usuário a uma variante de teste
   */
  async assignVariant(testId: string, userId?: string): Promise<ABTestVariant | null> {
    const test = await this.getTest(testId);

    if (!test || test.status !== 'running' || !test.variants.length) {
      return null;
    }

    // Gerar ID de usuário se não fornecido
    const actualUserId = userId || this.generateUserId();

    // Verificar se o usuário já está atribuído a uma variante
    if (this.userAssignments[testId]?.[actualUserId]) {
      const variantId = this.userAssignments[testId][actualUserId];
      return test.variants.find((v) => v.id === variantId) || null;
    }

    // Aplicar alocação de tráfego
    if (test.trafficAllocation && test.trafficAllocation < 100) {
      const random = Math.random() * 100;
      if (random >= test.trafficAllocation) {
        return null; // Usuário não participará do teste
      }
    }

    // Selecionar variante com base nos pesos
    const variant = this.selectVariantByWeight(test.variants);

    if (!variant) {
      return null;
    }

    // Armazenar atribuição
    if (!this.userAssignments[testId]) {
      this.userAssignments[testId] = {};
    }

    this.userAssignments[testId][actualUserId] = variant.id;

    // Atualizar contagem de participantes
    const testIndex = this.tests.findIndex((t) => t.id === testId);
    if (testIndex !== -1 && this.tests[testIndex].results) {
      this.tests[testIndex].results.totalParticipants++;

      const variantResultIndex = this.tests[testIndex].results.variantResults.findIndex(
        (vr) => vr.variantId === variant.id
      );

      if (variantResultIndex !== -1) {
        this.tests[testIndex].results.variantResults[variantResultIndex].participants++;
      }
    }

    return variant;
  }

  /**
   * Obtém a variante atribuída a um usuário para um teste específico
   */
  async getAssignedVariant(testId: string, userId?: string): Promise<ABTestVariant | null> {
    const test = await this.getTest(testId);

    if (!test) {
      return null;
    }

    // Usar ID de usuário fornecido ou tentar obter do armazenamento
    const actualUserId = userId || this.getUserIdFromStorage();

    if (
      !actualUserId ||
      !this.userAssignments[testId] ||
      !this.userAssignments[testId][actualUserId]
    ) {
      return null;
    }

    const variantId = this.userAssignments[testId][actualUserId];
    return test.variants.find((v) => v.id === variantId) || null;
  }

  /**
   * Rastreia uma conversão para um objetivo
   */
  async trackConversion(goalId: string, userId?: string, value?: number): Promise<boolean> {
    const goal = await this.getGoal(goalId);

    if (!goal) {
      return false;
    }

    // Usar ID de usuário fornecido ou tentar obter do armazenamento
    const actualUserId = userId || this.getUserIdFromStorage();

    if (!actualUserId) {
      return false;
    }

    // Encontrar testes que usam este objetivo
    const relevantTests = this.tests.filter(
      (test) =>
        test.status === 'running' &&
        (test.goals.primary === goalId || test.goals.secondary?.includes(goalId))
    );

    if (!relevantTests.length) {
      return false;
    }

    // Registrar conversão para cada teste relevante
    for (const test of relevantTests) {
      if (!this.userAssignments[test.id] || !this.userAssignments[test.id][actualUserId]) {
        continue;
      }

      const variantId = this.userAssignments[test.id][actualUserId];

      // Registrar conversão
      this.conversions.push({
        goalId,
        testId: test.id,
        variantId,
        userId: actualUserId,
        timestamp: new Date(),
        value,
      });

      // Atualizar contagem de conversões
      const testIndex = this.tests.findIndex((t) => t.id === test.id);
      if (testIndex !== -1 && this.tests[testIndex].results) {
        const variantResultIndex = this.tests[testIndex].results.variantResults.findIndex(
          (vr) => vr.variantId === variantId
        );

        if (variantResultIndex !== -1) {
          this.tests[testIndex].results.variantResults[variantResultIndex].conversions++;

          // Recalcular taxa de conversão
          const participants =
            this.tests[testIndex].results.variantResults[variantResultIndex].participants;
          const conversions =
            this.tests[testIndex].results.variantResults[variantResultIndex].conversions;

          if (participants > 0) {
            this.tests[testIndex].results.variantResults[variantResultIndex].conversionRate =
              conversions / participants;
          }
        }
      }
    }

    return true;
  }

  /**
   * Calcula o tamanho da amostra necessário para um teste
   */
  async calculateSampleSize(
    baselineConversionRate: number,
    minimumDetectableEffect: number,
    confidenceLevel = 0.95,
    statisticalPower = 0.8
  ): Promise<{
    requiredSampleSize: number;
    requiredSampleSizePerVariant: number;
    estimatedDuration?: number;
  }> {
    // Implementação simplificada do cálculo de tamanho de amostra
    // Em uma implementação real, usaríamos fórmulas estatísticas mais precisas

    // Valores z para níveis de confiança comuns
    const zValues: Record<number, number> = {
      0.8: 1.28,
      0.85: 1.44,
      0.9: 1.65,
      0.95: 1.96,
      0.99: 2.58,
    };

    // Valor z para o nível de confiança
    const zAlpha = zValues[confidenceLevel] || 1.96;

    // Valor z para o poder estatístico
    const zBeta = zValues[statisticalPower] || 0.84;

    // Conversão esperada no grupo de tratamento
    const expectedConversionRate = baselineConversionRate * (1 + minimumDetectableEffect);

    // Cálculo do tamanho da amostra por variante
    const p1 = baselineConversionRate;
    const p2 = expectedConversionRate;
    const pAvg = (p1 + p2) / 2;

    const sampleSizePerVariant = Math.ceil(
      ((zAlpha + zBeta) ** 2 * pAvg * (1 - pAvg) * 2) / (p2 - p1) ** 2
    );

    // Tamanho total da amostra (para duas variantes)
    const totalSampleSize = sampleSizePerVariant * 2;

    return {
      requiredSampleSize: totalSampleSize,
      requiredSampleSizePerVariant: sampleSizePerVariant,
      // Estimativa de duração seria calculada com base no tráfego atual do site
    };
  }

  /**
   * Analisa os resultados de um teste
   */
  async analyzeResults(testId: string): Promise<ABTest['results'] | null> {
    const test = await this.getTest(testId);

    if (!test || !test.results) {
      return null;
    }

    // Clonar resultados atuais
    const results = JSON.parse(JSON.stringify(test.results));

    // Encontrar variante de controle
    const controlVariant = test.variants.find((v) => v.isControl);

    if (!controlVariant) {
      return results;
    }

    // Encontrar resultado da variante de controle
    const controlResult = results.variantResults.find((vr) => vr.variantId === controlVariant.id);

    if (!controlResult) {
      return results;
    }

    // Calcular melhorias e significância estatística
    for (const variantResult of results.variantResults) {
      if (variantResult.variantId === controlVariant.id) {
        continue;
      }

      // Calcular melhoria em relação ao controle
      if (controlResult.conversionRate > 0) {
        variantResult.improvement =
          (variantResult.conversionRate - controlResult.conversionRate) /
          controlResult.conversionRate;
      } else {
        variantResult.improvement = 0;
      }

      // Calcular intervalo de confiança e valor p (simplificado)
      // Em uma implementação real, usaríamos cálculos estatísticos mais precisos
      const pValue = this.calculatePValue(
        controlResult.conversions,
        controlResult.participants,
        variantResult.conversions,
        variantResult.participants
      );

      variantResult.pValue = pValue;

      // Determinar significância estatística (p < 0.05)
      if (pValue < 0.05) {
        results.isStatisticallySignificant = true;

        // Determinar vencedor se houver significância estatística
        if (!results.winner && variantResult.improvement > 0) {
          results.winner = variantResult.variantId;
        } else if (results.winner && variantResult.improvement > 0) {
          // Comparar com o vencedor atual
          const currentWinnerResult = results.variantResults.find(
            (vr) => vr.variantId === results.winner
          );

          if (currentWinnerResult && variantResult.improvement > currentWinnerResult.improvement) {
            results.winner = variantResult.variantId;
          }
        }
      }
    }

    return results;
  }

  /**
   * Gera o código de inicialização do cliente de testes A/B
   */
  generateClientCode(): string {
    return `
      <script>
        // Cliente de testes A/B
        window.abTesting = {
          // Obter variante para um teste
          getVariant: async function(testId) {
            // Verificar se já temos uma variante atribuída no localStorage
            const storedVariant = localStorage.getItem('ab_test_' + testId);
            if (storedVariant) {
              return JSON.parse(storedVariant);
            }

            // Solicitar variante ao servidor
            try {
              const response = await fetch('/api/ab-testing/assign-variant', {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                  testId: testId,
                  userId: this.getUserId()
                })
              });

              if (!response.ok) {
                return null;
              }

              const data = await response.json();

              if (data.success && data.variant) {
                // Armazenar variante no localStorage
                localStorage.setItem('ab_test_' + testId, JSON.stringify(data.variant));
                return data.variant;
              }

              return null;
            } catch (error) {
              console.error('Erro ao obter variante de teste A/B:', error);
              return null;
            }
          },

          // Rastrear conversão
          trackConversion: async function(goalId, value) {
            try {
              await fetch('/api/ab-testing/track-conversion', {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                  goalId: goalId,
                  userId: this.getUserId(),
                  value: value
                })
              });
            } catch (error) {
              console.error('Erro ao rastrear conversão de teste A/B:', error);
            }
          },

          // Obter ou gerar ID de usuário
          getUserId: function() {
            let userId = localStorage.getItem('ab_testing_user_id');

            if (!userId) {
              userId = 'user_' + Math.random().toString(36).substring(2, 15);
              localStorage.setItem('ab_testing_user_id', userId);
            }

            return userId;
          }
        };
      </script>
    `;
  }

  /**
   * Seleciona uma variante com base nos pesos
   */
  private selectVariantByWeight(variants: ABTestVariant[]): ABTestVariant | null {
    if (!variants.length) {
      return null;
    }

    // Se não houver pesos definidos, distribuir igualmente
    const hasWeights = variants.some((v) => v.weight !== undefined);

    if (!hasWeights) {
      const randomIndex = Math.floor(Math.random() * variants.length);
      return variants[randomIndex];
    }

    // Normalizar pesos
    const totalWeight = variants.reduce((sum, v) => sum + (v.weight || 0), 0);
    const normalizedVariants = variants.map((v) => ({
      ...v,
      normalizedWeight: (v.weight || 0) / totalWeight,
    }));

    // Selecionar variante com base nos pesos
    const random = Math.random();
    let cumulativeWeight = 0;

    for (const variant of normalizedVariants) {
      cumulativeWeight += variant.normalizedWeight;

      if (random <= cumulativeWeight) {
        return variant;
      }
    }

    // Fallback para a última variante
    return variants[variants.length - 1];
  }

  /**
   * Gera um ID de usuário
   */
  private generateUserId(): string {
    return `user_${uuidv4()}`;
  }

  /**
   * Obtém o ID de usuário do armazenamento
   */
  private getUserIdFromStorage(): string | null {
    if (typeof localStorage !== 'undefined') {
      return localStorage.getItem('ab_testing_user_id');
    }

    return null;
  }

  /**
   * Calcula o valor p para significância estatística
   */
  private calculatePValue(
    controlConversions: number,
    controlParticipants: number,
    variantConversions: number,
    variantParticipants: number
  ): number {
    // Implementação simplificada do teste z para proporções
    // Em uma implementação real, usaríamos bibliotecas estatísticas mais precisas

    if (controlParticipants === 0 || variantParticipants === 0) {
      return 1;
    }

    const p1 = controlConversions / controlParticipants;
    const p2 = variantConversions / variantParticipants;
    const pPooled =
      (controlConversions + variantConversions) / (controlParticipants + variantParticipants);

    const standardError = Math.sqrt(
      pPooled * (1 - pPooled) * (1 / controlParticipants + 1 / variantParticipants)
    );

    if (standardError === 0) {
      return 1;
    }

    const zScore = Math.abs(p1 - p2) / standardError;

    // Aproximação simplificada do valor p a partir do z-score
    // Em uma implementação real, usaríamos uma função de distribuição normal mais precisa
    return 1 - this.normalCDF(zScore);
  }

  /**
   * Função de distribuição cumulativa normal (aproximação)
   */
  private normalCDF(x: number): number {
    // Aproximação da função de distribuição cumulativa normal
    // Fonte: https://stackoverflow.com/a/5259252
    const t = 1 / (1 + 0.2316419 * Math.abs(x));
    const d = 0.3989423 * Math.exp((-x * x) / 2);
    const p =
      d * t * (0.3193815 + t * (-0.3565638 + t * (1.781478 + t * (-1.821256 + t * 1.330274))));

    return x > 0 ? 1 - p : p;
  }
}
