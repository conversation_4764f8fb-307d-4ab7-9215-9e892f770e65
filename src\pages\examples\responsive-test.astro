---
import DaisyButton from '../../components/ui/DaisyButton.astro';
import DaisyCard from '../../components/ui/DaisyCard.astro';
import Alert from '../../components/ui/feedback/Alert.astro';
import Breadcrumbs from '../../components/ui/navigation/Breadcrumbs.astro';
import BaseLayout from '../../layouts/BaseLayout.astro';

// Importar componentes de layout
import { Container, Flex, GridItem, ResponsiveGrid, Section, Spacer } from '../../layouts/grid';

// Importar utilitários responsivos
import { breakpoints } from '../../layouts/responsive';

const title = 'Teste de Responsividade';

// Breadcrumbs para a página atual
const breadcrumbItems = [
  { href: '/', label: 'Início' },
  { href: '/examples', label: 'Exemplos' },
  { label: 'Teste de Responsividade' },
];

// Dados para o teste
const cards = [
  { title: 'Card 1', content: 'Conteúdo do card 1', color: 'primary' },
  { title: 'Card 2', content: 'Conteúdo do card 2', color: 'secondary' },
  { title: 'Card 3', content: 'Conteúdo do card 3', color: 'accent' },
  { title: 'Card 4', content: 'Conteúdo do card 4', color: 'info' },
  { title: 'Card 5', content: 'Conteúdo do card 5', color: 'success' },
  { title: 'Card 6', content: 'Conteúdo do card 6', color: 'warning' },
  { title: 'Card 7', content: 'Conteúdo do card 7', color: 'error' },
  { title: 'Card 8', content: 'Conteúdo do card 8', color: 'neutral' },
];

// Função para obter a classe de cor
function getColorClass(color, type = 'bg') {
  return `${type}-${color}`;
}
---

<BaseLayout title={title}>
  <Container>
    <Breadcrumbs items={breadcrumbItems} class="my-4" />
    
    <Section title={title} subtitle="Teste de layout em diferentes tamanhos de tela">
      <div class="mb-8">
        <Alert type="info">
          <p>
            Esta página é usada para testar a responsividade do layout em diferentes tamanhos de tela.
            Redimensione a janela do navegador ou use as ferramentas de desenvolvedor para simular diferentes dispositivos.
          </p>
        </Alert>
      </div>
      
      <!-- Informações do Dispositivo -->
      <Section title="Informações do Dispositivo" background="base-200" class="mb-8">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h3 class="text-xl font-bold mb-4">Breakpoint Atual</h3>
            <div class="grid grid-cols-6 gap-2 text-center">
              <div class="p-2 rounded bg-base-300 xs:bg-primary xs:text-primary-content">
                <span class="hidden xs:inline">xs</span>
                <span class="xs:hidden">-</span>
              </div>
              <div class="p-2 rounded bg-base-300 sm:bg-primary sm:text-primary-content">
                <span class="hidden sm:inline">sm</span>
                <span class="sm:hidden">-</span>
              </div>
              <div class="p-2 rounded bg-base-300 md:bg-primary md:text-primary-content">
                <span class="hidden md:inline">md</span>
                <span class="md:hidden">-</span>
              </div>
              <div class="p-2 rounded bg-base-300 lg:bg-primary lg:text-primary-content">
                <span class="hidden lg:inline">lg</span>
                <span class="lg:hidden">-</span>
              </div>
              <div class="p-2 rounded bg-base-300 xl:bg-primary xl:text-primary-content">
                <span class="hidden xl:inline">xl</span>
                <span class="xl:hidden">-</span>
              </div>
              <div class="p-2 rounded bg-base-300 2xl:bg-primary 2xl:text-primary-content">
                <span class="hidden 2xl:inline">2xl</span>
                <span class="2xl:hidden">-</span>
              </div>
            </div>
          </div>
          
          <div>
            <h3 class="text-xl font-bold mb-4">Orientação</h3>
            <div class="grid grid-cols-2 gap-2 text-center">
              <div class="p-2 rounded bg-base-300 portrait:bg-primary portrait:text-primary-content">
                <span class="hidden portrait:inline">Retrato</span>
                <span class="portrait:hidden">-</span>
              </div>
              <div class="p-2 rounded bg-base-300 landscape:bg-primary landscape:text-primary-content">
                <span class="hidden landscape:inline">Paisagem</span>
                <span class="landscape:hidden">-</span>
              </div>
            </div>
          </div>
        </div>
        
        <div class="mt-6">
          <h3 class="text-xl font-bold mb-4">Dimensões da Viewport</h3>
          <div id="viewport-info" class="p-4 bg-base-300 rounded-box text-center">
            Carregando informações...
          </div>
        </div>
      </Section>
      
      <!-- Teste de Grid -->
      <Section title="Teste de Grid" class="mb-8">
        <h3 class="text-xl font-bold mb-4">Grid Responsivo</h3>
        <p class="mb-4">
          Este grid muda o número de colunas conforme o tamanho da tela:
          <span class="block xs:hidden">xs: 1 coluna</span>
          <span class="hidden xs:block sm:hidden">sm: 2 colunas</span>
          <span class="hidden sm:block md:hidden">md: 3 colunas</span>
          <span class="hidden md:block lg:hidden">lg: 4 colunas</span>
          <span class="hidden lg:block">xl+: 4 colunas</span>
        </p>
        
        <ResponsiveGrid 
          cols={{ 
            base: 1,
            xs: 2,
            sm: 3,
            md: 4,
            lg: 4
          }} 
          gap={{ 
            base: 2,
            md: 4,
            lg: 6
          }}
        >
          {cards.map((card, index) => (
            <div class={`p-4 rounded-box ${getColorClass(card.color)} ${getColorClass(card.color + '-content', 'text')}`}>
              <h4 class="font-bold">{card.title}</h4>
              <p>{card.content}</p>
              <p class="text-xs mt-2">Item {index + 1}</p>
            </div>
          ))}
        </ResponsiveGrid>
      </Section>
      
      <!-- Teste de Flex -->
      <Section title="Teste de Flex" class="mb-8">
        <h3 class="text-xl font-bold mb-4">Flex Responsivo</h3>
        <p class="mb-4">
          Este flex muda a direção conforme o tamanho da tela:
          <span class="block md:hidden">Mobile: Coluna</span>
          <span class="hidden md:block">Desktop: Linha</span>
        </p>
        
        <Flex 
          direction={{ base: 'col', md: 'row' }} 
          wrap="wrap" 
          justify="between" 
          items="stretch" 
          gap={4}
        >
          <div class="p-4 rounded-box bg-primary text-primary-content flex-1 min-w-[200px]">
            <h4 class="font-bold">Item 1</h4>
            <p>Este item ocupa espaço flexível</p>
          </div>
          <div class="p-4 rounded-box bg-secondary text-secondary-content flex-1 min-w-[200px]">
            <h4 class="font-bold">Item 2</h4>
            <p>Este item ocupa espaço flexível</p>
          </div>
          <div class="p-4 rounded-box bg-accent text-accent-content flex-1 min-w-[200px]">
            <h4 class="font-bold">Item 3</h4>
            <p>Este item ocupa espaço flexível</p>
          </div>
        </Flex>
      </Section>
      
      <!-- Teste de Visibilidade -->
      <Section title="Teste de Visibilidade" class="mb-8">
        <h3 class="text-xl font-bold mb-4">Visibilidade Responsiva</h3>
        <p class="mb-4">
          Estes elementos são visíveis apenas em determinados breakpoints:
        </p>
        
        <div class="space-y-4">
          <div class="block xs:hidden p-4 bg-primary text-primary-content rounded-box">
            Visível apenas em dispositivos muito pequenos (menor que xs)
          </div>
          <div class="hidden xs:block sm:hidden p-4 bg-secondary text-secondary-content rounded-box">
            Visível apenas em dispositivos pequenos (xs)
          </div>
          <div class="hidden sm:block md:hidden p-4 bg-accent text-accent-content rounded-box">
            Visível apenas em dispositivos médios (sm)
          </div>
          <div class="hidden md:block lg:hidden p-4 bg-info text-info-content rounded-box">
            Visível apenas em tablets (md)
          </div>
          <div class="hidden lg:block xl:hidden p-4 bg-success text-success-content rounded-box">
            Visível apenas em desktops (lg)
          </div>
          <div class="hidden xl:block 2xl:hidden p-4 bg-warning text-warning-content rounded-box">
            Visível apenas em desktops grandes (xl)
          </div>
          <div class="hidden 2xl:block p-4 bg-error text-error-content rounded-box">
            Visível apenas em telas muito grandes (2xl)
          </div>
        </div>
      </Section>
      
      <!-- Teste de Tipografia -->
      <Section title="Teste de Tipografia" class="mb-8">
        <h3 class="text-xl font-bold mb-4">Tipografia Responsiva</h3>
        <p class="mb-4">
          Estes textos mudam de tamanho conforme o tamanho da tela:
        </p>
        
        <div class="space-y-6">
          <h1 class="text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-bold">
            Título Responsivo
          </h1>
          <h2 class="text-xl sm:text-2xl md:text-3xl lg:text-4xl font-bold">
            Subtítulo Responsivo
          </h2>
          <p class="text-sm sm:text-base md:text-lg lg:text-xl">
            Este parágrafo muda de tamanho conforme o breakpoint. Em dispositivos móveis, o texto é menor para economizar espaço. Em desktops, o texto é maior para melhor legibilidade.
          </p>
        </div>
      </Section>
      
      <!-- Teste de Imagens -->
      <Section title="Teste de Imagens" class="mb-8">
        <h3 class="text-xl font-bold mb-4">Imagens Responsivas</h3>
        <p class="mb-4">
          Estas imagens se adaptam ao tamanho da tela:
        </p>
        
        <ResponsiveGrid cols={{ base: 1, md: 2 }} gap={6}>
          <div>
            <h4 class="font-bold mb-2">Imagem com largura máxima</h4>
            <img 
              src="https://via.placeholder.com/1200x600/0066cc/ffffff?text=Imagem+Responsiva" 
              alt="Imagem responsiva" 
              class="w-full rounded-box"
            />
          </div>
          <div>
            <h4 class="font-bold mb-2">Imagem com proporção fixa</h4>
            <div class="aspect-video bg-base-300 rounded-box overflow-hidden">
              <img 
                src="https://via.placeholder.com/1200x600/ff6b6b/ffffff?text=Proporção+Fixa" 
                alt="Imagem com proporção fixa" 
                class="w-full h-full object-cover"
              />
            </div>
          </div>
        </ResponsiveGrid>
      </Section>
      
      <!-- Teste de Formulários -->
      <Section title="Teste de Formulários" class="mb-8">
        <h3 class="text-xl font-bold mb-4">Formulários Responsivos</h3>
        <p class="mb-4">
          Este formulário se adapta ao tamanho da tela:
        </p>
        
        <form class="space-y-6">
          <ResponsiveGrid cols={{ base: 1, md: 2 }} gap={4}>
            <div class="form-control">
              <label class="label">
                <span class="label-text">Nome</span>
              </label>
              <input type="text" placeholder="Nome" class="input input-bordered w-full" />
            </div>
            <div class="form-control">
              <label class="label">
                <span class="label-text">Email</span>
              </label>
              <input type="email" placeholder="Email" class="input input-bordered w-full" />
            </div>
          </ResponsiveGrid>
          
          <div class="form-control">
            <label class="label">
              <span class="label-text">Mensagem</span>
            </label>
            <textarea class="textarea textarea-bordered h-24" placeholder="Mensagem"></textarea>
          </div>
          
          <div class="flex flex-col sm:flex-row gap-4 justify-end">
            <DaisyButton variant="ghost">Cancelar</DaisyButton>
            <DaisyButton variant="primary">Enviar</DaisyButton>
          </div>
        </form>
      </Section>
      
      <!-- Teste de Navegação -->
      <Section title="Teste de Navegação" class="mb-8">
        <h3 class="text-xl font-bold mb-4">Navegação Responsiva</h3>
        <p class="mb-4">
          Esta navegação se adapta ao tamanho da tela:
        </p>
        
        <div class="navbar bg-base-100 rounded-box shadow-md">
          <div class="navbar-start">
            <div class="dropdown">
              <label tabindex="0" class="btn btn-ghost lg:hidden">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h8m-8 6h16" />
                </svg>
              </label>
              <ul tabindex="0" class="menu menu-sm dropdown-content mt-3 z-[1] p-2 shadow bg-base-100 rounded-box w-52">
                <li><a>Início</a></li>
                <li>
                  <a>Produtos</a>
                  <ul class="p-2">
                    <li><a>Categoria 1</a></li>
                    <li><a>Categoria 2</a></li>
                  </ul>
                </li>
                <li><a>Sobre</a></li>
                <li><a>Contato</a></li>
              </ul>
            </div>
            <a class="btn btn-ghost normal-case text-xl">Estação da Alfabetização</a>
          </div>
          <div class="navbar-center hidden lg:flex">
            <ul class="menu menu-horizontal px-1">
              <li><a>Início</a></li>
              <li tabindex="0">
                <details>
                  <summary>Produtos</summary>
                  <ul class="p-2">
                    <li><a>Categoria 1</a></li>
                    <li><a>Categoria 2</a></li>
                  </ul>
                </details>
              </li>
              <li><a>Sobre</a></li>
              <li><a>Contato</a></li>
            </ul>
          </div>
          <div class="navbar-end">
            <a class="btn btn-primary">Login</a>
          </div>
        </div>
      </Section>
    </Section>
  </Container>
</BaseLayout>

<script>
  // Atualizar informações da viewport
  function updateViewportInfo() {
    const viewportInfo = document.getElementById('viewport-info');
    if (viewportInfo) {
      const width = window.innerWidth;
      const height = window.innerHeight;
      const dpr = window.devicePixelRatio;
      
      viewportInfo.innerHTML = `
        <div class="grid grid-cols-1 sm:grid-cols-3 gap-4">
          <div>
            <div class="font-bold">Largura</div>
            <div>${width}px</div>
          </div>
          <div>
            <div class="font-bold">Altura</div>
            <div>${height}px</div>
          </div>
          <div>
            <div class="font-bold">Pixel Ratio</div>
            <div>${dpr}x</div>
          </div>
        </div>
      `;
    }
  }
  
  // Atualizar ao carregar e redimensionar
  window.addEventListener('load', updateViewportInfo);
  window.addEventListener('resize', updateViewportInfo);
</script>
