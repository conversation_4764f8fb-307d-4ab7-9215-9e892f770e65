---
import FinancialNav from '@components/navigation/FinancialNav.astro';
import { checkAdmin } from '@helpers/authGuard';
import { csrfHelper } from '@helpers/csrfHelper';
import AdminLayout from '@layouts/AdminLayout.astro';
import {
  CancellationPolicyType,
  cancellationPolicyService,
} from '@services/cancellationPolicyService';

// Protege a rota verificando se o usuário é admin
const authRedirect = await checkAdmin(Astro);
if (authRedirect) return authRedirect;

// Obter ID da política
const { id } = Astro.params;

// Buscar política
const policy = await cancellationPolicyService.getPolicy(id);

// Verificar se a política existe
if (!policy) {
  return Astro.redirect('/admin/financial/cancellation/policies?error=policy_not_found');
}

// Gerar token CSRF
const csrfToken = await csrfHelper.generateToken(Astro);

// Processar formulário
if (Astro.request.method === 'POST') {
  try {
    const formData = await Astro.request.formData();

    // Validar token CSRF
    const token = formData.get('csrf_token');
    const isValidToken = await csrfHelper.validateToken(Astro, token);

    if (!isValidToken) {
      return Astro.redirect(
        `/admin/financial/cancellation/policies/edit/${id}?error=invalid_token`
      );
    }

    // Obter dados do formulário
    const name = formData.get('name')?.toString();
    const description = formData.get('description')?.toString();
    const type = formData.get('type')?.toString() as CancellationPolicyType;
    const timeLimitHours = Number.parseInt(formData.get('time_limit_hours')?.toString() || '0');
    const refundPercentage = Number.parseFloat(
      formData.get('refund_percentage')?.toString() || '0'
    );
    const requiresApproval = formData.get('requires_approval') === 'on';
    const autoRefund = formData.get('auto_refund') === 'on';
    const active = formData.get('active') === 'on';

    // Validar dados
    if (!name || !type || timeLimitHours <= 0 || refundPercentage < 0 || refundPercentage > 100) {
      return Astro.redirect(`/admin/financial/cancellation/policies/edit/${id}?error=invalid_data`);
    }

    // Atualizar política
    await cancellationPolicyService.updatePolicy(id, {
      name,
      description,
      type,
      time_limit_hours: timeLimitHours,
      refund_percentage: refundPercentage,
      requires_approval: requiresApproval,
      auto_refund: autoRefund,
      active,
    });

    // Redirecionar para a lista de políticas
    return Astro.redirect('/admin/financial/cancellation/policies?success=true');
  } catch (error) {
    console.error('Erro ao atualizar política:', error);
    return Astro.redirect(`/admin/financial/cancellation/policies/edit/${id}?error=update_failed`);
  }
}

// Verificar se há parâmetro de erro na URL
const error = Astro.url.searchParams.get('error');
---

<AdminLayout title={`Editar Política - ${policy.name}`}>
  <div class="container mx-auto p-4">
    <h1 class="text-2xl font-bold mb-4">Gestão Financeira</h1>
    
    <FinancialNav />
    
    <div class="flex items-center mb-6">
      <a href="/admin/financial/cancellation/policies" class="btn btn-ghost btn-sm mr-2">
        <i class="fas fa-arrow-left"></i> Voltar
      </a>
      <h2 class="text-xl font-bold">Editar Política de Cancelamento</h2>
    </div>
    
    {error === 'invalid_token' && (
      <div class="alert alert-error mb-4">
        <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
        <span>Token inválido. Por favor, tente novamente.</span>
      </div>
    )}
    
    {error === 'invalid_data' && (
      <div class="alert alert-error mb-4">
        <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
        <span>Dados inválidos. Verifique os campos e tente novamente.</span>
      </div>
    )}
    
    <!-- Formulário de Edição -->
    <div class="card bg-base-100 shadow-xl">
      <div class="card-body">
        <h2 class="card-title">Editar Política: {policy.name}</h2>
        
        <form method="POST" class="space-y-4">
          <input type="hidden" name="csrf_token" value={csrfToken} />
          
          <!-- Nome da Política -->
          <div class="form-control">
            <label class="label">
              <span class="label-text">Nome da Política</span>
            </label>
            <input 
              type="text" 
              name="name" 
              value={policy.name} 
              class="input input-bordered" 
              required
            />
          </div>
          
          <!-- Descrição -->
          <div class="form-control">
            <label class="label">
              <span class="label-text">Descrição</span>
            </label>
            <textarea 
              name="description" 
              class="textarea textarea-bordered h-24"
            >{policy.description}</textarea>
          </div>
          
          <!-- Tipo de Política -->
          <div class="form-control">
            <label class="label">
              <span class="label-text">Tipo de Política</span>
            </label>
            <select name="type" class="select select-bordered" required>
              <option value="standard" selected={policy.type === 'standard'}>Padrão (Produtos Físicos)</option>
              <option value="digital_product" selected={policy.type === 'digital_product'}>Produtos Digitais</option>
              <option value="subscription" selected={policy.type === 'subscription'}>Assinaturas</option>
              <option value="custom" selected={policy.type === 'custom'}>Personalizada</option>
            </select>
          </div>
          
          <!-- Limite de Tempo -->
          <div class="form-control">
            <label class="label">
              <span class="label-text">Limite de Tempo (horas)</span>
            </label>
            <input 
              type="number" 
              name="time_limit_hours" 
              value={policy.time_limit_hours} 
              min="1" 
              class="input input-bordered" 
              required
            />
            <label class="label">
              <span class="label-text-alt">Tempo máximo para solicitar cancelamento após a compra</span>
            </label>
          </div>
          
          <!-- Percentual de Reembolso -->
          <div class="form-control">
            <label class="label">
              <span class="label-text">Percentual de Reembolso (%)</span>
            </label>
            <input 
              type="number" 
              name="refund_percentage" 
              value={policy.refund_percentage} 
              min="0" 
              max="100" 
              step="0.01" 
              class="input input-bordered" 
              required
            />
            <label class="label">
              <span class="label-text-alt">Percentual do valor a ser reembolsado</span>
            </label>
          </div>
          
          <!-- Requer Aprovação -->
          <div class="form-control">
            <label class="label cursor-pointer justify-start">
              <input 
                type="checkbox" 
                name="requires_approval" 
                class="checkbox mr-2" 
                checked={policy.requires_approval}
              />
              <span class="label-text">Requer aprovação manual</span>
            </label>
            <label class="label">
              <span class="label-text-alt">Se desmarcado, os cancelamentos serão aprovados automaticamente</span>
            </label>
          </div>
          
          <!-- Reembolso Automático -->
          <div class="form-control">
            <label class="label cursor-pointer justify-start">
              <input 
                type="checkbox" 
                name="auto_refund" 
                class="checkbox mr-2" 
                checked={policy.auto_refund}
              />
              <span class="label-text">Processar reembolso automaticamente</span>
            </label>
            <label class="label">
              <span class="label-text-alt">Se marcado, o reembolso será processado automaticamente após a aprovação</span>
            </label>
          </div>
          
          <!-- Status -->
          <div class="form-control">
            <label class="label cursor-pointer justify-start">
              <input 
                type="checkbox" 
                name="active" 
                class="checkbox mr-2" 
                checked={policy.active}
              />
              <span class="label-text">Política ativa</span>
            </label>
          </div>
          
          <!-- Botões de Ação -->
          <div class="card-actions justify-end">
            <a href="/admin/financial/cancellation/policies" class="btn btn-ghost">Cancelar</a>
            <button type="submit" class="btn btn-primary">Salvar Alterações</button>
          </div>
        </form>
      </div>
    </div>
  </div>
</AdminLayout>

<script>
  // Inicializar componentes interativos
  document.addEventListener('DOMContentLoaded', () => {
    // Código de inicialização, se necessário
  });
</script>
