/**
 * API de Download de XML de Documento Fiscal
 *
 * Endpoint para obter o XML de um documento fiscal.
 * Parte da implementação da tarefa 8.7.2 - Gestão de documentos fiscais
 */

import type { APIRoute } from 'astro';
import { FiscalDocumentRepository } from '../../../../domain/repositories/FiscalDocumentRepository';
import { FiscalProviderService } from '../../../../domain/services/FiscalProviderService';
import { PostgresFiscalDocumentRepository } from '../../../../infrastructure/database/repositories/PostgresFiscalDocumentRepository';
import { EfiPayFiscalProvider } from '../../../../infrastructure/services/EfiPayFiscalProvider';

// Inicializar repositório
const fiscalDocumentRepository: FiscalDocumentRepository = new PostgresFiscalDocumentRepository();

// Inicializar provedor fiscal
const fiscalProviderService: FiscalProviderService = new EfiPayFiscalProvider();

// Configurar provedor fiscal
(async () => {
  try {
    // Em um cenário real, estas configurações viriam do banco de dados
    await fiscalProviderService.initialize({
      apiKey: process.env.EFI_PAY_API_KEY || 'sandbox_api_key',
      apiSecret: process.env.EFI_PAY_API_SECRET || 'sandbox_api_secret',
      environment: 'homologation',
      companyDocument: '12345678000199',
      companyName: 'Estação da Alfabetização LTDA',
      timeout: 30000,
    });
  } catch (error) {
    console.error('Erro ao inicializar provedor fiscal:', error);
  }
})();

export const GET: APIRoute = async ({ params }) => {
  try {
    const { id } = params;

    if (!id) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'ID do documento é obrigatório.',
        }),
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    // Obter o documento fiscal
    const document = await fiscalDocumentRepository.getById(id);

    if (!document) {
      return new Response(
        JSON.stringify({
          success: false,
          error: `Documento fiscal com ID ${id} não encontrado.`,
        }),
        {
          status: 404,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    // Verificar se o documento tem XML
    if (document.xmlContent) {
      // Retornar o XML armazenado
      return new Response(document.xmlContent, {
        status: 200,
        headers: {
          'Content-Type': 'application/xml',
          'Content-Disposition': `attachment; filename="documento-fiscal-${id}.xml"`,
        },
      });
    }
    if (document.number && document.series) {
      // Tentar obter o XML do provedor fiscal
      const xml = await fiscalProviderService.getDocumentXml(
        document.type,
        document.number,
        document.series
      );

      if (xml) {
        // Atualizar o documento com o XML obtido
        const updatedDocument = { ...document, xmlContent: xml };
        await fiscalDocumentRepository.update(updatedDocument);

        // Retornar o XML obtido
        return new Response(xml, {
          status: 200,
          headers: {
            'Content-Type': 'application/xml',
            'Content-Disposition': `attachment; filename="documento-fiscal-${id}.xml"`,
          },
        });
      }
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Não foi possível obter o XML do documento fiscal.',
        }),
        {
          status: 404,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }
    return new Response(
      JSON.stringify({
        success: false,
        error: 'Documento fiscal não possui XML.',
      }),
      {
        status: 404,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  } catch (error) {
    console.error('Erro ao processar download de XML:', error);

    return new Response(
      JSON.stringify({
        success: false,
        error: 'Ocorreu um erro ao processar o download do XML. Por favor, tente novamente.',
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  }
};
