-- Migração para criar tabela de assinaturas de webhooks
-- Parte da implementação da tarefa 7.1.3 - Implementação de Webhooks

-- Criar enum para status de webhook
CREATE TYPE webhook_status AS ENUM (
  'active',
  'inactive',
  'failed'
);

-- Criar enum para eventos de webhook
CREATE TYPE webhook_event AS ENUM (
  'payment.created',
  'payment.updated',
  'payment.completed',
  'payment.failed',
  'payment.refunded',
  'order.created',
  'order.updated',
  'order.completed',
  'order.cancelled',
  'user.created',
  'user.updated',
  'product.created',
  'product.updated',
  'product.deleted'
);

-- Criar tabela para armazenar assinaturas de webhooks
CREATE TABLE IF NOT EXISTS webhook_subscriptions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(100) NOT NULL,
  url TEXT NOT NULL,
  events webhook_event[] NOT NULL,
  status webhook_status NOT NULL DEFAULT 'active',
  secret_key TEXT NOT NULL,
  description TEXT,
  headers JSONB,
  created_by UUID,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  last_used_at TIMESTAMP WITH TIME ZONE,
  
  CONSTRAINT fk_created_by FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
);

-- Criar índice para consultas por status
CREATE INDEX idx_webhook_subscriptions_status ON webhook_subscriptions(status);

-- Criar índice para consultas por eventos usando GIN para arrays
CREATE INDEX idx_webhook_subscriptions_events ON webhook_subscriptions USING GIN(events);

-- Criar tabela para armazenar histórico de entregas de webhooks
CREATE TABLE IF NOT EXISTS webhook_deliveries (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  subscription_id UUID NOT NULL,
  event webhook_event NOT NULL,
  payload JSONB NOT NULL,
  response_code INTEGER,
  response_body TEXT,
  status VARCHAR(20) NOT NULL,
  error_message TEXT,
  attempt_count INTEGER NOT NULL DEFAULT 1,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  completed_at TIMESTAMP WITH TIME ZONE,
  
  CONSTRAINT fk_subscription_id FOREIGN KEY (subscription_id) REFERENCES webhook_subscriptions(id) ON DELETE CASCADE
);

-- Criar índice para consultas por subscription_id
CREATE INDEX idx_webhook_deliveries_subscription_id ON webhook_deliveries(subscription_id);

-- Criar índice para consultas por status
CREATE INDEX idx_webhook_deliveries_status ON webhook_deliveries(status);

-- Adicionar comentários à tabela e colunas
COMMENT ON TABLE webhook_subscriptions IS 'Armazena assinaturas de webhooks para notificações de eventos';
COMMENT ON COLUMN webhook_subscriptions.id IS 'Identificador único da assinatura';
COMMENT ON COLUMN webhook_subscriptions.name IS 'Nome amigável para a assinatura';
COMMENT ON COLUMN webhook_subscriptions.url IS 'URL de destino para envio dos webhooks';
COMMENT ON COLUMN webhook_subscriptions.events IS 'Lista de eventos que acionam este webhook';
COMMENT ON COLUMN webhook_subscriptions.status IS 'Status atual da assinatura';
COMMENT ON COLUMN webhook_subscriptions.secret_key IS 'Chave secreta para assinatura dos payloads';
COMMENT ON COLUMN webhook_subscriptions.description IS 'Descrição opcional da assinatura';
COMMENT ON COLUMN webhook_subscriptions.headers IS 'Cabeçalhos HTTP adicionais a serem enviados';
COMMENT ON COLUMN webhook_subscriptions.created_by IS 'Usuário que criou a assinatura';
COMMENT ON COLUMN webhook_subscriptions.created_at IS 'Data e hora de criação da assinatura';
COMMENT ON COLUMN webhook_subscriptions.updated_at IS 'Data e hora da última atualização da assinatura';
COMMENT ON COLUMN webhook_subscriptions.last_used_at IS 'Data e hora do último envio bem-sucedido';

COMMENT ON TABLE webhook_deliveries IS 'Armazena histórico de entregas de webhooks';
COMMENT ON COLUMN webhook_deliveries.id IS 'Identificador único da entrega';
COMMENT ON COLUMN webhook_deliveries.subscription_id IS 'Referência à assinatura de webhook';
COMMENT ON COLUMN webhook_deliveries.event IS 'Evento que acionou o webhook';
COMMENT ON COLUMN webhook_deliveries.payload IS 'Payload enviado no webhook';
COMMENT ON COLUMN webhook_deliveries.response_code IS 'Código de resposta HTTP recebido';
COMMENT ON COLUMN webhook_deliveries.response_body IS 'Corpo da resposta recebida';
COMMENT ON COLUMN webhook_deliveries.status IS 'Status da entrega (success, failed, pending, etc)';
COMMENT ON COLUMN webhook_deliveries.error_message IS 'Mensagem de erro em caso de falha';
COMMENT ON COLUMN webhook_deliveries.attempt_count IS 'Número de tentativas de entrega';
COMMENT ON COLUMN webhook_deliveries.created_at IS 'Data e hora de criação da entrega';
COMMENT ON COLUMN webhook_deliveries.completed_at IS 'Data e hora de conclusão da entrega';
