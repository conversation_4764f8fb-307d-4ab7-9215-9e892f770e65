/**
 * Serviço de Fragmentos Estáticos
 *
 * Este serviço gerencia fragmentos estáticos para pré-renderização parcial.
 * Ele fornece métodos para invalidar fragmentos por ID, tag ou padrão.
 */

import { LogLevel } from '../application/interfaces/services/Logger';
import { deleteCache, getCache, setCache } from '../infrastructure/cache/CacheService';
import { ConsoleLogger } from '../infrastructure/logging/ConsoleLogger';

// Inicializar logger
const logger = new ConsoleLogger('StaticFragmentService', {
  level: LogLevel.INFO,
  useColors: true,
  format: 'text',
});

/**
 * Prefixo para chaves de fragmentos estáticos
 */
const FRAGMENT_PREFIX = 'static-fragment:';

/**
 * Prefixo para chaves de tags de fragmentos estáticos
 */
const TAG_PREFIX = 'static-fragment-tag:';

/**
 * Invalidar um fragmento estático específico
 *
 * @param id ID do fragmento
 * @param version Versão do fragmento (opcional)
 * @returns Verdadeiro se a invalidação foi bem-sucedida
 */
export async function invalidateFragment(id: string, version?: string): Promise<boolean> {
  try {
    // Se a versão for fornecida, invalidar apenas essa versão
    if (version) {
      const cacheKey = `${FRAGMENT_PREFIX}${id}:v${version}`;
      await deleteCache(cacheKey);
      logger.info(`Fragmento estático invalidado: ${cacheKey}`);
      return true;
    }

    // Caso contrário, invalidar todas as versões
    // Em uma implementação real, isso usaria algo como KEYS ou SCAN do Redis
    // para encontrar todas as chaves que correspondem ao padrão
    const pattern = `${FRAGMENT_PREFIX}${id}:*`;

    // Para simplificar, vamos apenas excluir a chave exata
    await deleteCache(pattern);
    logger.info(`Fragmento estático invalidado (todas as versões): ${id}`);

    return true;
  } catch (error) {
    logger.error(`Erro ao invalidar fragmento estático (${id}):`, error as Error);
    return false;
  }
}

/**
 * Invalidar fragmentos estáticos por tag
 *
 * @param tag Tag dos fragmentos
 * @returns Número de fragmentos invalidados
 */
export async function invalidateFragmentsByTag(tag: string): Promise<number> {
  try {
    // Obter todas as chaves associadas à tag
    const tagKey = `${TAG_PREFIX}${tag}`;
    const fragmentKeys = await getCache<string[]>(tagKey);

    if (!fragmentKeys || fragmentKeys.length === 0) {
      logger.info(`Nenhum fragmento encontrado para a tag: ${tag}`);
      return 0;
    }

    // Invalidar cada fragmento
    let invalidatedCount = 0;

    for (const key of fragmentKeys) {
      await deleteCache(key);
      invalidatedCount++;
    }

    // Limpar a lista de chaves da tag
    await deleteCache(tagKey);

    logger.info(`${invalidatedCount} fragmentos invalidados para a tag: ${tag}`);
    return invalidatedCount;
  } catch (error) {
    logger.error(`Erro ao invalidar fragmentos por tag (${tag}):`, error as Error);
    return 0;
  }
}

/**
 * Invalidar todos os fragmentos estáticos
 *
 * @returns Verdadeiro se a invalidação foi bem-sucedida
 */
export async function invalidateAllFragments(): Promise<boolean> {
  try {
    // Em uma implementação real, isso usaria algo como KEYS ou SCAN do Redis
    // para encontrar todas as chaves que correspondem ao padrão
    const pattern = `${FRAGMENT_PREFIX}*`;

    // Para simplificar, vamos apenas excluir a chave exata
    await deleteCache(pattern);

    // Limpar também todas as chaves de tags
    await deleteCache(`${TAG_PREFIX}*`);

    logger.info('Todos os fragmentos estáticos foram invalidados');
    return true;
  } catch (error) {
    logger.error('Erro ao invalidar todos os fragmentos estáticos:', error as Error);
    return false;
  }
}

/**
 * Obter informações sobre fragmentos em cache
 *
 * @returns Informações sobre fragmentos em cache
 */
export async function getFragmentStats(): Promise<{
  totalFragments: number;
  fragmentsByTag: Record<string, number>;
}> {
  try {
    // Em uma implementação real, isso usaria algo como KEYS ou SCAN do Redis
    // para obter estatísticas reais

    // Simulação de estatísticas
    return {
      totalFragments: 0,
      fragmentsByTag: {},
    };
  } catch (error) {
    logger.error('Erro ao obter estatísticas de fragmentos:', error as Error);
    return {
      totalFragments: 0,
      fragmentsByTag: {},
    };
  }
}
