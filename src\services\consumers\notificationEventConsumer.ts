/**
 * Consumidor de eventos de notificações
 *
 * Este serviço é responsável por consumir eventos relacionados a notificações
 * do Kafka.
 */

import { type EventHandler, eventConsumerService } from '@services/eventConsumerService';
interface ExtendedNotificationEvent extends NotificationEvent {
  templateData?: Record<string, unknown>;
  templateName?: string;
  alertType?: string;
  severity?: string;
  message?: string;
  source?: string;
}

import { queryHelper } from '@db/queryHelper';
import { emailService } from '@services/emailService';
import type { NotificationEvent } from '@services/eventProducerService';
import type { ProcessingContext } from '@services/messageProcessingService';
import { logger } from '@utils/logger';

/**
 * Handler para eventos de email enfileirado
 */
class EmailQueuedHandler implements EventHandler<NotificationEvent> {
  async handle(event: NotificationEvent, context: ProcessingContext): Promise<void> {
    try {
      logger.info(`Processando evento de email enfileirado: ${event.notificationId}`);

      // Verificar se a notificação existe
      const notification = await queryHelper.queryOne(
        'SELECT * FROM tab_notification WHERE ulid_notification = $1',
        [event.notificationId]
      );

      if (!notification) {
        logger.warn(`Notificação não encontrada: ${event.notificationId}`);
        return;
      }

      // Registrar evento no log
      await this.logNotificationEvent(event.notificationId, event);

      // Processar envio de email
      await this.processEmailSending(event);

      logger.info(`Evento de email enfileirado processado: ${event.notificationId}`);
    } catch (error) {
      logger.error(`Erro ao processar evento de email enfileirado ${event.notificationId}:`, error);
      throw error;
    }
  }

  /**
   * Registra evento de notificação no log
   * @param notificationId - ID da notificação
   * @param event - Evento de notificação
   */
  private async logNotificationEvent(
    notificationId: string,
    event: NotificationEvent
  ): Promise<void> {
    try {
      await queryHelper.query(
        `INSERT INTO tab_notification_log (
          ulid_notification_log, ulid_notification,
          event_type, event_data, created_at
        ) VALUES (
          gen_ulid(), $1, $2, $3, NOW()
        )`,
        [notificationId, 'email_queued', JSON.stringify(event)]
      );
    } catch (error) {
      logger.error(`Erro ao registrar evento de notificação ${notificationId}:`, error);
    }
  }

  /**
   * Processa envio de email
   * @param event - Evento de notificação
   */
  private async processEmailSending(event: NotificationEvent): Promise<void> {
    try {
      // Obter dados do template
      const extendedEvent = event as ExtendedNotificationEvent;
      const templateData = extendedEvent.templateData || {};
      const templateName = extendedEvent.templateName || 'default';

      // Verificar se há email e assunto
      if (!event.recipientEmail || !event.subject) {
        logger.warn(`Email ou assunto não fornecido para notificação ${event.notificationId}`);
        return;
      }

      // Enviar email
      const result = await emailService.sendEmail({
        to: event.recipientEmail,
        subject: event.subject,
        template: templateName,
        data: templateData,
      });

      // Atualizar status da notificação
      await queryHelper.query(
        `UPDATE tab_notification
         SET status = $1, updated_at = NOW()
         WHERE ulid_notification = $2`,
        [result.success ? 'sent' : 'failed', event.notificationId]
      );

      // Registrar resultado do envio
      await queryHelper.query(
        `INSERT INTO tab_notification_result (
          ulid_notification_result, ulid_notification,
          success, provider_response, created_at
        ) VALUES (
          gen_ulid(), $1, $2, $3, NOW()
        )`,
        [event.notificationId, result.success, JSON.stringify(result)]
      );
    } catch (error) {
      logger.error(`Erro ao processar envio de email ${event.notificationId}:`, error);

      // Atualizar status da notificação para falha
      await queryHelper.query(
        `UPDATE tab_notification
         SET status = 'failed', updated_at = NOW()
         WHERE ulid_notification = $1`,
        [event.notificationId]
      );

      // Registrar falha
      await queryHelper.query(
        `INSERT INTO tab_notification_result (
          ulid_notification_result, ulid_notification,
          success, error_message, created_at
        ) VALUES (
          gen_ulid(), $1, false, $2, NOW()
        )`,
        [event.notificationId, error instanceof Error ? error.message : 'Erro desconhecido']
      );
    }
  }
}

/**
 * Handler para eventos de alerta criado
 */
class AlertCreatedHandler implements EventHandler<NotificationEvent> {
  async handle(event: NotificationEvent, context: ProcessingContext): Promise<void> {
    try {
      logger.info(`Processando evento de alerta criado: ${event.notificationId}`);

      // Verificar se a notificação existe
      const notification = await queryHelper.queryOne(
        'SELECT * FROM tab_notification WHERE ulid_notification = $1',
        [event.notificationId]
      );

      if (!notification) {
        logger.warn(`Notificação não encontrada: ${event.notificationId}`);
        return;
      }

      // Registrar evento no log
      await this.logNotificationEvent(event.notificationId, event);

      // Processar alerta
      await this.processAlert(event);

      logger.info(`Evento de alerta criado processado: ${event.notificationId}`);
    } catch (error) {
      logger.error(`Erro ao processar evento de alerta criado ${event.notificationId}:`, error);
      throw error;
    }
  }

  /**
   * Registra evento de notificação no log
   * @param notificationId - ID da notificação
   * @param event - Evento de notificação
   */
  private async logNotificationEvent(
    notificationId: string,
    event: NotificationEvent
  ): Promise<void> {
    try {
      await queryHelper.query(
        `INSERT INTO tab_notification_log (
          ulid_notification_log, ulid_notification,
          event_type, event_data, created_at
        ) VALUES (
          gen_ulid(), $1, $2, $3, NOW()
        )`,
        [notificationId, 'alert_created', JSON.stringify(event)]
      );
    } catch (error) {
      logger.error(`Erro ao registrar evento de notificação ${notificationId}:`, error);
    }
  }

  /**
   * Processa alerta
   * @param event - Evento de notificação
   */
  private async processAlert(event: NotificationEvent): Promise<void> {
    try {
      // Obter dados do alerta
      const extendedEvent = event as ExtendedNotificationEvent;
      const alertType = extendedEvent.alertType || 'general';
      const severity = extendedEvent.severity || 'medium';
      const message = extendedEvent.message || 'Alerta sem mensagem';
      const source = extendedEvent.source || 'system';

      // Verificar se o alerta deve ser enviado por email
      const shouldSendEmail = severity === 'high' || severity === 'critical';

      if (shouldSendEmail) {
        // Obter emails dos administradores
        const admins = await queryHelper.query(
          `SELECT email FROM tab_user WHERE role = 'admin'`,
          []
        );

        // Enviar email para cada administrador
        for (const admin of admins.rows) {
          await emailService.sendEmail({
            to: admin.email,
            subject: `[${severity.toUpperCase()}] Alerta: ${alertType}`,
            template: 'alert-notification',
            data: {
              alertType,
              severity,
              message,
              source,
              timestamp: new Date().toISOString(),
              notificationId: event.notificationId,
            },
          });
        }
      }

      // Atualizar status da notificação
      await queryHelper.query(
        `UPDATE tab_notification
         SET status = 'processed', updated_at = NOW()
         WHERE ulid_notification = $1`,
        [event.notificationId]
      );
    } catch (error) {
      logger.error(`Erro ao processar alerta ${event.notificationId}:`, error);

      // Atualizar status da notificação para falha
      await queryHelper.query(
        `UPDATE tab_notification
         SET status = 'failed', updated_at = NOW()
         WHERE ulid_notification = $1`,
        [event.notificationId]
      );
    }
  }
}

/**
 * Inicializa o consumidor de eventos de notificação
 */
export async function initNotificationEventConsumer(): Promise<void> {
  try {
    // Registrar handlers
    eventConsumerService.registerHandler('notification.email.queued', new EmailQueuedHandler());

    eventConsumerService.registerHandler('notification.alert.created', new AlertCreatedHandler());

    // Iniciar consumidor
    await eventConsumerService.init({
      topics: [
        'notification.email.queued',
        'notification.email.sent',
        'notification.email.failed',
        'notification.alert.created',
        'notification.alert.resolved',
      ],
      groupId: 'notification-consumer-group',
    });

    logger.info('Consumidor de eventos de notificação inicializado');
  } catch (error) {
    logger.error('Erro ao inicializar consumidor de eventos de notificação:', error);
    throw error;
  }
}
