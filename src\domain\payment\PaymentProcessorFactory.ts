/**
 * Fábrica de processadores de pagamento
 *
 * Esta classe é responsável por criar instâncias de processadores de pagamento
 * com base na configuração do sistema.
 */

import { config } from '../../config';
import { logger } from '../../utils/logger';
import { PaymentProcessor } from '../interfaces/PaymentProcessor';
import { EfiPayProcessor } from './EfiPayProcessor';
import { MockPaymentProcessor } from './MockPaymentProcessor';

/**
 * Tipo para processadores de pagamento suportados
 */
export type SupportedPaymentProcessor = 'efipay' | 'mock';

/**
 * Fábrica de processadores de pagamento
 */
export namespace PaymentProcessorFactory {
  /**
   * Cache de instâncias de processadores
   */
  const instances: Map<string, PaymentProcessor> = new Map();

  /**
   * Obtém um processador de pagamento
   * @param type - Tipo de processador (opcional, usa o padrão da configuração se não informado)
   * @returns Instância do processador de pagamento
   */
  export function getProcessor(type?: SupportedPaymentProcessor): PaymentProcessor {
    // Se não informado, usar o padrão da configuração
    const processorType = type || (config.payment.defaultProcessor as SupportedPaymentProcessor);

    // Verificar se já existe uma instância no cache
    if (instances.has(processorType)) {
      return instances.get(processorType)!;
    }

    // Criar nova instância
    let processor: PaymentProcessor;

    switch (processorType) {
      case 'efipay':
        processor = new EfiPayProcessor();
        break;
      case 'mock':
        processor = new MockPaymentProcessor();
        break;
      default:
        logger.warn(
          `Tipo de processador de pagamento desconhecido: ${processorType}, usando EfiPay como fallback`
        );
        processor = new EfiPayProcessor();
    }

    // Armazenar no cache
    instances.set(processorType, processor);

    return processor;
  }

  /**
   * Limpa o cache de instâncias
   */
  export function clearCache(): void {
    instances.clear();
  }
}
