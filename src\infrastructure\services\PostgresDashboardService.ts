/**
 * PostgreSQL Dashboard Service
 *
 * Implementação do serviço de dashboard administrativo usando PostgreSQL.
 * Parte da implementação da tarefa 8.8.1 - Painel administrativo
 */

import { Pool } from 'pg';
import {
  ContentStats,
  DashboardStats,
  FiscalStats,
  SalesStats,
  SystemStats,
  TimeSeriesData,
  UserStats,
} from '../../domain/entities/DashboardStats';
import {
  DashboardFilter,
  DashboardService,
  DateRange,
} from '../../domain/services/DashboardService';
import { getDbConnection } from '../database/connection';

export class PostgresDashboardService implements DashboardService {
  private pool: Pool;

  constructor() {
    this.pool = getDbConnection();
  }

  /**
   * Obtém as estatísticas completas do dashboard
   */
  async getStats(filter?: DashboardFilter): Promise<DashboardStats> {
    const [
      users,
      content,
      sales,
      fiscal,
      system,
      recentActivity,
      salesChart,
      usersChart,
      contentChart,
      fiscalChart,
    ] = await Promise.all([
      this.getUserStats(filter),
      this.getContentStats(filter),
      this.getSalesStats(filter),
      this.getFiscalStats(filter),
      this.getSystemStats(),
      this.getRecentActivity(),
      this.getSalesChartData(filter?.dateRange || this.getDefaultDateRange(), 'day'),
      this.getUsersChartData(filter?.dateRange || this.getDefaultDateRange(), 'day'),
      this.getContentChartData(filter?.dateRange || this.getDefaultDateRange(), 'day'),
      this.getFiscalChartData(filter?.dateRange || this.getDefaultDateRange(), 'day'),
    ]);

    return {
      users,
      content,
      sales,
      fiscal,
      system,
      recentActivity,
      salesChart,
      usersChart,
      contentChart,
      fiscalChart,
      updatedAt: new Date(),
    };
  }

  /**
   * Obtém as estatísticas de usuários
   */
  async getUserStats(filter?: DashboardFilter): Promise<UserStats> {
    try {
      // Obter total de usuários
      const totalUsersQuery = `
        SELECT COUNT(*) as total
        FROM users
      `;

      // Obter usuários ativos
      const activeUsersQuery = `
        SELECT COUNT(*) as total
        FROM users
        WHERE is_active = true
      `;

      // Obter novos usuários hoje
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      const newUsersTodayQuery = `
        SELECT COUNT(*) as total
        FROM users
        WHERE created_at >= $1
      `;

      // Obter novos usuários esta semana
      const startOfWeek = new Date();
      startOfWeek.setDate(today.getDate() - today.getDay());
      startOfWeek.setHours(0, 0, 0, 0);

      const newUsersThisWeekQuery = `
        SELECT COUNT(*) as total
        FROM users
        WHERE created_at >= $1
      `;

      // Obter novos usuários este mês
      const startOfMonth = new Date();
      startOfMonth.setDate(1);
      startOfMonth.setHours(0, 0, 0, 0);

      const newUsersThisMonthQuery = `
        SELECT COUNT(*) as total
        FROM users
        WHERE created_at >= $1
      `;

      // Obter usuários por função
      const usersByRoleQuery = `
        SELECT role, COUNT(*) as total
        FROM users
        GROUP BY role
      `;

      // Executar consultas em paralelo
      const [
        totalUsersResult,
        activeUsersResult,
        newUsersTodayResult,
        newUsersThisWeekResult,
        newUsersThisMonthResult,
        usersByRoleResult,
      ] = await Promise.all([
        this.pool.query(totalUsersQuery),
        this.pool.query(activeUsersQuery),
        this.pool.query(newUsersTodayQuery, [today]),
        this.pool.query(newUsersThisWeekQuery, [startOfWeek]),
        this.pool.query(newUsersThisMonthQuery, [startOfMonth]),
        this.pool.query(usersByRoleQuery),
      ]);

      // Processar resultados
      const usersByRole: Record<string, number> = {};

      usersByRoleResult.rows.forEach((row) => {
        usersByRole[row.role] = Number.parseInt(row.total);
      });

      return {
        totalUsers: Number.parseInt(totalUsersResult.rows[0].total),
        activeUsers: Number.parseInt(activeUsersResult.rows[0].total),
        newUsersToday: Number.parseInt(newUsersTodayResult.rows[0].total),
        newUsersThisWeek: Number.parseInt(newUsersThisWeekResult.rows[0].total),
        newUsersThisMonth: Number.parseInt(newUsersThisMonthResult.rows[0].total),
        usersByRole,
      };
    } catch (error) {
      console.error('Erro ao obter estatísticas de usuários:', error);

      // Retornar valores padrão em caso de erro
      return {
        totalUsers: 0,
        activeUsers: 0,
        newUsersToday: 0,
        newUsersThisWeek: 0,
        newUsersThisMonth: 0,
        usersByRole: {},
      };
    }
  }

  /**
   * Obtém as estatísticas de conteúdo
   */
  async getContentStats(filter?: DashboardFilter): Promise<ContentStats> {
    try {
      // Obter total de conteúdo
      const totalContentQuery = `
        SELECT COUNT(*) as total
        FROM content
      `;

      // Obter conteúdo publicado
      const publishedContentQuery = `
        SELECT COUNT(*) as total
        FROM content
        WHERE status = 'published'
      `;

      // Obter conteúdo em rascunho
      const draftContentQuery = `
        SELECT COUNT(*) as total
        FROM content
        WHERE status = 'draft'
      `;

      // Obter conteúdo por tipo
      const contentByTypeQuery = `
        SELECT type, COUNT(*) as total
        FROM content
        GROUP BY type
      `;

      // Obter conteúdo por categoria
      const contentByCategoryQuery = `
        SELECT category, COUNT(*) as total
        FROM content
        GROUP BY category
      `;

      // Executar consultas em paralelo
      const [
        totalContentResult,
        publishedContentResult,
        draftContentResult,
        contentByTypeResult,
        contentByCategoryResult,
      ] = await Promise.all([
        this.pool.query(totalContentQuery),
        this.pool.query(publishedContentQuery),
        this.pool.query(draftContentQuery),
        this.pool.query(contentByTypeQuery),
        this.pool.query(contentByCategoryQuery),
      ]);

      // Processar resultados
      const contentByType: Record<string, number> = {};
      const contentByCategory: Record<string, number> = {};

      contentByTypeResult.rows.forEach((row) => {
        contentByType[row.type] = Number.parseInt(row.total);
      });

      contentByCategoryResult.rows.forEach((row) => {
        contentByCategory[row.category] = Number.parseInt(row.total);
      });

      return {
        totalContent: Number.parseInt(totalContentResult.rows[0].total),
        publishedContent: Number.parseInt(publishedContentResult.rows[0].total),
        draftContent: Number.parseInt(draftContentResult.rows[0].total),
        contentByType,
        contentByCategory,
      };
    } catch (error) {
      console.error('Erro ao obter estatísticas de conteúdo:', error);

      // Retornar valores padrão em caso de erro
      return {
        totalContent: 0,
        publishedContent: 0,
        draftContent: 0,
        contentByType: {},
        contentByCategory: {},
      };
    }
  }

  /**
   * Obtém as estatísticas de vendas
   */
  async getSalesStats(filter?: DashboardFilter): Promise<SalesStats> {
    try {
      // Obter total de vendas
      const totalSalesQuery = `
        SELECT COUNT(*) as total, SUM(amount) as amount
        FROM sales
        WHERE status = 'completed'
      `;

      // Obter vendas de hoje
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      const salesTodayQuery = `
        SELECT COUNT(*) as total, SUM(amount) as amount
        FROM sales
        WHERE status = 'completed'
        AND created_at >= $1
      `;

      // Obter vendas desta semana
      const startOfWeek = new Date();
      startOfWeek.setDate(today.getDate() - today.getDay());
      startOfWeek.setHours(0, 0, 0, 0);

      const salesThisWeekQuery = `
        SELECT COUNT(*) as total, SUM(amount) as amount
        FROM sales
        WHERE status = 'completed'
        AND created_at >= $1
      `;

      // Obter vendas deste mês
      const startOfMonth = new Date();
      startOfMonth.setDate(1);
      startOfMonth.setHours(0, 0, 0, 0);

      const salesThisMonthQuery = `
        SELECT COUNT(*) as total, SUM(amount) as amount
        FROM sales
        WHERE status = 'completed'
        AND created_at >= $1
      `;

      // Obter vendas por produto
      const salesByProductQuery = `
        SELECT product_id, COUNT(*) as total
        FROM sale_items
        GROUP BY product_id
      `;

      // Executar consultas em paralelo
      const [
        totalSalesResult,
        salesTodayResult,
        salesThisWeekResult,
        salesThisMonthResult,
        salesByProductResult,
      ] = await Promise.all([
        this.pool.query(totalSalesQuery),
        this.pool.query(salesTodayQuery, [today]),
        this.pool.query(salesThisWeekQuery, [startOfWeek]),
        this.pool.query(salesThisMonthQuery, [startOfMonth]),
        this.pool.query(salesByProductQuery),
      ]);

      // Processar resultados
      const salesByProduct: Record<string, number> = {};

      salesByProductResult.rows.forEach((row) => {
        salesByProduct[row.product_id] = Number.parseInt(row.total);
      });

      return {
        totalSales: Number.parseInt(totalSalesResult.rows[0].total) || 0,
        salesAmount: Number.parseFloat(totalSalesResult.rows[0].amount) || 0,
        salesToday: Number.parseInt(salesTodayResult.rows[0].total) || 0,
        salesTodayAmount: Number.parseFloat(salesTodayResult.rows[0].amount) || 0,
        salesThisWeek: Number.parseInt(salesThisWeekResult.rows[0].total) || 0,
        salesThisWeekAmount: Number.parseFloat(salesThisWeekResult.rows[0].amount) || 0,
        salesThisMonth: Number.parseInt(salesThisMonthResult.rows[0].total) || 0,
        salesThisMonthAmount: Number.parseFloat(salesThisMonthResult.rows[0].amount) || 0,
        salesByProduct,
      };
    } catch (error) {
      console.error('Erro ao obter estatísticas de vendas:', error);

      // Retornar valores padrão em caso de erro
      return {
        totalSales: 0,
        salesAmount: 0,
        salesToday: 0,
        salesTodayAmount: 0,
        salesThisWeek: 0,
        salesThisWeekAmount: 0,
        salesThisMonth: 0,
        salesThisMonthAmount: 0,
        salesByProduct: {},
      };
    }
  }

  /**
   * Obtém as estatísticas fiscais
   */
  async getFiscalStats(filter?: DashboardFilter): Promise<FiscalStats> {
    try {
      // Obter estatísticas fiscais da tabela fiscal_stats
      const fiscalStatsQuery = `
        SELECT * FROM fiscal_stats
        ORDER BY updated_at DESC
        LIMIT 1
      `;

      const fiscalStatsResult = await this.pool.query(fiscalStatsQuery);

      if (fiscalStatsResult.rows.length === 0) {
        return {
          totalDocuments: 0,
          issuedDocuments: 0,
          cancelledDocuments: 0,
          errorDocuments: 0,
          totalValue: 0,
          documentsByType: {},
          documentsByStatus: {},
        };
      }

      const stats = fiscalStatsResult.rows[0];

      return {
        totalDocuments: stats.total_documents,
        issuedDocuments: stats.issued_documents,
        cancelledDocuments: stats.cancelled_documents,
        errorDocuments: stats.error_documents,
        totalValue: Number.parseFloat(stats.total_value),
        documentsByType: stats.type_distribution,
        documentsByStatus: stats.status_distribution,
      };
    } catch (error) {
      console.error('Erro ao obter estatísticas fiscais:', error);

      // Retornar valores padrão em caso de erro
      return {
        totalDocuments: 0,
        issuedDocuments: 0,
        cancelledDocuments: 0,
        errorDocuments: 0,
        totalValue: 0,
        documentsByType: {},
        documentsByStatus: {},
      };
    }
  }

  /**
   * Obtém as estatísticas do sistema
   */
  async getSystemStats(): Promise<SystemStats> {
    try {
      // Em um cenário real, obteríamos essas informações de um serviço de monitoramento
      // Para este exemplo, vamos retornar valores simulados

      return {
        uptime: 86400, // 1 dia em segundos
        cpuUsage: 25.5, // 25.5%
        memoryUsage: 40.2, // 40.2%
        diskUsage: 65.8, // 65.8%
        lastBackup: new Date(Date.now() - 12 * 60 * 60 * 1000), // 12 horas atrás
        activeConnections: 42,
      };
    } catch (error) {
      console.error('Erro ao obter estatísticas do sistema:', error);

      // Retornar valores padrão em caso de erro
      return {
        uptime: 0,
        cpuUsage: 0,
        memoryUsage: 0,
        diskUsage: 0,
        activeConnections: 0,
      };
    }
  }

  /**
   * Obtém a atividade recente
   */
  async getRecentActivity(limit = 10): Promise<DashboardStats['recentActivity']> {
    try {
      // Obter atividade recente
      const recentActivityQuery = `
        SELECT * FROM activity_log
        ORDER BY timestamp DESC
        LIMIT $1
      `;

      const recentActivityResult = await this.pool.query(recentActivityQuery, [limit]);

      // Processar resultados
      return recentActivityResult.rows.map((row) => ({
        id: row.id,
        type: row.entity_type,
        action: row.action,
        user: row.user_id,
        timestamp: new Date(row.timestamp),
        details: row.details,
      }));
    } catch (error) {
      console.error('Erro ao obter atividade recente:', error);

      // Retornar array vazio em caso de erro
      return [];
    }
  }

  /**
   * Obtém dados de série temporal para vendas
   */
  async getSalesChartData(
    dateRange: DateRange,
    groupBy: 'day' | 'week' | 'month'
  ): Promise<TimeSeriesData> {
    try {
      // Definir formato de data e intervalo com base no agrupamento
      let dateFormat: string;
      let interval: string;

      switch (groupBy) {
        case 'day':
          dateFormat = 'YYYY-MM-DD';
          interval = '1 day';
          break;
        case 'week':
          dateFormat = 'YYYY-"W"IW';
          interval = '1 week';
          break;
        case 'month':
          dateFormat = 'YYYY-MM';
          interval = '1 month';
          break;
      }

      // Obter dados de vendas agrupados
      const salesChartQuery = `
        SELECT
          TO_CHAR(date_trunc($1, created_at), $2) as label,
          COUNT(*) as count,
          SUM(amount) as amount
        FROM sales
        WHERE created_at BETWEEN $3 AND $4
        AND status = 'completed'
        GROUP BY label
        ORDER BY label
      `;

      const salesChartResult = await this.pool.query(salesChartQuery, [
        groupBy,
        dateFormat,
        dateRange.startDate,
        dateRange.endDate,
      ]);

      // Processar resultados
      const labels: string[] = [];
      const countData: number[] = [];
      const amountData: number[] = [];

      salesChartResult.rows.forEach((row) => {
        labels.push(row.label);
        countData.push(Number.parseInt(row.count));
        amountData.push(Number.parseFloat(row.amount));
      });

      return {
        labels,
        datasets: [
          {
            label: 'Número de Vendas',
            data: countData,
            color: '#3b82f6', // blue-500
          },
          {
            label: 'Valor de Vendas',
            data: amountData,
            color: '#10b981', // emerald-500
          },
        ],
      };
    } catch (error) {
      console.error('Erro ao obter dados de gráfico de vendas:', error);

      // Retornar dados vazios em caso de erro
      return {
        labels: [],
        datasets: [
          {
            label: 'Número de Vendas',
            data: [],
          },
          {
            label: 'Valor de Vendas',
            data: [],
          },
        ],
      };
    }
  }

  /**
   * Obtém dados de série temporal para usuários
   */
  async getUsersChartData(
    dateRange: DateRange,
    groupBy: 'day' | 'week' | 'month'
  ): Promise<TimeSeriesData> {
    try {
      // Definir formato de data e intervalo com base no agrupamento
      let dateFormat: string;
      let interval: string;

      switch (groupBy) {
        case 'day':
          dateFormat = 'YYYY-MM-DD';
          interval = '1 day';
          break;
        case 'week':
          dateFormat = 'YYYY-"W"IW';
          interval = '1 week';
          break;
        case 'month':
          dateFormat = 'YYYY-MM';
          interval = '1 month';
          break;
      }

      // Obter dados de usuários agrupados
      const usersChartQuery = `
        SELECT
          TO_CHAR(date_trunc($1, created_at), $2) as label,
          COUNT(*) as count
        FROM users
        WHERE created_at BETWEEN $3 AND $4
        GROUP BY label
        ORDER BY label
      `;

      const usersChartResult = await this.pool.query(usersChartQuery, [
        groupBy,
        dateFormat,
        dateRange.startDate,
        dateRange.endDate,
      ]);

      // Processar resultados
      const labels: string[] = [];
      const countData: number[] = [];

      usersChartResult.rows.forEach((row) => {
        labels.push(row.label);
        countData.push(Number.parseInt(row.count));
      });

      return {
        labels,
        datasets: [
          {
            label: 'Novos Usuários',
            data: countData,
            color: '#8b5cf6', // violet-500
          },
        ],
      };
    } catch (error) {
      console.error('Erro ao obter dados de gráfico de usuários:', error);

      // Retornar dados vazios em caso de erro
      return {
        labels: [],
        datasets: [
          {
            label: 'Novos Usuários',
            data: [],
          },
        ],
      };
    }
  }

  /**
   * Obtém dados de série temporal para conteúdo
   */
  async getContentChartData(
    dateRange: DateRange,
    groupBy: 'day' | 'week' | 'month'
  ): Promise<TimeSeriesData> {
    try {
      // Definir formato de data e intervalo com base no agrupamento
      let dateFormat: string;
      let interval: string;

      switch (groupBy) {
        case 'day':
          dateFormat = 'YYYY-MM-DD';
          interval = '1 day';
          break;
        case 'week':
          dateFormat = 'YYYY-"W"IW';
          interval = '1 week';
          break;
        case 'month':
          dateFormat = 'YYYY-MM';
          interval = '1 month';
          break;
      }

      // Obter dados de conteúdo agrupados
      const contentChartQuery = `
        SELECT
          TO_CHAR(date_trunc($1, created_at), $2) as label,
          COUNT(*) as count,
          COUNT(*) FILTER (WHERE status = 'published') as published
        FROM content
        WHERE created_at BETWEEN $3 AND $4
        GROUP BY label
        ORDER BY label
      `;

      const contentChartResult = await this.pool.query(contentChartQuery, [
        groupBy,
        dateFormat,
        dateRange.startDate,
        dateRange.endDate,
      ]);

      // Processar resultados
      const labels: string[] = [];
      const totalData: number[] = [];
      const publishedData: number[] = [];

      contentChartResult.rows.forEach((row) => {
        labels.push(row.label);
        totalData.push(Number.parseInt(row.count));
        publishedData.push(Number.parseInt(row.published));
      });

      return {
        labels,
        datasets: [
          {
            label: 'Total de Conteúdo',
            data: totalData,
            color: '#f59e0b', // amber-500
          },
          {
            label: 'Conteúdo Publicado',
            data: publishedData,
            color: '#84cc16', // lime-500
          },
        ],
      };
    } catch (error) {
      console.error('Erro ao obter dados de gráfico de conteúdo:', error);

      // Retornar dados vazios em caso de erro
      return {
        labels: [],
        datasets: [
          {
            label: 'Total de Conteúdo',
            data: [],
          },
          {
            label: 'Conteúdo Publicado',
            data: [],
          },
        ],
      };
    }
  }

  /**
   * Obtém dados de série temporal para documentos fiscais
   */
  async getFiscalChartData(
    dateRange: DateRange,
    groupBy: 'day' | 'week' | 'month'
  ): Promise<TimeSeriesData> {
    try {
      // Definir formato de data e intervalo com base no agrupamento
      let dateFormat: string;
      let interval: string;

      switch (groupBy) {
        case 'day':
          dateFormat = 'YYYY-MM-DD';
          interval = '1 day';
          break;
        case 'week':
          dateFormat = 'YYYY-"W"IW';
          interval = '1 week';
          break;
        case 'month':
          dateFormat = 'YYYY-MM';
          interval = '1 month';
          break;
      }

      // Obter dados de documentos fiscais agrupados
      const fiscalChartQuery = `
        SELECT
          TO_CHAR(date_trunc($1, issue_date), $2) as label,
          COUNT(*) as count,
          SUM(final_value) as amount
        FROM fiscal_documents
        WHERE issue_date BETWEEN $3 AND $4
        AND status = 'ISSUED'
        GROUP BY label
        ORDER BY label
      `;

      const fiscalChartResult = await this.pool.query(fiscalChartQuery, [
        groupBy,
        dateFormat,
        dateRange.startDate,
        dateRange.endDate,
      ]);

      // Processar resultados
      const labels: string[] = [];
      const countData: number[] = [];
      const amountData: number[] = [];

      fiscalChartResult.rows.forEach((row) => {
        labels.push(row.label);
        countData.push(Number.parseInt(row.count));
        amountData.push(Number.parseFloat(row.amount));
      });

      return {
        labels,
        datasets: [
          {
            label: 'Número de Documentos',
            data: countData,
            color: '#ec4899', // pink-500
          },
          {
            label: 'Valor Total',
            data: amountData,
            color: '#14b8a6', // teal-500
          },
        ],
      };
    } catch (error) {
      console.error('Erro ao obter dados de gráfico de documentos fiscais:', error);

      // Retornar dados vazios em caso de erro
      return {
        labels: [],
        datasets: [
          {
            label: 'Número de Documentos',
            data: [],
          },
          {
            label: 'Valor Total',
            data: [],
          },
        ],
      };
    }
  }

  /**
   * Obtém um intervalo de datas padrão (últimos 30 dias)
   */
  private getDefaultDateRange(): DateRange {
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - 30);

    return { startDate, endDate };
  }
}
