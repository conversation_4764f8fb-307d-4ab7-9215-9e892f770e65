---
import { PageTransition } from '../../../components/transitions';
import DaisyButton from '../../../components/ui/DaisyButton.astro';
import DaisyCard from '../../../components/ui/DaisyCard.astro';
import DaisyTabs from '../../../components/ui/DaisyTabs.astro';
import Breadcrumbs from '../../../components/ui/navigation/Breadcrumbs.astro';
/**
 * Página de Gerenciamento de Redes Sociais
 *
 * Interface para gerenciar redes sociais.
 * Parte da implementação da tarefa 8.5.3 - Integração com redes sociais
 */
import BaseLayout from '../../../layouts/BaseLayout.astro';
import { Container, Section } from '../../../layouts/grid';

// Título da página
const title = 'Gerenciamento de Redes Sociais';

// Breadcrumbs para a página atual
const breadcrumbItems = [
  { href: '/', label: 'In<PERSON>cio' },
  { href: '/admin', label: 'Administração' },
  { label: 'Redes Sociais' },
];

// Obter parâmetros de consulta
const tab = Astro.url.searchParams.get('tab') || 'accounts';

// Em um cenário real, buscaríamos os dados do repositório
// Por enquanto, usaremos dados de exemplo
const connectedAccounts = [
  {
    id: 'fb_123',
    platform: 'facebook',
    username: 'estacaodaalfabetizacao',
    displayName: 'Estação da Alfabetização',
    profileUrl: 'https://facebook.com/estacaodaalfabetizacao',
    isConnected: true,
    lastSyncedAt: new Date(Date.now() - ********), // 1 dia atrás
    followerCount: 1250,
    followingCount: 350,
    postCount: 87,
  },
  {
    id: 'tw_456',
    platform: 'twitter',
    username: 'estacaoalfabet',
    displayName: 'Estação da Alfabetização',
    profileUrl: 'https://twitter.com/estacaoalfabet',
    isConnected: true,
    lastSyncedAt: new Date(Date.now() - *********), // 2 dias atrás
    followerCount: 850,
    followingCount: 420,
    postCount: 156,
  },
  {
    id: 'ig_789',
    platform: 'instagram',
    username: 'estacaodaalfabetizacao',
    displayName: 'Estação da Alfabetização',
    profileUrl: 'https://instagram.com/estacaodaalfabetizacao',
    isConnected: true,
    lastSyncedAt: new Date(Date.now() - *********), // 3 dias atrás
    followerCount: 2100,
    followingCount: 450,
    postCount: 95,
  },
];

// Dados de exemplo para posts recentes
const recentPosts = [
  {
    id: 'post_123',
    platform: 'facebook',
    content: 'Confira nosso novo material sobre alfabetização! 📚 #educação #alfabetização',
    url: 'https://facebook.com/estacaodaalfabetizacao/posts/123',
    publishedAt: new Date(Date.now() - ********), // 1 dia atrás
    analytics: {
      impressions: 1250,
      engagements: 320,
      clicks: 85,
      shares: 15,
      likes: 45,
      comments: 8,
      reach: 1800,
    },
  },
  {
    id: 'post_456',
    platform: 'twitter',
    content:
      'Dicas para ajudar crianças com dificuldades de leitura. Confira em nosso blog! #educação #leitura',
    url: 'https://twitter.com/estacaoalfabet/status/456',
    publishedAt: new Date(Date.now() - *********), // 2 dias atrás
    analytics: {
      impressions: 950,
      engagements: 210,
      clicks: 65,
      shares: 12,
      likes: 35,
      comments: 5,
      reach: 1400,
    },
  },
  {
    id: 'post_789',
    platform: 'instagram',
    content:
      'Atividades lúdicas para desenvolver a escrita. Swipe para ver todas as imagens! ✏️ #educação #escrita',
    url: 'https://instagram.com/p/789',
    publishedAt: new Date(Date.now() - *********), // 3 dias atrás
    analytics: {
      impressions: 1850,
      engagements: 420,
      clicks: 0, // Instagram não tem clicks diretos
      shares: 25,
      likes: 120,
      comments: 18,
      reach: 2200,
    },
  },
];

// Dados de exemplo para análises
const analytics = {
  facebook: {
    impressions: 12500,
    engagements: 3200,
    clicks: 850,
    shares: 150,
    likes: 450,
    comments: 80,
    reach: 18000,
  },
  twitter: {
    impressions: 9500,
    engagements: 2100,
    clicks: 650,
    shares: 120,
    likes: 350,
    comments: 50,
    reach: 14000,
  },
  instagram: {
    impressions: 18500,
    engagements: 4200,
    clicks: 0,
    shares: 250,
    likes: 1200,
    comments: 180,
    reach: 22000,
  },
};

// Função para formatar número
const formatNumber = (num: number): string => {
  if (num >= 1000000) {
    return `${(num / 1000000).toFixed(1)}M`;
  }
  if (num >= 1000) {
    return `${(num / 1000).toFixed(1)}K`;
  }
  return num.toString();
};

// Função para formatar data relativa
const formatRelativeTime = (date: Date): string => {
  const now = new Date();
  const diffMs = now.getTime() - date.getTime();
  const diffSec = Math.floor(diffMs / 1000);
  const diffMin = Math.floor(diffSec / 60);
  const diffHour = Math.floor(diffMin / 60);
  const diffDay = Math.floor(diffHour / 24);

  if (diffSec < 60) {
    return 'agora';
  }
  if (diffMin < 60) {
    return `${diffMin} min atrás`;
  }
  if (diffHour < 24) {
    return `${diffHour} h atrás`;
  }
  if (diffDay < 7) {
    return `${diffDay} d atrás`;
  }
  return date.toLocaleDateString('pt-BR');
};

// Função para obter ícone da plataforma
const getPlatformIcon = (platform: string): string => {
  switch (platform) {
    case 'facebook':
      return 'facebook';
    case 'twitter':
      return 'twitter';
    case 'instagram':
      return 'instagram';
    case 'linkedin':
      return 'linkedin';
    case 'youtube':
      return 'youtube';
    case 'tiktok':
      return 'tiktok';
    default:
      return 'share';
  }
};

// Função para obter cor da plataforma
const getPlatformColor = (platform: string): string => {
  switch (platform) {
    case 'facebook':
      return 'text-blue-600';
    case 'twitter':
      return 'text-blue-400';
    case 'instagram':
      return 'text-pink-500';
    case 'linkedin':
      return 'text-blue-700';
    case 'youtube':
      return 'text-red-600';
    case 'tiktok':
      return 'text-black';
    default:
      return 'text-gray-500';
  }
};

// Abas
const tabs = [
  { id: 'accounts', label: 'Contas Conectadas', content: '' },
  { id: 'posts', label: 'Posts Recentes', content: '' },
  { id: 'analytics', label: 'Análises', content: '' },
  { id: 'publish', label: 'Publicar', content: '' },
];
---

<BaseLayout title={title} enableViewTransitions={true} transitionType="fade">
  <Container>
    <PageTransition type="fade" duration="0.5s">
      <Section>
        <Breadcrumbs items={breadcrumbItems} class="mb-6" />
        
        <div class="flex justify-between items-center mb-6">
          <h1 class="text-3xl font-bold">{title}</h1>
          
          <div class="flex gap-2">
            <DaisyButton 
              href="/admin/social-media/connect" 
              variant="primary" 
              icon="plus"
              text="Conectar Conta"
            />
            
            <DaisyButton 
              href="/admin/social-media/publish" 
              variant="outline" 
              icon="send"
              text="Nova Publicação"
            />
          </div>
        </div>
        
        <div class="tabs-container">
          <div class="tabs tabs-boxed mb-6">
            {tabs.map(t => (
              <a 
                href={`/admin/social-media?tab=${t.id}`} 
                class={`tab ${t.id === tab ? 'tab-active' : ''}`}
              >
                {t.label}
              </a>
            ))}
          </div>
          
          <div class="tab-content">
            {tab === 'accounts' && (
              <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {connectedAccounts.map(account => (
                  <DaisyCard>
                    <div class="p-6">
                      <div class="flex items-center gap-4 mb-4">
                        <div class={`text-2xl ${getPlatformColor(account.platform)}`}>
                          <i class={`icon icon-${getPlatformIcon(account.platform)}`}></i>
                        </div>
                        <div>
                          <h3 class="font-bold">{account.displayName}</h3>
                          <p class="text-sm text-gray-500">@{account.username}</p>
                        </div>
                      </div>
                      
                      <div class="grid grid-cols-3 gap-2 mb-4 text-center">
                        <div>
                          <div class="font-bold">{formatNumber(account.followerCount)}</div>
                          <div class="text-xs text-gray-500">Seguidores</div>
                        </div>
                        <div>
                          <div class="font-bold">{formatNumber(account.followingCount)}</div>
                          <div class="text-xs text-gray-500">Seguindo</div>
                        </div>
                        <div>
                          <div class="font-bold">{formatNumber(account.postCount)}</div>
                          <div class="text-xs text-gray-500">Posts</div>
                        </div>
                      </div>
                      
                      <div class="text-xs text-gray-500 mb-4">
                        Última sincronização: {formatRelativeTime(account.lastSyncedAt)}
                      </div>
                      
                      <div class="flex justify-between">
                        <a 
                          href={account.profileUrl} 
                          target="_blank" 
                          rel="noopener noreferrer" 
                          class="btn btn-sm btn-ghost"
                        >
                          <i class="icon icon-external-link mr-1"></i>
                          Ver Perfil
                        </a>
                        
                        <div class="flex gap-1">
                          <button class="btn btn-sm btn-ghost" data-action="sync" data-id={account.id}>
                            <i class="icon icon-refresh-cw"></i>
                          </button>
                          
                          <button class="btn btn-sm btn-ghost text-error" data-action="disconnect" data-id={account.id}>
                            <i class="icon icon-log-out"></i>
                          </button>
                        </div>
                      </div>
                    </div>
                  </DaisyCard>
                ))}
                
                <DaisyCard>
                  <div class="p-6 flex flex-col items-center justify-center h-full text-center">
                    <div class="text-4xl text-gray-300 mb-4">
                      <i class="icon icon-plus-circle"></i>
                    </div>
                    <h3 class="font-bold mb-2">Conectar Nova Conta</h3>
                    <p class="text-sm text-gray-500 mb-4">Adicione uma nova rede social para expandir seu alcance.</p>
                    <a href="/admin/social-media/connect" class="btn btn-primary">
                      Conectar Conta
                    </a>
                  </div>
                </DaisyCard>
              </div>
            )}
            
            {tab === 'posts' && (
              <div class="space-y-6">
                {recentPosts.map(post => (
                  <DaisyCard>
                    <div class="p-6">
                      <div class="flex items-start gap-4">
                        <div class={`text-2xl ${getPlatformColor(post.platform)}`}>
                          <i class={`icon icon-${getPlatformIcon(post.platform)}`}></i>
                        </div>
                        <div class="flex-1">
                          <div class="flex justify-between items-start mb-2">
                            <h3 class="font-bold">{connectedAccounts.find(a => a.platform === post.platform)?.displayName}</h3>
                            <span class="text-xs text-gray-500">{formatRelativeTime(post.publishedAt)}</span>
                          </div>
                          
                          <p class="mb-4">{post.content}</p>
                          
                          <div class="grid grid-cols-4 gap-2 mb-4 text-center text-sm">
                            <div>
                              <div class="font-bold">{formatNumber(post.analytics.impressions)}</div>
                              <div class="text-xs text-gray-500">Impressões</div>
                            </div>
                            <div>
                              <div class="font-bold">{formatNumber(post.analytics.engagements)}</div>
                              <div class="text-xs text-gray-500">Engajamentos</div>
                            </div>
                            <div>
                              <div class="font-bold">{formatNumber(post.analytics.likes)}</div>
                              <div class="text-xs text-gray-500">Curtidas</div>
                            </div>
                            <div>
                              <div class="font-bold">{formatNumber(post.analytics.shares)}</div>
                              <div class="text-xs text-gray-500">Compartilhamentos</div>
                            </div>
                          </div>
                          
                          <div class="flex justify-between">
                            <a 
                              href={post.url} 
                              target="_blank" 
                              rel="noopener noreferrer" 
                              class="btn btn-sm btn-ghost"
                            >
                              <i class="icon icon-external-link mr-1"></i>
                              Ver Post
                            </a>
                            
                            <div class="flex gap-1">
                              <a href={`/admin/social-media/analytics?postId=${post.id}`} class="btn btn-sm btn-ghost">
                                <i class="icon icon-bar-chart-2"></i>
                              </a>
                              
                              <button class="btn btn-sm btn-ghost text-error" data-action="delete" data-id={post.id}>
                                <i class="icon icon-trash"></i>
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </DaisyCard>
                ))}
                
                <div class="flex justify-center">
                  <a href="/admin/social-media/posts" class="btn btn-outline">
                    Ver Todos os Posts
                  </a>
                </div>
              </div>
            )}
            
            {tab === 'analytics' && (
              <div class="space-y-6">
                <DaisyCard>
                  <div class="p-6">
                    <h2 class="text-xl font-bold mb-4">Resumo de Desempenho</h2>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                      <div class="bg-base-200 p-4 rounded-lg text-center">
                        <div class="text-3xl font-bold">
                          {formatNumber(
                            analytics.facebook.impressions + 
                            analytics.twitter.impressions + 
                            analytics.instagram.impressions
                          )}
                        </div>
                        <div class="text-sm text-gray-500">Impressões Totais</div>
                      </div>
                      
                      <div class="bg-base-200 p-4 rounded-lg text-center">
                        <div class="text-3xl font-bold">
                          {formatNumber(
                            analytics.facebook.engagements + 
                            analytics.twitter.engagements + 
                            analytics.instagram.engagements
                          )}
                        </div>
                        <div class="text-sm text-gray-500">Engajamentos Totais</div>
                      </div>
                      
                      <div class="bg-base-200 p-4 rounded-lg text-center">
                        <div class="text-3xl font-bold">
                          {formatNumber(
                            analytics.facebook.likes + 
                            analytics.twitter.likes + 
                            analytics.instagram.likes
                          )}
                        </div>
                        <div class="text-sm text-gray-500">Curtidas Totais</div>
                      </div>
                      
                      <div class="bg-base-200 p-4 rounded-lg text-center">
                        <div class="text-3xl font-bold">
                          {formatNumber(
                            analytics.facebook.shares + 
                            analytics.twitter.shares + 
                            analytics.instagram.shares
                          )}
                        </div>
                        <div class="text-sm text-gray-500">Compartilhamentos Totais</div>
                      </div>
                    </div>
                    
                    <h3 class="font-bold mb-2">Desempenho por Plataforma</h3>
                    
                    <div class="space-y-4">
                      {Object.entries(analytics).map(([platform, data]) => (
                        <div class="bg-base-200 p-4 rounded-lg">
                          <div class="flex items-center gap-2 mb-2">
                            <i class={`icon icon-${getPlatformIcon(platform)} ${getPlatformColor(platform)}`}></i>
                            <h4 class="font-bold">{platform.charAt(0).toUpperCase() + platform.slice(1)}</h4>
                          </div>
                          
                          <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-center text-sm">
                            <div>
                              <div class="font-bold">{formatNumber(data.impressions)}</div>
                              <div class="text-xs text-gray-500">Impressões</div>
                            </div>
                            <div>
                              <div class="font-bold">{formatNumber(data.engagements)}</div>
                              <div class="text-xs text-gray-500">Engajamentos</div>
                            </div>
                            <div>
                              <div class="font-bold">{formatNumber(data.likes)}</div>
                              <div class="text-xs text-gray-500">Curtidas</div>
                            </div>
                            <div>
                              <div class="font-bold">{formatNumber(data.shares)}</div>
                              <div class="text-xs text-gray-500">Compartilhamentos</div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </DaisyCard>
                
                <div class="flex justify-center">
                  <a href="/admin/social-media/analytics" class="btn btn-outline">
                    Ver Análises Detalhadas
                  </a>
                </div>
              </div>
            )}
            
            {tab === 'publish' && (
              <DaisyCard>
                <div class="p-6">
                  <h2 class="text-xl font-bold mb-4">Publicar nas Redes Sociais</h2>
                  
                  <form action="/admin/social-media/publish" method="POST" class="space-y-4">
                    <div class="form-control">
                      <label class="label">
                        <span class="label-text">Plataformas</span>
                      </label>
                      <div class="flex flex-wrap gap-4">
                        {connectedAccounts.map(account => (
                          <label class="flex items-center gap-2 cursor-pointer">
                            <input 
                              type="checkbox" 
                              name="platforms[]" 
                              value={account.platform} 
                              class="checkbox checkbox-primary" 
                              checked
                            />
                            <i class={`icon icon-${getPlatformIcon(account.platform)} ${getPlatformColor(account.platform)}`}></i>
                            <span>{account.platform.charAt(0).toUpperCase() + account.platform.slice(1)}</span>
                          </label>
                        ))}
                      </div>
                    </div>
                    
                    <div class="form-control">
                      <label class="label">
                        <span class="label-text">Conteúdo</span>
                      </label>
                      <textarea 
                        name="content" 
                        class="textarea textarea-bordered h-32" 
                        placeholder="O que você gostaria de compartilhar?"
                        required
                      ></textarea>
                    </div>
                    
                    <div class="form-control">
                      <label class="label">
                        <span class="label-text">Imagens (opcional)</span>
                      </label>
                      <input 
                        type="file" 
                        name="media" 
                        class="file-input file-input-bordered w-full" 
                        multiple
                        accept="image/*"
                      />
                    </div>
                    
                    <div class="form-control">
                      <label class="label">
                        <span class="label-text">Link (opcional)</span>
                      </label>
                      <input 
                        type="url" 
                        name="link" 
                        class="input input-bordered" 
                        placeholder="https://exemplo.com/pagina"
                      />
                    </div>
                    
                    <div class="form-control">
                      <label class="flex items-center gap-2 cursor-pointer">
                        <input 
                          type="checkbox" 
                          name="schedule_enabled" 
                          id="schedule-checkbox"
                          class="checkbox checkbox-primary" 
                        />
                        <span>Agendar publicação</span>
                      </label>
                    </div>
                    
                    <div id="schedule-options" class="hidden space-y-4">
                      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="form-control">
                          <label class="label">
                            <span class="label-text">Data</span>
                          </label>
                          <input 
                            type="date" 
                            name="schedule_date" 
                            class="input input-bordered" 
                          />
                        </div>
                        
                        <div class="form-control">
                          <label class="label">
                            <span class="label-text">Hora</span>
                          </label>
                          <input 
                            type="time" 
                            name="schedule_time" 
                            class="input input-bordered" 
                          />
                        </div>
                      </div>
                    </div>
                    
                    <div class="flex justify-end">
                      <button type="submit" class="btn btn-primary">
                        <i class="icon icon-send mr-2"></i>
                        Publicar
                      </button>
                    </div>
                  </form>
                </div>
              </DaisyCard>
            )}
          </div>
        </div>
      </Section>
    </PageTransition>
  </Container>
</BaseLayout>

<script>
  // Script para funcionalidade da página de redes sociais
  document.addEventListener('DOMContentLoaded', () => {
    // Mostrar/ocultar opções de agendamento
    const scheduleCheckbox = document.getElementById('schedule-checkbox') as HTMLInputElement;
    const scheduleOptions = document.getElementById('schedule-options');
    
    if (scheduleCheckbox && scheduleOptions) {
      scheduleCheckbox.addEventListener('change', () => {
        if (scheduleCheckbox.checked) {
          scheduleOptions.classList.remove('hidden');
        } else {
          scheduleOptions.classList.add('hidden');
        }
      });
    }
    
    // Botões de ação para contas
    const accountButtons = document.querySelectorAll('[data-action="sync"], [data-action="disconnect"]');
    
    accountButtons.forEach(button => {
      button.addEventListener('click', async () => {
        const action = button.getAttribute('data-action');
        const accountId = button.getAttribute('data-id');
        
        if (action === 'sync') {
          // Em um cenário real, aqui seria feita uma chamada para a API
          console.log(`Sincronizando conta ${accountId}`);
          alert(`Sincronizando conta ${accountId}`);
        } else if (action === 'disconnect') {
          if (confirm('Tem certeza que deseja desconectar esta conta?')) {
            // Em um cenário real, aqui seria feita uma chamada para a API
            console.log(`Desconectando conta ${accountId}`);
            alert(`Conta ${accountId} desconectada`);
          }
        }
      });
    });
    
    // Botões de ação para posts
    const postButtons = document.querySelectorAll('[data-action="delete"]');
    
    postButtons.forEach(button => {
      button.addEventListener('click', async () => {
        const postId = button.getAttribute('data-id');
        
        if (confirm('Tem certeza que deseja excluir este post?')) {
          // Em um cenário real, aqui seria feita uma chamada para a API
          console.log(`Excluindo post ${postId}`);
          alert(`Post ${postId} excluído`);
        }
      });
    });
  });
</script>
