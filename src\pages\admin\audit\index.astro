---
/**
 * <PERSON><PERSON>gina de logs de auditoria
 *
 * Esta página permite visualizar e filtrar logs de auditoria do sistema.
 */

import PermissionGate from '@components/auth/PermissionGate.astro';
// Importações
import MainLayout from '@layouts/MainLayout.astro';
import { auditRepository } from '@repository/auditRepository';
import { AuditEventType, AuditSeverity, auditService } from '@services/auditService';
import { getCurrentUser } from '@utils/authUtils';

// Verificar autenticação
const user = await getCurrentUser(Astro.cookies);

// Redirecionar se não estiver autenticado
if (!user) {
  return Astro.redirect('/signin?redirect=/admin/audit');
}

// Obter parâmetros de consulta
const page = Number.parseInt(Astro.url.searchParams.get('page') || '1', 10);
const pageSize = Number.parseInt(Astro.url.searchParams.get('pageSize') || '20', 10);
const eventType = Astro.url.searchParams.get('eventType') || undefined;
const userId = Astro.url.searchParams.get('userId') || undefined;
const resource = Astro.url.searchParams.get('resource') || undefined;
const action = Astro.url.searchParams.get('action') || undefined;
const severity = Astro.url.searchParams.get('severity') || undefined;
const startDateStr = Astro.url.searchParams.get('startDate') || undefined;
const endDateStr = Astro.url.searchParams.get('endDate') || undefined;

// Converter strings de data para objetos Date
const startDate = startDateStr ? new Date(startDateStr) : undefined;
const endDate = endDateStr ? new Date(endDateStr) : undefined;

// Buscar logs de auditoria
const auditLogs = await auditService.searchEvents(
  {
    eventType: eventType as AuditEventType | undefined,
    userId,
    resource,
    action,
    severity: severity as AuditSeverity | undefined,
    startDate,
    endDate,
  },
  page,
  pageSize
);

// Obter configurações de auditoria
const configResult = await auditRepository.getConfig();
const auditConfig = configResult.rows;

// Agrupar tipos de eventos por categoria
const eventCategories = {
  Autenticação: [
    AuditEventType.LOGIN_SUCCESS,
    AuditEventType.LOGIN_FAILURE,
    AuditEventType.LOGOUT,
    AuditEventType.PASSWORD_CHANGE,
    AuditEventType.PASSWORD_RESET,
  ],
  Autorização: [
    AuditEventType.PERMISSION_GRANTED,
    AuditEventType.PERMISSION_DENIED,
    AuditEventType.ROLE_ASSIGNED,
    AuditEventType.ROLE_REMOVED,
  ],
  Administração: [
    AuditEventType.USER_CREATED,
    AuditEventType.USER_UPDATED,
    AuditEventType.USER_DELETED,
    AuditEventType.ROLE_CREATED,
    AuditEventType.ROLE_UPDATED,
    AuditEventType.ROLE_DELETED,
    AuditEventType.PERMISSION_CREATED,
    AuditEventType.PERMISSION_UPDATED,
    AuditEventType.PERMISSION_DELETED,
  ],
  Recursos: [
    AuditEventType.RESOURCE_ACCESSED,
    AuditEventType.RESOURCE_CREATED,
    AuditEventType.RESOURCE_UPDATED,
    AuditEventType.RESOURCE_DELETED,
  ],
  Sistema: [
    AuditEventType.SYSTEM_ERROR,
    AuditEventType.SYSTEM_WARNING,
    AuditEventType.SYSTEM_CONFIG_CHANGED,
  ],
};

// Mapear descrições para tipos de eventos
const eventTypeDescriptions: Record<string, string> = {};
auditConfig.forEach((config) => {
  eventTypeDescriptions[config.event_type] = config.description;
});

// Título da página
const title = 'Logs de Auditoria';

// Função para formatar data
function formatDate(dateStr: string): string {
  const date = new Date(dateStr);
  return date.toLocaleString();
}

// Função para obter classe CSS com base na severidade
function getSeverityClass(severity: string): string {
  switch (severity) {
    case AuditSeverity.INFO:
      return 'bg-blue-100 text-blue-800';
    case AuditSeverity.WARNING:
      return 'bg-yellow-100 text-yellow-800';
    case AuditSeverity.ERROR:
      return 'bg-red-100 text-red-800';
    case AuditSeverity.CRITICAL:
      return 'bg-purple-100 text-purple-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
}

// Função para obter classe CSS com base no resultado
function getResultClass(result: string): string {
  switch (result) {
    case 'success':
      return 'bg-green-100 text-green-800';
    case 'failure':
      return 'bg-red-100 text-red-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
}
---

<MainLayout title={title}>
  <main class="container mx-auto px-4 py-8">
    <PermissionGate resource="audit" action="read">
      <div class="flex items-center justify-between mb-6">
        <h1 class="text-3xl font-bold">{title}</h1>
        
        <div class="flex space-x-2">
          <a 
            href="/admin" 
            class="bg-gray-200 text-gray-800 px-4 py-2 rounded hover:bg-gray-300 transition"
          >
            Voltar
          </a>
        </div>
      </div>
      
      <!-- Filtros -->
      <div class="bg-white rounded-lg shadow p-6 mb-6">
        <h2 class="text-xl font-semibold mb-4">Filtros</h2>
        
        <form method="get" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <!-- Tipo de evento -->
          <div>
            <label for="eventType" class="block text-sm font-medium text-gray-700 mb-1">Tipo de Evento</label>
            <select 
              id="eventType" 
              name="eventType" 
              class="w-full px-3 py-2 border border-gray-300 rounded-md"
            >
              <option value="">Todos os tipos</option>
              {Object.entries(eventCategories).map(([category, events]) => (
                <optgroup label={category}>
                  {events.map(event => (
                    <option value={event} selected={eventType === event}>
                      {eventTypeDescriptions[event] || event}
                    </option>
                  ))}
                </optgroup>
              ))}
            </select>
          </div>
          
          <!-- Recurso -->
          <div>
            <label for="resource" class="block text-sm font-medium text-gray-700 mb-1">Recurso</label>
            <input 
              type="text" 
              id="resource" 
              name="resource" 
              value={resource || ''}
              placeholder="Ex: users, roles, etc."
              class="w-full px-3 py-2 border border-gray-300 rounded-md"
            >
          </div>
          
          <!-- Ação -->
          <div>
            <label for="action" class="block text-sm font-medium text-gray-700 mb-1">Ação</label>
            <input 
              type="text" 
              id="action" 
              name="action" 
              value={action || ''}
              placeholder="Ex: create, update, delete"
              class="w-full px-3 py-2 border border-gray-300 rounded-md"
            >
          </div>
          
          <!-- Severidade -->
          <div>
            <label for="severity" class="block text-sm font-medium text-gray-700 mb-1">Severidade</label>
            <select 
              id="severity" 
              name="severity" 
              class="w-full px-3 py-2 border border-gray-300 rounded-md"
            >
              <option value="">Todas as severidades</option>
              <option value={AuditSeverity.INFO} selected={severity === AuditSeverity.INFO}>Informação</option>
              <option value={AuditSeverity.WARNING} selected={severity === AuditSeverity.WARNING}>Aviso</option>
              <option value={AuditSeverity.ERROR} selected={severity === AuditSeverity.ERROR}>Erro</option>
              <option value={AuditSeverity.CRITICAL} selected={severity === AuditSeverity.CRITICAL}>Crítico</option>
            </select>
          </div>
          
          <!-- Data inicial -->
          <div>
            <label for="startDate" class="block text-sm font-medium text-gray-700 mb-1">Data Inicial</label>
            <input 
              type="datetime-local" 
              id="startDate" 
              name="startDate" 
              value={startDateStr || ''}
              class="w-full px-3 py-2 border border-gray-300 rounded-md"
            >
          </div>
          
          <!-- Data final -->
          <div>
            <label for="endDate" class="block text-sm font-medium text-gray-700 mb-1">Data Final</label>
            <input 
              type="datetime-local" 
              id="endDate" 
              name="endDate" 
              value={endDateStr || ''}
              class="w-full px-3 py-2 border border-gray-300 rounded-md"
            >
          </div>
          
          <!-- Botões -->
          <div class="md:col-span-2 lg:col-span-3 flex justify-end space-x-2 mt-4">
            <a 
              href="/admin/audit" 
              class="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50 transition"
            >
              Limpar Filtros
            </a>
            <button 
              type="submit"
              class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition"
            >
              Filtrar
            </button>
          </div>
        </form>
      </div>
      
      <!-- Resultados -->
      <div class="bg-white rounded-lg shadow overflow-hidden">
        <div class="p-6 bg-gray-50 border-b">
          <h2 class="text-xl font-semibold">Logs de Auditoria</h2>
          <p class="mt-1 text-gray-600">
            Mostrando {auditLogs.events.length} de {auditLogs.total} registros
          </p>
        </div>
        
        <div class="overflow-x-auto">
          {auditLogs.events.length > 0 ? (
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Data/Hora
                  </th>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Evento
                  </th>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Usuário
                  </th>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Recurso
                  </th>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Ação
                  </th>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Resultado
                  </th>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Severidade
                  </th>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Detalhes
                  </th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                {auditLogs.events.map(event => (
                  <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {formatDate(event.timestamp)}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm font-medium text-gray-900">{eventTypeDescriptions[event.eventType] || event.eventType}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      {event.userName ? (
                        <div class="text-sm text-gray-900">{event.userName}</div>
                      ) : (
                        <span class="text-sm text-gray-500">-</span>
                      )}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm text-gray-900">{event.resource || '-'}</div>
                      {event.resourceId && (
                        <div class="text-xs text-gray-500">{event.resourceId}</div>
                      )}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {event.action || '-'}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      {event.result && (
                        <span class={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getResultClass(event.result)}`}>
                          {event.result}
                        </span>
                      )}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <span class={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getSeverityClass(event.severity)}`}>
                        {event.severity}
                      </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <button 
                        type="button"
                        class="text-blue-600 hover:text-blue-800"
                        data-event-id={event.id}
                      >
                        Ver detalhes
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          ) : (
            <div class="p-6 text-center text-gray-500">
              Nenhum registro encontrado com os filtros selecionados.
            </div>
          )}
        </div>
        
        <!-- Paginação -->
        {auditLogs.totalPages > 1 && (
          <div class="px-6 py-4 bg-gray-50 border-t flex items-center justify-between">
            <div class="text-sm text-gray-700">
              Página <span class="font-medium">{auditLogs.page}</span> de <span class="font-medium">{auditLogs.totalPages}</span>
            </div>
            
            <div class="flex space-x-2">
              {auditLogs.page > 1 && (
                <a 
                  href={`/admin/audit?page=${auditLogs.page - 1}&pageSize=${pageSize}${eventType ? `&eventType=${eventType}` : ''}${resource ? `&resource=${resource}` : ''}${action ? `&action=${action}` : ''}${severity ? `&severity=${severity}` : ''}${startDateStr ? `&startDate=${startDateStr}` : ''}${endDateStr ? `&endDate=${endDateStr}` : ''}`}
                  class="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50 transition"
                >
                  Anterior
                </a>
              )}
              
              {auditLogs.page < auditLogs.totalPages && (
                <a 
                  href={`/admin/audit?page=${auditLogs.page + 1}&pageSize=${pageSize}${eventType ? `&eventType=${eventType}` : ''}${resource ? `&resource=${resource}` : ''}${action ? `&action=${action}` : ''}${severity ? `&severity=${severity}` : ''}${startDateStr ? `&startDate=${startDateStr}` : ''}${endDateStr ? `&endDate=${endDateStr}` : ''}`}
                  class="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50 transition"
                >
                  Próxima
                </a>
              )}
            </div>
          </div>
        )}
      </div>
      
      <slot name="fallback">
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mt-6">
          <p>Você não tem permissão para acessar os logs de auditoria.</p>
          <p class="mt-2">
            <a href="/" class="text-red-700 font-medium hover:underline">Voltar para a página inicial</a>
          </p>
        </div>
      </slot>
    </PermissionGate>
  </main>
  
  <!-- Modal de detalhes -->
  <div id="event-details-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center hidden z-50">
    <div class="bg-white rounded-lg shadow-lg max-w-2xl w-full max-h-[80vh] overflow-y-auto">
      <div class="p-6 border-b">
        <div class="flex items-center justify-between">
          <h3 class="text-xl font-semibold" id="modal-title">Detalhes do Evento</h3>
          <button type="button" class="text-gray-400 hover:text-gray-500" id="close-modal">
            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
      </div>
      
      <div class="p-6" id="modal-content">
        <div class="space-y-4">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <!-- Conteúdo será preenchido via JavaScript -->
          </div>
          
          <div>
            <h4 class="font-medium mb-2">Metadados</h4>
            <pre id="event-metadata" class="bg-gray-50 p-4 rounded-md overflow-x-auto text-sm"></pre>
          </div>
        </div>
      </div>
      
      <div class="p-6 border-t bg-gray-50">
        <button 
          type="button"
          class="w-full bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition"
          id="close-modal-btn"
        >
          Fechar
        </button>
      </div>
    </div>
  </div>
</MainLayout>

<script>
  // Script para interatividade no cliente
  document.addEventListener('DOMContentLoaded', () => {
    // Modal de detalhes
    const modal = document.getElementById('event-details-modal');
    const closeModalBtn = document.getElementById('close-modal-btn');
    const closeModalX = document.getElementById('close-modal');
    const modalTitle = document.getElementById('modal-title');
    const modalContent = document.getElementById('modal-content').querySelector('.grid');
    const eventMetadata = document.getElementById('event-metadata');
    
    // Botões de detalhes
    const detailButtons = document.querySelectorAll('[data-event-id]');
    
    // Função para abrir modal com detalhes do evento
    const openEventDetails = async (eventId) => {
      try {
        // Buscar detalhes do evento
        const response = await fetch(`/api/audit/events/${eventId}`);
        const data = await response.json();
        
        if (data.success && data.event) {
          const event = data.event;
          
          // Atualizar título
          modalTitle.textContent = `Evento: ${event.eventType}`;
          
          // Limpar conteúdo anterior
          modalContent.innerHTML = '';
          
          // Adicionar detalhes do evento
          const details = [
            { label: 'ID', value: event.id },
            { label: 'Tipo', value: event.eventType },
            { label: 'Data/Hora', value: new Date(event.timestamp).toLocaleString() },
            { label: 'Usuário', value: event.userName || '-' },
            { label: 'ID do Usuário', value: event.userId || '-' },
            { label: 'IP', value: event.ipAddress || '-' },
            { label: 'User-Agent', value: event.userAgent || '-' },
            { label: 'Recurso', value: event.resource || '-' },
            { label: 'ID do Recurso', value: event.resourceId || '-' },
            { label: 'Ação', value: event.action || '-' },
            { label: 'Resultado', value: event.result || '-' },
            { label: 'Severidade', value: event.severity || '-' }
          ];
          
          details.forEach(detail => {
            const detailElement = document.createElement('div');
            detailElement.innerHTML = `
              <div class="text-sm font-medium text-gray-500">${detail.label}</div>
              <div class="text-sm text-gray-900">${detail.value}</div>
            `;
            modalContent.appendChild(detailElement);
          });
          
          // Adicionar metadados
          if (event.metadata) {
            eventMetadata.textContent = JSON.stringify(event.metadata, null, 2);
            eventMetadata.parentElement.classList.remove('hidden');
          } else {
            eventMetadata.textContent = '{}';
            eventMetadata.parentElement.classList.add('hidden');
          }
          
          // Exibir modal
          modal.classList.remove('hidden');
        } else {
          alert('Erro ao carregar detalhes do evento.');
        }
      } catch (error) {
        console.error('Erro ao carregar detalhes do evento:', error);
        alert('Erro ao carregar detalhes do evento.');
      }
    };
    
    // Adicionar evento de clique aos botões de detalhes
    detailButtons.forEach(button => {
      button.addEventListener('click', () => {
        const eventId = button.getAttribute('data-event-id');
        openEventDetails(eventId);
      });
    });
    
    // Fechar modal
    const closeModal = () => {
      modal.classList.add('hidden');
    };
    
    closeModalBtn.addEventListener('click', closeModal);
    closeModalX.addEventListener('click', closeModal);
    
    // Fechar modal ao clicar fora
    modal.addEventListener('click', (e) => {
      if (e.target === modal) {
        closeModal();
      }
    });
    
    // Fechar modal com tecla ESC
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape' && !modal.classList.contains('hidden')) {
        closeModal();
      }
    });
  });
</script>

<style>
  /* Estilos específicos da página */
</style>
