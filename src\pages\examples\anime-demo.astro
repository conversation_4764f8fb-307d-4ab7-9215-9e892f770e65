---
import IconSystem from '../../components/icons/IconSystem.astro';
import IllustrationSystem from '../../components/illustrations/IllustrationSystem.astro';
import { PageTransition } from '../../components/transitions';
import DaisyCard from '../../components/ui/DaisyCard.astro';
import Breadcrumbs from '../../components/ui/navigation/Breadcrumbs.astro';
/**
 * Página de demonstração do AnimeJS
 * Esta página mostra exemplos de animações usando AnimeJS
 */
import BaseLayout from '../../layouts/BaseLayout.astro';
import { Container, Grid, Section } from '../../layouts/grid';

// Título da página
const title = 'Demonstração AnimeJS';

// Breadcrumbs para a página atual
const breadcrumbItems = [
  { href: '/', label: 'Início' },
  { href: '/examples', label: 'Exemplos' },
  { label: 'AnimeJS' },
];

// Exemplos de animações
const animations = [
  { name: 'fadeIn', title: 'Fade In', description: 'Animação de aparecimento gradual' },
  { name: 'fadeOut', title: 'Fade Out', description: 'Animação de desaparecimento gradual' },
  { name: 'slideIn', title: 'Slide In', description: 'Animação de entrada deslizante' },
  { name: 'slideOut', title: 'Slide Out', description: 'Animação de saída deslizante' },
  { name: 'scaleIn', title: 'Scale In', description: 'Animação de aumento de escala' },
  { name: 'scaleOut', title: 'Scale Out', description: 'Animação de diminuição de escala' },
  { name: 'rotate', title: 'Rotate', description: 'Animação de rotação' },
  { name: 'pulse', title: 'Pulse', description: 'Animação de pulsação' },
  { name: 'shake', title: 'Shake', description: 'Animação de tremor' },
  { name: 'bounce', title: 'Bounce', description: 'Animação de quicar' },
  {
    name: 'textAnimation',
    title: 'Text Animation',
    description: 'Animação de texto letra por letra',
  },
];

// Exemplos de animações educacionais
const educationalAnimations = [
  { name: 'letterPop', title: 'Letter Pop', description: 'Animação de letras saltitantes' },
  { name: 'numberSpin', title: 'Number Spin', description: 'Animação de números girando' },
  {
    name: 'wordAnimation',
    title: 'Word Animation',
    description: 'Animação de palavras letra por letra',
  },
  {
    name: 'writingEffect',
    title: 'Writing Effect',
    description: 'Efeito de escrita em tempo real',
  },
  {
    name: 'trainAnimation',
    title: 'Train Animation',
    description: 'Animação de trem em movimento',
  },
];
---

<BaseLayout title={title} enableViewTransitions={true} transitionType="fade">
  <Container>
    <PageTransition type="fade" duration="0.5s">
      <Section>
        <Breadcrumbs items={breadcrumbItems} class="mb-6" />
        
        <h1 class="text-3xl font-bold mb-6">{title}</h1>
        
        <p class="mb-8">
          Esta página demonstra as animações disponíveis usando AnimeJS no projeto Estação da Alfabetização.
          Clique nos botões para ver as animações em ação.
        </p>
        
        <h2 class="text-2xl font-bold mb-4">Animações Básicas</h2>
        <p class="mb-4">
          Estas são animações gerais que podem ser aplicadas a qualquer elemento da interface.
        </p>
        
        <Grid cols={{ sm: 1, md: 2, lg: 3 }} gap={6} class="mb-12">
          {animations.map(animation => (
            <DaisyCard title={animation.title} class="h-full">
              <div class="flex flex-col h-full">
                <p class="mb-4">{animation.description}</p>
                
                <div class="flex justify-center items-center p-6 bg-base-200 rounded-box h-32 mb-4 overflow-hidden">
                  <div class="animation-target" data-animation={animation.name}>
                    {animation.name === "textAnimation" ? (
                      <span class="text-xl font-bold">Alfabetização</span>
                    ) : (
                      <IconSystem name="school" size={48} color="var(--primary-blue)" />
                    )}
                  </div>
                </div>
                
                <div class="mt-auto">
                  <button class="btn btn-primary w-full play-button" data-animation={animation.name}>
                    Executar Animação
                  </button>
                </div>
              </div>
            </DaisyCard>
          ))}
        </Grid>
        
        <h2 class="text-2xl font-bold mb-4 mt-12">Animações Educacionais</h2>
        <p class="mb-4">
          Estas são animações específicas para conteúdo educacional e alfabetização.
        </p>
        
        <Grid cols={{ sm: 1, md: 2, lg: 3 }} gap={6} class="mb-12">
          {educationalAnimations.map(animation => (
            <DaisyCard title={animation.title} class="h-full">
              <div class="flex flex-col h-full">
                <p class="mb-4">{animation.description}</p>
                
                <div class="flex justify-center items-center p-6 bg-base-200 rounded-box h-32 mb-4 overflow-hidden">
                  <div class="educational-target" data-animation={animation.name}>
                    {animation.name === "letterPop" ? (
                      <span class="text-4xl font-bold">ABC</span>
                    ) : animation.name === "numberSpin" ? (
                      <span class="text-4xl font-bold">123</span>
                    ) : animation.name === "wordAnimation" || animation.name === "writingEffect" ? (
                      <span class="text-xl font-bold">Alfabetização</span>
                    ) : animation.name === "trainAnimation" ? (
                      <IllustrationSystem name="train" width={150} height={80} />
                    ) : (
                      <IconSystem name="school" size={48} color="var(--primary-blue)" />
                    )}
                  </div>
                </div>
                
                <div class="mt-auto">
                  <button class="btn btn-accent w-full play-educational-button" data-animation={animation.name}>
                    Executar Animação
                  </button>
                </div>
              </div>
            </DaisyCard>
          ))}
        </Grid>
        
        <h2 class="text-2xl font-bold mb-4 mt-12">Sequência de Animações</h2>
        <p class="mb-4">
          Exemplo de como criar uma sequência de animações encadeadas.
        </p>
        
        <div class="border rounded-box p-6 mb-12">
          <div class="flex flex-col items-center">
            <div class="flex justify-center items-center p-6 bg-base-200 rounded-box w-full h-64 mb-4 overflow-hidden">
              <div class="sequence-container flex flex-col items-center">
                <div class="sequence-item mb-4">
                  <IllustrationSystem name="train" width={200} height={100} />
                </div>
                <div class="sequence-item text-3xl font-bold mb-4">Estação da Alfabetização</div>
                <div class="sequence-item flex gap-4">
                  <IconSystem name="book" size={48} color="var(--primary-blue)" />
                  <IconSystem name="school" size={48} color="var(--primary-blue)" />
                  <IconSystem name="assignment" size={48} color="var(--primary-blue)" />
                </div>
              </div>
            </div>
            
            <button class="btn btn-lg btn-primary play-sequence-button">
              Executar Sequência
            </button>
          </div>
        </div>
        
        <h2 class="text-2xl font-bold mb-4 mt-12">Animações ao Rolar</h2>
        <p class="mb-4">
          Exemplo de elementos que animam quando entram na viewport durante a rolagem.
        </p>
        
        <div class="border rounded-box p-6 mb-12">
          <div class="flex flex-col gap-12 py-8">
            {[1, 2, 3, 4, 5].map(index => (
              <div class="scroll-animate-item flex items-center gap-6 p-6 bg-base-200 rounded-box">
                <IconSystem name={["book", "school", "assignment", "quiz", "person"][index-1]} size={48} color="var(--primary-blue)" />
                <div>
                  <h3 class="text-xl font-bold mb-2">Item {index}</h3>
                  <p>Este elemento será animado quando entrar na viewport durante a rolagem da página.</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </Section>
    </PageTransition>
  </Container>
</BaseLayout>

<script>
  import { anime, animeHelpers, AnimationSequence } from '../../scripts/anime-config.js';
  import { educationalAnimations, setupScrollAnimations, prefersReducedMotion } from '../../scripts/animation-helpers.js';
  
  // Verificar se o usuário prefere movimento reduzido
  const reduceMotion = prefersReducedMotion();
  
  if (!reduceMotion) {
    // Configurar animações básicas
    document.querySelectorAll('.play-button').forEach(button => {
      button.addEventListener('click', () => {
        const animationName = button.getAttribute('data-animation');
        const target = button.closest('.card').querySelector('.animation-target');
        
        // Resetar o estado do elemento
        anime.set(target, {
          opacity: 1,
          translateX: 0,
          translateY: 0,
          scale: 1,
          rotate: 0
        });
        
        // Executar a animação correspondente
        if (animeHelpers[animationName]) {
          animeHelpers[animationName](target);
        }
      });
    });
    
    // Configurar animações educacionais
    document.querySelectorAll('.play-educational-button').forEach(button => {
      button.addEventListener('click', () => {
        const animationName = button.getAttribute('data-animation');
        const target = button.closest('.card').querySelector('.educational-target');
        
        // Resetar o estado do elemento
        anime.set(target, {
          opacity: 1,
          translateX: 0,
          translateY: 0,
          scale: 1,
          rotate: 0,
          rotateY: 0
        });
        
        // Executar a animação correspondente
        if (educationalAnimations[animationName]) {
          educationalAnimations[animationName](target);
        }
      });
    });
    
    // Configurar sequência de animações
    const sequenceButton = document.querySelector('.play-sequence-button');
    if (sequenceButton) {
      sequenceButton.addEventListener('click', () => {
        const items = document.querySelectorAll('.sequence-item');
        
        // Resetar o estado dos elementos
        anime.set(items, {
          opacity: 0,
          translateY: 20
        });
        
        // Criar e executar a sequência
        const sequence = new AnimationSequence();
        
        sequence
          .add(() => animeHelpers.slideIn(items[0], 'right'))
          .add(() => animeHelpers.textAnimation(items[1]), '+=300')
          .add(() => animeHelpers.fadeIn(items[2]), '+=300')
          .play();
      });
      
      // Executar a sequência automaticamente na carga da página
      setTimeout(() => {
        sequenceButton.click();
      }, 1000);
    }
    
    // Configurar animações ao rolar
    setupScrollAnimations('.scroll-animate-item', (element) => {
      animeHelpers.fadeIn(element, {
        translateY: [50, 0],
        duration: 800
      });
    }, {
      threshold: 0.2
    });
  } else {
    // Se o usuário prefere movimento reduzido, mostrar todos os elementos sem animação
    document.querySelectorAll('.animation-target, .educational-target, .sequence-item, .scroll-animate-item').forEach(el => {
      el.style.opacity = 1;
    });
    
    // Desabilitar botões
    document.querySelectorAll('.play-button, .play-educational-button, .play-sequence-button').forEach(button => {
      button.disabled = true;
      button.textContent = 'Animações desativadas';
    });
  }
</script>
