/**
 * Testes para o serviço de Dead Letter Queue
 */

import { type Mock, afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { kafka } from '../../config/kafka';
import { queryHelper } from '../../helpers/queryHelper';
import { kafkaDLQService } from '../../services/kafka-dlq.service';

// Mock do Kafka
vi.mock('../../config/kafka', () => ({
  kafka: {
    consumer: vi.fn().mockReturnValue({
      connect: vi.fn(),
      disconnect: vi.fn(),
      subscribe: vi.fn(),
      run: vi.fn(),
      stop: vi.fn(),
      commitOffsets: vi.fn(),
    }),
    producer: vi.fn().mockReturnValue({
      connect: vi.fn(),
      disconnect: vi.fn(),
      send: vi.fn(),
      isConnected: vi.fn().mockReturnValue(true),
    }),
  },
  producer: {
    connect: vi.fn(),
    disconnect: vi.fn(),
    send: vi.fn(),
    isConnected: vi.fn().mockReturnValue(true),
  },
}));

// Mock do queryHelper
vi.mock('../../helpers/queryHelper', () => ({
  queryHelper: {
    query: vi.fn(),
  },
}));

// Mock do logger
vi.mock('../../utils/logger', () => ({
  logger: {
    debug: vi.fn(),
    info: vi.fn(),
    warn: vi.fn(),
    error: vi.fn(),
  },
}));

// Mock do serviço de logging
vi.mock('../../services/kafka-logging.service', () => ({
  kafkaLoggingService: {
    info: vi.fn(),
    warn: vi.fn(),
    error: vi.fn(),
    debug: vi.fn(),
  },
}));

describe('KafkaDLQService', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('initConsumer', () => {
    it('deve inicializar o consumidor da DLQ', async () => {
      await kafkaDLQService.initConsumer();

      expect(kafka.consumer).toHaveBeenCalledWith({
        groupId: 'dlq-consumer-group',
        sessionTimeout: 30000,
      });

      const mockConsumer = kafka.consumer();
      expect(mockConsumer.connect).toHaveBeenCalled();
      expect(mockConsumer.subscribe).toHaveBeenCalledWith({
        topic: expect.any(String),
        fromBeginning: false,
      });
    });

    it('não deve inicializar o consumidor se já estiver inicializado', async () => {
      // Inicializar uma vez
      await kafkaDLQService.initConsumer();

      // Limpar mocks
      vi.clearAllMocks();

      // Inicializar novamente
      await kafkaDLQService.initConsumer();

      // Verificar que não foi chamado novamente
      expect(kafka.consumer).not.toHaveBeenCalled();
    });
  });

  describe('startProcessing', () => {
    it('deve iniciar o processamento da DLQ', async () => {
      // Inicializar consumidor
      await kafkaDLQService.initConsumer();

      // Limpar mocks
      vi.clearAllMocks();

      // Iniciar processamento
      await kafkaDLQService.startProcessing();

      const mockConsumer = kafka.consumer();
      expect(mockConsumer.run).toHaveBeenCalledWith({
        eachMessage: expect.any(Function),
      });
    });

    it('não deve iniciar o processamento se já estiver em execução', async () => {
      // Inicializar consumidor
      await kafkaDLQService.initConsumer();

      // Iniciar processamento
      await kafkaDLQService.startProcessing();

      // Limpar mocks
      vi.clearAllMocks();

      // Iniciar processamento novamente
      await kafkaDLQService.startProcessing();

      const mockConsumer = kafka.consumer();
      expect(mockConsumer.run).not.toHaveBeenCalled();
    });
  });

  describe('queryMessages', () => {
    it('deve consultar mensagens da DLQ com filtros', async () => {
      // Mock do resultado da consulta
      (queryHelper.query as Mock).mockResolvedValueOnce({
        rows: [
          {
            message_id: 'test-id-1',
            original_topic: 'test-topic',
            error_type: 'connection',
            error_message: 'Connection error',
            attempt_count: 3,
            processed: false,
            created_at: new Date(),
          },
          {
            message_id: 'test-id-2',
            original_topic: 'test-topic',
            error_type: 'serialization',
            error_message: 'Serialization error',
            attempt_count: 1,
            processed: true,
            created_at: new Date(),
          },
        ],
      });

      // Consultar mensagens
      const filter = {
        originalTopic: 'test-topic',
        processed: false,
        limit: 10,
      };

      const messages = await kafkaDLQService.queryMessages(filter);

      // Verificar resultado
      expect(messages).toHaveLength(2);
      expect(messages[0].messageId).toBe('test-id-1');
      expect(messages[1].messageId).toBe('test-id-2');

      // Verificar que query foi chamada com os filtros corretos
      expect(queryHelper.query).toHaveBeenCalledWith(
        expect.stringContaining('WHERE 1=1'),
        expect.arrayContaining(['test-topic', false, 10])
      );
    });
  });

  describe('reprocessMessages', () => {
    it('deve reprocessar mensagens da DLQ', async () => {
      // Mock do resultado da consulta
      (queryHelper.query as Mock).mockResolvedValueOnce({
        rows: [
          {
            message_id: 'test-id-1',
            original_topic: 'test-topic-1',
            original_key: 'test-key-1',
            original_value: 'test-value-1',
            original_headers: { 'test-header': 'value' },
          },
          {
            message_id: 'test-id-2',
            original_topic: 'test-topic-2',
            original_key: 'test-key-2',
            original_value: 'test-value-2',
            original_headers: null,
          },
        ],
      });

      // Mock das atualizações
      (queryHelper.query as Mock).mockResolvedValue({ rowCount: 1 });

      // Mock do envio de mensagens
      const mockProducer = {
        send: vi
          .fn()
          .mockResolvedValueOnce(undefined)
          .mockRejectedValueOnce(new Error('Send error')),
        isConnected: vi.fn().mockReturnValue(true),
      };

      // Reprocessar mensagens
      const result = await kafkaDLQService.reprocessMessages(
        ['test-id-1', 'test-id-2'],
        mockProducer as any
      );

      // Verificar resultado
      expect(result).toEqual({
        reprocessedCount: 2,
        successCount: 1,
        failureCount: 1,
        results: [
          {
            messageId: 'test-id-1',
            originalTopic: 'test-topic-1',
            success: true,
          },
          {
            messageId: 'test-id-2',
            originalTopic: 'test-topic-2',
            success: false,
            error: 'Send error',
          },
        ],
      });

      // Verificar que send foi chamado para cada mensagem
      expect(mockProducer.send).toHaveBeenCalledTimes(2);

      // Verificar que a tabela foi atualizada
      expect(queryHelper.query).toHaveBeenCalledWith(
        expect.stringContaining('UPDATE tab_dead_letter_queue'),
        ['Reprocessado com sucesso', 'test-id-1']
      );

      expect(queryHelper.query).toHaveBeenCalledWith(
        expect.stringContaining('UPDATE tab_dead_letter_queue'),
        [expect.stringContaining('Falha ao reprocessar'), 'test-id-2']
      );
    });

    it('deve retornar resultado vazio se não houver mensagens para reprocessar', async () => {
      const result = await kafkaDLQService.reprocessMessages([]);

      expect(result).toEqual({
        reprocessedCount: 0,
        successCount: 0,
        failureCount: 0,
        results: [],
      });

      expect(queryHelper.query).not.toHaveBeenCalled();
    });
  });

  describe('stopProcessing e shutdown', () => {
    it('deve parar o processamento da DLQ', async () => {
      // Inicializar e iniciar
      await kafkaDLQService.initConsumer();
      await kafkaDLQService.startProcessing();

      // Limpar mocks
      vi.clearAllMocks();

      // Parar processamento
      await kafkaDLQService.stopProcessing();

      const mockConsumer = kafka.consumer();
      expect(mockConsumer.stop).toHaveBeenCalled();
    });

    it('deve finalizar o consumidor da DLQ', async () => {
      // Inicializar e iniciar
      await kafkaDLQService.initConsumer();
      await kafkaDLQService.startProcessing();

      // Limpar mocks
      vi.clearAllMocks();

      // Finalizar
      await kafkaDLQService.shutdown();

      const mockConsumer = kafka.consumer();
      expect(mockConsumer.stop).toHaveBeenCalled();
      expect(mockConsumer.disconnect).toHaveBeenCalled();
    });
  });
});
