/**
 * Add Document Version Use Case
 *
 * Caso de uso para adicionar uma nova versão a um documento existente.
 * Parte da implementação da tarefa 8.3.1 - Armazenamento de PDFs
 */

import { calculateChecksum } from '../../../utils/fileUtils';
import { generateId } from '../../../utils/idGenerator';
import { DocumentMetadata, DocumentVersion } from '../../entities/Document';
import { DocumentRepository } from '../../repositories/DocumentRepository';

export interface AddDocumentVersionRequest {
  documentId: string;
  content: Uint8Array;
  contentType: string;
  userId: string;
  changeNotes?: string;
  title?: string;
  description?: string;
  author?: string;
  keywords?: string[];
  language?: string;
  category?: string;
  tags?: string[];
  pageCount?: number;
}

export interface AddDocumentVersionResponse {
  success: boolean;
  version?: number;
  error?: string;
}

export class AddDocumentVersionUseCase {
  constructor(private documentRepository: DocumentRepository) {}

  async execute(request: AddDocumentVersionRequest): Promise<AddDocumentVersionResponse> {
    try {
      // Buscar o documento existente
      const document = await this.documentRepository.getById(request.documentId, true);

      if (!document) {
        return {
          success: false,
          error: 'Documento não encontrado.',
        };
      }

      // Verificar se o usuário tem permissão para adicionar versão
      if (document.ownerId !== request.userId) {
        return {
          success: false,
          error: 'Você não tem permissão para modificar este documento.',
        };
      }

      // Validar o tipo de conteúdo
      if (!this.isValidContentType(request.contentType)) {
        return {
          success: false,
          error: 'Tipo de arquivo inválido. Apenas PDFs são permitidos.',
        };
      }

      // Validar tamanho do arquivo (máximo 10MB)
      const maxSize = 10 * 1024 * 1024; // 10MB em bytes
      if (request.content.length > maxSize) {
        return {
          success: false,
          error: 'Tamanho do arquivo excede o limite máximo de 10MB.',
        };
      }

      // Calcular checksum para verificação de integridade
      const checksum = await calculateChecksum(request.content);

      // Verificar se o conteúdo é idêntico à versão atual
      const currentVersion = document.getCurrentVersion();
      if (currentVersion && currentVersion.metadata.checksum === checksum) {
        return {
          success: false,
          error: 'O conteúdo é idêntico à versão atual do documento.',
        };
      }

      // Criar nova versão
      const newVersionNumber = document.currentVersion + 1;
      const now = new Date();

      // Criar metadados atualizados
      const metadata: DocumentMetadata = {
        ...document.metadata,
        title: request.title || document.metadata.title,
        description: request.description || document.metadata.description,
        author: request.author || document.metadata.author,
        keywords: request.keywords || document.metadata.keywords,
        updatedAt: now,
        fileSize: request.content.length,
        pageCount: request.pageCount || document.metadata.pageCount,
        language: request.language || document.metadata.language,
        category: request.category || document.metadata.category,
        tags: request.tags || document.metadata.tags,
        version: newVersionNumber,
        contentType: request.contentType,
        checksum,
      };

      // Criar objeto de versão
      const version: DocumentVersion = {
        id: generateId(),
        documentId: document.id,
        version: newVersionNumber,
        content: request.content,
        metadata,
        createdAt: now,
        createdBy: request.userId,
        changeNotes: request.changeNotes,
      };

      // Adicionar versão ao documento
      await this.documentRepository.addVersion(document.id, version);

      return {
        success: true,
        version: newVersionNumber,
      };
    } catch (error) {
      console.error('Erro ao adicionar nova versão ao documento:', error);
      return {
        success: false,
        error: 'Ocorreu um erro ao processar a nova versão do documento.',
      };
    }
  }

  private isValidContentType(contentType: string): boolean {
    const allowedTypes = ['application/pdf', 'application/x-pdf'];
    return allowedTypes.includes(contentType);
  }
}
