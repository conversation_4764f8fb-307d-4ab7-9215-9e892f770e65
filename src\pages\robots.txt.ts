/**
 * Robots.txt Endpoint
 *
 * Endpoint para gerar o arquivo robots.txt do site.
 * Parte da implementação da tarefa 8.9.1 - Otimização para buscadores
 */

import type { APIRoute } from 'astro';

export const GET: APIRoute = async () => {
  // Obter URL base do site
  const baseUrl = import.meta.env.BASE_URL || 'https://example.com';

  // Gerar conteúdo do robots.txt
  const robotsTxt = `# robots.txt
User-agent: *
Allow: /

# Disallow admin pages
Disallow: /admin/
Disallow: /api/
Disallow: /login
Disallow: /logout
Disallow: /register
Disallow: /reset-password
Disallow: /checkout
Disallow: /carrinho

# Sitemaps
Sitemap: ${baseUrl}/sitemap.xml
Sitemap: ${baseUrl}/sitemap-index.xml
`;

  return new Response(robotsTxt, {
    status: 200,
    headers: {
      'Content-Type': 'text/plain',
      'Cache-Control': 'public, max-age=3600',
    },
  });
};
