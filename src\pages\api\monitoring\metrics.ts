/**
 * API de Métricas de Monitoramento
 *
 * Este endpoint fornece acesso às métricas específicas
 * coletadas pelo sistema de monitoramento.
 */

import { isAdmin } from '@helpers/authGuard';
import { MetricType, applicationMonitoringService } from '@services/applicationMonitoringService';
import type { APIRoute } from 'astro';

/**
 * Endpoint GET para obter métricas por tipo
 */
export const GET: APIRoute = async ({ request, cookies }) => {
  try {
    // Verificar se o usuário é administrador
    const adminResult = await isAdmin({ request, cookies } as any);

    if (!adminResult) {
      return new Response(JSON.stringify({ error: 'Não autorizado' }), {
        status: 403,
        headers: {
          'Content-Type': 'application/json',
        },
      });
    }

    // Obter parâmetros da URL
    const url = new URL(request.url);
    const type = url.searchParams.get('type');
    const limit = url.searchParams.get('limit');

    // Validar tipo de métrica
    if (!type || !Object.values(MetricType).includes(type as MetricType)) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Tipo de métrica inválido',
        }),
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    // Obter métricas
    const metrics = applicationMonitoringService.getMetrics(
      type as MetricType,
      limit ? Number.parseInt(limit, 10) : undefined
    );

    return new Response(
      JSON.stringify({
        success: true,
        data: metrics,
      }),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  } catch (error) {
    return new Response(
      JSON.stringify({
        success: false,
        error: error.message,
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  }
};
