/**
 * Implementação do Serviço de Cache com Valkey
 *
 * Esta classe implementa a interface CacheService utilizando o cliente Valkey,
 * seguindo os princípios da Clean Architecture.
 */

import { type RedisClientType, createClient } from '@valkey/client';
import { CacheService } from '../../application/interfaces/CacheService';

/**
 * Implementação do Serviço de Cache com Valkey
 */
export class ValkeyCacheService implements CacheService {
  private client: RedisClientType;
  private isConnected = false;

  /**
   * Cria uma nova instância do serviço de cache
   *
   * @param url URL de conexão com o Valkey
   */
  constructor(private readonly url: string) {
    this.client = createClient({
      url: this.url,
    });

    this.client.on('error', (err) => {
      console.error('Erro no cliente Valkey:', err);
    });
  }

  /**
   * Conecta ao servidor Valkey
   */
  private async connect(): Promise<void> {
    if (!this.isConnected) {
      await this.client.connect();
      this.isConnected = true;
    }
  }

  /**
   * Obtém um valor do cache
   *
   * @param key Chave do cache
   * @returns Valor armazenado ou null se não existir
   */
  async get<T>(key: string): Promise<T | null> {
    try {
      await this.connect();

      const value = await this.client.get(key);

      if (!value) {
        return null;
      }

      return JSON.parse(value) as T;
    } catch (error) {
      console.error(`Erro ao obter valor do cache para chave ${key}:`, error);
      return null;
    }
  }

  /**
   * Define um valor no cache
   *
   * @param key Chave do cache
   * @param value Valor a ser armazenado
   * @param ttl Tempo de vida em segundos (opcional)
   */
  async set<T>(key: string, value: T, ttl?: number): Promise<void> {
    try {
      await this.connect();

      const stringValue = JSON.stringify(value);

      if (ttl) {
        await this.client.set(key, stringValue, { EX: ttl });
      } else {
        await this.client.set(key, stringValue);
      }
    } catch (error) {
      console.error(`Erro ao definir valor no cache para chave ${key}:`, error);
    }
  }

  /**
   * Remove um valor do cache
   *
   * @param key Chave do cache
   */
  async delete(key: string): Promise<void> {
    try {
      await this.connect();
      await this.client.del(key);
    } catch (error) {
      console.error(`Erro ao remover valor do cache para chave ${key}:`, error);
    }
  }

  /**
   * Remove valores do cache por padrão
   *
   * @param pattern Padrão de chaves a serem removidas
   */
  async deletePattern(pattern: string): Promise<void> {
    try {
      await this.connect();

      // Obter chaves que correspondem ao padrão
      const keys = await this.client.keys(pattern);

      if (keys.length > 0) {
        // Remover todas as chaves encontradas
        await this.client.del(keys);
      }
    } catch (error) {
      console.error(`Erro ao remover valores do cache para padrão ${pattern}:`, error);
    }
  }

  /**
   * Verifica se uma chave existe no cache
   *
   * @param key Chave do cache
   * @returns true se a chave existir, false caso contrário
   */
  async exists(key: string): Promise<boolean> {
    try {
      await this.connect();
      const result = await this.client.exists(key);
      return result === 1;
    } catch (error) {
      console.error(`Erro ao verificar existência de chave ${key} no cache:`, error);
      return false;
    }
  }

  /**
   * Define um tempo de vida para uma chave existente
   *
   * @param key Chave do cache
   * @param ttl Tempo de vida em segundos
   */
  async expire(key: string, ttl: number): Promise<void> {
    try {
      await this.connect();
      await this.client.expire(key, ttl);
    } catch (error) {
      console.error(`Erro ao definir tempo de vida para chave ${key} no cache:`, error);
    }
  }

  /**
   * Limpa todo o cache
   */
  async clear(): Promise<void> {
    try {
      await this.connect();
      await this.client.flushAll();
    } catch (error) {
      console.error('Erro ao limpar cache:', error);
    }
  }
}
