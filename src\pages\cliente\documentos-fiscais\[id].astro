---
import { PageTransition } from '../../../components/transitions';
import DaisyButton from '../../../components/ui/DaisyButton.astro';
import DaisyCard from '../../../components/ui/DaisyCard.astro';
import Breadcrumbs from '../../../components/ui/navigation/Breadcrumbs.astro';
import { FiscalDocumentStatus, FiscalDocumentType } from '../../../domain/entities/FiscalDocument';
import { FiscalDocumentRepository } from '../../../domain/repositories/FiscalDocumentRepository';
import { PostgresFiscalDocumentRepository } from '../../../infrastructure/database/repositories/PostgresFiscalDocumentRepository';
/**
 * Página de Detalhes de Documento Fiscal do Cliente
 *
 * Interface para o cliente visualizar detalhes de um documento fiscal.
 * Parte da implementação da tarefa 8.7.3 - Portal do cliente para documentos fiscais
 */
import BaseLayout from '../../../layouts/BaseLayout.astro';
import { Container, Section } from '../../../layouts/grid';

// Obter ID do documento da URL
const { id } = Astro.params;

// Em um cenário real, obteríamos o ID do cliente da sessão
// Para este exemplo, vamos usar um ID fixo
const customerId = '12345678901'; // CPF do cliente

// Inicializar repositório
const fiscalDocumentRepository: FiscalDocumentRepository = new PostgresFiscalDocumentRepository();

// Obter documento fiscal
const document = id ? await fiscalDocumentRepository.getById(id) : null;

// Verificar se o documento existe e pertence ao cliente
if (!document || document.customer.documentNumber !== customerId) {
  return Astro.redirect('/cliente/documentos-fiscais');
}

// Título da página
const title = `Documento Fiscal: ${document.type} ${document.number || document.id}`;

// Breadcrumbs para a página atual
const breadcrumbItems = [
  { href: '/', label: 'Início' },
  { href: '/cliente', label: 'Área do Cliente' },
  { href: '/cliente/documentos-fiscais', label: 'Documentos Fiscais' },
  { label: document.number || document.id },
];

// Função para formatar data
const formatDate = (dateString: string | null | undefined): string => {
  if (!dateString) return '-';
  const date = new Date(dateString);
  return date.toLocaleDateString('pt-BR', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
  });
};

// Função para formatar valor monetário
const formatCurrency = (value: number): string => {
  return value.toLocaleString('pt-BR', {
    style: 'currency',
    currency: 'BRL',
  });
};

// Função para formatar documento do cliente
const formatDocument = (type: 'CPF' | 'CNPJ', number: string): string => {
  if (type === 'CPF') {
    return number.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, '$1.$2.$3-$4');
  }
  return number.replace(/(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})/, '$1.$2.$3/$4-$5');
};

// Função para obter label do tipo de documento
const getTypeLabel = (type: FiscalDocumentType): string => {
  const typeLabels: Record<FiscalDocumentType, string> = {
    NFE: 'Nota Fiscal Eletrônica',
    NFSE: 'Nota Fiscal de Serviços Eletrônica',
    NFCE: 'Nota Fiscal de Consumidor Eletrônica',
  };

  return typeLabels[type] || type;
};

// Função para obter label do status
const getStatusLabel = (status: FiscalDocumentStatus): string => {
  const statusLabels: Record<FiscalDocumentStatus, string> = {
    DRAFT: 'Rascunho',
    PENDING: 'Pendente',
    PROCESSING: 'Processando',
    ISSUED: 'Emitido',
    CANCELLED: 'Cancelado',
    ERROR: 'Erro',
  };

  return statusLabels[status] || status;
};

// Função para obter cor do status
const getStatusColor = (status: FiscalDocumentStatus): string => {
  const statusColors: Record<FiscalDocumentStatus, string> = {
    DRAFT: 'info',
    PENDING: 'warning',
    PROCESSING: 'warning',
    ISSUED: 'success',
    CANCELLED: 'error',
    ERROR: 'error',
  };

  return statusColors[status] || 'neutral';
};
---

<BaseLayout title={title} enableViewTransitions={true} transitionType="fade">
  <Container>
    <PageTransition type="fade" duration="0.5s">
      <Section>
        <Breadcrumbs items={breadcrumbItems} class="mb-6" />
        
        <div class="flex justify-between items-center mb-6">
          <h1 class="text-3xl font-bold">{title}</h1>
          
          <div class="flex gap-2">
            <DaisyButton 
              href="/cliente/documentos-fiscais" 
              variant="outline" 
              icon="arrow-left"
              text="Voltar"
            />
          </div>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
          <DaisyCard class="md:col-span-2">
            <div class="p-6">
              <h2 class="text-xl font-bold mb-4">
                <i class="icon icon-file-text mr-2"></i>
                Informações do Documento
              </h2>
              
              <div class="overflow-x-auto">
                <table class="table w-full">
                  <tbody>
                    <tr>
                      <th>Tipo</th>
                      <td>{getTypeLabel(document.type)}</td>
                    </tr>
                    <tr>
                      <th>Número</th>
                      <td>{document.number || '-'}</td>
                    </tr>
                    <tr>
                      <th>Série</th>
                      <td>{document.series || '-'}</td>
                    </tr>
                    <tr>
                      <th>Status</th>
                      <td>
                        <div class={`badge badge-${getStatusColor(document.status)}`}>
                          {getStatusLabel(document.status)}
                        </div>
                      </td>
                    </tr>
                    <tr>
                      <th>Data de Emissão</th>
                      <td>{formatDate(document.issueDate?.toString())}</td>
                    </tr>
                    <tr>
                      <th>Valor Total</th>
                      <td>{formatCurrency(document.totalValue)}</td>
                    </tr>
                    <tr>
                      <th>Valor de Impostos</th>
                      <td>{formatCurrency(document.taxValue)}</td>
                    </tr>
                    <tr>
                      <th>Desconto</th>
                      <td>{formatCurrency(document.discountValue)}</td>
                    </tr>
                    <tr>
                      <th>Frete</th>
                      <td>{formatCurrency(document.shippingValue)}</td>
                    </tr>
                    <tr>
                      <th>Valor Final</th>
                      <td class="font-bold">{formatCurrency(document.finalValue)}</td>
                    </tr>
                    {document.notes && (
                      <tr>
                        <th>Observações</th>
                        <td>{document.notes}</td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
              
              <div class="flex justify-end mt-6 gap-2">
                <a 
                  href={`/api/fiscal/customer/download/${document.id}?customerId=${customerId}&format=pdf`} 
                  target="_blank" 
                  class="btn btn-primary"
                >
                  <i class="icon icon-file mr-2"></i>
                  Download PDF
                </a>
                
                <a 
                  href={`/api/fiscal/customer/download/${document.id}?customerId=${customerId}&format=xml`} 
                  class="btn btn-outline"
                >
                  <i class="icon icon-code mr-2"></i>
                  Download XML
                </a>
              </div>
            </div>
          </DaisyCard>
          
          <DaisyCard>
            <div class="p-6">
              <h2 class="text-xl font-bold mb-4">
                <i class="icon icon-user mr-2"></i>
                Dados do Cliente
              </h2>
              
              <div class="overflow-x-auto">
                <table class="table w-full">
                  <tbody>
                    <tr>
                      <th>Nome</th>
                      <td>{document.customer.name}</td>
                    </tr>
                    <tr>
                      <th>Documento</th>
                      <td>{formatDocument(document.customer.documentType, document.customer.documentNumber)}</td>
                    </tr>
                    {document.customer.email && (
                      <tr>
                        <th>Email</th>
                        <td>{document.customer.email}</td>
                      </tr>
                    )}
                    {document.customer.phone && (
                      <tr>
                        <th>Telefone</th>
                        <td>{document.customer.phone}</td>
                      </tr>
                    )}
                    <tr>
                      <th>Endereço</th>
                      <td>
                        {document.customer.address.street}, {document.customer.address.number}
                        {document.customer.address.complement && `, ${document.customer.address.complement}`}<br>
                        {document.customer.address.district} - {document.customer.address.city}/{document.customer.address.state}<br>
                        CEP: {document.customer.address.zipCode}
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </DaisyCard>
        </div>
        
        <DaisyCard>
          <div class="p-6">
            <h2 class="text-xl font-bold mb-4">
              <i class="icon icon-shopping-cart mr-2"></i>
              Itens do Documento
            </h2>
            
            <div class="overflow-x-auto">
              <table class="table w-full">
                <thead>
                  <tr>
                    <th>Código</th>
                    <th>Descrição</th>
                    <th>Quantidade</th>
                    <th>Valor Unitário</th>
                    <th>Valor Total</th>
                    <th>Imposto</th>
                  </tr>
                </thead>
                <tbody>
                  {document.items.map(item => (
                    <tr>
                      <td>{item.productCode}</td>
                      <td>{item.description}</td>
                      <td>{item.quantity}</td>
                      <td>{formatCurrency(item.unitValue)}</td>
                      <td>{formatCurrency(item.totalValue)}</td>
                      <td>{formatCurrency(item.taxValue)}</td>
                    </tr>
                  ))}
                </tbody>
                <tfoot>
                  <tr>
                    <th colspan="4" class="text-right">Total:</th>
                    <th>{formatCurrency(document.totalValue)}</th>
                    <th>{formatCurrency(document.taxValue)}</th>
                  </tr>
                </tfoot>
              </table>
            </div>
          </div>
        </DaisyCard>
      </Section>
    </PageTransition>
  </Container>
</BaseLayout>
