/**
 * Serviço de notificação por e-mail
 *
 * Este serviço implementa apenas a interface EmailNotificationChannel,
 * seguindo o Interface Segregation Principle.
 */

import { nanoid } from 'nanoid';
import nodemailer from 'nodemailer';
import { config } from '../../config';
import {
  EmailNotificationChannel,
  NotificationMessage,
} from '../../domain/interfaces/NotificationChannels';
import { logger } from '../../utils/logger';

/**
 * Serviço de notificação por e-mail
 */
export class EmailNotificationService implements EmailNotificationChannel {
  /**
   * Transportador de e-mail
   */
  private transporter: nodemailer.Transporter;

  /**
   * Cria uma nova instância do serviço de notificação por e-mail
   */
  constructor() {
    // Configurar transportador de e-mail
    this.transporter = nodemailer.createTransport({
      host: config.email.host,
      port: config.email.port,
      secure: config.email.secure,
      auth: {
        user: config.email.user,
        pass: config.email.password,
      },
    });
  }

  /**
   * Envia uma notificação por e-mail
   * @param message - Mensagem de notificação
   * @param emailAddress - Endereço de e-mail do destinatário
   * @param options - Opções adicionais para o e-mail
   * @returns ID da notificação enviada
   */
  async sendEmail(
    message: NotificationMessage,
    emailAddress: string,
    options?: {
      from?: string;
      subject?: string;
      template?: string;
      attachments?: Array<{
        filename: string;
        content: Buffer | string;
        contentType?: string;
      }>;
      cc?: string[];
      bcc?: string[];
    }
  ): Promise<string> {
    try {
      // Gerar ID para a notificação
      const notificationId = uuidv4();

      // Preparar dados do e-mail
      const emailData = {
        from: options?.from || config.email.defaultFrom,
        to: emailAddress,
        subject: options?.subject || message.title,
        text: message.body,
        html: await this.renderTemplate(options?.template, {
          title: message.title,
          body: message.body,
          userId: message.userId,
          metadata: message.metadata,
        }),
        attachments: options?.attachments,
        cc: options?.cc,
        bcc: options?.bcc,
      };

      // Enviar e-mail
      const info = await this.transporter.sendMail(emailData);

      // Registrar envio
      logger.info('E-mail notification sent', {
        notificationId,
        messageId: info.messageId,
        userId: message.userId,
        emailAddress,
      });

      // Salvar registro no banco de dados
      await this.saveNotificationRecord(notificationId, message, emailAddress, info.messageId);

      return notificationId;
    } catch (error) {
      // Registrar erro
      logger.error('Failed to send email notification', {
        error: error instanceof Error ? error.message : String(error),
        userId: message.userId,
        emailAddress,
      });

      // Propagar erro
      throw error;
    }
  }

  /**
   * Renderiza um template de e-mail
   * @param templatePath - Caminho para o template
   * @param data - Dados para o template
   * @returns HTML renderizado
   */
  private async renderTemplate(
    templatePath?: string,
    data?: Record<string, unknown>
  ): Promise<string> {
    // Se não houver template, retornar corpo da mensagem como HTML simples
    if (!templatePath || !data) {
      return `<html>
        <body>
          <h1>${data?.title || ''}</h1>
          <p>${data?.body || ''}</p>
        </body>
      </html>`;
    }

    // Implementação real usaria um sistema de templates como Handlebars ou EJS
    // Esta é uma implementação simplificada para exemplo
    try {
      // Aqui seria carregado e renderizado o template
      return `<html>
        <body>
          <h1>${data.title}</h1>
          <div>${data.body}</div>
        </body>
      </html>`;
    } catch (error) {
      logger.error('Failed to render email template', {
        error: error instanceof Error ? error.message : String(error),
        templatePath,
      });

      // Fallback para template simples
      return `<html>
        <body>
          <h1>${data.title}</h1>
          <p>${data.body}</p>
        </body>
      </html>`;
    }
  }

  /**
   * Salva registro da notificação no banco de dados
   * @param notificationId - ID da notificação
   * @param message - Mensagem de notificação
   * @param emailAddress - Endereço de e-mail do destinatário
   * @param messageId - ID da mensagem no servidor de e-mail
   */
  private async saveNotificationRecord(
    notificationId: string,
    message: NotificationMessage,
    emailAddress: string,
    messageId: string
  ): Promise<void> {
    try {
      // Aqui seria implementada a lógica para salvar no banco de dados
      // Exemplo simplificado para demonstração
      logger.debug('Saving email notification record', {
        notificationId,
        messageId,
        userId: message.userId,
        emailAddress,
      });
    } catch (error) {
      logger.error('Failed to save notification record', {
        error: error instanceof Error ? error.message : String(error),
        notificationId,
        userId: message.userId,
      });
    }
  }
}
