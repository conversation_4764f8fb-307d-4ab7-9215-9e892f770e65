---
import { actions } from 'astro:actions';
import SignContainer from '@components/form/SignContainer.astro';
import MainHeaderLogin from '@components/layout/MainHeaderSign.astro';
import type { SchoolTypeData } from 'src/database/interfacesHelper';
import BaseLayout from './BaseLayout.astro';

const schoolType = await Astro.callAction(actions.schoolTypeAction.read, {});

const result = Astro.getActionResult(actions.authAction.signup);
---
<BaseLayout title="Cadastrar">
  <MainHeaderLogin/>
  {result?.error && <div class="text-error text-sm mt-2" id="general-error" hidden>{result.error}</div>}
  <SignContainer title="Crie sua conta!">
    <form id="signup-form" method="POST" action={actions.authAction.signup}>
    <div class="c-email">
      <label class="input input-bordered input-primary input-sm flex items-center gap-2 c-shadowed">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 16 16"
          fill="currentColor"
          class="h-4 w-4 opacity-70">
          <path
          d="M2.5 3A1.5 1.5 0 0 0 1 4.5v.793c.026.009.051.02.076.032L7.674 8.51c.206.1.446.1.652 0l6.598-3.185A.755.755 0 0 1 15 5.293V4.5A1.5 1.5 0 0 0 13.5 3h-11Z" />
          <path
            d="M15 6.954 8.978 9.86a2.25 2.25 0 0 1-1.956 0L1 6.954V11.5A1.5 1.5 0 0 0 2.5 13h11a1.5 1.5 0 0 0 1.5-1.5V6.954Z" />
        </svg>
          <input type="text" id="email" name="email" class="grow" placeholder="Email" />
      </label>
      <span class="text-primary" id="email-error" hidden>* Email inválido</span>
    </div>
    <div class="c-password">
      <label class="input input-bordered input-primary input-sm flex items-center gap-2 c-shadowed">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 16 16"
          fill="currentColor"
          class="h-4 w-4 opacity-70">
          <path
            fill-rule="evenodd"
              d="M14 6a4 4 0 0 1-4.899 3.899l-1.955 1.955a.5.5 0 0 1-.353. 146H5v1.5a.5.5 0 0 1-.5.5h-2a.5.5 0 0 1-.5-.5v-2.293a.5.5 0 0 1 .146-.353l3.955-3.955A4 4 0 1 1 14 6Zm-4-2a.75.75 0 0 0 0 ******* 0 0 1 .********* 0 0 0 1.5 0 2 2 0 0 0-2-2Z"
            clip-rule="evenodd" />
          </svg>
            <input type="password" id="password" name="password" class="grow" placeholder="Senha"/>
        </label>
    </div>
    <div class="c-password">
      <label class="input input-bordered input-primary input-sm flex items-center gap-2 c-shadowed">
        <svg
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 16 16"
        fill="currentColor"
        class="h-4 w-4 opacity-70">
        <path
            fill-rule="evenodd"
            d="M14 6a4 4 0 0 1-4.899 3.899l-1.955 1.955a.5.5 0 0 1-.353.146H5v1.5a.5.5 0 0 1-.5.5h-2a.5.5 0 0 1-.5-.5v-2.293a.5.5 0 0 1 .146-.353l3.955-3.955A4 4 0 1 1 14 6Zm-4-2a.75.75 0 0 0 0 ******* 0 0 1 .********* 0 0 0 1.5 0 2 2 0 0 0-2-2Z"
            clip-rule="evenodd" />
        </svg>
        <input type="password" id="password-confirmation" class="grow" placeholder="Confirme sua senha" />
      </label>
      <span class="text-primary" id="password-error" hidden>* Não conferem</span>
    </div>
    <div class="form-control l-radio-group-adds c-shadowed">
      <label class="label cursor-pointer">
          <span class="label-text l-label-text-adds">Não sou Professor(a)</span>
          <input type="radio" name="teacherType" value="none" class="radio radio-xs bg-white checked:bg-blue-500" checked="checked" />
      </label>
        {schoolType.data?.result?.map((schoolType: SchoolTypeData ) => (
      <label class="label cursor-pointer">
            <span class="label-text l-label-text-adds">{schoolType.type}</span>
            <input type="radio" name="schoolType" value={schoolType.ulid_school_type} class="radio radio-xs bg-white checked:bg-blue-500" />
      </label>
        ))}
    </div>
      <select class="select select-primary select-sm w-full c-shadowed" id="select-state" name="state">
      <option disabled selected>Selecione UF</option>
      <option value="AC">Acre</option>
      <option value="AL">Alagoas</option>
      <option value="AP">Amapá</option>
      <option value="AM">Amazonas</option>
      <option value="BA">Bahia</option>
      <option value="CE">Ceará</option>
      <option value="DF">Distrito Federal</option>
      <option value="ES">Espírito Santo</option>
      <option value="GO">Goiás</option>
      <option value="MA">Maranhão</option>
      <option value="MT">Mato Grosso</option>
      <option value="MS">Mato Grosso do Sul</option>
      <option value="MG">Minas Gerais</option>
      <option value="PA">Pará</option>
      <option value="PB">Paraíba</option>
      <option value="PR">Paraná</option>
      <option value="PE">Pernambuco</option>
      <option value="PI">Piauí</option>
      <option value="RJ">Rio de Janeiro</option>
      <option value="RN">Rio Grande do Norte</option>
      <option value="RS">Rio Grande do Sul</option>
      <option value="RO">Rondônia</option>
      <option value="RR">Roraima</option>
      <option value="SC">Santa Catarina</option>
      <option value="SP">São Paulo</option>
      <option value="SE">Sergipe</option>
      <option value="TO">Tocantins</option>
    </select>
      <select class="select select-primary select-sm w-full c-shadowed" id="select-county" name="county">
      <option disabled selected>Selecione Município</option>
    </select>
      <div class="text-error text-sm mt-2" id="general-error" hidden>
        <span id="general-error-message">{result?.error}</span>
      </div>
    <div class="c-btn-confirm card-actions justify-end">
        <button type="submit" id="signup-button" class="btn btn-primary btn-block btn-sm c-shadowed">Confirmar</button>
    </div>
    </form>
  </SignContainer>
</BaseLayout>

<script>
  let invalidEmail = false;
  let notMatch     = false;
  let isSubmitting = false;

  const emailElement                = document.getElementById('email')                 as HTMLInputElement;
  const emailErrorElement           = document.getElementById('email-error')           as HTMLSpanElement;
  const passwordElement             = document.getElementById('password')              as HTMLInputElement;
  const passwordConfirmationElement = document.getElementById('password-confirmation') as HTMLInputElement;
  const passwordErrorElement        = document.getElementById('password-error')        as HTMLSpanElement;
  const selectStateElement          = document.getElementById('select-state')          as HTMLSelectElement;
  const selectCountyElement         = document.getElementById('select-county')         as HTMLSelectElement;
  const teacherTypeElements         = document.querySelectorAll('input[name="teacherType"]') as NodeListOf<HTMLInputElement>;
  const formElement                 = document.getElementById('signup-form')           as HTMLFormElement;
  const signupButton                = document.getElementById('signup-button')         as HTMLButtonElement;
  const generalErrorElement         = document.getElementById('general-error')         as HTMLDivElement;
  
  emailElement.addEventListener('change', () => {
    const email = emailElement.value;
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

    invalidEmail = !emailRegex.test(email) && email.length > 0;

    if (invalidEmail) {
      emailErrorElement.removeAttribute('hidden');
      emailElement.focus();
    } else {
      emailErrorElement.setAttribute('hidden', 'true');      
    }
  })

  passwordConfirmationElement.addEventListener('change', () => {
    notMatch = passwordElement.value != passwordConfirmationElement.value;

    if (notMatch) {
      passwordErrorElement.removeAttribute('hidden');
      passwordElement.focus();
    } else {
      passwordErrorElement.setAttribute('hidden', 'true');      
    }
  })

  selectStateElement.addEventListener('change', async () => {
    const state = selectStateElement.value;

    if (state) {
      selectCountyElement.innerHTML = '';
      // Adiciona a opção padrão
      const defaultOption = document.createElement('option');
      defaultOption.disabled = true;
      defaultOption.selected = true;
      defaultOption.innerText = 'Selecione Município';
      selectCountyElement.appendChild(defaultOption);
    
      try {
        const response = await fetch(`https://servicodados.ibge.gov.br/api/v1/localidades/estados/${state}/municipios`);
        
        if (!response.ok) {
          throw new Error('Falha ao buscar municípios');
        }
        
        const data = await response.json();

        for (const county of data) {
          const option     = document.createElement('option');
          option.value     = county.id;
          option.innerText = county.nome;

          selectCountyElement.appendChild(option);
        }
        
      } catch (error) {
        console.error('Erro ao buscar municípios:', error);
        showError('Não foi possível carregar os municípios. Tente novamente mais tarde.');
      }
    }
  });

  formElement.addEventListener('submit', (event) => {
    // Limpar erros anteriores
    hideError();
    
    // Validar todos os campos antes de enviar
    if (!validateForm()) {
      event.preventDefault();
      return;
    }
    
    if (isSubmitting) {
      event.preventDefault();
      return;
    }
    
    isSubmitting = true;
    signupButton.disabled = true;
    signupButton.classList.add('loading');
  });
  
  function validateForm(): boolean {
    // Validar email
    if (!emailElement.value.trim()) {
      showError('Email é obrigatório');
      emailElement.focus();
      return false;
    }
    
    if (invalidEmail) {
      showError('Por favor, insira um email válido');
      emailElement.focus();
      return false;
    }
    
    // Validar senha
    if (!passwordElement.value) {
      showError('Senha é obrigatória');
      passwordElement.focus();
      return false;
    }
    
    if (passwordElement.value.length < 8) {
      showError('A senha deve ter pelo menos 8 caracteres');
      passwordElement.focus();
      return false;
    }
    
    // Validar confirmação de senha
    if (passwordElement.value !== passwordConfirmationElement.value) {
      showError('As senhas não conferem');
      passwordConfirmationElement.focus();
      return false;
    }
    
    // Validar estado
    if (!selectStateElement.value || selectStateElement.selectedIndex === 0) {
      showError('Por favor, selecione um estado');
      selectStateElement.focus();
      return false;
    }
    
    // Validar município
    if (!selectCountyElement.value || selectCountyElement.selectedIndex === 0) {
      showError('Por favor, selecione um município');
      selectCountyElement.focus();
      return false;
    }
    
    return true;
  }
  
  function showError(message: string) {
    if (generalErrorElement) {
      generalErrorElement.textContent = message;
      generalErrorElement.removeAttribute('hidden');
    }
  }
  
  function hideError() {
    if (generalErrorElement) {
      generalErrorElement.setAttribute('hidden', 'true');
    }
  }
</script>

<style>
	.l-label-text-adds {
		font-weight: bold;
	}

  .l-radio-group-adds { 
    background-color: var(--color-base-100);
    border          : 1px solid var(--color-primary-100);
    border-radius   : 8px;
  }

  /* @media (width > 320px) {
    .figure {
      width: calc( 
             ((var(--image-max-width) - var(--image-min-width)) / 
             (100vw - 320px))); 
    }
  } */
</style>