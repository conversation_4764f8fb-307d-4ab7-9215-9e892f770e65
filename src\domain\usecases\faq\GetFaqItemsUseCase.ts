/**
 * Get FAQ Items Use Case
 *
 * Caso de uso para obter itens de FAQ.
 * Parte da implementação da tarefa 8.7.1 - Implementação de FAQ interativo
 */

import { FaqCategory } from '../../entities/FaqItem';
import {
  FaqFilter,
  FaqPaginationOptions,
  FaqRepository,
  FaqSortOptions,
  PaginatedFaqItems,
} from '../../repositories/FaqRepository';

export interface GetFaqItemsRequest {
  filter?: {
    ids?: string[];
    category?: FaqCategory | FaqCategory[];
    tags?: string[];
    isPublished?: boolean;
    search?: string;
  };
  sort?: {
    field: 'question' | 'viewCount' | 'helpfulCount' | 'createdAt' | 'updatedAt';
    direction: 'asc' | 'desc';
  };
  pagination?: {
    page: number;
    limit: number;
  };
  onlyPublished?: boolean;
}

export interface GetFaqItemsResponse {
  success: boolean;
  data?: PaginatedFaqItems;
  error?: string;
}

export class GetFaqItemsUseCase {
  constructor(private faqRepository: FaqRepository) {}

  async execute(request: GetFaqItemsRequest): Promise<GetFaqItemsResponse> {
    try {
      // Validar os dados de entrada
      if (request.pagination && (request.pagination.page < 1 || request.pagination.limit < 1)) {
        return {
          success: false,
          error: 'Parâmetros de paginação inválidos.',
        };
      }

      let result: PaginatedFaqItems;

      // Verificar se deve retornar apenas itens publicados
      if (request.onlyPublished) {
        // Aplicar filtro de publicados
        const filter: FaqFilter = {
          ...request.filter,
          isPublished: true,
        };

        result = await this.faqRepository.find(filter, request.sort, request.pagination);
      } else {
        // Usar filtro como fornecido
        result = await this.faqRepository.find(
          request.filter || {},
          request.sort,
          request.pagination
        );
      }

      return {
        success: true,
        data: result,
      };
    } catch (error) {
      console.error('Erro ao obter itens de FAQ:', error);

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Erro desconhecido ao obter itens de FAQ.',
      };
    }
  }
}
