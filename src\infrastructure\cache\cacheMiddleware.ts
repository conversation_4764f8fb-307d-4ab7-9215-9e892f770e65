import { defineMiddleware } from 'astro:middleware';
/**
 * Cache middleware for Astro
 * Provides caching for server-side rendered pages
 */
import type { APIContext, MiddlewareResponseHandler } from 'astro';
import { getCache, setCache } from './cacheClient';

// Cache configuration
const CACHE_ENABLED = process.env.ENABLE_CACHE === 'true';
const DEFAULT_TTL = 60 * 60; // 1 hour in seconds

// Cache exclusion patterns
const EXCLUDED_PATHS = ['/api/', '/auth/', '/dashboard/', '/admin/'];

// Cache key generator
function generateCacheKey(request: Request): string {
  const url = new URL(request.url);
  return `page:${url.pathname}${url.search}`;
}

// Check if a path should be excluded from caching
function shouldExcludeFromCache(path: string): boolean {
  return EXCLUDED_PATHS.some((prefix) => path.startsWith(prefix));
}

/**
 * Cache middleware handler
 * Implements page caching for GET requests
 */
export const cacheMiddleware: MiddlewareResponseHandler = defineMiddleware(
  async (context, next) => {
    // Skip caching if disabled or for non-GET requests
    if (!CACHE_ENABLED || context.request.method !== 'GET') {
      return await next();
    }

    const url = new URL(context.request.url);
    const path = url.pathname;

    // Skip caching for excluded paths
    if (shouldExcludeFromCache(path)) {
      return await next();
    }

    // Generate cache key
    const cacheKey = generateCacheKey(context.request);

    // Try to get from cache
    const cachedResponse = await getCache<{
      body: string;
      status: number;
      headers: [string, string][];
    }>(cacheKey);

    // Return cached response if available
    if (cachedResponse) {
      const { body, status, headers } = cachedResponse;
      const response = new Response(body, { status });

      // Set headers from cached response
      headers.forEach(([key, value]) => {
        response.headers.set(key, value);
      });

      // Add cache hit header
      response.headers.set('X-Cache', 'HIT');

      return response;
    }

    // Get fresh response
    const response = await next();

    // Only cache successful responses
    if (response.status === 200 && response.headers.get('Content-Type')?.includes('text/html')) {
      try {
        // Clone the response to avoid consuming it
        const clonedResponse = response.clone();

        // Get response body
        const body = await clonedResponse.text();

        // Get headers to cache
        const headers: [string, string][] = [];
        clonedResponse.headers.forEach((value, key) => {
          headers.push([key, value]);
        });

        // Cache the response
        await setCache(
          cacheKey,
          {
            body,
            status: clonedResponse.status,
            headers,
          },
          DEFAULT_TTL
        );

        // Add cache miss header
        response.headers.set('X-Cache', 'MISS');
      } catch (error) {
        console.error('Error caching response:', error);
      }
    }

    return response;
  }
);
