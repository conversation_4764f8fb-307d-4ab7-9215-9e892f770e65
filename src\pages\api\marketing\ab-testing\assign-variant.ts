/**
 * A/B Testing Assign Variant API
 *
 * Endpoint para atribuir uma variante de teste A/B a um usuário.
 * Parte da implementação da tarefa 8.9.4 - Marketing digital
 */

import type { APIRoute } from 'astro';
import { ABTestingService } from '../../../../domain/services/ABTestingService';
import { SimpleABTestingService } from '../../../../infrastructure/services/SimpleABTestingService';

// Inicializar serviço de testes A/B
const abTestingService: ABTestingService = new SimpleABTestingService();
await abTestingService.initialize();

export const POST: APIRoute = async ({ request }) => {
  try {
    // Obter dados do corpo da requisição
    const body = await request.json();

    // Validar dados
    if (!body.testId) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Parâmetro obrigatório: testId',
        }),
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    // Atribuir variante
    const variant = await abTestingService.assignVariant(body.testId, body.userId);

    if (!variant) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Não foi possível atribuir uma variante',
        }),
        {
          status: 404,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    return new Response(
      JSON.stringify({
        success: true,
        variant,
      }),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  } catch (error) {
    console.error('Erro ao processar requisição de atribuição de variante:', error);

    return new Response(
      JSON.stringify({
        success: false,
        error: 'Erro ao processar requisição',
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  }
};
