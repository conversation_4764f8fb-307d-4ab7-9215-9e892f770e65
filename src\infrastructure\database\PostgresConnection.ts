/**
 * Implementação da conexão com PostgreSQL
 *
 * Esta classe implementa a interface DatabaseConnection usando o driver pg
 * para PostgreSQL. Ela é responsável por gerenciar a conexão com o banco de dados.
 */

import { Pool, PoolClient } from 'pg';
import {
  DatabaseConnection,
  QueryResult,
  TransactionClient,
} from '../../adapters/interfaces/DatabaseConnection';
import { Logger } from '../../application/interfaces/services/Logger';
import { DatabaseConfig } from '../config/database.config';

/**
 * Cliente de transação PostgreSQL
 */
class PostgresTransactionClient implements TransactionClient {
  /**
   * Cria uma nova instância do cliente de transação
   *
   * @param client Cliente PostgreSQL
   * @param logger Serviço de logging
   */
  constructor(
    private readonly client: PoolClient,
    private readonly logger: Logger
  ) {}

  /**
   * Executa uma consulta SQL dentro da transação
   *
   * @param text Texto da consulta
   * @param params Parâmetros da consulta
   * @returns Resultado da consulta
   */
  async query(text: string, params?: any[]): Promise<QueryResult> {
    try {
      this.logger.debug(`Executando consulta na transação: ${text}`, { params });
      const result = await this.client.query(text, params);
      return {
        rows: result.rows,
        rowCount: result.rowCount,
        fields: result.fields,
      };
    } catch (error) {
      this.logger.error('Erro ao executar consulta na transação', error as Error, { text, params });
      throw error;
    }
  }

  /**
   * Confirma a transação
   */
  async commit(): Promise<void> {
    try {
      this.logger.debug('Confirmando transação');
      await this.client.query('COMMIT');
    } catch (error) {
      this.logger.error('Erro ao confirmar transação', error as Error);
      throw error;
    }
  }

  /**
   * Reverte a transação
   */
  async rollback(): Promise<void> {
    try {
      this.logger.debug('Revertendo transação');
      await this.client.query('ROLLBACK');
    } catch (error) {
      this.logger.error('Erro ao reverter transação', error as Error);
      throw error;
    }
  }

  /**
   * Libera o cliente de transação
   */
  async release(): Promise<void> {
    try {
      this.logger.debug('Liberando cliente de transação');
      this.client.release();
    } catch (error) {
      this.logger.error('Erro ao liberar cliente de transação', error as Error);
      throw error;
    }
  }
}

/**
 * Implementação da conexão com PostgreSQL
 */
export class PostgresConnection implements DatabaseConnection {
  private pool: Pool;

  /**
   * Cria uma nova instância da conexão
   *
   * @param config Configuração do banco de dados
   * @param logger Serviço de logging
   */
  constructor(
    private readonly config: DatabaseConfig,
    private readonly logger: Logger
  ) {
    this.pool = new Pool({
      host: config.host,
      port: config.port,
      database: config.database,
      user: config.user,
      password: config.password,
      ssl: config.ssl,
      max: config.poolSize,
      idleTimeoutMillis: config.idleTimeout,
      connectionTimeoutMillis: config.connectionTimeout,
    });

    this.setupListeners();
  }

  /**
   * Configura listeners para eventos do pool
   */
  private setupListeners(): void {
    this.pool.on('error', (err) => {
      this.logger.error('Erro inesperado no pool de conexões PostgreSQL', err);
    });

    this.pool.on('connect', () => {
      this.logger.debug('Nova conexão estabelecida com o PostgreSQL');
    });
  }

  /**
   * Executa uma consulta SQL
   *
   * @param text Texto da consulta
   * @param params Parâmetros da consulta
   * @returns Resultado da consulta
   */
  async query(text: string, params?: any[]): Promise<QueryResult> {
    try {
      this.logger.debug(`Executando consulta: ${text}`, { params });
      const result = await this.pool.query(text, params);
      return {
        rows: result.rows,
        rowCount: result.rowCount,
        fields: result.fields,
      };
    } catch (error) {
      this.logger.error('Erro ao executar consulta', error as Error, { text, params });
      throw error;
    }
  }

  /**
   * Inicia uma transação
   *
   * @returns Cliente de transação
   */
  async beginTransaction(): Promise<TransactionClient> {
    try {
      this.logger.debug('Iniciando transação');
      const client = await this.pool.connect();
      await client.query('BEGIN');
      return new PostgresTransactionClient(client, this.logger);
    } catch (error) {
      this.logger.error('Erro ao iniciar transação', error as Error);
      throw error;
    }
  }

  /**
   * Verifica se a conexão está ativa
   *
   * @returns Verdadeiro se a conexão estiver ativa
   */
  async isConnected(): Promise<boolean> {
    try {
      const client = await this.pool.connect();
      client.release();
      return true;
    } catch (error) {
      this.logger.error('Erro ao verificar conexão', error as Error);
      return false;
    }
  }

  /**
   * Fecha a conexão com o banco de dados
   */
  async disconnect(): Promise<void> {
    try {
      this.logger.info('Fechando conexão com o PostgreSQL');
      await this.pool.end();
    } catch (error) {
      this.logger.error('Erro ao fechar conexão com o PostgreSQL', error as Error);
      throw error;
    }
  }
}
