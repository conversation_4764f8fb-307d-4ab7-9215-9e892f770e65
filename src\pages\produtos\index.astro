---
/**
 * Página de listagem de produtos
 *
 * Esta página demonstra o uso de comunicação de dados server-side sem API tradicional,
 * seguindo a arquitetura Zero-JS.
 */

import Layout from '../../layouts/Layout.astro';
import { getProducts } from '../../services/data-service';

// Obter parâmetros de URL para filtragem e paginação
const page = Number.parseInt(Astro.url.searchParams.get('page') || '1');
const category = Astro.url.searchParams.get('categoria') || undefined;
const featured = Astro.url.searchParams.has('destaque')
  ? Astro.url.searchParams.get('destaque') === 'true'
  : undefined;
const sortBy = Astro.url.searchParams.get('ordenar') || 'createdAt';

// Buscar produtos com filtros
const {
  items: products,
  total,
  pages,
} = await getProducts({
  page,
  limit: 9,
  category,
  featured,
  sortBy,
});

// Categorias disponíveis para filtro
const categories = [
  { id: 'material', name: 'Materiais Didáticos' },
  { id: 'curso', name: 'Cursos Online' },
  { id: 'ebook', name: 'E-books' },
  { id: 'jogo', name: 'Jogos Educativos' },
];

// Opções de ordenação
const sortOptions = [
  { value: 'createdAt', label: 'Mais recentes' },
  { value: 'price', label: 'Menor preço' },
  { value: 'name', label: 'Nome (A-Z)' },
];

// Função para formatar preço
function formatPrice(price: number): string {
  return price.toLocaleString('pt-BR', {
    style: 'currency',
    currency: 'BRL',
  });
}

// Função para construir URL com filtros
function buildFilterUrl(params: Record<string, string | undefined>): string {
  const url = new URL(Astro.url);

  // Limpar parâmetros existentes
  for (const key of url.searchParams.keys()) {
    if (['page', 'categoria', 'destaque', 'ordenar'].includes(key)) {
      url.searchParams.delete(key);
    }
  }

  // Adicionar novos parâmetros
  for (const [key, value] of Object.entries(params)) {
    if (value !== undefined) {
      url.searchParams.set(key, value);
    }
  }

  return url.toString();
}
---

<Layout title="Produtos | Estação da Alfabetização">
  <main class="container mx-auto px-4 py-8">
    <h1 class="text-3xl font-bold mb-6">Produtos Educacionais</h1>
    
    <div class="flex flex-col md:flex-row gap-8">
      <!-- Filtros -->
      <aside class="w-full md:w-64 shrink-0">
        <div class="bg-gray-50 p-4 rounded-lg">
          <h2 class="text-xl font-semibold mb-4">Filtros</h2>
          
          <form method="get" class="space-y-6">
            <!-- Categorias -->
            <div class="filter-group">
              <h3 class="font-medium mb-2">Categorias</h3>
              
              <div class="space-y-2">
                <div class="filter-option">
                  <input 
                    type="radio" 
                    id="category-all" 
                    name="categoria" 
                    value=""
                    checked={!category}
                  >
                  <label for="category-all">Todas as categorias</label>
                </div>
                
                {categories.map(cat => (
                  <div class="filter-option">
                    <input 
                      type="radio" 
                      id={`category-${cat.id}`} 
                      name="categoria" 
                      value={cat.id}
                      checked={category === cat.id}
                    >
                    <label for={`category-${cat.id}`}>{cat.name}</label>
                  </div>
                ))}
              </div>
            </div>
            
            <!-- Produtos em destaque -->
            <div class="filter-group">
              <h3 class="font-medium mb-2">Destaque</h3>
              
              <div class="space-y-2">
                <div class="filter-option">
                  <input 
                    type="radio" 
                    id="featured-all" 
                    name="destaque" 
                    value=""
                    checked={featured === undefined}
                  >
                  <label for="featured-all">Todos os produtos</label>
                </div>
                
                <div class="filter-option">
                  <input 
                    type="radio" 
                    id="featured-yes" 
                    name="destaque" 
                    value="true"
                    checked={featured === true}
                  >
                  <label for="featured-yes">Em destaque</label>
                </div>
              </div>
            </div>
            
            <!-- Ordenação -->
            <div class="filter-group">
              <h3 class="font-medium mb-2">Ordenar por</h3>
              
              <select name="ordenar" class="w-full p-2 border rounded">
                {sortOptions.map(option => (
                  <option 
                    value={option.value}
                    selected={sortBy === option.value}
                  >
                    {option.label}
                  </option>
                ))}
              </select>
            </div>
            
            <button type="submit" class="w-full py-2 bg-primary-blue text-white rounded">
              Aplicar Filtros
            </button>
          </form>
        </div>
      </aside>
      
      <!-- Lista de produtos -->
      <div class="flex-1">
        {products.length === 0 ? (
          <div class="text-center py-12">
            <p class="text-lg text-gray-600">Nenhum produto encontrado com os filtros selecionados.</p>
            <a href="/produtos" class="inline-block mt-4 text-primary-blue hover:underline">
              Ver todos os produtos
            </a>
          </div>
        ) : (
          <>
            <div class="mb-4 flex justify-between items-center">
              <p class="text-gray-600">
                Exibindo {products.length} de {total} produtos
              </p>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {products.map(product => (
                <div class="product-card border rounded-lg overflow-hidden hover:shadow-lg transition-shadow">
                  <a href={`/produtos/${product.id}`} class="block">
                    <div class="aspect-w-3 aspect-h-2">
                      <img 
                        src={product.imageUrl} 
                        alt={product.name}
                        class="object-cover w-full h-48"
                        loading="lazy"
                      >
                    </div>
                    
                    <div class="p-4">
                      <h3 class="text-lg font-semibold mb-2">{product.name}</h3>
                      
                      <p class="text-gray-600 text-sm mb-3 line-clamp-2">
                        {product.description}
                      </p>
                      
                      <div class="flex justify-between items-center">
                        <span class="text-xl font-bold text-primary-blue">
                          {formatPrice(product.price)}
                        </span>
                        
                        {product.featured && (
                          <span class="bg-primary-yellow-light text-primary-yellow-dark text-xs px-2 py-1 rounded">
                            Destaque
                          </span>
                        )}
                      </div>
                    </div>
                  </a>
                </div>
              ))}
            </div>
            
            <!-- Paginação -->
            {pages > 1 && (
              <div class="pagination mt-8 flex justify-center">
                <div class="flex border rounded overflow-hidden">
                  {/* Botão Anterior */}
                  {page > 1 ? (
                    <a 
                      href={buildFilterUrl({ page: (page - 1).toString(), categoria: category, destaque: featured?.toString(), ordenar: sortBy })}
                      class="pagination-link px-4 py-2 border-r"
                    >
                      Anterior
                    </a>
                  ) : (
                    <span class="pagination-link px-4 py-2 border-r text-gray-400">
                      Anterior
                    </span>
                  )}
                  
                  {/* Números de página */}
                  {Array.from({ length: pages }).map((_, i) => (
                    <a 
                      href={buildFilterUrl({ page: (i + 1).toString(), categoria: category, destaque: featured?.toString(), ordenar: sortBy })}
                      class={`pagination-link px-4 py-2 border-r ${page === i + 1 ? 'bg-primary-blue text-white' : ''}`}
                    >
                      {i + 1}
                    </a>
                  ))}
                  
                  {/* Botão Próximo */}
                  {page < pages ? (
                    <a 
                      href={buildFilterUrl({ page: (page + 1).toString(), categoria: category, destaque: featured?.toString(), ordenar: sortBy })}
                      class="pagination-link px-4 py-2"
                    >
                      Próximo
                    </a>
                  ) : (
                    <span class="pagination-link px-4 py-2 text-gray-400">
                      Próximo
                    </span>
                  )}
                </div>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  </main>
</Layout>

<style>
  .filter-option {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }
  
  .filter-option input[type="radio"] {
    width: 1rem;
    height: 1rem;
  }
  
  .pagination-link:hover:not(.text-gray-400) {
    background-color: var(--primary-blue-light);
  }
</style>
