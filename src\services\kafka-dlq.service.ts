/**
 * Serviço de Dead Letter Queue para Kafka
 *
 * Este serviço gerencia a dead letter queue para mensagens Kafka que falharam
 * após múltiplas tentativas, incluindo armazenamento, consulta e reprocessamento.
 */

import { nanoid } from 'nanoid';
import pkg from 'kafkajs';
const { Consumer, Producer, Message } = pkg;
import type { EachMessagePayload } from 'kafkajs';
import { logger } from '../utils/logger';
import { kafkaLoggingService } from './kafka-logging.service';
import { queryHelper } from '@db/queryHelper';
import { kafka, producer as defaultProducer } from '../config/kafka';
import { kafkaProducerRetryConfig } from '../config/kafka-retry.config';

/**
 * Interface para mensagem da dead letter queue
 */
export interface DLQMessage {
  /**
   * ID da mensagem
   */
  messageId: string;

  /**
   * Tópico original
   */
  originalTopic: string;

  /**
   * Tipo de erro
   */
  errorType: string;

  /**
   * Mensagem de erro
   */
  errorMessage: string;

  /**
   * Stack trace do erro
   */
  errorStack?: string;

  /**
   * Número de tentativas
   */
  attemptCount: number;

  /**
   * Chave original
   */
  originalKey?: string;

  /**
   * Valor original
   */
  originalValue?: string;

  /**
   * Headers originais
   */
  originalHeaders?: Record<string, string>;

  /**
   * Metadados de retry
   */
  retryMetadata?: Record<string, any>;

  /**
   * Se a mensagem foi processada
   */
  processed: boolean;

  /**
   * Timestamp de processamento
   */
  processedAt?: Date;

  /**
   * Notas de processamento
   */
  processingNotes?: string;

  /**
   * Timestamp de criação
   */
  createdAt: Date;
}

/**
 * Interface para filtro de consulta de mensagens DLQ
 */
export interface DLQQueryFilter {
  /**
   * Tópico original
   */
  originalTopic?: string;

  /**
   * Tipo de erro
   */
  errorType?: string;

  /**
   * Se a mensagem foi processada
   */
  processed?: boolean;

  /**
   * Data de início
   */
  startDate?: Date;

  /**
   * Data de fim
   */
  endDate?: Date;

  /**
   * Limite de resultados
   */
  limit?: number;

  /**
   * Offset para paginação
   */
  offset?: number;
}

/**
 * Interface para resultado de reprocessamento
 */
export interface ReprocessResult {
  /**
   * Número de mensagens reprocessadas
   */
  reprocessedCount: number;

  /**
   * Número de mensagens com sucesso
   */
  successCount: number;

  /**
   * Número de mensagens com falha
   */
  failureCount: number;

  /**
   * Detalhes dos resultados
   */
  results: Array<{
    messageId: string;
    originalTopic: string;
    success: boolean;
    error?: string;
  }>;
}

/**
 * Classe para o serviço de Dead Letter Queue
 */
class KafkaDLQService {
  private static instance: KafkaDLQService;
  private consumer: Consumer | null = null;
  private isRunning = false;

  /**
   * Construtor privado para implementar Singleton
   */
  private constructor() {}

  /**
   * Obtém a instância única do serviço
   */
  public static getInstance(): KafkaDLQService {
    if (!KafkaDLQService.instance) {
      KafkaDLQService.instance = new KafkaDLQService();
    }
    return KafkaDLQService.instance;
  }

  /**
   * Inicializa o consumidor da DLQ
   */
  public async initConsumer(): Promise<void> {
    if (this.consumer) {
      return;
    }

    try {
      // Criar consumidor
      this.consumer = kafka.consumer({
        groupId: 'dlq-consumer-group',
        sessionTimeout: 30000,
      });

      // Conectar consumidor
      await this.consumer.connect();

      // Inscrever no tópico DLQ
      await this.consumer.subscribe({
        topic: kafkaProducerRetryConfig.deadLetterQueue.topic,
        fromBeginning: false,
      });

      kafkaLoggingService.info('kafka.dlq', 'Consumidor da DLQ inicializado');
    } catch (error) {
      kafkaLoggingService.error(
        'kafka.dlq',
        'Erro ao inicializar consumidor da DLQ',
        error
      );
      throw error;
    }
  }

  /**
   * Inicia o processamento da DLQ
   */
  public async startProcessing(): Promise<void> {
    if (this.isRunning || !this.consumer) {
      return;
    }

    try {
      this.isRunning = true;

      // Iniciar consumo
      await this.consumer.run({
        eachMessage: async (payload: EachMessagePayload) => {
          await this.processDLQMessage(payload);
        },
      });

      kafkaLoggingService.info('kafka.dlq', 'Processamento da DLQ iniciado');
    } catch (error) {
      this.isRunning = false;
      kafkaLoggingService.error(
        'kafka.dlq',
        'Erro ao iniciar processamento da DLQ',
        error
      );
      throw error;
    }
  }

  /**
   * Processa uma mensagem da DLQ
   * @param payload - Payload da mensagem
   */
  private async processDLQMessage(payload: EachMessagePayload): Promise<void> {
    const { topic, partition, message } = payload;

    try {
      // Extrair headers
      const headers = message.headers || {};
      const originalTopic =
        headers['dlq-original-topic']?.toString() || 'unknown';
      const errorType = headers['dlq-error-type']?.toString() || 'unknown';
      const attemptCount = Number.parseInt(
        headers['dlq-attempt-count']?.toString() || '0',
        10
      );

      // Extrair valor
      let messageValue: any = {};
      if (message.value) {
        try {
          messageValue = JSON.parse(message.value.toString());
        } catch (parseError) {
          kafkaLoggingService.warn(
            'kafka.dlq',
            'Erro ao parsear valor da mensagem DLQ',
            parseError
          );
        }
      }

      // Extrair informações
      const originalMessage = messageValue.originalMessage;
      const error = messageValue.error || {};
      const metadata = messageValue.metadata || {};

      // Salvar mensagem no banco de dados
      const messageId = nanoid();
      await queryHelper.query(
        `INSERT INTO tab_dead_letter_queue (
          message_id,
          original_topic,
          error_type,
          error_message,
          error_stack,
          attempt_count,
          original_key,
          original_value,
          original_headers,
          retry_metadata,
          created_at
        ) VALUES (
          $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, NOW()
        )`,
        [
          messageId,
          originalTopic,
          errorType,
          error.message || 'Unknown error',
          error.stack,
          attemptCount,
          message.key?.toString(),
          originalMessage,
          headers ? JSON.stringify(headers) : null,
          metadata.retryMetadata
            ? JSON.stringify(metadata.retryMetadata)
            : null,
        ]
      );

      kafkaLoggingService.info(
        'kafka.dlq',
        `Mensagem DLQ processada e armazenada: ${messageId}`,
        {
          messageId,
          originalTopic,
          errorType,
        }
      );

      // Commit offset
      if (this.consumer) {
        await this.consumer.commitOffsets([
          {
            topic,
            partition,
            offset: (BigInt(message.offset) + BigInt(1)).toString(),
          },
        ]);
      }
    } catch (error) {
      kafkaLoggingService.error(
        'kafka.dlq',
        'Erro ao processar mensagem DLQ',
        error
      );
    }
  }

  /**
   * Consulta mensagens da DLQ
   * @param filter - Filtro de consulta
   * @returns Lista de mensagens
   */
  public async queryMessages(
    filter: DLQQueryFilter = {}
  ): Promise<DLQMessage[]> {
    try {
      // Construir consulta
      let query = `
        SELECT
          message_id,
          original_topic,
          error_type,
          error_message,
          error_stack,
          attempt_count,
          original_key,
          original_value,
          original_headers,
          retry_metadata,
          processed,
          processed_at,
          processing_notes,
          created_at
        FROM
          tab_dead_letter_queue
        WHERE 1=1
      `;

      const params: any[] = [];
      let paramIndex = 1;

      // Adicionar filtros
      if (filter.originalTopic) {
        query += ` AND original_topic = $${paramIndex++}`;
        params.push(filter.originalTopic);
      }

      if (filter.errorType) {
        query += ` AND error_type = $${paramIndex++}`;
        params.push(filter.errorType);
      }

      if (filter.processed !== undefined) {
        query += ` AND processed = $${paramIndex++}`;
        params.push(filter.processed);
      }

      if (filter.startDate) {
        query += ` AND created_at >= $${paramIndex++}`;
        params.push(filter.startDate);
      }

      if (filter.endDate) {
        query += ` AND created_at <= $${paramIndex++}`;
        params.push(filter.endDate);
      }

      // Ordenar e limitar
      query += ` ORDER BY created_at DESC`;

      if (filter.limit) {
        query += ` LIMIT $${paramIndex++}`;
        params.push(filter.limit);
      }

      if (filter.offset) {
        query += ` OFFSET $${paramIndex++}`;
        params.push(filter.offset);
      }

      // Executar consulta
      const result = await queryHelper.query(query, params);

      return result.rows.map(row => ({
        messageId: row.message_id,
        originalTopic: row.original_topic,
        errorType: row.error_type,
        errorMessage: row.error_message,
        errorStack: row.error_stack,
        attemptCount: row.attempt_count,
        originalKey: row.original_key,
        originalValue: row.original_value,
        originalHeaders: row.original_headers,
        retryMetadata: row.retry_metadata,
        processed: row.processed,
        processedAt: row.processed_at,
        processingNotes: row.processing_notes,
        createdAt: row.created_at,
      }));
    } catch (error) {
      kafkaLoggingService.error(
        'kafka.dlq',
        'Erro ao consultar mensagens DLQ',
        error
      );
      throw error;
    }
  }

  /**
   * Reprocessa mensagens da DLQ
   * @param messageIds - IDs das mensagens a reprocessar
   * @param producer - Instância do produtor Kafka
   * @returns Resultado do reprocessamento
   */
  public async reprocessMessages(
    messageIds: string[],
    producer: Producer = defaultProducer
  ): Promise<ReprocessResult> {
    try {
      // Verificar se há mensagens para reprocessar
      if (!messageIds.length) {
        return {
          reprocessedCount: 0,
          successCount: 0,
          failureCount: 0,
          results: [],
        };
      }

      // Obter mensagens do banco de dados
      const result = await queryHelper.query(
        `SELECT
          message_id,
          original_topic,
          original_key,
          original_value,
          original_headers
        FROM
          tab_dead_letter_queue
        WHERE
          message_id = ANY($1)
          AND processed = FALSE
        `,
        [messageIds]
      );

      const messages = result.rows;
      const reprocessResult: ReprocessResult = {
        reprocessedCount: messages.length,
        successCount: 0,
        failureCount: 0,
        results: [],
      };

      // Conectar produtor se necessário
      if (!producer.isConnected()) {
        await producer.connect();
      }

      // Reprocessar cada mensagem
      for (const message of messages) {
        try {
          // Preparar mensagem para reenvio
          const kafkaMessage: Message = {
            key: message.original_key
              ? Buffer.from(message.original_key)
              : null,
            value: message.original_value
              ? Buffer.from(message.original_value)
              : null,
            headers: {
              ...(message.original_headers || {}),
              reprocessed: 'true',
              'reprocessed-at': Date.now().toString(),
              'original-dlq-id': message.message_id,
            },
          };

          // Enviar mensagem para tópico original
          await producer.send({
            topic: message.original_topic,
            messages: [kafkaMessage],
          });

          // Atualizar status da mensagem
          await queryHelper.query(
            `UPDATE tab_dead_letter_queue
            SET processed = TRUE, processed_at = NOW(), processing_notes = $1
            WHERE message_id = $2`,
            ['Reprocessado com sucesso', message.message_id]
          );

          // Registrar resultado
          reprocessResult.successCount++;
          reprocessResult.results.push({
            messageId: message.message_id,
            originalTopic: message.original_topic,
            success: true,
          });

          kafkaLoggingService.info(
            'kafka.dlq',
            `Mensagem reprocessada com sucesso: ${message.message_id}`,
            {
              messageId: message.message_id,
              originalTopic: message.original_topic,
            }
          );
        } catch (error) {
          // Registrar falha
          reprocessResult.failureCount++;
          reprocessResult.results.push({
            messageId: message.message_id,
            originalTopic: message.original_topic,
            success: false,
            error: error.message,
          });

          // Atualizar notas de processamento
          await queryHelper.query(
            `UPDATE tab_dead_letter_queue
            SET processing_notes = $1
            WHERE message_id = $2`,
            [`Falha ao reprocessar: ${error.message}`, message.message_id]
          );

          kafkaLoggingService.error(
            'kafka.dlq',
            `Erro ao reprocessar mensagem: ${message.message_id}`,
            error
          );
        }
      }

      return reprocessResult;
    } catch (error) {
      kafkaLoggingService.error(
        'kafka.dlq',
        'Erro ao reprocessar mensagens DLQ',
        error
      );
      throw error;
    }
  }

  /**
   * Para o processamento da DLQ
   */
  public async stopProcessing(): Promise<void> {
    if (!this.isRunning || !this.consumer) {
      return;
    }

    try {
      await this.consumer.stop();
      this.isRunning = false;
      kafkaLoggingService.info('kafka.dlq', 'Processamento da DLQ parado');
    } catch (error) {
      kafkaLoggingService.error(
        'kafka.dlq',
        'Erro ao parar processamento da DLQ',
        error
      );
      throw error;
    }
  }

  /**
   * Finaliza o consumidor da DLQ
   */
  public async shutdown(): Promise<void> {
    if (!this.consumer) {
      return;
    }

    try {
      if (this.isRunning) {
        await this.stopProcessing();
      }

      await this.consumer.disconnect();
      this.consumer = null;
      kafkaLoggingService.info('kafka.dlq', 'Consumidor da DLQ finalizado');
    } catch (error) {
      kafkaLoggingService.error(
        'kafka.dlq',
        'Erro ao finalizar consumidor da DLQ',
        error
      );
    }
  }
}

// Exportar instância única do serviço
export const kafkaDLQService = KafkaDLQService.getInstance();
