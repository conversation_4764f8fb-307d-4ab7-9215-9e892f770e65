import type { QueryResult } from 'pg';
import { pgHelper } from './pgHelper';

async function readByTable(tableName: string): Promise<QueryResult> {
  return await pgHelper.query(
    `SELECT a.attname                                       AS column_name,
            pg_catalog.format_type(a.atttypid, a.atttypmod) AS data_type,
            CASE 
              WHEN d.description IS NULL 
              THEN c.relname || '.' || a.attname
            ELSE 
              d.description 
            END                                             AS column_comment
       FROM pg_catalog.pg_attribute   a
       JOIN pg_catalog.pg_class       c ON a.attrelid = c.oid
  LEFT JOIN pg_catalog.pg_description d ON d.objoid   = c.oid    AND 
                                           d.objsubid = a.attnum
      WHERE c.relname = $1 AND     -- Usar parâmetro aqui
            a.attnum > 0     AND NOT -- Ignora colunas do sistema
            a.attisdropped;          -- Ignora colunas que foram removidas`,
    [tableName]
  );
}

export const informationSchemaColumnRepository = {
  readByTable,
};
