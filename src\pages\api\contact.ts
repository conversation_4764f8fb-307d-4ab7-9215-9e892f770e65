/**
 * API de Contato
 *
 * Endpoint para processamento do formulário de contato.
 * Parte da implementação da tarefa 8.6.1 - Implementação de formulário de contato
 */

import type { APIRoute } from 'astro';
import { getConfig } from '../../config';
import { ContactMessageRepository } from '../../domain/repositories/ContactMessageRepository';
import { EmailService } from '../../domain/services/EmailService';
import { SendContactMessageUseCase } from '../../domain/usecases/contact/SendContactMessageUseCase';
import { PostgresContactMessageRepository } from '../../infrastructure/database/repositories/PostgresContactMessageRepository';
import { NodemailerEmailService } from '../../infrastructure/services/NodemailerEmailService';

// Obter configurações
const config = getConfig();

// Inicializar serviços
const contactMessageRepository: ContactMessageRepository = new PostgresContactMessageRepository();
const emailService: EmailService = new NodemailerEmailService({
  host: config.email.host,
  port: config.email.port,
  secure: config.email.secure,
  auth: {
    user: config.email.user,
    pass: config.email.password,
  },
  defaultFrom: config.email.defaultFrom,
});

// Inicializar caso de uso
const sendContactMessageUseCase = new SendContactMessageUseCase(
  contactMessageRepository,
  emailService,
  config.contact.adminEmail,
  config.recaptcha.secretKey
);

export const POST: APIRoute = async ({ request, redirect }) => {
  try {
    // Verificar se a requisição é multipart/form-data
    const contentType = request.headers.get('content-type') || '';
    let formData: FormData;

    if (contentType.includes('multipart/form-data')) {
      formData = await request.formData();
    } else {
      const body = await request.text();
      const params = new URLSearchParams(body);
      formData = new FormData();

      for (const [key, value] of params.entries()) {
        formData.append(key, value);
      }
    }

    // Extrair dados do formulário
    const name = formData.get('name') as string;
    const email = formData.get('email') as string;
    const phone = formData.get('phone') as string;
    const subject = formData.get('subject') as string;
    const message = formData.get('message') as string;
    const category = formData.get('category') as string;
    const recaptchaToken = formData.get('g-recaptcha-response') as string;

    // Processar anexos, se houver
    const attachments: string[] = [];

    if (contentType.includes('multipart/form-data')) {
      const files = formData.getAll('attachments') as File[];

      for (const file of files) {
        if (file.size > 0) {
          // Em um cenário real, aqui seria feito o upload do arquivo
          // e o caminho seria armazenado em attachments
          console.log(`Processando anexo: ${file.name} (${file.size} bytes)`);

          // Simular caminho do arquivo
          attachments.push(`/uploads/${Date.now()}_${file.name}`);
        }
      }
    }

    // Validar dados básicos
    if (!name || !email || !subject || !message || !category) {
      return redirect('/contato?error=Todos os campos obrigatórios devem ser preenchidos.', 302);
    }

    // Enviar mensagem de contato
    const result = await sendContactMessageUseCase.execute({
      name,
      email,
      phone,
      subject,
      message,
      category: category as string,
      attachments,
      recaptchaToken,
    });

    if (result.success) {
      return redirect('/contato?success=true', 302);
    }
    return redirect(
      `/contato?error=${encodeURIComponent(result.error || 'Erro desconhecido')}`,
      302
    );
  } catch (error) {
    console.error('Erro ao processar formulário de contato:', error);

    return redirect(
      '/contato?error=Ocorreu um erro ao processar sua mensagem. Por favor, tente novamente.',
      302
    );
  }
};
