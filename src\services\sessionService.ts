/**
 * Serviço de gerenciamento de sessões
 *
 * Este serviço é responsável por gerenciar sessões de usuários
 * utilizando o Valkey como armazenamento.
 */

import { cacheService } from '@services/cacheService';
import { logger } from '@utils/logger';
import { ulid } from 'ulid';

/**
 * Interface para dados de sessão
 */
export interface SessionData {
  /**
   * ID da sessão
   */
  id: string;

  /**
   * ID do usuário
   */
  userId: string;

  /**
   * Nome do usuário
   */
  userName: string;

  /**
   * E-mail do usuário
   */
  userEmail: string;

  /**
   * Dados adicionais da sessão
   */
  data: Record<string, any>;

  /**
   * Timestamp de criação da sessão
   */
  createdAt: number;

  /**
   * Timestamp da última atualização da sessão
   */
  lastActivity: number;

  /**
   * Endereço IP da última atividade
   */
  ipAddress?: string;

  /**
   * User-Agent da última atividade
   */
  userAgent?: string;
}

/**
 * Serviço de sessão
 */
export const sessionService = {
  /**
   * Prefixo para chaves de sessão no cache
   */
  SESSION_PREFIX: 'session:',

  /**
   * Prefixo para índice de sessões de usuário no cache
   */
  USER_SESSIONS_PREFIX: 'user:sessions:',

  /**
   * Tempo de vida padrão da sessão em segundos (24 horas)
   */
  DEFAULT_TTL: 24 * 60 * 60,

  /**
   * Tempo de inatividade máximo antes da expiração (2 horas)
   */
  INACTIVITY_TIMEOUT: 2 * 60 * 60,

  /**
   * Tempo limite para renovação automática (30 minutos antes da expiração)
   */
  RENEWAL_THRESHOLD: 30 * 60,

  /**
   * Cria uma nova sessão
   * @param userId - ID do usuário
   * @param userName - Nome do usuário
   * @param userEmail - E-mail do usuário
   * @param data - Dados adicionais da sessão
   * @param ipAddress - Endereço IP
   * @param userAgent - User-Agent
   * @param ttl - Tempo de vida em segundos
   * @returns Dados da sessão criada
   */
  async create(
    userId: string,
    userName: string,
    userEmail: string,
    data: Record<string, any> = {},
    ipAddress?: string,
    userAgent?: string,
    ttl: number = this.DEFAULT_TTL
  ): Promise<SessionData> {
    try {
      // Gerar ID único para a sessão
      const sessionId = ulid();

      // Timestamp atual
      const now = Date.now();

      // Criar objeto de sessão
      const session: SessionData = {
        id: sessionId,
        userId,
        userName,
        userEmail,
        data,
        createdAt: now,
        lastActivity: now,
        ipAddress,
        userAgent,
      };

      // Armazenar sessão no cache
      const sessionKey = this.getSessionKey(sessionId);
      await cacheService.setItem(sessionKey, JSON.stringify(session), ttl);

      // Adicionar sessão ao índice de sessões do usuário
      const userSessionsKey = this.getUserSessionsKey(userId);
      await cacheService.sadd(userSessionsKey, sessionId);
      await cacheService.expire(userSessionsKey, ttl);

      return session;
    } catch (error) {
      logger.error('Erro ao criar sessão:', error);
      throw error;
    }
  },

  /**
   * Obtém uma sessão pelo ID
   * @param sessionId - ID da sessão
   * @returns Dados da sessão ou null se não encontrada
   */
  async get(sessionId: string): Promise<SessionData | null> {
    try {
      // Obter sessão do cache
      const sessionKey = this.getSessionKey(sessionId);
      const sessionData = await cacheService.getItem(sessionKey);

      if (!sessionData) {
        return null;
      }

      return JSON.parse(sessionData);
    } catch (error) {
      logger.error('Erro ao obter sessão:', error);
      return null;
    }
  },

  /**
   * Atualiza uma sessão existente
   * @param sessionId - ID da sessão
   * @param data - Novos dados da sessão
   * @param ipAddress - Endereço IP (opcional)
   * @param userAgent - User-Agent (opcional)
   * @param ttl - Novo tempo de vida em segundos (opcional)
   * @returns Dados da sessão atualizada ou null se não encontrada
   */
  async update(
    sessionId: string,
    data: Record<string, any>,
    ipAddress?: string,
    userAgent?: string,
    ttl: number = this.DEFAULT_TTL
  ): Promise<SessionData | null> {
    try {
      // Obter sessão atual
      const session = await this.get(sessionId);

      if (!session) {
        return null;
      }

      // Atualizar dados da sessão
      const updatedSession: SessionData = {
        ...session,
        data: {
          ...session.data,
          ...data,
        },
        lastActivity: Date.now(),
        ipAddress: ipAddress || session.ipAddress,
        userAgent: userAgent || session.userAgent,
      };

      // Armazenar sessão atualizada no cache
      const sessionKey = this.getSessionKey(sessionId);
      await cacheService.setItem(sessionKey, JSON.stringify(updatedSession), ttl);

      // Atualizar TTL do índice de sessões do usuário
      const userSessionsKey = this.getUserSessionsKey(session.userId);
      await cacheService.expire(userSessionsKey, ttl);

      return updatedSession;
    } catch (error) {
      logger.error('Erro ao atualizar sessão:', error);
      return null;
    }
  },

  /**
   * Atualiza apenas o timestamp de última atividade da sessão
   * @param sessionId - ID da sessão
   * @param ipAddress - Endereço IP (opcional)
   * @param ttl - Novo tempo de vida em segundos (opcional)
   * @returns Verdadeiro se a sessão foi atualizada
   */
  async touch(
    sessionId: string,
    ipAddress?: string,
    ttl: number = this.DEFAULT_TTL
  ): Promise<boolean> {
    try {
      // Obter sessão atual
      const session = await this.get(sessionId);

      if (!session) {
        return false;
      }

      // Atualizar timestamp de última atividade
      const updatedSession: SessionData = {
        ...session,
        lastActivity: Date.now(),
        ipAddress: ipAddress || session.ipAddress,
      };

      // Armazenar sessão atualizada no cache
      const sessionKey = this.getSessionKey(sessionId);
      await cacheService.setItem(sessionKey, JSON.stringify(updatedSession), ttl);

      // Atualizar TTL do índice de sessões do usuário
      const userSessionsKey = this.getUserSessionsKey(session.userId);
      await cacheService.expire(userSessionsKey, ttl);

      return true;
    } catch (error) {
      logger.error('Erro ao atualizar timestamp de sessão:', error);
      return false;
    }
  },

  /**
   * Exclui uma sessão
   * @param sessionId - ID da sessão
   * @returns Verdadeiro se a sessão foi excluída
   */
  async delete(sessionId: string): Promise<boolean> {
    try {
      // Obter sessão atual
      const session = await this.get(sessionId);

      if (!session) {
        return false;
      }

      // Excluir sessão do cache
      const sessionKey = this.getSessionKey(sessionId);
      await cacheService.delItem(sessionKey);

      // Remover sessão do índice de sessões do usuário
      const userSessionsKey = this.getUserSessionsKey(session.userId);
      await cacheService.srem(userSessionsKey, sessionId);

      return true;
    } catch (error) {
      logger.error('Erro ao excluir sessão:', error);
      return false;
    }
  },

  /**
   * Exclui todas as sessões de um usuário
   * @param userId - ID do usuário
   * @returns Número de sessões excluídas
   */
  async deleteUserSessions(userId: string): Promise<number> {
    try {
      // Obter IDs de sessões do usuário
      const userSessionsKey = this.getUserSessionsKey(userId);
      const sessionIds = await cacheService.smembers(userSessionsKey);

      if (!sessionIds || sessionIds.length === 0) {
        return 0;
      }

      // Excluir cada sessão
      let deletedCount = 0;

      for (const sessionId of sessionIds) {
        const sessionKey = this.getSessionKey(sessionId);
        await cacheService.delItem(sessionKey);
        deletedCount++;
      }

      // Excluir índice de sessões do usuário
      await cacheService.delItem(userSessionsKey);

      return deletedCount;
    } catch (error) {
      logger.error('Erro ao excluir sessões do usuário:', error);
      return 0;
    }
  },

  /**
   * Obtém todas as sessões de um usuário
   * @param userId - ID do usuário
   * @returns Lista de sessões do usuário
   */
  async getUserSessions(userId: string): Promise<SessionData[]> {
    try {
      // Obter IDs de sessões do usuário
      const userSessionsKey = this.getUserSessionsKey(userId);
      const sessionIds = await cacheService.smembers(userSessionsKey);

      if (!sessionIds || sessionIds.length === 0) {
        return [];
      }

      // Obter dados de cada sessão
      const sessions: SessionData[] = [];

      for (const sessionId of sessionIds) {
        const session = await this.get(sessionId);

        if (session) {
          sessions.push(session);
        }
      }

      return sessions;
    } catch (error) {
      logger.error('Erro ao obter sessões do usuário:', error);
      return [];
    }
  },

  /**
   * Verifica se uma sessão existe e está válida
   * @param sessionId - ID da sessão
   * @returns Verdadeiro se a sessão existe e está válida
   */
  async isValid(sessionId: string): Promise<boolean> {
    try {
      // Verificar se a sessão existe
      const session = await this.get(sessionId);

      return !!session;
    } catch (error) {
      logger.error('Erro ao verificar validade da sessão:', error);
      return false;
    }
  },

  /**
   * Obtém a chave de sessão para o cache
   * @param sessionId - ID da sessão
   * @returns Chave de sessão
   */
  getSessionKey(sessionId: string): string {
    return `${this.SESSION_PREFIX}${sessionId}`;
  },

  /**
   * Obtém a chave de índice de sessões do usuário para o cache
   * @param userId - ID do usuário
   * @returns Chave de índice de sessões do usuário
   */
  getUserSessionsKey(userId: string): string {
    return `${this.USER_SESSIONS_PREFIX}${userId}`;
  },

  /**
   * Verifica se uma sessão precisa ser renovada
   * @param session - Dados da sessão
   * @returns Verdadeiro se a sessão precisa ser renovada
   */
  shouldRenewSession(session: SessionData): boolean {
    if (!session) {
      return false;
    }

    // Calcular tempo desde a última atividade
    const now = Date.now();
    const inactivityTime = now - session.lastActivity;

    // Se inativo por muito tempo, não renovar (sessão deve expirar)
    if (inactivityTime > this.INACTIVITY_TIMEOUT * 1000) {
      return false;
    }

    // Calcular tempo desde a criação
    const sessionAge = now - session.createdAt;

    // Renovar se a sessão está próxima de expirar, mas ainda ativa
    return sessionAge > (this.DEFAULT_TTL - this.RENEWAL_THRESHOLD) * 1000;
  },

  /**
   * Renova uma sessão existente
   * @param sessionId - ID da sessão atual
   * @param ipAddress - Endereço IP atual
   * @param userAgent - User-Agent atual
   * @returns Nova sessão ou null em caso de erro
   */
  async renewSession(
    sessionId: string,
    ipAddress?: string,
    userAgent?: string
  ): Promise<SessionData | null> {
    try {
      // Obter sessão atual
      const session = await this.get(sessionId);

      if (!session) {
        return null;
      }

      // Verificar se a sessão deve ser renovada
      if (!this.shouldRenewSession(session)) {
        return session;
      }

      // Criar nova sessão com os mesmos dados
      const newSession = await this.create(
        session.userId,
        session.userName,
        session.userEmail,
        session.data,
        ipAddress || session.ipAddress,
        userAgent || session.userAgent
      );

      // Excluir sessão antiga
      await this.delete(sessionId);

      return newSession;
    } catch (error) {
      logger.error('Erro ao renovar sessão:', error);
      return null;
    }
  },

  /**
   * Verifica se uma sessão expirou por inatividade
   * @param session - Dados da sessão
   * @returns Verdadeiro se a sessão expirou por inatividade
   */
  hasSessionExpiredByInactivity(session: SessionData): boolean {
    if (!session) {
      return true;
    }

    // Calcular tempo desde a última atividade
    const now = Date.now();
    const inactivityTime = now - session.lastActivity;

    // Verificar se excedeu o tempo de inatividade máximo
    return inactivityTime > this.INACTIVITY_TIMEOUT * 1000;
  },

  /**
   * Limpa sessões expiradas de um usuário
   * @param userId - ID do usuário
   * @returns Número de sessões limpas
   */
  async cleanupExpiredSessions(userId: string): Promise<number> {
    try {
      // Obter todas as sessões do usuário
      const sessions = await this.getUserSessions(userId);

      if (sessions.length === 0) {
        return 0;
      }

      // Filtrar sessões expiradas por inatividade
      const expiredSessions = sessions.filter((session) =>
        this.hasSessionExpiredByInactivity(session)
      );

      // Excluir cada sessão expirada
      let cleanedCount = 0;

      for (const session of expiredSessions) {
        await this.delete(session.id);
        cleanedCount++;
      }

      return cleanedCount;
    } catch (error) {
      logger.error('Erro ao limpar sessões expiradas:', error);
      return 0;
    }
  },

  /**
   * Agenda limpeza periódica de sessões expiradas
   * @param intervalMinutes - Intervalo em minutos entre limpezas
   */
  scheduleSessionCleanup(intervalMinutes = 60): void {
    // Executar limpeza periodicamente
    setInterval(
      async () => {
        try {
          logger.info('Iniciando limpeza programada de sessões expiradas');

          // Obter chaves de índice de sessões de usuários
          const userSessionKeys = await cacheService.keys(`${this.USER_SESSIONS_PREFIX}*`);

          if (!userSessionKeys || userSessionKeys.length === 0) {
            return;
          }

          // Extrair IDs de usuário das chaves
          const userIds = userSessionKeys.map((key) => key.replace(this.USER_SESSIONS_PREFIX, ''));

          // Limpar sessões expiradas para cada usuário
          let totalCleaned = 0;

          for (const userId of userIds) {
            const cleaned = await this.cleanupExpiredSessions(userId);
            totalCleaned += cleaned;
          }

          logger.info(`Limpeza de sessões concluída: ${totalCleaned} sessões expiradas removidas`);
        } catch (error) {
          logger.error('Erro na limpeza programada de sessões:', error);
        }
      },
      intervalMinutes * 60 * 1000
    );
  },
};
