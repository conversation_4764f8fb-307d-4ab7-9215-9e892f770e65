---
/**
 * Página de Administração de Sessões
 * 
 * Esta página permite visualizar e gerenciar as sessões ativas
 * no sistema, incluindo invalidação de sessões por usuário ou IP.
 */

// Importações
import AdminLayout from '@layouts/AdminLayout.astro';
import { valkeySessionService } from '@services/valkeySessionService';
import { getCurrentUser } from '@utils/authUtils';
import { hasPermission } from '@utils/permissionUtils';
import { logger } from '@utils/logger';
import { formatDate, formatDuration } from '@utils/formatters';

// Verificar autenticação e permissão
const user = await getCurrentUser(Astro.cookies);

// Redirecionar se não estiver autenticado ou não tiver permissão
if (!user || !await hasPermission(user.ulid_user, 'security:manage')) {
  return Astro.redirect('/admin/login?redirect=/admin/sessions');
}

// Processar ações
let success = '';
let error = '';

if (Astro.request.method === 'POST') {
  try {
    const formData = await Astro.request.formData();
    const action = formData.get('action');
    
    if (action === 'invalidate_session') {
      const sessionId = formData.get('session_id') as string;
      
      if (sessionId) {
        const result = await valkeySessionService.delete(sessionId);
        
        if (result) {
          success = `Sessão ${sessionId} invalidada com sucesso`;
          logger.info(`Sessão invalidada manualmente: ${sessionId}`, {
            adminUser: user.ulid_user
          });
        } else {
          error = `Erro ao invalidar sessão ${sessionId}`;
        }
      }
    } else if (action === 'invalidate_user_sessions') {
      const userId = formData.get('user_id') as string;
      
      if (userId) {
        const count = await valkeySessionService.invalidateUserSessions(userId);
        
        if (count > 0) {
          success = `${count} sessões do usuário ${userId} invalidadas com sucesso`;
          logger.info(`Sessões de usuário invalidadas manualmente: ${userId}`, {
            adminUser: user.ulid_user,
            count
          });
        } else {
          error = `Nenhuma sessão encontrada para o usuário ${userId}`;
        }
      }
    } else if (action === 'invalidate_ip_sessions') {
      const ipAddress = formData.get('ip_address') as string;
      
      if (ipAddress) {
        const count = await valkeySessionService.invalidateIpSessions(ipAddress);
        
        if (count > 0) {
          success = `${count} sessões do IP ${ipAddress} invalidadas com sucesso`;
          logger.info(`Sessões de IP invalidadas manualmente: ${ipAddress}`, {
            adminUser: user.ulid_user,
            count
          });
        } else {
          error = `Nenhuma sessão encontrada para o IP ${ipAddress}`;
        }
      }
    } else if (action === 'invalidate_all_sessions') {
      // Verificar confirmação
      const confirmation = formData.get('confirmation') as string;
      
      if (confirmation === 'INVALIDATE_ALL') {
        const count = await valkeySessionService.invalidateAllSessions();
        
        if (count > 0) {
          success = `${count} sessões invalidadas com sucesso`;
          logger.warn(`Todas as sessões invalidadas manualmente`, {
            adminUser: user.ulid_user,
            count
          });
        } else {
          error = 'Nenhuma sessão encontrada para invalidar';
        }
      } else {
        error = 'Confirmação inválida. Digite INVALIDATE_ALL para confirmar.';
      }
    }
  } catch (err) {
    error = err.message;
    logger.error('Erro ao processar ação de sessão:', err);
  }
}

// Obter estatísticas de sessões
const stats = await valkeySessionService.getStats();

// Título da página
const title = 'Gerenciamento de Sessões';
---

<AdminLayout title={title}>
  <div class="container mx-auto px-4 py-8">
    <h1 class="text-3xl font-bold mb-6">{title}</h1>
    
    {error && (
      <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6" role="alert">
        <p class="font-bold">Erro</p>
        <p>{error}</p>
      </div>
    )}
    
    {success && (
      <div class="bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mb-6" role="alert">
        <p>{success}</p>
      </div>
    )}
    
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
      <div class="bg-white rounded-lg shadow-md p-6">
        <h2 class="text-xl font-semibold mb-4">Estatísticas</h2>
        <div class="grid grid-cols-2 gap-4">
          <div class="bg-blue-50 p-4 rounded-lg">
            <p class="text-sm text-gray-600">Sessões Ativas</p>
            <p class="text-2xl font-bold text-blue-600">{stats.activeSessions}</p>
          </div>
          
          <div class="bg-blue-50 p-4 rounded-lg">
            <p class="text-sm text-gray-600">Usuários Ativos</p>
            <p class="text-2xl font-bold text-blue-600">{stats.activeUsers}</p>
          </div>
          
          <div class="bg-blue-50 p-4 rounded-lg">
            <p class="text-sm text-gray-600">Sessões Criadas Hoje</p>
            <p class="text-2xl font-bold text-blue-600">{stats.sessionsCreatedToday}</p>
          </div>
          
          <div class="bg-blue-50 p-4 rounded-lg">
            <p class="text-sm text-gray-600">Duração Média</p>
            <p class="text-2xl font-bold text-blue-600">{formatDuration(stats.averageSessionDuration / 1000)}</p>
          </div>
        </div>
      </div>
      
      <div class="bg-white rounded-lg shadow-md p-6">
        <h2 class="text-xl font-semibold mb-4">Ações</h2>
        
        <div class="space-y-6">
          <div>
            <h3 class="font-medium text-gray-700 mb-2">Invalidar Sessão por ID</h3>
            <form method="POST" class="flex gap-2">
              <input type="hidden" name="action" value="invalidate_session" />
              <input 
                type="text" 
                name="session_id" 
                placeholder="ID da sessão" 
                class="flex-1 border border-gray-300 rounded-md px-3 py-2"
                required
              />
              <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md"
              >
                Invalidar
              </button>
            </form>
          </div>
          
          <div>
            <h3 class="font-medium text-gray-700 mb-2">Invalidar Sessões por Usuário</h3>
            <form method="POST" class="flex gap-2">
              <input type="hidden" name="action" value="invalidate_user_sessions" />
              <input 
                type="text" 
                name="user_id" 
                placeholder="ID do usuário" 
                class="flex-1 border border-gray-300 rounded-md px-3 py-2"
                required
              />
              <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md"
              >
                Invalidar
              </button>
            </form>
          </div>
          
          <div>
            <h3 class="font-medium text-gray-700 mb-2">Invalidar Sessões por IP</h3>
            <form method="POST" class="flex gap-2">
              <input type="hidden" name="action" value="invalidate_ip_sessions" />
              <input 
                type="text" 
                name="ip_address" 
                placeholder="Endereço IP" 
                class="flex-1 border border-gray-300 rounded-md px-3 py-2"
                required
              />
              <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md"
              >
                Invalidar
              </button>
            </form>
          </div>
          
          <div>
            <h3 class="font-medium text-gray-700 mb-2">Invalidar Todas as Sessões</h3>
            <form method="POST" class="space-y-2">
              <input type="hidden" name="action" value="invalidate_all_sessions" />
              <div class="flex gap-2">
                <input 
                  type="text" 
                  name="confirmation" 
                  placeholder="Digite INVALIDATE_ALL para confirmar" 
                  class="flex-1 border border-gray-300 rounded-md px-3 py-2"
                  required
                />
                <button 
                  type="submit" 
                  class="bg-red-600 hover:bg-red-700 text-white py-2 px-4 rounded-md"
                >
                  Invalidar Todas
                </button>
              </div>
              <p class="text-xs text-red-600">
                Atenção: Esta ação irá desconectar todos os usuários do sistema. Use apenas em caso de emergência.
              </p>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>
</AdminLayout>

<script>
  // Atualizar a página a cada 30 segundos para manter os dados atualizados
  setTimeout(() => {
    window.location.reload();
  }, 30000);
</script>
