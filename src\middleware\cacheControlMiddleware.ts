/**
 * Middleware de controle de cache
 *
 * Este middleware configura cabeçalhos de cache para recursos estáticos
 * e dinâmicos, melhorando a performance e reduzindo a carga no servidor.
 */

import { defineMiddleware } from 'astro:middleware';
import { logger } from '@utils/logger';

/**
 * Configurações de cache por tipo de recurso
 */
const CACHE_CONFIGS = {
  // Assets com hash no nome (cache longo)
  immutable: {
    // 1 ano
    maxAge: 60 * 60 * 24 * 365,
    // Permitir cache compartilhado (CDN, proxies)
    public: true,
    // Não revalidar
    immutable: true,
    // Padrões para recursos com hash
    patterns: [
      /\/assets\/.*\.[a-f0-9]{8}\.(js|css|woff|woff2|ttf|otf|eot)$/,
      /\/images\/optimized\/.*\-[a-f0-9]{8}\.(jpg|jpeg|png|gif|webp|avif|svg)$/,
      /\/fonts\/optimized\/.*\-[a-f0-9]{8}\.(woff|woff2|ttf|otf|eot)$/,
    ],
  },

  // Recursos estáticos (CSS, JS, imagens, fontes)
  static: {
    // 1 semana
    maxAge: 60 * 60 * 24 * 7,
    // Permitir cache compartilhado (CDN, proxies)
    public: true,
    // Permitir stale-while-revalidate por 1 dia
    staleWhileRevalidate: 60 * 60 * 24,
    // Extensões de arquivo para recursos estáticos
    extensions: [
      '.css',
      '.js',
      '.mjs',
      '.jpg',
      '.jpeg',
      '.png',
      '.gif',
      '.webp',
      '.avif',
      '.svg',
      '.woff',
      '.woff2',
      '.ttf',
      '.otf',
      '.eot',
      '.ico',
    ],
    // Padrões para recursos estáticos
    patterns: [/\/assets\//, /\/static\//, /\/images\//, /\/fonts\//],
  },

  // Recursos dinâmicos (HTML, JSON, XML)
  dynamic: {
    // 5 minutos
    maxAge: 60 * 5,
    // Permitir stale-while-revalidate por 1 hora
    staleWhileRevalidate: 60 * 60,
    // Não permitir cache compartilhado
    public: false,
    // Extensões de arquivo para recursos dinâmicos
    extensions: ['.html', '.json', '.xml'],
    // Padrões para recursos dinâmicos
    patterns: [/\.html$/, /\.json$/, /\.xml$/, /\/sitemap\.xml$/, /\/manifest\.webmanifest$/],
  },

  // API e endpoints dinâmicos
  api: {
    // Sem cache ou cache muito curto (10 segundos)
    maxAge: 10,
    // Não permitir cache compartilhado
    public: false,
    // Padrões para API e endpoints dinâmicos
    patterns: [/\/api\//, /\/auth\//, /\/dashboard\//],
  },

  // Recursos que não devem ser cacheados
  noCache: {
    // Extensões de arquivo para recursos que não devem ser cacheados
    extensions: ['.php', '.asp', '.aspx', '.jsp'],
    // Padrões para recursos que não devem ser cacheados
    patterns: [/\/admin\//, /\/checkout\//, /\/payment\//],
  },
};

/**
 * Determina o tipo de recurso com base na URL
 * @param url - URL do recurso
 * @returns Tipo de recurso
 */
function getResourceType(url: URL): 'immutable' | 'static' | 'dynamic' | 'api' | 'noCache' {
  const path = url.pathname;

  // Verificar padrões para recursos imutáveis (com hash)
  if (CACHE_CONFIGS.immutable.patterns.some((pattern) => pattern.test(path))) {
    return 'immutable';
  }

  // Verificar padrões para recursos que não devem ser cacheados
  if (CACHE_CONFIGS.noCache.patterns.some((pattern) => pattern.test(path))) {
    return 'noCache';
  }

  // Verificar extensões para recursos que não devem ser cacheados
  if (CACHE_CONFIGS.noCache.extensions.some((ext) => path.endsWith(ext))) {
    return 'noCache';
  }

  // Verificar padrões para API e endpoints dinâmicos
  if (CACHE_CONFIGS.api.patterns.some((pattern) => pattern.test(path))) {
    return 'api';
  }

  // Verificar padrões para recursos estáticos
  if (CACHE_CONFIGS.static.patterns.some((pattern) => pattern.test(path))) {
    return 'static';
  }

  // Verificar extensões para recursos estáticos
  if (CACHE_CONFIGS.static.extensions.some((ext) => path.endsWith(ext))) {
    return 'static';
  }

  // Verificar padrões para recursos dinâmicos
  if (CACHE_CONFIGS.dynamic.patterns.some((pattern) => pattern.test(path))) {
    return 'dynamic';
  }

  // Verificar extensões para recursos dinâmicos
  if (CACHE_CONFIGS.dynamic.extensions.some((ext) => path.endsWith(ext))) {
    return 'dynamic';
  }

  // Padrão para recursos sem extensão
  return 'dynamic';
}

/**
 * Gera um valor de ETag para uma resposta
 * @param response - Resposta HTTP
 * @returns Valor de ETag
 */
async function generateETag(response: Response): Promise<string> {
  try {
    // Clonar resposta para não consumir o corpo original
    const clonedResponse = response.clone();

    // Obter corpo como texto
    const body = await clonedResponse.text();

    // Gerar hash simples
    let hash = 0;
    for (let i = 0; i < body.length; i++) {
      const char = body.charCodeAt(i);
      hash = (hash << 5) - hash + char;
      hash = hash & hash; // Converter para inteiro de 32 bits
    }

    // Converter para string hexadecimal
    const etag = hash.toString(16);

    return `"${etag}"`;
  } catch (error) {
    // Em caso de erro, gerar ETag baseado no timestamp
    return `"${Date.now().toString(16)}"`;
  }
}

/**
 * Middleware de controle de cache
 */
export const onRequest = defineMiddleware(async (context, next) => {
  // Executar próximo middleware para obter a resposta
  const response = await next();

  try {
    // Verificar se a resposta já tem cabeçalhos de cache
    if (response.headers.has('cache-control')) {
      return response;
    }

    // Determinar tipo de recurso
    const resourceType = getResourceType(new URL(context.request.url));

    // Configurar cabeçalhos de cache com base no tipo de recurso
    if (resourceType === 'immutable') {
      // Recursos imutáveis (com hash): cache muito longo
      response.headers.set(
        'cache-control',
        `max-age=${CACHE_CONFIGS.immutable.maxAge}, ${CACHE_CONFIGS.immutable.public ? 'public' : 'private'}, immutable`
      );

      // Adicionar cabeçalho de expiração
      const expirationDate = new Date();
      expirationDate.setSeconds(expirationDate.getSeconds() + CACHE_CONFIGS.immutable.maxAge);
      response.headers.set('expires', expirationDate.toUTCString());

      // Adicionar cabeçalho de variação
      response.headers.set('vary', 'Accept-Encoding');
    } else if (resourceType === 'static') {
      // Recursos estáticos: cache longo com stale-while-revalidate
      let cacheControl = `max-age=${CACHE_CONFIGS.static.maxAge}, ${CACHE_CONFIGS.static.public ? 'public' : 'private'}`;

      // Adicionar stale-while-revalidate se configurado
      if (CACHE_CONFIGS.static.staleWhileRevalidate) {
        cacheControl += `, stale-while-revalidate=${CACHE_CONFIGS.static.staleWhileRevalidate}`;
      }

      response.headers.set('cache-control', cacheControl);

      // Adicionar cabeçalho de expiração
      const expirationDate = new Date();
      expirationDate.setSeconds(expirationDate.getSeconds() + CACHE_CONFIGS.static.maxAge);
      response.headers.set('expires', expirationDate.toUTCString());

      // Adicionar cabeçalho de variação
      response.headers.set('vary', 'Accept-Encoding');

      // Adicionar ETag para validação
      const etag = await generateETag(response);
      response.headers.set('etag', etag);

      // Verificar se o cliente enviou um ETag que corresponde
      const ifNoneMatch = context.request.headers.get('if-none-match');
      if (ifNoneMatch === etag) {
        // Retornar 304 Not Modified
        return new Response(null, {
          status: 304,
          headers: response.headers,
        });
      }
    } else if (resourceType === 'dynamic') {
      // Recursos dinâmicos: cache curto com stale-while-revalidate
      let cacheControl = `max-age=${CACHE_CONFIGS.dynamic.maxAge}, ${CACHE_CONFIGS.dynamic.public ? 'public' : 'private'}, must-revalidate`;

      // Adicionar stale-while-revalidate se configurado
      if (CACHE_CONFIGS.dynamic.staleWhileRevalidate) {
        cacheControl += `, stale-while-revalidate=${CACHE_CONFIGS.dynamic.staleWhileRevalidate}`;
      }

      response.headers.set('cache-control', cacheControl);

      // Adicionar cabeçalho de variação
      response.headers.set('vary', 'Accept-Encoding, Cookie');

      // Adicionar ETag para validação
      const etag = await generateETag(response);
      response.headers.set('etag', etag);

      // Verificar se o cliente enviou um ETag que corresponde
      const ifNoneMatch = context.request.headers.get('if-none-match');
      if (ifNoneMatch === etag) {
        // Retornar 304 Not Modified
        return new Response(null, {
          status: 304,
          headers: response.headers,
        });
      }
    } else if (resourceType === 'api') {
      // API e endpoints dinâmicos: cache muito curto
      response.headers.set(
        'cache-control',
        `max-age=${CACHE_CONFIGS.api.maxAge}, ${CACHE_CONFIGS.api.public ? 'public' : 'private'}, must-revalidate`
      );

      // Adicionar cabeçalho de variação
      response.headers.set('vary', 'Accept-Encoding, Cookie, Authorization');

      // Adicionar ETag para validação
      const etag = await generateETag(response);
      response.headers.set('etag', etag);
    } else {
      // Recursos que não devem ser cacheados
      response.headers.set(
        'cache-control',
        'no-store, no-cache, must-revalidate, proxy-revalidate'
      );
      response.headers.set('pragma', 'no-cache');
      response.headers.set('expires', '0');
      response.headers.set('surrogate-control', 'no-store');
    }

    // Adicionar cabeçalho de data
    response.headers.set('date', new Date().toUTCString());

    return response;
  } catch (error) {
    // Em caso de erro, retornar resposta original
    logger.error('Erro ao configurar cabeçalhos de cache:', error);
    return response;
  }
});
