/**
 * Serviço de pipeline de eventos
 *
 * Este serviço implementa o conceito de pipelines para processamento de eventos,
 * permitindo a definição de etapas sequenciais de processamento.
 */

import { logger } from '@utils/logger';
import { v4 as uuidv4 } from 'uuid';
import { cacheService } from './cacheService';
import { kafkaLoggingService } from './kafka-logging.service';
import { type ProcessingContext } from './messageProcessingService';
import { messageTransformationService } from './messageTransformationService';
import { schemaValidationService } from './schemaValidationService';

/**
 * Interface para etapa de pipeline
 */
export interface PipelineStep<T = Record<string, unknown>, R = Record<string, unknown>> {
  /**
   * Nome da etapa
   */
  name: string;

  /**
   * Processa a mensagem
   * @param message - Mensagem a ser processada
   * @param context - Contexto de processamento
   * @returns Mensagem processada
   */
  process(message: T, context: PipelineContext): Promise<R>;

  /**
   * Manipula erros na etapa
   * @param error - Erro ocorrido
   * @param message - Mensagem que causou o erro
   * @param context - Contexto de processamento
   * @returns Mensagem processada ou rethrow do erro
   */
  handleError?(error: Error, message: T, context: PipelineContext): Promise<R>;
}

/**
 * Interface para contexto de pipeline
 */
export interface PipelineContext extends ProcessingContext {
  /**
   * ID do pipeline
   */
  pipelineId: string;

  /**
   * Timestamp de início do processamento
   */
  startTime: number;

  /**
   * Métricas de processamento
   */
  metrics: {
    /**
     * Duração de cada etapa em ms
     */
    stepDurations: Record<string, number>;

    /**
     * Etapa atual
     */
    currentStep: string;

    /**
     * Etapas concluídas
     */
    completedSteps: string[];

    /**
     * Etapas com erro
     */
    errorSteps: string[];
  };

  /**
   * Dados adicionais do contexto
   */
  data: Record<string, unknown>;
}

/**
 * Interface para configuração de pipeline
 */
export interface PipelineConfig {
  /**
   * Nome do pipeline
   */
  name: string;

  /**
   * Descrição do pipeline
   */
  description?: string;

  /**
   * Etapas do pipeline
   */
  steps: PipelineStep[];

  /**
   * Tempo limite de execução em ms
   */
  timeoutMs?: number;

  /**
   * Se deve continuar processando em caso de erro
   */
  continueOnError?: boolean;

  /**
   * Se deve validar o esquema da mensagem
   */
  validateSchema?: boolean;

  /**
   * Nome do esquema para validação
   */
  schemaName?: string;

  /**
   * Se deve transformar a mensagem
   */
  transformMessage?: boolean;

  /**
   * Nome do transformador
   */
  transformerName?: string;

  /**
   * Se deve armazenar métricas
   */
  storeMetrics?: boolean;

  /**
   * TTL para métricas em segundos
   */
  metricsTtl?: number;
}

/**
 * Serviço de pipeline de eventos
 */
export const eventPipelineService = {
  /**
   * Pipelines registrados
   */
  pipelines: new Map<string, PipelineConfig>(),

  /**
   * Registra um pipeline
   * @param config - Configuração do pipeline
   */
  registerPipeline(config: PipelineConfig): void {
    this.pipelines.set(config.name, config);
    logger.info(`Pipeline registrado: ${config.name}`);
  },

  /**
   * Obtém um pipeline pelo nome
   * @param name - Nome do pipeline
   * @returns Configuração do pipeline
   */
  getPipeline(name: string): PipelineConfig | undefined {
    return this.pipelines.get(name);
  },

  /**
   * Cria um contexto de pipeline
   * @param messageContext - Contexto da mensagem
   * @param pipelineConfig - Configuração do pipeline
   * @returns Contexto de pipeline
   */
  createContext(
    messageContext: ProcessingContext,
    pipelineConfig: PipelineConfig
  ): PipelineContext {
    return {
      ...messageContext,
      pipelineId: uuidv4(),
      startTime: Date.now(),
      metrics: {
        stepDurations: {},
        currentStep: '',
        completedSteps: [],
        errorSteps: [],
      },
      data: {},
    };
  },

  /**
   * Executa um pipeline
   * @param pipelineName - Nome do pipeline
   * @param message - Mensagem a ser processada
   * @param messageContext - Contexto da mensagem
   * @returns Mensagem processada
   */
  async executePipeline<T = Record<string, unknown>, R = Record<string, unknown>>(
    pipelineName: string,
    message: T,
    messageContext: ProcessingContext
  ): Promise<R> {
    // Obter configuração do pipeline
    const pipelineConfig = this.getPipeline(pipelineName);
    if (!pipelineConfig) {
      throw new Error(`Pipeline não encontrado: ${pipelineName}`);
    }

    // Criar contexto de pipeline
    const context = this.createContext(messageContext, pipelineConfig);

    // Registrar início do pipeline
    logger.info(`Iniciando pipeline: ${pipelineName}`, {
      pipelineId: context.pipelineId,
      messageId: context.messageId,
    });

    kafkaLoggingService.info('pipeline', `Iniciando pipeline ${pipelineName}`, {
      pipelineId: context.pipelineId,
      messageId: context.messageId,
    });

    // Definir timeout se configurado
    let timeoutId: NodeJS.Timeout | undefined;
    const timeoutPromise = new Promise<never>((_, reject) => {
      if (pipelineConfig.timeoutMs) {
        timeoutId = setTimeout(() => {
          reject(new Error(`Pipeline timeout após ${pipelineConfig.timeoutMs}ms`));
        }, pipelineConfig.timeoutMs);
      }
    });

    try {
      // Validar esquema se configurado
      let processedMessage: T | R = message;
      if (pipelineConfig.validateSchema && pipelineConfig.schemaName) {
        const validator = await schemaValidationService.getValidator(pipelineConfig.schemaName);
        if (validator) {
          const isValid = validator.validate(message);
          if (!isValid) {
            throw new Error(
              `Validação de esquema falhou para ${pipelineConfig.schemaName}: ${JSON.stringify(
                validator.errors
              )}`
            );
          }
        }
      }

      // Transformar mensagem se configurado
      if (pipelineConfig.transformMessage && pipelineConfig.transformerName) {
        const transformer = await messageTransformationService.getTransformer(
          pipelineConfig.transformerName
        );
        if (transformer) {
          processedMessage = transformer.transform(message);
        }
      }

      // Executar etapas do pipeline
      for (const step of pipelineConfig.steps) {
        context.metrics.currentStep = step.name;
        const stepStartTime = Date.now();

        try {
          // Executar etapa com timeout
          processedMessage = await Promise.race([
            step.process(processedMessage, context),
            timeoutPromise,
          ]);

          // Registrar duração da etapa
          const stepDuration = Date.now() - stepStartTime;
          context.metrics.stepDurations[step.name] = stepDuration;
          context.metrics.completedSteps.push(step.name);

          logger.debug(`Etapa ${step.name} concluída em ${stepDuration}ms`, {
            pipelineId: context.pipelineId,
            messageId: context.messageId,
          });
        } catch (error) {
          // Registrar erro
          context.metrics.errorSteps.push(step.name);
          logger.error(`Erro na etapa ${step.name}:`, error);

          kafkaLoggingService.error(
            'pipeline',
            `Erro na etapa ${step.name} do pipeline ${pipelineName}`,
            error
          );

          // Tratar erro se handler disponível
          if (step.handleError) {
            processedMessage = await step.handleError(error as Error, processedMessage, context);
          } else if (!pipelineConfig.continueOnError) {
            throw error;
          }
        }
      }

      // Registrar métricas se configurado
      if (pipelineConfig.storeMetrics) {
        const pipelineDuration = Date.now() - context.startTime;
        const metricsKey = `pipeline:metrics:${pipelineName}:${context.pipelineId}`;

        await cacheService.set(
          metricsKey,
          JSON.stringify({
            pipelineId: context.pipelineId,
            messageId: context.messageId,
            pipelineName,
            duration: pipelineDuration,
            startTime: context.startTime,
            endTime: Date.now(),
            steps: context.metrics.completedSteps,
            errorSteps: context.metrics.errorSteps,
            stepDurations: context.metrics.stepDurations,
          }),
          pipelineConfig.metricsTtl || 86400 // 24 horas por padrão
        );
      }

      // Registrar conclusão do pipeline
      const pipelineDuration = Date.now() - context.startTime;
      logger.info(`Pipeline ${pipelineName} concluído em ${pipelineDuration}ms`, {
        pipelineId: context.pipelineId,
        messageId: context.messageId,
      });

      kafkaLoggingService.info(
        'pipeline',
        `Pipeline ${pipelineName} concluído em ${pipelineDuration}ms`,
        {
          pipelineId: context.pipelineId,
          messageId: context.messageId,
        }
      );

      return processedMessage as R;
    } finally {
      // Limpar timeout se definido
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
    }
  },
};
