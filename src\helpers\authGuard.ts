import type { APIContext } from 'astro';

export async function checkAuth(context: APIContext) {
  const { cookies, redirect } = context;
  const sessionToken = cookies.get('session')?.value;

  if (!sessionToken) {
    return redirect('/signin');
  }

  return null;
}

export async function checkAdmin(context: APIContext) {
  const authRedirect = await checkAuth(context);
  if (authRedirect) return authRedirect;

  const { locals, redirect } = context;

  // Verificar se o usuário tem permissão de admin
  if (!locals.user || !locals.user.userType) {
    return redirect('/signin');
  }

  return null;
}

export async function checkTeacher(context: APIContext) {
  const authRedirect = await checkAuth(context);
  if (authRedirect) return authRedirect;

  const { locals, redirect } = context;

  // Verificar se o usuário é professor
  if (!locals.user || !locals.user.isTeacher) {
    return redirect('/signin');
  }

  return null;
}
