/**
 * API de Validação de Dados Fiscais
 *
 * Endpoint para validar dados fiscais.
 * Parte da implementação da tarefa 8.7.1 - Integração fiscal
 */

import type { APIRoute } from 'astro';
import { ValidateFiscalDataUseCase } from '../../../domain/usecases/fiscal/ValidateFiscalDataUseCase';

// Inicializar caso de uso
const validateFiscalDataUseCase = new ValidateFiscalDataUseCase();

export const POST: APIRoute = async ({ request }) => {
  try {
    // Obter dados da requisição
    const body = await request.json();
    const { documentType, customer, items } = body;

    // Validar dados básicos
    if (!documentType) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Tipo de documento é obrigatório.',
        }),
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    // Validar dados fiscais
    const result = await validateFiscalDataUseCase.execute({
      documentType,
      customer,
      items,
    });

    if (result.success) {
      return new Response(
        JSON.stringify({
          success: true,
          isValid: result.isValid,
          validations: result.validations,
        }),
        {
          status: 200,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }
    return new Response(
      JSON.stringify({
        success: false,
        error: result.error || 'Erro desconhecido ao validar dados fiscais.',
      }),
      {
        status: 400,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  } catch (error) {
    console.error('Erro ao processar validação de dados fiscais:', error);

    return new Response(
      JSON.stringify({
        success: false,
        error:
          'Ocorreu um erro ao processar a validação dos dados fiscais. Por favor, tente novamente.',
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  }
};
