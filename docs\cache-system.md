# Sistema de Cache

## Visão Geral

Este documento descreve a arquitetura e implementação do sistema de cache da plataforma Estação da Alfabetização. O sistema utiliza o Valkey (fork do Redis) como mecanismo de armazenamento principal, complementado por estratégias avançadas de cache em múltiplas camadas.

## Arquitetura do Sistema de Cache

O sistema de cache é composto por várias camadas e componentes que trabalham em conjunto para otimizar a performance e a eficiência do acesso a dados:

### Camadas de Cache

1. **Cache em Memória**: Armazena dados frequentemente acessados diretamente na memória da aplicação, utilizando um algoritmo LRU (Least Recently Used) para gerenciar o espaço.

2. **Cache Distribuído**: Implementado com Valkey, permite compartilhar dados em cache entre múltiplas instâncias da aplicação.

3. **Cache Estratégico**: Implementa diferentes estratégias de cache adaptadas a diferentes tipos de dados e padrões de acesso.

### Componentes Principais

- **cacheService**: Serviço base que interage diretamente com o Valkey.
- **layeredCacheService**: Implementa cache em múltiplas camadas (memória + distribuído).
- **strategicCacheService**: Implementa estratégias avançadas de cache.
- **entityCacheService**: Especializado em cache de entidades do domínio.
- **queryCacheService**: Especializado em cache de resultados de consultas.

## Estratégias de Cache

O sistema implementa várias estratégias de cache para diferentes cenários:

### Standard

Cache padrão com TTL (Time-To-Live) fixo. Ideal para dados que mudam com frequência previsível.

```typescript
// Exemplo de uso
const data = await strategicCacheService.get('query', { query: 'SELECT * FROM products' }, fetchDataFn);
```

### Stale-While-Revalidate

Retorna dados expirados enquanto atualiza o cache em background. Ideal para dados que podem ser ligeiramente desatualizados sem impacto significativo.

```typescript
// Configuração
{
  strategy: CacheStrategy.STALE_WHILE_REVALIDATE,
  defaultTTL: 3600, // 1 hora
  staleTTL: 86400 // 24 horas
}
```

### Event-Based

Cache com invalidação baseada em eventos. Ideal para dados que mudam em resposta a ações específicas.

```typescript
// Emitir evento para invalidar cache
strategicCacheService.emitInvalidationEvent('product:update', { id: '123' });
```

### User-Aware

Cache com variação por usuário. Ideal para dados personalizados.

```typescript
// Obter dados específicos do usuário
const userData = await strategicCacheService.get('user', { userId: '123' }, fetchUserDataFn);
```

### Priority-Based

Cache com prioridade para evicção. Itens com maior prioridade são mantidos por mais tempo quando o espaço é limitado.

```typescript
// Configuração
{
  strategy: CacheStrategy.PRIORITY_BASED,
  priority: CachePriority.HIGH
}
```

## Configuração por Tipo de Dados

O sistema utiliza configurações específicas para diferentes tipos de dados:

### Entidades

```typescript
// Exemplo de configuração para usuários
{
  ttl: 1800, // 30 minutos
  refreshOnAccess: true,
  tags: ['user', 'auth'],
  priority: 8,
  useMemoryCache: true,
  compress: false,
  invalidationEvents: ['user:update', 'user:delete', 'user:logout']
}
```

### Consultas

```typescript
// Exemplo de configuração para resultados de consulta
{
  keyPrefix: 'query',
  defaultTTL: 300, // 5 minutos
  strategy: CacheStrategy.STANDARD,
  keyGenerator: (params) => {
    const { query, filters, page, limit } = params;
    return `${query}:${JSON.stringify(filters || {})}:${page || 1}:${limit || 20}`;
  }
}
```

## Compressão de Dados

O sistema suporta compressão automática para reduzir o uso de memória:

```typescript
// Configuração de compressão
compression: {
  enabled: true,
  threshold: 1024, // Comprimir apenas dados maiores que 1KB
  level: 6 // Nível de compressão (0-9)
}
```

## Invalidação de Cache

O sistema oferece vários métodos para invalidação de cache:

### Invalidação por Chave

```typescript
// Invalidar uma entidade específica
await entityCacheService.invalidate(EntityType.PRODUCT, '123');
```

### Invalidação por Padrão

```typescript
// Invalidar todas as entidades de um tipo
await entityCacheService.invalidate(EntityType.PRODUCT, '*');
```

### Invalidação por Tag

```typescript
// Invalidar todos os itens com uma tag específica
await layeredCacheService.invalidateByTag('catalog');
```

### Invalidação por Evento

```typescript
// Emitir evento para invalidar cache
entityCacheService.emitInvalidationEvent('product:update', { id: '123' });
```

## Warm-up de Cache

O sistema implementa estratégias de warm-up para pré-carregar dados frequentemente acessados:

```typescript
// Pré-carregar produtos populares
await entityCacheService.preload(
  EntityType.PRODUCT,
  async () => await productRepository.findPopular(100),
  100
);
```

## Monitoramento e Métricas

O sistema coleta métricas para monitoramento de performance:

```typescript
// Obter estatísticas do cache
const stats = layeredCacheService.getStats();
console.log(`Taxa de acertos: ${stats.metrics.hitRate.overall * 100}%`);
```

Métricas coletadas incluem:
- Taxa de acertos (hit rate)
- Número de itens em cache
- Tamanho do cache
- Tempo médio de acesso
- Distribuição de tipos de dados

## Integração com Repositórios

O sistema de cache é integrado com os repositórios através de decoradores:

```typescript
// Exemplo de repositório com cache
export class CachedProductRepository implements ProductRepository {
  constructor(
    private repository: ProductRepository,
    private cacheService: CacheService,
    private cacheTtl: number = 300
  ) {}

  async findById(id: string): Promise<Product | null> {
    const cacheKey = `product:${id}`;
    const cachedProduct = await this.cacheService.get<Product>(cacheKey);
    
    if (cachedProduct) {
      return cachedProduct;
    }
    
    const product = await this.repository.findById(id);
    
    if (product) {
      await this.cacheService.set(cacheKey, product, this.cacheTtl);
    }
    
    return product;
  }
}
```

## Considerações de Segurança

O sistema implementa várias medidas de segurança:

- **Autenticação**: Configuração de usuário e senha para acesso ao Valkey
- **TLS/SSL**: Comunicação criptografada entre a aplicação e o Valkey
- **Políticas de Acesso**: Controle de comandos permitidos por usuário
- **Sanitização de Dados**: Validação de dados antes de armazenar em cache

## Boas Práticas

### Quando Usar Cache

- Dados que são frequentemente lidos e raramente modificados
- Resultados de consultas computacionalmente intensivas
- Dados compartilhados entre múltiplos usuários
- Dados que podem ser ligeiramente desatualizados sem impacto significativo

### Quando Evitar Cache

- Dados que mudam frequentemente
- Dados críticos que precisam estar sempre atualizados
- Dados sensíveis que requerem controle de acesso rigoroso
- Dados muito grandes que consumiriam muito espaço em cache

### Dicas de Implementação

1. **Chaves de Cache**: Use chaves descritivas e estruturadas
2. **TTL Apropriado**: Configure TTL adequado para cada tipo de dado
3. **Invalidação Eficiente**: Prefira invalidação baseada em eventos
4. **Monitoramento**: Acompanhe métricas de performance regularmente
5. **Fallback**: Sempre tenha um plano para quando o cache falhar

## Exemplos de Uso

### Cache de Entidade

```typescript
// Obter usuário do cache
const user = await entityCacheService.get(EntityType.USER, userId);

if (!user) {
  // Buscar do banco de dados
  const user = await userRepository.findById(userId);
  
  // Armazenar no cache
  await entityCacheService.set(EntityType.USER, userId, user);
}
```

### Cache de Consulta

```typescript
// Obter resultados de consulta do cache
const results = await strategicCacheService.get(
  'query',
  { query: 'products', filters: { category: 'books' }, page: 1 },
  async () => await productRepository.findByCategory('books', 1)
);
```

### Cache em Camadas

```typescript
// Obter dados do cache em camadas
const data = await layeredCacheService.get('key', {
  memoryTTL: 60, // 1 minuto em memória
  distributedTTL: 3600, // 1 hora no Valkey
  compress: true
});
```

## Referências

- [Documentação do Valkey](https://valkey.io/docs)
- [Padrões de Cache](https://caching.readthedocs.io/en/latest/cache-patterns.html)
- [Estratégia Stale-While-Revalidate](https://web.dev/stale-while-revalidate/)
- [Cache Invalidation Strategies](https://www.mnot.net/cache_docs/)
