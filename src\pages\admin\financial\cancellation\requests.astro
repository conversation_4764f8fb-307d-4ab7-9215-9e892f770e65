---
import FinancialNav from '@components/navigation/FinancialNav.astro';
import { checkAdmin } from '@helpers/authGuard';
import AdminLayout from '@layouts/AdminLayout.astro';
import { cancellationPolicyService } from '@services/cancellationPolicyService';

// Protege a rota verificando se o usuário é admin
const authRedirect = await checkAdmin(Astro);
if (authRedirect) return authRedirect;

// Obter solicitações de cancelamento pendentes
const pendingRequests = await cancellationPolicyService.getPendingCancellations(20);

// Formatar data
const formatDate = (dateString) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  return `${date.toLocaleDateString('pt-BR')} ${date.toLocaleTimeString('pt-BR')}`;
};

// Formatar valor monetário
const formatCurrency = (value) => {
  return new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: 'BRL',
  }).format(value || 0);
};

// Formatar motivo de cancelamento
const formatReason = (reason) => {
  const reasons = {
    customer_request: 'Solicitação do Cliente',
    payment_issue: 'Problema de Pagamento',
    product_unavailable: 'Produto Indisponível',
    duplicate_order: 'Pedido Duplicado',
    fraud_suspicion: 'Suspeita de Fraude',
    other: 'Outro',
  };
  return reasons[reason] || reason;
};

// Verificar se há parâmetro de sucesso na URL
const success = Astro.url.searchParams.get('success');
const error = Astro.url.searchParams.get('error');
---

<AdminLayout title="Solicitações de Cancelamento">
  <div class="container mx-auto p-4">
    <h1 class="text-2xl font-bold mb-4">Gestão Financeira</h1>
    
    <FinancialNav />
    
    <div class="flex items-center justify-between mb-6">
      <h2 class="text-xl font-bold">Solicitações de Cancelamento</h2>
      <a href="/admin/financial/cancellation/policies" class="btn btn-outline">
        Gerenciar Políticas
      </a>
    </div>
    
    {success && (
      <div class="alert alert-success mb-4">
        <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
        <span>Operação realizada com sucesso!</span>
      </div>
    )}
    
    {error && (
      <div class="alert alert-error mb-4">
        <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
        <span>Erro: {error}</span>
      </div>
    )}
    
    <!-- Solicitações Pendentes -->
    <div class="card bg-base-100 shadow-xl mb-6">
      <div class="card-body">
        <h2 class="card-title">Solicitações Pendentes</h2>
        <p class="text-sm mb-4">Solicitações de cancelamento que aguardam aprovação.</p>
        
        <div class="overflow-x-auto">
          <table class="table table-zebra w-full">
            <thead>
              <tr>
                <th>Data</th>
                <th>Cliente</th>
                <th>Pedido</th>
                <th>Valor</th>
                <th>Motivo</th>
                <th>Política</th>
                <th>Ações</th>
              </tr>
            </thead>
            <tbody>
              {pendingRequests.length === 0 ? (
                <tr>
                  <td colspan="7" class="text-center py-4">Nenhuma solicitação pendente</td>
                </tr>
              ) : (
                pendingRequests.map(request => (
                  <tr>
                    <td>{formatDate(request.created_at)}</td>
                    <td>
                      <div>{request.customer_name}</div>
                      <div class="text-xs opacity-60">{request.customer_email}</div>
                    </td>
                    <td>{request.ulid_order.substring(0, 8)}...</td>
                    <td>{formatCurrency(request.order_total)}</td>
                    <td>
                      <div class="badge badge-outline">
                        {formatReason(request.reason)}
                      </div>
                    </td>
                    <td>
                      <div>{request.policy_name}</div>
                      <div class="text-xs opacity-60">Reembolso: {request.refund_percentage}%</div>
                    </td>
                    <td>
                      <div class="flex gap-2">
                        <a href={`/admin/financial/cancellation/requests/${request.ulid_cancellation}`} class="btn btn-xs btn-primary">
                          Detalhes
                        </a>
                        <button 
                          class="btn btn-xs btn-success approve-btn"
                          data-cancellation-id={request.ulid_cancellation}
                        >
                          Aprovar
                        </button>
                        <button 
                          class="btn btn-xs btn-error reject-btn"
                          data-cancellation-id={request.ulid_cancellation}
                        >
                          Rejeitar
                        </button>
                      </div>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
        
        <div class="card-actions justify-end mt-4">
          <a href="/admin/financial/cancellation/history" class="btn btn-outline btn-sm">
            Ver Histórico Completo
          </a>
        </div>
      </div>
    </div>
  </div>
</AdminLayout>

<script>
  // Inicializar componentes interativos
  document.addEventListener('DOMContentLoaded', () => {
    // Botões de aprovação
    document.querySelectorAll('.approve-btn').forEach(button => {
      button.addEventListener('click', async (e) => {
        const target = e.currentTarget as HTMLButtonElement;
        const cancellationId = target.dataset.cancellationId;
        
        if (!cancellationId) return;
        
        // Confirmar ação
        if (!confirm('Tem certeza que deseja aprovar esta solicitação de cancelamento?')) {
          return;
        }
        
        try {
          // Enviar requisição para aprovar
          const response = await fetch(`/api/cancellations/${cancellationId}/process`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              action: 'approve',
            }),
          });
          
          if (!response.ok) {
            throw new Error('Erro ao aprovar solicitação');
          }
          
          // Recarregar página
          window.location.href = '/admin/financial/cancellation/requests?success=true';
        } catch (error) {
          console.error('Erro:', error);
          alert('Erro ao aprovar solicitação');
        }
      });
    });
    
    // Botões de rejeição
    document.querySelectorAll('.reject-btn').forEach(button => {
      button.addEventListener('click', async (e) => {
        const target = e.currentTarget as HTMLButtonElement;
        const cancellationId = target.dataset.cancellationId;
        
        if (!cancellationId) return;
        
        // Solicitar motivo da rejeição
        const reason = prompt('Informe o motivo da rejeição:');
        
        if (!reason) {
          alert('É necessário informar um motivo para rejeitar a solicitação.');
          return;
        }
        
        try {
          // Enviar requisição para rejeitar
          const response = await fetch(`/api/cancellations/${cancellationId}/process`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              action: 'reject',
              reason,
            }),
          });
          
          if (!response.ok) {
            throw new Error('Erro ao rejeitar solicitação');
          }
          
          // Recarregar página
          window.location.href = '/admin/financial/cancellation/requests?success=true';
        } catch (error) {
          console.error('Erro:', error);
          alert('Erro ao rejeitar solicitação');
        }
      });
    });
  });
</script>
