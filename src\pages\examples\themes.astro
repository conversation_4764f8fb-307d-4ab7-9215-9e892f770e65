---
import DaisyButton from '../../components/ui/DaisyButton.astro';
import DaisyCard from '../../components/ui/DaisyCard.astro';
import ThemeToggle from '../../components/ui/ThemeToggle.astro';
import Breadcrumbs from '../../components/ui/navigation/Breadcrumbs.astro';
import Tabs from '../../components/ui/navigation/Tabs.astro';
import BaseLayout from '../../layouts/BaseLayout.astro';

// Importar componentes de layout
import { Container, Section } from '../../layouts/grid';

// Importar sistema de temas
import { themes } from '../../layouts/themes';

const title = 'Sistema de Temas';

// Breadcrumbs para a página atual
const breadcrumbItems = [
  { href: '/', label: 'Início' },
  { href: '/examples', label: 'Exemplos' },
  { label: 'Temas' },
];

// Tabs para as categorias
const themeTabs = [
  { id: 'overview', label: 'Visão Geral', isActive: true },
  { id: 'components', label: 'Componentes' },
  { id: 'colors', label: 'Cores' },
  { id: 'usage', label: 'Uso' },
];
---

<BaseLayout title={title}>
  <Container>
    <Breadcrumbs items={breadcrumbItems} class="my-4" />

    <Section title={title} subtitle="Sistema de temas para personalização visual da aplicação">
      <div class="mb-8 flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <p>
          O sistema de temas permite personalizar a aparência visual da aplicação,
          oferecendo temas claros, escuros e personalizados.
        </p>

        <div class="flex gap-4">
          <ThemeToggle variant="button" showLabel />
          <ThemeToggle variant="dropdown" showLabel />
        </div>
      </div>

      <Tabs tabs={themeTabs}>
        <!-- Visão Geral -->
        <div slot="overview" class="py-4">
          <h2 class="text-2xl font-bold mb-4">Temas Disponíveis</h2>

          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
            <DaisyCard title="Tema Claro">
              <div class="p-4 rounded-box bg-base-100 border border-base-300 mb-2">
                <div class="flex flex-col gap-2">
                  <div class="h-4 w-full bg-primary rounded"></div>
                  <div class="h-4 w-3/4 bg-secondary rounded"></div>
                  <div class="h-4 w-1/2 bg-accent rounded"></div>
                </div>
              </div>
              <DaisyButton variant="outline" size="sm" class="theme-select-btn" data-theme="light">
                Selecionar
              </DaisyButton>
            </DaisyCard>

            <DaisyCard title="Tema Escuro">
              <div class="p-4 rounded-box bg-base-100 border border-base-300 mb-2 theme-dark">
                <div class="flex flex-col gap-2">
                  <div class="h-4 w-full bg-primary rounded"></div>
                  <div class="h-4 w-3/4 bg-secondary rounded"></div>
                  <div class="h-4 w-1/2 bg-accent rounded"></div>
                </div>
              </div>
              <DaisyButton variant="outline" size="sm" class="theme-select-btn" data-theme="dark">
                Selecionar
              </DaisyButton>
            </DaisyCard>

            <DaisyCard title="Tema Estação">
              <div class="p-4 rounded-box bg-base-100 border border-base-300 mb-2 theme-estacao">
                <div class="flex flex-col gap-2">
                  <div class="h-4 w-full bg-primary rounded"></div>
                  <div class="h-4 w-3/4 bg-secondary rounded"></div>
                  <div class="h-4 w-1/2 bg-accent rounded"></div>
                </div>
              </div>
              <DaisyButton variant="outline" size="sm" class="theme-select-btn" data-theme="estacao">
                Selecionar
              </DaisyButton>
            </DaisyCard>
          </div>

          <h2 class="text-2xl font-bold mb-4">Características</h2>

          <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <DaisyCard title="Persistência">
              <p>
                O tema selecionado é salvo no armazenamento local do navegador,
                permitindo que a preferência do usuário seja mantida entre sessões.
              </p>
            </DaisyCard>

            <DaisyCard title="Preferência do Sistema">
              <p>
                O sistema pode detectar automaticamente a preferência de tema do sistema
                operacional do usuário (claro ou escuro) e aplicar o tema correspondente.
              </p>
            </DaisyCard>

            <DaisyCard title="Transições Suaves">
              <p>
                As mudanças de tema incluem transições suaves para uma experiência
                visual agradável, com opção de desativar para usuários que preferem
                redução de movimento.
              </p>
            </DaisyCard>
          </div>
        </div>

        <!-- Componentes -->
        <div slot="components" class="py-4">
          <h2 class="text-2xl font-bold mb-4">Componentes com Temas</h2>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            <DaisyCard title="Botões">
              <div class="flex flex-wrap gap-2">
                <DaisyButton variant="primary">Primário</DaisyButton>
                <DaisyButton variant="secondary">Secundário</DaisyButton>
                <DaisyButton variant="accent">Acento</DaisyButton>
                <DaisyButton variant="ghost">Ghost</DaisyButton>
                <DaisyButton variant="link">Link</DaisyButton>
              </div>
            </DaisyCard>

            <DaisyCard title="Alertas">
              <div class="space-y-2">
                <div class="alert alert-info">
                  <span>Informação: Este é um alerta informativo.</span>
                </div>
                <div class="alert alert-success">
                  <span>Sucesso: Operação concluída com sucesso.</span>
                </div>
                <div class="alert alert-warning">
                  <span>Aviso: Atenção necessária.</span>
                </div>
                <div class="alert alert-error">
                  <span>Erro: Algo deu errado.</span>
                </div>
              </div>
            </DaisyCard>

            <DaisyCard title="Formulários">
              <div class="space-y-4">
                <div class="form-control">
                  <label class="label">
                    <span class="label-text">Nome</span>
                  </label>
                  <input type="text" placeholder="Digite seu nome" class="input input-bordered w-full" />
                </div>
                <div class="form-control">
                  <label class="label">
                    <span class="label-text">Tema Favorito</span>
                  </label>
                  <select class="select select-bordered w-full">
                    <option disabled selected>Selecione um tema</option>
                    <option>Claro</option>
                    <option>Escuro</option>
                    <option>Estação</option>
                  </select>
                </div>
                <div class="form-control">
                  <label class="label cursor-pointer">
                    <span class="label-text">Ativar modo escuro automaticamente</span>
                    <input type="checkbox" class="toggle toggle-primary" />
                  </label>
                </div>
              </div>
            </DaisyCard>

            <DaisyCard title="Badges">
              <div class="flex flex-wrap gap-2">
                <div class="badge">Neutro</div>
                <div class="badge badge-primary">Primário</div>
                <div class="badge badge-secondary">Secundário</div>
                <div class="badge badge-accent">Acento</div>
                <div class="badge badge-outline">Outline</div>
                <div class="badge badge-info">Info</div>
                <div class="badge badge-success">Sucesso</div>
                <div class="badge badge-warning">Aviso</div>
                <div class="badge badge-error">Erro</div>
              </div>
            </DaisyCard>
          </div>

          <h2 class="text-2xl font-bold mb-4">Alternadores de Tema</h2>

          <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <DaisyCard title="Ícone Simples">
              <div class="flex justify-center">
                <ThemeToggle variant="icon" />
              </div>
              <p class="mt-4 text-sm">
                Botão de ícone simples que alterna entre os temas disponíveis.
                Ideal para barras de navegação e espaços limitados.
              </p>
            </DaisyCard>

            <DaisyCard title="Botão com Texto">
              <div class="flex justify-center">
                <ThemeToggle variant="button" showLabel />
              </div>
              <p class="mt-4 text-sm">
                Botão com texto que indica o tema atual e permite alternar entre temas.
                Oferece melhor acessibilidade e clareza.
              </p>
            </DaisyCard>

            <DaisyCard title="Dropdown">
              <div class="flex justify-center">
                <ThemeToggle variant="dropdown" showLabel />
              </div>
              <p class="mt-4 text-sm">
                Menu dropdown que permite selecionar diretamente um tema específico.
                Ideal quando há vários temas disponíveis.
              </p>
            </DaisyCard>
          </div>
        </div>

        <!-- Cores -->
        <div slot="colors" class="py-4">
          <h2 class="text-2xl font-bold mb-4">Paleta de Cores</h2>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            <DaisyCard title="Cores Principais">
              <div class="space-y-4">
                <div class="flex items-center gap-4">
                  <div class="w-16 h-16 bg-primary rounded-box"></div>
                  <div>
                    <div class="font-bold">Primária</div>
                    <div class="text-sm opacity-70">--primary</div>
                  </div>
                </div>
                <div class="flex items-center gap-4">
                  <div class="w-16 h-16 bg-secondary rounded-box"></div>
                  <div>
                    <div class="font-bold">Secundária</div>
                    <div class="text-sm opacity-70">--secondary</div>
                  </div>
                </div>
                <div class="flex items-center gap-4">
                  <div class="w-16 h-16 bg-accent rounded-box"></div>
                  <div>
                    <div class="font-bold">Acento</div>
                    <div class="text-sm opacity-70">--accent</div>
                  </div>
                </div>
              </div>
            </DaisyCard>

            <DaisyCard title="Cores de Estado">
              <div class="space-y-4">
                <div class="flex items-center gap-4">
                  <div class="w-16 h-16 bg-info rounded-box"></div>
                  <div>
                    <div class="font-bold">Informação</div>
                    <div class="text-sm opacity-70">--info</div>
                  </div>
                </div>
                <div class="flex items-center gap-4">
                  <div class="w-16 h-16 bg-success rounded-box"></div>
                  <div>
                    <div class="font-bold">Sucesso</div>
                    <div class="text-sm opacity-70">--success</div>
                  </div>
                </div>
                <div class="flex items-center gap-4">
                  <div class="w-16 h-16 bg-warning rounded-box"></div>
                  <div>
                    <div class="font-bold">Aviso</div>
                    <div class="text-sm opacity-70">--warning</div>
                  </div>
                </div>
                <div class="flex items-center gap-4">
                  <div class="w-16 h-16 bg-error rounded-box"></div>
                  <div>
                    <div class="font-bold">Erro</div>
                    <div class="text-sm opacity-70">--error</div>
                  </div>
                </div>
              </div>
            </DaisyCard>
          </div>

          <h2 class="text-2xl font-bold mb-4">Cores de Base</h2>

          <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="p-6 bg-base-100 border border-base-300 rounded-box">
              <div class="font-bold">Base 100</div>
              <div class="text-sm opacity-70">--base-100</div>
              <div class="mt-4 text-base-content">Texto de conteúdo</div>
            </div>
            <div class="p-6 bg-base-200 border border-base-300 rounded-box">
              <div class="font-bold">Base 200</div>
              <div class="text-sm opacity-70">--base-200</div>
              <div class="mt-4 text-base-content">Texto de conteúdo</div>
            </div>
            <div class="p-6 bg-base-300 border border-base-300 rounded-box">
              <div class="font-bold">Base 300</div>
              <div class="text-sm opacity-70">--base-300</div>
              <div class="mt-4 text-base-content">Texto de conteúdo</div>
            </div>
            <div class="p-6 bg-neutral text-neutral-content rounded-box">
              <div class="font-bold">Neutro</div>
              <div class="text-sm opacity-70">--neutral</div>
              <div class="mt-4">Texto de conteúdo neutro</div>
            </div>
          </div>
        </div>

        <!-- Uso -->
        <div slot="usage" class="py-4">
          <h2 class="text-2xl font-bold mb-4">Como Usar</h2>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            <DaisyCard title="Provedor de Tema">
              <p class="mb-4">
                Adicione o <code>ThemeProvider</code> ao seu layout principal para habilitar o sistema de temas:
              </p>
              <pre class="bg-base-300 p-4 rounded-box overflow-x-auto text-sm"><code>---
// Em seu layout principal
import { ThemeProvider } from '../layouts/themes';
---

&lt;ThemeProvider defaultTheme="light" enableSystem={true}&gt;
  &lt;YourApp /&gt;
&lt;/ThemeProvider&gt;</code></pre>
            </DaisyCard>

            <DaisyCard title="Alternador de Tema">
              <p class="mb-4">
                Adicione o componente <code>ThemeToggle</code> à sua navegação:
              </p>
              <pre class="bg-base-300 p-4 rounded-box overflow-x-auto text-sm"><code>---
// Em seu componente de navegação
import ThemeToggle from '../components/ui/ThemeToggle.astro';
---

&lt;nav&gt;
  &lt;!-- Outros elementos de navegação --&gt;
  &lt;ThemeToggle variant="icon" /&gt;
&lt;/nav&gt;</code></pre>
            </DaisyCard>
          </div>

          <DaisyCard title="API JavaScript">
            <p class="mb-4">
              Utilize as funções utilitárias para interagir com o sistema de temas em seus componentes:
            </p>
            <pre class="bg-base-300 p-4 rounded-box overflow-x-auto text-sm"><code>// Em seu componente JavaScript
import { getTheme, setTheme, toggleTheme, isDarkTheme } from '../layouts/themes';

// Obter o tema atual
const currentTheme = getTheme();

// Definir um tema específico
setTheme('dark');

// Alternar para o próximo tema
const newTheme = toggleTheme();

// Verificar se o tema atual é escuro
if (isDarkTheme()) {
  // Fazer algo específico para temas escuros
}

// Ouvir mudanças de tema
const removeListener = onThemeChange((theme) => {
  console.log('Tema alterado para:', theme);
});

// Remover o ouvinte quando não for mais necessário
removeListener();</code></pre>
          </DaisyCard>

          <h2 class="text-2xl font-bold my-4">Personalização</h2>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            <DaisyCard title="Adicionar Novos Temas">
              <p>
                Para adicionar novos temas, edite o arquivo <code>src/layouts/themes/themes.css</code> e adicione
                suas definições de tema seguindo o padrão existente. Em seguida, atualize a lista de temas disponíveis
                no arquivo <code>src/layouts/themes/index.js</code>.
              </p>
            </DaisyCard>

            <DaisyCard title="Modificar Temas Existentes">
              <p>
                Para modificar temas existentes, edite as variáveis CSS no arquivo <code>src/layouts/themes/themes.css</code>.
                Cada tema é definido como um conjunto de variáveis CSS que são aplicadas quando a classe correspondente
                está presente no elemento raiz.
              </p>
            </DaisyCard>
          </div>
        </div>
      </Tabs>

      <div class="mt-12 p-6 bg-base-200 rounded-box">
        <h2 class="text-2xl font-bold mb-4">Importação</h2>

        <p class="mb-4">
          Para usar o sistema de temas, importe os componentes e utilitários necessários:
        </p>

        <pre class="bg-base-300 p-4 rounded-box overflow-x-auto text-sm"><code>// Componentes
import { ThemeProvider } from '../layouts/themes';
import ThemeToggle from '../components/ui/ThemeToggle.astro';

// Utilitários
import {
  themes,
  getTheme,
  setTheme,
  toggleTheme,
  isDarkTheme,
  onThemeChange
} from '../layouts/themes';</code></pre>
      </div>
    </Section>
  </Container>
</BaseLayout>

<script>
  // Adicionar funcionalidade aos botões de seleção de tema
  document.addEventListener('DOMContentLoaded', () => {
    const themeButtons = document.querySelectorAll('.theme-select-btn');

    themeButtons.forEach(button => {
      button.addEventListener('click', () => {
        const theme = button.getAttribute('data-theme');
        if (theme && window.themeProvider) {
          window.themeProvider.setTheme(theme);
        }
      });
    });
  });
</script>
