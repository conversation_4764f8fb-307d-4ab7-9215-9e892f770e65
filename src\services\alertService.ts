/**
 * Serviço para gerenciamento de alertas de transações financeiras
 */

import { queryHelper } from '@db/queryHelper';
import { email } from '@helpers/emailHelper';
import { logger } from '@utils/logger';

// Tipos de alertas disponíveis
export type AlertType =
  | 'high_value_transaction'
  | 'suspicious_activity'
  | 'payment_failure'
  | 'chargeback_risk'
  | 'payment_success'
  | 'refund_processed';

// Interface para configuração de alertas
export interface AlertConfig {
  type: AlertType;
  enabled: boolean;
  threshold?: number;
  recipients: string[];
  notifyCustomer: boolean;
  template: string;
}

// Interface para dados de alerta
export interface AlertData {
  orderId: string;
  customerId: string;
  customerName: string;
  customerEmail: string;
  orderTotal: number;
  paymentMethod?: string;
  paymentStatus?: string;
  transactionDate: Date;
  additionalInfo?: Record<string, any>;
}

// Serviço de alertas
export const alertService = {
  /**
   * Obtém todas as configurações de alertas
   * @returns Lista de configurações de alertas
   */
  async getAlertConfigs(): Promise<AlertConfig[]> {
    try {
      const result = await queryHelper.query('SELECT * FROM tab_alert_config WHERE active = true');

      return result.rows.map((row) => ({
        type: row.type as AlertType,
        enabled: row.enabled,
        threshold: row.threshold,
        recipients: row.recipients ? row.recipients.split(',') : [],
        notifyCustomer: row.notify_customer,
        template: row.template,
      }));
    } catch (error) {
      logger.error('Erro ao obter configurações de alertas:', error);
      return [];
    }
  },

  /**
   * Obtém uma configuração de alerta específica
   * @param type - Tipo de alerta
   * @returns Configuração do alerta ou null se não encontrado
   */
  async getAlertConfig(type: AlertType): Promise<AlertConfig | null> {
    try {
      const result = await queryHelper.query(
        'SELECT * FROM tab_alert_config WHERE type = $1 AND active = true',
        [type]
      );

      if (result.rows.length === 0) {
        return null;
      }

      const row = result.rows[0];

      return {
        type: row.type as AlertType,
        enabled: row.enabled,
        threshold: row.threshold,
        recipients: row.recipients ? row.recipients.split(',') : [],
        notifyCustomer: row.notify_customer,
        template: row.template,
      };
    } catch (error) {
      logger.error(`Erro ao obter configuração de alerta ${type}:`, error);
      return null;
    }
  },

  /**
   * Atualiza uma configuração de alerta
   * @param config - Configuração de alerta
   * @returns Verdadeiro se a atualização foi bem-sucedida
   */
  async updateAlertConfig(config: AlertConfig): Promise<boolean> {
    try {
      await queryHelper.query(
        `UPDATE tab_alert_config
         SET enabled = $1,
             threshold = $2,
             recipients = $3,
             notify_customer = $4,
             template = $5
         WHERE type = $6`,
        [
          config.enabled,
          config.threshold,
          config.recipients.join(','),
          config.notifyCustomer,
          config.template,
          config.type,
        ]
      );

      return true;
    } catch (error) {
      logger.error(`Erro ao atualizar configuração de alerta ${config.type}:`, error);
      return false;
    }
  },

  /**
   * Verifica se um alerta deve ser disparado
   * @param type - Tipo de alerta
   * @param data - Dados para verificação
   * @returns Verdadeiro se o alerta deve ser disparado
   */
  async shouldTriggerAlert(type: AlertType, data: AlertData): Promise<boolean> {
    try {
      const config = await this.getAlertConfig(type);

      if (!config || !config.enabled) {
        return false;
      }

      switch (type) {
        case 'high_value_transaction':
          return data.orderTotal >= (config.threshold || 1000);

        case 'suspicious_activity':
          // Implementar lógica de detecção de atividades suspeitas
          // Por exemplo, múltiplas tentativas de pagamento em curto período
          return await this.checkSuspiciousActivity(data.customerId);

        case 'payment_failure':
          return data.paymentStatus === 'Rejeitado';

        case 'chargeback_risk':
          // Implementar lógica de detecção de risco de chargeback
          return await this.checkChargebackRisk(data.customerId, data.orderTotal);

        case 'payment_success':
          return data.paymentStatus === 'Aprovado';

        case 'refund_processed':
          return data.paymentStatus === 'Reembolsado';

        default:
          return false;
      }
    } catch (error) {
      logger.error(`Erro ao verificar alerta ${type}:`, error);
      return false;
    }
  },

  /**
   * Dispara um alerta
   * @param type - Tipo de alerta
   * @param data - Dados do alerta
   * @returns Verdadeiro se o alerta foi disparado com sucesso
   */
  async triggerAlert(type: AlertType, data: AlertData): Promise<boolean> {
    try {
      // Verificar se o alerta deve ser disparado
      const shouldTrigger = await this.shouldTriggerAlert(type, data);

      if (!shouldTrigger) {
        return false;
      }

      // Obter configuração do alerta
      const config = await this.getAlertConfig(type);

      if (!config) {
        return false;
      }

      // Registrar alerta no banco de dados
      await this.logAlert(type, data);

      // Enviar notificações
      const success = await this.sendAlertNotifications(config, data);

      return success;
    } catch (error) {
      logger.error(`Erro ao disparar alerta ${type}:`, error);
      return false;
    }
  },

  /**
   * Registra um alerta no banco de dados
   * @param type - Tipo de alerta
   * @param data - Dados do alerta
   */
  async logAlert(type: AlertType, data: AlertData): Promise<void> {
    try {
      await queryHelper.query(
        `INSERT INTO tab_alert_log (
           type,
           ulid_order,
           ulid_user,
           order_total,
           payment_method,
           payment_status,
           additional_info
         ) VALUES ($1, $2, $3, $4, $5, $6, $7)`,
        [
          type,
          data.orderId,
          data.customerId,
          data.orderTotal,
          data.paymentMethod,
          data.paymentStatus,
          JSON.stringify(data.additionalInfo || {}),
        ]
      );
    } catch (error) {
      logger.error(`Erro ao registrar alerta ${type}:`, error);
    }
  },

  /**
   * Envia notificações de alerta
   * @param config - Configuração do alerta
   * @param data - Dados do alerta
   * @returns Verdadeiro se as notificações foram enviadas com sucesso
   */
  async sendAlertNotifications(config: AlertConfig, data: AlertData): Promise<boolean> {
    try {
      // Preparar dados para o template
      const templateData = {
        ...data,
        alertType: this.getAlertTypeName(config.type),
        orderTotal: new Intl.NumberFormat('pt-BR', {
          style: 'currency',
          currency: 'BRL',
        }).format(data.orderTotal),
        transactionDate: data.transactionDate.toLocaleString('pt-BR'),
      };

      // Enviar e-mail para administradores
      if (config.recipients.length > 0) {
        await email.sendEmail({
          to: config.recipients,
          subject: `Alerta: ${this.getAlertTypeName(config.type)} - Pedido ${data.orderId.substring(0, 8)}`,
          template: config.template,
          data: templateData,
        });
      }

      // Enviar e-mail para o cliente, se configurado
      if (config.notifyCustomer && data.customerEmail) {
        await email.sendEmail({
          to: [data.customerEmail],
          subject: this.getCustomerSubject(config.type),
          template: 'customer-alert',
          data: {
            ...templateData,
            customerName: data.customerName,
          },
        });
      }

      return true;
    } catch (error) {
      logger.error(`Erro ao enviar notificações de alerta ${config.type}:`, error);
      return false;
    }
  },

  /**
   * Verifica se há atividade suspeita para um cliente
   * @param customerId - ID do cliente
   * @returns Verdadeiro se há atividade suspeita
   */
  async checkSuspiciousActivity(customerId: string): Promise<boolean> {
    try {
      // Verificar múltiplas tentativas de pagamento nas últimas 24 horas
      const result = await queryHelper.query(
        `SELECT COUNT(*) as count
         FROM tab_payment
         WHERE ulid_user = $1
         AND created_at >= NOW() - INTERVAL '24 hours'
         AND cod_status = 3`, // Rejeitado
        [customerId]
      );

      const failedAttempts = Number.parseInt(result.rows[0].count, 10);

      // Considerar suspeito se houver 3 ou mais tentativas falhas em 24h
      return failedAttempts >= 3;
    } catch (error) {
      logger.error('Erro ao verificar atividade suspeita:', error);
      return false;
    }
  },

  /**
   * Verifica se há risco de chargeback para um cliente
   * @param customerId - ID do cliente
   * @param orderTotal - Valor total do pedido
   * @returns Verdadeiro se há risco de chargeback
   */
  async checkChargebackRisk(customerId: string, orderTotal: number): Promise<boolean> {
    try {
      // Verificar histórico de chargebacks
      const chargebackResult = await queryHelper.query(
        `SELECT COUNT(*) as count
         FROM tab_payment
         WHERE ulid_user = $1
         AND cod_status = 5`, // Chargeback
        [customerId]
      );

      const chargebackCount = Number.parseInt(chargebackResult.rows[0].count, 10);

      // Verificar compras de alto valor nos últimos 7 dias
      const highValueResult = await queryHelper.query(
        `SELECT COUNT(*) as count
         FROM tab_order
         WHERE ulid_user = $1
         AND total >= $2
         AND created_at >= NOW() - INTERVAL '7 days'`,
        [customerId, orderTotal * 0.8] // 80% do valor atual
      );

      const highValueCount = Number.parseInt(highValueResult.rows[0].count, 10);

      // Considerar risco se houver histórico de chargeback ou padrão de compras de alto valor
      return chargebackCount > 0 || highValueCount >= 3;
    } catch (error) {
      logger.error('Erro ao verificar risco de chargeback:', error);
      return false;
    }
  },

  /**
   * Obtém o nome amigável do tipo de alerta
   * @param type - Tipo de alerta
   * @returns Nome amigável
   */
  getAlertTypeName(type: AlertType): string {
    const names: Record<AlertType, string> = {
      high_value_transaction: 'Transação de Alto Valor',
      suspicious_activity: 'Atividade Suspeita',
      payment_failure: 'Falha no Pagamento',
      chargeback_risk: 'Risco de Chargeback',
      payment_success: 'Pagamento Aprovado',
      refund_processed: 'Reembolso Processado',
    };

    return names[type] || type;
  },

  /**
   * Obtém o assunto do e-mail para o cliente
   * @param type - Tipo de alerta
   * @returns Assunto do e-mail
   */
  getCustomerSubject(type: AlertType): string {
    const subjects: Record<AlertType, string> = {
      high_value_transaction: 'Confirmação de Transação de Alto Valor',
      suspicious_activity: 'Verificação de Segurança da Sua Conta',
      payment_failure: 'Problema com Seu Pagamento',
      chargeback_risk: 'Informações Importantes Sobre Sua Compra',
      payment_success: 'Pagamento Aprovado com Sucesso',
      refund_processed: 'Seu Reembolso Foi Processado',
    };

    return subjects[type] || 'Notificação Importante';
  },
};
