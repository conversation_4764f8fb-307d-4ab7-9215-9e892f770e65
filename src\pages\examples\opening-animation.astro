---
import TrainAnimation from '../../components/animations/TrainAnimation.astro';
import { PageTransition } from '../../components/transitions';
import DaisyCard from '../../components/ui/DaisyCard.astro';
import Breadcrumbs from '../../components/ui/navigation/Breadcrumbs.astro';
/**
 * Página de demonstração da animação de abertura
 * Esta página mostra a animação de abertura com tema de trem
 */
import BaseLayout from '../../layouts/BaseLayout.astro';
import { Container, Section } from '../../layouts/grid';

// Título da página
const title = 'Animação de Abertura';

// Breadcrumbs para a página atual
const breadcrumbItems = [
  { href: '/', label: 'Início' },
  { href: '/examples', label: 'Exemplos' },
  { label: 'Animação de Abertura' },
];

// Função de callback para quando a animação terminar
const onCompleteFunction = `
  function animationCompleted() {
    const message = document.getElementById('completion-message');
    if (message) {
      message.style.display = 'block';
      message.classList.add('animate-fade-in');
    }
  }
`;
---

<BaseLayout title={title} enableViewTransitions={true} transitionType="fade">
  <Container>
    <PageTransition type="fade" duration="0.5s">
      <Section>
        <Breadcrumbs items={breadcrumbItems} class="mb-6" />
        
        <h1 class="text-3xl font-bold mb-6">{title}</h1>
        
        <p class="mb-8">
          Esta página demonstra a animação de abertura com tema de trem para o projeto Estação da Alfabetização.
          A animação utiliza AnimeJS para criar uma sequência animada e interativa.
        </p>
        
        <div class="mb-12">
          <DaisyCard title="Animação de Abertura Padrão">
            <div class="p-4">
              <TrainAnimation 
                autoplay={true} 
                duration={8000} 
                showSkipButton={true} 
                skipButtonText="Pular animação" 
                onComplete="animationCompleted"
              />
              
              <div id="completion-message" class="mt-4 p-4 bg-success text-white rounded-box text-center" style="display: none;">
                Animação concluída! Esta mensagem aparece quando a animação termina ou é pulada.
              </div>
            </div>
          </DaisyCard>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
          <DaisyCard title="Controles da Animação">
            <div class="p-4">
              <p class="mb-4">
                Você pode controlar a animação com os botões abaixo:
              </p>
              
              <div class="flex flex-wrap gap-2">
                <button id="play-button" class="btn btn-primary">Iniciar</button>
                <button id="pause-button" class="btn btn-secondary">Pausar</button>
                <button id="restart-button" class="btn btn-accent">Reiniciar</button>
                <button id="skip-button" class="btn btn-info">Pular</button>
              </div>
              
              <div class="mt-4">
                <TrainAnimation 
                  id="controlled-animation"
                  autoplay={false} 
                  duration={8000} 
                  showSkipButton={false}
                />
              </div>
            </div>
          </DaisyCard>
          
          <DaisyCard title="Opções de Configuração">
            <div class="p-4">
              <p class="mb-4">
                A animação de abertura pode ser configurada com várias opções:
              </p>
              
              <ul class="list-disc pl-5 space-y-2">
                <li><code>autoplay</code> - Se a animação deve iniciar automaticamente</li>
                <li><code>duration</code> - Duração total da animação em milissegundos</li>
                <li><code>showSkipButton</code> - Se deve mostrar o botão para pular a animação</li>
                <li><code>skipButtonText</code> - Texto do botão para pular</li>
                <li><code>onComplete</code> - Função a ser chamada quando a animação terminar</li>
              </ul>
              
              <pre class="mt-4 p-3 bg-base-200 rounded-box text-xs overflow-x-auto">
&lt;TrainAnimation 
  autoplay={true} 
  duration={8000} 
  showSkipButton={true} 
  skipButtonText="Pular animação" 
  onComplete="myCallbackFunction"
/&gt;
              </pre>
            </div>
          </DaisyCard>
        </div>
        
        <div class="mb-12">
          <DaisyCard title="Acessibilidade">
            <div class="p-4">
              <p class="mb-4">
                A animação respeita as preferências de movimento reduzido do usuário:
              </p>
              
              <ul class="list-disc pl-5 space-y-2">
                <li>Se o usuário tiver a configuração <code>prefers-reduced-motion: reduce</code> ativada, a animação não será executada</li>
                <li>Em vez disso, apenas o estado final da animação será mostrado</li>
                <li>O botão "Pular animação" permite que os usuários pulem a animação se desejarem</li>
                <li>A animação não bloqueia a interação com o resto da página</li>
              </ul>
            </div>
          </DaisyCard>
        </div>
      </Section>
    </PageTransition>
  </Container>
</BaseLayout>

<script set:html={onCompleteFunction}></script>

<script>
  // Controles para a animação
  document.addEventListener('DOMContentLoaded', () => {
    const controlledAnimation = document.getElementById('controlled-animation');
    if (!controlledAnimation) return;
    
    // Botões de controle
    const playButton = document.getElementById('play-button');
    const pauseButton = document.getElementById('pause-button');
    const restartButton = document.getElementById('restart-button');
    const skipButton = document.getElementById('skip-button');
    
    // Inicializar a animação (sem autoplay)
    let animation;
    
    // Aguardar um pouco para garantir que a animação foi inicializada
    setTimeout(() => {
      // Obter a referência à animação
      animation = controlledAnimation.animation;
      
      if (animation) {
        // Configurar os botões
        if (playButton) {
          playButton.addEventListener('click', () => {
            animation.play();
          });
        }
        
        if (pauseButton) {
          pauseButton.addEventListener('click', () => {
            animation.pause();
          });
        }
        
        if (restartButton) {
          restartButton.addEventListener('click', () => {
            animation.restart();
          });
        }
        
        if (skipButton) {
          skipButton.addEventListener('click', () => {
            // Pular para o final da animação
            animation.seek(animation.duration);
          });
        }
      }
    }, 1000);
  });
</script>
