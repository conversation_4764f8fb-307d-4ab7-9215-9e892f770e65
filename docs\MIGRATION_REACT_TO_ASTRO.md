# Migração de React para Astro Nativo

Este documento descreve como migrar funcionalidades React para implementações nativas do Astro.js no projeto Estação Alfabetização.

## Visão Geral

O projeto Estação Alfabetização segue uma arquitetura **Zero-JS** usando apenas componentes Astro nativos. Qualquer código React encontrado deve ser migrado para implementações Astro nativas.

## Arquivo Identificado para Migração

### `src/hooks/useAuth.tsx` ❌

**Status**: Depreciado - deve ser removido
**Problema**: Hook React que não deve existir no projeto
**Solução**: Usar componentes Astro nativos criados

## Componentes Astro Nativos Criados

### 1. `src/components/auth/AuthProvider.astro` ✅

**Substitui**: Context Provider React
**Funcionalidades**:
- Verificação de autenticação server-side
- Disponibilização de dados de auth no cliente
- Funções utilitárias globais
- Proteção de rotas

**Uso**:
```astro
---
import AuthProvider from '@components/auth/AuthProvider.astro';
---

<AuthProvider requireAuth={true} redirectTo="/signin">
  <!-- Conteúdo protegido -->
</AuthProvider>
```

### 2. `src/components/auth/AuthGuard.astro` ✅

**Substitui**: Componentes de proteção React
**Funcionalidades**:
- Proteção baseada em autenticação
- Proteção baseada em permissões/papéis
- Conteúdo alternativo para usuários não autorizados

**Uso**:
```astro
---
import AuthGuard from '@components/auth/AuthGuard.astro';
---

<AuthGuard requireAuth={true} permissions={['admin']} roles={['teacher']}>
  <!-- Conteúdo protegido -->
</AuthGuard>
```

### 3. `src/components/auth/LoginForm.astro` ✅

**Substitui**: Componentes de formulário React
**Funcionalidades**:
- Formulário de login server-side
- Validação client-side com JavaScript vanilla
- Estados de loading
- Tratamento de erros

**Uso**:
```astro
---
import LoginForm from '@components/auth/LoginForm.astro';
---

<LoginForm redirectTo="/dashboard" showRememberMe={true} />
```

## Serviço de Autenticação Existente

### `src/services/authService.ts` ✅

**Status**: Já implementado corretamente
**Funcionalidades**:
- Autenticação server-side com sessões Astro
- Métodos para login/logout
- Verificação de autenticação
- Gerenciamento de usuários

## Padrões de Migração

### De React Hook para Astro Service

**Antes (React)**:
```tsx
const { user, isAuthenticated, login, logout } = useAuth();
```

**Depois (Astro)**:
```astro
---
import { authService } from '@services/authService';

const isAuthenticated = await authService.isAuthenticated(Astro);
const user = isAuthenticated ? await authService.getAuthenticatedUser(Astro) : null;
---
```

### De React Context para Astro Global

**Antes (React)**:
```tsx
<AuthContext.Provider value={authData}>
  {children}
</AuthContext.Provider>
```

**Depois (Astro)**:
```astro
<AuthProvider requireAuth={true}>
  <slot />
</AuthProvider>
```

### De React State para JavaScript Vanilla

**Antes (React)**:
```tsx
const [isLoading, setIsLoading] = useState(false);
```

**Depois (Astro)**:
```javascript
// Em um script tag
let isLoading = false;

function setLoading(loading) {
  isLoading = loading;
  updateUI();
}
```

## Vantagens da Migração

### 1. **Performance**
- Sem JavaScript desnecessário no cliente
- Renderização server-side por padrão
- Carregamento mais rápido

### 2. **Simplicidade**
- Menos dependências
- Código mais direto
- Melhor manutenibilidade

### 3. **SEO**
- Conteúdo renderizado no servidor
- Melhor indexação
- Meta tags dinâmicas

### 4. **Acessibilidade**
- Funciona sem JavaScript
- Progressive enhancement
- Melhor compatibilidade

## Checklist de Migração

- [x] ~~Identificar arquivos React~~ (`src/hooks/useAuth.tsx`)
- [x] ~~Criar componentes Astro equivalentes~~
- [x] ~~Implementar serviços server-side~~
- [x] ~~Criar documentação de migração~~
- [ ] **Remover arquivo React** (`src/hooks/useAuth.tsx`)
- [ ] **Atualizar imports** que referenciam o hook React
- [ ] **Testar funcionalidades** migradas
- [ ] **Validar comportamento** em produção

## Próximos Passos

1. **Remover `src/hooks/useAuth.tsx`**
2. **Buscar e substituir** qualquer import do hook React
3. **Atualizar componentes** que usavam o hook
4. **Testar autenticação** em todas as páginas
5. **Validar performance** após migração

## Comandos Úteis

```bash
# Buscar referências ao hook React
grep -r "useAuth" src/

# Buscar imports React
grep -r "from 'react'" src/

# Verificar arquivos .tsx/.jsx
find src/ -name "*.tsx" -o -name "*.jsx"
```

## Suporte

Para dúvidas sobre a migração, consulte:
- [Documentação Astro](https://docs.astro.build/)
- [Arquitetura Zero-JS](./ZERO_JS_ARCHITECTURE.md)
- [Padrões de Código](./CODE_STANDARDS.md)
