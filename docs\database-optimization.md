# Sistema de Otimização de Banco de Dados

## Visão Geral

O sistema de otimização de banco de dados implementa funcionalidades para monitoramento, análise e otimização de consultas SQL, gerenciamento de conexões e manutenção de índices. Este sistema visa melhorar a performance, escalabilidade e eficiência do banco de dados PostgreSQL utilizado pela aplicação.

## Arquitetura

O sistema de otimização de banco de dados é composto pelos seguintes componentes:

1. **Serviço de Otimização (dbOptimizationService)**: Implementa a lógica de monitoramento, análise e otimização de consultas.
2. **Middleware de Otimização (queryOptimizationMiddleware)**: Intercepta consultas SQL para análise e otimização em tempo real.
3. **Interface de Administração**: Permite visualizar estatísticas, analisar consultas e gerenciar índices.
4. **Migrações de Otimização**: Scripts SQL para criar índices otimizados e estatísticas estendidas.

## Funcionalidades

### Monitoramento de Consultas

- **Detecção de Consultas Lentas**: Identificação automática de consultas que excedem um limiar de tempo de execução.
- **Estatísticas de Execução**: Coleta de métricas sobre tempo de execução, frequência e número de linhas afetadas.
- **Análise de Padrões**: Identificação de padrões problemáticos em consultas SQL.

### Otimização de Conexões

- **Pool de Conexões Otimizado**: Configuração automática do tamanho ideal do pool com base nos recursos do sistema.
- **Monitoramento de Conexões**: Acompanhamento de conexões ativas, ociosas e em espera.
- **Estratégias de Retry**: Implementação de estratégias de retry para falhas de conexão.

### Gerenciamento de Índices

- **Análise de Índices**: Identificação de índices não utilizados ou faltantes.
- **Sugestão de Índices**: Recomendação automática de índices com base em padrões de consulta.
- **Criação de Índices**: Interface para criação e gerenciamento de índices.

### Otimização de Consultas

- **Reescrita Automática**: Otimização automática de consultas ineficientes.
- **Paginação Eficiente**: Implementação de estratégias de paginação baseadas em cursor para grandes conjuntos de dados.
- **Análise de Planos de Execução**: Avaliação de planos de execução para identificar gargalos.

## Implementação Técnica

### Monitoramento de Consultas

O sistema intercepta todas as consultas SQL executadas pela aplicação e coleta as seguintes métricas:

```typescript
interface QueryStats {
  query: string;
  avgExecutionTime: number;
  executionCount: number;
  rowCount: number;
  lastExecuted: number;
  usesIndexes: boolean;
  tables: string[];
  queryType: string;
}
```

Estas estatísticas são armazenadas no cache Valkey para análise posterior e são acessíveis através da interface de administração.

### Detecção de Consultas Lentas

Consultas que excedem o limiar de tempo de execução (padrão: 500ms) são registradas como "lentas" e armazenadas para análise:

```typescript
async recordSlowQuery(
  query: string,
  executionTime: number,
  rowCount: number
): Promise<void> {
  // Incrementar contador de consultas lentas
  applicationMonitoringService.incrementCounter('db_slow_query');
  
  // Registrar detalhes da consulta lenta
  logger.warn('Consulta SQL lenta detectada:', {
    query: this.sanitizeQuery(query),
    executionTime: `${executionTime.toFixed(2)}ms`,
    rowCount,
    threshold: `${this.SLOW_QUERY_THRESHOLD}ms`,
  });
  
  // Armazenar no cache para análise posterior
  const slowQueryKey = `db:slow_query:${Date.now()}`;
  await cacheService.setItem(
    slowQueryKey,
    JSON.stringify({
      query: this.sanitizeQuery(query),
      executionTime,
      rowCount,
      timestamp: Date.now(),
    }),
    60 * 60 * 24 // 24 horas
  );
}
```

### Análise de Consultas

O middleware de otimização analisa consultas SQL em busca de padrões problemáticos:

```typescript
const PROBLEMATIC_PATTERNS = [
  {
    pattern: /SELECT\s+\*\s+FROM/i,
    issue: {
      type: 'performance',
      description: 'Uso de SELECT * pode retornar colunas desnecessárias',
      severity: 'medium',
    },
    suggestion: 'Especifique apenas as colunas necessárias em vez de usar SELECT *',
  },
  // Outros padrões...
];
```

### Otimização de Pool de Conexões

O tamanho ideal do pool de conexões é calculado com base no número de CPUs disponíveis:

```typescript
calculateOptimalPoolSize(): number {
  // Obter número de CPUs disponíveis
  const numCpus = require('os').cpus().length;

  // Fórmula: (Núcleos * 2) + 1
  // Esta é uma heurística comum para pools de conexão
  const optimalSize = (numCpus * 2) + 1;

  // Limitar a um máximo razoável
  return Math.min(optimalSize, 20);
}
```

### Índices Otimizados

O sistema cria índices otimizados para as consultas mais frequentes:

```sql
-- Índice para busca por email (login)
CREATE INDEX idx_user_email ON tab_user(email);

-- Índice para busca por nome (usando gin para busca parcial)
CREATE INDEX idx_product_name_gin ON tab_product USING gin(name gin_trgm_ops);
```

### Paginação Eficiente

O sistema implementa paginação baseada em cursor para grandes conjuntos de dados:

```typescript
generatePaginatedQuerySQL(
  tableName: string,
  columns: string[],
  whereClause = '',
  orderColumn: string,
  pageSize: number,
  cursor?: any,
  isDesc = false
): string {
  // Colunas a serem selecionadas
  const selectColumns = columns.join(', ');
  
  // Operador de comparação baseado na direção
  const operator = isDesc ? '<' : '>';
  
  // Cláusula WHERE com cursor
  const cursorClause = cursor
    ? `${whereClause ? 'AND' : 'WHERE'} ${orderColumn} ${operator} $cursor`
    : '';
  
  // Gerar SQL
  return `
    SELECT ${selectColumns}
    FROM ${tableName}
    ${whereClause}
    ${cursorClause}
    ORDER BY ${orderColumn} ${isDesc ? 'DESC' : 'ASC'}
    LIMIT ${pageSize};
  `.trim();
}
```

## Interface de Administração

A interface de administração (`/admin/database/optimization`) permite:

- Visualizar estatísticas de consultas e conexões
- Analisar consultas SQL e receber sugestões de otimização
- Gerenciar índices existentes e criar novos índices
- Monitorar consultas lentas

## Boas Práticas

### Consultas SQL

1. **Evite SELECT ***: Especifique apenas as colunas necessárias para reduzir a quantidade de dados transferidos.
2. **Use Índices Adequados**: Certifique-se de que as colunas usadas em cláusulas WHERE, JOIN e ORDER BY estejam indexadas.
3. **Limite Resultados**: Sempre use LIMIT para limitar o número de resultados retornados.
4. **Use Paginação Baseada em Cursor**: Para grandes conjuntos de dados, prefira paginação baseada em cursor em vez de OFFSET.
5. **Evite Funções em Cláusulas WHERE**: Funções aplicadas a colunas impedem o uso de índices.

### Índices

1. **Índices Seletivos**: Crie índices para colunas com alta cardinalidade (muitos valores distintos).
2. **Evite Índices Redundantes**: Não crie índices para colunas já cobertas por índices compostos.
3. **Monitore Uso de Índices**: Remova índices não utilizados para reduzir overhead de escrita.
4. **Índices Parciais**: Use índices parciais para subconjuntos de dados frequentemente consultados.
5. **Índices de Texto**: Use índices GIN ou GiST para busca em texto completo.

### Pool de Conexões

1. **Tamanho Adequado**: Configure o tamanho do pool de acordo com os recursos disponíveis.
2. **Libere Conexões**: Sempre libere conexões após o uso para evitar vazamentos.
3. **Monitore Conexões**: Acompanhe o número de conexões ativas e em espera.
4. **Timeout Adequado**: Configure timeouts para evitar conexões ociosas por muito tempo.
5. **Estratégia de Retry**: Implemente retry exponencial para falhas de conexão.

## Troubleshooting

### Problemas Comuns

1. **Consultas Lentas**: 
   - Verifique se as colunas usadas em cláusulas WHERE estão indexadas.
   - Analise o plano de execução com EXPLAIN ANALYZE.
   - Verifique se há bloqueios ou contenção de recursos.

2. **Pool de Conexões Esgotado**:
   - Aumente o tamanho máximo do pool.
   - Verifique se há vazamentos de conexão (conexões não liberadas).
   - Implemente timeouts para evitar conexões ociosas.

3. **Índices Não Utilizados**:
   - Verifique se as consultas estão usando os índices corretamente.
   - Analise o plano de execução com EXPLAIN.
   - Reconstrua índices fragmentados com REINDEX.

### Logs

O sistema registra eventos importantes nos logs:

- Consultas lentas
- Problemas de conexão
- Criação e remoção de índices
- Otimizações aplicadas

## Referências

- [PostgreSQL Documentation](https://www.postgresql.org/docs/)
- [PostgreSQL Performance Optimization](https://www.postgresql.org/docs/current/performance-tips.html)
- [PostgreSQL Indexing Strategies](https://www.postgresql.org/docs/current/indexes-strategies.html)
- [Connection Pooling in PostgreSQL](https://www.postgresql.org/docs/current/client-interfaces.html)
