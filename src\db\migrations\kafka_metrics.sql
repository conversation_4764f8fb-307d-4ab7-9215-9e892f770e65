-- Migração para criar tabelas de métricas do Kafka

-- Tabela para armazenar histórico de métricas do Kafka
CREATE TABLE IF NOT EXISTS tab_kafka_metrics_history (
  metric_id UUID PRIMARY KEY,
  broker_count INTEGER NOT NULL,
  topic_count INTEGER NOT NULL,
  partition_count INTEGER NOT NULL,
  under_replicated_partitions INTEGER NOT NULL,
  offline_partitions INTEGER NOT NULL,
  consumer_group_count INTEGER NOT NULL,
  total_lag BIGINT NOT NULL,
  message_in_rate INTEGER NOT NULL,
  message_out_rate INTEGER NOT NULL,
  active_alerts INTEGER NOT NULL,
  cpu_usage NUMERIC(5,2) NOT NULL,
  memory_usage NUMERIC(5,2) NOT NULL,
  disk_usage NUMERIC(5,2) NOT NULL,
  network_in NUMERIC(10,2) NOT NULL,
  network_out NUMERIC(10,2) NOT NULL,
  timestamp TIMESTAMP NOT NULL,
  created_at TIMESTAMP NOT NULL DEFAULT NOW()
);

-- Índices para melhorar performance de consultas
CREATE INDEX IF NOT EXISTS idx_kafka_metrics_timestamp ON tab_kafka_metrics_history (timestamp);

-- Tabela para armazenar métricas de tópicos
CREATE TABLE IF NOT EXISTS tab_kafka_topic_metrics (
  metric_id UUID PRIMARY KEY,
  topic_name VARCHAR(255) NOT NULL,
  partition_count INTEGER NOT NULL,
  replication_factor INTEGER NOT NULL,
  message_count BIGINT NOT NULL,
  message_rate INTEGER NOT NULL,
  size_bytes BIGINT NOT NULL,
  consumer_count INTEGER NOT NULL,
  total_lag BIGINT NOT NULL,
  timestamp TIMESTAMP NOT NULL,
  created_at TIMESTAMP NOT NULL DEFAULT NOW()
);

-- Índices para métricas de tópicos
CREATE INDEX IF NOT EXISTS idx_kafka_topic_metrics_topic ON tab_kafka_topic_metrics (topic_name);
CREATE INDEX IF NOT EXISTS idx_kafka_topic_metrics_timestamp ON tab_kafka_topic_metrics (timestamp);

-- Tabela para armazenar métricas de grupos de consumidores
CREATE TABLE IF NOT EXISTS tab_kafka_consumer_metrics (
  metric_id UUID PRIMARY KEY,
  group_id VARCHAR(255) NOT NULL,
  state VARCHAR(50) NOT NULL,
  member_count INTEGER NOT NULL,
  topic_count INTEGER NOT NULL,
  total_lag BIGINT NOT NULL,
  message_rate INTEGER NOT NULL,
  timestamp TIMESTAMP NOT NULL,
  created_at TIMESTAMP NOT NULL DEFAULT NOW()
);

-- Índices para métricas de consumidores
CREATE INDEX IF NOT EXISTS idx_kafka_consumer_metrics_group ON tab_kafka_consumer_metrics (group_id);
CREATE INDEX IF NOT EXISTS idx_kafka_consumer_metrics_timestamp ON tab_kafka_consumer_metrics (timestamp);

-- Função para limpar métricas antigas
CREATE OR REPLACE FUNCTION fn_clean_kafka_metrics(p_days INTEGER)
RETURNS INTEGER AS $$
DECLARE
  v_count INTEGER;
BEGIN
  -- Limpar métricas gerais
  DELETE FROM tab_kafka_metrics_history
  WHERE timestamp < NOW() - (p_days || ' days')::INTERVAL;
  
  GET DIAGNOSTICS v_count = ROW_COUNT;
  
  -- Limpar métricas de tópicos
  DELETE FROM tab_kafka_topic_metrics
  WHERE timestamp < NOW() - (p_days || ' days')::INTERVAL;
  
  -- Limpar métricas de consumidores
  DELETE FROM tab_kafka_consumer_metrics
  WHERE timestamp < NOW() - (p_days || ' days')::INTERVAL;
  
  RETURN v_count;
END;
$$ LANGUAGE plpgsql;

-- Visualização para resumo de métricas
CREATE OR REPLACE VIEW vw_kafka_metrics_summary AS
SELECT
  date_trunc('hour', timestamp) AS hour,
  AVG(broker_count) AS avg_broker_count,
  AVG(topic_count) AS avg_topic_count,
  AVG(partition_count) AS avg_partition_count,
  AVG(under_replicated_partitions) AS avg_under_replicated_partitions,
  AVG(offline_partitions) AS avg_offline_partitions,
  AVG(consumer_group_count) AS avg_consumer_group_count,
  AVG(total_lag) AS avg_total_lag,
  AVG(message_in_rate) AS avg_message_in_rate,
  AVG(message_out_rate) AS avg_message_out_rate,
  AVG(active_alerts) AS avg_active_alerts,
  AVG(cpu_usage) AS avg_cpu_usage,
  AVG(memory_usage) AS avg_memory_usage,
  AVG(disk_usage) AS avg_disk_usage,
  AVG(network_in) AS avg_network_in,
  AVG(network_out) AS avg_network_out
FROM
  tab_kafka_metrics_history
WHERE
  timestamp > NOW() - INTERVAL '7 days'
GROUP BY
  date_trunc('hour', timestamp)
ORDER BY
  hour DESC;

-- Visualização para métricas de tópicos
CREATE OR REPLACE VIEW vw_kafka_topic_metrics_summary AS
SELECT
  topic_name,
  AVG(partition_count) AS avg_partition_count,
  AVG(replication_factor) AS avg_replication_factor,
  AVG(message_count) AS avg_message_count,
  AVG(message_rate) AS avg_message_rate,
  AVG(size_bytes) AS avg_size_bytes,
  AVG(consumer_count) AS avg_consumer_count,
  AVG(total_lag) AS avg_total_lag,
  MAX(timestamp) AS last_updated
FROM
  tab_kafka_topic_metrics
WHERE
  timestamp > NOW() - INTERVAL '1 day'
GROUP BY
  topic_name
ORDER BY
  avg_message_rate DESC;

-- Visualização para métricas de consumidores
CREATE OR REPLACE VIEW vw_kafka_consumer_metrics_summary AS
SELECT
  group_id,
  MAX(state) AS current_state,
  AVG(member_count) AS avg_member_count,
  AVG(topic_count) AS avg_topic_count,
  AVG(total_lag) AS avg_total_lag,
  AVG(message_rate) AS avg_message_rate,
  MAX(timestamp) AS last_updated
FROM
  tab_kafka_consumer_metrics
WHERE
  timestamp > NOW() - INTERVAL '1 day'
GROUP BY
  group_id
ORDER BY
  avg_total_lag DESC;

-- Função para obter métricas por período
CREATE OR REPLACE FUNCTION fn_get_kafka_metrics_by_period(
  p_start_time TIMESTAMP,
  p_end_time TIMESTAMP,
  p_interval VARCHAR DEFAULT '1 hour'
)
RETURNS TABLE (
  period TIMESTAMP,
  broker_count NUMERIC,
  topic_count NUMERIC,
  partition_count NUMERIC,
  under_replicated_partitions NUMERIC,
  offline_partitions NUMERIC,
  consumer_group_count NUMERIC,
  total_lag NUMERIC,
  message_in_rate NUMERIC,
  message_out_rate NUMERIC,
  active_alerts NUMERIC,
  cpu_usage NUMERIC,
  memory_usage NUMERIC,
  disk_usage NUMERIC,
  network_in NUMERIC,
  network_out NUMERIC
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    date_trunc(p_interval, timestamp) AS period,
    AVG(broker_count)::NUMERIC AS broker_count,
    AVG(topic_count)::NUMERIC AS topic_count,
    AVG(partition_count)::NUMERIC AS partition_count,
    AVG(under_replicated_partitions)::NUMERIC AS under_replicated_partitions,
    AVG(offline_partitions)::NUMERIC AS offline_partitions,
    AVG(consumer_group_count)::NUMERIC AS consumer_group_count,
    AVG(total_lag)::NUMERIC AS total_lag,
    AVG(message_in_rate)::NUMERIC AS message_in_rate,
    AVG(message_out_rate)::NUMERIC AS message_out_rate,
    AVG(active_alerts)::NUMERIC AS active_alerts,
    AVG(cpu_usage)::NUMERIC AS cpu_usage,
    AVG(memory_usage)::NUMERIC AS memory_usage,
    AVG(disk_usage)::NUMERIC AS disk_usage,
    AVG(network_in)::NUMERIC AS network_in,
    AVG(network_out)::NUMERIC AS network_out
  FROM
    tab_kafka_metrics_history
  WHERE
    timestamp BETWEEN p_start_time AND p_end_time
  GROUP BY
    period
  ORDER BY
    period ASC;
END;
$$ LANGUAGE plpgsql;
