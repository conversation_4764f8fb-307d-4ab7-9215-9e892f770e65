---
import { actions } from 'astro:actions';
import ControlButtons from '@components/form/ControlButtons.astro';
import FormBase from '@components/form/FormBase.astro';
import InputHidden from '@components/form/InputHidden.astro';
import InputText from '@components/form/InputText.astro';
import BaseLayout from '@layouts/BaseLayout.astro';
import type { UserTypeData } from 'src/database/interfacesHelper';

// Define interfaces for type safety
interface ActionResult {
  data?: UserTypeData | UserTypeData[];
  error?: string;
}

// Obter parâmetro da URL
const { ulid_user_type = '' } = Astro.params;

// Busca dados do tipo de usuário
let data: UserTypeData = {};
try {
  const result = await actions.userTypeAction.read({
    filter: 'ulid_user_type',
    ulid_user_type,
  });
  data = (result.data as UserTypeData) || {};
} catch (error) {
  console.error('Erro ao carregar tipo de usuário:', error);
  return Astro.redirect('/admin/register/user/type?error=load');
}

const formValidation = `
  const nameInput = form.querySelector('input[name="name"]');
  if (!nameInput || !nameInput.value.trim()) {
    alert('O nome do tipo de usuário é obrigatório');
    return false;
  }
  return true;
`;
---
<BaseLayout title="Formulário de Tipo de Usuário">
  <div class="container mx-auto p-4">
    <h1 class="text-2xl font-bold mb-4">
      {ulid_user_type ? "Editar" : "Novo"} Tipo de Usuário
    </h1>

    <FormBase
      action={actions.userTypeAction.update}
      formType="userType"
      onSubmitValidation={formValidation}
    >
      <!-- Campos ocultos -->
      <InputHidden ulid={data.ulid_user_type ?? ""} field="ulid_user" />

      <!-- Campo nome -->
      <InputText 
        label="Tipo" 
        name="type" 
        value={data.type ?? ""} 
        required={true}
      />

      <!-- Botões de controle -->
      <ControlButtons 
        saveLabel="Salvar"
        cancelHref="/admin/register/user/type"
      />
    </FormBase>
  </div>
</BaseLayout>