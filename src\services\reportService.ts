/**
 * Serviço para geração e exportação de relatórios financeiros
 */

import { Readable } from 'node:stream';
import { queryHelper } from '@db/queryHelper';
import PDFDocument from 'pdfkit';

// Tipos de relatórios disponíveis
export type ReportType = 'sales' | 'revenue' | 'payments' | 'transactions';

// Formato de exportação
export type ExportFormat = 'csv' | 'json' | 'pdf';

// Interface para filtros de relatório
export interface ReportFilters {
  startDate?: string;
  endDate?: string;
  paymentType?: string;
  status?: string;
  userId?: string;
}

// Serviço de relatórios
export const reportService = {
  /**
   * Gera dados para relatório de vendas
   * @param filters - Filtros para o relatório
   * @returns Dados do relatório
   */
  async generateSalesReport(filters: ReportFilters = {}) {
    // Construir consulta SQL com filtros
    let query = `
      SELECT
        DATE_TRUNC('day', o.created_at) as date,
        COUNT(*) as count,
        SUM(o.total) as total
      FROM tab_order o
      WHERE 1=1
    `;

    const params = [];
    let paramIndex = 1;

    if (filters.startDate) {
      query += ` AND o.created_at >= $${paramIndex}`;
      params.push(filters.startDate);
      paramIndex++;
    }

    if (filters.endDate) {
      query += ` AND o.created_at <= $${paramIndex}`;
      params.push(filters.endDate);
      paramIndex++;
    }

    if (filters.userId) {
      query += ` AND o.ulid_user = $${paramIndex}`;
      params.push(filters.userId);
      paramIndex++;
    }

    query += ` GROUP BY DATE_TRUNC('day', o.created_at) ORDER BY date ASC`;

    // Executar consulta
    const result = await queryHelper.query(query, params);

    return result.rows;
  },

  /**
   * Gera dados para relatório de receita
   * @param filters - Filtros para o relatório
   * @returns Dados do relatório
   */
  async generateRevenueReport(filters: ReportFilters = {}) {
    // Construir consulta SQL com filtros
    let query = `
      SELECT
        DATE_TRUNC('day', p.created_at) as date,
        pt.type as payment_type,
        s.status as payment_status,
        COUNT(*) as count,
        SUM(p.value) as total
      FROM tab_payment p
      JOIN tab_payment_type pt ON p.ulid_payment_type = pt.ulid_payment_type
      JOIN tab_status s ON p.cod_status = s.cod_status
      WHERE 1=1
    `;

    const params = [];
    let paramIndex = 1;

    if (filters.startDate) {
      query += ` AND p.created_at >= $${paramIndex}`;
      params.push(filters.startDate);
      paramIndex++;
    }

    if (filters.endDate) {
      query += ` AND p.created_at <= $${paramIndex}`;
      params.push(filters.endDate);
      paramIndex++;
    }

    if (filters.paymentType) {
      query += ` AND pt.type = $${paramIndex}`;
      params.push(filters.paymentType);
      paramIndex++;
    }

    if (filters.status) {
      query += ` AND s.status = $${paramIndex}`;
      params.push(filters.status);
      paramIndex++;
    }

    query += ` GROUP BY DATE_TRUNC('day', p.created_at), pt.type, s.status ORDER BY date ASC`;

    // Executar consulta
    const result = await queryHelper.query(query, params);

    return result.rows;
  },

  /**
   * Gera dados para relatório de transações
   * @param filters - Filtros para o relatório
   * @returns Dados do relatório
   */
  async generateTransactionsReport(filters: ReportFilters = {}) {
    // Construir consulta SQL com filtros
    let query = `
      SELECT
        o.ulid_order,
        o.created_at as order_date,
        u.name as customer_name,
        u.email as customer_email,
        o.total as order_total,
        p.value as payment_value,
        pt.type as payment_type,
        s.status as payment_status,
        p.created_at as payment_date
      FROM tab_order o
      JOIN tab_user u ON o.ulid_user = u.ulid_user
      LEFT JOIN tab_payment p ON o.ulid_order = p.ulid_order
      LEFT JOIN tab_payment_type pt ON p.ulid_payment_type = pt.ulid_payment_type
      LEFT JOIN tab_status s ON p.cod_status = s.cod_status
      WHERE 1=1
    `;

    const params = [];
    let paramIndex = 1;

    if (filters.startDate) {
      query += ` AND o.created_at >= $${paramIndex}`;
      params.push(filters.startDate);
      paramIndex++;
    }

    if (filters.endDate) {
      query += ` AND o.created_at <= $${paramIndex}`;
      params.push(filters.endDate);
      paramIndex++;
    }

    if (filters.paymentType) {
      query += ` AND pt.type = $${paramIndex}`;
      params.push(filters.paymentType);
      paramIndex++;
    }

    if (filters.status) {
      query += ` AND s.status = $${paramIndex}`;
      params.push(filters.status);
      paramIndex++;
    }

    if (filters.userId) {
      query += ` AND o.ulid_user = $${paramIndex}`;
      params.push(filters.userId);
      paramIndex++;
    }

    query += ' ORDER BY o.created_at DESC';

    // Executar consulta
    const result = await queryHelper.query(query, params);

    return result.rows;
  },

  /**
   * Converte dados para formato CSV
   * @param data - Dados a serem convertidos
   * @returns String no formato CSV
   */
  convertToCSV(data: any[]) {
    if (data.length === 0) return '';

    // Obter cabeçalhos
    const headers = Object.keys(data[0]);

    // Criar linhas
    const csvRows = [
      // Cabeçalho
      headers.join(','),
      // Dados
      ...data.map((row) => {
        return headers
          .map((header) => {
            // Formatar valor para CSV
            const value = row[header] === null ? '' : row[header];
            // Escapar aspas e adicionar aspas ao redor do valor
            return `"${String(value).replace(/"/g, '""')}"`;
          })
          .join(',');
      }),
    ];

    return csvRows.join('\n');
  },

  /**
   * Converte dados para formato JSON
   * @param data - Dados a serem convertidos
   * @returns String no formato JSON
   */
  convertToJSON(data: any[]) {
    return JSON.stringify(data, null, 2);
  },

  /**
   * Converte dados para formato PDF
   * @param data - Dados a serem convertidos
   * @param reportType - Tipo de relatório
   * @param filters - Filtros aplicados
   * @returns Buffer contendo o PDF
   */
  async convertToPDF(
    data: any[],
    reportType: ReportType,
    filters: ReportFilters = {}
  ): Promise<Buffer> {
    return new Promise((resolve, reject) => {
      try {
        // Criar documento PDF
        const doc = new PDFDocument({
          margin: 50,
          size: 'A4',
          info: {
            Title: `Relatório - ${this.getReportTitle(reportType)}`,
            Author: 'Estação Alfabetização',
            Subject: 'Relatório Financeiro',
            Keywords: 'relatório, financeiro, estação alfabetização',
            Creator: 'Sistema de Relatórios',
            Producer: 'PDFKit',
          },
        });

        // Armazenar chunks do PDF
        const chunks: Buffer[] = [];

        // Capturar chunks
        doc.on('data', (chunk) => chunks.push(chunk));

        // Quando o documento for finalizado, resolver a Promise com o buffer
        doc.on('end', () => {
          const pdfBuffer = Buffer.concat(chunks);
          resolve(pdfBuffer);
        });

        // Adicionar cabeçalho
        this.addPDFHeader(doc, reportType, filters);

        // Adicionar tabela de dados
        this.addPDFTable(doc, data);

        // Adicionar rodapé
        this.addPDFFooter(doc);

        // Finalizar documento
        doc.end();
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * Adiciona cabeçalho ao PDF
   * @param doc - Documento PDF
   * @param reportType - Tipo de relatório
   * @param filters - Filtros aplicados
   */
  addPDFHeader(doc: PDFKit.PDFDocument, reportType: ReportType, filters: ReportFilters): void {
    // Adicionar logo (se disponível)
    // doc.image('path/to/logo.png', 50, 45, { width: 50 });

    // Título do relatório
    doc
      .fontSize(20)
      .font('Helvetica-Bold')
      .text(`Relatório - ${this.getReportTitle(reportType)}`, 50, 50, {
        align: 'center',
      });

    // Data de geração
    doc
      .fontSize(10)
      .font('Helvetica')
      .text(`Gerado em: ${new Date().toLocaleString('pt-BR')}`, 50, 80, {
        align: 'center',
      });

    // Adicionar informações de filtros
    doc.moveDown(2);
    doc.fontSize(12).font('Helvetica-Bold').text('Filtros Aplicados:', 50, 100);

    doc.fontSize(10).font('Helvetica');

    let yPosition = 120;

    if (filters.startDate) {
      doc.text(
        `Data Inicial: ${new Date(filters.startDate).toLocaleDateString('pt-BR')}`,
        50,
        yPosition
      );
      yPosition += 15;
    }

    if (filters.endDate) {
      doc.text(
        `Data Final: ${new Date(filters.endDate).toLocaleDateString('pt-BR')}`,
        50,
        yPosition
      );
      yPosition += 15;
    }

    if (filters.paymentType) {
      doc.text(`Método de Pagamento: ${filters.paymentType}`, 50, yPosition);
      yPosition += 15;
    }

    if (filters.status) {
      doc.text(`Status: ${filters.status}`, 50, yPosition);
      yPosition += 15;
    }

    // Adicionar linha separadora
    doc.moveDown(2);
    doc
      .moveTo(50, yPosition + 10)
      .lineTo(550, yPosition + 10)
      .stroke();

    // Mover para a posição da tabela
    doc.moveDown(3);
  },

  /**
   * Adiciona tabela de dados ao PDF
   * @param doc - Documento PDF
   * @param data - Dados a serem exibidos
   */
  addPDFTable(doc: PDFKit.PDFDocument, data: any[]): void {
    if (data.length === 0) {
      doc
        .fontSize(12)
        .font('Helvetica-Italic')
        .text('Nenhum dado encontrado para os filtros selecionados.', 50, 200, {
          align: 'center',
        });
      return;
    }

    // Obter cabeçalhos
    const headers = Object.keys(data[0]);

    // Configurações da tabela
    const tableTop = 200;
    const tableLeft = 50;
    const cellPadding = 5;
    const columnWidth = 500 / headers.length;

    // Desenhar cabeçalhos
    doc.fontSize(10).font('Helvetica-Bold');

    headers.forEach((header, i) => {
      const x = tableLeft + i * columnWidth;
      doc.text(this.formatHeaderName(header), x, tableTop, {
        width: columnWidth,
        align: 'left',
      });
    });

    // Linha separadora após cabeçalhos
    doc
      .moveTo(tableLeft, tableTop + 15)
      .lineTo(tableLeft + 500, tableTop + 15)
      .stroke();

    // Desenhar dados
    doc.fontSize(9).font('Helvetica');

    let rowTop = tableTop + 25;

    data.forEach((row, rowIndex) => {
      // Verificar se precisa adicionar nova página
      if (rowTop > 750) {
        doc.addPage();
        rowTop = 50;

        // Redesenhar cabeçalhos na nova página
        doc.fontSize(10).font('Helvetica-Bold');

        headers.forEach((header, i) => {
          const x = tableLeft + i * columnWidth;
          doc.text(this.formatHeaderName(header), x, rowTop, {
            width: columnWidth,
            align: 'left',
          });
        });

        // Linha separadora após cabeçalhos
        doc
          .moveTo(tableLeft, rowTop + 15)
          .lineTo(tableLeft + 500, rowTop + 15)
          .stroke();

        // Ajustar posição para os dados
        rowTop += 25;

        // Restaurar fonte para dados
        doc.fontSize(9).font('Helvetica');
      }

      // Desenhar células da linha
      headers.forEach((header, i) => {
        const x = tableLeft + i * columnWidth;
        const value = this.formatCellValue(row[header]);

        doc.text(value, x, rowTop, {
          width: columnWidth,
          align: 'left',
        });
      });

      // Linha separadora entre linhas de dados (opcional)
      if (rowIndex < data.length - 1) {
        doc
          .moveTo(tableLeft, rowTop + 15)
          .lineTo(tableLeft + 500, rowTop + 15)
          .lineWidth(0.5)
          .stroke();
      }

      rowTop += 20;
    });
  },

  /**
   * Adiciona rodapé ao PDF
   * @param doc - Documento PDF
   */
  addPDFFooter(doc: PDFKit.PDFDocument): void {
    const pageCount = doc.bufferedPageRange().count;

    for (let i = 0; i < pageCount; i++) {
      doc.switchToPage(i);

      // Linha separadora do rodapé
      doc.moveTo(50, 780).lineTo(550, 780).stroke();

      // Texto do rodapé
      doc
        .fontSize(8)
        .font('Helvetica')
        .text(
          `Estação Alfabetização - Relatório gerado em ${new Date().toLocaleString('pt-BR')}`,
          50,
          785,
          { align: 'center' }
        );

      // Número da página
      doc.text(`Página ${i + 1} de ${pageCount}`, 50, 785, { align: 'right' });
    }
  },

  /**
   * Formata o nome do cabeçalho para exibição
   * @param header - Nome do cabeçalho
   * @returns Nome formatado
   */
  formatHeaderName(header: string): string {
    // Mapeamento de nomes de cabeçalhos
    const headerMap: Record<string, string> = {
      ulid_order: 'ID do Pedido',
      order_date: 'Data do Pedido',
      customer_name: 'Cliente',
      customer_email: 'Email',
      order_total: 'Valor Total',
      payment_value: 'Valor Pago',
      payment_type: 'Método',
      payment_status: 'Status',
      payment_date: 'Data do Pagamento',
      date: 'Data',
      count: 'Quantidade',
      total: 'Total',
      status: 'Status',
    };

    return headerMap[header] || this.capitalizeFirstLetter(header.replace(/_/g, ' '));
  },

  /**
   * Formata o valor da célula para exibição
   * @param value - Valor da célula
   * @returns Valor formatado
   */
  formatCellValue(value: any): string {
    if (value === null || value === undefined) {
      return 'N/A';
    }

    // Formatar datas
    if (
      value instanceof Date ||
      (typeof value === 'string' && value.match(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/))
    ) {
      return new Date(value).toLocaleString('pt-BR');
    }

    // Formatar valores monetários
    if (typeof value === 'number' && !Number.isInteger(value)) {
      return new Intl.NumberFormat('pt-BR', {
        style: 'currency',
        currency: 'BRL',
      }).format(value);
    }

    return String(value);
  },

  /**
   * Capitaliza a primeira letra de uma string
   * @param str - String a ser capitalizada
   * @returns String capitalizada
   */
  capitalizeFirstLetter(str: string): string {
    return str.charAt(0).toUpperCase() + str.slice(1);
  },

  /**
   * Obtém o título do relatório com base no tipo
   * @param reportType - Tipo de relatório
   * @returns Título do relatório
   */
  getReportTitle(reportType: ReportType): string {
    const titles: Record<ReportType, string> = {
      sales: 'Vendas',
      revenue: 'Receita',
      payments: 'Pagamentos',
      transactions: 'Transações',
    };

    return titles[reportType] || 'Relatório';
  },
};
