/**
 * Tipos seguros para processamento de pagamentos
 * Seguindo princípios de type safety para dados financeiros críticos
 */

import { z } from 'zod';

// Schemas de validação para dados de pagamento
export const PaymentAmountSchema = z.object({
  value: z.number().positive('Valor deve ser positivo').max(999999.99, 'Valor máximo excedido'),
  currency: z.string().length(3, 'Código de moeda deve ter 3 caracteres').default('BRL'),
});

export const PixPaymentSchema = z.object({
  amount: PaymentAmountSchema,
  description: z.string().min(1, 'Descrição é obrigatória').max(255, 'Descrição muito longa'),
  payerName: z.string().min(2, 'Nome do pagador é obrigatório'),
  payerDocument: z.string().regex(/^\d{11}$|^\d{14}$/, 'CPF ou CNPJ inválido'),
  payerEmail: z.string().email('Email inválido'),
  expirationDate: z.date().min(new Date(), 'Data de expiração deve ser futura'),
});

export const BoletoPaymentSchema = z.object({
  amount: PaymentAmountSchema,
  description: z.string().min(1, 'Descrição é obrigatória').max(255, 'Descrição muito longa'),
  payerName: z.string().min(2, 'Nome do pagador é obrigatório'),
  payerDocument: z.string().regex(/^\d{11}$|^\d{14}$/, 'CPF ou CNPJ inválido'),
  payerEmail: z.string().email('Email inválido'),
  dueDate: z.date().min(new Date(), 'Data de vencimento deve ser futura'),
  instructions: z.string().max(500, 'Instruções muito longas').optional(),
});

export const CreditCardSchema = z.object({
  number: z.string().regex(/^\d{13,19}$/, 'Número do cartão inválido'),
  holderName: z.string().min(2, 'Nome do portador é obrigatório'),
  expirationMonth: z.number().int().min(1).max(12),
  expirationYear: z.number().int().min(new Date().getFullYear()),
  cvv: z.string().regex(/^\d{3,4}$/, 'CVV inválido'),
});

export const CreditCardPaymentSchema = z.object({
  amount: PaymentAmountSchema,
  description: z.string().min(1, 'Descrição é obrigatória').max(255, 'Descrição muito longa'),
  card: CreditCardSchema,
  installments: z.number().int().min(1).max(12).default(1),
});

// Tipos inferidos dos schemas
export type PaymentAmount = z.infer<typeof PaymentAmountSchema>;
export type PixPaymentData = z.infer<typeof PixPaymentSchema>;
export type BoletoPaymentData = z.infer<typeof BoletoPaymentSchema>;
export type CreditCardData = z.infer<typeof CreditCardSchema>;
export type CreditCardPaymentData = z.infer<typeof CreditCardPaymentSchema>;

// Enums para status e tipos de pagamento
export enum PaymentStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  CANCELLED = 'cancelled',
  EXPIRED = 'expired',
  REFUNDED = 'refunded',
  PARTIALLY_REFUNDED = 'partially_refunded',
}

export enum PaymentMethod {
  PIX = 'pix',
  BOLETO = 'boleto',
  CREDIT_CARD = 'credit_card',
  DEBIT_CARD = 'debit_card',
  BANK_TRANSFER = 'bank_transfer',
}

export enum PaymentProvider {
  EFI_PAY = 'efi_pay',
  MERCADO_PAGO = 'mercado_pago',
  PAGSEGURO = 'pagseguro',
  STRIPE = 'stripe',
}

// Interfaces para dados de pagamento
export interface PaymentTransaction {
  readonly id: string;
  readonly orderId: string;
  readonly userId: string;
  readonly method: PaymentMethod;
  readonly provider: PaymentProvider;
  readonly status: PaymentStatus;
  readonly amount: PaymentAmount;
  readonly description: string;
  readonly metadata: Record<string, unknown>;
  readonly createdAt: Date;
  readonly updatedAt: Date;
  readonly expiresAt?: Date;
  readonly approvedAt?: Date;
  readonly rejectedAt?: Date;
  readonly cancelledAt?: Date;
}

export interface PaymentResponse {
  readonly success: boolean;
  readonly transactionId: string;
  readonly status: PaymentStatus;
  readonly paymentUrl?: string;
  readonly qrCode?: string;
  readonly barCode?: string;
  readonly pixKey?: string;
  readonly expiresAt?: Date;
  readonly metadata?: Record<string, unknown>;
}

export interface PaymentError {
  readonly success: false;
  readonly error: string;
  readonly code: PaymentErrorCode;
  readonly details?: Record<string, string[]>;
  readonly transactionId?: string;
}

export enum PaymentErrorCode {
  INVALID_AMOUNT = 'INVALID_AMOUNT',
  INVALID_PAYMENT_METHOD = 'INVALID_PAYMENT_METHOD',
  INVALID_CARD_DATA = 'INVALID_CARD_DATA',
  INSUFFICIENT_FUNDS = 'INSUFFICIENT_FUNDS',
  CARD_EXPIRED = 'CARD_EXPIRED',
  CARD_BLOCKED = 'CARD_BLOCKED',
  PROVIDER_ERROR = 'PROVIDER_ERROR',
  NETWORK_ERROR = 'NETWORK_ERROR',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  TRANSACTION_NOT_FOUND = 'TRANSACTION_NOT_FOUND',
  TRANSACTION_EXPIRED = 'TRANSACTION_EXPIRED',
  UNAUTHORIZED = 'UNAUTHORIZED',
  RATE_LIMIT_EXCEEDED = 'RATE_LIMIT_EXCEEDED',
}

// Interfaces para webhook de pagamento
export interface PaymentWebhook {
  readonly event: PaymentWebhookEvent;
  readonly transactionId: string;
  readonly status: PaymentStatus;
  readonly timestamp: Date;
  readonly signature: string;
  readonly data: Record<string, unknown>;
}

export enum PaymentWebhookEvent {
  PAYMENT_CREATED = 'payment.created',
  PAYMENT_APPROVED = 'payment.approved',
  PAYMENT_REJECTED = 'payment.rejected',
  PAYMENT_CANCELLED = 'payment.cancelled',
  PAYMENT_EXPIRED = 'payment.expired',
  PAYMENT_REFUNDED = 'payment.refunded',
  PAYMENT_CHARGEBACK = 'payment.chargeback',
}

// Interfaces para dados do banco de dados
export interface PaymentData {
  readonly ulid_payment: string;
  readonly ulid_order: string;
  readonly ulid_user: string;
  readonly cod_status: number;
  readonly payment_method: string;
  readonly provider: string;
  readonly amount: number;
  readonly currency: string;
  readonly description: string;
  readonly external_id?: string;
  readonly payment_url?: string;
  readonly qr_code?: string;
  readonly bar_code?: string;
  readonly metadata: Record<string, unknown>;
  readonly created_at: Date;
  readonly updated_at: Date;
  readonly expires_at?: Date;
  readonly approved_at?: Date;
  readonly rejected_at?: Date;
  readonly cancelled_at?: Date;
}

export interface OrderData {
  readonly ulid_order: string;
  readonly ulid_user: string;
  readonly cod_status: number;
  readonly total_amount: number;
  readonly currency: string;
  readonly description: string;
  readonly metadata: Record<string, unknown>;
  readonly created_at: Date;
  readonly updated_at: Date;
}

// Type guards para validação de tipos
export function isPaymentTransaction(obj: unknown): obj is PaymentTransaction {
  return (
    typeof obj === 'object' &&
    obj !== null &&
    'id' in obj &&
    'method' in obj &&
    'status' in obj &&
    'amount' in obj
  );
}

export function isPaymentWebhook(obj: unknown): obj is PaymentWebhook {
  return (
    typeof obj === 'object' &&
    obj !== null &&
    'event' in obj &&
    'transactionId' in obj &&
    'signature' in obj
  );
}

// Utilitários para validação de pagamento
export function validatePaymentAmount(amount: number): boolean {
  return amount > 0 && amount <= 999999.99 && Number.isFinite(amount);
}

export function validateCPF(cpf: string): boolean {
  const cleanCPF = cpf.replace(/\D/g, '');
  if (cleanCPF.length !== 11) return false;

  // Verificar se todos os dígitos são iguais
  if (/^(\d)\1{10}$/.test(cleanCPF)) return false;

  // Validar dígitos verificadores
  let sum = 0;
  for (let i = 0; i < 9; i++) {
    sum += Number.parseInt(cleanCPF.charAt(i)) * (10 - i);
  }
  let digit1 = 11 - (sum % 11);
  if (digit1 > 9) digit1 = 0;

  sum = 0;
  for (let i = 0; i < 10; i++) {
    sum += Number.parseInt(cleanCPF.charAt(i)) * (11 - i);
  }
  let digit2 = 11 - (sum % 11);
  if (digit2 > 9) digit2 = 0;

  return (
    digit1 === Number.parseInt(cleanCPF.charAt(9)) &&
    digit2 === Number.parseInt(cleanCPF.charAt(10))
  );
}

export function validateCNPJ(cnpj: string): boolean {
  const cleanCNPJ = cnpj.replace(/\D/g, '');
  if (cleanCNPJ.length !== 14) return false;

  // Verificar se todos os dígitos são iguais
  if (/^(\d)\1{13}$/.test(cleanCNPJ)) return false;

  // Validar primeiro dígito verificador
  const weights1 = [5, 4, 3, 2, 9, 8, 7, 6, 5, 4, 3, 2];
  let sum = 0;
  for (let i = 0; i < 12; i++) {
    sum += Number.parseInt(cleanCNPJ.charAt(i)) * weights1[i];
  }
  let digit1 = sum % 11;
  digit1 = digit1 < 2 ? 0 : 11 - digit1;

  // Validar segundo dígito verificador
  const weights2 = [6, 5, 4, 3, 2, 9, 8, 7, 6, 5, 4, 3, 2];
  sum = 0;
  for (let i = 0; i < 13; i++) {
    sum += Number.parseInt(cleanCNPJ.charAt(i)) * weights2[i];
  }
  let digit2 = sum % 11;
  digit2 = digit2 < 2 ? 0 : 11 - digit2;

  return (
    digit1 === Number.parseInt(cleanCNPJ.charAt(12)) &&
    digit2 === Number.parseInt(cleanCNPJ.charAt(13))
  );
}

export function maskCreditCard(cardNumber: string): string {
  const clean = cardNumber.replace(/\D/g, '');
  if (clean.length < 4) return cardNumber;

  const firstFour = clean.slice(0, 4);
  const lastFour = clean.slice(-4);
  const middle = '*'.repeat(clean.length - 8);

  return `${firstFour}${middle}${lastFour}`;
}

// Constantes para configuração de pagamento
export const PAYMENT_LIMITS = {
  PIX: {
    MIN: 0.01,
    MAX: 999999.99,
  },
  BOLETO: {
    MIN: 5.0,
    MAX: 999999.99,
  },
  CREDIT_CARD: {
    MIN: 1.0,
    MAX: 999999.99,
  },
} as const;

export const PAYMENT_TIMEOUTS = {
  PIX: 24 * 60 * 60 * 1000, // 24 horas
  BOLETO: 7 * 24 * 60 * 60 * 1000, // 7 dias
  CREDIT_CARD: 30 * 60 * 1000, // 30 minutos
} as const;
