/**
 * API para visualização segura de PDF
 *
 * Este endpoint serve como proxy para visualização segura de PDFs.
 * Parte da implementação da tarefa 8.3.2 - Visualização segura
 */

import type { APIRoute } from 'astro';
import { WatermarkService } from '../../domain/services/WatermarkService';
import { PdfWatermarkService } from '../../infrastructure/services/PdfWatermarkService';

// Inicializar serviço de marca d'água
const watermarkService = new PdfWatermarkService();

export const GET: APIRoute = async ({ request, cookies, redirect }) => {
  try {
    // Obter parâmetros da URL
    const url = new URL(request.url);
    const pdfUrl = url.searchParams.get('url');
    const allowCopy = url.searchParams.get('allowCopy') === 'true';
    const applyWatermark = url.searchParams.get('watermark') === 'true';
    const watermarkText = url.searchParams.get('watermarkText') || undefined;

    // Verificar se a URL do PDF foi fornecida
    if (!pdfUrl) {
      return new Response('URL do PDF não fornecida', { status: 400 });
    }

    // Obter informações do usuário da sessão
    const sessionId = cookies.get('session_id')?.value;
    const userId = cookies.get('user_id')?.value || 'anonymous';

    // Verificar se o usuário está autenticado (se necessário)
    if (!sessionId && url.searchParams.get('requireAuth') === 'true') {
      return redirect(`/login?redirect=${encodeURIComponent(request.url)}`);
    }

    // Buscar o PDF
    const pdfResponse = await fetch(pdfUrl);

    if (!pdfResponse.ok) {
      return new Response(`Erro ao buscar PDF: ${pdfResponse.statusText}`, {
        status: pdfResponse.status,
      });
    }

    // Obter o conteúdo do PDF como array de bytes
    const pdfContent = new Uint8Array(await pdfResponse.arrayBuffer());

    // Aplicar marca d'água se solicitado
    let finalContent = pdfContent;

    if (applyWatermark) {
      const watermarkOptions = watermarkText
        ? { text: watermarkText }
        : watermarkService.generateWatermarkText(userId);

      finalContent = await watermarkService.applyWatermark(pdfContent, watermarkOptions);
    }

    // Configurar cabeçalhos de segurança
    const headers = new Headers();
    headers.set('Content-Type', 'application/pdf');
    headers.set('Content-Length', finalContent.length.toString());

    // Impedir cache para garantir que sempre seja buscada a versão mais recente
    headers.set('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate');
    headers.set('Pragma', 'no-cache');
    headers.set('Expires', '0');

    // Impedir download direto
    headers.set('Content-Disposition', 'inline');

    // Impedir que o PDF seja incorporado em outros sites (proteção contra clickjacking)
    headers.set('X-Frame-Options', 'SAMEORIGIN');

    // Configurar CSP para impedir scripts no PDF
    headers.set(
      'Content-Security-Policy',
      "default-src 'self'; script-src 'none'; object-src 'self'"
    );

    // Registrar acesso ao documento (em um cenário real, isso seria feito no banco de dados)
    console.log(`Acesso ao documento: ${pdfUrl} por usuário: ${userId}`);

    // Retornar o PDF
    return new Response(finalContent, {
      status: 200,
      headers,
    });
  } catch (error) {
    console.error('Erro ao processar visualização segura de PDF:', error);
    return new Response('Erro interno ao processar o PDF', { status: 500 });
  }
};
