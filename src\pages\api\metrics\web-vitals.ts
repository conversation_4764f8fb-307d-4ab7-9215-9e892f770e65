/**
 * API Endpoint para receber métricas de Core Web Vitals
 *
 * Este endpoint recebe dados de métricas de performance enviados pelo
 * componente WebVitalsMonitor e os armazena para análise posterior.
 */

import { createClient } from '@vercel/kv';
import type { APIRoute } from 'astro';
import { logger } from '../../../utils/logger';

// Tipos para métricas
interface WebVitalMetric {
  name: 'LCP' | 'FID' | 'CLS' | 'TTFB' | 'FCP';
  value: number;
  rating: 'good' | 'needs-improvement' | 'poor';
  id: string;
  pageId: string;
  timestamp: number;
  attribution?: {
    element: any;
    navigationEntry?: any;
    largestShiftEntry?: any;
    largestShiftSource?: string;
    loadState?: string;
    eventEntry?: any;
  };
  device?: {
    userAgent: string;
    viewport: {
      width: number;
      height: number;
    };
    screenSize: {
      width: number;
      height: number;
    };
    devicePixelRatio: number;
    connection?: {
      effectiveType?: string;
      downlink?: number;
      rtt?: number;
      saveData?: boolean;
    };
    memory?: {
      jsHeapSizeLimit?: number;
      totalJSHeapSize?: number;
      usedJSHeapSize?: number;
    };
  };
  navigation?: {
    type: string;
    redirectCount: number;
    isBackForward: boolean;
    isReload: boolean;
  };
  user?: {
    language: string;
    languages: string[];
    doNotTrack: string;
    cookieEnabled: boolean;
  };
}

// Configuração do cliente KV
const kv = createClient({
  url: import.meta.env.KV_REST_API_URL || 'https://example.upstash.io',
  token: import.meta.env.KV_REST_API_TOKEN || 'example_token',
});

// Limite de armazenamento para métricas (últimas 1000 por tipo)
const METRICS_LIMIT = 1000;

// Função para validar métrica
function isValidMetric(metric: any): metric is WebVitalMetric {
  return (
    metric &&
    typeof metric === 'object' &&
    typeof metric.name === 'string' &&
    typeof metric.value === 'number' &&
    typeof metric.id === 'string' &&
    typeof metric.timestamp === 'number'
  );
}

// Função para sanitizar métrica (remover dados sensíveis ou muito grandes)
function sanitizeMetric(metric: WebVitalMetric): WebVitalMetric {
  // Criar cópia para não modificar o original
  const sanitized = { ...metric };

  // Limitar tamanho de atribuição de elemento
  if (sanitized.attribution?.element) {
    // Limitar tamanho do textContent
    if (
      sanitized.attribution.element.textContent &&
      sanitized.attribution.element.textContent.length > 100
    ) {
      sanitized.attribution.element.textContent = `${sanitized.attribution.element.textContent.substring(0, 100)}...`;
    }
  }

  return sanitized;
}

// Função para armazenar métrica no KV
async function storeMetric(metric: WebVitalMetric) {
  try {
    // Chave para a lista de métricas deste tipo
    const metricListKey = `web-vitals:${metric.name.toLowerCase()}`;

    // Chave para esta métrica específica
    const metricKey = `web-vitals:${metric.name.toLowerCase()}:${metric.id}`;

    // Sanitizar métrica
    const sanitizedMetric = sanitizeMetric(metric);

    // Armazenar métrica individual
    await kv.set(metricKey, JSON.stringify(sanitizedMetric));

    // Adicionar ID à lista de métricas deste tipo
    await kv.lpush(metricListKey, metric.id);

    // Manter apenas as últimas N métricas
    await kv.ltrim(metricListKey, 0, METRICS_LIMIT - 1);

    // Atualizar estatísticas agregadas
    await updateAggregatedStats(metric);

    return true;
  } catch (error) {
    logger.error('Erro ao armazenar métrica:', error);
    return false;
  }
}

// Função para atualizar estatísticas agregadas
async function updateAggregatedStats(metric: WebVitalMetric) {
  try {
    // Chave para estatísticas agregadas deste tipo de métrica
    const statsKey = `web-vitals:stats:${metric.name.toLowerCase()}`;

    // Chave para estatísticas por página
    const pageStatsKey = `web-vitals:stats:${metric.name.toLowerCase()}:${metric.pageId}`;

    // Obter estatísticas atuais
    const statsJson = await kv.get(statsKey);
    const stats = statsJson
      ? JSON.parse(statsJson)
      : {
          count: 0,
          sum: 0,
          min: Number.POSITIVE_INFINITY,
          max: Number.NEGATIVE_INFINITY,
          ratings: {
            good: 0,
            'needs-improvement': 0,
            poor: 0,
          },
          lastUpdated: 0,
        };

    // Obter estatísticas da página
    const pageStatsJson = await kv.get(pageStatsKey);
    const pageStats = pageStatsJson
      ? JSON.parse(pageStatsJson)
      : {
          count: 0,
          sum: 0,
          min: Number.POSITIVE_INFINITY,
          max: Number.NEGATIVE_INFINITY,
          ratings: {
            good: 0,
            'needs-improvement': 0,
            poor: 0,
          },
          lastUpdated: 0,
        };

    // Atualizar estatísticas globais
    stats.count++;
    stats.sum += metric.value;
    stats.min = Math.min(stats.min, metric.value);
    stats.max = Math.max(stats.max, metric.value);
    stats.ratings[metric.rating]++;
    stats.lastUpdated = Date.now();

    // Atualizar estatísticas da página
    pageStats.count++;
    pageStats.sum += metric.value;
    pageStats.min = Math.min(pageStats.min, metric.value);
    pageStats.max = Math.max(pageStats.max, metric.value);
    pageStats.ratings[metric.rating]++;
    pageStats.lastUpdated = Date.now();

    // Salvar estatísticas atualizadas
    await kv.set(statsKey, JSON.stringify(stats));
    await kv.set(pageStatsKey, JSON.stringify(pageStats));

    return true;
  } catch (error) {
    logger.error('Erro ao atualizar estatísticas agregadas:', error);
    return false;
  }
}

// Endpoint para receber métricas
export const POST: APIRoute = async ({ request }) => {
  try {
    // Verificar método
    if (request.method !== 'POST') {
      return new Response(JSON.stringify({ error: 'Método não permitido' }), {
        status: 405,
        headers: {
          'Content-Type': 'application/json',
        },
      });
    }

    // Obter dados da requisição
    const metric = await request.json();

    // Validar métrica
    if (!isValidMetric(metric)) {
      return new Response(JSON.stringify({ error: 'Dados de métrica inválidos' }), {
        status: 400,
        headers: {
          'Content-Type': 'application/json',
        },
      });
    }

    // Armazenar métrica
    const success = await storeMetric(metric);

    if (success) {
      return new Response(JSON.stringify({ success: true }), {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      });
    }
    return new Response(JSON.stringify({ error: 'Erro ao armazenar métrica' }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  } catch (error) {
    logger.error('Erro ao processar requisição de métrica:', error);

    return new Response(JSON.stringify({ error: 'Erro interno do servidor' }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  }
};
