# Sistema de Auditoria

Este documento descreve o sistema de auditoria implementado na plataforma Estação da Alfabetização, incluindo os tipos de eventos registrados, políticas de retenção e ferramentas disponíveis para visualização e análise de logs.

## Visão Geral

O sistema de auditoria é responsável por registrar ações sensíveis realizadas no sistema, como tentativas de login, alterações de permissões, acesso a recursos protegidos e operações administrativas. Esses registros são essenciais para:

- Rastreabilidade de ações
- Investigação de incidentes de segurança
- Conformidade com requisitos regulatórios
- Análise de comportamento de usuários
- Detecção de atividades suspeitas

## Tipos de Eventos

Os eventos de auditoria são categorizados em diferentes tipos para facilitar a análise e filtragem:

### Eventos de Autenticação
- `auth.login.success` - Login bem-sucedido
- `auth.login.failed` - Tentativa de login falha
- `auth.logout` - Logout do sistema
- `auth.password.changed` - Alteração de senha
- `auth.password.reset.requested` - Solicitação de redefinição de senha
- `auth.password.reset.completed` - Redefinição de senha concluída
- `auth.email.changed` - Alteração de email
- `auth.profile.updated` - Atualização de perfil
- `auth.2fa.enabled` - Autenticação de dois fatores ativada
- `auth.2fa.disabled` - Autenticação de dois fatores desativada
- `auth.2fa.verification.success` - Verificação de dois fatores bem-sucedida
- `auth.2fa.verification.failed` - Verificação de dois fatores falha

### Eventos de Autorização
- `auth.permission.changed` - Alteração de permissão
- `auth.permission.granted` - Permissão concedida
- `auth.permission.denied` - Permissão negada
- `auth.role.assigned` - Papel atribuído
- `auth.role.removed` - Papel removido

### Eventos de Administração
- `admin.user.created` - Usuário criado
- `admin.user.updated` - Usuário atualizado
- `admin.user.deleted` - Usuário excluído
- `admin.user.blocked` - Usuário bloqueado
- `admin.user.unblocked` - Usuário desbloqueado
- `admin.role.created` - Papel criado
- `admin.role.updated` - Papel atualizado
- `admin.role.deleted` - Papel excluído
- `admin.permission.created` - Permissão criada
- `admin.permission.updated` - Permissão atualizada
- `admin.permission.deleted` - Permissão excluída
- `admin.action` - Ação administrativa genérica
- `admin.settings.changed` - Configurações alteradas

### Eventos de Acesso a Recursos
- `resource.accessed` - Recurso acessado
- `resource.created` - Recurso criado
- `resource.updated` - Recurso atualizado
- `resource.deleted` - Recurso excluído
- `resource.sensitive.accessed` - Dados sensíveis acessados
- `resource.sensitive.exported` - Dados sensíveis exportados

### Eventos de Sistema
- `system.started` - Sistema iniciado
- `system.stopped` - Sistema parado
- `system.error` - Erro de sistema
- `system.warning` - Aviso de sistema
- `system.config.changed` - Configuração alterada
- `system.maintenance` - Modo de manutenção
- `system.backup.created` - Backup criado
- `system.backup.restored` - Backup restaurado

### Eventos de Conteúdo
- `content.viewed` - Conteúdo visualizado
- `content.created` - Conteúdo criado
- `content.updated` - Conteúdo atualizado
- `content.deleted` - Conteúdo excluído
- `content.published` - Conteúdo publicado
- `content.unpublished` - Conteúdo despublicado

### Eventos de Relatórios
- `report.generated` - Relatório gerado
- `report.export.created` - Exportação criada
- `report.import.completed` - Importação concluída

### Eventos de Auditoria
- `audit.viewed` - Logs de auditoria visualizados
- `audit.exported` - Logs de auditoria exportados
- `audit.settings.changed` - Configurações de auditoria alteradas
- `audit.archived` - Logs de auditoria arquivados
- `audit.purged` - Logs de auditoria excluídos

## Níveis de Severidade

Cada evento de auditoria possui um nível de severidade que indica sua importância:

- **INFO** - Eventos informativos normais
- **WARNING** - Eventos que merecem atenção, mas não são críticos
- **ERROR** - Eventos que indicam falhas ou problemas
- **CRITICAL** - Eventos críticos que requerem atenção imediata

## Políticas de Retenção

As políticas de retenção definem por quanto tempo os logs de auditoria são mantidos no sistema antes de serem arquivados ou excluídos:

| Categoria | Período de Retenção | Arquivamento | Formato |
|-----------|---------------------|--------------|---------|
| Segurança Crítica | 2 anos | Sim | JSON compactado |
| Segurança Padrão | 1 ano | Sim | JSON compactado |
| Acesso a Recursos | 6 meses | Sim | CSV compactado |
| Eventos de Sistema | 3 meses | Sim | JSON compactado |
| Atividades Padrão | 3 meses | Não | - |

## Arquivamento de Logs

Os logs de auditoria são arquivados periodicamente para liberar espaço no banco de dados principal, mantendo-os disponíveis para consulta quando necessário:

1. Os logs que atingirem o período de retenção são exportados para arquivos JSON ou CSV
2. Os arquivos são compactados para economizar espaço
3. Os logs são marcados como arquivados no banco de dados
4. Periodicamente, logs arquivados são removidos do banco de dados

## Visualização e Análise

O sistema oferece várias ferramentas para visualização e análise de logs de auditoria:

### Painel de Auditoria

O painel de auditoria permite aos administradores:

- Visualizar eventos recentes
- Filtrar eventos por tipo, usuário, recurso, severidade e período
- Exportar logs para análise externa
- Visualizar estatísticas e tendências

### Relatórios de Auditoria

Os relatórios de auditoria fornecem análises mais detalhadas:

- Distribuição de eventos por tipo
- Atividade por usuário
- Tendências temporais
- Alertas de segurança

## Implementação Técnica

O sistema de auditoria é implementado através dos seguintes componentes:

- `auditService.ts` - Serviço principal para registro e consulta de eventos
- `auditRepository.ts` - Repositório para acesso ao banco de dados
- `auditRetentionService.ts` - Serviço para gerenciamento de retenção e arquivamento
- `auditMiddleware.ts` - Middleware para registro automático de eventos em rotas
- `tab_audit_log` - Tabela principal para armazenamento de logs
- `tab_audit_retention_policy` - Tabela para configuração de políticas de retenção
- `tab_audit_archive` - Tabela para registro de arquivos de arquivamento

## Boas Práticas

Para garantir a eficácia do sistema de auditoria:

1. **Registre eventos significativos** - Foque em eventos que têm valor para segurança e conformidade
2. **Use níveis de severidade adequados** - Reserve níveis críticos para eventos realmente importantes
3. **Inclua contexto suficiente** - Registre informações que permitam entender o que aconteceu
4. **Proteja os logs** - Trate logs de auditoria como dados sensíveis
5. **Revise regularmente** - Estabeleça processos para revisão periódica dos logs

## Configuração

As configurações do sistema de auditoria podem ser ajustadas através do painel administrativo ou diretamente no banco de dados:

- Tipos de eventos a serem registrados
- Períodos de retenção por categoria
- Configurações de arquivamento
- Alertas automáticos

## Referências

- [OWASP Logging Cheat Sheet](https://cheatsheetseries.owasp.org/cheatsheets/Logging_Cheat_Sheet.html)
- [NIST SP 800-92: Guide to Computer Security Log Management](https://nvlpubs.nist.gov/nistpubs/Legacy/SP/nistspecialpublication800-92.pdf)
