---
import ServerSideAccordion from '../../components/common/ServerSideAccordion.astro';
import ServerSideModal from '../../components/common/ServerSideModal.astro';
import ServerSideTabs from '../../components/common/ServerSideTabs.astro';
import ServerSideForm from '../../components/forms/ServerSideForm.astro';

// Example data for tabs
const tabs = [
  {
    id: 'tab1',
    label: 'Sobre',
    content: `
      <h2>Sobre o Projeto</h2>
      <p>A Estação da Alfabetização é uma plataforma educacional dedicada a apoiar professores e alunos no processo de alfabetização.</p>
      <p>Nossa missão é fornecer recursos de alta qualidade e ferramentas eficazes para tornar o aprendizado da leitura e escrita mais acessível e eficiente.</p>
    `,
  },
  {
    id: 'tab2',
    label: 'Recursos',
    content: `
      <h2>Recursos Disponíveis</h2>
      <ul>
        <li>Materiais didáticos para alfabetização</li>
        <li>Atividades interativas para alunos</li>
        <li>Guias para professores</li>
        <li>Avaliações de progresso</li>
        <li>Comunidade de educadores</li>
      </ul>
    `,
  },
  {
    id: 'tab3',
    label: 'Contato',
    content: `
      <h2>Entre em Contato</h2>
      <p>Estamos disponíveis para responder suas dúvidas e receber feedback.</p>
      <p>Email: <EMAIL></p>
      <p>Telefone: (11) 1234-5678</p>
    `,
  },
];

// Example data for accordion
const faqItems = [
  {
    id: 'faq1',
    title: 'Como posso acessar os materiais didáticos?',
    content: `
      <p>Para acessar os materiais didáticos, você precisa criar uma conta na plataforma. Após o login, navegue até a seção "Materiais" e escolha a categoria desejada.</p>
      <p>Todos os materiais podem ser visualizados online ou baixados em formato PDF para uso offline.</p>
    `,
  },
  {
    id: 'faq2',
    title: 'Os recursos são gratuitos?',
    content: `
      <p>Oferecemos uma combinação de recursos gratuitos e premium:</p>
      <ul>
        <li>Recursos básicos: disponíveis gratuitamente para todos os usuários</li>
        <li>Recursos premium: disponíveis para assinantes do plano Pro</li>
      </ul>
      <p>Escolas públicas podem solicitar acesso gratuito aos recursos premium através do nosso programa de parceria educacional.</p>
    `,
  },
  {
    id: 'faq3',
    title: 'Como posso contribuir com a plataforma?',
    content: `
      <p>Existem várias maneiras de contribuir com a Estação da Alfabetização:</p>
      <ul>
        <li>Compartilhando materiais didáticos que você desenvolveu</li>
        <li>Participando da comunidade de educadores</li>
        <li>Fornecendo feedback sobre os recursos existentes</li>
        <li>Divulgando a plataforma para outros educadores</li>
      </ul>
      <p>Para mais informações, entre em contato com nossa equipe.</p>
    `,
  },
];
---

<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Componentes Server-Side | Estação da Alfabetização</title>
  <style>
    :root {
      --color-primary: #4a6cf7;
      --color-primary-dark: #3a5ce5;
      --color-text: #334155;
      --color-text-light: #64748b;
      --color-text-dark: #1e293b;
      --color-bg: #f8fafc;
      --color-bg-dark: #f1f5f9;
    }
    
    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }
    
    body {
      font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      line-height: 1.6;
      color: var(--color-text);
      background-color: var(--color-bg);
    }
    
    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 2rem;
    }
    
    header {
      margin-bottom: 2rem;
      text-align: center;
    }
    
    h1 {
      color: var(--color-text-dark);
      margin-bottom: 0.5rem;
    }
    
    .subtitle {
      color: var(--color-text-light);
      font-size: 1.125rem;
    }
    
    section {
      margin-bottom: 3rem;
      background-color: white;
      border-radius: 8px;
      padding: 2rem;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }
    
    h2 {
      color: var(--color-text-dark);
      margin-bottom: 1rem;
      border-bottom: 2px solid var(--color-bg-dark);
      padding-bottom: 0.5rem;
    }
    
    .description {
      margin-bottom: 1.5rem;
    }
    
    .example-wrapper {
      margin-top: 2rem;
    }
  </style>
</head>
<body>
  <div class="container">
    <header>
      <h1>Componentes Server-Side</h1>
      <p class="subtitle">Exemplos de componentes interativos sem JavaScript client-side</p>
    </header>
    
    <section id="tabs">
      <h2>Tabs</h2>
      <p class="description">
        Este componente implementa tabs usando fragmentos de URL (#) para controlar qual tab está ativa.
        Não requer JavaScript no cliente e funciona com navegação para trás/frente do navegador.
      </p>
      
      <div class="example-wrapper">
        <ServerSideTabs tabs={tabs} defaultTab="tab1" />
      </div>
    </section>
    
    <section id="accordion">
      <h2>Accordion</h2>
      <p class="description">
        Este componente implementa um accordion usando parâmetros de URL para controlar quais itens estão expandidos.
        Funciona sem JavaScript no cliente e mantém o estado entre recarregamentos de página.
      </p>
      
      <div class="example-wrapper">
        <ServerSideAccordion items={faqItems} paramName="faq" />
      </div>
    </section>
    
    <section id="modal">
      <h2>Modal</h2>
      <p class="description">
        Este componente implementa um modal de diálogo usando parâmetros de URL para controlar sua visibilidade.
        Não requer JavaScript no cliente e funciona com navegação para trás/frente do navegador.
      </p>
      
      <div class="example-wrapper">
        <ServerSideModal id="example-modal" title="Exemplo de Modal" paramName="dialog">
          <p slot="trigger">Abrir Modal de Exemplo</p>
          
          <p>Este é um exemplo de modal server-side que não requer JavaScript no cliente.</p>
          <p>O modal é controlado através de parâmetros de URL, permitindo que funcione mesmo com JavaScript desabilitado.</p>
          
          <div slot="footer">
            <a href="?dialog=" class="modal-button">Fechar</a>
          </div>
        </ServerSideModal>
      </div>
    </section>
    
    <section id="form">
      <h2>Formulário</h2>
      <p class="description">
        Este componente implementa um formulário com validação server-side e feedback de erros.
        Não requer JavaScript no cliente e preserva os dados do formulário em caso de erro.
      </p>
      
      <div class="example-wrapper">
        <ServerSideForm 
          formId="example-form"
          successMessage="Mensagem enviada com sucesso! Entraremos em contato em breve."
          errorMessage="Ocorreu um erro ao enviar a mensagem. Por favor, tente novamente."
        />
      </div>
    </section>
  </div>
</body>
</html>
