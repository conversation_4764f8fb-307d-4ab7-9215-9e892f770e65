/**
 * API de Cancelamento de Documento Fiscal
 *
 * Endpoint para cancelar um documento fiscal.
 * Parte da implementação da tarefa 8.7.1 - Integração fiscal
 */

import type { APIRoute } from 'astro';
import { FiscalDocumentRepository } from '../../../../domain/repositories/FiscalDocumentRepository';
import { FiscalProviderService } from '../../../../domain/services/FiscalProviderService';
import { CancelFiscalDocumentUseCase } from '../../../../domain/usecases/fiscal/CancelFiscalDocumentUseCase';
import { PostgresFiscalDocumentRepository } from '../../../../infrastructure/database/repositories/PostgresFiscalDocumentRepository';
import { EfiPayFiscalProvider } from '../../../../infrastructure/services/EfiPayFiscalProvider';

// Inicializar repositório
const fiscalDocumentRepository: FiscalDocumentRepository = new PostgresFiscalDocumentRepository();

// Inicializar provedor fiscal
const fiscalProviderService: FiscalProviderService = new EfiPayFiscalProvider();

// Inicializar caso de uso
const cancelFiscalDocumentUseCase = new CancelFiscalDocumentUseCase(
  fiscalDocumentRepository,
  fiscalProviderService
);

// Configurar provedor fiscal
(async () => {
  try {
    // Em um cenário real, estas configurações viriam do banco de dados
    await fiscalProviderService.initialize({
      apiKey: process.env.EFI_PAY_API_KEY || 'sandbox_api_key',
      apiSecret: process.env.EFI_PAY_API_SECRET || 'sandbox_api_secret',
      environment: 'homologation',
      companyDocument: '12345678000199',
      companyName: 'Estação da Alfabetização LTDA',
      timeout: 30000,
    });
  } catch (error) {
    console.error('Erro ao inicializar provedor fiscal:', error);
  }
})();

export const POST: APIRoute = async ({ params, request }) => {
  try {
    const { id } = params;

    if (!id) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'ID do documento é obrigatório.',
        }),
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    // Obter dados da requisição
    const body = await request.json();
    const { reason } = body;

    if (!reason || reason.trim().length < 5) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Motivo do cancelamento é obrigatório e deve ter pelo menos 5 caracteres.',
        }),
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    // Cancelar documento fiscal
    const result = await cancelFiscalDocumentUseCase.execute({
      documentId: id,
      reason,
    });

    if (result.success) {
      return new Response(
        JSON.stringify({
          success: true,
          document: result.document,
        }),
        {
          status: 200,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }
    return new Response(
      JSON.stringify({
        success: false,
        error: result.error || 'Erro desconhecido ao cancelar documento fiscal.',
      }),
      {
        status: 400,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  } catch (error) {
    console.error('Erro ao processar cancelamento de documento fiscal:', error);

    return new Response(
      JSON.stringify({
        success: false,
        error:
          'Ocorreu um erro ao processar o cancelamento do documento fiscal. Por favor, tente novamente.',
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  }
};
