/**
 * File Sitemap Repository
 *
 * Implementação do repositório de sitemaps usando arquivos.
 * Parte da implementação da tarefa 8.9.1 - Otimização para buscadores
 */

import fs from 'node:fs/promises';
import path from 'node:path';
import { Sitemap, SitemapUrl } from '../../../domain/entities/Sitemap';
import { SitemapRepository } from '../../../domain/repositories/SitemapRepository';

export class FileSitemapRepository implements SitemapRepository {
  private baseDir: string;
  private sitemaps: Record<string, Sitemap>;

  constructor(baseDir: string) {
    this.baseDir = baseDir;
    this.sitemaps = {};
  }

  /**
   * Inicializa o repositório
   */
  async initialize(): Promise<void> {
    try {
      // Criar diretório base se não existir
      await fs.mkdir(this.baseDir, { recursive: true });

      // Carregar sitemaps existentes
      const files = await fs.readdir(this.baseDir);

      for (const file of files) {
        if (file.endsWith('.json')) {
          const name = file.replace('.json', '');
          const content = await fs.readFile(path.join(this.baseDir, file), 'utf-8');
          const data = JSON.parse(content);

          this.sitemaps[name] = new Sitemap({
            urls: data.urls,
            lastUpdated: new Date(data.lastUpdated),
          });
        }
      }

      // Criar sitemap principal se não existir
      if (!this.sitemaps.main) {
        this.sitemaps.main = new Sitemap();
        await this.saveSitemapToFile('main', this.sitemaps.main);
      }
    } catch (error) {
      console.error('Erro ao inicializar repositório de sitemaps:', error);
    }
  }

  /**
   * Salva um sitemap
   */
  async save(sitemap: Sitemap): Promise<Sitemap> {
    try {
      // Salvar sitemap principal
      this.sitemaps.main = sitemap;
      await this.saveSitemapToFile('main', sitemap);

      return sitemap;
    } catch (error) {
      console.error('Erro ao salvar sitemap:', error);
      return sitemap;
    }
  }

  /**
   * Obtém o sitemap principal
   */
  async getMainSitemap(): Promise<Sitemap | null> {
    try {
      return this.sitemaps.main || null;
    } catch (error) {
      console.error('Erro ao obter sitemap principal:', error);
      return null;
    }
  }

  /**
   * Obtém um sitemap pelo nome
   */
  async getByName(name: string): Promise<Sitemap | null> {
    try {
      return this.sitemaps[name] || null;
    } catch (error) {
      console.error(`Erro ao obter sitemap ${name}:`, error);
      return null;
    }
  }

  /**
   * Adiciona uma URL ao sitemap
   */
  async addUrl(url: SitemapUrl, sitemapName = 'main'): Promise<boolean> {
    try {
      // Obter sitemap
      const sitemap = this.sitemaps[sitemapName];

      if (!sitemap) {
        // Criar novo sitemap se não existir
        this.sitemaps[sitemapName] = new Sitemap();
        this.sitemaps[sitemapName].addUrl(url);
      } else {
        // Adicionar URL ao sitemap existente
        sitemap.addUrl(url);
      }

      // Salvar sitemap
      await this.saveSitemapToFile(sitemapName, this.sitemaps[sitemapName]);

      return true;
    } catch (error) {
      console.error('Erro ao adicionar URL ao sitemap:', error);
      return false;
    }
  }

  /**
   * Remove uma URL do sitemap
   */
  async removeUrl(loc: string, sitemapName = 'main'): Promise<boolean> {
    try {
      // Obter sitemap
      const sitemap = this.sitemaps[sitemapName];

      if (!sitemap) {
        return false;
      }

      // Remover URL do sitemap
      sitemap.removeUrl(loc);

      // Salvar sitemap
      await this.saveSitemapToFile(sitemapName, sitemap);

      return true;
    } catch (error) {
      console.error('Erro ao remover URL do sitemap:', error);
      return false;
    }
  }

  /**
   * Atualiza uma URL no sitemap
   */
  async updateUrl(
    loc: string,
    updates: Partial<Omit<SitemapUrl, 'loc'>>,
    sitemapName = 'main'
  ): Promise<boolean> {
    try {
      // Obter sitemap
      const sitemap = this.sitemaps[sitemapName];

      if (!sitemap) {
        return false;
      }

      // Atualizar URL no sitemap
      sitemap.updateUrl(loc, updates);

      // Salvar sitemap
      await this.saveSitemapToFile(sitemapName, sitemap);

      return true;
    } catch (error) {
      console.error('Erro ao atualizar URL no sitemap:', error);
      return false;
    }
  }

  /**
   * Gera o XML do sitemap
   */
  async generateXml(sitemapName = 'main'): Promise<string> {
    try {
      // Obter sitemap
      const sitemap = this.sitemaps[sitemapName];

      if (!sitemap) {
        return '';
      }

      // Gerar XML
      const xml = sitemap.generateXml();

      // Salvar XML em arquivo
      await fs.writeFile(path.join(this.baseDir, `${sitemapName}.xml`), xml, 'utf-8');

      return xml;
    } catch (error) {
      console.error('Erro ao gerar XML do sitemap:', error);
      return '';
    }
  }

  /**
   * Gera o índice de sitemaps
   */
  async generateSitemapIndex(): Promise<string> {
    try {
      // Obter nomes de sitemaps
      const sitemapNames = Object.keys(this.sitemaps);

      // Criar lista de sitemaps para o índice
      const sitemaps = sitemapNames.map((name) => ({
        loc: `${process.env.BASE_URL}/sitemaps/${name}.xml`,
        lastmod: this.sitemaps[name].lastUpdated,
      }));

      // Gerar XML do índice
      const xml = Sitemap.generateSitemapIndex(sitemaps);

      // Salvar XML em arquivo
      await fs.writeFile(path.join(this.baseDir, 'sitemap-index.xml'), xml, 'utf-8');

      return xml;
    } catch (error) {
      console.error('Erro ao gerar índice de sitemaps:', error);
      return '';
    }
  }

  /**
   * Obtém todas as URLs do sitemap
   */
  async getAllUrls(sitemapName = 'main'): Promise<SitemapUrl[]> {
    try {
      // Obter sitemap
      const sitemap = this.sitemaps[sitemapName];

      if (!sitemap) {
        return [];
      }

      return sitemap.urls;
    } catch (error) {
      console.error('Erro ao obter URLs do sitemap:', error);
      return [];
    }
  }

  /**
   * Verifica se uma URL existe no sitemap
   */
  async urlExists(loc: string, sitemapName = 'main'): Promise<boolean> {
    try {
      // Obter sitemap
      const sitemap = this.sitemaps[sitemapName];

      if (!sitemap) {
        return false;
      }

      // Verificar se a URL existe
      return sitemap.urls.some((url) => url.loc === loc);
    } catch (error) {
      console.error('Erro ao verificar existência de URL no sitemap:', error);
      return false;
    }
  }

  /**
   * Atualiza a data de última modificação de uma URL
   */
  async updateLastmod(loc: string, lastmod: Date, sitemapName = 'main'): Promise<boolean> {
    try {
      // Obter sitemap
      const sitemap = this.sitemaps[sitemapName];

      if (!sitemap) {
        return false;
      }

      // Atualizar data de última modificação
      sitemap.updateUrl(loc, { lastmod });

      // Salvar sitemap
      await this.saveSitemapToFile(sitemapName, sitemap);

      return true;
    } catch (error) {
      console.error('Erro ao atualizar data de última modificação:', error);
      return false;
    }
  }

  /**
   * Salva um sitemap em arquivo
   */
  private async saveSitemapToFile(name: string, sitemap: Sitemap): Promise<void> {
    try {
      // Criar diretório se não existir
      await fs.mkdir(this.baseDir, { recursive: true });

      // Salvar sitemap em arquivo JSON
      await fs.writeFile(
        path.join(this.baseDir, `${name}.json`),
        JSON.stringify({
          urls: sitemap.urls,
          lastUpdated: sitemap.lastUpdated,
        }),
        'utf-8'
      );
    } catch (error) {
      console.error(`Erro ao salvar sitemap ${name} em arquivo:`, error);
    }
  }
}
