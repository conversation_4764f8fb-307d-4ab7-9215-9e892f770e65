# Documentação de Testes Automatizados

## Visão Geral

Este documento descreve a implementação de testes automatizados no projeto Estação Alfabetização, incluindo testes unitários, de integração e end-to-end. A estratégia de testes foi projetada para garantir a qualidade do código, facilitar a manutenção e permitir a detecção precoce de problemas.

## Ferramentas Utilizadas

- **Vitest**: Framework de testes para JavaScript/TypeScript
- **Testing Library**: Biblioteca para testes de componentes
- **Playwright**: Ferramenta para testes end-to-end
- **Mock Service Worker**: Biblioteca para simular requisições HTTP
- **Vitest UI**: Interface gráfica para visualização de testes
- **Vitest Coverage**: Ferramenta para análise de cobertura de código

## Estrutura de Diretórios

```
tests/
├── setup.ts                  # Configuração global para testes
├── unit/                     # Testes unitários
│   ├── domain/               # Testes para a camada de domínio
│   │   ├── services/         # Testes para serviços
│   │   ├── utils/            # Testes para utilitários
│   │   └── entities/         # Testes para entidades
│   ├── components/           # Testes para componentes
│   └── actions/              # Testes para Astro Actions
├── integration/              # Testes de integração
│   ├── api/                  # Testes para APIs
│   └── flows/                # Testes para fluxos de negócio
└── e2e/                      # Testes end-to-end
    ├── pages/                # Testes para páginas
    └── fixtures/             # Dados de teste
```

## Configuração de Testes

### Configuração Global

O arquivo `tests/setup.ts` contém a configuração global para todos os testes, incluindo:

- Extensão dos matchers do Jest para o Testing Library
- Limpeza automática após cada teste
- Mocks para APIs globais como `fetch`, `localStorage` e `sessionStorage`
- Configuração de ambiente de teste

### Configuração do Vitest

O arquivo `vitest.config.ts` contém a configuração do Vitest, incluindo:

- Configuração de ambiente de teste
- Configuração de cobertura de código
- Configuração de mocks globais
- Configuração de resolução de módulos

## Tipos de Testes

### Testes Unitários

Os testes unitários são focados em testar unidades individuais de código de forma isolada. Eles são rápidos, confiáveis e fornecem feedback imediato sobre a qualidade do código.

#### Exemplos de Testes Unitários

- **Testes de Serviços**: Testam a lógica de negócio implementada nos serviços.
  - `AuthenticationService.test.ts`: Testa o serviço de autenticação
  - `TokenService.test.ts`: Testa o serviço de token

- **Testes de Utilitários**: Testam funções utilitárias.
  - `StringUtils.test.ts`: Testa funções de manipulação de strings

- **Testes de Componentes**: Testam componentes de interface.
  - `LoginForm.test.tsx`: Testa o componente de formulário de login
  - `RegisterForm.test.tsx`: Testa o componente de formulário de registro

### Testes de Integração

Os testes de integração verificam a interação entre diferentes partes do sistema. Eles são mais lentos que os testes unitários, mas fornecem maior confiança na integração entre componentes.

#### Exemplos de Testes de Integração

- **Testes de API**: Testam a integração com APIs.
  - `AuthApi.test.ts`: Testa a API de autenticação
  - `UserApi.test.ts`: Testa a API de usuários

- **Testes de Fluxos**: Testam fluxos de negócio completos.
  - `LoginFlow.test.ts`: Testa o fluxo de login
  - `RegistrationFlow.test.ts`: Testa o fluxo de registro

### Testes End-to-End

Os testes end-to-end simulam a interação do usuário com a aplicação em um ambiente próximo ao de produção. Eles são mais lentos, mas fornecem maior confiança na funcionalidade completa da aplicação.

#### Exemplos de Testes End-to-End

- **Testes de Páginas**: Testam páginas completas.
  - `HomePage.spec.ts`: Testa a página inicial
  - `LoginPage.spec.ts`: Testa a página de login

- **Testes de Fluxos de Usuário**: Testam fluxos completos de usuário.
  - `UserRegistrationFlow.spec.ts`: Testa o fluxo de registro de usuário
  - `PurchaseFlow.spec.ts`: Testa o fluxo de compra

## Mocks e Stubs

Para isolar os testes unitários, utilizamos mocks e stubs para simular dependências externas:

- **Mocks de Serviços**: Simulam o comportamento de serviços externos.
  - `UserRepository`: Mock do repositório de usuários
  - `TokenService`: Mock do serviço de token

- **Mocks de APIs**: Simulam requisições HTTP.
  - `fetch`: Mock global para requisições HTTP
  - `Mock Service Worker`: Biblioteca para simular APIs

- **Mocks de Armazenamento**: Simulam armazenamento local.
  - `localStorage`: Mock para localStorage
  - `sessionStorage`: Mock para sessionStorage

## Cobertura de Código

A cobertura de código é medida usando o Vitest Coverage. A meta de cobertura é de pelo menos 80% para:

- **Linhas**: Porcentagem de linhas de código executadas
- **Funções**: Porcentagem de funções executadas
- **Branches**: Porcentagem de branches (if/else) executados
- **Statements**: Porcentagem de statements executados

## Integração com CI/CD

Os testes automatizados estão integrados ao pipeline de CI/CD:

- **Pull Requests**: Todos os testes são executados em pull requests
- **Merge para Main**: Todos os testes são executados antes de merge para main
- **Deploy para Produção**: Todos os testes são executados antes de deploy para produção

## Boas Práticas

### Nomenclatura de Testes

- Use nomes descritivos para testes
- Siga o padrão "should [expected behavior] when [condition]"
- Agrupe testes relacionados em blocos `describe`

### Organização de Testes

- Um arquivo de teste por unidade testada
- Organize testes em blocos `describe` para agrupar testes relacionados
- Use `beforeEach` e `afterEach` para configuração e limpeza

### Asserções

- Use asserções específicas e descritivas
- Evite asserções múltiplas em um único teste
- Use matchers apropriados para o tipo de asserção

## Executando Testes

### Testes Unitários

```bash
# Executar todos os testes unitários
npm run test

# Executar testes em modo de observação
npm run test:watch

# Executar testes com interface gráfica
npm run test:ui

# Executar testes com cobertura
npm run test:coverage
```

### Testes de Integração

```bash
# Executar todos os testes de integração
npm run test:integration

# Executar testes de integração específicos
npm run test:integration -- -t "nome do teste"
```

### Testes End-to-End

```bash
# Executar todos os testes end-to-end
npm run test:e2e

# Executar testes end-to-end em modo de observação
npm run test:e2e:watch

# Executar testes end-to-end em modo de depuração
npm run test:e2e:debug
```

## Conclusão

A implementação de testes automatizados no projeto Estação Alfabetização segue as melhores práticas da indústria e fornece uma base sólida para garantir a qualidade do código. A combinação de testes unitários, de integração e end-to-end fornece uma cobertura abrangente e permite a detecção precoce de problemas.
