/**
 * Default Slug Service
 *
 * Implementação padrão do serviço de slugs.
 * Parte da implementação da tarefa 8.8.3 - Gestão de conteúdo
 */

import { SlugService } from '../../domain/services/SlugService';

export class DefaultSlugService implements SlugService {
  /**
   * Gera um slug a partir de um texto
   */
  generate(text: string): string {
    if (!text) {
      return '';
    }

    return this.normalize(text);
  }

  /**
   * Normaliza um slug
   */
  normalize(slug: string): string {
    if (!slug) {
      return '';
    }

    // Converter para minúsculas
    let normalizedSlug = slug.toLowerCase();

    // Remover acentos
    normalizedSlug = this.removeAccents(normalizedSlug);

    // Substituir espaços por hífens
    normalizedSlug = normalizedSlug.replace(/\s+/g, '-');

    // Remover caracteres especiais
    normalizedSlug = normalizedSlug.replace(/[^a-z0-9-]/g, '');

    // Remover hífens duplicados
    normalizedSlug = normalizedSlug.replace(/-+/g, '-');

    // Remover hífens no início e no fim
    normalizedSlug = normalizedSlug.replace(/^-+|-+$/g, '');

    return normalizedSlug;
  }

  /**
   * Verifica se um slug é válido
   */
  isValid(slug: string): boolean {
    if (!slug) {
      return false;
    }

    // Verificar se o slug contém apenas caracteres válidos
    return /^[a-z0-9-]+$/.test(slug);
  }

  /**
   * Remove acentos de um texto
   */
  private removeAccents(text: string): string {
    return text.normalize('NFD').replace(/[\u0300-\u036f]/g, '');
  }
}
