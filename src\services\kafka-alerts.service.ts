/**
 * Serviço de alertas para o Kafka
 * 
 * Este serviço implementa funcionalidades de alertas para o Kafka,
 * incluindo detecção de problemas, notificações e registro de alertas.
 */

import { v4 as uuidv4 } from 'uuid';
import { 
  kafkaAlertConfigs, 
  alertChannelConfigs, 
  KafkaAlertType, 
  KafkaAlertSeverity, 
  KafkaAlertChannel 
} from '../config/kafka-alerts.config';
import { kafkaLoggingService } from './kafka-logging.service';
import { queryHelper } from '../helpers/queryHelper';
import { notificationService } from './notification/NotificationService';
import { kafka } from '../config/kafka';
import { offsetManagementService } from './offsetManagementService';

// Interface para alerta
export interface KafkaAlert {
  id: string;
  type: KafkaAlertType;
  severity: KafkaAlertSeverity;
  timestamp: string;
  message: string;
  details?: Record<string, any>;
  status: 'active' | 'resolved' | 'acknowledged';
  resolvedAt?: string;
  acknowledgedBy?: string;
  acknowledgedAt?: string;
}

// Classe para o serviço de alertas do Kafka
class KafkaAlertService {
  private static instance: KafkaAlertService;
  private activeAlerts: Map<string, KafkaAlert> = new Map();
  private alertCooldowns: Map<string, number> = new Map();
  private monitorTimers: Map<KafkaAlertType, NodeJS.Timeout> = new Map();
  private admin = kafka.admin();
  private isConnected = false;
  
  /**
   * Construtor privado para implementar Singleton
   */
  private constructor() {
    this.initialize();
  }
  
  /**
   * Obtém a instância única do serviço
   */
  public static getInstance(): KafkaAlertService {
    if (!KafkaAlertService.instance) {
      KafkaAlertService.instance = new KafkaAlertService();
    }
    return KafkaAlertService.instance;
  }
  
  /**
   * Inicializa o serviço de alertas
   */
  private async initialize(): Promise<void> {
    try {
      // Conectar ao admin do Kafka
      await this.admin.connect();
      this.isConnected = true;
      
      // Iniciar monitoramento para cada tipo de alerta
      Object.values(KafkaAlertType).forEach(alertType => {
        const config = kafkaAlertConfigs[alertType];
        
        if (config.enabled) {
          this.setupMonitoring(alertType);
        }
      });
      
      // Carregar alertas ativos do banco de dados
      await this.loadActiveAlerts();
      
      kafkaLoggingService.info('kafka.alerts', 'Serviço de alertas do Kafka inicializado');
    } catch (error) {
      kafkaLoggingService.error('kafka.alerts', 'Erro ao inicializar serviço de alertas do Kafka', error);
    }
  }
  
  /**
   * Carrega alertas ativos do banco de dados
   */
  private async loadActiveAlerts(): Promise<void> {
    try {
      const result = await queryHelper.query(
        `SELECT
          alert_id,
          type,
          severity,
          timestamp,
          message,
          details,
          status,
          resolved_at,
          acknowledged_by,
          acknowledged_at
        FROM
          tab_kafka_alerts
        WHERE
          status = 'active'
        ORDER BY
          timestamp DESC`
      );
      
      for (const row of result.rows) {
        const alert: KafkaAlert = {
          id: row.alert_id,
          type: row.type as KafkaAlertType,
          severity: row.severity as KafkaAlertSeverity,
          timestamp: row.timestamp,
          message: row.message,
          details: row.details,
          status: row.status,
          resolvedAt: row.resolved_at,
          acknowledgedBy: row.acknowledged_by,
          acknowledgedAt: row.acknowledged_at,
        };
        
        this.activeAlerts.set(alert.id, alert);
      }
      
      kafkaLoggingService.info('kafka.alerts', `Carregados ${this.activeAlerts.size} alertas ativos`);
    } catch (error) {
      kafkaLoggingService.error('kafka.alerts', 'Erro ao carregar alertas ativos', error);
    }
  }
  
  /**
   * Configura monitoramento para um tipo de alerta
   * @param alertType - Tipo de alerta
   */
  private setupMonitoring(alertType: KafkaAlertType): void {
    // Limpar timer existente
    if (this.monitorTimers.has(alertType)) {
      clearInterval(this.monitorTimers.get(alertType));
    }
    
    const config = kafkaAlertConfigs[alertType];
    let checkInterval = 60000; // Padrão: 1 minuto
    
    // Definir intervalo de verificação com base no tipo de alerta
    switch (alertType) {
      case KafkaAlertType.BROKER_DOWN:
        checkInterval = 30000; // 30 segundos
        break;
      case KafkaAlertType.CONSUMER_LAG:
        checkInterval = config.additionalConfig?.checkInterval * 1000 || 60000;
        break;
      case KafkaAlertType.DISK_SPACE:
        checkInterval = config.additionalConfig?.checkInterval * 1000 || 300000;
        break;
      case KafkaAlertType.HIGH_CPU_USAGE:
      case KafkaAlertType.HIGH_MEMORY_USAGE:
        checkInterval = config.additionalConfig?.checkInterval * 1000 || 60000;
        break;
      default:
        checkInterval = 60000; // 1 minuto
    }
    
    // Configurar timer para verificação periódica
    const timer = setInterval(async () => {
      try {
        await this.checkAlert(alertType);
      } catch (error) {
        kafkaLoggingService.error('kafka.alerts', `Erro ao verificar alerta ${alertType}`, error);
      }
    }, checkInterval);
    
    this.monitorTimers.set(alertType, timer);
  }
  
  /**
   * Verifica um tipo específico de alerta
   * @param alertType - Tipo de alerta
   */
  private async checkAlert(alertType: KafkaAlertType): Promise<void> {
    if (!this.isConnected) {
      return;
    }
    
    try {
      switch (alertType) {
        case KafkaAlertType.BROKER_DOWN:
          await this.checkBrokerDown();
          break;
        case KafkaAlertType.CONSUMER_LAG:
          await this.checkConsumerLag();
          break;
        case KafkaAlertType.UNDER_REPLICATED_PARTITIONS:
          await this.checkUnderReplicatedPartitions();
          break;
        case KafkaAlertType.OFFLINE_PARTITIONS:
          await this.checkOfflinePartitions();
          break;
        case KafkaAlertType.TOPIC_UNAVAILABLE:
          await this.checkTopicAvailability();
          break;
        // Outros tipos de alerta seriam implementados aqui
      }
    } catch (error) {
      kafkaLoggingService.error('kafka.alerts', `Erro ao verificar alerta ${alertType}`, error);
    }
  }
  
  /**
   * Verifica se algum broker está indisponível
   */
  private async checkBrokerDown(): Promise<void> {
    try {
      const metadata = await this.admin.fetchTopicMetadata();
      const brokers = metadata.brokers;
      
      // Verificar se o número de brokers é menor que o esperado
      const expectedBrokers = parseInt(process.env.KAFKA_EXPECTED_BROKERS || '3', 10);
      
      if (brokers.length < expectedBrokers) {
        const missingCount = expectedBrokers - brokers.length;
        
        this.triggerAlert(
          KafkaAlertType.BROKER_DOWN,
          KafkaAlertSeverity.CRITICAL,
          `${missingCount} broker(s) Kafka indisponível(is)`,
          {
            expectedBrokers,
            actualBrokers: brokers.length,
            availableBrokers: brokers.map(b => `${b.host}:${b.port}`),
          }
        );
      } else {
        // Resolver alertas ativos deste tipo
        this.resolveAlertsByType(KafkaAlertType.BROKER_DOWN);
      }
    } catch (error) {
      kafkaLoggingService.error('kafka.alerts', 'Erro ao verificar disponibilidade de brokers', error);
      
      // Se não conseguir conectar, provavelmente todos os brokers estão indisponíveis
      this.triggerAlert(
        KafkaAlertType.BROKER_DOWN,
        KafkaAlertSeverity.CRITICAL,
        'Não foi possível conectar ao cluster Kafka',
        { error: error.message }
      );
    }
  }
  
  /**
   * Verifica lag de consumidores
   */
  private async checkConsumerLag(): Promise<void> {
    try {
      const config = kafkaAlertConfigs[KafkaAlertType.CONSUMER_LAG];
      const groups = await this.admin.listGroups();
      
      for (const group of groups.groups) {
        // Ignorar grupos excluídos
        if (config.additionalConfig?.excludedGroups?.includes(group.groupId)) {
          continue;
        }
        
        // Obter offsets do consumidor
        const consumerOffsets = await this.admin.fetchOffsets({
          groupId: group.groupId,
        });
        
        for (const { topic, partitions } of consumerOffsets) {
          // Obter offsets mais recentes para o tópico
          const topicOffsets = await this.admin.fetchTopicOffsets(topic);
          
          for (const { partition, offset } of partitions) {
            const latestOffset = topicOffsets.find(
              t => t.partition === partition
            )?.offset || '0';
            
            const consumerOffset = offset;
            const lag = Number(BigInt(latestOffset) - BigInt(consumerOffset));
            
            // Verificar se o lag excede os limiares
            if (lag > 0) {
              let severity = null;
              
              if (config.thresholds.critical && lag >= config.thresholds.critical) {
                severity = KafkaAlertSeverity.CRITICAL;
              } else if (config.thresholds.error && lag >= config.thresholds.error) {
                severity = KafkaAlertSeverity.ERROR;
              } else if (config.thresholds.warning && lag >= config.thresholds.warning) {
                severity = KafkaAlertSeverity.WARNING;
              }
              
              // Verificar se é um grupo crítico (severidade mais alta)
              const isCriticalGroup = config.additionalConfig?.criticalGroups?.includes(group.groupId);
              if (isCriticalGroup && severity === KafkaAlertSeverity.WARNING) {
                severity = KafkaAlertSeverity.ERROR;
              }
              
              if (severity) {
                this.triggerAlert(
                  KafkaAlertType.CONSUMER_LAG,
                  severity,
                  `Alto lag detectado para grupo ${group.groupId} no tópico ${topic} (partição ${partition})`,
                  {
                    groupId: group.groupId,
                    topic,
                    partition,
                    lag,
                    consumerOffset,
                    latestOffset,
                    isCriticalGroup,
                  }
                );
              }
            }
          }
        }
      }
    } catch (error) {
      kafkaLoggingService.error('kafka.alerts', 'Erro ao verificar lag de consumidores', error);
    }
  }
  
  /**
   * Verifica partições sub-replicadas
   */
  private async checkUnderReplicatedPartitions(): Promise<void> {
    try {
      const metadata = await this.admin.fetchTopicMetadata();
      const underReplicatedPartitions = [];
      
      for (const topic of metadata.topics) {
        for (const partition of topic.partitions) {
          if (partition.replicas.length > partition.isr.length) {
            underReplicatedPartitions.push({
              topic: topic.name,
              partition: partition.partitionId,
              replicas: partition.replicas.length,
              isr: partition.isr.length,
            });
          }
        }
      }
      
      if (underReplicatedPartitions.length > 0) {
        const config = kafkaAlertConfigs[KafkaAlertType.UNDER_REPLICATED_PARTITIONS];
        let severity = KafkaAlertSeverity.WARNING;
        
        if (config.thresholds.critical && underReplicatedPartitions.length >= config.thresholds.critical) {
          severity = KafkaAlertSeverity.CRITICAL;
        } else if (config.thresholds.error && underReplicatedPartitions.length >= config.thresholds.error) {
          severity = KafkaAlertSeverity.ERROR;
        }
        
        this.triggerAlert(
          KafkaAlertType.UNDER_REPLICATED_PARTITIONS,
          severity,
          `${underReplicatedPartitions.length} partição(ões) sub-replicada(s) detectada(s)`,
          {
            count: underReplicatedPartitions.length,
            partitions: underReplicatedPartitions,
          }
        );
      } else {
        // Resolver alertas ativos deste tipo
        this.resolveAlertsByType(KafkaAlertType.UNDER_REPLICATED_PARTITIONS);
      }
    } catch (error) {
      kafkaLoggingService.error('kafka.alerts', 'Erro ao verificar partições sub-replicadas', error);
    }
  }
  
  /**
   * Verifica partições offline
   */
  private async checkOfflinePartitions(): Promise<void> {
    try {
      const metadata = await this.admin.fetchTopicMetadata();
      const offlinePartitions = [];
      
      for (const topic of metadata.topics) {
        for (const partition of topic.partitions) {
          if (partition.isr.length === 0 || partition.leader < 0) {
            offlinePartitions.push({
              topic: topic.name,
              partition: partition.partitionId,
              leader: partition.leader,
              isr: partition.isr,
            });
          }
        }
      }
      
      if (offlinePartitions.length > 0) {
        this.triggerAlert(
          KafkaAlertType.OFFLINE_PARTITIONS,
          KafkaAlertSeverity.CRITICAL,
          `${offlinePartitions.length} partição(ões) offline detectada(s)`,
          {
            count: offlinePartitions.length,
            partitions: offlinePartitions,
          }
        );
      } else {
        // Resolver alertas ativos deste tipo
        this.resolveAlertsByType(KafkaAlertType.OFFLINE_PARTITIONS);
      }
    } catch (error) {
      kafkaLoggingService.error('kafka.alerts', 'Erro ao verificar partições offline', error);
    }
  }
  
  /**
   * Verifica disponibilidade de tópicos
   */
  private async checkTopicAvailability(): Promise<void> {
    try {
      const config = kafkaAlertConfigs[KafkaAlertType.TOPIC_UNAVAILABLE];
      const criticalTopics = config.additionalConfig?.criticalTopics || [];
      
      if (criticalTopics.length === 0) {
        return;
      }
      
      const metadata = await this.admin.fetchTopicMetadata();
      const availableTopics = new Set(metadata.topics.map(t => t.name));
      const unavailableTopics = criticalTopics.filter(topic => !availableTopics.has(topic));
      
      if (unavailableTopics.length > 0) {
        this.triggerAlert(
          KafkaAlertType.TOPIC_UNAVAILABLE,
          KafkaAlertSeverity.CRITICAL,
          `${unavailableTopics.length} tópico(s) crítico(s) indisponível(is)`,
          {
            count: unavailableTopics.length,
            topics: unavailableTopics,
          }
        );
      } else {
        // Resolver alertas ativos deste tipo
        this.resolveAlertsByType(KafkaAlertType.TOPIC_UNAVAILABLE);
      }
    } catch (error) {
      kafkaLoggingService.error('kafka.alerts', 'Erro ao verificar disponibilidade de tópicos', error);
    }
  }
  
  /**
   * Dispara um alerta
   * @param type - Tipo de alerta
   * @param severity - Severidade do alerta
   * @param message - Mensagem do alerta
   * @param details - Detalhes adicionais
   */
  public async triggerAlert(
    type: KafkaAlertType,
    severity: KafkaAlertSeverity,
    message: string,
    details?: Record<string, any>
  ): Promise<void> {
    try {
      const config = kafkaAlertConfigs[type];
      
      // Verificar se o alerta está em cooldown
      const cooldownKey = `${type}:${JSON.stringify(details || {})}`;
      const lastTriggered = this.alertCooldowns.get(cooldownKey);
      
      if (lastTriggered && Date.now() - lastTriggered < config.cooldown * 1000) {
        return;
      }
      
      // Atualizar cooldown
      this.alertCooldowns.set(cooldownKey, Date.now());
      
      // Criar alerta
      const alert: KafkaAlert = {
        id: uuidv4(),
        type,
        severity,
        timestamp: new Date().toISOString(),
        message,
        details,
        status: 'active',
      };
      
      // Armazenar alerta
      this.activeAlerts.set(alert.id, alert);
      
      // Salvar no banco de dados
      await queryHelper.query(
        `INSERT INTO tab_kafka_alerts (
          alert_id,
          type,
          severity,
          timestamp,
          message,
          details,
          status,
          created_at
        ) VALUES (
          $1, $2, $3, $4, $5, $6, $7, NOW()
        )`,
        [
          alert.id,
          alert.type,
          alert.severity,
          alert.timestamp,
          alert.message,
          alert.details ? JSON.stringify(alert.details) : null,
          alert.status,
        ]
      );
      
      // Enviar notificações
      await this.sendAlertNotifications(alert);
      
      kafkaLoggingService.warn('kafka.alerts', `Alerta disparado: ${type} - ${message}`, details);
    } catch (error) {
      kafkaLoggingService.error('kafka.alerts', 'Erro ao disparar alerta', error);
    }
  }
  
  /**
   * Envia notificações para um alerta
   * @param alert - Alerta a ser notificado
   */
  private async sendAlertNotifications(alert: KafkaAlert): Promise<void> {
    try {
      const config = kafkaAlertConfigs[alert.type];
      
      for (const channelType of config.channels) {
        const channelConfig = alertChannelConfigs[channelType];
        
        if (!channelConfig.enabled) {
          continue;
        }
        
        switch (channelType) {
          case KafkaAlertChannel.EMAIL:
            await this.sendEmailNotification(alert, channelConfig.config);
            break;
          case KafkaAlertChannel.SLACK:
            await this.sendSlackNotification(alert, channelConfig.config);
            break;
          case KafkaAlertChannel.SMS:
            // Enviar SMS apenas para alertas críticos se configurado assim
            if (!channelConfig.config.onlyCritical || alert.severity === KafkaAlertSeverity.CRITICAL) {
              await this.sendSmsNotification(alert, channelConfig.config);
            }
            break;
          case KafkaAlertChannel.WEBHOOK:
            await this.sendWebhookNotification(alert, channelConfig.config);
            break;
          case KafkaAlertChannel.LOG:
            // Já registrado no log ao disparar o alerta
            break;
        }
      }
    } catch (error) {
      kafkaLoggingService.error('kafka.alerts', 'Erro ao enviar notificações de alerta', error);
    }
  }
  
  /**
   * Envia notificação por e-mail
   * @param alert - Alerta a ser notificado
   * @param config - Configuração do canal de e-mail
   */
  private async sendEmailNotification(
    alert: KafkaAlert,
    config: Record<string, any>
  ): Promise<void> {
    try {
      const subject = config.subject
        .replace('{severity}', alert.severity)
        .replace('{type}', alert.type);
      
      const content = `
        <h2>Alerta Kafka: ${alert.type}</h2>
        <p><strong>Severidade:</strong> ${alert.severity}</p>
        <p><strong>Timestamp:</strong> ${alert.timestamp}</p>
        <p><strong>Mensagem:</strong> ${alert.message}</p>
        ${config.includeDetails && alert.details ? `<h3>Detalhes:</h3><pre>${JSON.stringify(alert.details, null, 2)}</pre>` : ''}
      `;
      
      await notificationService.sendEmail({
        to: config.recipients,
        from: config.from,
        subject,
        html: content,
      });
    } catch (error) {
      kafkaLoggingService.error('kafka.alerts', 'Erro ao enviar notificação por e-mail', error);
    }
  }
  
  /**
   * Envia notificação para o Slack
   * @param alert - Alerta a ser notificado
   * @param config - Configuração do canal do Slack
   */
  private async sendSlackNotification(
    alert: KafkaAlert,
    config: Record<string, any>
  ): Promise<void> {
    try {
      // Implementação simplificada - em um sistema real, usaria a API do Slack
      const severityColors = {
        [KafkaAlertSeverity.INFO]: '#2196F3',
        [KafkaAlertSeverity.WARNING]: '#FF9800',
        [KafkaAlertSeverity.ERROR]: '#F44336',
        [KafkaAlertSeverity.CRITICAL]: '#9C27B0',
      };
      
      const payload = {
        channel: config.channel,
        username: config.username,
        icon_emoji: config.iconEmoji,
        attachments: [
          {
            color: severityColors[alert.severity] || '#2196F3',
            title: `Alerta Kafka: ${alert.type}`,
            text: alert.message,
            fields: [
              {
                title: 'Severidade',
                value: alert.severity,
                short: true,
              },
              {
                title: 'Timestamp',
                value: alert.timestamp,
                short: true,
              },
            ],
            footer: 'Estação da Alfabetização - Monitoramento Kafka',
          },
        ],
      };
      
      // Adicionar detalhes se configurado
      if (config.includeDetails && alert.details) {
        payload.attachments[0].fields.push({
          title: 'Detalhes',
          value: '```' + JSON.stringify(alert.details, null, 2) + '```',
          short: false,
        });
      }
      
      // Enviar para webhook do Slack
      if (config.webhookUrl) {
        await fetch(config.webhookUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(payload),
        });
      }
    } catch (error) {
      kafkaLoggingService.error('kafka.alerts', 'Erro ao enviar notificação para o Slack', error);
    }
  }
  
  /**
   * Envia notificação por SMS
   * @param alert - Alerta a ser notificado
   * @param config - Configuração do canal de SMS
   */
  private async sendSmsNotification(
    alert: KafkaAlert,
    config: Record<string, any>
  ): Promise<void> {
    try {
      // Implementação simplificada - em um sistema real, usaria um provedor de SMS
      const message = `[${alert.severity}] Alerta Kafka: ${alert.type} - ${alert.message}`;
      
      // Enviar para cada número configurado
      for (const phoneNumber of config.phoneNumbers) {
        await notificationService.sendSms({
          to: phoneNumber,
          message,
        });
      }
    } catch (error) {
      kafkaLoggingService.error('kafka.alerts', 'Erro ao enviar notificação por SMS', error);
    }
  }
  
  /**
   * Envia notificação para webhook
   * @param alert - Alerta a ser notificado
   * @param config - Configuração do canal de webhook
   */
  private async sendWebhookNotification(
    alert: KafkaAlert,
    config: Record<string, any>
  ): Promise<void> {
    try {
      if (!config.url) {
        return;
      }
      
      const payload = {
        id: alert.id,
        type: alert.type,
        severity: alert.severity,
        timestamp: alert.timestamp,
        message: alert.message,
        status: alert.status,
      };
      
      // Adicionar detalhes se configurado
      if (config.includeDetails && alert.details) {
        payload['details'] = alert.details;
      }
      
      // Enviar para webhook
      await fetch(config.url, {
        method: config.method || 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...config.headers,
        },
        body: JSON.stringify(payload),
      });
    } catch (error) {
      kafkaLoggingService.error('kafka.alerts', 'Erro ao enviar notificação para webhook', error);
    }
  }
  
  /**
   * Resolve alertas por tipo
   * @param type - Tipo de alerta
   */
  private async resolveAlertsByType(type: KafkaAlertType): Promise<void> {
    try {
      const now = new Date().toISOString();
      const alertsToResolve = [];
      
      // Encontrar alertas ativos do tipo especificado
      for (const [id, alert] of this.activeAlerts.entries()) {
        if (alert.type === type && alert.status === 'active') {
          alert.status = 'resolved';
          alert.resolvedAt = now;
          alertsToResolve.push(alert);
          this.activeAlerts.delete(id);
        }
      }
      
      if (alertsToResolve.length === 0) {
        return;
      }
      
      // Atualizar no banco de dados
      for (const alert of alertsToResolve) {
        await queryHelper.query(
          `UPDATE tab_kafka_alerts
          SET status = 'resolved', resolved_at = $1
          WHERE alert_id = $2`,
          [now, alert.id]
        );
      }
      
      kafkaLoggingService.info('kafka.alerts', `${alertsToResolve.length} alerta(s) do tipo ${type} resolvido(s)`);
    } catch (error) {
      kafkaLoggingService.error('kafka.alerts', `Erro ao resolver alertas do tipo ${type}`, error);
    }
  }
  
  /**
   * Reconhece um alerta
   * @param alertId - ID do alerta
   * @param acknowledgedBy - Identificação de quem reconheceu o alerta
   */
  public async acknowledgeAlert(alertId: string, acknowledgedBy: string): Promise<void> {
    try {
      const alert = this.activeAlerts.get(alertId);
      
      if (!alert) {
        throw new Error(`Alerta ${alertId} não encontrado`);
      }
      
      const now = new Date().toISOString();
      
      alert.status = 'acknowledged';
      alert.acknowledgedBy = acknowledgedBy;
      alert.acknowledgedAt = now;
      
      // Atualizar no banco de dados
      await queryHelper.query(
        `UPDATE tab_kafka_alerts
        SET status = 'acknowledged', acknowledged_by = $1, acknowledged_at = $2
        WHERE alert_id = $3`,
        [acknowledgedBy, now, alertId]
      );
      
      kafkaLoggingService.info('kafka.alerts', `Alerta ${alertId} reconhecido por ${acknowledgedBy}`);
    } catch (error) {
      kafkaLoggingService.error('kafka.alerts', `Erro ao reconhecer alerta ${alertId}`, error);
      throw error;
    }
  }
  
  /**
   * Obtém alertas ativos
   * @returns Lista de alertas ativos
   */
  public getActiveAlerts(): KafkaAlert[] {
    return Array.from(this.activeAlerts.values());
  }
  
  /**
   * Obtém alertas por tipo
   * @param type - Tipo de alerta
   * @returns Lista de alertas do tipo especificado
   */
  public getAlertsByType(type: KafkaAlertType): KafkaAlert[] {
    return Array.from(this.activeAlerts.values()).filter(alert => alert.type === type);
  }
}

// Exportar instância única do serviço
export const kafkaAlertService = KafkaAlertService.getInstance();
