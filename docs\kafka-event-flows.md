# Fluxos de Eventos Kafka

## Visão Geral

Este documento descreve os fluxos de eventos Kafka implementados na aplicação Estação da Alfabetização. Os fluxos de eventos são responsáveis por processar mensagens assíncronas, permitindo a comunicação entre diferentes partes do sistema de forma desacoplada.

## Arquitetura

A arquitetura de fluxos de eventos é composta por:

1. **Produtores**: Serviços que geram eventos e os enviam para tópicos Kafka
2. **Consumidores**: Serviços que consomem eventos de tópicos Kafka
3. **Handlers**: Componentes que processam eventos específicos
4. **Pipelines**: Sequências de etapas para processamento de eventos
5. **Transformadores**: Componentes que convertem eventos entre diferentes formatos

## Fluxos Implementados

### 1. Fluxo de Processamento de Pagamentos

Este fluxo processa eventos relacionados a pagamentos, atualizando o status de pagamentos e pedidos, enviando notificações e realizando outras ações necessárias.

#### Tópicos

- `payment.transaction.created`: Evento de criação de pagamento
- `payment.transaction.updated`: Evento de atualização de pagamento
- `payment.transaction.failed`: Evento de falha de pagamento
- `payment.refund.requested`: Evento de solicitação de reembolso
- `payment.refund.processed`: Evento de processamento de reembolso
- `payment.webhook.received`: Evento de webhook de pagamento recebido

#### Pipeline

O pipeline de processamento de pagamentos consiste nas seguintes etapas:

1. **Validação**: Verifica se o evento contém todos os campos necessários
2. **Enriquecimento**: Adiciona informações adicionais ao evento
3. **Processamento**: Executa a lógica de negócio para o evento
4. **Notificação**: Envia notificações para os usuários
5. **Auditoria**: Registra o evento para fins de auditoria

#### Handlers

- `PaymentCreatedHandler`: Processa eventos de criação de pagamento
- `PaymentUpdatedHandler`: Processa eventos de atualização de pagamento
- `PaymentFailedHandler`: Processa eventos de falha de pagamento
- `RefundRequestedHandler`: Processa eventos de solicitação de reembolso
- `RefundProcessedHandler`: Processa eventos de processamento de reembolso
- `PaymentWebhookHandler`: Processa eventos de webhook de pagamento

### 2. Transformações de Eventos

#### Transformadores de Pagamento

- `LegacyToNewPaymentTransformer`: Converte eventos de pagamento legados para o novo formato
- `EfiPayToInternalTransformer`: Converte eventos de pagamento do formato Efí Pay para o formato interno
- `InternalToNotificationTransformer`: Converte eventos de pagamento do formato interno para o formato de notificação

#### Transformadores de Pedido

- `LegacyToNewOrderTransformer`: Converte eventos de pedido legados para o novo formato
- `InternalToNotificationOrderTransformer`: Converte eventos de pedido do formato interno para o formato de notificação

## Configuração e Inicialização

Os fluxos de eventos são inicializados no arquivo `src/services/flows/initEventFlows.ts`, que registra os handlers necessários e inicia os consumidores Kafka.

```typescript
// Exemplo de inicialização
import { initEventFlows } from '@services/flows/initEventFlows';

// Inicializar fluxos de eventos
await initEventFlows();
```

## Formato de Eventos

### Evento de Pagamento

```json
{
  "paymentId": "string",
  "orderId": "string",
  "userId": "string",
  "value": 100.0,
  "paymentType": "pix",
  "status": "approved",
  "externalId": "string",
  "timestamp": "2023-01-01T00:00:00Z",
  "metadata": {
    "key": "value"
  }
}
```

### Evento de Pedido

```json
{
  "orderId": "string",
  "userId": "string",
  "status": "paid",
  "total": 100.0,
  "timestamp": "2023-01-01T00:00:00Z",
  "items": [
    {
      "productId": "string",
      "name": "string",
      "quantity": 1,
      "price": 100.0
    }
  ],
  "metadata": {
    "key": "value"
  }
}
```

## Monitoramento e Logging

Os fluxos de eventos são monitorados usando o serviço `kafkaLoggingService`, que registra eventos importantes durante o processamento. Além disso, métricas de pipeline são armazenadas no cache para análise posterior.

## Tratamento de Erros

Os fluxos de eventos implementam estratégias de retry para lidar com falhas temporárias. Cada etapa do pipeline pode definir seu próprio handler de erro, permitindo um tratamento específico para diferentes tipos de falhas.

## Extensão

Para adicionar um novo fluxo de eventos:

1. Crie um novo arquivo de fluxo em `src/services/flows/`
2. Implemente os handlers necessários em `src/services/handlers/`
3. Defina transformadores se necessário em `src/services/transformers/`
4. Registre os handlers e transformadores em `src/services/flows/initEventFlows.ts`

## Considerações de Segurança

- Todos os eventos são validados antes do processamento
- Informações sensíveis são mascaradas nos logs
- Acesso aos tópicos Kafka é controlado por autenticação e autorização

## Referências

- [Documentação do Kafka](https://kafka.apache.org/documentation/)
- [Padrões de Mensageria](https://www.enterpriseintegrationpatterns.com/)
- [Arquitetura Orientada a Eventos](https://martinfowler.com/articles/201701-event-driven.html)
