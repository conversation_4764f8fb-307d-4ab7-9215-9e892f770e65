---
import { PageTransition } from '../../components/transitions';
import DaisyButton from '../../components/ui/DaisyButton.astro';
import DaisyCard from '../../components/ui/DaisyCard.astro';
import Breadcrumbs from '../../components/ui/navigation/Breadcrumbs.astro';
/**
 * Página de exemplos
 * Demonstra os diferentes exemplos disponíveis no projeto
 */
import BaseLayout from '../../layouts/BaseLayout.astro';
import { Container, Grid, Section } from '../../layouts/grid';

// Título da página
const title = 'Exemplos';
const description = 'Exemplos de componentes e funcionalidades do projeto Estação da Alfabetização';

// Breadcrumbs para a página atual
const breadcrumbItems = [{ href: '/', label: 'Início' }, { label: 'Exemplos' }];

// Lista de exemplos disponíveis
const examples = [
  {
    title: 'Componentes',
    description: 'Biblioteca de componentes reutilizáveis',
    href: '/examples/components',
    icon: 'puzzle-piece',
    color: 'primary',
    transitionType: 'fade',
  },
  {
    title: 'Layout',
    description: 'Sistema de layout responsivo',
    href: '/examples/layout',
    icon: 'template',
    color: 'secondary',
    transitionType: 'slide',
  },
  {
    title: 'Temas',
    description: 'Sistema de temas e personalização',
    href: '/examples/themes',
    icon: 'paint-brush',
    color: 'accent',
    transitionType: 'scale',
  },
  {
    title: 'Paleta de Cores',
    description: 'Paleta de cores e variáveis CSS',
    href: '/examples/color-palette',
    icon: 'palette',
    color: 'info',
    transitionType: 'flip',
  },
  {
    title: 'Tipografia',
    description: 'Sistema tipográfico e estilos de texto',
    href: '/examples/typography',
    icon: 'text-size',
    color: 'success',
    transitionType: 'fade',
  },
  {
    title: 'Elementos Gráficos',
    description: 'Ícones, ilustrações e animações',
    href: '/examples/graphics',
    icon: 'image',
    color: 'primary',
    transitionType: 'scale',
  },
  {
    title: 'Animações AnimeJS',
    description: 'Demonstração de animações com AnimeJS',
    href: '/examples/anime-demo',
    icon: 'animation',
    color: 'accent',
    transitionType: 'scale',
  },
  {
    title: 'Animação de Abertura',
    description: 'Animação de abertura com tema de trem',
    href: '/examples/opening-animation',
    icon: 'train',
    color: 'warning',
    transitionType: 'fade',
  },
  {
    title: 'Transições',
    description: 'Transições entre páginas e elementos',
    href: '/examples/transitions',
    icon: 'transition',
    color: 'success',
    transitionType: 'flip',
  },
  {
    title: 'Micro-interações',
    description: 'Micro-interações e animações de elementos',
    href: '/examples/micro-interactions',
    icon: 'sparkles',
    color: 'error',
    transitionType: 'slide',
  },
  {
    title: 'Responsividade',
    description: 'Adaptação para diferentes dispositivos',
    href: '/examples/breakpoints',
    icon: 'devices',
    color: 'warning',
    transitionType: 'slide',
  },
];
---

<BaseLayout title={title} description={description} enableViewTransitions={true} transitionType="fade">
  <Container>
    <PageTransition type="fade" duration="0.5s">
      <Section>
        <Breadcrumbs items={breadcrumbItems} class="mb-6" />

        <h1 class="text-3xl font-bold mb-6">{title}</h1>

        <p class="mb-8">
          Esta página contém exemplos de componentes, layouts e funcionalidades disponíveis no projeto Estação da Alfabetização.
          Explore os diferentes exemplos para entender como utilizar cada recurso.
        </p>

        <Grid cols={{ sm: 1, md: 2, lg: 3 }} gap={6}>
          {examples.map(example => (
            <DaisyCard
              title={example.title}
              color={example.color}
              class="h-full"
            >
              <div class="flex flex-col h-full">
                <p class="mb-4 flex-grow">{example.description}</p>
                <div class="mt-auto pt-4">
                  <DaisyButton
                    href={example.href}
                    color={example.color}
                    data-astro-transition
                    data-transition-type={example.transitionType}
                    data-transition-direction={example.transitionType === 'slide' ? 'left' : 'right'}
                    class="w-full"
                  >
                    Ver exemplo
                  </DaisyButton>
                </div>
              </div>
            </DaisyCard>
          ))}
        </Grid>

        <div class="mt-12 text-center">
          <DaisyButton
            href="/"
            color="neutral"
            variant="outline"
            data-astro-transition
            data-transition-type="fade"
          >
            Voltar para a página inicial
          </DaisyButton>
        </div>
      </Section>
    </PageTransition>
  </Container>
</BaseLayout>
