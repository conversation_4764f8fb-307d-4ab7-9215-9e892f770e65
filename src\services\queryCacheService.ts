/**
 * Serviço de cache de consultas
 *
 * Este serviço otimiza consultas ao banco de dados armazenando
 * resultados em cache para melhorar a performance.
 */

import { createHash } from 'node:crypto';
import { pgHelper } from '@repository/pgHelper';
import { CacheableDataType, dataCacheService } from '@services/dataCacheService';
import { logger } from '@utils/logger';

/**
 * Interface para configuração de cache de consulta
 */
export interface QueryCacheConfig {
  /**
   * Tempo de vida em segundos
   * @default 300 (5 minutos)
   */
  ttl?: number;

  /**
   * Tags para categorizar a consulta em cache
   */
  tags?: string[];

  /**
   * Se deve usar cache para esta consulta
   * @default true
   */
  useCache?: boolean;

  /**
   * Se deve atualizar o cache com o resultado da consulta
   * @default true
   */
  updateCache?: boolean;
}

/**
 * Serviço de cache de consultas
 */
export const queryCacheService = {
  /**
   * Executa uma consulta com suporte a cache
   * @param query - Consulta SQL
   * @param params - Parâmetros da consulta
   * @param config - Configuração de cache
   * @returns Resultado da consulta
   */
  async query(query: string, params: any[] = [], config: QueryCacheConfig = {}): Promise<any> {
    // Configuração padrão
    const fullConfig: QueryCacheConfig = {
      ttl: 300, // 5 minutos
      tags: ['query'],
      useCache: true,
      updateCache: true,
      ...config,
    };

    // Se não deve usar cache, executar consulta diretamente
    if (!fullConfig.useCache) {
      return await pgHelper.query(query, params);
    }

    try {
      // Gerar chave de cache baseada na consulta e parâmetros
      const cacheKey = this.generateCacheKey(query, params);

      // Tentar obter do cache
      const cachedResult = await dataCacheService.get(CacheableDataType.QUERY_RESULT, cacheKey, {
        ttl: fullConfig.ttl,
        tags: fullConfig.tags,
      });

      // Registrar métrica (se o serviço estiver disponível)
      try {
        const { cacheMetricsService } = await import('@services/cacheMetricsService');
        cacheMetricsService.recordRequest(CacheableDataType.QUERY_RESULT, !!cachedResult);
      } catch (e) {
        // Ignorar erro se o serviço de métricas não estiver disponível
      }

      if (cachedResult) {
        logger.debug('Resultado obtido do cache', { cacheKey });
        return cachedResult;
      }

      // Executar consulta
      const result = await pgHelper.query(query, params);

      // Atualizar cache se configurado
      if (fullConfig.updateCache) {
        await dataCacheService.set(CacheableDataType.QUERY_RESULT, cacheKey, result, {
          ttl: fullConfig.ttl,
          tags: fullConfig.tags,
        });
      }

      return result;
    } catch (error) {
      logger.error('Erro ao executar consulta com cache:', error);

      // Em caso de erro no cache, executar consulta diretamente
      return await pgHelper.query(query, params);
    }
  },

  /**
   * Gera uma chave de cache para uma consulta
   * @param query - Consulta SQL
   * @param params - Parâmetros da consulta
   * @returns Chave de cache
   */
  generateCacheKey(query: string, params: any[] = []): string {
    // Normalizar consulta (remover espaços extras)
    const normalizedQuery = query.replace(/\s+/g, ' ').trim();

    // Criar string para hash
    const hashInput = `${normalizedQuery}:${JSON.stringify(params)}`;

    // Gerar hash SHA-256
    return createHash('sha256').update(hashInput).digest('hex');
  },

  /**
   * Invalida cache para consultas relacionadas a uma tabela
   * @param tableName - Nome da tabela
   * @returns Número de itens invalidados
   */
  async invalidateTableCache(tableName: string): Promise<number> {
    try {
      // Invalidar por tag de tabela
      const invalidated = await dataCacheService.invalidateByTag(`table:${tableName}`);

      logger.info(`Cache invalidado para tabela ${tableName}`, { invalidated });

      return invalidated;
    } catch (error) {
      logger.error(`Erro ao invalidar cache para tabela ${tableName}:`, error);
      return 0;
    }
  },

  /**
   * Executa uma consulta de leitura com cache
   * @param query - Consulta SQL
   * @param params - Parâmetros da consulta
   * @param ttl - Tempo de vida em segundos
   * @returns Resultado da consulta
   */
  async queryRead(query: string, params: any[] = [], ttl = 300): Promise<any> {
    // Detectar tabelas envolvidas na consulta
    const tables = this.detectTablesInQuery(query);

    // Configurar tags baseadas nas tabelas
    const tags = ['query', 'read', ...tables.map((table) => `table:${table}`)];

    return this.query(query, params, {
      ttl,
      tags,
      useCache: true,
      updateCache: true,
    });
  },

  /**
   * Executa uma consulta de escrita e invalida caches relacionados
   * @param query - Consulta SQL
   * @param params - Parâmetros da consulta
   * @returns Resultado da consulta
   */
  async queryWrite(query: string, params: any[] = []): Promise<any> {
    // Detectar tabelas envolvidas na consulta
    const tables = this.detectTablesInQuery(query);

    // Executar consulta sem cache
    const result = await pgHelper.query(query, params);

    // Invalidar cache para cada tabela
    for (const table of tables) {
      await this.invalidateTableCache(table);
    }

    return result;
  },

  /**
   * Detecta tabelas envolvidas em uma consulta SQL
   * @param query - Consulta SQL
   * @returns Lista de nomes de tabelas
   */
  detectTablesInQuery(query: string): string[] {
    // Normalizar consulta
    const normalizedQuery = query.toLowerCase().replace(/\s+/g, ' ');

    // Expressões regulares para detectar tabelas
    const patterns = [
      /from\s+([a-z0-9_]+)/g,
      /join\s+([a-z0-9_]+)/g,
      /update\s+([a-z0-9_]+)/g,
      /insert\s+into\s+([a-z0-9_]+)/g,
      /delete\s+from\s+([a-z0-9_]+)/g,
    ];

    // Conjunto para armazenar tabelas únicas
    const tables = new Set<string>();

    // Aplicar cada padrão
    for (const pattern of patterns) {
      let match;
      while ((match = pattern.exec(normalizedQuery)) !== null) {
        // Adicionar tabela ao conjunto
        tables.add(match[1]);
      }
    }

    return Array.from(tables);
  },

  /**
   * Limpa todo o cache de consultas
   * @returns Número de itens invalidados
   */
  async clearAllQueryCache(): Promise<number> {
    try {
      // Invalidar por tag de consulta
      const invalidated = await dataCacheService.invalidateByTag('query');

      logger.info('Cache de consultas limpo', { invalidated });

      return invalidated;
    } catch (error) {
      logger.error('Erro ao limpar cache de consultas:', error);
      return 0;
    }
  },
};
