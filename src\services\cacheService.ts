/**
 * Serviço para gerenciamento de cache com Valkey
 * Implementa funções para armazenamento, recuperação e invalidação de cache
 */

import { createClient, createCluster } from '@valkey/client';
import type { RedisClientType, RedisClusterType } from '@valkey/client';
import { cacheConfig } from '@config/cache';
import { logger } from '@utils/logger';
import { gzip, gunzip } from 'node:zlib';
import { promisify } from 'node:util';
import * as fs from 'node:fs';

// Promisify zlib functions
const gzipAsync = promisify(gzip);
const gunzipAsync = promisify(gunzip);

// Tipos de dados que podem ser armazenados em cache
export type CacheableDataType =
  | 'user'
  | 'product'
  | 'category'
  | 'order'
  | 'payment'
  | 'session'
  | 'token'
  | 'query'
  | 'security'
  | 'audit';

// Interface para opções de cache
export interface CacheOptions {
  ttl?: number;
  compress?: boolean;
}

// Classe para gerenciamento de conexão com o Valkey
class CacheClient {
  private client: RedisClientType | RedisClusterType;
  private isConnected = false;
  private static instance: CacheClient;

  private constructor() {
    // Importar configurações de segurança
    const { getSecurityConfig } = require('@config/cache/security.config');
    const { getTLSConfig } = require('@config/cache/tls-config');

    // Obter configurações
    const securityConfig = getSecurityConfig();
    const tlsConfig = getTLSConfig();

    // Configurar opções de TLS
    const tlsOptions = tlsConfig.enabled
      ? {
          tls: true,
          rejectUnauthorized: tlsConfig.verifyClient,
          ca: tlsConfig.caPath ? fs.readFileSync(tlsConfig.caPath) : undefined,
          cert: tlsConfig.certPath
            ? fs.readFileSync(tlsConfig.certPath)
            : undefined,
          key: tlsConfig.keyPath
            ? fs.readFileSync(tlsConfig.keyPath)
            : undefined,
        }
      : undefined;

    // Criar cliente Valkey
    if (cacheConfig.cluster.enabled) {
      // Configuração de cluster
      this.client = createCluster({
        rootNodes: cacheConfig.cluster.nodes.map(node => ({
          socket: {
            host: node.host,
            port: node.port,
            tls: tlsOptions,
          },
        })),
        defaults: {
          username: cacheConfig.server.username || undefined,
          password: cacheConfig.server.password || undefined,
          database: cacheConfig.server.db,
        },
      });
    } else {
      // Configuração standalone
      this.client = createClient({
        socket: {
          host: cacheConfig.server.host,
          port: cacheConfig.server.port,
          tls: tlsOptions,
        },
        username: cacheConfig.server.username || undefined,
        password: cacheConfig.server.password || undefined,
        database: cacheConfig.server.db,
      });
    }

    // Configurar eventos
    this.setupEvents();

    // Conectar ao servidor
    this.connect();
  }

  // Conectar ao servidor
  private async connect(): Promise<void> {
    try {
      await this.client.connect();
    } catch (error) {
      logger.error('Erro ao conectar com Valkey:', error);
    }
  }

  // Padrão Singleton para garantir uma única instância
  public static getInstance(): CacheClient {
    if (!CacheClient.instance) {
      CacheClient.instance = new CacheClient();
    }
    return CacheClient.instance;
  }

  // Configurar eventos de conexão
  private setupEvents(): void {
    this.client.on('connect', () => {
      this.isConnected = true;
      if (cacheConfig.logging.enabled) {
        logger.info('Conectado ao servidor Valkey');
      }
    });

    this.client.on('error', err => {
      this.isConnected = false;
      logger.error('Erro na conexão com Valkey:', err);
    });

    this.client.on('reconnecting', () => {
      if (cacheConfig.logging.enabled) {
        logger.info('Reconectando ao servidor Valkey');
      }
    });

    this.client.on('end', () => {
      this.isConnected = false;
      if (cacheConfig.logging.enabled) {
        logger.info('Conexão com Valkey encerrada');
      }
    });
  }

  // Obter cliente Valkey
  public getClient(): RedisClientType | RedisClusterType {
    return this.client;
  }

  // Verificar se está conectado
  public isReady(): boolean {
    return this.isConnected && this.client.isReady;
  }

  // Fechar conexão
  public async close(): Promise<void> {
    await this.client.quit();
    this.isConnected = false;
  }
}

// Serviço de cache
export const cacheService = {
  /**
   * Armazena um valor no cache (versão simplificada para sessões)
   * @param key - Chave para o valor
   * @param value - Valor a ser armazenado
   * @param ttl - Tempo de vida em segundos (opcional)
   * @returns Verdadeiro se o valor foi armazenado com sucesso
   */
  async setItem(key: string, value: string, ttl?: number): Promise<boolean> {
    try {
      const client = CacheClient.getInstance().getClient();

      // Verificar se o cliente está conectado
      if (!CacheClient.getInstance().isReady()) {
        return false;
      }

      // Armazenar no cache com TTL
      if (ttl) {
        await client.set(key, value, { EX: ttl });
      } else {
        await client.set(key, value);
      }

      return true;
    } catch (error) {
      logger.error('Erro ao armazenar em cache:', error);
      return false;
    }
  },

  /**
   * Recupera um valor do cache (versão simplificada para sessões)
   * @param key - Chave do valor
   * @returns Valor armazenado ou null se não encontrado
   */
  async getItem(key: string): Promise<string | null> {
    try {
      const client = CacheClient.getInstance().getClient();

      // Verificar se o cliente está conectado
      if (!CacheClient.getInstance().isReady()) {
        return null;
      }

      // Buscar do cache
      return await client.get(key);
    } catch (error) {
      logger.error('Erro ao recuperar do cache:', error);
      return null;
    }
  },

  /**
   * Remove um valor do cache (versão simplificada para sessões)
   * @param key - Chave do valor
   * @returns Verdadeiro se o valor foi removido com sucesso
   */
  async delItem(key: string): Promise<boolean> {
    try {
      const client = CacheClient.getInstance().getClient();

      // Verificar se o cliente está conectado
      if (!CacheClient.getInstance().isReady()) {
        return false;
      }

      // Remover do cache
      await client.del(key);

      return true;
    } catch (error) {
      logger.error('Erro ao remover do cache:', error);
      return false;
    }
  },

  /**
   * Adiciona um valor a um conjunto (Set)
   * @param key - Chave do conjunto
   * @param member - Valor a ser adicionado
   * @returns Verdadeiro se o valor foi adicionado com sucesso
   */
  async sadd(key: string, member: string): Promise<boolean> {
    try {
      const client = CacheClient.getInstance().getClient();

      // Verificar se o cliente está conectado
      if (!CacheClient.getInstance().isReady()) {
        return false;
      }

      // Adicionar ao conjunto
      await client.sAdd(key, member);

      return true;
    } catch (error) {
      logger.error('Erro ao adicionar ao conjunto:', error);
      return false;
    }
  },

  /**
   * Remove um valor de um conjunto (Set)
   * @param key - Chave do conjunto
   * @param member - Valor a ser removido
   * @returns Verdadeiro se o valor foi removido com sucesso
   */
  async srem(key: string, member: string): Promise<boolean> {
    try {
      const client = CacheClient.getInstance().getClient();

      // Verificar se o cliente está conectado
      if (!CacheClient.getInstance().isReady()) {
        return false;
      }

      // Remover do conjunto
      await client.sRem(key, member);

      return true;
    } catch (error) {
      logger.error('Erro ao remover do conjunto:', error);
      return false;
    }
  },

  /**
   * Obtém todos os membros de um conjunto (Set)
   * @param key - Chave do conjunto
   * @returns Array com os membros do conjunto ou null em caso de erro
   */
  async smembers(key: string): Promise<string[] | null> {
    try {
      const client = CacheClient.getInstance().getClient();

      // Verificar se o cliente está conectado
      if (!CacheClient.getInstance().isReady()) {
        return null;
      }

      // Obter membros do conjunto
      const members = await client.sMembers(key);

      return members;
    } catch (error) {
      logger.error('Erro ao obter membros do conjunto:', error);
      return null;
    }
  },

  /**
   * Define o tempo de expiração de uma chave
   * @param key - Chave a ser expirada
   * @param seconds - Tempo em segundos
   * @returns Verdadeiro se o tempo foi definido com sucesso
   */
  async expire(key: string, seconds: number): Promise<boolean> {
    try {
      const client = CacheClient.getInstance().getClient();

      // Verificar se o cliente está conectado
      if (!CacheClient.getInstance().isReady()) {
        return false;
      }

      // Definir tempo de expiração
      const result = await client.expire(key, seconds);

      return result === 1;
    } catch (error) {
      logger.error('Erro ao definir tempo de expiração:', error);
      return false;
    }
  },

  /**
   * Adiciona um valor a um conjunto ordenado (Sorted Set)
   * @param key - Chave do conjunto ordenado
   * @param score - Pontuação para ordenação
   * @param member - Valor a ser adicionado
   * @returns Verdadeiro se o valor foi adicionado com sucesso
   */
  async zadd(key: string, score: number, member: string): Promise<boolean> {
    try {
      const client = CacheClient.getInstance().getClient();

      // Verificar se o cliente está conectado
      if (!CacheClient.getInstance().isReady()) {
        return false;
      }

      // Adicionar ao conjunto ordenado
      await client.zAdd(key, [{ score, value: member }]);

      return true;
    } catch (error) {
      logger.error('Erro ao adicionar ao conjunto ordenado:', error);
      return false;
    }
  },

  /**
   * Remove elementos de um conjunto ordenado (Sorted Set) por índice
   * @param key - Chave do conjunto ordenado
   * @param start - Índice inicial (inclusive)
   * @param stop - Índice final (inclusive)
   * @returns Verdadeiro se os elementos foram removidos com sucesso
   */
  async zremrangebyrank(
    key: string,
    start: number,
    stop: number
  ): Promise<boolean> {
    try {
      const client = CacheClient.getInstance().getClient();

      // Verificar se o cliente está conectado
      if (!CacheClient.getInstance().isReady()) {
        return false;
      }

      // Remover elementos por índice
      await client.zRemRangeByRank(key, start, stop);

      return true;
    } catch (error) {
      logger.error(
        'Erro ao remover elementos do conjunto ordenado por índice:',
        error
      );
      return false;
    }
  },

  /**
   * Obtém elementos de um conjunto ordenado (Sorted Set) por índice, em ordem reversa
   * @param key - Chave do conjunto ordenado
   * @param start - Índice inicial (inclusive)
   * @param stop - Índice final (inclusive)
   * @returns Array com os elementos ou null em caso de erro
   */
  async zrevrange(
    key: string,
    start: number,
    stop: number
  ): Promise<string[] | null> {
    try {
      const client = CacheClient.getInstance().getClient();

      // Verificar se o cliente está conectado
      if (!CacheClient.getInstance().isReady()) {
        return null;
      }

      // Obter elementos por índice em ordem reversa
      const members = await client.zRevRange(key, start, stop);

      return members;
    } catch (error) {
      logger.error(
        'Erro ao obter elementos do conjunto ordenado em ordem reversa:',
        error
      );
      return null;
    }
  },

  /**
   * Obtém chaves que correspondem a um padrão
   * @param pattern - Padrão para busca de chaves
   * @returns Array com as chaves encontradas ou null em caso de erro
   */
  async keys(pattern: string): Promise<string[] | null> {
    try {
      const client = CacheClient.getInstance().getClient();

      // Verificar se o cliente está conectado
      if (!CacheClient.getInstance().isReady()) {
        return null;
      }

      // Obter chaves que correspondem ao padrão
      const keys = await client.keys(pattern);

      return keys;
    } catch (error) {
      logger.error('Erro ao buscar chaves no cache:', error);
      return null;
    }
  },

  /**
   * Obtém estatísticas do servidor de cache
   * @returns Objeto com estatísticas ou null em caso de erro
   */
  async getStats(): Promise<Record<string, string> | null> {
    try {
      const client = CacheClient.getInstance().getClient();

      // Verificar se o cliente está conectado
      if (!CacheClient.getInstance().isReady()) {
        return null;
      }

      // Obter informações do servidor
      const info = await client.info();

      // Parsear informações
      const stats: Record<string, string> = {};

      if (typeof info === 'string') {
        // Dividir por seções
        const sections = info.split('#');

        for (const section of sections) {
          // Dividir por linhas
          const lines = section.trim().split('\n');

          // Pular a primeira linha (nome da seção)
          for (let i = 1; i < lines.length; i++) {
            const line = lines[i].trim();

            if (line && line.includes(':')) {
              const [key, value] = line.split(':');
              stats[key.trim()] = value.trim();
            }
          }
        }
      }

      return stats;
    } catch (error) {
      logger.error('Erro ao obter estatísticas do cache:', error);
      return null;
    }
  },

  /**
   * Obtém o tempo de vida restante de uma chave
   * @param key - Chave para verificar
   * @returns Tempo de vida em segundos ou -1 se a chave não existir ou -2 se não tiver expiração
   */
  async ttl(key: string): Promise<number> {
    try {
      const client = CacheClient.getInstance().getClient();

      // Verificar se o cliente está conectado
      if (!CacheClient.getInstance().isReady()) {
        return -1;
      }

      // Obter TTL da chave
      const ttl = await client.ttl(key);

      return ttl;
    } catch (error) {
      logger.error('Erro ao obter TTL da chave:', error);
      return -1;
    }
  },
  /**
   * Armazena um valor no cache
   * @param type - Tipo de dado
   * @param key - Chave para o valor
   * @param value - Valor a ser armazenado
   * @param options - Opções de cache
   * @returns Verdadeiro se o valor foi armazenado com sucesso
   */
  async set(
    type: CacheableDataType,
    key: string,
    value: unknown,
    options: CacheOptions = {}
  ): Promise<boolean> {
    try {
      const client = CacheClient.getInstance().getClient();

      // Verificar se o cliente está conectado
      if (!CacheClient.getInstance().isReady()) {
        return false;
      }

      // Construir chave completa com prefixo
      const fullKey = `${cacheConfig.policies.keyPrefixes[type]}${key}`;

      // Serializar valor
      let serializedValue = JSON.stringify(value);

      // Comprimir dados se necessário
      const shouldCompress =
        options.compress !== undefined
          ? options.compress
          : cacheConfig.compression.enabled &&
            serializedValue.length > cacheConfig.compression.threshold;

      if (shouldCompress) {
        const compressed = await gzipAsync(Buffer.from(serializedValue));
        serializedValue = compressed.toString('base64');

        // Adicionar prefixo para indicar que o valor está comprimido
        serializedValue = `gz:${serializedValue}`;
      }

      // Determinar TTL com base nas políticas de cache
      let ttl = options.ttl;

      if (!ttl) {
        try {
          // Importar dinamicamente para evitar dependência circular
          const { cachePolicyService } = await import('./cachePolicyService');
          ttl = cachePolicyService.getTTL(type, key);

          // Incrementar contador de acesso para estratégias adaptativas
          cachePolicyService.incrementAccessCounter(type, key);
        } catch (policyError) {
          // Fallback para TTL padrão do tipo
          ttl =
            cacheConfig.policies.ttlByType[type] ||
            cacheConfig.policies.defaultTTL;
          logger.warn(
            `Erro ao aplicar política de cache para ${type}, usando TTL padrão:`,
            policyError
          );
        }
      }

      // Armazenar no cache com TTL
      if (ttl > 0) {
        await client.set(fullKey, serializedValue, {
          EX: ttl,
        });
      } else {
        // TTL 0 significa sem expiração
        await client.set(fullKey, serializedValue);
      }

      if (cacheConfig.logging.enabled) {
        logger.debug(
          `Cache SET: ${fullKey} (TTL: ${ttl || 'none'}s, Compressed: ${shouldCompress})`
        );
      }

      return true;
    } catch (error) {
      logger.error('Erro ao armazenar em cache:', error);
      return false;
    }
  },

  /**
   * Recupera um valor do cache
   * @param type - Tipo de dado
   * @param key - Chave do valor
   * @returns Valor armazenado ou null se não encontrado
   */
  async get<T = unknown>(
    type: CacheableDataType,
    key: string
  ): Promise<T | null> {
    try {
      const client = CacheClient.getInstance().getClient();

      // Verificar se o cliente está conectado
      if (!CacheClient.getInstance().isReady()) {
        return null;
      }

      // Construir chave completa com prefixo
      const fullKey = `${cacheConfig.policies.keyPrefixes[type]}${key}`;

      // Buscar do cache
      const cachedValue = await client.get(fullKey);

      if (!cachedValue) {
        return null;
      }

      // Verificar se o valor está comprimido
      if (cachedValue.startsWith('gz:')) {
        // Remover prefixo
        const compressedValue = cachedValue.substring(3);

        // Descomprimir
        const buffer = Buffer.from(compressedValue, 'base64');
        const decompressed = await gunzipAsync(buffer);

        // Deserializar
        const value = JSON.parse(decompressed.toString());

        if (cacheConfig.logging.enabled) {
          logger.debug(`Cache HIT (compressed): ${fullKey}`);
        }

        // Atualizar TTL para estratégias de expiração deslizante
        try {
          // Importar dinamicamente para evitar dependência circular
          const { cachePolicyService } = await import('./cachePolicyService');

          // Incrementar contador de acesso para estratégias adaptativas
          cachePolicyService.incrementAccessCounter(type, key);

          // Atualizar TTL para estratégias deslizantes
          await cachePolicyService.updateSlidingExpiration(type, fullKey);
        } catch (policyError) {
          // Ignorar erros de política - o valor ainda será retornado
          if (cacheConfig.logging.enabled) {
            logger.warn(
              `Erro ao aplicar política de cache para ${type}:`,
              policyError
            );
          }
        }

        return value as T;
      }

      // Deserializar valor não comprimido
      const value = JSON.parse(cachedValue);

      if (cacheConfig.logging.enabled) {
        logger.debug(`Cache HIT: ${fullKey}`);
      }

      // Atualizar TTL para estratégias de expiração deslizante
      try {
        // Importar dinamicamente para evitar dependência circular
        const { cachePolicyService } = await import('./cachePolicyService');

        // Incrementar contador de acesso para estratégias adaptativas
        cachePolicyService.incrementAccessCounter(type, key);

        // Atualizar TTL para estratégias deslizantes
        await cachePolicyService.updateSlidingExpiration(type, fullKey);
      } catch (policyError) {
        // Ignorar erros de política - o valor ainda será retornado
        if (cacheConfig.logging.enabled) {
          logger.warn(
            `Erro ao aplicar política de cache para ${type}:`,
            policyError
          );
        }
      }

      return value as T;
    } catch (error) {
      logger.error('Erro ao recuperar do cache:', error);
      return null;
    }
  },

  /**
   * Remove um valor do cache
   * @param type - Tipo de dado
   * @param key - Chave do valor
   * @returns Verdadeiro se o valor foi removido com sucesso
   */
  async delete(type: CacheableDataType, key: string): Promise<boolean> {
    try {
      const client = CacheClient.getInstance().getClient();

      // Verificar se o cliente está conectado
      if (!CacheClient.getInstance().isReady()) {
        return false;
      }

      // Construir chave completa com prefixo
      const fullKey = `${cacheConfig.policies.keyPrefixes[type]}${key}`;

      // Remover do cache
      await client.del(fullKey);

      if (cacheConfig.logging.enabled) {
        logger.debug(`Cache DELETE: ${fullKey}`);
      }

      return true;
    } catch (error) {
      logger.error('Erro ao remover do cache:', error);
      return false;
    }
  },

  /**
   * Invalida todos os valores de um determinado tipo
   * @param type - Tipo de dado
   * @returns Verdadeiro se os valores foram invalidados com sucesso
   */
  async invalidateType(type: CacheableDataType): Promise<boolean> {
    try {
      const client = CacheClient.getInstance().getClient();

      // Verificar se o cliente está conectado
      if (!CacheClient.getInstance().isReady()) {
        return false;
      }

      // Construir padrão para busca de chaves
      const pattern = `${cacheConfig.policies.keyPrefixes[type]}*`;

      // Buscar todas as chaves que correspondem ao padrão
      const keys = await client.keys(pattern);

      if (keys.length > 0) {
        // Remover todas as chaves encontradas
        if (keys.length === 1) {
          await client.del(keys[0]);
        } else {
          await client.del(keys);
        }

        if (cacheConfig.logging.enabled) {
          logger.debug(`Cache INVALIDATE TYPE: ${type} (${keys.length} keys)`);
        }
      }

      return true;
    } catch (error) {
      logger.error('Erro ao invalidar tipo de cache:', error);
      return false;
    }
  },

  /**
   * Verifica se uma chave existe no cache
   * @param type - Tipo de dado
   * @param key - Chave a verificar
   * @returns Verdadeiro se a chave existe
   */
  async exists(type: CacheableDataType, key: string): Promise<boolean> {
    try {
      const client = CacheClient.getInstance().getClient();

      // Verificar se o cliente está conectado
      if (!CacheClient.getInstance().isReady()) {
        return false;
      }

      // Construir chave completa com prefixo
      const fullKey = `${cacheConfig.policies.keyPrefixes[type]}${key}`;

      // Verificar se a chave existe
      const exists = await client.exists(fullKey);

      return exists === 1;
    } catch (error) {
      logger.error('Erro ao verificar existência no cache:', error);
      return false;
    }
  },

  /**
   * Atualiza o TTL de uma chave
   * @param type - Tipo de dado
   * @param key - Chave a atualizar
   * @param ttl - Novo TTL em segundos
   * @returns Verdadeiro se o TTL foi atualizado com sucesso
   */
  async updateTTL(
    type: CacheableDataType,
    key: string,
    ttl: number
  ): Promise<boolean> {
    try {
      const client = CacheClient.getInstance().getClient();

      // Verificar se o cliente está conectado
      if (!CacheClient.getInstance().isReady()) {
        return false;
      }

      // Construir chave completa com prefixo
      const fullKey = `${cacheConfig.policies.keyPrefixes[type]}${key}`;

      // Atualizar TTL
      const result = await client.expire(fullKey, ttl);

      return result === 1;
    } catch (error) {
      logger.error('Erro ao atualizar TTL no cache:', error);
      return false;
    }
  },

  /**
   * Limpa todo o cache
   * @returns Verdadeiro se o cache foi limpo com sucesso
   */
  async clear(): Promise<boolean> {
    try {
      const client = CacheClient.getInstance().getClient();

      // Verificar se o cliente está conectado
      if (!CacheClient.getInstance().isReady()) {
        return false;
      }

      // Limpar todo o cache
      await client.flushdb();

      if (cacheConfig.logging.enabled) {
        logger.debug('Cache CLEAR: All keys removed');
      }

      return true;
    } catch (error) {
      logger.error('Erro ao limpar cache:', error);
      return false;
    }
  },

  /**
   * Obtém estatísticas do cache
   * @returns Objeto com estatísticas ou null em caso de erro
   */
  async getStats(): Promise<Record<string, string> | null> {
    try {
      const client = CacheClient.getInstance().getClient();

      // Verificar se o cliente está conectado
      if (!CacheClient.getInstance().isReady()) {
        return null;
      }

      // Obter informações do servidor
      const info = await client.info();

      // Processar informações
      const stats: Record<string, string> = {};
      const sections = info.split('#');

      for (const section of sections) {
        const lines = section.split('\r\n').filter(line => line.includes(':'));

        for (const line of lines) {
          const [key, value] = line.split(':');
          if (key && value) {
            stats[key.trim()] = value.trim();
          }
        }
      }

      return stats;
    } catch (error) {
      logger.error('Erro ao obter estatísticas do cache:', error);
      return null;
    }
  },

  /**
   * Invalida cache com base em um evento
   * @param event - Nome do evento
   * @param data - Dados adicionais para invalidação seletiva
   * @returns Verdadeiro se a invalidação foi bem-sucedida
   */
  async invalidateOnEvent(event: string, data: any = {}): Promise<boolean> {
    try {
      // Importar dinamicamente para evitar dependência circular
      const { cachePolicyService } = await import('./cachePolicyService');

      // Emitir evento de invalidação
      cachePolicyService.emitInvalidationEvent(event, data);

      if (cacheConfig.logging.enabled) {
        logger.debug(`Cache INVALIDATE EVENT: ${event}`, data);
      }

      return true;
    } catch (error) {
      logger.error(`Erro ao invalidar cache para evento ${event}:`, error);
      return false;
    }
  },

  /**
   * Remove chaves que correspondem a um padrão
   * @param pattern - Padrão para busca de chaves
   * @returns Número de chaves removidas ou -1 em caso de erro
   */
  async deleteByPattern(pattern: string): Promise<number> {
    try {
      const client = CacheClient.getInstance().getClient();

      // Verificar se o cliente está conectado
      if (!CacheClient.getInstance().isReady()) {
        return -1;
      }

      // Buscar chaves que correspondem ao padrão
      const keys = await client.keys(pattern);

      if (keys.length === 0) {
        return 0;
      }

      // Remover chaves encontradas
      let deleted = 0;
      if (keys.length === 1) {
        deleted = await client.del(keys[0]);
      } else {
        deleted = await client.del(keys);
      }

      if (cacheConfig.logging.enabled) {
        logger.debug(`Cache DELETE PATTERN: ${pattern} (${deleted} keys)`);
      }

      return deleted;
    } catch (error) {
      logger.error(`Erro ao remover chaves por padrão ${pattern}:`, error);
      return -1;
    }
  },

  /**
   * Obtém o cliente Valkey
   * @returns Cliente Valkey
   */
  async getClient(): Promise<RedisClientType | RedisClusterType> {
    return CacheClient.getInstance().getClient();
  },
};
