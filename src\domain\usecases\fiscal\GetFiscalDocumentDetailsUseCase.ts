/**
 * Get Fiscal Document Details Use Case
 *
 * Caso de uso para obter detalhes de um documento fiscal.
 * Parte da implementação da tarefa 8.7.2 - Gestão de documentos fiscais
 */

import { FiscalDocument } from '../../entities/FiscalDocument';
import { FiscalDocumentRepository } from '../../repositories/FiscalDocumentRepository';

export interface GetFiscalDocumentDetailsRequest {
  id: string;
}

export interface GetFiscalDocumentDetailsResponse {
  success: boolean;
  data?: FiscalDocument;
  error?: string;
}

export class GetFiscalDocumentDetailsUseCase {
  constructor(private fiscalDocumentRepository: FiscalDocumentRepository) {}

  async execute(
    request: GetFiscalDocumentDetailsRequest
  ): Promise<GetFiscalDocumentDetailsResponse> {
    try {
      // Validar os dados de entrada
      if (!request.id) {
        return {
          success: false,
          error: 'ID do documento é obrigatório.',
        };
      }

      // Obter o documento fiscal
      const document = await this.fiscalDocumentRepository.getById(request.id);

      if (!document) {
        return {
          success: false,
          error: `Documento fiscal com ID ${request.id} não encontrado.`,
        };
      }

      return {
        success: true,
        data: document,
      };
    } catch (error) {
      console.error('Erro ao obter detalhes do documento fiscal:', error);

      return {
        success: false,
        error:
          error instanceof Error
            ? error.message
            : 'Erro desconhecido ao obter detalhes do documento fiscal.',
      };
    }
  }
}
