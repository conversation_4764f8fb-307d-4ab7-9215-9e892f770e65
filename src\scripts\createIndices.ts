/**
 * Script para criar índices otimizados no banco de dados
 *
 * Este script cria índices para melhorar a performance de consultas frequentes.
 */

import { pgHelper } from '@repository/pgHelper';
import { logger } from '@utils/logger';

/**
 * Interface para definição de índice
 */
interface IndexDefinition {
  /**
   * Nome do índice
   */
  name: string;

  /**
   * Tabela a ser indexada
   */
  table: string;

  /**
   * Colunas a serem indexadas
   */
  columns: string[];

  /**
   * Tipo de índice (btree, hash, gin, gist, etc.)
   * @default 'btree'
   */
  type?: string;

  /**
   * Se o índice deve ser único
   * @default false
   */
  unique?: boolean;

  /**
   * Condição para o índice (WHERE)
   */
  where?: string;

  /**
   * Descrição do propósito do índice
   */
  description: string;
}

/**
 * Lista de índices a serem criados
 */
const INDICES: IndexDefinition[] = [
  // Índices para usuários
  {
    name: 'idx_users_email',
    table: 'users',
    columns: ['email'],
    unique: true,
    description: 'Otimiza busca de usuários por email',
  },
  {
    name: 'idx_users_active',
    table: 'users',
    columns: ['active'],
    description: 'Otimiza filtragem de usuários ativos/inativos',
  },

  // Índices para produtos
  {
    name: 'idx_products_slug',
    table: 'products',
    columns: ['slug'],
    unique: true,
    description: 'Otimiza busca de produtos por slug',
  },
  {
    name: 'idx_products_category',
    table: 'products',
    columns: ['category_id'],
    description: 'Otimiza busca de produtos por categoria',
  },
  {
    name: 'idx_products_active_featured',
    table: 'products',
    columns: ['active', 'featured'],
    description: 'Otimiza busca de produtos ativos e/ou em destaque',
  },
  {
    name: 'idx_products_price',
    table: 'products',
    columns: ['price'],
    description: 'Otimiza ordenação e filtragem por preço',
  },

  // Índices para categorias
  {
    name: 'idx_categories_slug',
    table: 'categories',
    columns: ['slug'],
    unique: true,
    description: 'Otimiza busca de categorias por slug',
  },
  {
    name: 'idx_categories_parent',
    table: 'categories',
    columns: ['parent_id'],
    description: 'Otimiza busca de subcategorias',
  },

  // Índices para pedidos
  {
    name: 'idx_orders_user',
    table: 'orders',
    columns: ['user_id'],
    description: 'Otimiza busca de pedidos por usuário',
  },
  {
    name: 'idx_orders_status',
    table: 'orders',
    columns: ['status'],
    description: 'Otimiza filtragem de pedidos por status',
  },
  {
    name: 'idx_orders_created_at',
    table: 'orders',
    columns: ['created_at'],
    description: 'Otimiza ordenação e filtragem por data de criação',
  },

  // Índices para pagamentos
  {
    name: 'idx_payments_order',
    table: 'payments',
    columns: ['order_id'],
    description: 'Otimiza busca de pagamentos por pedido',
  },
  {
    name: 'idx_payments_status',
    table: 'payments',
    columns: ['status'],
    description: 'Otimiza filtragem de pagamentos por status',
  },

  // Índices para sessões
  {
    name: 'idx_sessions_user',
    table: 'sessions',
    columns: ['user_id'],
    description: 'Otimiza busca de sessões por usuário',
  },
  {
    name: 'idx_sessions_expires',
    table: 'sessions',
    columns: ['expires_at'],
    description: 'Otimiza limpeza de sessões expiradas',
  },

  // Índices para auditoria
  {
    name: 'idx_audit_logs_user',
    table: 'audit_logs',
    columns: ['user_id'],
    description: 'Otimiza busca de logs por usuário',
  },
  {
    name: 'idx_audit_logs_event_type',
    table: 'audit_logs',
    columns: ['event_type'],
    description: 'Otimiza filtragem por tipo de evento',
  },
  {
    name: 'idx_audit_logs_timestamp',
    table: 'audit_logs',
    columns: ['timestamp'],
    description: 'Otimiza ordenação e filtragem por data',
  },
  {
    name: 'idx_audit_logs_resource',
    table: 'audit_logs',
    columns: ['resource', 'resource_id'],
    description: 'Otimiza busca de logs por recurso',
  },

  // Índices para busca de texto
  {
    name: 'idx_products_text_search',
    table: 'products',
    columns: ['name', 'description'],
    type: 'gin',
    description: 'Otimiza busca de texto em produtos',
    where: 'active = true',
  },
  {
    name: 'idx_categories_text_search',
    table: 'categories',
    columns: ['name', 'description'],
    type: 'gin',
    description: 'Otimiza busca de texto em categorias',
    where: 'active = true',
  },
];

/**
 * Cria um índice no banco de dados
 * @param index - Definição do índice
 * @returns Verdadeiro se o índice foi criado com sucesso
 */
async function createIndex(index: IndexDefinition): Promise<boolean> {
  try {
    // Verificar se o índice já existe
    const checkQuery = `
      SELECT 1 FROM pg_indexes 
      WHERE indexname = $1
    `;

    const checkResult = await pgHelper.query(checkQuery, [index.name]);

    if (checkResult.rowCount > 0) {
      logger.info(`Índice ${index.name} já existe, pulando...`);
      return true;
    }

    // Construir consulta SQL para criar o índice
    let createQuery = `
      CREATE ${index.unique ? 'UNIQUE ' : ''}INDEX ${index.name}
      ON ${index.table} USING ${index.type || 'btree'}
      (${index.columns.join(', ')})
    `;

    // Adicionar condição WHERE se especificada
    if (index.where) {
      createQuery += ` WHERE ${index.where}`;
    }

    // Executar consulta
    await pgHelper.query(createQuery);

    // Adicionar comentário ao índice
    const commentQuery = `
      COMMENT ON INDEX ${index.name} IS '${index.description}'
    `;

    await pgHelper.query(commentQuery);

    logger.info(`Índice ${index.name} criado com sucesso`);
    return true;
  } catch (error) {
    logger.error(`Erro ao criar índice ${index.name}:`, error);
    return false;
  }
}

/**
 * Cria todos os índices definidos
 */
export async function createAllIndices(): Promise<void> {
  try {
    logger.info('Iniciando criação de índices otimizados');

    let successCount = 0;
    let errorCount = 0;

    // Criar cada índice
    for (const index of INDICES) {
      const success = await createIndex(index);

      if (success) {
        successCount++;
      } else {
        errorCount++;
      }
    }

    logger.info(`Criação de índices concluída: ${successCount} criados, ${errorCount} erros`);
  } catch (error) {
    logger.error('Erro durante criação de índices:', error);
    throw error;
  }
}

// Executar criação de índices se este arquivo for executado diretamente
if (require.main === module) {
  createAllIndices()
    .then(() => {
      logger.info('Script de criação de índices concluído');
      process.exit(0);
    })
    .catch((error) => {
      logger.error('Erro no script de criação de índices:', error);
      process.exit(1);
    });
}
