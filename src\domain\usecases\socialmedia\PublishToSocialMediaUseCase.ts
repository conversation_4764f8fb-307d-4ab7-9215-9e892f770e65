/**
 * Publish To Social Media Use Case
 *
 * Caso de uso para publicar conteúdo nas redes sociais.
 * Parte da implementação da tarefa 8.5.3 - Integração com redes sociais
 */

import {
  SocialMediaPlatform,
  SocialMediaPost,
  SocialMediaPostResult,
  SocialMediaService,
} from '../../services/SocialMediaService';

export interface PublishToSocialMediaRequest {
  content: string;
  mediaUrls?: string[];
  link?: string;
  linkTitle?: string;
  linkDescription?: string;
  linkImage?: string;
  platforms: SocialMediaPlatform[];
  scheduledAt?: Date;
}

export interface PublishToSocialMediaResponse {
  success: boolean;
  results: Record<SocialMediaPlatform, SocialMediaPostResult>;
  error?: string;
}

export class PublishToSocialMediaUseCase {
  private socialMediaServices: Record<SocialMediaPlatform, SocialMediaService>;

  constructor(socialMediaServices: Record<SocialMediaPlatform, SocialMediaService>) {
    this.socialMediaServices = socialMediaServices;
  }

  async execute(request: PublishToSocialMediaRequest): Promise<PublishToSocialMediaResponse> {
    try {
      // Validar os dados de entrada
      if (!this.validateRequest(request)) {
        return {
          success: false,
          results: {},
          error: 'Dados inválidos para publicação nas redes sociais.',
        };
      }

      // Preparar o post para cada plataforma
      const post: SocialMediaPost = {
        content: request.content,
        mediaUrls: request.mediaUrls,
        link: request.link,
        linkTitle: request.linkTitle,
        linkDescription: request.linkDescription,
        linkImage: request.linkImage,
        scheduledAt: request.scheduledAt,
      };

      // Publicar em cada plataforma solicitada
      const results: Record<SocialMediaPlatform, SocialMediaPostResult> = {};
      let overallSuccess = true;

      for (const platform of request.platforms) {
        const service = this.socialMediaServices[platform];

        if (!service) {
          results[platform] = {
            success: false,
            error: `Serviço para a plataforma ${platform} não disponível.`,
            timestamp: new Date(),
          };
          overallSuccess = false;
          continue;
        }

        try {
          // Verificar se a plataforma está conectada
          const isConnected = await service.isPlatformConnected(platform);

          if (!isConnected) {
            results[platform] = {
              success: false,
              error: `Plataforma ${platform} não está conectada.`,
              timestamp: new Date(),
            };
            overallSuccess = false;
            continue;
          }

          // Publicar ou agendar o post
          let result: SocialMediaPostResult;

          if (request.scheduledAt && request.scheduledAt > new Date()) {
            result = await service.schedulePost(platform, post, request.scheduledAt);
          } else {
            result = await service.publishPost(platform, post);
          }

          results[platform] = result;

          if (!result.success) {
            overallSuccess = false;
          }
        } catch (error) {
          results[platform] = {
            success: false,
            error:
              error instanceof Error
                ? error.message
                : `Erro desconhecido ao publicar no ${platform}.`,
            timestamp: new Date(),
          };
          overallSuccess = false;
        }
      }

      return {
        success: overallSuccess,
        results,
      };
    } catch (error) {
      console.error('Erro ao publicar nas redes sociais:', error);

      return {
        success: false,
        results: {},
        error:
          error instanceof Error
            ? error.message
            : 'Erro desconhecido ao publicar nas redes sociais.',
      };
    }
  }

  private validateRequest(request: PublishToSocialMediaRequest): boolean {
    // Validar conteúdo
    if (!request.content || request.content.trim() === '') {
      return false;
    }

    // Validar plataformas
    if (!request.platforms || request.platforms.length === 0) {
      return false;
    }

    // Validar plataformas suportadas
    const supportedPlatforms: SocialMediaPlatform[] = [
      'facebook',
      'instagram',
      'twitter',
      'linkedin',
      'youtube',
      'tiktok',
    ];

    for (const platform of request.platforms) {
      if (!supportedPlatforms.includes(platform)) {
        return false;
      }
    }

    // Validar URLs de mídia
    if (request.mediaUrls && request.mediaUrls.length > 0) {
      for (const url of request.mediaUrls) {
        if (!this.isValidUrl(url)) {
          return false;
        }
      }
    }

    // Validar URL do link
    if (request.link && !this.isValidUrl(request.link)) {
      return false;
    }

    // Validar data de agendamento
    if (request.scheduledAt && request.scheduledAt < new Date()) {
      return false;
    }

    return true;
  }

  private isValidUrl(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch (error) {
      return false;
    }
  }
}
