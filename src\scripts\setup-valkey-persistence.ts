/**
 * Script para configuração de persistência do Valkey
 * 
 * Este script configura a persistência de dados para o Valkey,
 * incluindo configurações de RDB (snapshots) e AOF (append-only file),
 * além de configurar backups automáticos.
 */

import { createClient } from 'valkey';
import { getPersistenceConfig, createPersistenceDirectories, PersistenceType } from '@config/cache/persistence.config';
import { logger } from '@utils/logger';
import { exec } from 'child_process';
import { promisify } from 'util';
import * as fs from 'fs';
import * as path from 'path';
import * as cron from 'node-cron';

// Promisificar funções
const execAsync = promisify(exec);
const writeFileAsync = promisify(fs.writeFile);
const readFileAsync = promisify(fs.readFile);
const mkdirAsync = promisify(fs.mkdir);
const statAsync = promisify(fs.stat);
const readdirAsync = promisify(fs.readdir);
const unlinkAsync = promisify(fs.unlink);

/**
 * Verifica se o Valkey está instalado
 */
async function checkValkeyInstallation(): Promise<boolean> {
  try {
    const { stdout } = await execAsync('valkey-cli --version');
    logger.info(`Valkey instalado: ${stdout.trim()}`);
    return true;
  } catch (error) {
    logger.error('Valkey não está instalado ou não está no PATH');
    return false;
  }
}

/**
 * Configura persistência RDB
 */
async function configureRDB(): Promise<void> {
  try {
    const config = getPersistenceConfig();
    
    if (config.type !== PersistenceType.RDB && config.type !== PersistenceType.BOTH) {
      logger.info('Persistência RDB não está habilitada. Pulando configuração.');
      return;
    }
    
    logger.info('Configurando persistência RDB');
    
    // Verificar diretório de dados
    await mkdirAsync(config.rdb.dir, { recursive: true });
    
    // Configurar salvamento automático
    const saveCommands = config.rdb.saveSettings.map(([seconds, changes]) => {
      return `CONFIG SET save "${seconds} ${changes}"`;
    });
    
    // Executar comandos de configuração
    for (const command of saveCommands) {
      await execAsync(`valkey-cli ${command}`);
    }
    
    // Configurar outras opções
    await execAsync(`valkey-cli CONFIG SET stop-writes-on-bgsave-error ${config.rdb.stopWritesOnError ? 'yes' : 'no'}`);
    await execAsync(`valkey-cli CONFIG SET rdbcompression ${config.rdb.compression ? 'yes' : 'no'}`);
    await execAsync(`valkey-cli CONFIG SET rdbchecksum ${config.rdb.checksum ? 'yes' : 'no'}`);
    await execAsync(`valkey-cli CONFIG SET dbfilename ${config.rdb.filename}`);
    await execAsync(`valkey-cli CONFIG SET dir ${config.rdb.dir}`);
    
    logger.info('Persistência RDB configurada com sucesso');
    
    // Executar salvamento manual
    logger.info('Executando salvamento RDB manual');
    await execAsync('valkey-cli SAVE');
    logger.info('Salvamento RDB concluído');
  } catch (error) {
    logger.error('Erro ao configurar persistência RDB:', error);
    throw error;
  }
}

/**
 * Configura persistência AOF
 */
async function configureAOF(): Promise<void> {
  try {
    const config = getPersistenceConfig();
    
    if (config.type !== PersistenceType.AOF && config.type !== PersistenceType.BOTH) {
      logger.info('Persistência AOF não está habilitada. Pulando configuração.');
      return;
    }
    
    logger.info('Configurando persistência AOF');
    
    // Verificar diretório de dados
    await mkdirAsync(config.aof.dir, { recursive: true });
    
    // Configurar AOF
    await execAsync(`valkey-cli CONFIG SET appendonly yes`);
    await execAsync(`valkey-cli CONFIG SET appendfilename "${config.aof.filename}"`);
    await execAsync(`valkey-cli CONFIG SET appendfsync ${config.aof.syncStrategy}`);
    await execAsync(`valkey-cli CONFIG SET no-appendfsync-on-rewrite ${config.aof.autoRewrite ? 'yes' : 'no'}`);
    await execAsync(`valkey-cli CONFIG SET auto-aof-rewrite-percentage ${config.aof.rewritePercentage}`);
    await execAsync(`valkey-cli CONFIG SET auto-aof-rewrite-min-size ${config.aof.rewriteMinSize}`);
    await execAsync(`valkey-cli CONFIG SET aof-load-truncated ${config.aof.loadTruncated ? 'yes' : 'no'}`);
    await execAsync(`valkey-cli CONFIG SET aof-use-rdb-preamble ${config.aof.useRdbPreamble ? 'yes' : 'no'}`);
    
    logger.info('Persistência AOF configurada com sucesso');
  } catch (error) {
    logger.error('Erro ao configurar persistência AOF:', error);
    throw error;
  }
}

/**
 * Cria script de backup
 */
async function createBackupScript(): Promise<string> {
  try {
    const config = getPersistenceConfig();
    
    if (!config.backup.enabled) {
      logger.info('Backup automático não está habilitado. Pulando criação de script.');
      return '';
    }
    
    logger.info('Criando script de backup');
    
    // Criar diretório de backups
    await mkdirAsync(config.backup.dir, { recursive: true });
    
    // Gerar script de backup
    const timestamp = '$(date +"%Y%m%d_%H%M%S")';
    const backupFilename = `valkey_backup_${timestamp}`;
    const backupPath = path.join(config.backup.dir, backupFilename);
    
    const script = [
      '#!/bin/bash',
      '',
      '# Script de backup automático para Valkey',
      `# Gerado automaticamente em ${new Date().toISOString()}`,
      '',
      '# Configurações',
      `BACKUP_DIR="${config.backup.dir}"`,
      `RETENTION=${config.backup.retention}`,
      `COMPRESS=${config.backup.compress ? 'true' : 'false'}`,
      '',
      '# Criar diretório de backup se não existir',
      'mkdir -p "$BACKUP_DIR"',
      '',
      '# Executar backup',
      'echo "Iniciando backup do Valkey..."',
      'TIMESTAMP=$(date +"%Y%m%d_%H%M%S")',
      'BACKUP_FILE="$BACKUP_DIR/valkey_backup_$TIMESTAMP"',
      '',
      '# Executar comando de backup',
      'valkey-cli SAVE',
      '',
      '# Copiar arquivos de dados',
      `cp ${config.rdb.dir}/${config.rdb.filename} "$BACKUP_FILE.rdb"`,
      config.type === PersistenceType.BOTH || config.type === PersistenceType.AOF 
        ? `cp ${config.aof.dir}/${config.aof.filename} "$BACKUP_FILE.aof"` 
        : '# AOF não habilitado',
      '',
      '# Comprimir backup se necessário',
      'if [ "$COMPRESS" = "true" ]; then',
      '  echo "Comprimindo backup..."',
      '  tar -czf "$BACKUP_FILE.tar.gz" "$BACKUP_FILE.rdb"' + 
        (config.type === PersistenceType.BOTH || config.type === PersistenceType.AOF ? ' "$BACKUP_FILE.aof"' : ''),
      '  rm "$BACKUP_FILE.rdb"' + 
        (config.type === PersistenceType.BOTH || config.type === PersistenceType.AOF ? ' "$BACKUP_FILE.aof"' : ''),
      '  echo "Backup comprimido: $BACKUP_FILE.tar.gz"',
      'else',
      '  echo "Backup concluído: $BACKUP_FILE.rdb"' + 
        (config.type === PersistenceType.BOTH || config.type === PersistenceType.AOF ? ' e $BACKUP_FILE.aof' : ''),
      'fi',
      '',
      '# Limpar backups antigos',
      'echo "Verificando backups antigos..."',
      'BACKUP_COUNT=$(ls -1 "$BACKUP_DIR"/valkey_backup_* 2>/dev/null | wc -l)',
      '',
      'if [ "$BACKUP_COUNT" -gt "$RETENTION" ]; then',
      '  echo "Removendo backups antigos..."',
      '  ls -1t "$BACKUP_DIR"/valkey_backup_* | tail -n +$(($RETENTION + 1)) | xargs rm -f',
      'fi',
      '',
      'echo "Processo de backup concluído com sucesso"',
      '',
    ];
    
    // Adicionar código para destinos externos
    if (config.backup.destinations && config.backup.destinations.length > 0) {
      script.push('# Copiar para destinos externos');
      
      config.backup.destinations.forEach(dest => {
        script.push(`echo "Copiando backup para ${dest.type}:${dest.path}..."`);
        
        switch (dest.type) {
          case 's3':
            script.push(`aws s3 cp "$BACKUP_FILE.${config.backup.compress ? 'tar.gz' : 'rdb'}" "${dest.path}/"`);
            break;
          case 'ftp':
            script.push(`curl -T "$BACKUP_FILE.${config.backup.compress ? 'tar.gz' : 'rdb'}" "ftp://${dest.credentials?.username}:${dest.credentials?.password}@${dest.path}"`);
            break;
          case 'sftp':
            script.push(`scp "$BACKUP_FILE.${config.backup.compress ? 'tar.gz' : 'rdb'}" "${dest.path}"`);
            break;
          case 'local':
            script.push(`cp "$BACKUP_FILE.${config.backup.compress ? 'tar.gz' : 'rdb'}" "${dest.path}/"`);
            break;
        }
      });
      
      script.push('');
    }
    
    // Salvar script
    const scriptPath = path.join(config.backup.dir, 'valkey_backup.sh');
    await writeFileAsync(scriptPath, script.join('\n'));
    
    // Tornar script executável
    await execAsync(`chmod +x ${scriptPath}`);
    
    logger.info(`Script de backup criado em ${scriptPath}`);
    
    return scriptPath;
  } catch (error) {
    logger.error('Erro ao criar script de backup:', error);
    throw error;
  }
}

/**
 * Configura cron job para backup automático
 * @param scriptPath Caminho do script de backup
 */
async function setupBackupCron(scriptPath: string): Promise<void> {
  try {
    const config = getPersistenceConfig();
    
    if (!config.backup.enabled) {
      logger.info('Backup automático não está habilitado. Pulando configuração de cron.');
      return;
    }
    
    logger.info('Configurando cron job para backup automático');
    
    // Criar arquivo crontab
    const cronPath = path.join(config.backup.dir, 'valkey_backup.cron');
    await writeFileAsync(cronPath, `${config.backup.schedule} ${scriptPath} >> ${path.join(config.backup.dir, 'backup.log')} 2>&1\n`);
    
    // Instalar crontab
    await execAsync(`crontab ${cronPath}`);
    
    logger.info('Cron job configurado com sucesso');
    
    // Executar backup inicial
    logger.info('Executando backup inicial');
    await execAsync(scriptPath);
    logger.info('Backup inicial concluído');
  } catch (error) {
    logger.error('Erro ao configurar cron job para backup:', error);
    throw error;
  }
}

/**
 * Configura persistência do Valkey
 */
async function setupValkeyPersistence(): Promise<void> {
  try {
    logger.info('Iniciando configuração de persistência do Valkey');
    
    // Verificar instalação do Valkey
    const isInstalled = await checkValkeyInstallation();
    
    if (!isInstalled) {
      logger.error('Valkey não está instalado. Instale o Valkey antes de continuar.');
      process.exit(1);
    }
    
    // Criar diretórios para persistência
    await createPersistenceDirectories();
    
    // Configurar RDB
    await configureRDB();
    
    // Configurar AOF
    await configureAOF();
    
    // Criar script de backup
    const backupScriptPath = await createBackupScript();
    
    // Configurar cron job para backup
    if (backupScriptPath) {
      await setupBackupCron(backupScriptPath);
    }
    
    logger.info('Configuração de persistência do Valkey concluída com sucesso');
  } catch (error) {
    logger.error('Erro durante configuração de persistência do Valkey:', error);
    throw error;
  }
}

/**
 * Função principal
 */
async function main(): Promise<void> {
  try {
    await setupValkeyPersistence();
    process.exit(0);
  } catch (error) {
    logger.error('Erro ao executar script:', error);
    process.exit(1);
  }
}

// Executar script
if (require.main === module) {
  main();
}

export { setupValkeyPersistence };
