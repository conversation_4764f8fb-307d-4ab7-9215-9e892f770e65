---
import { actions } from 'astro:actions';
import ControlButtons from '@components/form/ControlButtons.astro';
import InputHidden from '@components/form/InputHidden.astro';
import InputText from '@components/form/InputText.astro';
import InputTextLookup from '@components/form/InputTextLookup.astro';
import BaseLayout from '@layouts/BaseLayout.astro';

// Obter parâmetro da URL
const { ulid_order = '' } = Astro.params;

// Buscar dados do pedido
let data = {};
try {
  const result = await Astro.callAction(actions.orderAction.read, {
    filter: 'ulid_order',
    ulid_order,
  });
  data = result.data || {};
} catch (error) {
  console.error('Erro ao carregar pedido:', error);
  return Astro.redirect('/admin/financial/order?error=load');
}

// Buscar status de pedido disponíveis
let orderStatuses = [];
try {
  const result = await Astro.callAction(actions.orderStatusAction.read, {
    filter: 'active',
    active: true,
  });
  orderStatuses = result.data || [];
} catch (error) {
  console.error('Erro ao carregar status:', error);
}

// Buscar usuários disponíveis
let users = [];
try {
  const result = await Astro.callAction(actions.userAction.read, {
    filter: 'active',
    active: true,
  });
  users = result.data || [];
} catch (error) {
  console.error('Erro ao carregar usuários:', error);
}
---

<BaseLayout title="Formulário de Pedido">
  <div class="container mx-auto p-4">
    <h1 class="text-2xl font-bold mb-4">
      {ulid_order ? "Editar" : "Novo"} Pedido
    </h1>

    <form 
      method="POST" 
      action={actions.orderAction.update}
      class="space-y-4"
      data-form-type="order"
    >
      <!-- Campos ocultos -->
      <InputHidden ulid={data.ulid_order ?? ""} field="ulid_order" />

      <!-- Status do Pedido -->
      <InputTextLookup 
        label="Status do Pedido" 
        name="order_status" 
        value={data.order_status ?? ""} 
        options={orderStatuses}
        required={true}
      />

      <!-- Usuário -->
      <InputTextLookup 
        label="Usuário" 
        name="user" 
        value={data.user ?? ""} 
        options={users}
        required={true}
      />

      <!-- Total -->
      <InputText 
        label="Total" 
        name="total" 
        value={data.total ?? "0"} 
        required={true}
        type="number"
        min="0"
        step="0.01"
      />

      <!-- Botões de controle -->
      <ControlButtons 
        saveLabel={ulid_order ? "Atualizar" : "Criar"}
        cancelHref="/admin/financial/order"
      />
    </form>
  </div>
</BaseLayout>

<script>
// Validação client-side
document.addEventListener('DOMContentLoaded', () => {
  const form = document.querySelector('form[data-form-type="order"]');
  if (!form) return;

  form.addEventListener('submit', async (e) => {
    e.preventDefault();

    // Validação dos campos
    const total = form.querySelector('input[name="total"]').value;
    const orderStatus = form.querySelector('input[name="order_status"]').value;
    const user = form.querySelector('input[name="user"]').value;

    if (!total || isNaN(total) || Number(total) < 0) {
      alert('Total inválido');
      return;
    }

    if (!orderStatus) {
      alert('Status do pedido é obrigatório');
      return;
    }

    if (!user) {
      alert('Usuário é obrigatório');
      return;
    }

    try {
      const formData = new FormData(form);
      const response = await fetch(form.action, {
        method: 'POST',
        body: formData
      });

      const result = await response.json();
      
      if (result.error) {
        alert(result.error);
        return;
      }

      // Redireciona em caso de sucesso
      window.location.href = '/admin/financial/order?success=true';
    } catch (error) {
      console.error('Erro ao salvar:', error);
      alert('Erro ao salvar pedido. Tente novamente.');
    }
  });
});
</script>

<style>
.error-message {
  color: red;
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

.success-message {
  color: green;
  font-size: 0.875rem;
  margin-top: 0.25rem;
}
</style>