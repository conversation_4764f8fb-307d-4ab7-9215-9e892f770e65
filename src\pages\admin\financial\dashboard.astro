---
import Dashboard from '@components/data/Dashboard.astro';
import FinancialNav from '@components/navigation/FinancialNav.astro';
import { checkAdmin } from '@helpers/authGuard';
import AdminLayout from '@layouts/AdminLayout.astro';

// Protege a rota verificando se o usuário é admin
const authRedirect = await checkAdmin(Astro);
if (authRedirect) return authRedirect;
---

<AdminLayout title="Dashboard Financeiro">
  <div class="container mx-auto p-4">
    <h1 class="text-2xl font-bold mb-4">Gestão Financeira</h1>

    <FinancialNav />

    <Dashboard />
  </div>
</AdminLayout>
