/**
 * API para verificação de status de pagamento PIX
 *
 * Este endpoint verifica o status de um pagamento PIX pelo txid.
 */

import { efiPayService } from '@services/efiPayService';
import { RateLimitType, rateLimitService } from '@services/rateLimitService';
import { logger } from '@utils/logger';
import type { APIRoute } from 'astro';

export const GET: APIRoute = async ({ request }) => {
  try {
    // Obter IP do cliente para rate limiting
    const clientIp =
      request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown';

    // Verificar rate limit
    const rateLimitResult = await rateLimitService.check(clientIp, RateLimitType.PAYMENT, {
      maxRequests: 30,
      windowSizeInSeconds: 60,
    });

    if (rateLimitResult.limited) {
      return new Response(
        JSON.stringify({
          error: 'Too Many Requests',
          message: 'Você excedeu o limite de requisições. Tente novamente mais tarde.',
          retryAfter: rateLimitResult.resetInSeconds,
        }),
        {
          status: 429,
          headers: {
            'Content-Type': 'application/json',
            'Retry-After': String(rateLimitResult.resetInSeconds),
          },
        }
      );
    }

    // Obter txid da query string
    const url = new URL(request.url);
    const txid = url.searchParams.get('txid');

    if (!txid) {
      return new Response(
        JSON.stringify({
          error: 'Missing txid',
          message: 'O parâmetro txid é obrigatório',
        }),
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    // Consultar status da cobrança
    const pixCharge = await efiPayService.getPixCharge(txid);

    // Mapear status
    let status: string;

    switch (pixCharge.status) {
      case 'ATIVA':
        status = 'PENDING';
        break;
      case 'CONCLUIDA':
        status = 'COMPLETED';
        break;
      case 'REMOVIDA_PELO_USUARIO_RECEBEDOR':
        status = 'CANCELLED';
        break;
      case 'REMOVIDA_PELO_PSP':
        status = 'CANCELLED';
        break;
      default:
        status = 'EXPIRED';
    }

    // Verificar se há pagamentos
    if (pixCharge.chave && pixCharge.chave.length > 0) {
      status = 'COMPLETED';
    }

    // Extrair informações do pagador
    const payer = pixCharge.devedor || {};

    // Extrair valor
    const value = pixCharge.valor?.original ? Number.parseFloat(pixCharge.valor.original) * 100 : 0;

    // Extrair data de pagamento
    const paidAt = pixCharge.pix?.[0]?.horario
      ? new Date(pixCharge.pix[0].horario).toISOString()
      : null;

    // Retornar resposta
    return new Response(
      JSON.stringify({
        txid,
        status,
        paidAmount: status === 'COMPLETED' ? value : null,
        paidAt: status === 'COMPLETED' ? paidAt : null,
        payerName: payer.nome,
        payerDocument: payer.cpf || payer.cnpj,
      }),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'no-store, no-cache, must-revalidate, proxy-revalidate',
          Pragma: 'no-cache',
          Expires: '0',
        },
      }
    );
  } catch (error) {
    logger.error('Erro ao verificar status de pagamento PIX:', error);

    return new Response(
      JSON.stringify({
        error: 'Internal Server Error',
        message: 'Ocorreu um erro ao verificar o status do pagamento',
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  }
};
