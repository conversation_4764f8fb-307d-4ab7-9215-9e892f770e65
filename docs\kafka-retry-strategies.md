# Estratégias de Retry para Produtores Kafka

Este documento descreve as estratégias de retry implementadas para os produtores Kafka na plataforma Estação da Alfabetização.

## Visão Geral

As estratégias de retry são essenciais para garantir a confiabilidade na entrega de mensagens em sistemas distribuídos. Quando ocorrem falhas na produção de mensagens para o Kafka, o sistema tenta novamente seguindo políticas configuráveis, aumentando a resiliência da aplicação.

## Estratégias Implementadas

Implementamos cinco estratégias principais de retry:

### 1. Exponential Backoff com Jitter

Esta é nossa estratégia padrão, que aumenta o tempo de espera exponencialmente entre tentativas, com um componente aleatório para evitar tempestades de retry.

**Fórmula**: `delay = initialRetryTime * (multiplier ^ (attempt - 1)) * (1 ± jitter)`

**Características**:
- Evita sobrecarga do sistema em caso de falhas persistentes
- Adiciona aleatoriedade para evitar sincronização de retries
- Aumenta progressivamente o tempo de espera

**Uso recomendado**:
- Para a maioria dos cenários de produção
- Quando há muitos clientes tentando se reconectar simultaneamente

### 2. Intervalo Fixo

Usa o mesmo intervalo de tempo entre todas as tentativas.

**Fórmula**: `delay = initialRetryTime`

**Características**:
- Simples e previsível
- Comportamento consistente

**Uso recomendado**:
- Para operações com tempo de recuperação conhecido
- Quando a previsibilidade é mais importante que a adaptabilidade

### 3. Backoff Linear

Aumenta o tempo de espera linearmente entre tentativas.

**Fórmula**: `delay = initialRetryTime * attempt`

**Características**:
- Crescimento mais gradual que o exponencial
- Balanceamento entre tentativas rápidas iniciais e espera progressiva

**Uso recomendado**:
- Para operações que podem se recuperar gradualmente
- Quando o backoff exponencial é muito agressivo

### 4. Imediato

Tenta novamente imediatamente após uma falha.

**Fórmula**: `delay = 0`

**Características**:
- Sem espera entre tentativas
- Máxima velocidade de retry

**Uso recomendado**:
- Para falhas transitórias muito breves
- Quando a latência é crítica
- Apenas para sistemas com baixo volume de mensagens

### 5. Personalizada

Permite implementar uma estratégia de retry personalizada para casos específicos.

**Características**:
- Flexibilidade máxima
- Pode combinar diferentes abordagens

**Uso recomendado**:
- Para casos de uso específicos não atendidos pelas estratégias padrão

## Configuração por Tipo de Erro

O sistema permite configurar estratégias de retry diferentes para cada tipo de erro:

| Tipo de Erro | Descrição | Estratégia Padrão | Max Retries | Intervalo Inicial |
|--------------|-----------|-------------------|-------------|-------------------|
| CONNECTION | Erro de conexão com o broker | Exponential Backoff | 10 | 1000ms |
| AUTHENTICATION | Erro de autenticação | Exponential Backoff | 5 | 100ms |
| AUTHORIZATION | Erro de autorização | Exponential Backoff | 5 | 100ms |
| SERIALIZATION | Erro de serialização da mensagem | Sem retry | 0 | N/A |
| VALIDATION | Erro de validação da mensagem | Sem retry | 0 | N/A |
| TIMEOUT | Erro de timeout | Exponential Backoff | 5 | 100ms |
| BROKER_NOT_AVAILABLE | Erro de broker não disponível | Exponential Backoff | 10 | 1000ms |
| TOPIC_NOT_EXISTS | Erro de tópico não existente | Exponential Backoff | 2 | 500ms |
| PARTITION_NOT_AVAILABLE | Erro de partição não disponível | Exponential Backoff | 5 | 100ms |
| MESSAGE_TOO_LARGE | Erro de mensagem muito grande | Sem retry | 0 | N/A |
| UNKNOWN | Erro desconhecido | Exponential Backoff | 5 | 100ms |

## Configuração por Tópico

Também é possível configurar estratégias de retry específicas para cada tópico:

| Tópico | Max Retries | Intervalo Inicial | Intervalo Máximo |
|--------|-------------|-------------------|------------------|
| payment.transaction.created | 10 | 200ms | 60000ms |
| payment.transaction.updated | 10 | 200ms | 60000ms |
| order.created | 8 | 200ms | 45000ms |
| Outros tópicos | 5 | 100ms | 30000ms |

## Dead Letter Queue (DLQ)

Quando uma mensagem falha após esgotar todas as tentativas de retry, ela é enviada para uma Dead Letter Queue (DLQ) para análise posterior.

### Configuração da DLQ

- **Tópico**: `dead-letter-queue`
- **Conteúdo**: Mensagem original, informações de erro, metadados de retry
- **Armazenamento**: Além do tópico Kafka, as mensagens são armazenadas em banco de dados para consulta e análise

### Processamento da DLQ

O sistema inclui um serviço dedicado para gerenciar a DLQ, com as seguintes funcionalidades:

1. **Consumo automático**: Consome mensagens da DLQ e as armazena em banco de dados
2. **Interface de consulta**: Permite consultar mensagens da DLQ por tópico, tipo de erro, etc.
3. **Reprocessamento**: Permite reprocessar mensagens da DLQ manualmente
4. **Estatísticas**: Gera estatísticas de falhas por tópico e tipo de erro

## Implementação Técnica

A implementação das estratégias de retry é feita através dos seguintes componentes:

1. **kafka-retry.config.ts**: Define as configurações de retry para produtores Kafka
2. **kafka-producer-retry.service.ts**: Implementa o serviço de retry para produtores
3. **kafka-dlq.service.ts**: Implementa o serviço de Dead Letter Queue
4. **eventProducerService.ts**: Integra o serviço de retry com o produtor de eventos

### Exemplo de Uso

```typescript
// Enviar mensagem com retry
const result = await kafkaProducerRetryService.sendWithRetry(
  topic,
  message,
  producer
);

if (result.success) {
  logger.debug(`Mensagem enviada com sucesso para tópico ${topic} na tentativa ${result.attempts}`);
} else {
  logger.error(`Falha ao enviar mensagem para tópico ${topic} após ${result.attempts} tentativas`);
}
```

## Monitoramento e Observabilidade

O sistema inclui recursos para monitoramento e observabilidade das operações de retry:

1. **Logging detalhado**: Cada tentativa de retry é registrada com informações detalhadas
2. **Métricas**: Número de retries, taxa de sucesso após retry, mensagens enviadas para DLQ
3. **Alertas**: Configuráveis para alta taxa de falhas ou crescimento da DLQ
4. **Dashboard**: Visualização de estatísticas de retry e DLQ

## Considerações de Performance

### Impacto no Sistema

- **Latência**: As estratégias de retry podem aumentar a latência de entrega de mensagens
- **Throughput**: O sistema é projetado para minimizar o impacto no throughput geral
- **Recursos**: O consumo de recursos é monitorado para evitar degradação do sistema

### Recomendações

1. **Ajuste fino**: Configure os parâmetros de retry de acordo com as características de cada tópico
2. **Monitoramento**: Acompanhe as métricas de retry para identificar problemas recorrentes
3. **Análise da DLQ**: Analise regularmente as mensagens na DLQ para identificar problemas sistêmicos

## Configuração via Variáveis de Ambiente

O comportamento de retry pode ser configurado através de variáveis de ambiente:

| Variável | Descrição | Valor Padrão |
|----------|-----------|--------------|
| KAFKA_PRODUCER_RETRY_STRATEGY | Estratégia de retry global | exponential_backoff |
| KAFKA_PRODUCER_MAX_RETRIES | Número máximo de tentativas | 5 |
| KAFKA_PRODUCER_INITIAL_RETRY_TIME_MS | Intervalo inicial entre tentativas (ms) | 100 |
| KAFKA_PRODUCER_MAX_RETRY_TIME_MS | Intervalo máximo entre tentativas (ms) | 30000 |
| KAFKA_PRODUCER_MULTIPLIER | Fator de multiplicação para backoff exponencial | 2 |
| KAFKA_PRODUCER_JITTER | Fator de jitter (0-1) para adicionar aleatoriedade | 0.2 |
| KAFKA_PRODUCER_DLQ_ENABLED | Se a dead letter queue está habilitada | true |
| KAFKA_PRODUCER_DLQ_TOPIC | Tópico para a dead letter queue | dead-letter-queue |
