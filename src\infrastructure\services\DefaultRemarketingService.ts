/**
 * Default Remarketing Service
 *
 * Implementação padrão do serviço de remarketing.
 * Parte da implementação da tarefa 8.9.4 - Marketing digital
 */

import {
  RemarketingAudience,
  RemarketingEvent,
  RemarketingPixelConfig,
  RemarketingService,
} from '../../domain/services/RemarketingService';

export class DefaultRemarketingService implements RemarketingService {
  private pixelConfigs: RemarketingPixelConfig[] = [];
  private audiences: RemarketingAudience[] = [];
  private isInitialized = false;

  /**
   * Inicializa o serviço de remarketing com as configurações fornecidas
   */
  async initialize(configs: RemarketingPixelConfig[]): Promise<boolean> {
    try {
      this.pixelConfigs = configs;
      this.isInitialized = true;
      return true;
    } catch (error) {
      console.error('Erro ao inicializar serviço de remarketing:', error);
      return false;
    }
  }

  /**
   * Adiciona uma configuração de pixel de remarketing
   */
  async addPixelConfig(config: RemarketingPixelConfig): Promise<boolean> {
    try {
      // Verificar se já existe um pixel com o mesmo ID
      const existingIndex = this.pixelConfigs.findIndex((p) => p.id === config.id);

      if (existingIndex >= 0) {
        return false;
      }

      this.pixelConfigs.push(config);
      return true;
    } catch (error) {
      console.error('Erro ao adicionar configuração de pixel:', error);
      return false;
    }
  }

  /**
   * Remove uma configuração de pixel de remarketing
   */
  async removePixelConfig(id: string): Promise<boolean> {
    try {
      const initialLength = this.pixelConfigs.length;
      this.pixelConfigs = this.pixelConfigs.filter((config) => config.id !== id);

      return this.pixelConfigs.length < initialLength;
    } catch (error) {
      console.error('Erro ao remover configuração de pixel:', error);
      return false;
    }
  }

  /**
   * Atualiza uma configuração de pixel de remarketing
   */
  async updatePixelConfig(id: string, updates: Partial<RemarketingPixelConfig>): Promise<boolean> {
    try {
      const index = this.pixelConfigs.findIndex((config) => config.id === id);

      if (index === -1) {
        return false;
      }

      this.pixelConfigs[index] = {
        ...this.pixelConfigs[index],
        ...updates,
      };

      return true;
    } catch (error) {
      console.error('Erro ao atualizar configuração de pixel:', error);
      return false;
    }
  }

  /**
   * Obtém todas as configurações de pixel de remarketing
   */
  async getPixelConfigs(): Promise<RemarketingPixelConfig[]> {
    return this.pixelConfigs;
  }

  /**
   * Obtém uma configuração de pixel de remarketing específica
   */
  async getPixelConfig(id: string): Promise<RemarketingPixelConfig | null> {
    const config = this.pixelConfigs.find((config) => config.id === id);
    return config || null;
  }

  /**
   * Rastreia um evento de remarketing
   */
  async trackEvent(event: RemarketingEvent): Promise<boolean> {
    if (!this.isInitialized) {
      console.error('Serviço de remarketing não inicializado');
      return false;
    }

    try {
      // Filtrar pixels por tipo, se especificado
      const pixelsToTrack = event.pixelTypes
        ? this.pixelConfigs.filter((p) => p.enabled && event.pixelTypes?.includes(p.type))
        : this.pixelConfigs.filter((p) => p.enabled);

      // Rastrear evento em cada pixel
      for (const pixel of pixelsToTrack) {
        await this.trackEventForPixel(pixel, event);
      }

      return true;
    } catch (error) {
      console.error('Erro ao rastrear evento de remarketing:', error);
      return false;
    }
  }

  /**
   * Rastreia uma visualização de página para remarketing
   */
  async trackPageView(path: string, params?: Record<string, any>): Promise<boolean> {
    return this.trackEvent({
      name: 'page_view',
      params: {
        page_path: path,
        page_title: document.title,
        ...params,
      },
    });
  }

  /**
   * Rastreia uma conversão para remarketing
   */
  async trackConversion(
    name: string,
    value?: number,
    currency?: string,
    params?: Record<string, any>
  ): Promise<boolean> {
    return this.trackEvent({
      name: 'conversion',
      params: {
        conversion_name: name,
        value,
        currency,
        ...params,
      },
    });
  }

  /**
   * Rastreia uma adição ao carrinho para remarketing
   */
  async trackAddToCart(
    productId: string,
    quantity?: number,
    price?: number,
    currency?: string,
    params?: Record<string, any>
  ): Promise<boolean> {
    return this.trackEvent({
      name: 'add_to_cart',
      params: {
        product_id: productId,
        quantity,
        price,
        currency,
        ...params,
      },
    });
  }

  /**
   * Rastreia uma compra para remarketing
   */
  async trackPurchase(
    orderId: string,
    value: number,
    currency?: string,
    products?: Array<{ id: string; quantity?: number; price?: number }>,
    params?: Record<string, any>
  ): Promise<boolean> {
    return this.trackEvent({
      name: 'purchase',
      params: {
        order_id: orderId,
        value,
        currency,
        products,
        ...params,
      },
    });
  }

  /**
   * Cria uma nova audiência de remarketing
   */
  async createAudience(audience: RemarketingAudience): Promise<boolean> {
    try {
      // Verificar se já existe uma audiência com o mesmo ID
      const existingIndex = this.audiences.findIndex((a) => a.id === audience.id);

      if (existingIndex >= 0) {
        return false;
      }

      this.audiences.push(audience);
      return true;
    } catch (error) {
      console.error('Erro ao criar audiência de remarketing:', error);
      return false;
    }
  }

  /**
   * Atualiza uma audiência de remarketing existente
   */
  async updateAudience(id: string, updates: Partial<RemarketingAudience>): Promise<boolean> {
    try {
      const index = this.audiences.findIndex((audience) => audience.id === id);

      if (index === -1) {
        return false;
      }

      this.audiences[index] = {
        ...this.audiences[index],
        ...updates,
      };

      return true;
    } catch (error) {
      console.error('Erro ao atualizar audiência de remarketing:', error);
      return false;
    }
  }

  /**
   * Remove uma audiência de remarketing
   */
  async removeAudience(id: string): Promise<boolean> {
    try {
      const initialLength = this.audiences.length;
      this.audiences = this.audiences.filter((audience) => audience.id !== id);

      return this.audiences.length < initialLength;
    } catch (error) {
      console.error('Erro ao remover audiência de remarketing:', error);
      return false;
    }
  }

  /**
   * Obtém todas as audiências de remarketing
   */
  async getAudiences(): Promise<RemarketingAudience[]> {
    return this.audiences;
  }

  /**
   * Obtém uma audiência de remarketing específica
   */
  async getAudience(id: string): Promise<RemarketingAudience | null> {
    const audience = this.audiences.find((audience) => audience.id === id);
    return audience || null;
  }

  /**
   * Gera o código de inicialização dos pixels de remarketing
   */
  generatePixelInitCode(): string {
    let code = '';

    // Gerar código para cada pixel ativo
    for (const pixel of this.pixelConfigs.filter((p) => p.enabled)) {
      switch (pixel.type) {
        case 'facebook':
          code += this.generateFacebookPixelCode(pixel);
          break;

        case 'google':
          code += this.generateGooglePixelCode(pixel);
          break;

        case 'tiktok':
          code += this.generateTikTokPixelCode(pixel);
          break;

        case 'custom':
          if (pixel.customScript) {
            code += pixel.customScript;
          }
          break;
      }
    }

    return code;
  }

  /**
   * Rastreia um evento para um pixel específico
   */
  private async trackEventForPixel(
    pixel: RemarketingPixelConfig,
    event: RemarketingEvent
  ): Promise<void> {
    // Verificar se o evento está na lista de eventos permitidos para o pixel
    if (pixel.events && !pixel.events.includes(event.name)) {
      return;
    }

    switch (pixel.type) {
      case 'facebook':
        if (typeof window !== 'undefined' && window.fbq) {
          window.fbq('track', event.name, event.params);
        }
        break;

      case 'google':
        if (typeof window !== 'undefined' && window.gtag) {
          window.gtag('event', event.name, event.params);
        }
        break;

      case 'tiktok':
        if (typeof window !== 'undefined' && window.ttq) {
          window.ttq.track(event.name, event.params);
        }
        break;

      case 'custom':
        // Implementação personalizada para pixels customizados
        break;
    }
  }

  /**
   * Gera o código de inicialização do pixel do Facebook
   */
  private generateFacebookPixelCode(pixel: RemarketingPixelConfig): string {
    return `
      <!-- Facebook Pixel Code -->
      <script>
        !function(f,b,e,v,n,t,s)
        {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
        n.callMethod.apply(n,arguments):n.queue.push(arguments)};
        if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
        n.queue=[];t=b.createElement(e);t.async=!0;
        t.src=v;s=b.getElementsByTagName(e)[0];
        s.parentNode.insertBefore(t,s)}(window, document,'script',
        'https://connect.facebook.net/en_US/fbevents.js');
        fbq('init', '${pixel.id}');
        fbq('track', 'PageView');
      </script>
      <noscript>
        <img height="1" width="1" style="display:none"
        src="https://www.facebook.com/tr?id=${pixel.id}&ev=PageView&noscript=1"/>
      </noscript>
      <!-- End Facebook Pixel Code -->
    `;
  }

  /**
   * Gera o código de inicialização do pixel do Google
   */
  private generateGooglePixelCode(pixel: RemarketingPixelConfig): string {
    return `
      <!-- Google Tag Manager -->
      <script>
        (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
        new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
        j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
        'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
        })(window,document,'script','dataLayer','${pixel.id}');
      </script>
      <!-- End Google Tag Manager -->
      
      <!-- Google Tag Manager (noscript) -->
      <noscript>
        <iframe src="https://www.googletagmanager.com/ns.html?id=${pixel.id}"
        height="0" width="0" style="display:none;visibility:hidden"></iframe>
      </noscript>
      <!-- End Google Tag Manager (noscript) -->
    `;
  }

  /**
   * Gera o código de inicialização do pixel do TikTok
   */
  private generateTikTokPixelCode(pixel: RemarketingPixelConfig): string {
    return `
      <!-- TikTok Pixel Code -->
      <script>
        !function (w, d, t) {
          w.TiktokAnalyticsObject=t;var ttq=w[t]=w[t]||[];ttq.methods=["page","track","identify","instances","debug","on","off","once","ready","alias","group","enableCookie","disableCookie"],ttq.setAndDefer=function(t,e){t[e]=function(){t.push([e].concat(Array.prototype.slice.call(arguments,0)))}};for(var i=0;i<ttq.methods.length;i++)ttq.setAndDefer(ttq,ttq.methods[i]);ttq.instance=function(t){for(var e=ttq._i[t]||[],n=0;n<ttq.methods.length;n++)ttq.setAndDefer(e,ttq.methods[n]);return e},ttq.load=function(e,n){var i="https://analytics.tiktok.com/i18n/pixel/events.js";ttq._i=ttq._i||{},ttq._i[e]=[],ttq._i[e]._u=i,ttq._t=ttq._t||{},ttq._t[e]=+new Date,ttq._o=ttq._o||{},ttq._o[e]=n||{};var o=document.createElement("script");o.type="text/javascript",o.async=!0,o.src=i+"?sdkid="+e+"&lib="+t;var a=document.getElementsByTagName("script")[0];a.parentNode.insertBefore(o,a)};
          ttq.load('${pixel.id}');
          ttq.page();
        }(window, document, 'ttq');
      </script>
      <!-- End TikTok Pixel Code -->
    `;
  }
}
