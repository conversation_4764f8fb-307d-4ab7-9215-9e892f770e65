/**
 * Serviço para gerenciamento de tokens JWT
 * Implementa funções para geração, validação e renovação de tokens
 *
 * Este serviço segue as melhores práticas de segurança para tokens JWT:
 * - Uso de JTI (JWT ID) para identificação única de tokens
 * - Difer<PERSON>s chaves secretas para diferentes tipos de tokens
 * - Suporte para revogação de tokens (blacklisting)
 * - Rotação automática de tokens
 * - Validação de audiência e emissor
 * - Expiração configurável por tipo de token
 */

import { randomBytes } from 'node:crypto';
import jwt from 'jsonwebtoken';

// Tipos para payload do token
export interface JwtPayload {
  sub: string; // ID do usuário (subject)
  name?: string; // Nome do usuário
  email?: string; // Email do usuário
  role?: string; // Papel do usuário (tipo de usuário)
  permissions?: string[]; // Permissões do usuário
  type: 'access' | 'refresh' | 'reset' | 'verification' | 'api'; // Tipo de token
  jti?: string; // ID único do token (para blacklist)
  iat?: number; // Timestamp de emissão (issued at)
  exp?: number; // Timestamp de expiração (expiration)
  nbf?: number; // Timestamp a partir do qual o token é válido (not before)
  iss?: string; // Emissor do token (issuer)
  aud?: string; // Audiência do token (audience)
  device?: string; // ID do dispositivo
  ip?: string; // Endereço IP
  fingerprint?: string; // Fingerprint do navegador/dispositivo
}

// Configurações padrão
const DEFAULT_ACCESS_TOKEN_EXPIRY = '15m'; // 15 minutos
const DEFAULT_REFRESH_TOKEN_EXPIRY = '7d'; // 7 dias
const DEFAULT_RESET_TOKEN_EXPIRY = '1h'; // 1 hora
const DEFAULT_VERIFICATION_TOKEN_EXPIRY = '24h'; // 24 horas
const DEFAULT_API_TOKEN_EXPIRY = '30d'; // 30 dias

// Configurações de segurança
const TOKEN_ISSUER = process.env.JWT_ISSUER || 'estacao-alfabetizacao';
const TOKEN_AUDIENCE = process.env.JWT_AUDIENCE || 'estacao-alfabetizacao-app';

// Serviço JWT
export const jwtService = {
  /**
   * Gera um token JWT
   * @param payload - Dados a serem incluídos no token
   * @param expiresIn - Tempo de expiração (ex: '15m', '1h', '7d')
   * @param options - Opções adicionais para geração do token
   * @returns Token JWT assinado
   */
  generateToken(
    payload: Omit<JwtPayload, 'jti' | 'iat' | 'exp' | 'nbf' | 'iss' | 'aud'>,
    expiresInParam?: string,
    options: {
      notBefore?: string | number;
      audience?: string;
      issuer?: string;
      subject?: string;
      algorithm?: jwt.Algorithm;
    } = {}
  ): string {
    // Determinar tempo de expiração com base no tipo de token
    let tokenExpiry = expiresInParam;
    if (!tokenExpiry) {
      switch (payload.type) {
        case 'access':
          tokenExpiry = DEFAULT_ACCESS_TOKEN_EXPIRY;
          break;
        case 'refresh':
          tokenExpiry = DEFAULT_REFRESH_TOKEN_EXPIRY;
          break;
        case 'reset':
          tokenExpiry = DEFAULT_RESET_TOKEN_EXPIRY;
          break;
        case 'verification':
          tokenExpiry = DEFAULT_VERIFICATION_TOKEN_EXPIRY;
          break;
        case 'api':
          tokenExpiry = DEFAULT_API_TOKEN_EXPIRY;
          break;
        default:
          tokenExpiry = DEFAULT_ACCESS_TOKEN_EXPIRY;
      }
    }

    // Gerar ID único para o token (útil para blacklist)
    const jti = randomBytes(16).toString('hex');

    // Obter chave secreta do ambiente
    const secret = this.getSecretKey(payload.type);

    // Configurar opções de assinatura
    const signOptions: jwt.SignOptions = {
      expiresIn: tokenExpiry,
      jwtid: jti,
      issuer: options.issuer || TOKEN_ISSUER,
      audience: options.audience || TOKEN_AUDIENCE,
      subject: options.subject || payload.sub,
      algorithm: options.algorithm || 'HS256',
    };

    // Adicionar notBefore se fornecido
    if (options.notBefore) {
      signOptions.notBefore = options.notBefore;
    }

    // Gerar token
    return jwt.sign({ ...payload, jti }, secret, signOptions);
  },

  /**
   * Verifica e decodifica um token JWT
   * @param token - Token JWT a ser verificado
   * @param type - Tipo de token ('access', 'refresh', 'reset', etc.)
   * @param options - Opções adicionais para verificação do token
   * @returns Payload decodificado ou null se inválido
   */
  verifyToken(
    token: string,
    type: JwtPayload['type'],
    options: {
      audience?: string | string[];
      issuer?: string | string[];
      complete?: boolean;
      ignoreExpiration?: boolean;
      ignoreNotBefore?: boolean;
      subject?: string;
      clockTolerance?: number;
      maxAge?: string | number;
    } = {}
  ): JwtPayload | null {
    try {
      const secret = this.getSecretKey(type);

      // Configurar opções de verificação
      const verifyOptions: jwt.VerifyOptions = {
        audience: options.audience || TOKEN_AUDIENCE,
        issuer: options.issuer || TOKEN_ISSUER,
        complete: options.complete || false,
        ignoreExpiration: options.ignoreExpiration || false,
        ignoreNotBefore: options.ignoreNotBefore || false,
        subject: options.subject,
        clockTolerance: options.clockTolerance || 0,
        maxAge: options.maxAge,
      };

      // Verificar token
      const decoded = jwt.verify(token, secret, verifyOptions) as JwtPayload;

      // Verificar se o tipo do token corresponde ao esperado
      if (decoded.type !== type) {
        console.warn(`Tipo de token inválido: esperado ${type}, recebido ${decoded.type}`);
        return null;
      }

      return decoded;
    } catch (error) {
      if (error instanceof jwt.TokenExpiredError) {
        console.warn('Token JWT expirado:', error.message);
      } else if (error instanceof jwt.NotBeforeError) {
        console.warn('Token JWT ainda não válido:', error.message);
      } else if (error instanceof jwt.JsonWebTokenError) {
        console.warn('Token JWT inválido:', error.message);
      } else {
        console.error('Erro ao verificar token JWT:', error);
      }
      return null;
    }
  },

  /**
   * Gera um novo access token a partir de um refresh token
   * @param refreshToken - Refresh token válido
   * @returns Novo access token ou null se refresh token inválido
   */
  refreshAccessToken(refreshToken: string): string | null {
    // Verificar refresh token
    const payload = this.verifyToken(refreshToken, 'refresh');

    if (!payload) {
      return null;
    }

    // Gerar novo access token
    return this.generateToken({
      sub: payload.sub,
      name: payload.name,
      email: payload.email,
      role: payload.role,
      type: 'access',
    });
  },

  /**
   * Obtém a chave secreta apropriada com base no tipo de token
   * @param type - Tipo de token ('access', 'refresh', 'reset', etc.)
   * @returns Chave secreta
   */
  getSecretKey(type: JwtPayload['type']): string {
    // Usar chaves diferentes para diferentes tipos de tokens
    // aumenta a segurança, pois comprometer uma chave não compromete todos os tipos
    let secret: string | undefined;

    switch (type) {
      case 'access':
        secret = process.env.JWT_ACCESS_SECRET;
        break;
      case 'refresh':
        secret = process.env.JWT_REFRESH_SECRET;
        break;
      case 'reset':
        secret = process.env.JWT_RESET_SECRET;
        break;
      case 'verification':
        secret = process.env.JWT_VERIFICATION_SECRET;
        break;
      case 'api':
        secret = process.env.JWT_API_SECRET;
        break;
      default:
        secret = process.env.JWT_SECRET;
    }

    // Fallback para chave secreta geral
    if (!secret) {
      secret = process.env.JWT_SECRET;
    }

    if (!secret) {
      throw new Error(`Chave secreta para token ${type} não configurada`);
    }

    return secret;
  },

  /**
   * Verifica se um token está revogado
   * @param token - Token JWT ou JTI (ID do token)
   * @returns Verdadeiro se o token estiver revogado
   */
  async isTokenRevoked(tokenOrJti: string): Promise<boolean> {
    try {
      // Extrair JTI do token se for um token completo
      let jti = tokenOrJti;

      if (tokenOrJti.includes('.')) {
        try {
          // Decodificar token sem verificar assinatura
          const decoded = jwt.decode(tokenOrJti) as JwtPayload;
          if (decoded?.jti) {
            jti = decoded.jti;
          }
        } catch (decodeError) {
          console.warn('Erro ao decodificar token para verificação de revogação:', decodeError);
        }
      }

      // Verificar no banco de dados se o token está revogado
      // Isso é mais confiável que apenas o cache, mas mais lento
      try {
        const { tokenRepository } = await import('@repository/tokenRepository');
        const result = await tokenRepository.findTokenByJti(jti);

        if (result.rowCount > 0 && result.rows[0].is_revoked) {
          return true;
        }
      } catch (dbError) {
        console.warn('Erro ao verificar revogação no banco de dados:', dbError);
      }

      // Verificar no cache (Valkey/Redis) para resposta mais rápida
      try {
        const { cacheService } = await import('@services/cacheService');
        const cacheKey = `revoked_token:${jti}`;
        const isRevoked = await cacheService.get(cacheKey);

        return isRevoked === 'true';
      } catch (cacheError) {
        console.warn('Erro ao verificar revogação no cache:', cacheError);
      }

      return false;
    } catch (error) {
      console.error('Erro ao verificar revogação de token:', error);
      return false;
    }
  },

  /**
   * Revoga um token adicionando-o à blacklist
   * @param token - Token JWT ou JTI (ID do token)
   * @param expiresIn - Tempo até a expiração do token (opcional)
   * @returns Verdadeiro se o token foi revogado com sucesso
   */
  async revokeToken(tokenOrJti: string, expiresIn?: number): Promise<boolean> {
    try {
      // Extrair JTI e tempo de expiração do token se for um token completo
      let jti = tokenOrJti;
      let expiry = expiresIn;

      if (tokenOrJti.includes('.')) {
        try {
          // Decodificar token sem verificar assinatura
          const decoded = jwt.decode(tokenOrJti) as JwtPayload;
          if (decoded?.jti) {
            jti = decoded.jti;
          }

          // Calcular tempo até expiração se não fornecido
          if (!expiry && decoded?.exp) {
            const now = Math.floor(Date.now() / 1000);
            expiry = decoded.exp - now;

            // Se o token já expirou, não é necessário adicioná-lo à blacklist
            if (expiry <= 0) {
              return true;
            }
          }
        } catch (decodeError) {
          console.warn('Erro ao decodificar token para revogação:', decodeError);
        }
      }

      // Adicionar ao cache (Valkey/Redis)
      try {
        const { cacheService } = await import('@services/cacheService');
        const cacheKey = `revoked_token:${jti}`;

        // Armazenar no cache com TTL igual ao tempo restante de validade do token
        // Isso garante que tokens expirados sejam automaticamente removidos da blacklist
        if (expiry && expiry > 0) {
          await cacheService.set(cacheKey, 'true', expiry);
        } else {
          // Se não soubermos quando o token expira, usar um TTL padrão de 24 horas
          await cacheService.set(cacheKey, 'true', 60 * 60 * 24);
        }
      } catch (cacheError) {
        console.warn('Erro ao adicionar token à blacklist no cache:', cacheError);
      }

      return true;
    } catch (error) {
      console.error('Erro ao revogar token:', error);
      return false;
    }
  },
};
