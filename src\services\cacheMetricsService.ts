/**
 * Serviço de métricas de cache
 *
 * Este serviço coleta e analisa métricas de uso do cache
 * para monitoramento e otimização.
 */

import { cacheService } from '@services/cacheService';
import { CacheableDataType } from '@services/dataCacheService';
import { logger } from '@utils/logger';

/**
 * Interface para métricas de cache
 */
export interface CacheMetrics {
  /**
   * Timestamp da coleta
   */
  timestamp: number;

  /**
   * Total de requisições ao cache
   */
  totalRequests: number;

  /**
   * Total de hits (acertos) no cache
   */
  hits: number;

  /**
   * Total de misses (erros) no cache
   */
  misses: number;

  /**
   * Taxa de acertos (hits / totalRequests)
   */
  hitRate: number;

  /**
   * Métricas por tipo de dado
   */
  byType: Record<CacheableDataType, TypeMetrics>;

  /**
   * Estatísticas do servidor de cache
   */
  serverStats: Record<string, string>;
}

/**
 * Interface para métricas por tipo de dado
 */
export interface TypeMetrics {
  /**
   * Total de requisições para este tipo
   */
  requests: number;

  /**
   * Total de hits para este tipo
   */
  hits: number;

  /**
   * Total de misses para este tipo
   */
  misses: number;

  /**
   * Taxa de acertos para este tipo
   */
  hitRate: number;
}

/**
 * Serviço de métricas de cache
 */
export const cacheMetricsService = {
  /**
   * Contadores de métricas
   */
  counters: {
    totalRequests: 0,
    hits: 0,
    misses: 0,
    byType: {} as Record<CacheableDataType, { requests: number; hits: number; misses: number }>,
  },

  /**
   * Histórico de métricas
   */
  history: [] as CacheMetrics[],

  /**
   * Tamanho máximo do histórico
   */
  MAX_HISTORY_SIZE: 100,

  /**
   * Inicializa o serviço de métricas
   * @param collectInterval - Intervalo em minutos para coleta automática
   */
  initialize(collectInterval = 15): void {
    // Inicializar contadores por tipo
    Object.values(CacheableDataType).forEach((type) => {
      this.counters.byType[type] = {
        requests: 0,
        hits: 0,
        misses: 0,
      };
    });

    // Agendar coleta periódica
    if (collectInterval > 0) {
      setInterval(
        () => {
          this.collectMetrics();
        },
        collectInterval * 60 * 1000
      );
    }

    logger.info('Serviço de métricas de cache inicializado', {
      collectInterval,
    });
  },

  /**
   * Registra uma requisição ao cache
   * @param type - Tipo de dado
   * @param isHit - Se foi um acerto no cache
   */
  recordRequest(type: CacheableDataType, isHit: boolean): void {
    // Incrementar contadores globais
    this.counters.totalRequests++;

    if (isHit) {
      this.counters.hits++;
    } else {
      this.counters.misses++;
    }

    // Incrementar contadores por tipo
    if (!this.counters.byType[type]) {
      this.counters.byType[type] = {
        requests: 0,
        hits: 0,
        misses: 0,
      };
    }

    this.counters.byType[type].requests++;

    if (isHit) {
      this.counters.byType[type].hits++;
    } else {
      this.counters.byType[type].misses++;
    }
  },

  /**
   * Coleta métricas atuais
   * @returns Métricas coletadas
   */
  async collectMetrics(): Promise<CacheMetrics> {
    try {
      // Obter estatísticas do servidor
      const serverStats = (await cacheService.getStats()) || {};

      // Calcular métricas por tipo
      const byType = {} as Record<CacheableDataType, TypeMetrics>;

      Object.entries(this.counters.byType).forEach(([type, counters]) => {
        const hitRate = counters.requests > 0 ? counters.hits / counters.requests : 0;

        byType[type as CacheableDataType] = {
          requests: counters.requests,
          hits: counters.hits,
          misses: counters.misses,
          hitRate,
        };
      });

      // Calcular taxa de acertos global
      const hitRate =
        this.counters.totalRequests > 0 ? this.counters.hits / this.counters.totalRequests : 0;

      // Criar objeto de métricas
      const metrics: CacheMetrics = {
        timestamp: Date.now(),
        totalRequests: this.counters.totalRequests,
        hits: this.counters.hits,
        misses: this.counters.misses,
        hitRate,
        byType,
        serverStats,
      };

      // Adicionar ao histórico
      this.history.push(metrics);

      // Limitar tamanho do histórico
      if (this.history.length > this.MAX_HISTORY_SIZE) {
        this.history.shift();
      }

      // Registrar métricas no log
      logger.info('Métricas de cache coletadas', {
        totalRequests: metrics.totalRequests,
        hitRate: metrics.hitRate.toFixed(2),
        memoryUsed: serverStats.used_memory_human || 'N/A',
      });

      return metrics;
    } catch (error) {
      logger.error('Erro ao coletar métricas de cache:', error);

      // Retornar métricas parciais em caso de erro
      return {
        timestamp: Date.now(),
        totalRequests: this.counters.totalRequests,
        hits: this.counters.hits,
        misses: this.counters.misses,
        hitRate:
          this.counters.totalRequests > 0 ? this.counters.hits / this.counters.totalRequests : 0,
        byType: {} as Record<CacheableDataType, TypeMetrics>,
        serverStats: {},
      };
    }
  },

  /**
   * Obtém métricas atuais
   * @returns Métricas atuais
   */
  async getCurrentMetrics(): Promise<CacheMetrics> {
    return this.collectMetrics();
  },

  /**
   * Obtém histórico de métricas
   * @param limit - Número máximo de registros a retornar
   * @returns Histórico de métricas
   */
  getMetricsHistory(limit = 10): CacheMetrics[] {
    // Limitar ao tamanho do histórico
    const actualLimit = Math.min(limit, this.history.length);

    // Retornar os registros mais recentes
    return this.history.slice(-actualLimit);
  },

  /**
   * Reseta os contadores de métricas
   */
  resetCounters(): void {
    this.counters.totalRequests = 0;
    this.counters.hits = 0;
    this.counters.misses = 0;

    Object.keys(this.counters.byType).forEach((type) => {
      this.counters.byType[type as CacheableDataType] = {
        requests: 0,
        hits: 0,
        misses: 0,
      };
    });

    logger.info('Contadores de métricas de cache resetados');
  },
};
