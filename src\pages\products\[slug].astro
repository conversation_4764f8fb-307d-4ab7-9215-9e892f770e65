---
import ProductGrid from '../../components/product/ProductGrid.astro';
import { PageTransition } from '../../components/transitions';
import DaisyButton from '../../components/ui/DaisyButton.astro';
import DaisyTabs from '../../components/ui/DaisyTabs.astro';
import Breadcrumbs from '../../components/ui/navigation/Breadcrumbs.astro';
/**
 * Página de Detalhes do Produto
 *
 * Esta página exibe os detalhes de um produto específico.
 * Parte da implementação da tarefa 8.3.3 - Catálogo de produtos
 */
import BaseLayout from '../../layouts/BaseLayout.astro';
import { Container, Section } from '../../layouts/grid';

// Obter slug do produto da URL
const { slug } = Astro.params;

// Em um cenário real, buscaríamos o produto do repositório
// Por enquanto, usaremos dados de exemplo
const products = [
  {
    id: 'prod-001',
    name: '<PERSON>tilha de Alfabetização',
    slug: 'cartilha-de-alfabetizacao',
    description: `
      <p>A <strong>Cartilha de Alfabetização</strong> é um material didático completo desenvolvido para auxiliar no processo de alfabetização de crianças. Com uma abordagem lúdica e pedagógica, este material facilita o aprendizado das letras, sílabas e formação de palavras.</p>
      
      <p>Desenvolvida por especialistas em educação infantil, a cartilha apresenta uma progressão cuidadosamente planejada, começando com vogais, seguindo para consoantes simples e avançando gradualmente para combinações mais complexas.</p>
      
      <h4>Características principais:</h4>
      <ul>
        <li>Ilustrações coloridas e atrativas</li>
        <li>Exercícios práticos para fixação do conteúdo</li>
        <li>Atividades de leitura e escrita</li>
        <li>Jogos educativos integrados</li>
        <li>Guia para professores e pais</li>
      </ul>
      
      <p>Ideal para uso em sala de aula ou como material complementar para estudos em casa.</p>
    `,
    shortDescription: 'Material didático para alfabetização de crianças',
    price: 29.9,
    originalPrice: 39.9,
    discountPercentage: 25,
    imageUrl: '/images/products/cartilha.jpg',
    images: [
      { url: '/images/products/cartilha.jpg', alt: 'Capa da cartilha' },
      { url: '/images/products/cartilha-interna1.jpg', alt: 'Páginas internas' },
      { url: '/images/products/cartilha-interna2.jpg', alt: 'Exercícios' },
    ],
    isFeatured: true,
    isDigital: true,
    categories: [{ id: 'cat-001', name: 'Alfabetização', slug: 'alfabetizacao' }],
    tags: ['alfabetização', 'educação', 'leitura', 'infantil'],
    rating: 4.5,
    reviewCount: 28,
    inStock: true,
    specifications: [
      { name: 'Páginas', value: '64' },
      { name: 'Formato', value: 'A4' },
      { name: 'Idade recomendada', value: '5 a 7 anos' },
      { name: 'Tipo', value: 'PDF Digital' },
      { name: 'Idioma', value: 'Português' },
    ],
    reviews: [
      {
        id: 'rev-001',
        author: 'Maria S.',
        rating: 5,
        comment: 'Excelente material! Meus alunos adoraram as atividades.',
        date: '2023-05-15',
      },
      {
        id: 'rev-002',
        author: 'João P.',
        rating: 4,
        comment: 'Muito bom, mas poderia ter mais exercícios de escrita.',
        date: '2023-04-22',
      },
      {
        id: 'rev-003',
        author: 'Ana C.',
        rating: 5,
        comment: 'Material completo e bem estruturado. Recomendo!',
        date: '2023-03-10',
      },
    ],
  },
  {
    id: 'prod-002',
    name: 'Kit de Atividades Matemáticas',
    slug: 'kit-de-atividades-matematicas',
    shortDescription: 'Conjunto de atividades para ensino de matemática básica',
    price: 45.0,
    imageUrl: '/images/products/matematica.jpg',
    isNew: true,
    categories: [{ id: 'cat-002', name: 'Matemática', slug: 'matematica' }],
    rating: 4.0,
    reviewCount: 15,
    inStock: true,
  },
  {
    id: 'prod-003',
    name: 'Livro de Histórias Infantis',
    slug: 'livro-de-historias-infantis',
    shortDescription: 'Coletânea de histórias para estimular a leitura',
    price: 35.5,
    originalPrice: 42.0,
    discountPercentage: 15,
    imageUrl: '/images/products/historias.jpg',
    categories: [{ id: 'cat-003', name: 'Leitura', slug: 'leitura' }],
    rating: 4.8,
    reviewCount: 42,
    inStock: true,
  },
  {
    id: 'prod-004',
    name: 'Jogo Educativo de Palavras',
    slug: 'jogo-educativo-de-palavras',
    shortDescription: 'Jogo para aprender novas palavras e melhorar o vocabulário',
    price: 59.9,
    imageUrl: '/images/products/jogo.jpg',
    categories: [
      { id: 'cat-001', name: 'Alfabetização', slug: 'alfabetizacao' },
      { id: 'cat-004', name: 'Jogos', slug: 'jogos' },
    ],
    rating: 4.2,
    reviewCount: 18,
    inStock: false,
  },
];

// Buscar o produto pelo slug
const product = products.find((p) => p.slug === slug);

// Se o produto não for encontrado, redirecionar para a página de produtos
if (!product) {
  return Astro.redirect('/products');
}

// Formatar preço
const formatter = new Intl.NumberFormat('pt-BR', {
  style: 'currency',
  currency: 'BRL',
});

const formattedPrice = formatter.format(product.price);
const formattedOriginalPrice = product.originalPrice
  ? formatter.format(product.originalPrice)
  : null;

// Calcular desconto
const hasDiscount = product.originalPrice && product.originalPrice > product.price;
const discount = hasDiscount
  ? product.discountPercentage ||
    Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100)
  : null;

// Buscar produtos relacionados (mesma categoria)
const relatedProducts = products
  .filter(
    (p) =>
      p.id !== product.id &&
      p.categories.some((c) => product.categories.some((pc) => pc.id === c.id))
  )
  .slice(0, 4);

// Título da página
const title = product.name;

// Breadcrumbs
const breadcrumbItems = [
  { href: '/', label: 'Início' },
  { href: '/products', label: 'Produtos' },
  ...product.categories
    .slice(0, 1)
    .map((cat) => ({ href: `/products?categoryId=${cat.id}`, label: cat.name })),
  { label: product.name },
];

// Abas de informações
const tabs = [
  {
    id: 'description',
    label: 'Descrição',
    content: product.description || 'Sem descrição disponível.',
  },
  {
    id: 'specifications',
    label: 'Especificações',
    content: product.specifications
      ? `<div class="overflow-x-auto">
      <table class="table table-zebra w-full">
        <tbody>
          ${product.specifications
            .map(
              (spec) => `
            <tr>
              <td class="font-medium">${spec.name}</td>
              <td>${spec.value}</td>
            </tr>
          `
            )
            .join('')}
        </tbody>
      </table>
    </div>`
      : 'Sem especificações disponíveis.',
  },
  {
    id: 'reviews',
    label: 'Avaliações',
    content: product.reviews
      ? `<div class="space-y-4">
      ${product.reviews
        .map(
          (review) => `
        <div class="bg-base-200 p-4 rounded-lg">
          <div class="flex justify-between items-center mb-2">
            <div>
              <span class="font-bold">${review.author}</span>
              <div class="rating rating-sm">
                ${[1, 2, 3, 4, 5]
                  .map(
                    (star) => `
                  <input type="radio" class="mask mask-star-2 ${star <= review.rating ? 'bg-warning' : 'bg-gray-300'}" disabled ${star === review.rating ? 'checked' : ''} />
                `
                  )
                  .join('')}
              </div>
            </div>
            <span class="text-sm text-gray-500">${new Date(review.date).toLocaleDateString()}</span>
          </div>
          <p>${review.comment}</p>
        </div>
      `
        )
        .join('')}
    </div>`
      : 'Sem avaliações disponíveis.',
  },
];
---

<BaseLayout title={title} enableViewTransitions={true} transitionType="fade">
  <Container>
    <PageTransition type="fade" duration="0.5s">
      <Section>
        <Breadcrumbs items={breadcrumbItems} class="mb-6" />
        
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
          <!-- Galeria de imagens -->
          <div>
            <div class="bg-base-200 rounded-lg overflow-hidden mb-4">
              <img 
                src={product.images?.[0]?.url || product.imageUrl} 
                alt={product.images?.[0]?.alt || product.name} 
                class="w-full h-auto object-contain aspect-square"
                id="main-product-image"
              />
            </div>
            
            {product.images && product.images.length > 1 && (
              <div class="grid grid-cols-4 gap-2">
                {product.images.map((image, index) => (
                  <button 
                    class="bg-base-200 rounded-lg overflow-hidden border-2 hover:border-primary transition-colors thumbnail-btn"
                    data-image-url={image.url}
                    data-image-alt={image.alt}
                    aria-label={`Ver imagem ${index + 1}`}
                  >
                    <img 
                      src={image.url} 
                      alt={image.alt} 
                      class="w-full h-auto object-cover aspect-square"
                    />
                  </button>
                ))}
              </div>
            )}
          </div>
          
          <!-- Informações do produto -->
          <div>
            <h1 class="text-3xl font-bold mb-2">{product.name}</h1>
            
            <div class="flex items-center mb-4">
              <div class="rating rating-sm mr-2">
                {[1, 2, 3, 4, 5].map(star => (
                  <input 
                    type="radio" 
                    class={`mask mask-star-2 ${star <= product.rating ? 'bg-warning' : 'bg-gray-300'}`} 
                    disabled 
                    checked={star === Math.round(product.rating)}
                  />
                ))}
              </div>
              
              <span class="text-sm text-gray-500">
                {product.rating} ({product.reviewCount} avaliações)
              </span>
            </div>
            
            <div class="mb-6">
              <p class="text-sm text-gray-600">{product.shortDescription}</p>
            </div>
            
            <div class="flex items-center mb-6">
              {hasDiscount && formattedOriginalPrice && (
                <span class="text-lg text-gray-500 line-through mr-2">{formattedOriginalPrice}</span>
              )}
              
              <span class="text-3xl font-bold text-primary">{formattedPrice}</span>
              
              {hasDiscount && discount && (
                <span class="ml-2 badge badge-lg badge-accent">-{discount}%</span>
              )}
            </div>
            
            <div class="mb-6">
              {product.inStock ? (
                <div class="text-success flex items-center">
                  <i class="icon icon-check-circle mr-1"></i>
                  <span>{product.isDigital ? 'Disponível para download' : 'Em estoque'}</span>
                </div>
              ) : (
                <div class="text-error flex items-center">
                  <i class="icon icon-x-circle mr-1"></i>
                  <span>Fora de estoque</span>
                </div>
              )}
            </div>
            
            <div class="flex flex-wrap gap-2 mb-6">
              {product.tags && product.tags.map(tag => (
                <a href={`/products?tag=${tag}`} class="badge badge-outline hover:badge-primary transition-colors">
                  {tag}
                </a>
              ))}
            </div>
            
            <div class="flex flex-col sm:flex-row gap-4 mb-8">
              {product.inStock && (
                <button class="btn btn-primary flex-1">
                  <i class="icon icon-shopping-cart mr-2"></i>
                  {product.isDigital ? 'Comprar Download' : 'Adicionar ao Carrinho'}
                </button>
              )}
              
              <button class="btn btn-outline btn-primary flex-1 sm:flex-none">
                <i class="icon icon-heart mr-2"></i>
                Favoritar
              </button>
            </div>
            
            <div class="divider"></div>
            
            <div class="text-sm">
              <div class="mb-2">
                <span class="font-medium">Categorias:</span>
                {product.categories.map((cat, index) => (
                  <>
                    <a href={`/products?categoryId=${cat.id}`} class="hover:text-primary">
                      {cat.name}
                    </a>
                    {index < product.categories.length - 1 && ", "}
                  </>
                ))}
              </div>
              
              {product.isDigital && (
                <div class="mb-2">
                  <span class="font-medium">Tipo:</span> Produto Digital
                </div>
              )}
              
              <div>
                <span class="font-medium">ID do Produto:</span> {product.id}
              </div>
            </div>
          </div>
        </div>
        
        <!-- Abas de informações -->
        <div class="mb-12">
          <DaisyTabs tabs={tabs} />
        </div>
        
        <!-- Produtos relacionados -->
        {relatedProducts.length > 0 && (
          <div>
            <h2 class="text-2xl font-bold mb-6">Produtos Relacionados</h2>
            <ProductGrid
              products={relatedProducts}
              columns={{ sm: 1, md: 2, lg: 4 }}
              gap="md"
              cardSize="sm"
            />
          </div>
        )}
      </Section>
    </PageTransition>
  </Container>
</BaseLayout>

<script>
  // Script para galeria de imagens
  document.addEventListener('DOMContentLoaded', () => {
    const mainImage = document.getElementById('main-product-image') as HTMLImageElement;
    const thumbnails = document.querySelectorAll('.thumbnail-btn');
    
    if (mainImage && thumbnails.length > 0) {
      thumbnails.forEach(thumbnail => {
        thumbnail.addEventListener('click', () => {
          const imageUrl = thumbnail.getAttribute('data-image-url');
          const imageAlt = thumbnail.getAttribute('data-image-alt');
          
          if (imageUrl) {
            mainImage.src = imageUrl;
          }
          
          if (imageAlt) {
            mainImage.alt = imageAlt;
          }
          
          // Adicionar classe ativa ao thumbnail selecionado
          thumbnails.forEach(t => t.classList.remove('border-primary'));
          thumbnail.classList.add('border-primary');
        });
      });
      
      // Definir o primeiro thumbnail como ativo
      thumbnails[0].classList.add('border-primary');
    }
  });
</script>
