/**
 * Serviço de monitoramento do Valkey
 *
 * Este serviço monitora o estado do cluster Valkey e a persistência de dados,
 * coletando métricas e enviando alertas quando necessário.
 */

import { createClient, createCluster } from '@valkey/client';
import type { RedisClientType, RedisClusterType } from '@valkey/client';
import { getClusterConfig } from '@config/cache/cluster.config';
import { getPersistenceConfig } from '@config/cache/persistence.config';
import { cacheConfig } from '@config/cache';
import { logger } from '@utils/logger';
import { EventEmitter } from 'events';
import * as fs from 'fs';
import * as path from 'path';
import { promisify } from 'util';
import axios from 'axios';

// Promisificar funções
const statAsync = promisify(fs.stat);
const writeFileAsync = promisify(fs.writeFile);
const mkdirAsync = promisify(fs.mkdir);

/**
 * Interface para métricas do Valkey
 */
export interface ValkeyMetrics {
  /**
   * Timestamp da coleta
   */
  timestamp: number;

  /**
   * Uso de memória
   */
  memory: {
    /**
     * Uso total de memória (bytes)
     */
    used: number;

    /**
     * Pico de uso de memória (bytes)
     */
    peak: number;

    /**
     * Limite de memória (bytes)
     */
    limit: number;

    /**
     * Fragmentação de memória
     */
    fragmentation: number;
  };

  /**
   * Estatísticas de clientes
   */
  clients: {
    /**
     * Número de conexões
     */
    connected: number;

    /**
     * Conexões bloqueadas
     */
    blocked: number;

    /**
     * Maior tempo de conexão (segundos)
     */
    maxElapsed: number;
  };

  /**
   * Estatísticas de CPU
   */
  cpu: {
    /**
     * Uso de CPU pelo sistema (%)
     */
    system: number;

    /**
     * Uso de CPU pelo usuário (%)
     */
    user: number;

    /**
     * Uso de CPU por filhos (%)
     */
    children: number;
  };

  /**
   * Estatísticas de cluster
   */
  cluster?: {
    /**
     * Estado do cluster
     */
    state: string;

    /**
     * Número de nós
     */
    nodes: number;

    /**
     * Slots atribuídos
     */
    slotsAssigned: number;

    /**
     * Slots não atribuídos
     */
    slotsUnassigned: number;
  };

  /**
   * Estatísticas de persistência
   */
  persistence: {
    /**
     * Último salvamento RDB bem-sucedido (timestamp)
     */
    lastSave: number;

    /**
     * Mudanças desde último salvamento
     */
    changesSinceSave: number;

    /**
     * Status do BGSAVE
     */
    bgsaveInProgress: boolean;

    /**
     * Status do AOF
     */
    aofEnabled: boolean;

    /**
     * Status da reescrita de AOF
     */
    aofRewriteInProgress: boolean;
  };

  /**
   * Estatísticas de comandos
   */
  commands: {
    /**
     * Total de comandos processados
     */
    total: number;

    /**
     * Comandos por segundo
     */
    perSecond: number;

    /**
     * Latência média (ms)
     */
    latency: number;
  };

  /**
   * Estatísticas de keyspace
   */
  keyspace: {
    /**
     * Número total de chaves
     */
    keys: number;

    /**
     * Expiração média (segundos)
     */
    avgTtl: number;

    /**
     * Taxa de acertos de cache (%)
     */
    hitRate: number;

    /**
     * Chaves expiradas
     */
    expired: number;

    /**
     * Chaves removidas
     */
    evicted: number;
  };
}

/**
 * Interface para alerta do Valkey
 */
export interface ValkeyAlert {
  /**
   * Timestamp do alerta
   */
  timestamp: number;

  /**
   * Tipo do alerta
   */
  type: 'memory' | 'cluster' | 'persistence' | 'performance' | 'connection';

  /**
   * Nível de severidade
   */
  severity: 'info' | 'warning' | 'critical';

  /**
   * Mensagem do alerta
   */
  message: string;

  /**
   * Dados adicionais
   */
  data?: any;
}

/**
 * Serviço de monitoramento do Valkey
 */
export const valkeyMonitoringService = {
  /**
   * Cliente Valkey
   */
  client: null as RedisClientType | RedisClusterType | null,

  /**
   * Emissor de eventos
   */
  events: new EventEmitter(),

  /**
   * Intervalo de monitoramento
   */
  monitoringInterval: null as NodeJS.Timeout | null,

  /**
   * Histórico de métricas
   */
  metricsHistory: [] as ValkeyMetrics[],

  /**
   * Histórico de alertas
   */
  alertsHistory: [] as ValkeyAlert[],

  /**
   * Inicializa o serviço de monitoramento
   */
  async initialize(): Promise<void> {
    try {
      logger.info('Inicializando serviço de monitoramento do Valkey');

      // Criar diretório para métricas
      const metricsDir = path.join(process.cwd(), 'metrics');
      await mkdirAsync(metricsDir, { recursive: true });

      // Conectar ao Valkey
      await this.connect();

      // Iniciar monitoramento
      const clusterConfig = getClusterConfig();

      if (clusterConfig.monitoring.enabled) {
        this.startMonitoring(clusterConfig.monitoring.interval);
      }

      logger.info('Serviço de monitoramento do Valkey inicializado com sucesso');
    } catch (error) {
      logger.error('Erro ao inicializar serviço de monitoramento do Valkey:', error);
      throw error;
    }
  },

  /**
   * Conecta ao Valkey
   */
  async connect(): Promise<void> {
    try {
      // Obter configurações
      const clusterConfig = getClusterConfig();

      // Configurar TLS
      const tlsOptions = cacheConfig.server.tls ? {
        // Opções TLS aqui
      } : undefined;

      // Criar cliente
      if (clusterConfig.enabled) {
        // Configuração de cluster
        this.client = createCluster({
          rootNodes: clusterConfig.nodes.map(node => ({
            socket: {
              host: node.host,
              port: node.port,
              tls: tlsOptions,
            },
          })),
          defaults: {
            username: cacheConfig.server.username || undefined,
            password: cacheConfig.server.password || undefined,
            database: cacheConfig.server.db,
          },
        });
      } else {
        // Configuração standalone
        this.client = createClient({
          socket: {
            host: cacheConfig.server.host,
            port: cacheConfig.server.port,
            tls: tlsOptions,
          },
          username: cacheConfig.server.username || undefined,
          password: cacheConfig.server.password || undefined,
          database: cacheConfig.server.db,
        });
      }

      // Configurar eventos
      this.client.on('error', (err) => {
        logger.error('Erro no cliente Valkey:', err);
        this.createAlert('connection', 'critical', `Erro de conexão: ${err.message}`);
      });

      // Conectar
      await this.client.connect();

      logger.info('Conectado ao Valkey para monitoramento');
    } catch (error) {
      logger.error('Erro ao conectar ao Valkey para monitoramento:', error);
      throw error;
    }
  },

  /**
   * Inicia o monitoramento
   * @param interval Intervalo de monitoramento (ms)
   */
  startMonitoring(interval: number = 10000): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
    }

    this.monitoringInterval = setInterval(async () => {
      try {
        await this.collectMetrics();
      } catch (error) {
        logger.error('Erro ao coletar métricas do Valkey:', error);
      }
    }, interval);

    logger.info(`Monitoramento do Valkey iniciado com intervalo de ${interval}ms`);
  },

  /**
   * Para o monitoramento
   */
  stopMonitoring(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
      logger.info('Monitoramento do Valkey parado');
    }
  },

  /**
   * Coleta métricas do Valkey
   */
  async collectMetrics(): Promise<ValkeyMetrics> {
    try {
      if (!this.client) {
        throw new Error('Cliente Valkey não inicializado');
      }

      // Coletar informações
      const info = await this.client.info();
      const clusterInfo = getClusterConfig().enabled ? await this.client.clusterInfo() : null;

      // Processar informações
      const sections = this.parseInfo(info);

      // Criar métricas
      const metrics: ValkeyMetrics = {
        timestamp: Date.now(),
        memory: {
          used: parseInt(sections.memory['used_memory'] || '0', 10),
          peak: parseInt(sections.memory['used_memory_peak'] || '0', 10),
          limit: parseInt(sections.memory['maxmemory'] || '0', 10),
          fragmentation: parseFloat(sections.memory['mem_fragmentation_ratio'] || '0'),
        },
        clients: {
          connected: parseInt(sections.clients['connected_clients'] || '0', 10),
          blocked: parseInt(sections.clients['blocked_clients'] || '0', 10),
          maxElapsed: parseInt(sections.clients['client_longest_output_list'] || '0', 10),
        },
        cpu: {
          system: parseFloat(sections.cpu['used_cpu_sys'] || '0'),
          user: parseFloat(sections.cpu['used_cpu_user'] || '0'),
          children: parseFloat(sections.cpu['used_cpu_sys_children'] || '0') + parseFloat(sections.cpu['used_cpu_user_children'] || '0'),
        },
        persistence: {
          lastSave: parseInt(sections.persistence['rdb_last_save_time'] || '0', 10) * 1000,
          changesSinceSave: parseInt(sections.persistence['rdb_changes_since_last_save'] || '0', 10),
          bgsaveInProgress: sections.persistence['rdb_bgsave_in_progress'] === '1',
          aofEnabled: sections.persistence['aof_enabled'] === '1',
          aofRewriteInProgress: sections.persistence['aof_rewrite_in_progress'] === '1',
        },
        commands: {
          total: parseInt(sections.stats['total_commands_processed'] || '0', 10),
          perSecond: parseInt(sections.stats['instantaneous_ops_per_sec'] || '0', 10),
          latency: parseFloat(sections.stats['instantaneous_latency'] || '0'),
        },
        keyspace: {
          keys: this.countKeys(sections.keyspace),
          avgTtl: this.calculateAvgTtl(sections.keyspace),
          hitRate: this.calculateHitRate(sections.stats),
          expired: parseInt(sections.stats['expired_keys'] || '0', 10),
          evicted: parseInt(sections.stats['evicted_keys'] || '0', 10),
        },
      };

      // Adicionar informações de cluster se disponíveis
      if (clusterInfo) {
        metrics.cluster = {
          state: clusterInfo.state || 'unknown',
          nodes: parseInt(clusterInfo.cluster_known_nodes || '0', 10),
          slotsAssigned: 16384 - parseInt(clusterInfo.cluster_slots_pfail || '0', 10) - parseInt(clusterInfo.cluster_slots_fail || '0', 10),
          slotsUnassigned: parseInt(clusterInfo.cluster_slots_pfail || '0', 10) + parseInt(clusterInfo.cluster_slots_fail || '0', 10),
        };
      }

      // Armazenar métricas no histórico
      this.metricsHistory.push(metrics);

      // Limitar tamanho do histórico
      if (this.metricsHistory.length > 1000) {
        this.metricsHistory = this.metricsHistory.slice(-1000);
      }

      // Verificar alertas
      this.checkAlerts(metrics);

      // Salvar métricas em arquivo
      await this.saveMetrics(metrics);

      // Emitir evento
      this.events.emit('metrics', metrics);

      return metrics;
    } catch (error) {
      logger.error('Erro ao coletar métricas do Valkey:', error);
      throw error;
    }
  },

  /**
   * Analisa informações do Valkey
   * @param info Informações do Valkey
   * @returns Informações organizadas por seção
   */
  parseInfo(info: string): Record<string, Record<string, string>> {
    const sections: Record<string, Record<string, string>> = {};
    let currentSection = '';

    info.split('\n').forEach(line => {
      // Ignorar linhas vazias e comentários
      if (!line || line.startsWith('#')) {
        return;
      }

      // Verificar se é uma seção
      if (line.startsWith('# ')) {
        currentSection = line.substring(2).toLowerCase();
        sections[currentSection] = {};
        return;
      }

      // Processar linha de informação
      const parts = line.split(':');
      if (parts.length >= 2) {
        const key = parts[0];
        const value = parts.slice(1).join(':');

        if (currentSection && key) {
          sections[currentSection][key] = value;
        }
      }
    });

    return sections;
  },

  /**
   * Conta o número total de chaves
   * @param keyspace Informações de keyspace
   * @returns Número total de chaves
   */
  countKeys(keyspace: Record<string, string>): number {
    let total = 0;

    Object.keys(keyspace).forEach(db => {
      const dbInfo = keyspace[db];
      const match = dbInfo.match(/keys=(\d+)/);

      if (match && match[1]) {
        total += parseInt(match[1], 10);
      }
    });

    return total;
  },

  /**
   * Calcula o TTL médio
   * @param keyspace Informações de keyspace
   * @returns TTL médio em segundos
   */
  calculateAvgTtl(keyspace: Record<string, string>): number {
    let totalTtl = 0;
    let totalKeys = 0;

    Object.keys(keyspace).forEach(db => {
      const dbInfo = keyspace[db];
      const keysMatch = dbInfo.match(/keys=(\d+)/);
      const ttlMatch = dbInfo.match(/avg_ttl=(\d+)/);

      if (keysMatch && keysMatch[1] && ttlMatch && ttlMatch[1]) {
        const keys = parseInt(keysMatch[1], 10);
        const ttl = parseInt(ttlMatch[1], 10);

        totalKeys += keys;
        totalTtl += ttl * keys;
      }
    });

    return totalKeys > 0 ? totalTtl / totalKeys : 0;
  },

  /**
   * Calcula a taxa de acertos de cache
   * @param stats Estatísticas do Valkey
   * @returns Taxa de acertos (%)
   */
  calculateHitRate(stats: Record<string, string>): number {
    const hits = parseInt(stats['keyspace_hits'] || '0', 10);
    const misses = parseInt(stats['keyspace_misses'] || '0', 10);

    const total = hits + misses;
    return total > 0 ? (hits / total) * 100 : 100;
  },

  /**
   * Verifica alertas com base nas métricas
   * @param metrics Métricas coletadas
   */
  checkAlerts(metrics: ValkeyMetrics): void {
    // Verificar uso de memória
    const memoryUsagePercent = metrics.memory.limit > 0
      ? (metrics.memory.used / metrics.memory.limit) * 100
      : 0;

    if (memoryUsagePercent > 90) {
      this.createAlert('memory', 'critical', `Uso de memória crítico: ${memoryUsagePercent.toFixed(2)}%`);
    } else if (memoryUsagePercent > 80) {
      this.createAlert('memory', 'warning', `Uso de memória elevado: ${memoryUsagePercent.toFixed(2)}%`);
    }

    // Verificar fragmentação de memória
    if (metrics.memory.fragmentation > 1.5) {
      this.createAlert('memory', 'warning', `Fragmentação de memória elevada: ${metrics.memory.fragmentation.toFixed(2)}`);
    }

    // Verificar estado do cluster
    if (metrics.cluster && metrics.cluster.slotsUnassigned > 0) {
      this.createAlert('cluster', 'warning', `Slots não atribuídos no cluster: ${metrics.cluster.slotsUnassigned}`);
    }

    // Verificar persistência
    const now = Date.now();
    const lastSaveAge = (now - metrics.persistence.lastSave) / 1000 / 60; // em minutos

    if (lastSaveAge > 60) {
      this.createAlert('persistence', 'warning', `Último salvamento RDB há mais de 60 minutos: ${lastSaveAge.toFixed(0)} minutos`);
    }

    if (metrics.persistence.changesSinceSave > 10000 && !metrics.persistence.bgsaveInProgress) {
      this.createAlert('persistence', 'warning', `Muitas alterações desde o último salvamento: ${metrics.persistence.changesSinceSave}`);
    }

    // Verificar taxa de acertos de cache
    if (metrics.keyspace.hitRate < 50) {
      this.createAlert('performance', 'warning', `Taxa de acertos de cache baixa: ${metrics.keyspace.hitRate.toFixed(2)}%`);
    }

    // Verificar clientes bloqueados
    if (metrics.clients.blocked > 5) {
      this.createAlert('performance', 'warning', `Muitos clientes bloqueados: ${metrics.clients.blocked}`);
    }
  },

  /**
   * Cria um alerta
   * @param type Tipo do alerta
   * @param severity Severidade do alerta
   * @param message Mensagem do alerta
   * @param data Dados adicionais
   */
  createAlert(type: ValkeyAlert['type'], severity: ValkeyAlert['severity'], message: string, data?: any): void {
    const alert: ValkeyAlert = {
      timestamp: Date.now(),
      type,
      severity,
      message,
      data
    };

    // Armazenar alerta no histórico
    this.alertsHistory.push(alert);

    // Limitar tamanho do histórico
    if (this.alertsHistory.length > 1000) {
      this.alertsHistory = this.alertsHistory.slice(-1000);
    }

    // Registrar alerta no log
    const logMethod = severity === 'critical' ? 'error' : (severity === 'warning' ? 'warn' : 'info');
    logger[logMethod](`Alerta Valkey [${type}]: ${message}`, data);

    // Emitir evento
    this.events.emit('alert', alert);

    // Enviar notificações
    this.sendAlertNotifications(alert);
  },

  /**
   * Envia notificações de alerta
   * @param alert Alerta a ser enviado
   */
  async sendAlertNotifications(alert: ValkeyAlert): Promise<void> {
    try {
      const clusterConfig = getClusterConfig();

      if (!clusterConfig.monitoring.alerts.enabled) {
        return;
      }

      // Enviar apenas alertas críticos e warnings
      if (alert.severity === 'info') {
        return;
      }

      // Enviar para cada canal configurado
      for (const channel of clusterConfig.monitoring.alerts.channels) {
        switch (channel) {
          case 'email':
            // Implementação de envio de email
            logger.info(`Enviando alerta por email: ${alert.message}`);
            break;

          case 'slack':
            // Implementação de envio para Slack
            logger.info(`Enviando alerta para Slack: ${alert.message}`);
            break;

          case 'webhook':
            // Enviar para webhook
            if (clusterConfig.monitoring.alerts.webhookUrl) {
              try {
                await axios.post(clusterConfig.monitoring.alerts.webhookUrl, {
                  alert,
                  source: 'valkey-monitoring',
                  environment: process.env.NODE_ENV
                });
                logger.info(`Alerta enviado para webhook: ${alert.message}`);
              } catch (error) {
                logger.error(`Erro ao enviar alerta para webhook: ${error}`);
              }
            }
            break;
        }
      }
    } catch (error) {
      logger.error('Erro ao enviar notificações de alerta:', error);
    }
  },

  /**
   * Salva métricas em arquivo
   * @param metrics Métricas a serem salvas
   */
  async saveMetrics(metrics: ValkeyMetrics): Promise<void> {
    try {
      // Criar diretório para métricas
      const metricsDir = path.join(process.cwd(), 'metrics');
      await mkdirAsync(metricsDir, { recursive: true });

      // Gerar nome do arquivo
      const date = new Date(metrics.timestamp);
      const dateStr = date.toISOString().split('T')[0];
      const filePath = path.join(metricsDir, `valkey_metrics_${dateStr}.jsonl`);

      // Adicionar métricas ao arquivo
      const metricsJson = JSON.stringify(metrics) + '\n';
      await fs.promises.appendFile(filePath, metricsJson);
    } catch (error) {
      logger.error('Erro ao salvar métricas em arquivo:', error);
    }
  },

  /**
   * Obtém métricas recentes
   * @param count Número de métricas a retornar
   * @returns Métricas recentes
   */
  getRecentMetrics(count: number = 60): ValkeyMetrics[] {
    return this.metricsHistory.slice(-count);
  },

  /**
   * Obtém alertas recentes
   * @param count Número de alertas a retornar
   * @returns Alertas recentes
   */
  getRecentAlerts(count: number = 20): ValkeyAlert[] {
    return this.alertsHistory.slice(-count);
  },

  /**
   * Finaliza o serviço de monitoramento
   */
  async shutdown(): Promise<void> {
    try {
      // Parar monitoramento
      this.stopMonitoring();

      // Desconectar cliente
      if (this.client) {
        await this.client.disconnect();
        this.client = null;
      }

      logger.info('Serviço de monitoramento do Valkey finalizado');
    } catch (error) {
      logger.error('Erro ao finalizar serviço de monitoramento do Valkey:', error);
    }
  }
};
