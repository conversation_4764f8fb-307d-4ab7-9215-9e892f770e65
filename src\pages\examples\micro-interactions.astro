---
/**
 * Página de exemplo para demonstrar micro-interações
 * Esta página mostra diferentes tipos de micro-interações disponíveis
 */
import BaseLayout from '../../layouts/BaseLayout.astro';
import { Container, Section } from '../../layouts/grid';

import IconSystem from '../../components/icons/IconSystem.astro';
import ButtonInteraction from '../../components/interactions/ButtonInteraction.astro';
import CardInteraction from '../../components/interactions/CardInteraction.astro';
import InputInteraction from '../../components/interactions/InputInteraction.astro';
import {
  FeedbackTransition,
  FormTransition,
  MicroTransition,
  PageTransition,
} from '../../components/transitions';
import DaisyButton from '../../components/ui/DaisyButton.astro';
import DaisyCard from '../../components/ui/DaisyCard.astro';
import Breadcrumbs from '../../components/ui/navigation/Breadcrumbs.astro';
import Tabs from '../../components/ui/navigation/Tabs.astro';

// Título da página
const title = 'Micro-interações';

// Breadcrumbs para a página atual
const breadcrumbItems = [
  { href: '/', label: 'Início' },
  { href: '/examples', label: 'Exemplos' },
  { label: 'Micro-interações' },
];

// Tabs para os diferentes tipos de micro-interações
const interactionTabs = [
  { id: 'micro', label: 'Micro Transições', isActive: true },
  { id: 'feedback', label: 'Feedback Visual' },
  { id: 'form', label: 'Formulários' },
  { id: 'buttons', label: 'Botões Interativos' },
  { id: 'cards', label: 'Cards Interativos' },
  { id: 'inputs', label: 'Inputs Interativos' },
];

// Exemplos de código
const microCode = `import { MicroTransition } from '../components/transitions';

<MicroTransition name="pulse" trigger="hover">
  <button>Botão com efeito pulse</button>
</MicroTransition>`;

const feedbackCode = `import { FeedbackTransition } from '../components/transitions';

<FeedbackTransition type="success" autoHide={true}>
  Operação realizada com sucesso!
</FeedbackTransition>`;

const formCode = `import { FormTransition } from '../components/transitions';

<FormTransition enableFieldValidation={true}>
  <form>
    <div class="ft-step">
      <!-- Passo 1 do formulário -->
    </div>
    <div class="ft-step">
      <!-- Passo 2 do formulário -->
    </div>
  </form>
</FormTransition>`;
---

<BaseLayout title={title} enableViewTransitions={true} transitionType="fade">
  <Container>
    <PageTransition type="fade" duration="0.5s">
      <Section>
        <Breadcrumbs items={breadcrumbItems} class="mb-6" />

        <h1 class="text-3xl font-bold mb-6">{title}</h1>

        <p class="mb-8">
          Esta página demonstra as diferentes micro-interações disponíveis no projeto Estação da Alfabetização.
          As micro-interações são pequenas animações que melhoram a experiência do usuário e fornecem feedback visual.
        </p>

        <Tabs tabs={interactionTabs}>
          <div slot="micro" class="py-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <DaisyCard title="Micro Transições">
                <p class="mb-4">
                  Micro transições são pequenas animações aplicadas a elementos de interface para melhorar a experiência do usuário.
                  Elas podem ser acionadas por hover, click, focus ou carregamento da página.
                </p>
                <pre class="bg-base-200 p-4 rounded-box overflow-x-auto mb-4"><code>{microCode}</code></pre>
              </DaisyCard>

              <div class="border rounded-box p-6 bg-base-200">
                <h3 class="text-xl font-bold mb-4">Exemplos</h3>

                <div class="space-y-4">
                  <MicroTransition name="pulse" trigger="hover">
                    <DaisyButton color="primary">Efeito Pulse (Hover)</DaisyButton>
                  </MicroTransition>

                  <MicroTransition name="bounce" trigger="click">
                    <DaisyButton color="secondary">Efeito Bounce (Click)</DaisyButton>
                  </MicroTransition>

                  <MicroTransition name="shake" trigger="hover">
                    <DaisyButton color="accent">Efeito Shake (Hover)</DaisyButton>
                  </MicroTransition>

                  <MicroTransition name="scale" trigger="hover">
                    <DaisyButton color="info">Efeito Scale (Hover)</DaisyButton>
                  </MicroTransition>

                  <MicroTransition name="rotate" trigger="click">
                    <DaisyButton color="warning">Efeito Rotate (Click)</DaisyButton>
                  </MicroTransition>

                  <MicroTransition name="highlight" trigger="hover">
                    <DaisyButton color="error">Efeito Highlight (Hover)</DaisyButton>
                  </MicroTransition>
                </div>
              </div>
            </div>
          </div>

          <div slot="feedback" class="py-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <DaisyCard title="Feedback Visual">
                <p class="mb-4">
                  Componentes de feedback visual fornecem informações importantes ao usuário sobre o resultado de suas ações.
                  Eles podem ser configurados para diferentes tipos de mensagens e comportamentos.
                </p>
                <pre class="bg-base-200 p-4 rounded-box overflow-x-auto mb-4"><code>{feedbackCode}</code></pre>
              </DaisyCard>

              <div class="border rounded-box p-6 bg-base-200">
                <h3 class="text-xl font-bold mb-4">Exemplos</h3>

                <div class="space-y-4">
                  <FeedbackTransition type="success" id="feedback-success" visible={false}>
                    Operação realizada com sucesso!
                  </FeedbackTransition>

                  <FeedbackTransition type="error" id="feedback-error" visible={false}>
                    Ocorreu um erro ao processar sua solicitação.
                  </FeedbackTransition>

                  <FeedbackTransition type="warning" id="feedback-warning" visible={false}>
                    Atenção! Esta ação não pode ser desfeita.
                  </FeedbackTransition>

                  <FeedbackTransition type="info" id="feedback-info" visible={false}>
                    Sua sessão expirará em 5 minutos.
                  </FeedbackTransition>

                  <div class="flex flex-wrap gap-2 mt-4">
                    <DaisyButton color="success" id="show-success">Mostrar Sucesso</DaisyButton>
                    <DaisyButton color="error" id="show-error">Mostrar Erro</DaisyButton>
                    <DaisyButton color="warning" id="show-warning">Mostrar Alerta</DaisyButton>
                    <DaisyButton color="info" id="show-info">Mostrar Info</DaisyButton>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div slot="form" class="py-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <DaisyCard title="Transições em Formulários">
                <p class="mb-4">
                  Transições em formulários melhoram a experiência do usuário durante o preenchimento e navegação entre etapas.
                  Elas incluem validação visual de campos e transições suaves entre etapas.
                </p>
                <pre class="bg-base-200 p-4 rounded-box overflow-x-auto mb-4"><code>{formCode}</code></pre>
              </DaisyCard>

              <div class="border rounded-box p-6 bg-base-200">
                <h3 class="text-xl font-bold mb-4">Exemplo de Formulário</h3>

                <FormTransition id="demo-form">
                  <form class="space-y-4">
                    <div class="ft-step">
                      <h4 class="text-lg font-semibold mb-3">Passo 1: Informações Pessoais</h4>

                      <div class="form-control w-full">
                        <label class="label">
                          <span class="label-text">Nome</span>
                        </label>
                        <input type="text" placeholder="Seu nome" class="input input-bordered w-full" required />
                      </div>

                      <div class="form-control w-full">
                        <label class="label">
                          <span class="label-text">Email</span>
                        </label>
                        <input type="email" placeholder="<EMAIL>" class="input input-bordered w-full" required />
                      </div>

                      <div class="mt-4">
                        <DaisyButton color="primary" class="ft-next">Próximo</DaisyButton>
                      </div>
                    </div>

                    <div class="ft-step">
                      <h4 class="text-lg font-semibold mb-3">Passo 2: Preferências</h4>

                      <div class="form-control w-full">
                        <label class="label">
                          <span class="label-text">Tema Preferido</span>
                        </label>
                        <select class="select select-bordered w-full" required>
                          <option value="">Selecione um tema</option>
                          <option value="light">Claro</option>
                          <option value="dark">Escuro</option>
                          <option value="system">Sistema</option>
                        </select>
                      </div>

                      <div class="form-control">
                        <label class="label cursor-pointer">
                          <span class="label-text">Receber notificações</span>
                          <input type="checkbox" class="checkbox" />
                        </label>
                      </div>

                      <div class="mt-4 flex gap-2">
                        <DaisyButton color="neutral" class="ft-prev">Anterior</DaisyButton>
                        <DaisyButton color="primary" type="submit">Enviar</DaisyButton>
                      </div>
                    </div>
                  </form>
                </FormTransition>
              </div>
            </div>
          </div>
          <div slot="buttons" class="py-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <DaisyCard title="Botões Interativos">
                <p class="mb-4">
                  Botões interativos fornecem feedback visual imediato às ações do usuário,
                  melhorando a experiência e tornando a interface mais responsiva.
                </p>
                <pre class="bg-base-200 p-4 rounded-box overflow-x-auto mb-4"><code>import ButtonInteraction from '../components/interactions/ButtonInteraction.astro';

&lt;ButtonInteraction
  text="Clique-me"
  type="ripple"
  variant="primary"
  icon="search"
  onClick="myFunction"
/&gt;</code></pre>
              </DaisyCard>

              <div class="border rounded-box p-6 bg-base-200">
                <h3 class="text-xl font-bold mb-4">Exemplos</h3>

                <div class="space-y-4">
                  <ButtonInteraction
                    text="Efeito Ripple"
                    type="ripple"
                    variant="primary"
                    icon="search"
                    onClick="buttonClicked('Ripple')"
                  />

                  <ButtonInteraction
                    text="Efeito Pulse"
                    type="pulse"
                    variant="secondary"
                    icon="notification"
                    onClick="buttonClicked('Pulse')"
                  />

                  <ButtonInteraction
                    text="Efeito Bounce"
                    type="bounce"
                    variant="accent"
                    icon="book"
                    onClick="buttonClicked('Bounce')"
                  />

                  <ButtonInteraction
                    text="Efeito Scale"
                    type="scale"
                    variant="info"
                    icon="info"
                    onClick="buttonClicked('Scale')"
                  />

                  <ButtonInteraction
                    text="Efeito Shake"
                    type="shake"
                    variant="warning"
                    icon="notification"
                    onClick="buttonClicked('Shake')"
                  />

                  <ButtonInteraction
                    text="Efeito Glow"
                    type="glow"
                    variant="success"
                    icon="school"
                    onClick="buttonClicked('Glow')"
                  />

                  <div id="button-message" class="mt-4 p-2 bg-info text-info-content rounded-box text-center" style="display: none;">
                    Botão clicado!
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div slot="cards" class="py-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <DaisyCard title="Cards Interativos">
                <p class="mb-4">
                  Cards interativos adicionam dinamismo ao conteúdo, tornando a navegação mais
                  envolvente e destacando informações importantes.
                </p>
                <pre class="bg-base-200 p-4 rounded-box overflow-x-auto mb-4"><code>import CardInteraction from '../components/interactions/CardInteraction.astro';

&lt;CardInteraction
  type="hover"
  title="Título do Card"
  variant="primary"
&gt;
  Conteúdo do card
&lt;/CardInteraction&gt;</code></pre>
              </DaisyCard>

              <div class="border rounded-box p-6 bg-base-200">
                <h3 class="text-xl font-bold mb-4">Exemplos</h3>

                <div class="grid grid-cols-2 gap-4 mb-4">
                  <CardInteraction type="hover" title="Hover" shadow={true} class="h-full">
                    <div class="p-2 flex flex-col items-center">
                      <IconSystem name="book" size={32} color="var(--primary-blue)" />
                      <p class="text-center text-sm mt-2">Passe o mouse</p>
                    </div>
                  </CardInteraction>

                  <CardInteraction type="tilt" title="Tilt" variant="primary" class="h-full">
                    <div class="p-2 flex flex-col items-center">
                      <IconSystem name="school" size={32} color="white" />
                      <p class="text-center text-sm mt-2">Mova o mouse</p>
                    </div>
                  </CardInteraction>

                  <CardInteraction
                    type="flip"
                    title="Flip"
                    variant="secondary"
                    class="h-full"
                    backContent="<div class='p-2 flex flex-col items-center'><p class='text-center text-sm'>Verso do card</p></div>"
                  >
                    <div class="p-2 flex flex-col items-center">
                      <IconSystem name="assignment" size={32} color="white" />
                      <p class="text-center text-sm mt-2">Clique</p>
                    </div>
                  </CardInteraction>

                  <CardInteraction type="expand" title="Expand" variant="accent" class="h-full">
                    <div class="p-2 flex flex-col items-center">
                      <IconSystem name="search" size={32} color="white" />
                      <p class="text-center text-sm mt-2">Clique</p>
                    </div>
                  </CardInteraction>
                </div>
              </div>
            </div>
          </div>

          <div slot="inputs" class="py-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <DaisyCard title="Inputs Interativos">
                <p class="mb-4">
                  Campos de entrada interativos melhoram o feedback durante o preenchimento de formulários,
                  tornando a validação mais clara e a experiência mais agradável.
                </p>
                <pre class="bg-base-200 p-4 rounded-box overflow-x-auto mb-4"><code>import InputInteraction from '../components/interactions/InputInteraction.astro';

&lt;InputInteraction
  type="text"
  interactionType="focus"
  label="Nome"
  placeholder="Digite seu nome"
  icon="person"
  required={true}
/&gt;</code></pre>
              </DaisyCard>

              <div class="border rounded-box p-6 bg-base-200">
                <h3 class="text-xl font-bold mb-4">Exemplos</h3>

                <div class="space-y-4">
                  <InputInteraction
                    type="text"
                    interactionType="focus"
                    label="Efeito Focus"
                    placeholder="Clique aqui"
                    icon="person"
                  />

                  <InputInteraction
                    type="email"
                    interactionType="validation"
                    label="Efeito Validation"
                    placeholder="Digite um email"
                    icon="email"
                    required={true}
                    errorMessage="Email inválido"
                    successMessage="Email válido!"
                  />

                  <InputInteraction
                    type="text"
                    interactionType="typing"
                    label="Efeito Typing"
                    placeholder="Digite algo"
                    icon="chat"
                  />

                  <InputInteraction
                    type="text"
                    interactionType="shake"
                    label="Efeito Shake"
                    placeholder="Campo obrigatório"
                    icon="warning"
                    required={true}
                    errorMessage="Este campo é obrigatório"
                  />

                  <button class="btn btn-sm btn-primary mt-2" id="shake-test-button">
                    Testar Validação
                  </button>
                </div>
              </div>
            </div>
          </div>
        </Tabs>
      </Section>
    </PageTransition>
  </Container>
</BaseLayout>

<script>
  // Executar após carregamento da página
  document.addEventListener('astro:page-load', () => {
    // Configurar botões de feedback
    document.getElementById('show-success')?.addEventListener('click', () => {
      if (typeof window.feedbackTransitions !== 'undefined') {
        window.feedbackTransitions['feedback-success']?.show();
      }
    });

    document.getElementById('show-error')?.addEventListener('click', () => {
      if (typeof window.feedbackTransitions !== 'undefined') {
        window.feedbackTransitions['feedback-error']?.show();
      }
    });

    document.getElementById('show-warning')?.addEventListener('click', () => {
      if (typeof window.feedbackTransitions !== 'undefined') {
        window.feedbackTransitions['feedback-warning']?.show();
      }
    });

    document.getElementById('show-info')?.addEventListener('click', () => {
      if (typeof window.feedbackTransitions !== 'undefined') {
        window.feedbackTransitions['feedback-info']?.show();
      }
    });

    // Configurar botão de teste de validação
    document.getElementById('shake-test-button')?.addEventListener('click', () => {
      const input = document.querySelector('.input-interaction-shake input');
      if (input) {
        input.reportValidity();
      }
    });
  });
</script>

<script>
  function buttonClicked(type) {
    const message = document.getElementById('button-message');
    if (message) {
      message.textContent = `Botão "${type}" clicado!`;
      message.style.display = 'block';
      setTimeout(() => {
        message.style.opacity = '0';
        setTimeout(() => {
          message.style.display = 'none';
          message.style.opacity = '1';
        }, 300);
      }, 2000);
    }
  }
</script>
