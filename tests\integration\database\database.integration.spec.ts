/**
 * Testes de integração para banco de dados
 * 
 * Este arquivo contém testes que verificam a integração entre o código
 * e o banco de dados, testando operações CRUD e transações.
 * Parte da implementação da tarefa 9.1.2 - Testes de integração
 */

import { test, expect } from '@playwright/test';
import { Pool } from 'pg';
import { v4 as uuidv4 } from 'uuid';

// Configuração do banco de dados de teste
const dbConfig = {
  host: process.env.TEST_DB_HOST || 'localhost',
  port: parseInt(process.env.TEST_DB_PORT || '5432'),
  database: process.env.TEST_DB_NAME || 'test_db',
  user: process.env.TEST_DB_USER || 'postgres',
  password: process.env.TEST_DB_PASSWORD || 'postgres',
  ssl: process.env.TEST_DB_SSL === 'true' ? { rejectUnauthorized: false } : undefined
};

// Prefixo para tabelas de teste
const TEST_PREFIX = 'test_integration_';

test.describe('Integração com Banco de Dados', () => {
  let pool: Pool;
  
  // Antes de todos os testes, conectar ao banco de dados
  test.beforeAll(async () => {
    pool = new Pool(dbConfig);
    
    // Verificar conexão
    try {
      await pool.query('SELECT NOW()');
      console.log('Conexão com banco de dados estabelecida');
    } catch (error) {
      console.error('Erro ao conectar ao banco de dados:', error);
      throw error;
    }
    
    // Criar tabelas de teste
    await pool.query(`
      CREATE TABLE IF NOT EXISTS ${TEST_PREFIX}users (
        id UUID PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        email VARCHAR(255) UNIQUE NOT NULL,
        password VARCHAR(255) NOT NULL,
        role VARCHAR(50) NOT NULL DEFAULT 'user',
        created_at TIMESTAMP NOT NULL DEFAULT NOW(),
        updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
        is_active BOOLEAN NOT NULL DEFAULT TRUE
      )
    `);
    
    await pool.query(`
      CREATE TABLE IF NOT EXISTS ${TEST_PREFIX}products (
        id UUID PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        description TEXT,
        price DECIMAL(10, 2) NOT NULL,
        stock INTEGER NOT NULL DEFAULT 0,
        created_at TIMESTAMP NOT NULL DEFAULT NOW(),
        updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
        is_active BOOLEAN NOT NULL DEFAULT TRUE
      )
    `);
    
    await pool.query(`
      CREATE TABLE IF NOT EXISTS ${TEST_PREFIX}orders (
        id UUID PRIMARY KEY,
        user_id UUID NOT NULL REFERENCES ${TEST_PREFIX}users(id),
        status VARCHAR(50) NOT NULL DEFAULT 'pending',
        total DECIMAL(10, 2) NOT NULL,
        created_at TIMESTAMP NOT NULL DEFAULT NOW(),
        updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
        CONSTRAINT fk_user FOREIGN KEY (user_id) REFERENCES ${TEST_PREFIX}users(id)
      )
    `);
    
    await pool.query(`
      CREATE TABLE IF NOT EXISTS ${TEST_PREFIX}order_items (
        id UUID PRIMARY KEY,
        order_id UUID NOT NULL,
        product_id UUID NOT NULL,
        quantity INTEGER NOT NULL,
        price DECIMAL(10, 2) NOT NULL,
        created_at TIMESTAMP NOT NULL DEFAULT NOW(),
        CONSTRAINT fk_order FOREIGN KEY (order_id) REFERENCES ${TEST_PREFIX}orders(id),
        CONSTRAINT fk_product FOREIGN KEY (product_id) REFERENCES ${TEST_PREFIX}products(id)
      )
    `);
  });
  
  // Após todos os testes, limpar tabelas e desconectar
  test.afterAll(async () => {
    // Limpar tabelas de teste
    await pool.query(`DROP TABLE IF EXISTS ${TEST_PREFIX}order_items`);
    await pool.query(`DROP TABLE IF EXISTS ${TEST_PREFIX}orders`);
    await pool.query(`DROP TABLE IF EXISTS ${TEST_PREFIX}products`);
    await pool.query(`DROP TABLE IF EXISTS ${TEST_PREFIX}users`);
    
    // Fechar conexão
    await pool.end();
  });
  
  // Antes de cada teste, limpar dados
  test.beforeEach(async () => {
    await pool.query(`DELETE FROM ${TEST_PREFIX}order_items`);
    await pool.query(`DELETE FROM ${TEST_PREFIX}orders`);
    await pool.query(`DELETE FROM ${TEST_PREFIX}products`);
    await pool.query(`DELETE FROM ${TEST_PREFIX}users`);
  });
  
  test('deve inserir e recuperar um usuário', async () => {
    const userId = uuidv4();
    const userData = {
      id: userId,
      name: 'Usuário Teste',
      email: '<EMAIL>',
      password: 'senha_hash',
      role: 'user'
    };
    
    // Inserir usuário
    await pool.query(`
      INSERT INTO ${TEST_PREFIX}users (id, name, email, password, role)
      VALUES ($1, $2, $3, $4, $5)
    `, [userData.id, userData.name, userData.email, userData.password, userData.role]);
    
    // Recuperar usuário
    const result = await pool.query(`
      SELECT * FROM ${TEST_PREFIX}users WHERE id = $1
    `, [userId]);
    
    expect(result.rows.length).toBe(1);
    expect(result.rows[0].id).toBe(userId);
    expect(result.rows[0].name).toBe(userData.name);
    expect(result.rows[0].email).toBe(userData.email);
    expect(result.rows[0].role).toBe(userData.role);
    expect(result.rows[0].is_active).toBe(true);
  });
  
  test('deve atualizar um usuário', async () => {
    const userId = uuidv4();
    const userData = {
      id: userId,
      name: 'Usuário Teste',
      email: '<EMAIL>',
      password: 'senha_hash',
      role: 'user'
    };
    
    // Inserir usuário
    await pool.query(`
      INSERT INTO ${TEST_PREFIX}users (id, name, email, password, role)
      VALUES ($1, $2, $3, $4, $5)
    `, [userData.id, userData.name, userData.email, userData.password, userData.role]);
    
    // Atualizar usuário
    const updatedName = 'Usuário Atualizado';
    await pool.query(`
      UPDATE ${TEST_PREFIX}users SET name = $1, updated_at = NOW() WHERE id = $2
    `, [updatedName, userId]);
    
    // Recuperar usuário atualizado
    const result = await pool.query(`
      SELECT * FROM ${TEST_PREFIX}users WHERE id = $1
    `, [userId]);
    
    expect(result.rows.length).toBe(1);
    expect(result.rows[0].name).toBe(updatedName);
  });
  
  test('deve excluir um usuário', async () => {
    const userId = uuidv4();
    const userData = {
      id: userId,
      name: 'Usuário Teste',
      email: '<EMAIL>',
      password: 'senha_hash',
      role: 'user'
    };
    
    // Inserir usuário
    await pool.query(`
      INSERT INTO ${TEST_PREFIX}users (id, name, email, password, role)
      VALUES ($1, $2, $3, $4, $5)
    `, [userData.id, userData.name, userData.email, userData.password, userData.role]);
    
    // Excluir usuário
    await pool.query(`
      DELETE FROM ${TEST_PREFIX}users WHERE id = $1
    `, [userId]);
    
    // Verificar que o usuário foi excluído
    const result = await pool.query(`
      SELECT * FROM ${TEST_PREFIX}users WHERE id = $1
    `, [userId]);
    
    expect(result.rows.length).toBe(0);
  });
  
  test('deve criar um pedido com itens em uma transação', async () => {
    // Criar usuário
    const userId = uuidv4();
    await pool.query(`
      INSERT INTO ${TEST_PREFIX}users (id, name, email, password, role)
      VALUES ($1, $2, $3, $4, $5)
    `, [userId, 'Usuário Teste', '<EMAIL>', 'senha_hash', 'user']);
    
    // Criar produtos
    const product1Id = uuidv4();
    const product2Id = uuidv4();
    
    await pool.query(`
      INSERT INTO ${TEST_PREFIX}products (id, name, description, price, stock)
      VALUES ($1, $2, $3, $4, $5)
    `, [product1Id, 'Produto 1', 'Descrição do Produto 1', 100.00, 10]);
    
    await pool.query(`
      INSERT INTO ${TEST_PREFIX}products (id, name, description, price, stock)
      VALUES ($1, $2, $3, $4, $5)
    `, [product2Id, 'Produto 2', 'Descrição do Produto 2', 200.00, 5]);
    
    // Iniciar transação
    const client = await pool.connect();
    try {
      await client.query('BEGIN');
      
      // Criar pedido
      const orderId = uuidv4();
      const orderTotal = 500.00; // 1 * 100 + 2 * 200
      
      await client.query(`
        INSERT INTO ${TEST_PREFIX}orders (id, user_id, status, total)
        VALUES ($1, $2, $3, $4)
      `, [orderId, userId, 'pending', orderTotal]);
      
      // Adicionar itens ao pedido
      const item1Id = uuidv4();
      const item2Id = uuidv4();
      
      await client.query(`
        INSERT INTO ${TEST_PREFIX}order_items (id, order_id, product_id, quantity, price)
        VALUES ($1, $2, $3, $4, $5)
      `, [item1Id, orderId, product1Id, 1, 100.00]);
      
      await client.query(`
        INSERT INTO ${TEST_PREFIX}order_items (id, order_id, product_id, quantity, price)
        VALUES ($1, $2, $3, $4, $5)
      `, [item2Id, orderId, product2Id, 2, 200.00]);
      
      // Atualizar estoque dos produtos
      await client.query(`
        UPDATE ${TEST_PREFIX}products SET stock = stock - 1 WHERE id = $1
      `, [product1Id]);
      
      await client.query(`
        UPDATE ${TEST_PREFIX}products SET stock = stock - 2 WHERE id = $1
      `, [product2Id]);
      
      // Confirmar transação
      await client.query('COMMIT');
      
      // Verificar pedido
      const orderResult = await pool.query(`
        SELECT * FROM ${TEST_PREFIX}orders WHERE id = $1
      `, [orderId]);
      
      expect(orderResult.rows.length).toBe(1);
      expect(orderResult.rows[0].user_id).toBe(userId);
      expect(orderResult.rows[0].status).toBe('pending');
      expect(parseFloat(orderResult.rows[0].total)).toBe(orderTotal);
      
      // Verificar itens do pedido
      const itemsResult = await pool.query(`
        SELECT * FROM ${TEST_PREFIX}order_items WHERE order_id = $1
      `, [orderId]);
      
      expect(itemsResult.rows.length).toBe(2);
      
      // Verificar estoque atualizado
      const product1Result = await pool.query(`
        SELECT stock FROM ${TEST_PREFIX}products WHERE id = $1
      `, [product1Id]);
      
      const product2Result = await pool.query(`
        SELECT stock FROM ${TEST_PREFIX}products WHERE id = $1
      `, [product2Id]);
      
      expect(product1Result.rows[0].stock).toBe(9);
      expect(product2Result.rows[0].stock).toBe(3);
    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }
  });
  
  test('deve fazer rollback em caso de erro na transação', async () => {
    // Criar usuário
    const userId = uuidv4();
    await pool.query(`
      INSERT INTO ${TEST_PREFIX}users (id, name, email, password, role)
      VALUES ($1, $2, $3, $4, $5)
    `, [userId, 'Usuário Teste', '<EMAIL>', 'senha_hash', 'user']);
    
    // Criar produto
    const productId = uuidv4();
    await pool.query(`
      INSERT INTO ${TEST_PREFIX}products (id, name, description, price, stock)
      VALUES ($1, $2, $3, $4, $5)
    `, [productId, 'Produto Teste', 'Descrição do Produto Teste', 100.00, 10]);
    
    // Iniciar transação
    const client = await pool.connect();
    try {
      await client.query('BEGIN');
      
      // Criar pedido
      const orderId = uuidv4();
      await client.query(`
        INSERT INTO ${TEST_PREFIX}orders (id, user_id, status, total)
        VALUES ($1, $2, $3, $4)
      `, [orderId, userId, 'pending', 100.00]);
      
      // Tentar adicionar item com produto inexistente (deve falhar)
      const itemId = uuidv4();
      const nonExistentProductId = uuidv4();
      
      try {
        await client.query(`
          INSERT INTO ${TEST_PREFIX}order_items (id, order_id, product_id, quantity, price)
          VALUES ($1, $2, $3, $4, $5)
        `, [itemId, orderId, nonExistentProductId, 1, 100.00]);
        
        // Se chegar aqui, o teste falhou
        expect(true).toBe(false);
      } catch (error) {
        // Esperado falhar devido à restrição de chave estrangeira
        await client.query('ROLLBACK');
      }
      
      // Verificar que o pedido não foi criado (rollback)
      const orderResult = await pool.query(`
        SELECT * FROM ${TEST_PREFIX}orders WHERE id = $1
      `, [orderId]);
      
      expect(orderResult.rows.length).toBe(0);
    } finally {
      client.release();
    }
  });
});
