/**
 * Implementação de logger para console
 *
 * Esta classe implementa um logger que escreve no console.
 */

import { LogLevel, LoggerConfig } from '../../domain/interfaces/Logger';
import { BaseLogger } from './BaseLogger';

/**
 * Cores para diferentes níveis de log
 */
const LEVEL_COLORS = {
  [LogLevel.DEBUG]: '\x1b[34m', // Azul
  [LogLevel.INFO]: '\x1b[32m', // Verde
  [LogLevel.WARN]: '\x1b[33m', // Amarelo
  [LogLevel.ERROR]: '\x1b[31m', // Vermelho
  [LogLevel.FATAL]: '\x1b[35m', // Magenta
};

/**
 * Reset de cor
 */
const RESET_COLOR = '\x1b[0m';

/**
 * Logger para console
 */
export class ConsoleLogger extends BaseLogger {
  /**
   * Se deve usar cores no console
   */
  private readonly useColors: boolean;

  /**
   * Cria uma nova instância de ConsoleLogger
   * @param context - Contexto do logger
   * @param config - Configuração do logger
   */
  constructor(context: string, config: LoggerConfig) {
    super(context, config);
    this.useColors = config.useColors !== false;
  }

  /**
   * Escreve uma entrada de log no console
   * @param level - Nível de log
   * @param logEntry - Entrada de log formatada
   */
  protected writeLog(level: LogLevel, logEntry: any): void {
    const format = this.config.format || 'text';

    if (format === 'json') {
      this.writeJsonLog(level, logEntry);
    } else {
      this.writeTextLog(level, logEntry);
    }
  }

  /**
   * Escreve uma entrada de log no formato JSON
   * @param level - Nível de log
   * @param logEntry - Entrada de log formatada
   */
  private writeJsonLog(level: LogLevel, logEntry: any): void {
    const json = JSON.stringify(logEntry);

    switch (level) {
      case LogLevel.DEBUG:
        console.debug(json);
        break;
      case LogLevel.INFO:
        console.info(json);
        break;
      case LogLevel.WARN:
        console.warn(json);
        break;
      case LogLevel.ERROR:
      case LogLevel.FATAL:
        console.error(json);
        break;
      default:
        console.log(json);
    }
  }

  /**
   * Escreve uma entrada de log no formato texto
   * @param level - Nível de log
   * @param logEntry - Entrada de log formatada
   */
  private writeTextLog(level: LogLevel, logEntry: any): void {
    let message = '';

    // Adicionar timestamp
    if (logEntry.timestamp) {
      message += `[${logEntry.timestamp}] `;
    }

    // Adicionar nível
    const levelStr = level.toUpperCase().padEnd(5);
    if (this.useColors) {
      message += `${LEVEL_COLORS[level]}${levelStr}${RESET_COLOR} `;
    } else {
      message += `${levelStr} `;
    }

    // Adicionar contexto
    if (logEntry.context) {
      message += `[${logEntry.context}] `;
    }

    // Adicionar mensagem
    message += logEntry.message;

    // Adicionar metadados
    if (logEntry.metadata) {
      const metadataStr = JSON.stringify(logEntry.metadata);
      message += ` ${metadataStr}`;
    }

    // Escrever no console
    switch (level) {
      case LogLevel.DEBUG:
        console.debug(message);
        break;
      case LogLevel.INFO:
        console.info(message);
        break;
      case LogLevel.WARN:
        console.warn(message);
        break;
      case LogLevel.ERROR:
      case LogLevel.FATAL:
        console.error(message);
        break;
      default:
        console.log(message);
    }
  }
}
