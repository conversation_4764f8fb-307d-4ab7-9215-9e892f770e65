/**
 * Serviço de Otimização de Banco de Dados
 *
 * Este serviço implementa funcionalidades para otimização de consultas SQL,
 * monitoramento de performance e gerenciamento de conexões com o banco de dados.
 *
 * Características:
 * - Análise de consultas lentas
 * - Otimização de pool de conexões
 * - Criação e manutenção de índices
 * - Implementação de paginação eficiente
 * - Monitoramento de performance
 */

import { getDatabaseConfig } from '@infrastructure/config/database.config';
import { pgHelper } from '@repository/pgHelper';
import { applicationMonitoringService } from '@services/applicationMonitoringService';
import { cacheService } from '@services/cacheService';
import { logger } from '@utils/logger';

/**
 * Interface para estatísticas de consulta
 */
export interface QueryStats {
  /**
   * Texto da consulta SQL
   */
  query: string;

  /**
   * Tempo médio de execução em milissegundos
   */
  avgExecutionTime: number;

  /**
   * Número de execuções
   */
  executionCount: number;

  /**
   * Número de linhas retornadas/afetadas
   */
  rowCount: number;

  /**
   * Timestamp da última execução
   */
  lastExecuted: number;

  /**
   * Se a consulta usa índices adequadamente
   */
  usesIndexes: boolean;

  /**
   * Tabelas envolvidas na consulta
   */
  tables: string[];

  /**
   * Tipo de consulta (SELECT, INSERT, UPDATE, DELETE)
   */
  queryType: string;
}

/**
 * Interface para configuração de pool de conexões
 */
export interface PoolConfig {
  /**
   * Número máximo de conexões no pool
   */
  maxConnections: number;

  /**
   * Tempo máximo de inatividade em milissegundos
   */
  idleTimeoutMillis: number;

  /**
   * Tempo máximo de conexão em milissegundos
   */
  connectionTimeoutMillis: number;

  /**
   * Estratégia de retry para conexões
   */
  retryStrategy: 'exponential' | 'linear' | 'none';
}

/**
 * Interface para informações de índice
 */
export interface IndexInfo {
  /**
   * Nome do índice
   */
  indexName: string;

  /**
   * Tabela do índice
   */
  tableName: string;

  /**
   * Colunas incluídas no índice
   */
  columns: string[];

  /**
   * Tipo de índice (btree, hash, gin, etc.)
   */
  indexType: string;

  /**
   * Se o índice é único
   */
  isUnique: boolean;

  /**
   * Tamanho do índice em bytes
   */
  indexSize: number;

  /**
   * Número de scans usando este índice
   */
  indexScans: number;
}

/**
 * Serviço de otimização de banco de dados
 */
export const dbOptimizationService = {
  /**
   * Chave de cache para estatísticas de consultas
   */
  QUERY_STATS_CACHE_KEY: 'db:query_stats',

  /**
   * Chave de cache para informações de índices
   */
  INDEX_INFO_CACHE_KEY: 'db:index_info',

  /**
   * Limiar para consultas lentas em milissegundos
   */
  SLOW_QUERY_THRESHOLD: 500,

  /**
   * Inicializa o serviço de otimização de banco de dados
   */
  async initialize(): Promise<void> {
    try {
      logger.info('Inicializando serviço de otimização de banco de dados');

      // Configurar pool de conexões otimizado
      await this.optimizeConnectionPool();

      // Analisar índices existentes
      await this.analyzeIndexes();

      // Configurar monitoramento de consultas
      this.setupQueryMonitoring();

      logger.info('Serviço de otimização de banco de dados inicializado com sucesso');
    } catch (error) {
      logger.error('Erro ao inicializar serviço de otimização de banco de dados:', error);
      throw error;
    }
  },

  /**
   * Configura o pool de conexões com valores otimizados
   */
  async optimizeConnectionPool(): Promise<void> {
    try {
      // Obter configuração atual
      const dbConfig = getDatabaseConfig();

      // Calcular configuração otimizada com base no ambiente
      const optimizedConfig: PoolConfig = {
        maxConnections: this.calculateOptimalPoolSize(),
        idleTimeoutMillis: 30000, // 30 segundos
        connectionTimeoutMillis: 5000, // 5 segundos
        retryStrategy: 'exponential',
      };

      logger.info('Configuração otimizada do pool de conexões:', optimizedConfig);

      // Registrar configuração no monitoramento
      applicationMonitoringService.setMetadata('db_pool_config', optimizedConfig);
    } catch (error) {
      logger.error('Erro ao otimizar pool de conexões:', error);
      throw error;
    }
  },

  /**
   * Calcula o tamanho ideal do pool de conexões
   * @returns Número ideal de conexões
   */
  calculateOptimalPoolSize(): number {
    // Obter número de CPUs disponíveis
    const numCpus = require('node:os').cpus().length;

    // Fórmula: (Núcleos * 2) + 1
    // Esta é uma heurística comum para pools de conexão
    const optimalSize = numCpus * 2 + 1;

    // Limitar a um máximo razoável
    return Math.min(optimalSize, 20);
  },

  /**
   * Configura monitoramento de consultas SQL
   */
  setupQueryMonitoring(): void {
    // Sobrescrever método de consulta para adicionar monitoramento
    const originalQuery = pgHelper.query;

    // @ts-ignore - Sobrescrever método para adicionar monitoramento
    pgHelper.query = async (text: string, params?: unknown[], config?: any) => {
      // Iniciar timer
      const startTime = performance.now();

      try {
        // Executar consulta original
        const result = await originalQuery(text, params, config);

        // Calcular tempo de execução
        const executionTime = performance.now() - startTime;

        // Registrar métrica de tempo de execução
        applicationMonitoringService.recordMetric('db_query_duration', executionTime);

        // Verificar se é uma consulta lenta
        if (executionTime > this.SLOW_QUERY_THRESHOLD) {
          this.recordSlowQuery(text, executionTime, result.rowCount || 0);
        }

        // Atualizar estatísticas da consulta
        this.updateQueryStats(text, executionTime, result.rowCount || 0);

        return result;
      } catch (error) {
        // Registrar erro
        applicationMonitoringService.incrementCounter('db_query_error');

        // Registrar detalhes do erro para análise
        logger.error('Erro ao executar consulta SQL:', {
          query: text,
          params,
          error: error.message,
          stack: error.stack,
        });

        throw error;
      }
    };
  },

  /**
   * Registra uma consulta lenta para análise posterior
   * @param query - Consulta SQL
   * @param executionTime - Tempo de execução em milissegundos
   * @param rowCount - Número de linhas retornadas/afetadas
   */
  async recordSlowQuery(query: string, executionTime: number, rowCount: number): Promise<void> {
    try {
      // Incrementar contador de consultas lentas
      applicationMonitoringService.incrementCounter('db_slow_query');

      // Registrar detalhes da consulta lenta
      logger.warn('Consulta SQL lenta detectada:', {
        query: this.sanitizeQuery(query),
        executionTime: `${executionTime.toFixed(2)}ms`,
        rowCount,
        threshold: `${this.SLOW_QUERY_THRESHOLD}ms`,
      });

      // Armazenar no cache para análise posterior
      const slowQueryKey = `db:slow_query:${Date.now()}`;
      await cacheService.setItem(
        slowQueryKey,
        JSON.stringify({
          query: this.sanitizeQuery(query),
          executionTime,
          rowCount,
          timestamp: Date.now(),
        }),
        60 * 60 * 24 // 24 horas
      );
    } catch (error) {
      logger.error('Erro ao registrar consulta lenta:', error);
    }
  },

  /**
   * Atualiza estatísticas de consulta
   * @param query - Consulta SQL
   * @param executionTime - Tempo de execução em milissegundos
   * @param rowCount - Número de linhas retornadas/afetadas
   */
  async updateQueryStats(query: string, executionTime: number, rowCount: number): Promise<void> {
    try {
      // Sanitizar consulta para remover dados sensíveis e parâmetros
      const sanitizedQuery = this.sanitizeQuery(query);

      // Obter estatísticas existentes
      const statsJson = await cacheService.getItem(this.QUERY_STATS_CACHE_KEY);
      const stats: Record<string, QueryStats> = statsJson ? JSON.parse(statsJson) : {};

      // Atualizar ou criar estatísticas para esta consulta
      if (stats[sanitizedQuery]) {
        const currentStats = stats[sanitizedQuery];
        const newCount = currentStats.executionCount + 1;

        // Atualizar média de tempo de execução
        currentStats.avgExecutionTime =
          (currentStats.avgExecutionTime * currentStats.executionCount + executionTime) / newCount;

        currentStats.executionCount = newCount;
        currentStats.rowCount = (currentStats.rowCount + rowCount) / 2;
        currentStats.lastExecuted = Date.now();
      } else {
        // Determinar tipo de consulta
        const queryType = this.determineQueryType(sanitizedQuery);

        // Determinar tabelas envolvidas
        const tables = this.extractTablesFromQuery(sanitizedQuery);

        // Criar novas estatísticas
        stats[sanitizedQuery] = {
          query: sanitizedQuery,
          avgExecutionTime: executionTime,
          executionCount: 1,
          rowCount,
          lastExecuted: Date.now(),
          usesIndexes: false, // Será atualizado posteriormente
          tables,
          queryType,
        };
      }

      // Salvar estatísticas atualizadas
      await cacheService.setItem(
        this.QUERY_STATS_CACHE_KEY,
        JSON.stringify(stats),
        60 * 60 * 24 * 7 // 7 dias
      );
    } catch (error) {
      logger.error('Erro ao atualizar estatísticas de consulta:', error);
    }
  },

  /**
   * Sanitiza uma consulta SQL para remover dados sensíveis
   * @param query - Consulta SQL original
   * @returns Consulta sanitizada
   */
  sanitizeQuery(query: string): string {
    // Remover quebras de linha e espaços extras
    let sanitized = query.replace(/\s+/g, ' ').trim();

    // Remover valores literais (podem conter dados sensíveis)
    sanitized = sanitized
      // Substituir strings
      .replace(/'[^']*'/g, "'?'")
      // Substituir números
      .replace(/\b\d+\b/g, '?')
      // Substituir UUIDs
      .replace(/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}/gi, '?')
      // Substituir timestamps
      .replace(/\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}.\d{3}Z/g, '?');

    return sanitized;
  },

  /**
   * Determina o tipo de consulta SQL
   * @param query - Consulta SQL
   * @returns Tipo de consulta
   */
  determineQueryType(query: string): string {
    const upperQuery = query.toUpperCase().trim();

    if (upperQuery.startsWith('SELECT')) return 'SELECT';
    if (upperQuery.startsWith('INSERT')) return 'INSERT';
    if (upperQuery.startsWith('UPDATE')) return 'UPDATE';
    if (upperQuery.startsWith('DELETE')) return 'DELETE';
    if (upperQuery.startsWith('CREATE')) return 'CREATE';
    if (upperQuery.startsWith('ALTER')) return 'ALTER';
    if (upperQuery.startsWith('DROP')) return 'DROP';

    return 'OTHER';
  },

  /**
   * Extrai nomes de tabelas de uma consulta SQL
   * @param query - Consulta SQL
   * @returns Lista de tabelas
   */
  extractTablesFromQuery(query: string): string[] {
    const tables: string[] = [];
    const upperQuery = query.toUpperCase();

    // Expressão regular para encontrar tabelas após FROM e JOIN
    const fromRegex = /FROM\s+([a-zA-Z0-9_]+)/g;
    const joinRegex = /JOIN\s+([a-zA-Z0-9_]+)/g;

    // Encontrar tabelas após FROM
    let match: RegExpExecArray | null;
    match = fromRegex.exec(upperQuery);
    while (match !== null) {
      if (match[1] && !tables.includes(match[1])) {
        tables.push(match[1]);
      }
      match = fromRegex.exec(upperQuery);
    }

    // Encontrar tabelas após JOIN
    match = joinRegex.exec(upperQuery);
    while (match !== null) {
      if (match[1] && !tables.includes(match[1])) {
        tables.push(match[1]);
      }
      match = joinRegex.exec(upperQuery);
    }

    return tables;
  },

  /**
   * Analisa índices existentes no banco de dados
   */
  async analyzeIndexes(): Promise<void> {
    try {
      // Consulta para obter informações sobre índices
      const result = await pgHelper.query(`
        SELECT
          i.relname AS index_name,
          t.relname AS table_name,
          array_agg(a.attname ORDER BY array_position(ix.indkey, a.attnum)) AS columns,
          ix.indisunique AS is_unique,
          am.amname AS index_type,
          pg_size_pretty(pg_relation_size(i.oid)) AS index_size,
          pg_stat_get_numscans(i.oid) AS index_scans
        FROM
          pg_index ix
          JOIN pg_class i ON i.oid = ix.indexrelid
          JOIN pg_class t ON t.oid = ix.indrelid
          JOIN pg_am am ON am.oid = i.relam
          JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
        WHERE
          t.relkind = 'r'
          AND t.relname NOT LIKE 'pg_%'
          AND t.relname NOT LIKE 'sql_%'
        GROUP BY
          i.relname,
          t.relname,
          ix.indisunique,
          am.amname,
          i.oid
        ORDER BY
          t.relname,
          i.relname;
      `);

      // Processar resultados
      const indexes: IndexInfo[] = result.rows.map((row) => ({
        indexName: row.index_name,
        tableName: row.table_name,
        columns: row.columns,
        indexType: row.index_type,
        isUnique: row.is_unique,
        indexSize: Number.parseInt(row.index_size.replace(/\D/g, '')),
        indexScans: Number.parseInt(row.index_scans),
      }));

      // Armazenar informações de índices no cache
      await cacheService.setItem(
        this.INDEX_INFO_CACHE_KEY,
        JSON.stringify(indexes),
        60 * 60 * 24 // 24 horas
      );

      // Registrar informações
      logger.info(`Analisados ${indexes.length} índices no banco de dados`);

      // Identificar índices não utilizados
      const unusedIndexes = indexes.filter((index) => index.indexScans === 0);

      if (unusedIndexes.length > 0) {
        logger.warn(`Detectados ${unusedIndexes.length} índices não utilizados:`, {
          indexes: unusedIndexes.map((idx) => `${idx.tableName}.${idx.indexName}`),
        });
      }
    } catch (error) {
      logger.error('Erro ao analisar índices:', error);
    }
  },

  /**
   * Obtém estatísticas de consultas
   * @returns Estatísticas de consultas
   */
  async getQueryStats(): Promise<QueryStats[]> {
    try {
      const statsJson = await cacheService.getItem(this.QUERY_STATS_CACHE_KEY);

      if (!statsJson) {
        return [];
      }

      const stats: Record<string, QueryStats> = JSON.parse(statsJson);
      return Object.values(stats).sort((a, b) => b.avgExecutionTime - a.avgExecutionTime);
    } catch (error) {
      logger.error('Erro ao obter estatísticas de consultas:', error);
      return [];
    }
  },

  /**
   * Obtém informações sobre índices
   * @returns Informações de índices
   */
  async getIndexInfo(): Promise<IndexInfo[]> {
    try {
      const infoJson = await cacheService.getItem(this.INDEX_INFO_CACHE_KEY);

      if (!infoJson) {
        return [];
      }

      return JSON.parse(infoJson);
    } catch (error) {
      logger.error('Erro ao obter informações de índices:', error);
      return [];
    }
  },

  /**
   * Obtém consultas lentas
   * @param limit - Limite de resultados
   * @returns Lista de consultas lentas
   */
  async getSlowQueries(limit = 10): Promise<any[]> {
    try {
      // Obter chaves de consultas lentas
      const keys = await cacheService.keys('db:slow_query:*');

      // Limitar número de chaves
      const limitedKeys = keys.slice(0, limit);

      // Obter dados para cada chave
      const queries = [];

      for (const key of limitedKeys) {
        const data = await cacheService.getItem(key);

        if (data) {
          queries.push(JSON.parse(data));
        }
      }

      // Ordenar por tempo de execução (mais lento primeiro)
      return queries.sort((a, b) => b.executionTime - a.executionTime);
    } catch (error) {
      logger.error('Erro ao obter consultas lentas:', error);
      return [];
    }
  },

  /**
   * Gera SQL para criar um índice
   * @param tableName - Nome da tabela
   * @param columns - Colunas para o índice
   * @param indexName - Nome do índice (opcional)
   * @param unique - Se o índice deve ser único
   * @returns SQL para criar o índice
   */
  generateCreateIndexSQL(
    tableName: string,
    columns: string[],
    indexName?: string,
    unique = false
  ): string {
    // Gerar nome do índice se não fornecido
    const idxName = indexName || `idx_${tableName}_${columns.join('_')}`;

    // Gerar SQL
    return `
      CREATE ${unique ? 'UNIQUE ' : ''}INDEX ${idxName}
      ON ${tableName} (${columns.join(', ')});
    `.trim();
  },

  /**
   * Gera SQL para consulta com paginação eficiente
   * @param tableName - Nome da tabela
   * @param columns - Colunas a serem selecionadas
   * @param whereClause - Cláusula WHERE (opcional)
   * @param orderColumn - Coluna para ordenação
   * @param pageSize - Tamanho da página
   * @param cursor - Valor do cursor para paginação
   * @param isDesc - Se a ordenação é descendente
   * @returns SQL com paginação
   */
  generatePaginatedQuerySQL(
    tableName: string,
    columns: string[],
    whereClause: string | undefined,
    orderColumn: string,
    pageSize: number,
    cursor?: any,
    isDesc = false
  ): string {
    // Colunas a serem selecionadas
    const selectColumns = columns.join(', ');

    // Operador de comparação baseado na direção
    const operator = isDesc ? '<' : '>';

    // Cláusula WHERE com cursor
    const cursorClause = cursor
      ? `${whereClause ? 'AND' : 'WHERE'} ${orderColumn} ${operator} $cursor`
      : '';

    // Gerar SQL
    return `
      SELECT ${selectColumns}
      FROM ${tableName}
      ${whereClause}
      ${cursorClause}
      ORDER BY ${orderColumn} ${isDesc ? 'DESC' : 'ASC'}
      LIMIT ${pageSize};
    `.trim();
  },
};
