/**
 * Webhook para recebimento de notificações de pagamentos PIX
 *
 * Este endpoint recebe notificações do Efí Pay quando um pagamento PIX é realizado.
 */

import { cacheService } from '@services/cacheService';
import { db } from '@services/dbService';
import { efiPayService } from '@services/efiPayService';
import { logger } from '@utils/logger';
import type { APIRoute } from 'astro';

/**
 * Prefixo para chaves de cache de webhook
 */
const WEBHOOK_CACHE_PREFIX = 'webhook:pix:';

/**
 * Tempo de cache para evitar processamento duplicado (em segundos)
 */
const WEBHOOK_CACHE_TTL = 86400; // 24 horas

export const POST: APIRoute = async ({ request }) => {
  try {
    // Verificar assinatura do webhook (em produção, implementar verificação de segurança)
    // const signature = request.headers.get('x-signature');

    // Obter corpo da requisição
    const payload = await request.json();

    // Validar payload
    if (!payload || !payload.pix) {
      return new Response(
        JSON.stringify({
          error: 'Invalid payload',
          message: 'Payload inválido',
        }),
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    // Processar cada pagamento PIX recebido
    for (const pix of payload.pix) {
      // Verificar se o webhook já foi processado (evitar duplicação)
      const webhookId = pix.endToEndId;
      const cacheKey = `${WEBHOOK_CACHE_PREFIX}${webhookId}`;

      const alreadyProcessed = await cacheService.getItem(cacheKey);

      if (alreadyProcessed) {
        logger.info(`Webhook PIX já processado: ${webhookId}`);
        continue;
      }

      // Extrair informações do pagamento
      const txid = pix.txid;
      const e2eId = pix.endToEndId;
      const value = Number.parseFloat(pix.valor) * 100; // Converter para centavos
      const paidAt = new Date(pix.horario);

      // Obter detalhes completos da cobrança
      const pixCharge = await efiPayService.getPixCharge(txid);

      // Extrair informações adicionais
      const additionalInfo = {};

      if (pixCharge.infoAdicionais) {
        pixCharge.infoAdicionais.forEach((info) => {
          additionalInfo[info.nome] = info.valor;
        });
      }

      // Extrair informações do pagador
      const payer = pixCharge.devedor || {};

      // Registrar pagamento no banco de dados
      await db.query(
        `INSERT INTO payments (
          txid, e2e_id, payment_type, amount, status, paid_at,
          payer_name, payer_document, metadata, created_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, NOW())`,
        [
          txid,
          e2eId,
          'pix',
          value,
          'completed',
          paidAt,
          payer.nome || null,
          payer.cpf || payer.cnpj || null,
          JSON.stringify(additionalInfo),
        ]
      );

      // Processar ações pós-pagamento
      await processPayment(txid, value, additionalInfo);

      // Marcar webhook como processado
      await cacheService.setItem(cacheKey, 'processed', WEBHOOK_CACHE_TTL);

      logger.info(`Pagamento PIX processado: ${txid}, valor: ${value / 100}`);
    }

    // Retornar resposta de sucesso
    return new Response(
      JSON.stringify({
        message: 'Webhook processado com sucesso',
      }),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  } catch (error) {
    logger.error('Erro ao processar webhook PIX:', error);

    return new Response(
      JSON.stringify({
        error: 'Internal Server Error',
        message: 'Ocorreu um erro ao processar o webhook',
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  }
};

/**
 * Processa ações após confirmação de pagamento
 * @param txid - ID da transação
 * @param amount - Valor do pagamento em centavos
 * @param metadata - Metadados adicionais
 */
async function processPayment(
  txid: string,
  amount: number,
  metadata: Record<string, string>
): Promise<void> {
  try {
    // Verificar se é um pagamento de assinatura
    if (metadata.type === 'subscription') {
      const subscriptionId = metadata.subscription_id;

      if (subscriptionId) {
        // Atualizar assinatura
        await db.query(
          `UPDATE subscriptions SET
            status = 'active',
            last_payment_date = NOW(),
            last_payment_amount = $1,
            last_payment_txid = $2,
            updated_at = NOW()
          WHERE id = $3`,
          [amount, txid, subscriptionId]
        );

        // Verificar se é uma renovação ou nova assinatura
        const isRenewal = metadata.is_renewal === 'true';

        if (!isRenewal) {
          // Nova assinatura - calcular data de expiração
          const planDuration = Number.parseInt(metadata.plan_duration || '30', 10);

          await db.query(
            `UPDATE subscriptions SET
              start_date = NOW(),
              expiration_date = NOW() + INTERVAL '${planDuration} days'
            WHERE id = $1`,
            [subscriptionId]
          );
        } else {
          // Renovação - estender data de expiração
          const planDuration = Number.parseInt(metadata.plan_duration || '30', 10);

          await db.query(
            `UPDATE subscriptions SET
              expiration_date = 
                CASE 
                  WHEN expiration_date > NOW() THEN expiration_date + INTERVAL '${planDuration} days'
                  ELSE NOW() + INTERVAL '${planDuration} days'
                END
            WHERE id = $1`,
            [subscriptionId]
          );
        }
      }
    }

    // Verificar se é um pagamento de produto
    else if (metadata.type === 'product') {
      const orderId = metadata.order_id;

      if (orderId) {
        // Atualizar pedido
        await db.query(
          `UPDATE orders SET
            status = 'paid',
            payment_date = NOW(),
            payment_txid = $1,
            updated_at = NOW()
          WHERE id = $2`,
          [txid, orderId]
        );

        // Processar itens do pedido (ex: liberar acesso a produtos digitais)
        await db.query(
          `UPDATE order_items SET
            status = 'delivered'
          WHERE order_id = $1 AND product_type = 'digital'`,
          [orderId]
        );
      }
    }

    // Outros tipos de pagamento podem ser processados aqui
  } catch (error) {
    logger.error('Erro ao processar ações pós-pagamento:', error);
    throw error;
  }
}
