/**
 * Serviço de Monitoramento da Aplicação
 *
 * Este serviço implementa o monitoramento centralizado da aplicação,
 * coletando métricas de performance, saúde e uso de recursos.
 */

import { EventEmitter } from 'node:events';
import os from 'node:os';
import { performance } from 'node:perf_hooks';
import { pgHelper } from '@repository/pgHelper';
import { cacheService } from '@services/cacheService';
import { kafkaService } from '@services/kafkaService';
import { valkeyMonitoringService } from '@services/valkeyMonitoringService';
import { logger } from '@utils/logger';

/**
 * Tipos de métricas monitoradas
 */
export enum MetricType {
  // Métricas de sistema
  CPU_USAGE = 'cpu_usage',
  MEMORY_USAGE = 'memory_usage',
  HEAP_USAGE = 'heap_usage',
  ACTIVE_HANDLES = 'active_handles',
  ACTIVE_REQUESTS = 'active_requests',
  EVENT_LOOP_LAG = 'event_loop_lag',

  // Métricas de aplicação
  REQUEST_COUNT = 'request_count',
  REQUEST_DURATION = 'request_duration',
  REQUEST_ERROR_COUNT = 'request_error_count',
  REQUEST_SUCCESS_RATE = 'request_success_rate',

  // Métricas de banco de dados
  DB_QUERY_COUNT = 'db_query_count',
  DB_QUERY_DURATION = 'db_query_duration',
  DB_ERROR_COUNT = 'db_error_count',
  DB_CONNECTION_COUNT = 'db_connection_count',

  // Métricas de cache
  CACHE_HIT_COUNT = 'cache_hit_count',
  CACHE_MISS_COUNT = 'cache_miss_count',
  CACHE_HIT_RATE = 'cache_hit_rate',
  CACHE_SIZE = 'cache_size',

  // Métricas de Kafka
  KAFKA_PRODUCER_COUNT = 'kafka_producer_count',
  KAFKA_CONSUMER_COUNT = 'kafka_consumer_count',
  KAFKA_LAG = 'kafka_lag',
  KAFKA_ERROR_COUNT = 'kafka_error_count',

  // Métricas de negócio
  ACTIVE_USERS = 'active_users',
  TRANSACTION_COUNT = 'transaction_count',
  TRANSACTION_VALUE = 'transaction_value',
  ERROR_RATE = 'error_rate',
}

/**
 * Níveis de severidade para alertas
 */
export enum AlertSeverity {
  INFO = 'info',
  WARNING = 'warning',
  ERROR = 'error',
  CRITICAL = 'critical',
}

/**
 * Interface para configuração de alertas
 */
export interface AlertConfig {
  metricType: MetricType;
  threshold: number;
  operator: 'gt' | 'lt' | 'gte' | 'lte' | 'eq';
  severity: AlertSeverity;
  description: string;
  enabled: boolean;
}

/**
 * Interface para alerta gerado
 */
export interface Alert {
  id: string;
  timestamp: Date;
  metricType: MetricType;
  value: number;
  threshold: number;
  severity: AlertSeverity;
  description: string;
  resolved: boolean;
  resolvedAt?: Date;
}

/**
 * Interface para métrica coletada
 */
export interface Metric {
  type: MetricType;
  value: number;
  timestamp: Date;
  tags?: Record<string, string>;
}

/**
 * Configuração do serviço de monitoramento
 */
export const monitoringConfig = {
  // Intervalo de coleta de métricas em milissegundos
  collectionInterval: Number.parseInt(process.env.MONITORING_INTERVAL || '60000', 10),

  // Retenção de métricas em dias
  metricRetention: Number.parseInt(process.env.METRIC_RETENTION_DAYS || '30', 10),

  // Retenção de alertas em dias
  alertRetention: Number.parseInt(process.env.ALERT_RETENTION_DAYS || '90', 10),

  // Canais de notificação
  notificationChannels: {
    email: process.env.ALERT_EMAIL === 'true',
    slack: process.env.ALERT_SLACK === 'true',
    webhook: process.env.ALERT_WEBHOOK === 'true',
  },

  // Endpoints para notificações
  endpoints: {
    slack: process.env.SLACK_WEBHOOK_URL || '',
    webhook: process.env.ALERT_WEBHOOK_URL || '',
  },

  // Destinatários de email
  emailRecipients: (process.env.ALERT_EMAIL_RECIPIENTS || '').split(',').filter(Boolean),

  // Configurações de alertas
  alertConfigs: [
    {
      metricType: MetricType.CPU_USAGE,
      threshold: 80,
      operator: 'gt',
      severity: AlertSeverity.WARNING,
      description: 'CPU usage is high',
      enabled: true,
    },
    {
      metricType: MetricType.MEMORY_USAGE,
      threshold: 85,
      operator: 'gt',
      severity: AlertSeverity.WARNING,
      description: 'Memory usage is high',
      enabled: true,
    },
    {
      metricType: MetricType.EVENT_LOOP_LAG,
      threshold: 100,
      operator: 'gt',
      severity: AlertSeverity.ERROR,
      description: 'Event loop lag is high',
      enabled: true,
    },
    {
      metricType: MetricType.REQUEST_ERROR_COUNT,
      threshold: 50,
      operator: 'gt',
      severity: AlertSeverity.ERROR,
      description: 'High number of request errors',
      enabled: true,
    },
    {
      metricType: MetricType.DB_ERROR_COUNT,
      threshold: 10,
      operator: 'gt',
      severity: AlertSeverity.ERROR,
      description: 'High number of database errors',
      enabled: true,
    },
    {
      metricType: MetricType.CACHE_HIT_RATE,
      threshold: 50,
      operator: 'lt',
      severity: AlertSeverity.WARNING,
      description: 'Cache hit rate is low',
      enabled: true,
    },
    {
      metricType: MetricType.KAFKA_LAG,
      threshold: 1000,
      operator: 'gt',
      severity: AlertSeverity.WARNING,
      description: 'Kafka consumer lag is high',
      enabled: true,
    },
    {
      metricType: MetricType.ERROR_RATE,
      threshold: 5,
      operator: 'gt',
      severity: AlertSeverity.ERROR,
      description: 'Application error rate is high',
      enabled: true,
    },
  ] as AlertConfig[],
};

/**
 * Emissor de eventos para o serviço de monitoramento
 */
export const monitoringEvents = new EventEmitter();

// Configurar limite de listeners para evitar vazamentos de memória
monitoringEvents.setMaxListeners(100);

/**
 * Serviço de monitoramento da aplicação
 */
export const applicationMonitoringService = {
  /**
   * Métricas coletadas em memória
   */
  metrics: new Map<MetricType, Metric[]>(),

  /**
   * Alertas ativos
   */
  activeAlerts: new Map<string, Alert>(),

  /**
   * Contadores para métricas incrementais
   */
  counters: new Map<MetricType, number>(),

  /**
   * Intervalo de coleta de métricas
   */
  collectionInterval: null as NodeJS.Timeout | null,

  /**
   * Inicializa o serviço de monitoramento
   */
  async initialize(): Promise<void> {
    try {
      logger.info('Inicializando serviço de monitoramento da aplicação');

      // Inicializar contadores
      this.initializeCounters();

      // Registrar listeners para eventos
      this.registerEventListeners();

      // Iniciar coleta periódica de métricas
      this.startMetricCollection();

      logger.info('Serviço de monitoramento da aplicação inicializado com sucesso');
    } catch (error) {
      logger.error('Erro ao inicializar serviço de monitoramento da aplicação:', error);
      throw error;
    }
  },

  /**
   * Inicializa contadores para métricas incrementais
   */
  initializeCounters(): void {
    this.counters.set(MetricType.REQUEST_COUNT, 0);
    this.counters.set(MetricType.REQUEST_ERROR_COUNT, 0);
    this.counters.set(MetricType.DB_QUERY_COUNT, 0);
    this.counters.set(MetricType.DB_ERROR_COUNT, 0);
    this.counters.set(MetricType.CACHE_HIT_COUNT, 0);
    this.counters.set(MetricType.CACHE_MISS_COUNT, 0);
    this.counters.set(MetricType.KAFKA_PRODUCER_COUNT, 0);
    this.counters.set(MetricType.KAFKA_CONSUMER_COUNT, 0);
    this.counters.set(MetricType.KAFKA_ERROR_COUNT, 0);
    this.counters.set(MetricType.TRANSACTION_COUNT, 0);
    this.counters.set(MetricType.TRANSACTION_VALUE, 0);
  },

  /**
   * Registra listeners para eventos de monitoramento
   */
  registerEventListeners(): void {
    // Eventos de requisição
    monitoringEvents.on('request_start', ({ path, method }) => {
      this.incrementCounter(MetricType.REQUEST_COUNT);
    });

    monitoringEvents.on('request_end', ({ path, method, duration, statusCode }) => {
      this.recordMetric(MetricType.REQUEST_DURATION, duration);

      if (statusCode >= 400) {
        this.incrementCounter(MetricType.REQUEST_ERROR_COUNT);
      }
    });

    // Eventos de banco de dados
    monitoringEvents.on('db_query_start', ({ query }) => {
      this.incrementCounter(MetricType.DB_QUERY_COUNT);
    });

    monitoringEvents.on('db_query_end', ({ query, duration }) => {
      this.recordMetric(MetricType.DB_QUERY_DURATION, duration);
    });

    monitoringEvents.on('db_error', ({ error }) => {
      this.incrementCounter(MetricType.DB_ERROR_COUNT);
    });

    // Eventos de cache
    monitoringEvents.on('cache_hit', ({ key }) => {
      this.incrementCounter(MetricType.CACHE_HIT_COUNT);
    });

    monitoringEvents.on('cache_miss', ({ key }) => {
      this.incrementCounter(MetricType.CACHE_MISS_COUNT);
    });

    // Eventos de Kafka
    monitoringEvents.on('kafka_producer_send', ({ topic }) => {
      this.incrementCounter(MetricType.KAFKA_PRODUCER_COUNT);
    });

    monitoringEvents.on('kafka_consumer_receive', ({ topic }) => {
      this.incrementCounter(MetricType.KAFKA_CONSUMER_COUNT);
    });

    monitoringEvents.on('kafka_error', ({ error }) => {
      this.incrementCounter(MetricType.KAFKA_ERROR_COUNT);
    });

    // Eventos de negócio
    monitoringEvents.on('transaction_complete', ({ value }) => {
      this.incrementCounter(MetricType.TRANSACTION_COUNT);
      this.incrementCounter(MetricType.TRANSACTION_VALUE, value);
    });
  },

  /**
   * Inicia a coleta periódica de métricas
   */
  startMetricCollection(): void {
    // Coletar métricas imediatamente
    this.collectMetrics();

    // Configurar intervalo para coleta periódica
    this.collectionInterval = setInterval(() => {
      this.collectMetrics();
    }, monitoringConfig.collectionInterval);

    // Garantir que o intervalo não impede o encerramento do processo
    if (this.collectionInterval.unref) {
      this.collectionInterval.unref();
    }
  },

  /**
   * Para a coleta periódica de métricas
   */
  stopMetricCollection(): void {
    if (this.collectionInterval) {
      clearInterval(this.collectionInterval);
      this.collectionInterval = null;
    }
  },

  /**
   * Coleta todas as métricas do sistema e da aplicação
   */
  async collectMetrics(): Promise<void> {
    try {
      // Coletar métricas do sistema
      this.collectSystemMetrics();

      // Coletar métricas de banco de dados
      await this.collectDatabaseMetrics();

      // Coletar métricas de cache
      await this.collectCacheMetrics();

      // Coletar métricas de Kafka
      await this.collectKafkaMetrics();

      // Coletar métricas de negócio
      await this.collectBusinessMetrics();

      // Verificar alertas
      this.checkAlerts();

      // Limpar métricas antigas
      this.cleanupOldMetrics();
    } catch (error) {
      logger.error('Erro ao coletar métricas:', error);
    }
  },

  /**
   * Coleta métricas do sistema operacional e Node.js
   */
  collectSystemMetrics(): void {
    // CPU Usage
    const cpuUsage = process.cpuUsage();
    const cpuUsagePercent = ((cpuUsage.user + cpuUsage.system) / 1000000 / os.cpus().length) * 100;
    this.recordMetric(MetricType.CPU_USAGE, cpuUsagePercent);

    // Memory Usage
    const memoryUsage = process.memoryUsage();
    const totalMemory = os.totalmem();
    const freeMemory = os.freemem();
    const memoryUsagePercent = ((totalMemory - freeMemory) / totalMemory) * 100;
    this.recordMetric(MetricType.MEMORY_USAGE, memoryUsagePercent);

    // Heap Usage
    const heapUsagePercent = (memoryUsage.heapUsed / memoryUsage.heapTotal) * 100;
    this.recordMetric(MetricType.HEAP_USAGE, heapUsagePercent);

    // Event Loop Lag
    const start = performance.now();
    setImmediate(() => {
      const lag = performance.now() - start;
      this.recordMetric(MetricType.EVENT_LOOP_LAG, lag);
    });
  },

  /**
   * Coleta métricas do banco de dados
   */
  async collectDatabaseMetrics(): Promise<void> {
    try {
      // Número de conexões ativas
      const connectionCountResult = await pgHelper.query(
        'SELECT count(*) as connection_count FROM pg_stat_activity WHERE state = $1',
        ['active']
      );

      if (connectionCountResult.rows.length > 0) {
        const connectionCount = Number.parseInt(connectionCountResult.rows[0].connection_count, 10);
        this.recordMetric(MetricType.DB_CONNECTION_COUNT, connectionCount);
      }
    } catch (error) {
      logger.error('Erro ao coletar métricas de banco de dados:', error);
    }
  },

  /**
   * Coleta métricas do cache
   */
  async collectCacheMetrics(): Promise<void> {
    try {
      // Obter estatísticas do cache
      const cacheStats = await cacheService.info();

      // Calcular taxa de acertos
      const hitCount = this.counters.get(MetricType.CACHE_HIT_COUNT) || 0;
      const missCount = this.counters.get(MetricType.CACHE_MISS_COUNT) || 0;
      const totalCount = hitCount + missCount;

      if (totalCount > 0) {
        const hitRate = (hitCount / totalCount) * 100;
        this.recordMetric(MetricType.CACHE_HIT_RATE, hitRate);
      }

      // Obter tamanho do cache
      const cacheSize = await cacheService.dbsize();
      this.recordMetric(MetricType.CACHE_SIZE, cacheSize);
    } catch (error) {
      logger.error('Erro ao coletar métricas de cache:', error);
    }
  },

  /**
   * Coleta métricas do Kafka
   */
  async collectKafkaMetrics(): Promise<void> {
    try {
      // Obter lag de consumidores
      const consumerGroups = await kafkaService.getConsumerGroups();

      let totalLag = 0;

      for (const group of consumerGroups) {
        const lag = await kafkaService.getConsumerLag(group);
        totalLag += lag;
      }

      this.recordMetric(MetricType.KAFKA_LAG, totalLag);
    } catch (error) {
      logger.error('Erro ao coletar métricas do Kafka:', error);
    }
  },

  /**
   * Coleta métricas de negócio
   */
  async collectBusinessMetrics(): Promise<void> {
    try {
      // Usuários ativos
      const activeUsersResult = await pgHelper.query(
        'SELECT count(distinct user_id) as active_users FROM sessions WHERE last_activity > NOW() - INTERVAL $1 HOUR',
        [24]
      );

      if (activeUsersResult.rows.length > 0) {
        const activeUsers = Number.parseInt(activeUsersResult.rows[0].active_users, 10);
        this.recordMetric(MetricType.ACTIVE_USERS, activeUsers);
      }

      // Taxa de erro
      const requestCount = this.counters.get(MetricType.REQUEST_COUNT) || 0;
      const errorCount = this.counters.get(MetricType.REQUEST_ERROR_COUNT) || 0;

      if (requestCount > 0) {
        const errorRate = (errorCount / requestCount) * 100;
        this.recordMetric(MetricType.ERROR_RATE, errorRate);
      }
    } catch (error) {
      logger.error('Erro ao coletar métricas de negócio:', error);
    }
  },

  /**
   * Registra uma métrica
   * @param type Tipo da métrica
   * @param value Valor da métrica
   * @param tags Tags opcionais
   */
  recordMetric(type: MetricType, value: number, tags?: Record<string, string>): void {
    const metric: Metric = {
      type,
      value,
      timestamp: new Date(),
      tags,
    };

    // Armazenar em memória
    if (!this.metrics.has(type)) {
      this.metrics.set(type, []);
    }

    const metrics = this.metrics.get(type)!;
    metrics.push(metric);

    // Limitar número de métricas em memória
    if (metrics.length > 1000) {
      metrics.shift();
    }

    // Emitir evento de métrica
    monitoringEvents.emit('metric_recorded', metric);
  },

  /**
   * Incrementa um contador
   * @param type Tipo do contador
   * @param value Valor a incrementar (padrão: 1)
   */
  incrementCounter(type: MetricType, value = 1): void {
    const currentValue = this.counters.get(type) || 0;
    this.counters.set(type, currentValue + value);
  },

  /**
   * Verifica alertas com base nas métricas coletadas
   */
  checkAlerts(): void {
    for (const config of monitoringConfig.alertConfigs) {
      if (!config.enabled) {
        continue;
      }

      const metrics = this.metrics.get(config.metricType);

      if (!metrics || metrics.length === 0) {
        continue;
      }

      // Obter a métrica mais recente
      const latestMetric = metrics[metrics.length - 1];

      // Verificar se a métrica ultrapassa o limite
      let thresholdExceeded = false;

      switch (config.operator) {
        case 'gt':
          thresholdExceeded = latestMetric.value > config.threshold;
          break;
        case 'lt':
          thresholdExceeded = latestMetric.value < config.threshold;
          break;
        case 'gte':
          thresholdExceeded = latestMetric.value >= config.threshold;
          break;
        case 'lte':
          thresholdExceeded = latestMetric.value <= config.threshold;
          break;
        case 'eq':
          thresholdExceeded = latestMetric.value === config.threshold;
          break;
      }

      if (thresholdExceeded) {
        this.triggerAlert(config, latestMetric.value);
      } else {
        this.resolveAlerts(config.metricType);
      }
    }
  },

  /**
   * Dispara um alerta
   * @param config Configuração do alerta
   * @param value Valor que disparou o alerta
   */
  triggerAlert(config: AlertConfig, value: number): void {
    const alertId = `${config.metricType}_${config.severity}`;

    // Verificar se o alerta já está ativo
    if (this.activeAlerts.has(alertId)) {
      return;
    }

    // Criar novo alerta
    const alert: Alert = {
      id: alertId,
      timestamp: new Date(),
      metricType: config.metricType,
      value,
      threshold: config.threshold,
      severity: config.severity,
      description: config.description,
      resolved: false,
    };

    // Armazenar alerta
    this.activeAlerts.set(alertId, alert);

    // Emitir evento de alerta
    monitoringEvents.emit('alert_triggered', alert);

    // Enviar notificações
    this.sendAlertNotifications(alert);

    logger.warn(
      `Alerta disparado: ${config.description} (${config.metricType} = ${value}, limite: ${config.threshold})`
    );
  },

  /**
   * Resolve alertas para um tipo de métrica
   * @param metricType Tipo de métrica
   */
  resolveAlerts(metricType: MetricType): void {
    for (const [alertId, alert] of this.activeAlerts.entries()) {
      if (alert.metricType === metricType && !alert.resolved) {
        // Marcar alerta como resolvido
        alert.resolved = true;
        alert.resolvedAt = new Date();

        // Emitir evento de alerta resolvido
        monitoringEvents.emit('alert_resolved', alert);

        // Remover da lista de alertas ativos
        this.activeAlerts.delete(alertId);

        logger.info(`Alerta resolvido: ${alert.description} (${alert.metricType})`);
      }
    }
  },

  /**
   * Envia notificações para um alerta
   * @param alert Alerta a ser notificado
   */
  async sendAlertNotifications(alert: Alert): Promise<void> {
    try {
      const { notificationChannels, endpoints, emailRecipients } = monitoringConfig;

      // Preparar mensagem de alerta
      const message = `[${alert.severity.toUpperCase()}] ${alert.description}: ${alert.metricType} = ${alert.value} (limite: ${alert.threshold})`;

      // Enviar para canais configurados
      if (notificationChannels.email && emailRecipients.length > 0) {
        // Implementação de envio de email seria aqui
        logger.debug(`Enviando alerta por email: ${message}`);
      }

      if (notificationChannels.slack && endpoints.slack) {
        // Implementação de envio para Slack seria aqui
        logger.debug(`Enviando alerta para Slack: ${message}`);
      }

      if (notificationChannels.webhook && endpoints.webhook) {
        // Implementação de envio para webhook seria aqui
        logger.debug(`Enviando alerta para webhook: ${message}`);
      }
    } catch (error) {
      logger.error('Erro ao enviar notificações de alerta:', error);
    }
  },

  /**
   * Remove métricas antigas da memória
   */
  cleanupOldMetrics(): void {
    const now = new Date();
    const retentionMs = monitoringConfig.metricRetention * 24 * 60 * 60 * 1000;

    for (const [type, metrics] of this.metrics.entries()) {
      const filteredMetrics = metrics.filter((metric) => {
        const age = now.getTime() - metric.timestamp.getTime();
        return age < retentionMs;
      });

      this.metrics.set(type, filteredMetrics);
    }
  },

  /**
   * Obtém métricas por tipo
   * @param type Tipo de métrica
   * @param limit Limite de resultados (opcional)
   * @returns Métricas do tipo especificado
   */
  getMetrics(type: MetricType, limit?: number): Metric[] {
    const metrics = this.metrics.get(type) || [];

    if (limit && limit > 0) {
      return metrics.slice(-limit);
    }

    return metrics;
  },

  /**
   * Obtém o valor mais recente de uma métrica
   * @param type Tipo de métrica
   * @returns Valor mais recente ou null se não houver métricas
   */
  getLatestMetricValue(type: MetricType): number | null {
    const metrics = this.metrics.get(type) || [];

    if (metrics.length === 0) {
      return null;
    }

    return metrics[metrics.length - 1].value;
  },

  /**
   * Obtém alertas ativos
   * @returns Lista de alertas ativos
   */
  getActiveAlerts(): Alert[] {
    return Array.from(this.activeAlerts.values());
  },

  /**
   * Obtém o valor atual de um contador
   * @param type Tipo do contador
   * @returns Valor do contador
   */
  getCounterValue(type: MetricType): number {
    return this.counters.get(type) || 0;
  },

  /**
   * Reseta um contador
   * @param type Tipo do contador
   */
  resetCounter(type: MetricType): void {
    this.counters.set(type, 0);
  },

  /**
   * Obtém estatísticas resumidas do sistema
   * @returns Estatísticas do sistema
   */
  getSystemStats(): Record<string, any> {
    return {
      cpu: this.getLatestMetricValue(MetricType.CPU_USAGE) || 0,
      memory: this.getLatestMetricValue(MetricType.MEMORY_USAGE) || 0,
      heap: this.getLatestMetricValue(MetricType.HEAP_USAGE) || 0,
      eventLoopLag: this.getLatestMetricValue(MetricType.EVENT_LOOP_LAG) || 0,
      uptime: process.uptime(),
      activeAlerts: this.getActiveAlerts().length,
      requestCount: this.getCounterValue(MetricType.REQUEST_COUNT),
      errorCount: this.getCounterValue(MetricType.REQUEST_ERROR_COUNT),
      errorRate: this.getLatestMetricValue(MetricType.ERROR_RATE) || 0,
      activeUsers: this.getLatestMetricValue(MetricType.ACTIVE_USERS) || 0,
    };
  },
};
