---
import { actions } from 'astro:actions';
import ControlButtons from '@components/form/ControlButtons.astro';
import FormBase from '@components/form/FormBase.astro';
import InputHidden from '@components/form/InputHidden.astro';
import InputText from '@components/form/InputText.astro';
import BaseLayout from '@layouts/BaseLayout.astro';

const formValidation = `
  const typeInput = form.querySelector('input[name="type"]');
  if (!typeInput || !typeInput.value.trim()) {
      alert('O tipo de pagamento é obrigatório');
      return false;
  }
  return true;
`;
---
<BaseLayout title="Formulário de Tipo de Pagamento">
  <div class="container mx-auto p-4">Novo Tipo de Pagamento</div>

  <FormBase
    action={actions.paymentTypeAction.create}
    formType="paymentType"
    onSubmitValidation={formValidation}
  >
    <InputHidden field="ulid_payment_type" id="ulid_payment_type" />
    <InputText 
      label="Tipo" 
      name="type" 
      value="" 
      required={true} 
    />
    <ControlButtons 
      saveLabel="Criar"
      cancelHref="/admin/register/payment/type"
    />
  </FormBase>
</BaseLayout>