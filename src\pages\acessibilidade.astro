---
/**
 * Página de demonstração de recursos de acessibilidade
 */

import AccessibilityProvider from '../components/accessibility/AccessibilityProvider.astro';
import ImageDescription from '../components/accessibility/ImageDescription.astro';
import MediaCaptions from '../components/accessibility/MediaCaptions.astro';
import TextReader from '../components/accessibility/TextReader.astro';
import Layout from '../layouts/Layout.astro';
---

<Layout title="Recursos de Acessibilidade">
  <AccessibilityProvider />
  
  <main id="main-content" class="container mx-auto px-4 py-8">
    <h1 class="text-3xl font-bold mb-8">Recursos de Acessibilidade</h1>
    
    <section class="mb-12" aria-labelledby="intro-heading">
      <h2 id="intro-heading" class="text-2xl font-bold mb-4">Introdução</h2>
      
      <div class="bg-blue-50 dark:bg-blue-900 p-6 rounded-lg mb-6">
        <p class="mb-4">
          Esta página demonstra os recursos de acessibilidade disponíveis na plataforma. 
          Nosso objetivo é garantir que todos os usuários, independentemente de suas habilidades 
          ou limitações, possam utilizar nossos serviços de forma eficiente e agradável.
        </p>
        
        <p data-readable>
          Utilizamos as melhores práticas de acessibilidade digital, seguindo as diretrizes 
          WCAG (Web Content Accessibility Guidelines) e a norma brasileira de acessibilidade digital.
        </p>
      </div>
      
      <TextReader 
        selector=".bg-blue-50 p, [data-readable]"
        buttonText="Ler esta seção"
        showAdvancedControls={true}
      />
    </section>
    
    <section class="mb-12" aria-labelledby="features-heading">
      <h2 id="features-heading" class="text-2xl font-bold mb-4">Recursos Disponíveis</h2>
      
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div class="feature-card p-6 border rounded-lg">
          <h3 class="text-xl font-semibold mb-2">Configurações de Acessibilidade</h3>
          <p data-readable>
            Personalize a experiência de acordo com suas necessidades. Ajuste o tamanho do texto, 
            contraste, modo de cores, reduza animações e ative fontes para dislexia.
          </p>
          <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">
            Acesse através do botão "Acessibilidade" no canto inferior direito da tela.
          </p>
        </div>
        
        <div class="feature-card p-6 border rounded-lg">
          <h3 class="text-xl font-semibold mb-2">Navegação por Teclado</h3>
          <p data-readable>
            Navegue por toda a plataforma usando apenas o teclado. Utilize a tecla Tab para 
            mover entre elementos e Enter para ativá-los.
          </p>
          <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">
            Pressione "?" para ver todos os atalhos de teclado disponíveis.
          </p>
        </div>
        
        <div class="feature-card p-6 border rounded-lg">
          <h3 class="text-xl font-semibold mb-2">Leitor de Texto</h3>
          <p data-readable>
            Ouça o conteúdo da página em voz alta. Útil para pessoas com deficiência visual 
            ou dificuldades de leitura.
          </p>
          <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">
            Disponível em seções específicas da plataforma.
          </p>
        </div>
        
        <div class="feature-card p-6 border rounded-lg">
          <h3 class="text-xl font-semibold mb-2">Descrição de Imagens</h3>
          <p data-readable>
            Todas as imagens possuem descrições detalhadas, permitindo que usuários com 
            deficiência visual compreendam seu conteúdo.
          </p>
          <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">
            Clique no ícone "i" nas imagens para ver a descrição completa.
          </p>
        </div>
      </div>
    </section>
    
    <section class="mb-12" aria-labelledby="examples-heading">
      <h2 id="examples-heading" class="text-2xl font-bold mb-4">Exemplos Práticos</h2>
      
      <div class="mb-8">
        <h3 class="text-xl font-semibold mb-4">Descrição de Imagens</h3>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <ImageDescription 
            src="/images/turma-da-monica.jpg" 
            alt="Turma da Mônica reunida em um cenário colorido"
            description="Na imagem, os personagens principais da Turma da Mônica estão reunidos em um cenário colorido de parque. Da esquerda para a direita: Cascão vestindo camiseta amarela e shorts marrons, Cebolinha com camiseta verde e shorts pretos, Mônica ao centro usando vestido vermelho e segurando seu coelho Sansão, e Magali à direita com vestido amarelo. Todos estão sorrindo e de mãos dadas, simbolizando amizade. O fundo apresenta árvores verdes, flores coloridas e um céu azul claro com algumas nuvens brancas."
            width="400"
            height="300"
          />
          
          <ImageDescription 
            src="/images/alfabetizacao.jpg" 
            alt="Criança aprendendo a ler com blocos de letras"
            description="A imagem mostra uma criança de aproximadamente 6 anos, com cabelos castanhos curtos, sentada em uma mesa de madeira clara. Ela está concentrada organizando blocos coloridos de madeira com letras do alfabeto. Na mesa, há vários blocos formando palavras simples como 'CASA' e 'BOLA'. Ao lado da criança, há um livro infantil aberto com ilustrações coloridas. O ambiente é bem iluminado, com uma parede azul clara ao fundo e uma estante com mais livros infantis visível parcialmente no canto direito da imagem."
            width="400"
            height="300"
          />
        </div>
      </div>
      
      <div class="mb-8">
        <h3 class="text-xl font-semibold mb-4">Legendas e Transcrições</h3>
        
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div>
            <video 
              id="video-example" 
              controls 
              width="100%" 
              poster="/images/video-thumbnail.jpg"
            >
              <source src="/videos/alfabetizacao.mp4" type="video/mp4">
              Seu navegador não suporta a reprodução de vídeos.
            </video>
            
            <MediaCaptions 
              mediaId="video-example"
              captionsUrl="/captions/alfabetizacao.vtt"
              transcriptionText="Neste vídeo educativo sobre alfabetização, a professora Maria demonstra métodos lúdicos para ensinar as crianças a reconhecer letras e formar palavras simples. Ela começa apresentando cartões coloridos com as letras do alfabeto, pronunciando cada som claramente. Em seguida, mostra como combinar as letras para formar sílabas e palavras curtas como 'bola', 'casa' e 'pato'. As crianças participam repetindo os sons e tentando formar suas próprias palavras com blocos de letras. A professora enfatiza a importância de associar as letras a imagens e objetos do cotidiano para facilitar a memorização. O vídeo termina com uma atividade prática onde as crianças identificam palavras em um livro ilustrado."
            />
          </div>
          
          <div>
            <audio 
              id="audio-example" 
              controls 
              width="100%"
            >
              <source src="/audio/historia.mp3" type="audio/mpeg">
              Seu navegador não suporta a reprodução de áudio.
            </audio>
            
            <MediaCaptions 
              mediaId="audio-example"
              captionsUrl="/captions/historia.vtt"
              transcriptionText="Neste áudio, o narrador conta a história 'A Aventura da Letra A', uma narrativa educativa sobre alfabetização. A história começa com a Letra A acordando cedo e decidindo fazer novos amigos. Ela sai de casa e encontra primeiro a Letra B, que está brincando no balanço. Juntas, elas vão procurar a Letra C, que está comendo um delicioso chocolate. As três letras decidem formar uma palavra e chamam a Letra O para ajudar. Juntas, elas formam a palavra 'CABO'. Durante a aventura, elas encontram outras letras e formam diferentes palavras simples como 'BOCA', 'BOLA' e 'CASA'. A história termina com todas as letras fazendo uma festa para celebrar as novas amizades e as palavras que conseguiram formar juntas. O narrador conclui explicando como é divertido aprender a ler e escrever, convidando as crianças a continuarem explorando o mundo das letras."
            />
          </div>
        </div>
      </div>
    </section>
    
    <section class="mb-12" aria-labelledby="tips-heading">
      <h2 id="tips-heading" class="text-2xl font-bold mb-4">Dicas de Uso</h2>
      
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div class="tip-card p-6 border rounded-lg">
          <h3 class="text-xl font-semibold mb-2">Para Deficiência Visual</h3>
          <ul class="list-disc pl-5 space-y-2">
            <li>Utilize o leitor de tela do seu dispositivo</li>
            <li>Aumente o tamanho do texto nas configurações</li>
            <li>Ative o modo de alto contraste</li>
            <li>Use o leitor de texto para conteúdos longos</li>
          </ul>
        </div>
        
        <div class="tip-card p-6 border rounded-lg">
          <h3 class="text-xl font-semibold mb-2">Para Dislexia</h3>
          <ul class="list-disc pl-5 space-y-2">
            <li>Ative a fonte para dislexia nas configurações</li>
            <li>Aumente o espaçamento entre linhas e letras</li>
            <li>Use o leitor de texto para auxiliar na leitura</li>
            <li>Escolha o modo de cores que facilite sua leitura</li>
          </ul>
        </div>
        
        <div class="tip-card p-6 border rounded-lg">
          <h3 class="text-xl font-semibold mb-2">Para Mobilidade Reduzida</h3>
          <ul class="list-disc pl-5 space-y-2">
            <li>Utilize a navegação por teclado</li>
            <li>Ative os atalhos de teclado nas configurações</li>
            <li>Use a navegação por zonas para mover-se rapidamente</li>
            <li>Configure seu dispositivo para aceitar comandos de voz</li>
          </ul>
        </div>
      </div>
    </section>
    
    <section aria-labelledby="feedback-heading">
      <h2 id="feedback-heading" class="text-2xl font-bold mb-4">Feedback</h2>
      
      <div class="bg-gray-50 dark:bg-gray-800 p-6 rounded-lg">
        <p class="mb-4" data-readable>
          Estamos constantemente trabalhando para melhorar a acessibilidade da nossa plataforma. 
          Se você encontrar alguma dificuldade ou tiver sugestões, por favor, entre em contato conosco.
        </p>
        
        <a href="/contato" class="inline-block px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors">
          Enviar feedback
        </a>
      </div>
    </section>
  </main>
</Layout>

<style>
  .feature-card, .tip-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
  }
  
  .feature-card:hover, .tip-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
  }
  
  /* Estilos para reduzir animações quando necessário */
  body.reduce-animations .feature-card,
  body.reduce-animations .tip-card {
    transition: none;
  }
  
  body.reduce-animations .feature-card:hover,
  body.reduce-animations .tip-card:hover {
    transform: none;
    box-shadow: none;
  }
</style>
