/**
 * Validate Coupon Use Case
 *
 * Caso de uso para validar um cupom de desconto.
 * Parte da implementação da tarefa 8.4.1 - Sistema de cupons
 */

import { Coupon } from '../../entities/Coupon';
import { CouponRepository } from '../../repositories/CouponRepository';

export interface ValidateCouponRequest {
  code: string;
  purchaseAmount: number;
  userId?: string;
  isNewCustomer?: boolean;
  products?: Array<{
    id: string;
    price: number;
    quantity: number;
    categoryIds: string[];
  }>;
}

export interface ValidateCouponResponse {
  isValid: boolean;
  coupon?: Coupon;
  discountAmount?: number;
  errorCode?:
    | 'COUPON_NOT_FOUND'
    | 'COUPON_EXPIRED'
    | 'COUPON_INACTIVE'
    | 'USAGE_LIMIT_REACHED'
    | 'MINIMUM_PURCHASE_NOT_MET'
    | 'NEW_CUSTOMERS_ONLY'
    | 'INVALID_PRODUCTS';
  errorMessage?: string;
}

export class ValidateCouponUseCase {
  constructor(private couponRepository: CouponRepository) {}

  async execute(request: ValidateCouponRequest): Promise<ValidateCouponResponse> {
    try {
      // Normalizar código do cupom
      const code = request.code.trim().toUpperCase();

      // Buscar o cupom pelo código
      const coupon = await this.couponRepository.getByCode(code);

      // Verificar se o cupom existe
      if (!coupon) {
        return {
          isValid: false,
          errorCode: 'COUPON_NOT_FOUND',
          errorMessage: 'Cupom não encontrado.',
        };
      }

      // Verificar se o cupom está ativo
      if (!coupon.isActive) {
        return {
          isValid: false,
          coupon,
          errorCode: 'COUPON_INACTIVE',
          errorMessage: 'Este cupom não está ativo.',
        };
      }

      // Verificar se o cupom expirou
      if (coupon.hasExpired()) {
        return {
          isValid: false,
          coupon,
          errorCode: 'COUPON_EXPIRED',
          errorMessage: 'Este cupom expirou.',
        };
      }

      // Verificar se o cupom ainda não começou
      if (coupon.isUpcoming()) {
        return {
          isValid: false,
          coupon,
          errorCode: 'COUPON_EXPIRED',
          errorMessage: `Este cupom só será válido a partir de ${coupon.startDate?.toLocaleDateString()}.`,
        };
      }

      // Verificar se o cupom atingiu o limite de uso
      if (coupon.hasReachedUsageLimit()) {
        return {
          isValid: false,
          coupon,
          errorCode: 'USAGE_LIMIT_REACHED',
          errorMessage: 'Este cupom atingiu o limite de uso.',
        };
      }

      // Verificar valor mínimo de compra
      if (
        coupon.restrictions?.minPurchaseAmount &&
        request.purchaseAmount < coupon.restrictions.minPurchaseAmount
      ) {
        return {
          isValid: false,
          coupon,
          errorCode: 'MINIMUM_PURCHASE_NOT_MET',
          errorMessage: `O valor mínimo para este cupom é ${coupon.restrictions.minPurchaseAmount.toLocaleString('pt-BR', { style: 'currency', currency: 'BRL' })}.`,
        };
      }

      // Verificar restrição de novos clientes
      if (coupon.restrictions?.newCustomersOnly && request.isNewCustomer === false) {
        return {
          isValid: false,
          coupon,
          errorCode: 'NEW_CUSTOMERS_ONLY',
          errorMessage: 'Este cupom é válido apenas para novos clientes.',
        };
      }

      // Verificar produtos aplicáveis
      if (coupon.restrictions?.applicableProducts?.length && request.products?.length) {
        const hasApplicableProduct = request.products.some((product) =>
          coupon.restrictions?.applicableProducts?.includes(product.id)
        );

        if (!hasApplicableProduct) {
          return {
            isValid: false,
            coupon,
            errorCode: 'INVALID_PRODUCTS',
            errorMessage: 'Este cupom não é válido para os produtos selecionados.',
          };
        }
      }

      // Verificar categorias aplicáveis
      if (coupon.restrictions?.applicableCategories?.length && request.products?.length) {
        const hasApplicableCategory = request.products.some((product) =>
          product.categoryIds.some((categoryId) =>
            coupon.restrictions?.applicableCategories?.includes(categoryId)
          )
        );

        if (!hasApplicableCategory) {
          return {
            isValid: false,
            coupon,
            errorCode: 'INVALID_PRODUCTS',
            errorMessage: 'Este cupom não é válido para as categorias selecionadas.',
          };
        }
      }

      // Calcular o valor do desconto
      const discountAmount = coupon.calculateDiscount(request.purchaseAmount, {
        products: request.products?.map((p) => ({
          id: p.id,
          price: p.price,
          categoryIds: p.categoryIds,
        })),
        isNewCustomer: request.isNewCustomer,
      });

      return {
        isValid: true,
        coupon,
        discountAmount,
      };
    } catch (error) {
      console.error('Erro ao validar cupom:', error);
      return {
        isValid: false,
        errorMessage: 'Ocorreu um erro ao validar o cupom.',
      };
    }
  }
}
