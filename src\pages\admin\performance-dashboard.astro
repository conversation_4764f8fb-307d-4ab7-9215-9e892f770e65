---
/**
 * Página de dashboard de performance
 *
 * Esta página exibe métricas de performance coletadas dos usuários,
 * permitindo monitorar a saúde do site.
 */

import PerformanceChart from '../../components/admin/PerformanceChart.astro';
import OptimizedLayout from '../../layouts/OptimizedLayout.astro';
import { getMetricStats, getPerformanceMetrics } from '../../services/metrics-service';

// Verificar autenticação (simplificado para exemplo)
const isAuthenticated = true; // Em uma implementação real, verificaríamos a autenticação

// Se não estiver autenticado, redirecionar para login
if (!isAuthenticated) {
  return Astro.redirect('/admin/login');
}

// Obter período de análise dos parâmetros
const period = Astro.url.searchParams.get('period') || '7';
const days = Number.parseInt(period);

// Obter métricas
const metrics = await getPerformanceMetrics(days);

// Agrupar métricas por tipo
const metricsByName: Record<string, any[]> = {};

metrics.forEach((metric) => {
  if (!metricsByName[metric.name]) {
    metricsByName[metric.name] = [];
  }

  metricsByName[metric.name].push(metric);
});

// Obter estatísticas para cada tipo de métrica
const statsPromises = Object.keys(metricsByName).map((name) => getMetricStats(name, days));
const statsResults = await Promise.all(statsPromises);

// Criar mapa de estatísticas
const statsMap: Record<string, any> = {};
statsResults.forEach((stats) => {
  if (stats) {
    statsMap[stats.name] = stats;
  }
});

// Definir limites e objetivos para métricas principais
const metricConfig = {
  LCP: { threshold: 2500, target: 1500, unit: 'ms' },
  FID: { threshold: 100, target: 50, unit: 'ms' },
  CLS: { threshold: 0.1, target: 0.05, unit: '' },
  FCP: { threshold: 1800, target: 1000, unit: 'ms' },
  TTFB: { threshold: 600, target: 300, unit: 'ms' },
  APP_STARTUP: { threshold: 3000, target: 1500, unit: 'ms' },
};

// Ordenar métricas por importância
const priorityOrder = ['LCP', 'FID', 'CLS', 'FCP', 'TTFB', 'APP_STARTUP'];
const sortedMetricNames = Object.keys(metricsByName).sort((a, b) => {
  const aIndex = priorityOrder.indexOf(a);
  const bIndex = priorityOrder.indexOf(b);

  if (aIndex === -1 && bIndex === -1) return a.localeCompare(b);
  if (aIndex === -1) return 1;
  if (bIndex === -1) return -1;

  return aIndex - bIndex;
});

// Calcular pontuação geral de performance
let performanceScore = 100;
let coreWebVitalsCount = 0;

['LCP', 'FID', 'CLS'].forEach((metric) => {
  if (statsMap[metric]) {
    coreWebVitalsCount++;
    const stats = statsMap[metric];
    const config = metricConfig[metric as keyof typeof metricConfig];

    // Calcular pontuação para esta métrica (0-100)
    let metricScore = 100;

    if (stats.p75 > config.threshold) {
      // Se p75 exceder o limite, pontuação baixa
      metricScore = Math.max(0, 50 - (stats.p75 / config.threshold - 1) * 50);
    } else if (stats.p75 > config.target) {
      // Se p75 estiver entre alvo e limite, pontuação proporcional
      metricScore = 50 + ((config.threshold - stats.p75) / (config.threshold - config.target)) * 50;
    }

    // Adicionar à pontuação geral
    performanceScore += metricScore;
  }
});

// Calcular média
if (coreWebVitalsCount > 0) {
  performanceScore = performanceScore / (coreWebVitalsCount + 1);
}

// Determinar status com base na pontuação
let performanceStatus = 'good';
if (performanceScore < 50) {
  performanceStatus = 'bad';
} else if (performanceScore < 80) {
  performanceStatus = 'warning';
}
---

<OptimizedLayout title="Dashboard de Performance | Admin" pageName="admin">
  <div class="dashboard-container">
    <header class="dashboard-header">
      <h1>Dashboard de Performance</h1>
      
      <div class="period-selector">
        <label for="period-select">Período:</label>
        <select id="period-select" name="period">
          <option value="1" selected={period === '1'}>Último dia</option>
          <option value="7" selected={period === '7'}>Últimos 7 dias</option>
          <option value="30" selected={period === '30'}>Últimos 30 dias</option>
          <option value="90" selected={period === '90'}>Últimos 90 dias</option>
        </select>
      </div>
    </header>
    
    <div class={`performance-score status-${performanceStatus}`}>
      <div class="score-value">{Math.round(performanceScore)}</div>
      <div class="score-label">Pontuação de Performance</div>
    </div>
    
    <section class="metrics-section">
      <h2>Core Web Vitals</h2>
      
      <div class="metrics-grid">
        {['LCP', 'FID', 'CLS'].map(name => {
          if (!metricsByName[name]) return null;
          
          const config = metricConfig[name as keyof typeof metricConfig];
          
          return (
            <PerformanceChart
              data={metricsByName[name]}
              name={name}
              threshold={config.threshold}
              target={config.target}
              unit={config.unit}
              days={days}
            />
          );
        })}
      </div>
    </section>
    
    <section class="metrics-section">
      <h2>Métricas Adicionais</h2>
      
      <div class="metrics-grid">
        {sortedMetricNames
          .filter(name => !['LCP', 'FID', 'CLS'].includes(name))
          .map(name => {
            const config = metricConfig[name as keyof typeof metricConfig] || {
              threshold: 1000,
              target: 500,
              unit: 'ms'
            };
            
            return (
              <PerformanceChart
                data={metricsByName[name]}
                name={name}
                threshold={config.threshold}
                target={config.target}
                unit={config.unit}
                days={days}
              />
            );
          })}
      </div>
    </section>
    
    <section class="metrics-section">
      <h2>Estatísticas Detalhadas</h2>
      
      <table class="stats-table">
        <thead>
          <tr>
            <th>Métrica</th>
            <th>Média</th>
            <th>Mediana</th>
            <th>P75</th>
            <th>P95</th>
            <th>Min</th>
            <th>Max</th>
            <th>Amostras</th>
          </tr>
        </thead>
        <tbody>
          {sortedMetricNames.map(name => {
            const stats = statsMap[name];
            if (!stats) return null;
            
            const config = metricConfig[name as keyof typeof metricConfig] || {
              threshold: 1000,
              target: 500,
              unit: 'ms'
            };
            
            // Determinar status com base no p75
            let status = 'good';
            if (stats.p75 > config.threshold) {
              status = 'bad';
            } else if (stats.p75 > config.target) {
              status = 'warning';
            }
            
            return (
              <tr class={`status-${status}`}>
                <td>{name}</td>
                <td>{stats.avg.toFixed(2)}{config.unit}</td>
                <td>{stats.median.toFixed(2)}{config.unit}</td>
                <td>{stats.p75.toFixed(2)}{config.unit}</td>
                <td>{stats.p95.toFixed(2)}{config.unit}</td>
                <td>{stats.min.toFixed(2)}{config.unit}</td>
                <td>{stats.max.toFixed(2)}{config.unit}</td>
                <td>{stats.count}</td>
              </tr>
            );
          })}
        </tbody>
      </table>
    </section>
  </div>
</OptimizedLayout>

<style>
  .dashboard-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem 1rem;
  }
  
  .dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
  }
  
  .period-selector {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }
  
  .period-selector select {
    padding: 0.5rem;
    border-radius: 4px;
    border: 1px solid var(--border-medium);
    background-color: var(--background-primary);
  }
  
  .performance-score {
    background-color: var(--background-secondary);
    border-radius: 8px;
    padding: 2rem;
    margin-bottom: 2rem;
    text-align: center;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
  
  .score-value {
    font-size: 4rem;
    font-weight: 700;
    line-height: 1;
    margin-bottom: 0.5rem;
  }
  
  .score-label {
    font-size: 1.25rem;
    color: var(--text-secondary);
  }
  
  .status-good {
    border-left: 4px solid var(--primary-green);
  }
  
  .status-good .score-value {
    color: var(--primary-green);
  }
  
  .status-warning {
    border-left: 4px solid var(--primary-yellow);
  }
  
  .status-warning .score-value {
    color: var(--primary-yellow);
  }
  
  .status-bad {
    border-left: 4px solid var(--primary-red);
  }
  
  .status-bad .score-value {
    color: var(--primary-red);
  }
  
  .metrics-section {
    margin-bottom: 3rem;
  }
  
  .metrics-section h2 {
    margin-bottom: 1.5rem;
    font-size: 1.5rem;
  }
  
  .metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(500px, 1fr));
    gap: 1.5rem;
  }
  
  .stats-table {
    width: 100%;
    border-collapse: collapse;
    background-color: var(--background-secondary);
    border-radius: 8px;
    overflow: hidden;
  }
  
  .stats-table th,
  .stats-table td {
    padding: 0.75rem 1rem;
    text-align: left;
  }
  
  .stats-table th {
    background-color: var(--background-tertiary);
    font-weight: 600;
  }
  
  .stats-table tr {
    border-bottom: 1px solid var(--border-light);
  }
  
  .stats-table tr:last-child {
    border-bottom: none;
  }
  
  @media (max-width: 768px) {
    .metrics-grid {
      grid-template-columns: 1fr;
    }
    
    .dashboard-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 1rem;
    }
    
    .stats-table {
      font-size: 0.875rem;
    }
  }
</style>

<script>
  // Atualizar página quando o período for alterado
  document.addEventListener('DOMContentLoaded', () => {
    const periodSelect = document.getElementById('period-select');
    
    if (periodSelect) {
      periodSelect.addEventListener('change', (e) => {
        const target = e.target as HTMLSelectElement;
        const period = target.value;
        
        // Redirecionar para a mesma página com novo período
        window.location.href = `?period=${period}`;
      });
    }
  });
</script>
