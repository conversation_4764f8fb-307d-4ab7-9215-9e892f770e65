/**
 * API de Notificação de Emissão de Documento Fiscal
 *
 * Endpoint para notificar o cliente sobre a emissão de um documento fiscal.
 * Parte da implementação da tarefa 8.7.3 - Portal do cliente para documentos fiscais
 */

import type { APIRoute } from 'astro';
import { FiscalDocumentRepository } from '../../../../domain/repositories/FiscalDocumentRepository';
import { NotificationService } from '../../../../domain/services/NotificationService';
import { NotifyFiscalDocumentEmissionUseCase } from '../../../../domain/usecases/fiscal/NotifyFiscalDocumentEmissionUseCase';
import { PostgresFiscalDocumentRepository } from '../../../../infrastructure/database/repositories/PostgresFiscalDocumentRepository';
import { EmailNotificationService } from '../../../../infrastructure/services/EmailNotificationService';

// Inicializar repositório
const fiscalDocumentRepository: FiscalDocumentRepository = new PostgresFiscalDocumentRepository();

// Inicializar serviço de notificação
const notificationService: NotificationService = new EmailNotificationService({
  host: process.env.EMAIL_HOST || 'smtp.example.com',
  port: Number.parseInt(process.env.EMAIL_PORT || '587'),
  secure: process.env.EMAIL_SECURE === 'true',
  auth: {
    user: process.env.EMAIL_USER || '<EMAIL>',
    pass: process.env.EMAIL_PASS || 'password',
  },
  from: process.env.EMAIL_FROM || '<EMAIL>',
});

// Inicializar caso de uso
const notifyFiscalDocumentEmissionUseCase = new NotifyFiscalDocumentEmissionUseCase(
  fiscalDocumentRepository,
  notificationService
);

export const POST: APIRoute = async ({ params }) => {
  try {
    const { id } = params;

    if (!id) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'ID do documento é obrigatório.',
        }),
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    // Notificar emissão de documento fiscal
    const result = await notifyFiscalDocumentEmissionUseCase.execute({
      documentId: id,
    });

    if (result.success) {
      return new Response(
        JSON.stringify({
          success: true,
          data: result.data,
        }),
        {
          status: 200,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }
    return new Response(
      JSON.stringify({
        success: false,
        error: result.error || 'Erro desconhecido ao notificar emissão de documento fiscal.',
      }),
      {
        status: 400,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  } catch (error) {
    console.error('Erro ao processar notificação de emissão de documento fiscal:', error);

    return new Response(
      JSON.stringify({
        success: false,
        error:
          'Ocorreu um erro ao processar a notificação de emissão do documento fiscal. Por favor, tente novamente.',
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  }
};
