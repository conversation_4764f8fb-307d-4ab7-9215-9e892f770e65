/**
 * API para encerrar todas as sessões exceto a atual
 */

import { AuditEventType, AuditSeverity, auditService } from '@services/auditService';
import { sessionService } from '@services/sessionService';
import { getCurrentUser } from '@utils/authUtils';
import { logger } from '@utils/logger';
import type { APIRoute } from 'astro';

export const POST: APIRoute = async ({ cookies, request }) => {
  try {
    // Verificar autenticação
    const user = await getCurrentUser(cookies);

    if (!user) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Não autorizado',
        }),
        {
          status: 401,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    // Obter ID da sessão atual
    const currentSessionId = cookies.get('session_id')?.value;

    if (!currentSessionId) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Sessão atual não encontrada',
        }),
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    // Obter todas as sessões do usuário
    const sessions = await sessionService.getUserSessions(user.ulid_user);

    // Filtrar sessões para excluir a atual
    const sessionsToTerminate = sessions.filter((session) => session.id !== currentSessionId);

    // Encerrar cada sessão
    let terminatedCount = 0;

    for (const session of sessionsToTerminate) {
      const result = await sessionService.delete(session.id);

      if (result) {
        terminatedCount++;
      }
    }

    // Registrar ação no log de auditoria
    await auditService.logEvent({
      eventType: AuditEventType.SYSTEM_CONFIG_CHANGED,
      userId: user.ulid_user,
      userName: user.name,
      ipAddress:
        request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',
      userAgent: request.headers.get('user-agent') || 'unknown',
      resource: 'session',
      action: 'terminate-all',
      result: 'success',
      severity: AuditSeverity.INFO,
      metadata: {
        terminatedCount,
        totalSessions: sessions.length,
        currentSessionId,
      },
    });

    return new Response(
      JSON.stringify({
        success: true,
        message: `${terminatedCount} sessões encerradas com sucesso`,
        terminatedCount,
      }),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  } catch (error) {
    logger.error('Erro ao encerrar todas as sessões:', error);

    return new Response(
      JSON.stringify({
        success: false,
        error: 'Erro interno do servidor',
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  }
};
