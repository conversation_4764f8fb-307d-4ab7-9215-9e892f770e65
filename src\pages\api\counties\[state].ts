/**
 * API para obter municípios por estado
 *
 * Este endpoint retorna a lista de municípios para um estado específico.
 */

import { countyRepository } from '@repository/countyRepository';
import { logger } from '@utils/logger';
import type { APIRoute } from 'astro';

export const GET: APIRoute = async ({ params, request }) => {
  try {
    const { state } = params;

    if (!state) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Estado não especificado',
        }),
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    // Obter municípios do estado
    const counties = await countyRepository.read(undefined, state);

    return new Response(
      JSON.stringify({
        success: true,
        counties: counties.rows,
      }),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'max-age=3600', // Cache por 1 hora
        },
      }
    );
  } catch (error) {
    logger.error('Erro ao obter municípios:', error);

    return new Response(
      JSON.stringify({
        success: false,
        error: 'Erro ao obter municípios',
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  }
};
