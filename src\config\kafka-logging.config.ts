/**
 * Configuração de logging para o Kafka
 * 
 * Este arquivo contém as configurações de logging para o Kafka,
 * incluindo níveis de log, rotação de logs e armazenamento.
 */

// Níveis de log para o Kafka
export enum KafkaLogLevel {
  DEBUG = 'DEBUG',
  INFO = 'INFO',
  WARN = 'WARN',
  ERROR = 'ERROR',
  FATAL = 'FATAL',
}

// Tipos de destinos para logs
export enum KafkaLogDestination {
  CONSOLE = 'console',
  FILE = 'file',
  DATABASE = 'database',
  KAFKA = 'kafka',
}

// Interface para configuração de destino de log
export interface KafkaLogDestinationConfig {
  type: KafkaLogDestination;
  level: KafkaLogLevel;
  enabled: boolean;
  config?: {
    // Configurações específicas para arquivo
    filePath?: string;
    maxSize?: number; // em bytes
    maxFiles?: number;
    rotationInterval?: number; // em milissegundos
    
    // Configurações específicas para banco de dados
    tableName?: string;
    batchSize?: number;
    flushInterval?: number; // em milissegundos
    
    // Configurações específicas para Kafka
    topic?: string;
    partition?: number;
  };
}

// Interface para configuração de logging
export interface KafkaLoggingConfig {
  defaultLevel: KafkaLogLevel;
  includeTimestamp: boolean;
  includeHostname: boolean;
  includeProcessId: boolean;
  destinations: KafkaLogDestinationConfig[];
  
  // Configurações específicas para componentes
  components: {
    broker: {
      level: KafkaLogLevel;
      includeMetrics: boolean;
    };
    consumer: {
      level: KafkaLogLevel;
      includeOffsets: boolean;
    };
    producer: {
      level: KafkaLogLevel;
      includePartitions: boolean;
    };
    admin: {
      level: KafkaLogLevel;
    };
    topics: {
      level: KafkaLogLevel;
    };
  };
}

// Configuração padrão de logging para o Kafka
const defaultLoggingConfig: KafkaLoggingConfig = {
  defaultLevel: KafkaLogLevel.INFO,
  includeTimestamp: true,
  includeHostname: true,
  includeProcessId: true,
  destinations: [
    {
      type: KafkaLogDestination.CONSOLE,
      level: KafkaLogLevel.INFO,
      enabled: true,
    },
    {
      type: KafkaLogDestination.FILE,
      level: KafkaLogLevel.DEBUG,
      enabled: true,
      config: {
        filePath: 'logs/kafka.log',
        maxSize: 10 * 1024 * 1024, // 10 MB
        maxFiles: 10,
        rotationInterval: 24 * 60 * 60 * 1000, // 24 horas
      },
    },
    {
      type: KafkaLogDestination.DATABASE,
      level: KafkaLogLevel.WARN,
      enabled: true,
      config: {
        tableName: 'tab_kafka_logs',
        batchSize: 100,
        flushInterval: 60 * 1000, // 1 minuto
      },
    },
    {
      type: KafkaLogDestination.KAFKA,
      level: KafkaLogLevel.ERROR,
      enabled: false, // Desabilitado por padrão para evitar loops
      config: {
        topic: 'system.logs.kafka',
      },
    },
  ],
  components: {
    broker: {
      level: KafkaLogLevel.INFO,
      includeMetrics: true,
    },
    consumer: {
      level: KafkaLogLevel.INFO,
      includeOffsets: true,
    },
    producer: {
      level: KafkaLogLevel.INFO,
      includePartitions: true,
    },
    admin: {
      level: KafkaLogLevel.INFO,
    },
    topics: {
      level: KafkaLogLevel.INFO,
    },
  },
};

// Carregar configuração do ambiente
function loadConfigFromEnv(): Partial<KafkaLoggingConfig> {
  return {
    defaultLevel: (process.env.KAFKA_LOG_LEVEL as KafkaLogLevel) || KafkaLogLevel.INFO,
    includeTimestamp: process.env.KAFKA_LOG_TIMESTAMP !== 'false',
    includeHostname: process.env.KAFKA_LOG_HOSTNAME !== 'false',
    includeProcessId: process.env.KAFKA_LOG_PROCESS_ID !== 'false',
    destinations: [
      {
        type: KafkaLogDestination.CONSOLE,
        level: (process.env.KAFKA_LOG_CONSOLE_LEVEL as KafkaLogLevel) || KafkaLogLevel.INFO,
        enabled: process.env.KAFKA_LOG_CONSOLE_ENABLED !== 'false',
      },
      {
        type: KafkaLogDestination.FILE,
        level: (process.env.KAFKA_LOG_FILE_LEVEL as KafkaLogLevel) || KafkaLogLevel.DEBUG,
        enabled: process.env.KAFKA_LOG_FILE_ENABLED !== 'false',
        config: {
          filePath: process.env.KAFKA_LOG_FILE_PATH || 'logs/kafka.log',
          maxSize: parseInt(process.env.KAFKA_LOG_FILE_MAX_SIZE || '10485760', 10),
          maxFiles: parseInt(process.env.KAFKA_LOG_FILE_MAX_FILES || '10', 10),
          rotationInterval: parseInt(process.env.KAFKA_LOG_FILE_ROTATION_INTERVAL || '86400000', 10),
        },
      },
      {
        type: KafkaLogDestination.DATABASE,
        level: (process.env.KAFKA_LOG_DB_LEVEL as KafkaLogLevel) || KafkaLogLevel.WARN,
        enabled: process.env.KAFKA_LOG_DB_ENABLED === 'true',
        config: {
          tableName: process.env.KAFKA_LOG_DB_TABLE || 'tab_kafka_logs',
          batchSize: parseInt(process.env.KAFKA_LOG_DB_BATCH_SIZE || '100', 10),
          flushInterval: parseInt(process.env.KAFKA_LOG_DB_FLUSH_INTERVAL || '60000', 10),
        },
      },
      {
        type: KafkaLogDestination.KAFKA,
        level: (process.env.KAFKA_LOG_KAFKA_LEVEL as KafkaLogLevel) || KafkaLogLevel.ERROR,
        enabled: process.env.KAFKA_LOG_KAFKA_ENABLED === 'true',
        config: {
          topic: process.env.KAFKA_LOG_KAFKA_TOPIC || 'system.logs.kafka',
        },
      },
    ],
    components: {
      broker: {
        level: (process.env.KAFKA_LOG_BROKER_LEVEL as KafkaLogLevel) || KafkaLogLevel.INFO,
        includeMetrics: process.env.KAFKA_LOG_BROKER_METRICS !== 'false',
      },
      consumer: {
        level: (process.env.KAFKA_LOG_CONSUMER_LEVEL as KafkaLogLevel) || KafkaLogLevel.INFO,
        includeOffsets: process.env.KAFKA_LOG_CONSUMER_OFFSETS !== 'false',
      },
      producer: {
        level: (process.env.KAFKA_LOG_PRODUCER_LEVEL as KafkaLogLevel) || KafkaLogLevel.INFO,
        includePartitions: process.env.KAFKA_LOG_PRODUCER_PARTITIONS !== 'false',
      },
      admin: {
        level: (process.env.KAFKA_LOG_ADMIN_LEVEL as KafkaLogLevel) || KafkaLogLevel.INFO,
      },
      topics: {
        level: (process.env.KAFKA_LOG_TOPICS_LEVEL as KafkaLogLevel) || KafkaLogLevel.INFO,
      },
    },
  };
}

// Mesclar configuração padrão com configuração do ambiente
const envConfig = loadConfigFromEnv();
const kafkaLoggingConfig: KafkaLoggingConfig = {
  ...defaultLoggingConfig,
  ...envConfig,
  components: {
    ...defaultLoggingConfig.components,
    ...envConfig.components,
  },
};

// Exportar configuração de logging
export { kafkaLoggingConfig };
