---
import MonitoringDashboard from '@components/admin/MonitoringDashboard.astro';
import { checkAdmin } from '@helpers/authGuard';
/**
 * Página de Dashboard de Monitoramento
 *
 * Esta página exibe métricas e estatísticas de monitoramento
 * do sistema em um painel administrativo.
 */
import AdminLayout from '@layouts/AdminLayout.astro';

// Protege a rota verificando se o usuário é admin
const authRedirect = await checkAdmin(Astro);
if (authRedirect) return authRedirect;

// Título da página
const title = 'Monitoramento do Sistema';
---

<AdminLayout title={title}>
  <div class="page-header">
    <h1>{title}</h1>
    <div class="page-actions">
      <button id="refresh-btn" class="btn btn-primary">
        <i class="fas fa-sync-alt"></i> Atualizar
      </button>
      <button id="reset-counters-btn" class="btn btn-secondary">
        <i class="fas fa-redo"></i> Resetar Contadores
      </button>
    </div>
  </div>
  
  <MonitoringDashboard />
</AdminLayout>

<script>
  // Função para atualizar a página
  function refreshPage() {
    window.location.reload();
  }
  
  // Função para resetar contadores
  async function resetCounters() {
    if (!confirm('Tem certeza que deseja resetar os contadores de métricas?')) {
      return;
    }
    
    try {
      const response = await fetch('/api/monitoring/reset', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          types: [
            'request_count',
            'request_error_count',
            'db_query_count',
            'db_error_count',
            'cache_hit_count',
            'cache_miss_count',
            'kafka_producer_count',
            'kafka_consumer_count',
            'kafka_error_count',
            'transaction_count',
            'transaction_value'
          ]
        })
      });
      
      const data = await response.json();
      
      if (data.success) {
        alert('Contadores resetados com sucesso!');
        refreshPage();
      } else {
        alert(`Erro ao resetar contadores: ${data.error}`);
      }
    } catch (error) {
      alert(`Erro ao resetar contadores: ${error.message}`);
    }
  }
  
  // Adicionar event listeners quando o DOM estiver pronto
  document.addEventListener('DOMContentLoaded', () => {
    // Botão de atualizar
    const refreshBtn = document.getElementById('refresh-btn');
    if (refreshBtn) {
      refreshBtn.addEventListener('click', refreshPage);
    }
    
    // Botão de resetar contadores
    const resetCountersBtn = document.getElementById('reset-counters-btn');
    if (resetCountersBtn) {
      resetCountersBtn.addEventListener('click', resetCounters);
    }
  });
</script>

<style>
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
  }
  
  .page-actions {
    display: flex;
    gap: 0.5rem;
  }
  
  .btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s, color 0.2s;
    border: none;
  }
  
  .btn-primary {
    background-color: #4CAF50;
    color: white;
  }
  
  .btn-primary:hover {
    background-color: #388E3C;
  }
  
  .btn-secondary {
    background-color: #607D8B;
    color: white;
  }
  
  .btn-secondary:hover {
    background-color: #455A64;
  }
  
  @media (max-width: 768px) {
    .page-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 1rem;
    }
  }
</style>
