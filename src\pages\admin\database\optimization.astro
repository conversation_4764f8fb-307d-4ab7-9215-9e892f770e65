---
/**
 * Página de Otimização de Banco de Dados
 *
 * Esta página permite visualizar estatísticas de performance do banco de dados,
 * analisar consultas lentas, gerenciar índices e executar otimizações.
 */

// Importações
import AdminLayout from '@layouts/AdminLayout.astro';
import { pgHelper } from '@repository/pgHelper';
import { dbOptimizationService } from '@services/dbOptimizationService';
import { getCurrentUser } from '@utils/authUtils';
import { formatDate, formatDuration } from '@utils/formatters';
import { logger } from '@utils/logger';
import { hasPermission } from '@utils/permissionUtils';

// Verificar autenticação e permissão
const user = await getCurrentUser(Astro.cookies);

// Redirecionar se não estiver autenticado ou não tiver permissão
if (!user || !(await hasPermission(user.ulid_user, 'database:manage'))) {
  return Astro.redirect('/admin/login?redirect=/admin/database/optimization');
}

// Processar ações
let success = '';
let error = '';
let createIndexSQL = '';
let analyzedQuery = null;

if (Astro.request.method === 'POST') {
  try {
    const formData = await Astro.request.formData();
    const action = formData.get('action');

    if (action === 'analyze_query') {
      const query = formData.get('query') as string;

      if (query) {
        // Usar o middleware para analisar a consulta
        const { queryOptimizationMiddleware } = await import(
          '@middleware/queryOptimizationMiddleware'
        );
        analyzedQuery = queryOptimizationMiddleware.analyzeQuery(query);

        if (analyzedQuery.suggestedIndexes.length > 0) {
          createIndexSQL = analyzedQuery.suggestedIndexes[0].createSQL;
        }
      }
    } else if (action === 'create_index') {
      const tableName = formData.get('table_name') as string;
      const columnNames = formData.get('column_names') as string;
      const indexName = formData.get('index_name') as string;
      const isUnique = formData.get('is_unique') === 'true';

      if (tableName && columnNames) {
        const columns = columnNames.split(',').map((col) => col.trim());

        // Gerar SQL para criar índice
        createIndexSQL = dbOptimizationService.generateCreateIndexSQL(
          tableName,
          columns,
          indexName,
          isUnique
        );

        success = 'SQL para criação de índice gerado com sucesso';
      } else {
        error = 'Nome da tabela e colunas são obrigatórios';
      }
    } else if (action === 'execute_index_creation') {
      const indexSQL = formData.get('index_sql') as string;

      if (indexSQL) {
        // Executar SQL para criar índice
        await pgHelper.query(indexSQL);

        // Atualizar análise de índices
        await dbOptimizationService.analyzeIndexes();

        success = 'Índice criado com sucesso';
        logger.info('Índice criado manualmente:', {
          sql: indexSQL,
          adminUser: user.ulid_user,
        });
      } else {
        error = 'SQL para criação de índice é obrigatório';
      }
    }
  } catch (err) {
    error = err.message;
    logger.error('Erro ao processar ação de otimização de banco de dados:', err);
  }
}

// Obter estatísticas de consultas
const queryStats = await dbOptimizationService.getQueryStats();

// Obter informações de índices
const indexInfo = await dbOptimizationService.getIndexInfo();

// Obter consultas lentas
const slowQueries = await dbOptimizationService.getSlowQueries(10);

// Obter status do pool de conexões
const poolStatus = await pgHelper.getPoolStatus();

// Título da página
const title = 'Otimização de Banco de Dados';
---

<AdminLayout title={title}>
  <div class="container mx-auto px-4 py-8">
    <h1 class="text-3xl font-bold mb-6">{title}</h1>
    
    {error && (
      <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6" role="alert">
        <p class="font-bold">Erro</p>
        <p>{error}</p>
      </div>
    )}
    
    {success && (
      <div class="bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mb-6" role="alert">
        <p>{success}</p>
      </div>
    )}
    
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
      <div class="bg-white rounded-lg shadow-md p-6">
        <h2 class="text-xl font-semibold mb-4">Status do Pool de Conexões</h2>
        <div class="grid grid-cols-2 gap-4">
          <div class="bg-blue-50 p-4 rounded-lg">
            <p class="text-sm text-gray-600">Conexões Totais</p>
            <p class="text-2xl font-bold text-blue-600">{poolStatus.total}</p>
          </div>
          
          <div class="bg-blue-50 p-4 rounded-lg">
            <p class="text-sm text-gray-600">Conexões Ativas</p>
            <p class="text-2xl font-bold text-blue-600">{poolStatus.active}</p>
          </div>
          
          <div class="bg-blue-50 p-4 rounded-lg">
            <p class="text-sm text-gray-600">Conexões Ociosas</p>
            <p class="text-2xl font-bold text-blue-600">{poolStatus.idle}</p>
          </div>
          
          <div class="bg-blue-50 p-4 rounded-lg">
            <p class="text-sm text-gray-600">Conexões em Espera</p>
            <p class="text-2xl font-bold text-blue-600">{poolStatus.waiting}</p>
          </div>
        </div>
      </div>
      
      <div class="bg-white rounded-lg shadow-md p-6">
        <h2 class="text-xl font-semibold mb-4">Analisar Consulta SQL</h2>
        <form method="POST" class="space-y-4">
          <input type="hidden" name="action" value="analyze_query" />
          <div>
            <label for="query" class="block text-sm font-medium text-gray-700 mb-1">Consulta SQL</label>
            <textarea 
              id="query" 
              name="query" 
              rows="4" 
              class="w-full border border-gray-300 rounded-md px-3 py-2"
              placeholder="Digite a consulta SQL para análise"
              required
            ></textarea>
          </div>
          <button 
            type="submit" 
            class="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md"
          >
            Analisar Consulta
          </button>
        </form>
        
        {analyzedQuery && (
          <div class="mt-4 border-t pt-4">
            <h3 class="font-medium text-gray-700 mb-2">Resultado da Análise</h3>
            
            {analyzedQuery.issues.length > 0 ? (
              <div class="mb-3">
                <p class="font-medium text-red-600">Problemas Detectados:</p>
                <ul class="list-disc pl-5 mt-1 space-y-1">
                  {analyzedQuery.issues.map((issue) => (
                    <li class="text-sm">
                      <span class={`font-medium ${
                        issue.severity === 'critical' ? 'text-red-700' :
                        issue.severity === 'high' ? 'text-orange-600' :
                        issue.severity === 'medium' ? 'text-yellow-600' : 'text-blue-600'
                      }`}>
                        [{issue.severity.toUpperCase()}]
                      </span> {issue.description}
                    </li>
                  ))}
                </ul>
              </div>
            ) : (
              <p class="text-green-600 mb-3">Nenhum problema detectado na consulta.</p>
            )}
            
            {analyzedQuery.suggestions.length > 0 && (
              <div class="mb-3">
                <p class="font-medium text-blue-600">Sugestões:</p>
                <ul class="list-disc pl-5 mt-1 space-y-1">
                  {analyzedQuery.suggestions.map((suggestion) => (
                    <li class="text-sm">{suggestion}</li>
                  ))}
                </ul>
              </div>
            )}
            
            {analyzedQuery.wasOptimized && analyzedQuery.optimizedQuery && (
              <div class="mb-3">
                <p class="font-medium text-green-600">Consulta Otimizada:</p>
                <pre class="bg-gray-100 p-2 rounded text-sm mt-1 overflow-x-auto">
                  {analyzedQuery.optimizedQuery}
                </pre>
              </div>
            )}
            
            {analyzedQuery.suggestedIndexes.length > 0 && (
              <div>
                <p class="font-medium text-purple-600">Índices Sugeridos:</p>
                <div class="mt-1 space-y-2">
                  {analyzedQuery.suggestedIndexes.map((index) => (
                    <div class="bg-gray-100 p-2 rounded text-sm">
                      <p>Tabela: <span class="font-medium">{index.table}</span></p>
                      <p>Colunas: <span class="font-medium">{index.columns.join(', ')}</span></p>
                      <pre class="bg-gray-200 p-1 rounded mt-1 overflow-x-auto">
                        {index.createSQL}
                      </pre>
                      <form method="POST" class="mt-2">
                        <input type="hidden" name="action" value="execute_index_creation" />
                        <input type="hidden" name="index_sql" value={index.createSQL} />
                        <button 
                          type="submit" 
                          class="bg-purple-600 hover:bg-purple-700 text-white py-1 px-3 rounded-md text-xs"
                        >
                          Criar Índice
                        </button>
                      </form>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
    
    <div class="grid grid-cols-1 gap-6 mb-8">
      <div class="bg-white rounded-lg shadow-md p-6">
        <h2 class="text-xl font-semibold mb-4">Consultas Lentas</h2>
        
        {slowQueries.length > 0 ? (
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Consulta</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tempo (ms)</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Linhas</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Data</th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                {slowQueries.map((query) => (
                  <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      <div class="max-w-md truncate">{query.query}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {query.executionTime.toFixed(2)}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {query.rowCount}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {formatDate(new Date(query.timestamp))}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <p class="text-gray-500">Nenhuma consulta lenta registrada.</p>
        )}
      </div>
    </div>
    
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
      <div class="bg-white rounded-lg shadow-md p-6">
        <h2 class="text-xl font-semibold mb-4">Estatísticas de Consultas</h2>
        
        {queryStats.length > 0 ? (
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Consulta</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tempo Médio (ms)</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Execuções</th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                {queryStats.slice(0, 10).map((stat) => (
                  <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      <div class="max-w-md truncate">{stat.query}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {stat.avgExecutionTime.toFixed(2)}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {stat.executionCount}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <p class="text-gray-500">Nenhuma estatística de consulta disponível.</p>
        )}
      </div>
      
      <div class="bg-white rounded-lg shadow-md p-6">
        <h2 class="text-xl font-semibold mb-4">Criar Índice</h2>
        
        <form method="POST" class="space-y-4">
          <input type="hidden" name="action" value="create_index" />
          
          <div>
            <label for="table_name" class="block text-sm font-medium text-gray-700 mb-1">Nome da Tabela</label>
            <input 
              type="text" 
              id="table_name" 
              name="table_name" 
              class="w-full border border-gray-300 rounded-md px-3 py-2"
              required
            />
          </div>
          
          <div>
            <label for="column_names" class="block text-sm font-medium text-gray-700 mb-1">Colunas (separadas por vírgula)</label>
            <input 
              type="text" 
              id="column_names" 
              name="column_names" 
              class="w-full border border-gray-300 rounded-md px-3 py-2"
              required
            />
          </div>
          
          <div>
            <label for="index_name" class="block text-sm font-medium text-gray-700 mb-1">Nome do Índice (opcional)</label>
            <input 
              type="text" 
              id="index_name" 
              name="index_name" 
              class="w-full border border-gray-300 rounded-md px-3 py-2"
            />
          </div>
          
          <div class="flex items-center">
            <input 
              type="checkbox" 
              id="is_unique" 
              name="is_unique" 
              value="true" 
              class="h-4 w-4 text-blue-600 border-gray-300 rounded"
            />
            <label for="is_unique" class="ml-2 block text-sm text-gray-700">
              Índice Único
            </label>
          </div>
          
          <button 
            type="submit" 
            class="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md"
          >
            Gerar SQL
          </button>
        </form>
        
        {createIndexSQL && (
          <div class="mt-4 border-t pt-4">
            <h3 class="font-medium text-gray-700 mb-2">SQL para Criação de Índice</h3>
            <pre class="bg-gray-100 p-2 rounded text-sm overflow-x-auto">
              {createIndexSQL}
            </pre>
            
            <form method="POST" class="mt-4">
              <input type="hidden" name="action" value="execute_index_creation" />
              <input type="hidden" name="index_sql" value={createIndexSQL} />
              <button 
                type="submit" 
                class="bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-md"
              >
                Executar SQL
              </button>
            </form>
          </div>
        )}
      </div>
    </div>
    
    <div class="grid grid-cols-1 gap-6">
      <div class="bg-white rounded-lg shadow-md p-6">
        <h2 class="text-xl font-semibold mb-4">Índices Existentes</h2>
        
        {indexInfo.length > 0 ? (
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Nome</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tabela</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Colunas</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tipo</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tamanho</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Scans</th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                {indexInfo.map((index) => (
                  <tr class={index.indexScans === 0 ? 'bg-red-50' : ''}>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {index.indexName}
                      {index.isUnique && <span class="ml-1 text-xs text-blue-600">(único)</span>}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {index.tableName}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {index.columns.join(', ')}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {index.indexType}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {index.indexSize} KB
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {index.indexScans}
                      {index.indexScans === 0 && (
                        <span class="ml-1 text-xs text-red-600">(não utilizado)</span>
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <p class="text-gray-500">Nenhuma informação de índice disponível.</p>
        )}
      </div>
    </div>
  </div>
</AdminLayout>

<script>
  // Atualizar a página a cada 5 minutos para manter os dados atualizados
  setTimeout(() => {
    window.location.reload();
  }, 5 * 60 * 1000);
</script>
