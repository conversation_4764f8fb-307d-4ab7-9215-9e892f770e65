---
import { actions as productActions } from 'astro:actions';
import AdminLayout from '../../../../layouts/AdminLayout.astro';

const query = Astro.url.searchParams.get('query') || undefined;
const products = await productActions.productAction.read({ filter: 'name', name: query });
---
<AdminLayout title="Produtos">
  <div class="container mx-auto p-4">
    <div class="flex justify-between items-center mb-6">
      <h1 class="text-2xl font-bold">Produtos</h1>
      <a href="/admin/register/product/new" class="btn btn-primary">Novo Produto</a>
    </div>
    <!-- Formulário de pesquisa e listagem -->
    <div class="overflow-x-auto">
      <table class="table table-zebra w-full">
        <thead>
          <tr>
            <th>ID</th>
            <th>Nome</th>
            <th>Ações</th>
          </tr>
        </thead>
        <tbody>
          {products.data?.map((product: { ulid_product: string, name: string }) => (
            <tr>
              <td>{product.ulid_product}</td>
              <td>{product.name}</td>
              <td>
                <a href={`/admin/register/product/${product.ulid_product}`} class="btn btn-sm btn-info">Editar</a>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  </div>
</AdminLayout>