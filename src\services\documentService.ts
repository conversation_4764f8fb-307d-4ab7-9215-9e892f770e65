/**
 * Serviço de gerenciamento de documentos
 *
 * Este serviço implementa funcionalidades para armazenamento,
 * recuperação e gerenciamento de documentos PDF no banco de dados.
 */

import { createHash } from 'node:crypto';
import { cacheService } from '@services/cacheService';
import { db } from '@services/dbService';
import { logger } from '@utils/logger';
import { v4 as uuidv4 } from 'uuid';

/**
 * Interface para documento
 */
export interface Document {
  /**
   * ID único do documento
   */
  id: string;

  /**
   * Título do documento
   */
  title: string;

  /**
   * Descrição do documento
   */
  description?: string;

  /**
   * Tipo de documento (MIME type)
   */
  type: string;

  /**
   * Tamanho do documento em bytes
   */
  size: number;

  /**
   * Hash do conteúdo (SHA-256)
   */
  hash: string;

  /**
   * Versão do documento
   */
  version: number;

  /**
   * ID do usuário que criou o documento
   */
  createdBy: string;

  /**
   * Data de criação
   */
  createdAt: Date;

  /**
   * ID do usuário que atualizou o documento
   */
  updatedBy?: string;

  /**
   * Data de atualização
   */
  updatedAt?: Date;

  /**
   * Metadados adicionais
   */
  metadata?: Record<string, any>;
}

/**
 * Interface para documento com conteúdo
 */
export interface DocumentWithContent extends Document {
  /**
   * Conteúdo do documento (Buffer)
   */
  content: Buffer;
}

/**
 * Interface para opções de upload
 */
export interface UploadOptions {
  /**
   * Se deve verificar vírus/malware
   * @default true
   */
  scanForMalware?: boolean;

  /**
   * Se deve comprimir o documento
   * @default false
   */
  compress?: boolean;

  /**
   * Se deve criar uma versão em vez de substituir
   * @default true
   */
  createVersion?: boolean;

  /**
   * Metadados adicionais
   */
  metadata?: Record<string, any>;
}

/**
 * Interface para opções de download
 */
export interface DownloadOptions {
  /**
   * Versão específica para download (se não fornecido, usa a mais recente)
   */
  version?: number;

  /**
   * Se deve registrar o download
   * @default true
   */
  logDownload?: boolean;

  /**
   * ID do usuário que está fazendo o download
   */
  userId: string;
}

/**
 * Interface para resultado de verificação de acesso
 */
export interface AccessCheckResult {
  /**
   * Se o acesso é permitido
   */
  allowed: boolean;

  /**
   * Motivo da negação (se aplicável)
   */
  reason?: string;
}

/**
 * Prefixo para chaves de cache
 */
const CACHE_PREFIX = 'document:';

/**
 * Tempo de cache para documentos (em segundos)
 */
const CACHE_TTL = 3600; // 1 hora

/**
 * Serviço de documentos
 */
export const documentService = {
  /**
   * Cria um novo documento no banco de dados
   * @param title - Título do documento
   * @param content - Conteúdo do documento (Buffer)
   * @param type - Tipo MIME do documento
   * @param userId - ID do usuário que está criando o documento
   * @param description - Descrição opcional
   * @param options - Opções adicionais
   * @returns Documento criado
   */
  async createDocument(
    title: string,
    content: Buffer,
    type: string,
    userId: string,
    description?: string,
    options: UploadOptions = {}
  ): Promise<Document> {
    try {
      // Valores padrão para opções
      const { scanForMalware = true, compress = false, metadata = {} } = options;

      // Verificar tipo de documento
      if (!type.startsWith('application/pdf')) {
        throw new Error('Tipo de documento não suportado. Apenas PDFs são permitidos.');
      }

      // Verificar tamanho (limite de 10MB)
      if (content.length > 10 * 1024 * 1024) {
        throw new Error('Documento muito grande. O tamanho máximo é 10MB.');
      }

      // Verificar malware se necessário
      if (scanForMalware) {
        const isSafe = await this.scanForMalware(content);

        if (!isSafe) {
          throw new Error('O documento contém conteúdo potencialmente malicioso.');
        }
      }

      // Comprimir se necessário
      let finalContent = content;
      if (compress) {
        finalContent = await this.compressDocument(content);
      }

      // Gerar hash do conteúdo
      const hash = this.generateHash(finalContent);

      // Gerar ID único
      const id = uuidv4();

      // Criar documento no banco de dados
      const result = await db.query(
        `INSERT INTO documents (
          id, title, description, type, size, hash, version,
          content, created_by, created_at, metadata
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, NOW(), $10)
        RETURNING id, title, description, type, size, hash, version,
                  created_by, created_at, metadata`,
        [
          id,
          title,
          description || null,
          type,
          finalContent.length,
          hash,
          1, // Versão inicial
          finalContent,
          userId,
          JSON.stringify(metadata),
        ]
      );

      // Retornar documento criado
      const document = this.mapDbRowToDocument(result.rows[0]);

      // Armazenar em cache (sem conteúdo)
      await this.cacheDocument(document);

      return document;
    } catch (error) {
      logger.error('Erro ao criar documento:', error);
      throw error;
    }
  },

  /**
   * Atualiza um documento existente
   * @param id - ID do documento
   * @param content - Novo conteúdo
   * @param userId - ID do usuário que está atualizando
   * @param title - Novo título (opcional)
   * @param description - Nova descrição (opcional)
   * @param options - Opções adicionais
   * @returns Documento atualizado
   */
  async updateDocument(
    id: string,
    content: Buffer,
    userId: string,
    title?: string,
    description?: string,
    options: UploadOptions = {}
  ): Promise<Document> {
    try {
      // Valores padrão para opções
      const {
        scanForMalware = true,
        compress = false,
        createVersion = true,
        metadata = {},
      } = options;

      // Verificar se o documento existe
      const existingDoc = await this.getDocumentById(id);

      if (!existingDoc) {
        throw new Error('Documento não encontrado.');
      }

      // Verificar permissão
      const hasAccess = await this.checkWriteAccess(id, userId);

      if (!hasAccess.allowed) {
        throw new Error(`Acesso negado: ${hasAccess.reason}`);
      }

      // Verificar malware se necessário
      if (scanForMalware) {
        const isSafe = await this.scanForMalware(content);

        if (!isSafe) {
          throw new Error('O documento contém conteúdo potencialmente malicioso.');
        }
      }

      // Comprimir se necessário
      let finalContent = content;
      if (compress) {
        finalContent = await this.compressDocument(content);
      }

      // Gerar hash do conteúdo
      const hash = this.generateHash(finalContent);

      let result: any;

      if (createVersion) {
        // Criar nova versão
        const newVersion = existingDoc.version + 1;

        result = await db.query(
          `INSERT INTO document_versions (
            document_id, version, content, size, hash,
            created_by, created_at, metadata
          ) VALUES ($1, $2, $3, $4, $5, $6, NOW(), $7)
          RETURNING document_id as id`,
          [
            id,
            newVersion,
            finalContent,
            finalContent.length,
            hash,
            userId,
            JSON.stringify(metadata),
          ]
        );

        // Atualizar documento principal
        await db.query(
          `UPDATE documents SET
            title = COALESCE($1, title),
            description = COALESCE($2, description),
            version = $3,
            size = $4,
            hash = $5,
            updated_by = $6,
            updated_at = NOW(),
            metadata = COALESCE($7, metadata)
          WHERE id = $8`,
          [
            title || null,
            description || null,
            newVersion,
            finalContent.length,
            hash,
            userId,
            Object.keys(metadata).length > 0 ? JSON.stringify(metadata) : null,
            id,
          ]
        );
      } else {
        // Atualizar documento diretamente
        result = await db.query(
          `UPDATE documents SET
            title = COALESCE($1, title),
            description = COALESCE($2, description),
            content = $3,
            size = $4,
            hash = $5,
            updated_by = $6,
            updated_at = NOW(),
            metadata = COALESCE($7, metadata)
          WHERE id = $8
          RETURNING id, title, description, type, size, hash, version,
                    created_by, created_at, updated_by, updated_at, metadata`,
          [
            title || null,
            description || null,
            finalContent,
            finalContent.length,
            hash,
            userId,
            Object.keys(metadata).length > 0 ? JSON.stringify(metadata) : null,
            id,
          ]
        );
      }

      // Obter documento atualizado
      const updatedDoc = await this.getDocumentById(id);

      if (!updatedDoc) {
        throw new Error('Erro ao recuperar documento atualizado.');
      }

      // Invalidar cache
      await this.invalidateDocumentCache(id);

      return updatedDoc;
    } catch (error) {
      logger.error('Erro ao atualizar documento:', error);
      throw error;
    }
  },

  /**
   * Obtém um documento pelo ID
   * @param id - ID do documento
   * @param includeContent - Se deve incluir o conteúdo
   * @returns Documento ou null se não encontrado
   */
  async getDocumentById(
    id: string,
    includeContent = false
  ): Promise<Document | DocumentWithContent | null> {
    try {
      // Tentar obter do cache (se não precisar do conteúdo)
      if (!includeContent) {
        const cachedDoc = await this.getCachedDocument(id);

        if (cachedDoc) {
          return cachedDoc;
        }
      }

      // Construir consulta
      const contentField = includeContent ? ', content' : '';

      // Buscar do banco de dados
      const result = await db.query(
        `SELECT id, title, description, type, size, hash, version,
                created_by, created_at, updated_by, updated_at, metadata${contentField}
         FROM documents
         WHERE id = $1`,
        [id]
      );

      if (result.rows.length === 0) {
        return null;
      }

      // Mapear resultado
      const document = this.mapDbRowToDocument(result.rows[0]);

      // Armazenar em cache (se não incluir conteúdo)
      if (!includeContent) {
        await this.cacheDocument(document);
      }

      return document;
    } catch (error) {
      logger.error('Erro ao obter documento por ID:', error);
      return null;
    }
  },

  /**
   * Obtém o conteúdo de um documento
   * @param id - ID do documento
   * @param options - Opções de download
   * @returns Buffer com o conteúdo ou null se não encontrado
   */
  async getDocumentContent(id: string, options: DownloadOptions): Promise<Buffer | null> {
    try {
      const { version, logDownload = true, userId } = options;

      // Verificar permissão
      const hasAccess = await this.checkReadAccess(id, userId);

      if (!hasAccess.allowed) {
        logger.warn(`Acesso negado ao documento ${id} para usuário ${userId}: ${hasAccess.reason}`);
        return null;
      }

      let content: Buffer | null = null;

      if (version) {
        // Buscar versão específica
        const result = await db.query(
          `SELECT content FROM document_versions
           WHERE document_id = $1 AND version = $2`,
          [id, version]
        );

        if (result.rows.length > 0) {
          content = result.rows[0].content;
        }
      } else {
        // Buscar versão mais recente
        const result = await db.query('SELECT content FROM documents WHERE id = $1', [id]);

        if (result.rows.length > 0) {
          content = result.rows[0].content;
        }
      }

      // Registrar download se necessário
      if (content && logDownload) {
        await this.logDocumentAccess(id, userId, 'download');
      }

      return content;
    } catch (error) {
      logger.error('Erro ao obter conteúdo do documento:', error);
      return null;
    }
  },

  /**
   * Exclui um documento
   * @param id - ID do documento
   * @param userId - ID do usuário que está excluindo
   * @returns Verdadeiro se excluído com sucesso
   */
  async deleteDocument(id: string, userId: string): Promise<boolean> {
    try {
      // Verificar permissão
      const hasAccess = await this.checkWriteAccess(id, userId);

      if (!hasAccess.allowed) {
        throw new Error(`Acesso negado: ${hasAccess.reason}`);
      }

      // Excluir documento
      const result = await db.query('DELETE FROM documents WHERE id = $1 RETURNING id', [id]);

      const success = result.rows.length > 0;

      // Invalidar cache
      if (success) {
        await this.invalidateDocumentCache(id);
      }

      return success;
    } catch (error) {
      logger.error('Erro ao excluir documento:', error);
      return false;
    }
  },

  /**
   * Verifica se um usuário tem permissão de leitura
   * @param documentId - ID do documento
   * @param userId - ID do usuário
   * @returns Resultado da verificação
   */
  async checkReadAccess(documentId: string, userId: string): Promise<AccessCheckResult> {
    try {
      // Verificar se o documento existe
      const document = await this.getDocumentById(documentId);

      if (!document) {
        return { allowed: false, reason: 'Documento não encontrado' };
      }

      // Verificar se o usuário é o criador
      if (document.createdBy === userId) {
        return { allowed: true };
      }

      // Verificar permissões específicas no banco de dados
      const result = await db.query(
        `SELECT permission_type FROM document_permissions
         WHERE document_id = $1 AND user_id = $2
         AND (permission_type = 'read' OR permission_type = 'write')`,
        [documentId, userId]
      );

      if (result.rows.length > 0) {
        return { allowed: true };
      }

      // Verificar permissões de grupo
      const groupResult = await db.query(
        `SELECT dp.permission_type FROM document_permissions dp
         JOIN user_groups ug ON dp.group_id = ug.group_id
         WHERE dp.document_id = $1 AND ug.user_id = $2
         AND (dp.permission_type = 'read' OR dp.permission_type = 'write')`,
        [documentId, userId]
      );

      if (groupResult.rows.length > 0) {
        return { allowed: true };
      }

      return { allowed: false, reason: 'Usuário não tem permissão de leitura' };
    } catch (error) {
      logger.error('Erro ao verificar permissão de leitura:', error);
      return { allowed: false, reason: 'Erro ao verificar permissão' };
    }
  },

  /**
   * Verifica se um usuário tem permissão de escrita
   * @param documentId - ID do documento
   * @param userId - ID do usuário
   * @returns Resultado da verificação
   */
  async checkWriteAccess(documentId: string, userId: string): Promise<AccessCheckResult> {
    try {
      // Verificar se o documento existe
      const document = await this.getDocumentById(documentId);

      if (!document) {
        return { allowed: false, reason: 'Documento não encontrado' };
      }

      // Verificar se o usuário é o criador
      if (document.createdBy === userId) {
        return { allowed: true };
      }

      // Verificar permissões específicas no banco de dados
      const result = await db.query(
        `SELECT permission_type FROM document_permissions
         WHERE document_id = $1 AND user_id = $2
         AND permission_type = 'write'`,
        [documentId, userId]
      );

      if (result.rows.length > 0) {
        return { allowed: true };
      }

      // Verificar permissões de grupo
      const groupResult = await db.query(
        `SELECT dp.permission_type FROM document_permissions dp
         JOIN user_groups ug ON dp.group_id = ug.group_id
         WHERE dp.document_id = $1 AND ug.user_id = $2
         AND dp.permission_type = 'write'`,
        [documentId, userId]
      );

      if (groupResult.rows.length > 0) {
        return { allowed: true };
      }

      return { allowed: false, reason: 'Usuário não tem permissão de escrita' };
    } catch (error) {
      logger.error('Erro ao verificar permissão de escrita:', error);
      return { allowed: false, reason: 'Erro ao verificar permissão' };
    }
  },

  /**
   * Registra um acesso ao documento
   * @param documentId - ID do documento
   * @param userId - ID do usuário
   * @param actionType - Tipo de ação (view, download, etc)
   * @returns Verdadeiro se registrado com sucesso
   */
  async logDocumentAccess(
    documentId: string,
    userId: string,
    actionType: string
  ): Promise<boolean> {
    try {
      await db.query(
        `INSERT INTO document_access_logs (
          document_id, user_id, action_type, access_time, ip_address
        ) VALUES ($1, $2, $3, NOW(), $4)`,
        [documentId, userId, actionType, '0.0.0.0'] // IP fictício
      );

      return true;
    } catch (error) {
      logger.error('Erro ao registrar acesso ao documento:', error);
      return false;
    }
  },

  /**
   * Gera um hash para o conteúdo
   * @param content - Conteúdo do documento
   * @returns Hash SHA-256
   */
  generateHash(content: Buffer): string {
    return createHash('sha256').update(content).digest('hex');
  },

  /**
   * Verifica se um documento contém malware
   * @param content - Conteúdo do documento
   * @returns Verdadeiro se o documento é seguro
   */
  async scanForMalware(content: Buffer): Promise<boolean> {
    // Implementação simplificada - em produção, integrar com serviço de antivírus
    // Por enquanto, apenas verifica se o PDF começa com a assinatura correta
    const signature = content.slice(0, 4).toString('hex');
    return signature === '25504446'; // %PDF em hex
  },

  /**
   * Comprime um documento
   * @param content - Conteúdo do documento
   * @returns Conteúdo comprimido
   */
  async compressDocument(content: Buffer): Promise<Buffer> {
    // Implementação simplificada - em produção, usar biblioteca de compressão
    // Por enquanto, apenas retorna o conteúdo original
    return content;
  },

  /**
   * Armazena um documento no cache
   * @param document - Documento a ser armazenado
   * @returns Verdadeiro se armazenado com sucesso
   */
  async cacheDocument(document: Document): Promise<boolean> {
    try {
      const cacheKey = `${CACHE_PREFIX}${document.id}`;

      return await cacheService.setItem(cacheKey, JSON.stringify(document), CACHE_TTL);
    } catch (error) {
      logger.error('Erro ao armazenar documento em cache:', error);
      return false;
    }
  },

  /**
   * Obtém um documento do cache
   * @param id - ID do documento
   * @returns Documento ou null se não encontrado
   */
  async getCachedDocument(id: string): Promise<Document | null> {
    try {
      const cacheKey = `${CACHE_PREFIX}${id}`;

      const cachedData = await cacheService.getItem(cacheKey);

      if (!cachedData) {
        return null;
      }

      return JSON.parse(cachedData);
    } catch (error) {
      logger.error('Erro ao obter documento do cache:', error);
      return null;
    }
  },

  /**
   * Invalida o cache de um documento
   * @param id - ID do documento
   * @returns Verdadeiro se invalidado com sucesso
   */
  async invalidateDocumentCache(id: string): Promise<boolean> {
    try {
      const cacheKey = `${CACHE_PREFIX}${id}`;

      return await cacheService.delItem(cacheKey);
    } catch (error) {
      logger.error('Erro ao invalidar cache do documento:', error);
      return false;
    }
  },

  /**
   * Mapeia uma linha do banco de dados para um objeto Document
   * @param row - Linha do banco de dados
   * @returns Objeto Document
   */
  mapDbRowToDocument(row: any): Document | DocumentWithContent {
    const document: any = {
      id: row.id,
      title: row.title,
      description: row.description,
      type: row.type,
      size: Number.parseInt(row.size, 10),
      hash: row.hash,
      version: Number.parseInt(row.version, 10),
      createdBy: row.created_by,
      createdAt: new Date(row.created_at),
      metadata: row.metadata ? JSON.parse(row.metadata) : {},
    };

    if (row.updated_by) {
      document.updatedBy = row.updated_by;
    }

    if (row.updated_at) {
      document.updatedAt = new Date(row.updated_at);
    }

    if (row.content) {
      document.content = row.content;
    }

    return document;
  },
};
