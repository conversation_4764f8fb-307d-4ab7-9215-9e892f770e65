---
/**
 * Página de criação de novo papel (role)
 *
 * Esta página permite criar um novo papel no sistema.
 */

import Notification from '@components/admin/Notification.astro';
import PermissionGate from '@components/auth/PermissionGate.astro';
// Importações
import MainLayout from '@layouts/MainLayout.astro';
import { roleRepository } from '@repository/roleRepository';
import { getCurrentUser } from '@utils/authUtils';

// Verificar autenticação
const user = await getCurrentUser(Astro.cookies);

// Redirecionar se não estiver autenticado
if (!user) {
  return Astro.redirect('/signin?redirect=/admin/roles/new');
}

// Título da página
const title = 'Criar Novo Papel';

// Variáveis para armazenar mensagens de erro
let errorMessage = '';

// Processar formulário
if (Astro.request.method === 'POST') {
  try {
    const formData = await Astro.request.formData();

    // Obter dados do formulário
    const name = formData.get('name') as string;
    const description = formData.get('description') as string;

    // Validar dados
    if (!name) {
      errorMessage = 'O nome do papel é obrigatório.';
    } else {
      // Verificar se já existe um papel com este nome
      const existingRole = await roleRepository.read(undefined, name);

      if (existingRole.rowCount > 0) {
        errorMessage = 'Já existe um papel com este nome.';
      } else {
        // Criar novo papel
        const result = await roleRepository.create(name, description);
        const roleId = result.rows[0].ulid_role;

        // Redirecionar para a página do papel criado
        return Astro.redirect(`/admin/roles/${roleId}?success=created`);
      }
    }
  } catch (error) {
    console.error('Erro ao criar papel:', error);
    errorMessage = 'Ocorreu um erro ao criar o papel. Tente novamente.';
  }
}
---

<MainLayout title={title}>
  <main class="container mx-auto px-4 py-8">
    <PermissionGate resource="roles" action="create">
      <div class="flex items-center justify-between mb-6">
        <h1 class="text-3xl font-bold">{title}</h1>
        
        <div class="flex space-x-2">
          <a 
            href="/admin/permissions" 
            class="bg-gray-200 text-gray-800 px-4 py-2 rounded hover:bg-gray-300 transition"
          >
            Cancelar
          </a>
        </div>
      </div>
      
      {errorMessage && (
        <div class="mb-6">
          <Notification 
            type="error" 
            title="Erro" 
            message={errorMessage}
          />
        </div>
      )}
      
      <div class="max-w-2xl mx-auto">
        <div class="bg-white rounded-lg shadow p-6">
          <form method="POST" class="space-y-6">
            <div>
              <label for="name" class="block text-sm font-medium text-gray-700 mb-1">Nome do Papel *</label>
              <input 
                type="text" 
                id="name" 
                name="name" 
                placeholder="Ex: editor_conteudo"
                class="w-full px-3 py-2 border border-gray-300 rounded-md"
                required
              >
              <p class="mt-1 text-sm text-gray-500">
                Use um nome único e descritivo, sem espaços (use underscore).
              </p>
            </div>
            
            <div>
              <label for="description" class="block text-sm font-medium text-gray-700 mb-1">Descrição</label>
              <textarea 
                id="description" 
                name="description" 
                placeholder="Ex: Papel para editores de conteúdo educacional"
                class="w-full px-3 py-2 border border-gray-300 rounded-md"
                rows="3"
              ></textarea>
              <p class="mt-1 text-sm text-gray-500">
                Descreva o propósito deste papel e suas responsabilidades.
              </p>
            </div>
            
            <div class="pt-4">
              <button 
                type="submit"
                class="w-full bg-blue-600 text-white px-4 py-3 rounded-md hover:bg-blue-700 transition"
              >
                Criar Papel
              </button>
            </div>
          </form>
        </div>
        
        <div class="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h3 class="text-lg font-medium text-blue-800 mb-2">O que acontece depois?</h3>
          <p class="text-blue-700">
            Após criar o papel, você poderá:
          </p>
          <ul class="mt-2 space-y-1 text-blue-700 list-disc list-inside">
            <li>Atribuir permissões específicas ao papel</li>
            <li>Associar usuários ao papel</li>
            <li>Configurar hierarquia de papéis</li>
          </ul>
        </div>
      </div>
      
      <slot name="fallback">
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mt-6">
          <p>Você não tem permissão para criar novos papéis.</p>
          <p class="mt-2">
            <a href="/admin/permissions" class="text-red-700 font-medium hover:underline">Voltar para gerenciamento de permissões</a>
          </p>
        </div>
      </slot>
    </PermissionGate>
  </main>
</MainLayout>

<script>
  // Script para interatividade no cliente
  document.addEventListener('DOMContentLoaded', () => {
    // Formatar nome do papel (remover espaços, converter para minúsculas)
    const nameInput = document.getElementById('name') as HTMLInputElement;
    if (nameInput) {
      nameInput.addEventListener('input', () => {
        nameInput.value = nameInput.value
          .toLowerCase()
          .replace(/\s+/g, '_')
          .replace(/[^a-z0-9_]/g, '');
      });
    }
  });
</script>

<style>
  /* Estilos específicos da página */
</style>
