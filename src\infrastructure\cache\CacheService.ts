/**
 * Serviço de cache
 *
 * Este serviço implementa um sistema de cache para dados,
 * utilizando o Valkey (Redis) para armazenamento.
 */

import { createClient } from '@valkey/client';
import { ConsoleLogger } from '../logging/ConsoleLogger';
import { LogLevel } from '../../application/interfaces/services/Logger';

// Inicializar logger
const logger = new ConsoleLogger('CacheService', {
  level: LogLevel.INFO,
  useColors: true,
  format: 'text'
});

// Configuração do cache
const cacheConfig = {
  url: process.env.REDIS_URL || 'valkey://localhost:6379',
  ttl: parseInt(process.env.CACHE_TTL || '3600', 10), // Tempo de vida padrão em segundos (1 hora)
  prefix: process.env.CACHE_PREFIX || 'estacao:'
};

// Variável para controlar se o cache está disponível
let cacheAvailable = false;
let client: any = null;

// Tentar criar e conectar ao cliente Valkey
try {
  client = createClient({
    url: cacheConfig.url
  });

  // Conectar ao Valkey
  client.connect()
    .then(() => {
      cacheAvailable = true;
      logger.info('Conectado ao Valkey com sucesso');
    })
    .catch((err: Error) => {
      cacheAvailable = false;
      logger.warn('Não foi possível conectar ao Valkey. Cache desabilitado.', err);
    });
} catch (err) {
  cacheAvailable = false;
  logger.warn('Não foi possível criar cliente Valkey. Cache desabilitado.', err as Error);
}

/**
 * Obtém um valor do cache
 *
 * @param key Chave do cache
 * @returns Valor armazenado ou null se não encontrado
 */
export async function getCacheValue<T>(key: string): Promise<T | null> {
  // Se o cache não estiver disponível, retornar null
  if (!cacheAvailable || !client) {
    return null;
  }

  try {
    // Adicionar prefixo à chave
    const prefixedKey = `${cacheConfig.prefix}${key}`;

    // Obter valor do cache
    const value = await client.get(prefixedKey);

    // Se não houver valor, retornar null
    if (!value) {
      return null;
    }

    // Tentar converter para objeto
    try {
      return JSON.parse(value) as T;
    } catch {
      // Se não for um JSON válido, retornar como string
      return value as unknown as T;
    }
  } catch (error) {
    // Registrar erro
    logger.error(`Erro ao obter valor do cache para chave ${key}`, error as Error);

    // Em caso de erro, retornar null
    return null;
  }
}

/**
 * Define um valor no cache
 *
 * @param key Chave do cache
 * @param value Valor a ser armazenado
 * @param ttl Tempo de vida em segundos (opcional)
 * @returns Verdadeiro se o valor foi armazenado com sucesso
 */
export async function setCacheValue<T>(key: string, value: T, ttl?: number): Promise<boolean> {
  // Se o cache não estiver disponível, retornar false
  if (!cacheAvailable || !client) {
    return false;
  }

  try {
    // Adicionar prefixo à chave
    const prefixedKey = `${cacheConfig.prefix}${key}`;

    // Converter valor para string
    const stringValue = typeof value === 'string' ? value : JSON.stringify(value);

    // Definir tempo de vida
    const expiration = ttl || cacheConfig.ttl;

    // Armazenar valor no cache
    await client.set(prefixedKey, stringValue, { EX: expiration });

    return true;
  } catch (error) {
    // Registrar erro
    logger.error(`Erro ao definir valor no cache para chave ${key}`, error as Error);

    // Em caso de erro, retornar false
    return false;
  }
}

/**
 * Remove um valor do cache
 *
 * @param key Chave do cache
 * @returns Verdadeiro se o valor foi removido com sucesso
 */
export async function removeCacheValue(key: string): Promise<boolean> {
  // Se o cache não estiver disponível, retornar false
  if (!cacheAvailable || !client) {
    return false;
  }

  try {
    // Adicionar prefixo à chave
    const prefixedKey = `${cacheConfig.prefix}${key}`;

    // Remover valor do cache
    await client.del(prefixedKey);

    return true;
  } catch (error) {
    // Registrar erro
    logger.error(`Erro ao remover valor do cache para chave ${key}`, error as Error);

    // Em caso de erro, retornar false
    return false;
  }
}

/**
 * Limpa todo o cache com o prefixo configurado
 *
 * @returns Verdadeiro se o cache foi limpo com sucesso
 */
export async function clearCache(): Promise<boolean> {
  // Se o cache não estiver disponível, retornar false
  if (!cacheAvailable || !client) {
    return false;
  }

  try {
    // Obter todas as chaves com o prefixo
    const keys = await client.keys(`${cacheConfig.prefix}*`);

    // Se não houver chaves, retornar true
    if (keys.length === 0) {
      return true;
    }

    // Remover todas as chaves
    await client.del(keys);

    return true;
  } catch (error) {
    // Registrar erro
    logger.error('Erro ao limpar cache', error as Error);

    // Em caso de erro, retornar false
    return false;
  }
}

/**
 * Verifica se o serviço de cache está funcionando
 *
 * @returns Verdadeiro se o serviço estiver funcionando
 */
export async function verifyCacheService(): Promise<boolean> {
  // Se o cache não estiver disponível, retornar false
  if (!cacheAvailable || !client) {
    return false;
  }

  try {
    // Tentar definir um valor de teste
    await client.set('test', 'test');

    // Tentar obter o valor de teste
    const value = await client.get('test');

    // Verificar se o valor obtido é igual ao valor definido
    return value === 'test';
  } catch (error) {
    // Registrar erro
    logger.error('Erro ao verificar serviço de cache', error as Error);

    // Em caso de erro, retornar false
    return false;
  }
}
