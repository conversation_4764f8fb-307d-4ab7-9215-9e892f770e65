/**
 * Serviço de gerenciamento de offsets
 *
 * Este serviço fornece funcionalidades para gerenciamento de offsets do Kafka,
 * incluindo estratégias de commit, monitoramento e recuperação.
 */

import pkg from 'kafkajs';
const { Admin, Consumer, KafkaMessage } = pkg;
import type { EachMessagePayload } from 'kafkajs';
import { logger } from '@utils/logger';
import { cacheService } from '@services/cacheService';
import { queryHelper } from '@db/queryHelper';
import kafka from '@config/kafka';
import { v4 as uuidv4 } from 'uuid';

/**
 * Estratégia de commit de offsets
 */
export enum CommitStrategy {
  /**
   * Commit automático pelo Kafka
   */
  AUTO = 'auto',

  /**
   * Commit manual após cada mensagem
   */
  MANUAL_IMMEDIATE = 'manual_immediate',

  /**
   * Commit manual em lotes
   */
  MANUAL_BATCH = 'manual_batch',

  /**
   * Commit manual após processamento bem-sucedido
   */
  MANUAL_SUCCESS = 'manual_success',
}

/**
 * Configuração de gerenciamento de offsets
 */
export interface OffsetManagementConfig {
  /**
   * Estratégia de commit
   */
  commitStrategy: CommitStrategy;

  /**
   * Intervalo de commit em milissegundos (para MANUAL_BATCH)
   */
  commitInterval?: number;

  /**
   * Tamanho do lote para commit (para MANUAL_BATCH)
   */
  commitBatchSize?: number;

  /**
   * Se deve persistir offsets no banco de dados
   */
  persistOffsets?: boolean;

  /**
   * Intervalo de persistência em milissegundos
   */
  persistInterval?: number;

  /**
   * Se deve monitorar consumer lag
   */
  monitorLag?: boolean;

  /**
   * Intervalo de monitoramento em milissegundos
   */
  monitorInterval?: number;

  /**
   * Limiar de lag para alertas
   */
  lagAlertThreshold?: number;
}

/**
 * Informações de offset
 */
export interface OffsetInfo {
  /**
   * Tópico
   */
  topic: string;

  /**
   * Partição
   */
  partition: number;

  /**
   * Offset
   */
  offset: string;

  /**
   * ID do grupo de consumidores
   */
  groupId: string;

  /**
   * Timestamp do commit
   */
  timestamp: number;

  /**
   * Metadados adicionais
   */
  metadata?: Record<string, unknown>;
}

/**
 * Informações de lag de consumidor
 */
export interface ConsumerLagInfo {
  /**
   * Tópico
   */
  topic: string;

  /**
   * Partição
   */
  partition: number;

  /**
   * Offset atual do consumidor
   */
  consumerOffset: string;

  /**
   * Offset mais recente do tópico
   */
  latestOffset: string;

  /**
   * Lag (diferença entre latestOffset e consumerOffset)
   */
  lag: number;

  /**
   * ID do grupo de consumidores
   */
  groupId: string;

  /**
   * Timestamp da medição
   */
  timestamp: number;
}

/**
 * Serviço de gerenciamento de offsets
 */
export const offsetManagementService = {
  /**
   * Configuração padrão
   */
  defaultConfig: {
    commitStrategy: CommitStrategy.MANUAL_SUCCESS,
    commitInterval: 5000, // 5 segundos
    commitBatchSize: 100, // 100 mensagens
    persistOffsets: true,
    persistInterval: 60000, // 1 minuto
    monitorLag: true,
    monitorInterval: 30000, // 30 segundos
    lagAlertThreshold: 1000, // 1000 mensagens
  } as OffsetManagementConfig,

  /**
   * Instância do admin Kafka
   */
  admin: kafka.admin(),

  /**
   * Lotes de offsets pendentes por grupo de consumidores
   */
  pendingOffsets: new Map<string, Map<string, Map<number, string>>>(),

  /**
   * Contadores de mensagens por grupo de consumidores
   */
  messageCounters: new Map<string, number>(),

  /**
   * Timers de commit por grupo de consumidores
   */
  commitTimers: new Map<string, NodeJS.Timeout>(),

  /**
   * Timers de persistência por grupo de consumidores
   */
  persistTimers: new Map<string, NodeJS.Timeout>(),

  /**
   * Timers de monitoramento por grupo de consumidores
   */
  monitorTimers: new Map<string, NodeJS.Timeout>(),

  /**
   * Inicializa o serviço de gerenciamento de offsets
   * @param consumer - Instância do consumidor Kafka
   * @param groupId - ID do grupo de consumidores
   * @param config - Configuração de gerenciamento de offsets
   */
  async init(
    consumer: Consumer,
    groupId: string,
    config: Partial<OffsetManagementConfig> = {}
  ): Promise<void> {
    try {
      // Mesclar configuração com padrões
      const mergedConfig = {
        ...this.defaultConfig,
        ...config,
      };

      // Inicializar admin se necessário
      if (!this.admin.isConnected()) {
        await this.admin.connect();
      }

      // Inicializar estruturas de dados para o grupo
      this.pendingOffsets.set(groupId, new Map());
      this.messageCounters.set(groupId, 0);

      // Configurar commit em lotes se necessário
      if (mergedConfig.commitStrategy === CommitStrategy.MANUAL_BATCH) {
        this.setupBatchCommit(consumer, groupId, mergedConfig);
      }

      // Configurar persistência de offsets se necessário
      if (mergedConfig.persistOffsets) {
        this.setupOffsetPersistence(consumer, groupId, mergedConfig);
      }

      // Configurar monitoramento de lag se necessário
      if (mergedConfig.monitorLag) {
        this.setupLagMonitoring(consumer, groupId, mergedConfig);
      }

      logger.info(`Serviço de gerenciamento de offsets inicializado para grupo ${groupId}`, {
        commitStrategy: mergedConfig.commitStrategy,
        persistOffsets: mergedConfig.persistOffsets,
        monitorLag: mergedConfig.monitorLag,
      });
    } catch (error) {
      logger.error(`Erro ao inicializar serviço de gerenciamento de offsets para grupo ${groupId}:`, error);
      throw error;
    }
  },

  /**
   * Processa uma mensagem e gerencia seu offset
   * @param payload - Payload da mensagem
   * @param consumer - Instância do consumidor Kafka
   * @param groupId - ID do grupo de consumidores
   * @param config - Configuração de gerenciamento de offsets
   * @param processor - Função de processamento da mensagem
   */
  async processMessage(
    payload: EachMessagePayload,
    consumer: Consumer,
    groupId: string,
    config: OffsetManagementConfig,
    processor: (message: KafkaMessage, topic: string, partition: number) => Promise<boolean>
  ): Promise<void> {
    const { topic, partition, message } = payload;

    try {
      // Incrementar contador de mensagens
      const counter = (this.messageCounters.get(groupId) || 0) + 1;
      this.messageCounters.set(groupId, counter);

      // Processar mensagem
      const success = await processor(message, topic, partition);

      // Gerenciar offset com base na estratégia de commit
      switch (config.commitStrategy) {
        case CommitStrategy.MANUAL_IMMEDIATE:
          // Commit imediato após processamento
          await consumer.commitOffsets([
            {
              topic,
              partition,
              offset: (BigInt(message.offset) + BigInt(1)).toString(),
            },
          ]);
          break;

        case CommitStrategy.MANUAL_SUCCESS:
          // Commit apenas se o processamento for bem-sucedido
          if (success) {
            await consumer.commitOffsets([
              {
                topic,
                partition,
                offset: (BigInt(message.offset) + BigInt(1)).toString(),
              },
            ]);
          }
          break;

        case CommitStrategy.MANUAL_BATCH:
          // Adicionar offset ao lote pendente
          const topicOffsets = this.pendingOffsets.get(groupId)?.get(topic) || new Map();
          topicOffsets.set(partition, (BigInt(message.offset) + BigInt(1)).toString());

          const groupOffsets = this.pendingOffsets.get(groupId) || new Map();
          groupOffsets.set(topic, topicOffsets);
          this.pendingOffsets.set(groupId, groupOffsets);

          // Commit se atingir o tamanho do lote
          if (counter >= (config.commitBatchSize || 100)) {
            await this.commitPendingOffsets(consumer, groupId);
            this.messageCounters.set(groupId, 0);
          }
          break;

        case CommitStrategy.AUTO:
          // Não fazer nada, o Kafka gerencia automaticamente
          break;
      }

      // Registrar offset processado para persistência
      if (config.persistOffsets) {
        await this.recordProcessedOffset(topic, partition, message.offset, groupId);
      }
    } catch (error) {
      logger.error(`Erro ao processar mensagem do tópico ${topic}:`, error);
      throw error;
    }
  },

  /**
   * Configura commit em lotes
   * @param consumer - Instância do consumidor Kafka
   * @param groupId - ID do grupo de consumidores
   * @param config - Configuração de gerenciamento de offsets
   */
  setupBatchCommit(
    consumer: Consumer,
    groupId: string,
    config: OffsetManagementConfig
  ): void {
    // Limpar timer existente
    if (this.commitTimers.has(groupId)) {
      clearInterval(this.commitTimers.get(groupId));
    }

    // Configurar novo timer
    const timer = setInterval(async () => {
      try {
        await this.commitPendingOffsets(consumer, groupId);
      } catch (error) {
        logger.error(`Erro ao commit de offsets em lote para grupo ${groupId}:`, error);
      }
    }, config.commitInterval || 5000);

    this.commitTimers.set(groupId, timer);
  },

  /**
   * Configura persistência de offsets
   * @param consumer - Instância do consumidor Kafka
   * @param groupId - ID do grupo de consumidores
   * @param config - Configuração de gerenciamento de offsets
   */
  setupOffsetPersistence(
    consumer: Consumer,
    groupId: string,
    config: OffsetManagementConfig
  ): void {
    // Limpar timer existente
    if (this.persistTimers.has(groupId)) {
      clearInterval(this.persistTimers.get(groupId));
    }

    // Configurar novo timer
    const timer = setInterval(async () => {
      try {
        await this.persistOffsets(groupId);
      } catch (error) {
        logger.error(`Erro ao persistir offsets para grupo ${groupId}:`, error);
      }
    }, config.persistInterval || 60000);

    this.persistTimers.set(groupId, timer);
  },

  /**
   * Configura monitoramento de lag
   * @param consumer - Instância do consumidor Kafka
   * @param groupId - ID do grupo de consumidores
   * @param config - Configuração de gerenciamento de offsets
   */
  setupLagMonitoring(
    consumer: Consumer,
    groupId: string,
    config: OffsetManagementConfig
  ): void {
    // Limpar timer existente
    if (this.monitorTimers.has(groupId)) {
      clearInterval(this.monitorTimers.get(groupId));
    }

    // Configurar novo timer
    const timer = setInterval(async () => {
      try {
        await this.monitorConsumerLag(consumer, groupId, config);
      } catch (error) {
        logger.error(`Erro ao monitorar lag para grupo ${groupId}:`, error);
      }
    }, config.monitorInterval || 30000);

    this.monitorTimers.set(groupId, timer);
  },

  /**
   * Commit de offsets pendentes
   * @param consumer - Instância do consumidor Kafka
   * @param groupId - ID do grupo de consumidores
   */
  async commitPendingOffsets(
    consumer: Consumer,
    groupId: string
  ): Promise<void> {
    const groupOffsets = this.pendingOffsets.get(groupId);

    if (!groupOffsets || groupOffsets.size === 0) {
      return;
    }

    const offsetsToCommit = [];

    for (const [topic, partitionOffsets] of groupOffsets.entries()) {
      for (const [partition, offset] of partitionOffsets.entries()) {
        offsetsToCommit.push({
          topic,
          partition,
          offset,
        });
      }
    }

    if (offsetsToCommit.length > 0) {
      await consumer.commitOffsets(offsetsToCommit);
      logger.debug(`Commit de ${offsetsToCommit.length} offsets para grupo ${groupId}`);

      // Limpar offsets commitados
      this.pendingOffsets.set(groupId, new Map());
    }
  },

  /**
   * Registra offset processado
   * @param topic - Tópico
   * @param partition - Partição
   * @param offset - Offset
   * @param groupId - ID do grupo de consumidores
   */
  async recordProcessedOffset(
    topic: string,
    partition: number,
    offset: string,
    groupId: string
  ): Promise<void> {
    const offsetInfo: OffsetInfo = {
      topic,
      partition,
      offset,
      groupId,
      timestamp: Date.now(),
    };

    // Armazenar em cache para persistência posterior
    const cacheKey = `offset:${groupId}:${topic}:${partition}`;
    await cacheService.set(cacheKey, JSON.stringify(offsetInfo), 3600);
  },

  /**
   * Persiste offsets no banco de dados
   * @param groupId - ID do grupo de consumidores
   */
  async persistOffsets(groupId: string): Promise<void> {
    try {
      // Obter todos os offsets do cache
      const keys = await cacheService.keys(`offset:${groupId}:*`);

      if (keys.length === 0) {
        return;
      }

      // Preparar valores para inserção em lote
      const values = [];

      for (const key of keys) {
        const offsetInfoJson = await cacheService.get(key);

        if (offsetInfoJson) {
          const offsetInfo = JSON.parse(offsetInfoJson) as OffsetInfo;

          values.push([
            uuidv4(),
            offsetInfo.topic,
            offsetInfo.partition,
            offsetInfo.offset,
            offsetInfo.groupId,
            new Date(offsetInfo.timestamp),
            JSON.stringify(offsetInfo.metadata || {}),
          ]);

          // Remover do cache após processamento
          await cacheService.delete(key);
        }
      }

      if (values.length > 0) {
        // Inserir em lote no banco de dados
        await queryHelper.batchQuery(
          `INSERT INTO tab_consumer_offset (
            offset_id,
            topic,
            partition,
            offset_value,
            group_id,
            timestamp,
            metadata,
            created_at
          ) VALUES (
            $1, $2, $3, $4, $5, $6, $7, NOW()
          )`,
          values
        );

        logger.debug(`Persistidos ${values.length} offsets para grupo ${groupId}`);
      }
    } catch (error) {
      logger.error(`Erro ao persistir offsets para grupo ${groupId}:`, error);
    }
  },

  /**
   * Monitora lag de consumidor
   * @param consumer - Instância do consumidor Kafka
   * @param groupId - ID do grupo de consumidores
   * @param config - Configuração de gerenciamento de offsets
   */
  async monitorConsumerLag(
    consumer: Consumer,
    groupId: string,
    config: OffsetManagementConfig
  ): Promise<void> {
    try {
      // Obter tópicos assinados
      const assignment = await consumer.describeGroup();

      if (!assignment.members || assignment.members.length === 0) {
        return;
      }

      const topics = new Set<string>();

      for (const member of assignment.members) {
        if (member.memberAssignment) {
          const assignment = JSON.parse(member.memberAssignment);

          for (const topic of Object.keys(assignment)) {
            topics.add(topic);
          }
        }
      }

      if (topics.size === 0) {
        return;
      }

      // Obter offsets do grupo
      const consumerOffsets = await this.admin.fetchOffsets({
        groupId,
        topics: Array.from(topics),
      });

      // Obter offsets mais recentes dos tópicos
      const topicOffsets = await this.admin.fetchTopicOffsets(Array.from(topics));

      // Calcular lag
      const lagInfo: ConsumerLagInfo[] = [];

      for (const { topic, partitions } of consumerOffsets) {
        const topicLatestOffsets = topicOffsets.find(t => t.topic === topic);

        if (!topicLatestOffsets) {
          continue;
        }

        for (const { partition, offset } of partitions) {
          const latestOffset = topicLatestOffsets.partitions.find(
            p => p.partition === partition
          )?.offset || '0';

          const consumerOffset = offset;
          const lag = Number(BigInt(latestOffset) - BigInt(consumerOffset));

          lagInfo.push({
            topic,
            partition,
            consumerOffset,
            latestOffset,
            lag,
            groupId,
            timestamp: Date.now(),
          });

          // Verificar se o lag excede o limiar
          if (lag > (config.lagAlertThreshold || 1000)) {
            logger.warn(`Alto lag detectado para ${topic}:${partition} no grupo ${groupId}`, {
              lag,
              consumerOffset,
              latestOffset,
            });
          }
        }
      }

      // Registrar métricas de lag
      await this.recordLagMetrics(lagInfo);

      logger.debug(`Monitoramento de lag concluído para grupo ${groupId}`);
    } catch (error) {
      logger.error(`Erro ao monitorar lag para grupo ${groupId}:`, error);
    }
  },

  /**
   * Registra métricas de lag
   * @param lagInfo - Informações de lag
   */
  async recordLagMetrics(lagInfo: ConsumerLagInfo[]): Promise<void> {
    try {
      // Preparar valores para inserção em lote
      const values = [];

      for (const info of lagInfo) {
        values.push([
          uuidv4(),
          info.topic,
          info.partition,
          info.consumerOffset,
          info.latestOffset,
          info.lag,
          info.groupId,
          new Date(info.timestamp),
        ]);
      }

      if (values.length > 0) {
        // Inserir em lote no banco de dados
        await queryHelper.batchQuery(
          `INSERT INTO tab_consumer_lag (
            lag_id,
            topic,
            partition,
            consumer_offset,
            latest_offset,
            lag_value,
            group_id,
            timestamp,
            created_at
          ) VALUES (
            $1, $2, $3, $4, $5, $6, $7, $8, NOW()
          )`,
          values
        );
      }
    } catch (error) {
      logger.error('Erro ao registrar métricas de lag:', error);
    }
  },
};
