/**
 * Handler para eventos de pagamento
 *
 * Este arquivo contém handlers para processar eventos de pagamento
 * usando o fluxo de processamento de pagamentos.
 */

import { type EventHandler } from '@services/eventConsumerService';
import { type PaymentEvent } from '@services/eventProducerService';
import { type ProcessedPaymentEvent, paymentEventFlow } from '@services/flows/paymentEventFlow';
import { kafkaLoggingService } from '@services/kafka-logging.service';
import { type ProcessingContext } from '@services/messageProcessingService';
import { logger } from '@utils/logger';

/**
 * Handler para eventos de criação de pagamento
 */
export class PaymentCreatedHandler implements EventHandler<PaymentEvent> {
  async handle(event: PaymentEvent, context: ProcessingContext): Promise<void> {
    try {
      logger.info(`Processando evento de criação de pagamento: ${event.paymentId}`);

      // Converter para formato do fluxo de processamento
      const processedEvent: ProcessedPaymentEvent = {
        paymentId: event.paymentId,
        orderId: event.orderId,
        userId: event.userId,
        value: event.value,
        paymentType: event.paymentType,
        status: event.status,
        externalId: event.externalId,
        metadata: {},
        processingTimestamp: new Date().toISOString(),
      };

      // Processar evento usando o fluxo de pagamento
      await paymentEventFlow.processPaymentEvent(processedEvent, context);
    } catch (error) {
      logger.error(`Erro ao processar evento de criação de pagamento ${event.paymentId}:`, error);
      kafkaLoggingService.error(
        'payment.handler',
        `Erro ao processar evento de criação de pagamento ${event.paymentId}`,
        error
      );
    }
  }
}

/**
 * Handler para eventos de atualização de pagamento
 */
export class PaymentUpdatedHandler implements EventHandler<PaymentEvent> {
  async handle(event: PaymentEvent, context: ProcessingContext): Promise<void> {
    try {
      logger.info(`Processando evento de atualização de pagamento: ${event.paymentId}`);

      // Converter para formato do fluxo de processamento
      const processedEvent: ProcessedPaymentEvent = {
        paymentId: event.paymentId,
        orderId: event.orderId,
        userId: event.userId,
        value: event.value,
        paymentType: event.paymentType,
        status: event.status,
        externalId: event.externalId,
        metadata: {},
        processingTimestamp: new Date().toISOString(),
      };

      // Processar evento usando o fluxo de pagamento
      await paymentEventFlow.processPaymentEvent(processedEvent, context);
    } catch (error) {
      logger.error(
        `Erro ao processar evento de atualização de pagamento ${event.paymentId}:`,
        error
      );
      kafkaLoggingService.error(
        'payment.handler',
        `Erro ao processar evento de atualização de pagamento ${event.paymentId}`,
        error
      );
    }
  }
}

/**
 * Handler para eventos de falha de pagamento
 */
export class PaymentFailedHandler implements EventHandler<PaymentEvent> {
  async handle(event: PaymentEvent, context: ProcessingContext): Promise<void> {
    try {
      logger.info(`Processando evento de falha de pagamento: ${event.paymentId}`);

      // Converter para formato do fluxo de processamento
      const processedEvent: ProcessedPaymentEvent = {
        paymentId: event.paymentId,
        orderId: event.orderId,
        userId: event.userId,
        value: event.value,
        paymentType: event.paymentType,
        status: 'failed',
        externalId: event.externalId,
        metadata: {
          failureReason: event.metadata?.failureReason || 'Falha no processamento',
          failureCode: event.metadata?.failureCode,
        },
        processingTimestamp: new Date().toISOString(),
      };

      // Processar evento usando o fluxo de pagamento
      await paymentEventFlow.processPaymentEvent(processedEvent, context);
    } catch (error) {
      logger.error(`Erro ao processar evento de falha de pagamento ${event.paymentId}:`, error);
      kafkaLoggingService.error(
        'payment.handler',
        `Erro ao processar evento de falha de pagamento ${event.paymentId}`,
        error
      );
    }
  }
}

/**
 * Handler para eventos de reembolso solicitado
 */
export class RefundRequestedHandler implements EventHandler<PaymentEvent> {
  async handle(event: PaymentEvent, context: ProcessingContext): Promise<void> {
    try {
      logger.info(`Processando evento de reembolso solicitado: ${event.paymentId}`);

      // Converter para formato do fluxo de processamento
      const processedEvent: ProcessedPaymentEvent = {
        paymentId: event.paymentId,
        orderId: event.orderId,
        userId: event.userId,
        value: event.value,
        paymentType: event.paymentType,
        status: 'refund_requested',
        externalId: event.externalId,
        metadata: {
          refundReason: event.metadata?.refundReason || 'Reembolso solicitado pelo cliente',
          refundAmount: event.metadata?.refundAmount || event.value,
          requestedBy: event.metadata?.requestedBy,
        },
        processingTimestamp: new Date().toISOString(),
      };

      // Processar evento usando o fluxo de pagamento
      await paymentEventFlow.processPaymentEvent(processedEvent, context);
    } catch (error) {
      logger.error(`Erro ao processar evento de reembolso solicitado ${event.paymentId}:`, error);
      kafkaLoggingService.error(
        'payment.handler',
        `Erro ao processar evento de reembolso solicitado ${event.paymentId}`,
        error
      );
    }
  }
}

/**
 * Handler para eventos de reembolso processado
 */
export class RefundProcessedHandler implements EventHandler<PaymentEvent> {
  async handle(event: PaymentEvent, context: ProcessingContext): Promise<void> {
    try {
      logger.info(`Processando evento de reembolso processado: ${event.paymentId}`);

      // Converter para formato do fluxo de processamento
      const processedEvent: ProcessedPaymentEvent = {
        paymentId: event.paymentId,
        orderId: event.orderId,
        userId: event.userId,
        value: event.value,
        paymentType: event.paymentType,
        status: 'refunded',
        externalId: event.externalId,
        metadata: {
          refundReason: event.metadata?.refundReason || 'Reembolso processado',
          refundAmount: event.metadata?.refundAmount || event.value,
          refundId: event.metadata?.refundId,
          refundDate: event.metadata?.refundDate || new Date().toISOString(),
        },
        processingTimestamp: new Date().toISOString(),
      };

      // Processar evento usando o fluxo de pagamento
      await paymentEventFlow.processPaymentEvent(processedEvent, context);
    } catch (error) {
      logger.error(`Erro ao processar evento de reembolso processado ${event.paymentId}:`, error);
      kafkaLoggingService.error(
        'payment.handler',
        `Erro ao processar evento de reembolso processado ${event.paymentId}`,
        error
      );
    }
  }
}

/**
 * Handler para eventos de webhook de pagamento
 */
export class PaymentWebhookHandler implements EventHandler<PaymentEvent> {
  async handle(event: PaymentEvent, context: ProcessingContext): Promise<void> {
    try {
      logger.info(`Processando evento de webhook de pagamento: ${event.paymentId}`);

      // Extrair informações do webhook
      const webhookData = event.metadata?.webhookData || {};
      const webhookEvent = webhookData.event || '';

      // Determinar status com base no evento do webhook
      let status = event.status;
      if (webhookEvent.includes('CONFIRMED') || webhookEvent.includes('APPROVED')) {
        status = 'approved';
      } else if (webhookEvent.includes('PENDING')) {
        status = 'pending';
      } else if (webhookEvent.includes('FAILED') || webhookEvent.includes('DENIED')) {
        status = 'failed';
      } else if (webhookEvent.includes('REFUND')) {
        status = 'refunded';
      }

      // Converter para formato do fluxo de processamento
      const processedEvent: ProcessedPaymentEvent = {
        paymentId: event.paymentId,
        orderId: event.orderId,
        userId: event.userId,
        value: event.value,
        paymentType: event.paymentType,
        status,
        externalId: event.externalId,
        metadata: {
          webhookEvent,
          webhookData,
          provider: webhookData.provider || 'unknown',
          receivedAt: webhookData.receivedAt || new Date().toISOString(),
        },
        processingTimestamp: new Date().toISOString(),
      };

      // Processar evento usando o fluxo de pagamento
      await paymentEventFlow.processPaymentEvent(processedEvent, context);
    } catch (error) {
      logger.error(`Erro ao processar evento de webhook de pagamento ${event.paymentId}:`, error);
      kafkaLoggingService.error(
        'payment.handler',
        `Erro ao processar evento de webhook de pagamento ${event.paymentId}`,
        error
      );
    }
  }
}
