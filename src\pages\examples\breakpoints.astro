---
import DaisyButton from '../../components/ui/DaisyButton.astro';
import DaisyCard from '../../components/ui/DaisyCard.astro';
import Breadcrumbs from '../../components/ui/navigation/Breadcrumbs.astro';
import Tabs from '../../components/ui/navigation/Tabs.astro';
import BaseLayout from '../../layouts/BaseLayout.astro';

// Importar componentes de layout
import { Container, Section } from '../../layouts/grid';

// Importar utilitários responsivos
import { breakpoints } from '../../layouts/responsive';

const title = 'Breakpoints';

// Breadcrumbs para a página atual
const breadcrumbItems = [
  { href: '/', label: 'Início' },
  { href: '/examples', label: 'Exemplos' },
  { label: 'Breakpoints' },
];

// Tabs para as categorias
const breakpointTabs = [
  { id: 'values', label: 'Valores', isActive: true },
  { id: 'media', label: 'Media Queries' },
  { id: 'utils', label: 'Utilitários' },
  { id: 'demo', label: 'Demonstração' },
];

// Exemplos de código
const breakpointsCode = `import { breakpoints } from '../../layouts/responsive';

// Valores de breakpoints
const mdBreakpoint = breakpoints.values.md; // 768px

// Media queries
const tabletQuery = breakpoints.mediaQueries.tablet;
// @media (min-width: 768px) and (max-width: 1023px)

// Verificar breakpoint atual (apenas no cliente)
const isMobile = breakpoints.isMobile();
const currentBreakpoint = breakpoints.getCurrentBreakpoint();`;

const responsiveUtilsCode = `import { 
  getResponsiveValue, 
  getResponsiveClasses 
} from '../../layouts/responsive';

// Obter valor com base no breakpoint
const fontSize = getResponsiveValue({
  xs: '14px',
  md: '16px',
  lg: '18px'
}, '16px');

// Obter classes com base no breakpoint
const classes = getResponsiveClasses({
  base: 'w-full',
  md: 'w-1/2',
  lg: 'w-1/3'
});`;

const mediaQueriesCode = `/* CSS */
.responsive-element {
  width: 100%;
}

@media (min-width: 768px) {
  .responsive-element {
    width: 50%;
  }
}

@media (min-width: 1024px) {
  .responsive-element {
    width: 33.333%;
  }
}

/* Ou com Tailwind */
<div class="w-full md:w-1/2 lg:w-1/3">
  Elemento responsivo
</div>`;

const breakpointListenerCode = `import { onBreakpointChange } from '../../layouts/responsive';

// No cliente
const removeListener = onBreakpointChange((breakpoint) => {
  console.log('Breakpoint atual:', breakpoint);
  
  // Fazer algo com base no breakpoint
  if (breakpoint === 'md') {
    // Lógica para tablet
  } else if (breakpoint === 'lg') {
    // Lógica para desktop
  }
});

// Remover listener quando não for mais necessário
removeListener();`;
---

<BaseLayout title={title}>
  <Container>
    <Breadcrumbs items={breadcrumbItems} class="my-4" />
    
    <Section title={title} subtitle="Sistema de breakpoints para layouts responsivos">
      <div class="mb-8">
        <p>
          O sistema de breakpoints define pontos de quebra para diferentes tamanhos de tela,
          permitindo a criação de layouts responsivos que se adaptam a diferentes dispositivos.
        </p>
        
        <p class="mt-4">
          Os breakpoints são baseados no Tailwind CSS, com algumas adições específicas para o projeto.
        </p>
      </div>
      
      <Tabs tabs={breakpointTabs}>
        <!-- Valores -->
        <div slot="values" class="py-4">
          <h2 class="text-2xl font-bold mb-4">Valores de Breakpoints</h2>
          
          <p class="mb-4">
            Os breakpoints são definidos em pixels e representam a largura mínima da viewport para cada categoria de dispositivo.
          </p>
          
          <div class="overflow-x-auto mb-8">
            <table class="table table-zebra w-full">
              <thead>
                <tr>
                  <th>Nome</th>
                  <th>Valor</th>
                  <th>Descrição</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td><code>xs</code></td>
                  <td>{breakpoints.values.xs}px</td>
                  <td>Dispositivos móveis pequenos</td>
                </tr>
                <tr>
                  <td><code>sm</code></td>
                  <td>{breakpoints.values.sm}px</td>
                  <td>Dispositivos móveis grandes</td>
                </tr>
                <tr>
                  <td><code>md</code></td>
                  <td>{breakpoints.values.md}px</td>
                  <td>Tablets</td>
                </tr>
                <tr>
                  <td><code>lg</code></td>
                  <td>{breakpoints.values.lg}px</td>
                  <td>Desktops</td>
                </tr>
                <tr>
                  <td><code>xl</code></td>
                  <td>{breakpoints.values.xl}px</td>
                  <td>Desktops grandes</td>
                </tr>
                <tr>
                  <td><code>2xl</code></td>
                  <td>{breakpoints.values['2xl']}px</td>
                  <td>Telas muito grandes</td>
                </tr>
              </tbody>
            </table>
          </div>
          
          <h3 class="text-xl font-bold mb-2">Larguras de Container</h3>
          <p class="mb-4">
            Para cada breakpoint, há uma largura máxima recomendada para containers:
          </p>
          
          <div class="overflow-x-auto mb-8">
            <table class="table table-zebra w-full">
              <thead>
                <tr>
                  <th>Breakpoint</th>
                  <th>Largura Máxima</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td><code>xs</code></td>
                  <td>{breakpoints.container.xs}</td>
                </tr>
                <tr>
                  <td><code>sm</code></td>
                  <td>{breakpoints.container.sm}</td>
                </tr>
                <tr>
                  <td><code>md</code></td>
                  <td>{breakpoints.container.md}</td>
                </tr>
                <tr>
                  <td><code>lg</code></td>
                  <td>{breakpoints.container.lg}</td>
                </tr>
                <tr>
                  <td><code>xl</code></td>
                  <td>{breakpoints.container.xl}</td>
                </tr>
                <tr>
                  <td><code>2xl</code></td>
                  <td>{breakpoints.container['2xl']}</td>
                </tr>
              </tbody>
            </table>
          </div>
          
          <div class="bg-base-200 p-6 rounded-box">
            <h3 class="text-xl font-bold mb-4">Exemplo de Uso</h3>
            
            <pre class="bg-base-300 p-4 rounded-box overflow-x-auto text-sm"><code>{breakpointsCode}</code></pre>
          </div>
        </div>
        
        <!-- Media Queries -->
        <div slot="media" class="py-4">
          <h2 class="text-2xl font-bold mb-4">Media Queries</h2>
          
          <p class="mb-4">
            O sistema fornece media queries pré-definidas para diferentes breakpoints e características de dispositivos.
          </p>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            <DaisyCard title="Breakpoints Padrão">
              <div class="space-y-2">
                <div>
                  <div class="text-sm font-medium"><code>xs</code></div>
                  <div class="text-xs opacity-70">{breakpoints.mediaQueries.xs}</div>
                </div>
                <div>
                  <div class="text-sm font-medium"><code>sm</code></div>
                  <div class="text-xs opacity-70">{breakpoints.mediaQueries.sm}</div>
                </div>
                <div>
                  <div class="text-sm font-medium"><code>md</code></div>
                  <div class="text-xs opacity-70">{breakpoints.mediaQueries.md}</div>
                </div>
                <div>
                  <div class="text-sm font-medium"><code>lg</code></div>
                  <div class="text-xs opacity-70">{breakpoints.mediaQueries.lg}</div>
                </div>
                <div>
                  <div class="text-sm font-medium"><code>xl</code></div>
                  <div class="text-xs opacity-70">{breakpoints.mediaQueries.xl}</div>
                </div>
                <div>
                  <div class="text-sm font-medium"><code>2xl</code></div>
                  <div class="text-xs opacity-70">{breakpoints.mediaQueries['2xl']}</div>
                </div>
              </div>
            </DaisyCard>
            
            <DaisyCard title="Dispositivos Específicos">
              <div class="space-y-2">
                <div>
                  <div class="text-sm font-medium"><code>mobile</code></div>
                  <div class="text-xs opacity-70">{breakpoints.mediaQueries.mobile}</div>
                </div>
                <div>
                  <div class="text-sm font-medium"><code>tablet</code></div>
                  <div class="text-xs opacity-70">{breakpoints.mediaQueries.tablet}</div>
                </div>
                <div>
                  <div class="text-sm font-medium"><code>desktop</code></div>
                  <div class="text-xs opacity-70">{breakpoints.mediaQueries.desktop}</div>
                </div>
                <div>
                  <div class="text-sm font-medium"><code>phone</code></div>
                  <div class="text-xs opacity-70">{breakpoints.mediaQueries.phone}</div>
                </div>
                <div>
                  <div class="text-sm font-medium"><code>tabletPortrait</code></div>
                  <div class="text-xs opacity-70">{breakpoints.mediaQueries.tabletPortrait}</div>
                </div>
                <div>
                  <div class="text-sm font-medium"><code>desktopLarge</code></div>
                  <div class="text-xs opacity-70">{breakpoints.mediaQueries.desktopLarge}</div>
                </div>
              </div>
            </DaisyCard>
            
            <DaisyCard title="Orientação e Características">
              <div class="space-y-2">
                <div>
                  <div class="text-sm font-medium"><code>portrait</code></div>
                  <div class="text-xs opacity-70">{breakpoints.mediaQueries.portrait}</div>
                </div>
                <div>
                  <div class="text-sm font-medium"><code>landscape</code></div>
                  <div class="text-xs opacity-70">{breakpoints.mediaQueries.landscape}</div>
                </div>
                <div>
                  <div class="text-sm font-medium"><code>touch</code></div>
                  <div class="text-xs opacity-70">{breakpoints.mediaQueries.touch}</div>
                </div>
                <div>
                  <div class="text-sm font-medium"><code>mouse</code></div>
                  <div class="text-xs opacity-70">{breakpoints.mediaQueries.mouse}</div>
                </div>
              </div>
            </DaisyCard>
            
            <DaisyCard title="Preferências do Usuário">
              <div class="space-y-2">
                <div>
                  <div class="text-sm font-medium"><code>reducedMotion</code></div>
                  <div class="text-xs opacity-70">{breakpoints.mediaQueries.reducedMotion}</div>
                </div>
                <div>
                  <div class="text-sm font-medium"><code>darkMode</code></div>
                  <div class="text-xs opacity-70">{breakpoints.mediaQueries.darkMode}</div>
                </div>
                <div>
                  <div class="text-sm font-medium"><code>lightMode</code></div>
                  <div class="text-xs opacity-70">{breakpoints.mediaQueries.lightMode}</div>
                </div>
              </div>
            </DaisyCard>
          </div>
          
          <div class="bg-base-200 p-6 rounded-box">
            <h3 class="text-xl font-bold mb-4">Exemplo de Uso</h3>
            
            <pre class="bg-base-300 p-4 rounded-box overflow-x-auto text-sm"><code>{mediaQueriesCode}</code></pre>
          </div>
        </div>
        
        <!-- Utilitários -->
        <div slot="utils" class="py-4">
          <h2 class="text-2xl font-bold mb-4">Utilitários Responsivos</h2>
          
          <p class="mb-4">
            O sistema fornece utilitários para trabalhar com layouts responsivos no JavaScript.
          </p>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            <DaisyCard title="Funções de Detecção">
              <div class="space-y-2">
                <div>
                  <div class="text-sm font-medium"><code>getCurrentBreakpoint()</code></div>
                  <div class="text-xs opacity-70">Retorna o breakpoint atual</div>
                </div>
                <div>
                  <div class="text-sm font-medium"><code>isMobile()</code></div>
                  <div class="text-xs opacity-70">Verifica se estamos em um dispositivo móvel</div>
                </div>
                <div>
                  <div class="text-sm font-medium"><code>isTablet()</code></div>
                  <div class="text-xs opacity-70">Verifica se estamos em um tablet</div>
                </div>
                <div>
                  <div class="text-sm font-medium"><code>isDesktop()</code></div>
                  <div class="text-xs opacity-70">Verifica se estamos em um desktop</div>
                </div>
                <div>
                  <div class="text-sm font-medium"><code>isPortrait()</code></div>
                  <div class="text-xs opacity-70">Verifica se estamos em orientação retrato</div>
                </div>
                <div>
                  <div class="text-sm font-medium"><code>isLandscape()</code></div>
                  <div class="text-xs opacity-70">Verifica se estamos em orientação paisagem</div>
                </div>
              </div>
            </DaisyCard>
            
            <DaisyCard title="Funções de Valor">
              <div class="space-y-2">
                <div>
                  <div class="text-sm font-medium"><code>getResponsiveValue(values, defaultValue)</code></div>
                  <div class="text-xs opacity-70">Retorna um valor com base no breakpoint atual</div>
                </div>
                <div>
                  <div class="text-sm font-medium"><code>getResponsiveClasses(classes)</code></div>
                  <div class="text-xs opacity-70">Retorna classes CSS com base no breakpoint atual</div>
                </div>
                <div>
                  <div class="text-sm font-medium"><code>getResponsiveStyles(styles)</code></div>
                  <div class="text-xs opacity-70">Gera estilos CSS para diferentes breakpoints</div>
                </div>
                <div>
                  <div class="text-sm font-medium"><code>onBreakpointChange(callback)</code></div>
                  <div class="text-xs opacity-70">Hook para detectar mudanças de breakpoint</div>
                </div>
              </div>
            </DaisyCard>
          </div>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            <div>
              <h3 class="text-xl font-bold mb-2">Valores Responsivos</h3>
              <pre class="bg-base-300 p-4 rounded-box overflow-x-auto text-sm"><code>{responsiveUtilsCode}</code></pre>
            </div>
            
            <div>
              <h3 class="text-xl font-bold mb-2">Listener de Breakpoint</h3>
              <pre class="bg-base-300 p-4 rounded-box overflow-x-auto text-sm"><code>{breakpointListenerCode}</code></pre>
            </div>
          </div>
        </div>
        
        <!-- Demonstração -->
        <div slot="demo" class="py-4">
          <h2 class="text-2xl font-bold mb-4">Demonstração Responsiva</h2>
          
          <p class="mb-4">
            Redimensione a janela do navegador para ver como os elementos se adaptam a diferentes tamanhos de tela.
          </p>
          
          <div class="mb-8">
            <h3 class="text-xl font-bold mb-4">Breakpoint Atual</h3>
            <div class="grid grid-cols-6 gap-2 text-center">
              <div class="p-2 rounded bg-base-200 xs:bg-primary xs:text-primary-content">
                <span class="hidden xs:inline">xs</span>
                <span class="xs:hidden">-</span>
              </div>
              <div class="p-2 rounded bg-base-200 sm:bg-primary sm:text-primary-content">
                <span class="hidden sm:inline">sm</span>
                <span class="sm:hidden">-</span>
              </div>
              <div class="p-2 rounded bg-base-200 md:bg-primary md:text-primary-content">
                <span class="hidden md:inline">md</span>
                <span class="md:hidden">-</span>
              </div>
              <div class="p-2 rounded bg-base-200 lg:bg-primary lg:text-primary-content">
                <span class="hidden lg:inline">lg</span>
                <span class="lg:hidden">-</span>
              </div>
              <div class="p-2 rounded bg-base-200 xl:bg-primary xl:text-primary-content">
                <span class="hidden xl:inline">xl</span>
                <span class="xl:hidden">-</span>
              </div>
              <div class="p-2 rounded bg-base-200 2xl:bg-primary 2xl:text-primary-content">
                <span class="hidden 2xl:inline">2xl</span>
                <span class="2xl:hidden">-</span>
              </div>
            </div>
          </div>
          
          <div class="mb-8">
            <h3 class="text-xl font-bold mb-4">Layout Responsivo</h3>
            <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4">
              {Array.from({ length: 12 }).map((_, i) => (
                <div class="bg-primary text-primary-content p-4 rounded text-center">
                  Item {i + 1}
                </div>
              ))}
            </div>
          </div>
          
          <div class="mb-8">
            <h3 class="text-xl font-bold mb-4">Tamanho de Texto Responsivo</h3>
            <p class="text-sm sm:text-base md:text-lg lg:text-xl xl:text-2xl">
              Este texto muda de tamanho conforme o breakpoint.
            </p>
          </div>
          
          <div class="mb-8">
            <h3 class="text-xl font-bold mb-4">Visibilidade Responsiva</h3>
            <div class="space-y-4">
              <div class="block sm:hidden p-4 bg-primary text-primary-content rounded">
                Visível apenas em dispositivos móveis pequenos (xs)
              </div>
              <div class="hidden sm:block md:hidden p-4 bg-secondary text-secondary-content rounded">
                Visível apenas em dispositivos móveis grandes (sm)
              </div>
              <div class="hidden md:block lg:hidden p-4 bg-accent text-accent-content rounded">
                Visível apenas em tablets (md)
              </div>
              <div class="hidden lg:block xl:hidden p-4 bg-info text-info-content rounded">
                Visível apenas em desktops (lg)
              </div>
              <div class="hidden xl:block 2xl:hidden p-4 bg-success text-success-content rounded">
                Visível apenas em desktops grandes (xl)
              </div>
              <div class="hidden 2xl:block p-4 bg-warning text-warning-content rounded">
                Visível apenas em telas muito grandes (2xl)
              </div>
            </div>
          </div>
          
          <div class="mb-8">
            <h3 class="text-xl font-bold mb-4">Orientação</h3>
            <div class="space-y-4">
              <div class="hidden portrait:block p-4 bg-primary text-primary-content rounded">
                Visível apenas em orientação retrato
              </div>
              <div class="hidden landscape:block p-4 bg-secondary text-secondary-content rounded">
                Visível apenas em orientação paisagem
              </div>
            </div>
          </div>
        </div>
      </Tabs>
      
      <div class="mt-12 p-6 bg-base-200 rounded-box">
        <h2 class="text-2xl font-bold mb-4">Importação</h2>
        
        <p class="mb-4">
          Para usar os breakpoints e utilitários responsivos, importe-os do módulo <code>layouts/responsive</code>:
        </p>
        
        <pre class="bg-base-300 p-4 rounded-box overflow-x-auto text-sm"><code>import { 
  breakpoints,
  getResponsiveValue,
  getResponsiveClasses,
  onBreakpointChange,
  getResponsiveStyles
} from '../../layouts/responsive';</code></pre>
      </div>
    </Section>
  </Container>
</BaseLayout>
