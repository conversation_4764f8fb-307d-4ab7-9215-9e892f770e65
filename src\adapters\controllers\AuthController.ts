/**
 * Controlador de Autenticação
 *
 * Este controlador implementa a lógica de interface para autenticação de usuários,
 * seguindo os princípios da Clean Architecture.
 */

import { AuthenticateUserUseCase } from '../../application/usecases/user/AuthenticateUserUseCase';
import { type UnknownRecord } from '../../types/common';

/**
 * Resposta de login
 */
export interface LoginResponse {
  success: boolean;
  data?: UnknownRecord;
  errors?: Record<string, string>;
  redirect?: string;
}

/**
 * Controlador de Autenticação
 */
export class AuthController {
  /**
   * Cria uma nova instância do controlador
   *
   * @param authenticateUserUseCase Caso de uso de autenticação
   */
  constructor(private readonly authenticateUserUseCase: AuthenticateUserUseCase) {}

  /**
   * Processa o login de usuário
   *
   * @param formData Dados do formulário de login
   * @returns Resposta de login
   */
  async login(formData: FormData): Promise<LoginResponse> {
    try {
      // Extrair dados do formulário
      const email = formData.get('email')?.toString() || '';
      const password = formData.get('password')?.toString() || '';

      // Validar dados do formulário
      const errors: Record<string, string> = {};

      if (!email) {
        errors.email = 'Email é obrigatório';
      }

      if (!password) {
        errors.password = 'Senha é obrigatória';
      }

      // Se houver erros de validação, retornar erro
      if (Object.keys(errors).length > 0) {
        return {
          success: false,
          errors,
        };
      }

      // Executar caso de uso de autenticação
      const result = await this.authenticateUserUseCase.execute(email, password);

      // Se a autenticação falhar, retornar erro
      if (!result.success) {
        return {
          success: false,
          errors: {
            form: result.error || 'Erro ao realizar login',
          },
        };
      }

      // Autenticação bem-sucedida
      return {
        success: true,
        data: result.user,
        redirect: this.getRedirectUrl(result.user?.role),
      };
    } catch (error) {
      console.error('Erro ao processar login:', error);

      // Retornar erro genérico
      return {
        success: false,
        errors: {
          form: 'Ocorreu um erro ao processar o login. Tente novamente.',
        },
      };
    }
  }

  /**
   * Processa o logout de usuário
   *
   * @returns Resposta de logout
   */
  async logout(): Promise<LoginResponse> {
    try {
      // Em uma implementação real, aqui seria feita a invalidação da sessão

      return {
        success: true,
        redirect: '/',
      };
    } catch (error) {
      console.error('Erro ao processar logout:', error);

      // Retornar erro genérico
      return {
        success: false,
        errors: {
          form: 'Ocorreu um erro ao processar o logout. Tente novamente.',
        },
      };
    }
  }

  /**
   * Obtém a URL de redirecionamento com base no perfil do usuário
   *
   * @param role Perfil do usuário
   * @returns URL de redirecionamento
   */
  private getRedirectUrl(role?: string): string {
    switch (role) {
      case 'admin':
        return '/admin/dashboard';
      case 'teacher':
        return '/professor/dashboard';
      case 'student':
        return '/aluno/dashboard';
      case 'parent':
        return '/responsavel/dashboard';
      default:
        return '/dashboard';
    }
  }
}
