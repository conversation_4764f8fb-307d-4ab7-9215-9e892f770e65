import type { QueryResult } from 'pg';
import { pgH<PERSON><PERSON> } from './pgHelper';

async function create(
  ulid_order: string,
  ulid_payment_type: string,
  status: number,
  value: number
): Promise<QueryResult> {
  return await pgHelper.query(
    `INSERT INTO tab_payment (
       ulid_order, 
       ulid_payment_type, 
       status, 
       value) 
     VALUES ('$1', '$2', $3, $4)
     RETURNING *`,
    [ulid_order, ulid_payment_type, status, value]
  );
}

async function read(ulid_payment?: string): Promise<QueryResult> {
  return await pgHelper.query(
    `SELECT * 
       FROM tab_payment
       ${ulid_payment ? ' WHERE ulid_payment = $1' : ''}`,
    [ulid_payment]
  );
}

async function update(ulid_payment: string, status: number): Promise<QueryResult> {
  return await pgHelper.query(
    `UPDATE tab_payment 
        SET cod_status   = $2 
      WHERE ulid_payment = '$1',
            updated_at   = NOW()
     RETURNING *`,
    [ulid_payment, status]
  );
}

async function deleteByUlid(ulid_payment: string): Promise<QueryResult> {
  return await pgHelper.query(
    `DELETE FROM tab_payment 
      WHERE ulid_payment = $1 
     RETURNING *`,
    [ulid_payment]
  );
}

export const paymentRepository = {
  create,
  read,
  update,
  deleteByUlid,
};
