/**
 * Serviço para gerenciamento de políticas de cache
 * 
 * Este serviço implementa as políticas de cache definidas nas configurações,
 * incluindo expiração, invalidação e limites de memória.
 */

import { logger } from '@utils/logger';
import { cacheService, type CacheableDataType } from '@services/cacheService';
import { 
  ExpirationStrategy, 
  getExpirationPolicy, 
  calculateTTL 
} from '@config/cache/expiration-policy.config';
import { 
  InvalidationStrategy, 
  getInvalidationStrategy, 
  shouldInvalidateOnEvent,
  getInvalidationKeyPattern,
  getInvalidationDependencies,
  getDependentTypes
} from '@config/cache/invalidation-strategy.config';
import { getMemoryLimits, parseMemorySize } from '@config/cache/memory-limits.config';
import { EventEmitter } from 'node:events';

// Emissor de eventos para invalidação baseada em eventos
const eventEmitter = new EventEmitter();

// Contador de acessos para estratégias adaptativas
const accessCounters: Record<string, Record<string, number>> = {};

/**
 * Serviço de políticas de cache
 */
export const cachePolicyService = {
  /**
   * Inicializa o serviço de políticas de cache
   */
  async initialize(): Promise<void> {
    try {
      logger.info('Inicializando serviço de políticas de cache');
      
      // Configurar limites de memória
      await this.configureMemoryLimits();
      
      // Inicializar contadores de acesso
      this.resetAccessCounters();
      
      // Configurar limpeza periódica de contadores
      setInterval(() => {
        this.resetAccessCounters();
      }, 24 * 60 * 60 * 1000); // 24 horas
      
      logger.info('Serviço de políticas de cache inicializado com sucesso');
    } catch (error) {
      logger.error('Erro ao inicializar serviço de políticas de cache:', error);
      throw error;
    }
  },
  
  /**
   * Configura os limites de memória no Valkey
   */
  async configureMemoryLimits(): Promise<void> {
    try {
      const memoryLimits = getMemoryLimits();
      const maxMemoryBytes = parseMemorySize(memoryLimits.maxMemory);
      
      // Configurar maxmemory e política de evicção via CONFIG SET
      // Isso só funciona se o Valkey permitir CONFIG SET (não é o caso em alguns ambientes)
      try {
        const client = await cacheService.getClient();
        await client.configSet('maxmemory', maxMemoryBytes.toString());
        await client.configSet('maxmemory-policy', memoryLimits.evictionPolicy);
        await client.configSet('maxmemory-samples', '10');
        
        logger.info(`Limites de memória configurados: ${memoryLimits.maxMemory}, política: ${memoryLimits.evictionPolicy}`);
      } catch (configError) {
        logger.warn('Não foi possível configurar limites de memória via CONFIG SET:', configError);
        logger.warn('Os limites de memória devem ser configurados no arquivo valkey.conf');
      }
      
      // Configurar monitoramento de uso de memória
      this.startMemoryMonitoring(memoryLimits.warningThreshold, memoryLimits.criticalThreshold);
    } catch (error) {
      logger.error('Erro ao configurar limites de memória:', error);
      throw error;
    }
  },
  
  /**
   * Inicia monitoramento de uso de memória
   * @param warningThreshold Limite de aviso (porcentagem)
   * @param criticalThreshold Limite crítico (porcentagem)
   */
  startMemoryMonitoring(warningThreshold: number, criticalThreshold: number): void {
    // Verificar uso de memória a cada minuto
    const interval = setInterval(async () => {
      try {
        const client = await cacheService.getClient();
        const info = await client.info('memory');
        
        // Extrair used_memory e maxmemory
        const usedMemoryMatch = info.match(/used_memory:(\d+)/);
        const maxMemoryMatch = info.match(/maxmemory:(\d+)/);
        
        if (usedMemoryMatch && maxMemoryMatch) {
          const usedMemory = parseInt(usedMemoryMatch[1], 10);
          const maxMemory = parseInt(maxMemoryMatch[1], 10);
          
          if (maxMemory > 0) {
            const usagePercent = (usedMemory / maxMemory) * 100;
            
            // Registrar uso de memória
            logger.debug(`Uso de memória do Valkey: ${usagePercent.toFixed(2)}% (${this.formatBytes(usedMemory)}/${this.formatBytes(maxMemory)})`);
            
            // Verificar limites
            if (usagePercent >= criticalThreshold) {
              logger.error(`USO CRÍTICO DE MEMÓRIA: ${usagePercent.toFixed(2)}% - Iniciando limpeza de emergência`);
              await this.performEmergencyCleanup();
            } else if (usagePercent >= warningThreshold) {
              logger.warn(`Aviso de uso de memória: ${usagePercent.toFixed(2)}% - Considere limpar caches não essenciais`);
            }
          }
        }
      } catch (error) {
        logger.error('Erro ao monitorar uso de memória:', error);
      }
    }, 60 * 1000); // 1 minuto
    
    // Garantir que o intervalo não impede o encerramento do processo
    interval.unref();
  },
  
  /**
   * Realiza limpeza de emergência quando o uso de memória está crítico
   */
  async performEmergencyCleanup(): Promise<void> {
    try {
      logger.info('Iniciando limpeza de emergência de cache');
      
      // Limpar caches com prioridade mais baixa primeiro
      const lowPriorityCaches = ['temp', 'metrics', 'search', 'query', 'page'];
      
      for (const cacheType of lowPriorityCaches) {
        const keyPattern = getInvalidationKeyPattern(cacheType);
        if (keyPattern) {
          logger.info(`Limpando cache de ${cacheType} (${keyPattern})`);
          await cacheService.deleteByPattern(keyPattern);
        }
      }
      
      logger.info('Limpeza de emergência concluída');
    } catch (error) {
      logger.error('Erro durante limpeza de emergência:', error);
    }
  },
  
  /**
   * Formata bytes para exibição
   * @param bytes Número de bytes
   * @returns String formatada
   */
  formatBytes(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  },
  
  /**
   * Reseta contadores de acesso
   */
  resetAccessCounters(): void {
    Object.keys(accessCounters).forEach(type => {
      accessCounters[type] = {};
    });
  },
  
  /**
   * Incrementa contador de acesso para uma chave
   * @param type Tipo de conteúdo
   * @param key Chave do cache
   */
  incrementAccessCounter(type: string, key: string): void {
    if (!accessCounters[type]) {
      accessCounters[type] = {};
    }
    
    accessCounters[type][key] = (accessCounters[type][key] || 0) + 1;
  },
  
  /**
   * Obtém contador de acesso para uma chave
   * @param type Tipo de conteúdo
   * @param key Chave do cache
   * @returns Número de acessos
   */
  getAccessCount(type: string, key: string): number {
    return accessCounters[type]?.[key] || 0;
  },
  
  /**
   * Obtém TTL para um item com base na política de expiração
   * @param type Tipo de conteúdo
   * @param key Chave do cache
   * @returns TTL em segundos
   */
  getTTL(type: CacheableDataType, key: string): number {
    const accessCount = this.getAccessCount(type, key);
    return calculateTTL(type, accessCount);
  },
  
  /**
   * Atualiza TTL para um item com estratégia de expiração deslizante
   * @param type Tipo de conteúdo
   * @param key Chave do cache
   */
  async updateSlidingExpiration(type: CacheableDataType, key: string): Promise<void> {
    const policy = getExpirationPolicy(type);
    
    if (policy.strategy === ExpirationStrategy.SLIDING) {
      const ttl = policy.ttl;
      await cacheService.expire(key, ttl);
    }
  },
  
  /**
   * Emite um evento para invalidação de cache
   * @param event Nome do evento
   * @param data Dados do evento
   */
  emitInvalidationEvent(event: string, data: any = {}): void {
    logger.debug(`Emitindo evento de invalidação: ${event}`, data);
    eventEmitter.emit(event, data);
    
    // Também emitir evento genérico para qualquer invalidação
    eventEmitter.emit('cache:invalidation', { event, data });
  },
  
  /**
   * Registra um listener para eventos de invalidação
   * @param event Nome do evento
   * @param listener Função de callback
   */
  onInvalidationEvent(event: string, listener: (data: any) => void): void {
    eventEmitter.on(event, listener);
  },
  
  /**
   * Invalida cache com base em um evento
   * @param event Nome do evento
   * @param data Dados do evento
   */
  async invalidateOnEvent(event: string, data: any = {}): Promise<void> {
    logger.debug(`Processando invalidação para evento: ${event}`, data);
    
    // Encontrar todos os tipos que devem ser invalidados por este evento
    const typesToInvalidate = Object.entries(invalidationStrategies)
      .filter(([_, config]) => config.events?.includes(event))
      .map(([type, _]) => type);
    
    // Invalidar cada tipo
    for (const type of typesToInvalidate) {
      await this.invalidateType(type, data);
      
      // Invalidar tipos dependentes em cascata
      const dependentTypes = getDependentTypes(type);
      for (const dependentType of dependentTypes) {
        await this.invalidateType(dependentType, data);
      }
    }
  },
  
  /**
   * Invalida cache de um tipo específico
   * @param type Tipo de conteúdo
   * @param data Dados adicionais para invalidação seletiva
   */
  async invalidateType(type: string, data: any = {}): Promise<void> {
    const keyPattern = getInvalidationKeyPattern(type);
    
    if (!keyPattern) {
      logger.debug(`Nenhum padrão de chave definido para invalidação de ${type}`);
      return;
    }
    
    // Se houver dados específicos, tentar invalidação seletiva
    if (data.id) {
      const specificPattern = keyPattern.replace('*', `${data.id}*`);
      logger.debug(`Invalidando cache seletivamente: ${specificPattern}`);
      await cacheService.deleteByPattern(specificPattern);
    } else {
      // Invalidação completa do tipo
      logger.debug(`Invalidando cache completamente: ${keyPattern}`);
      await cacheService.deleteByPattern(keyPattern);
    }
  },
};

// Registrar handler para eventos de invalidação
cachePolicyService.onInvalidationEvent('cache:invalidation', async ({ event, data }) => {
  await cachePolicyService.invalidateOnEvent(event, data);
});
