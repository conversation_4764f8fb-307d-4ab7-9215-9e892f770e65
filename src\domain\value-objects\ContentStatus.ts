/**
 * Status de conteúdo educacional
 *
 * Este objeto de valor representa os status possíveis de um conteúdo educacional no sistema.
 */

export enum ContentStatus {
  DRAFT = 'draft',
  REVIEW = 'review',
  PUBLISHED = 'published',
  ARCHIVED = 'archived',
  REJECTED = 'rejected',
}

/**
 * Verifica se um valor é um status de conteúdo válido
 *
 * @param value Valor a ser verificado
 * @returns Verdadeiro se o valor for um status de conteúdo válido
 */
export function isValidContentStatus(value: string): value is ContentStatus {
  return Object.values(ContentStatus).includes(value as ContentStatus);
}

/**
 * Obtém a descrição de um status de conteúdo
 *
 * @param status Status de conteúdo
 * @returns Descrição do status de conteúdo
 */
export function getContentStatusDescription(status: ContentStatus): string {
  const descriptions: Record<ContentStatus, string> = {
    [ContentStatus.DRAFT]: 'Rascunho',
    [ContentStatus.REVIEW]: 'Em Revisão',
    [ContentStatus.PUBLISHED]: 'Publicado',
    [ContentStatus.ARCHIVED]: 'Arquivado',
    [ContentStatus.REJECTED]: 'Rejeitado',
  };

  return descriptions[status] || 'Desconhecido';
}

/**
 * Obtém a cor associada a um status de conteúdo
 *
 * @param status Status de conteúdo
 * @returns Código de cor para o status
 */
export function getContentStatusColor(status: ContentStatus): string {
  const colors: Record<ContentStatus, string> = {
    [ContentStatus.DRAFT]: 'gray',
    [ContentStatus.REVIEW]: 'blue',
    [ContentStatus.PUBLISHED]: 'green',
    [ContentStatus.ARCHIVED]: 'purple',
    [ContentStatus.REJECTED]: 'red',
  };

  return colors[status] || 'gray';
}

/**
 * Verifica se um conteúdo com o status especificado pode ser editado
 *
 * @param status Status de conteúdo
 * @returns Verdadeiro se o conteúdo puder ser editado
 */
export function canEditContent(status: ContentStatus): boolean {
  return [ContentStatus.DRAFT, ContentStatus.REJECTED].includes(status);
}

/**
 * Verifica se um conteúdo com o status especificado pode ser publicado
 *
 * @param status Status de conteúdo
 * @returns Verdadeiro se o conteúdo puder ser publicado
 */
export function canPublishContent(status: ContentStatus): boolean {
  return [ContentStatus.DRAFT, ContentStatus.REVIEW, ContentStatus.REJECTED].includes(status);
}

/**
 * Verifica se um conteúdo com o status especificado pode ser arquivado
 *
 * @param status Status de conteúdo
 * @returns Verdadeiro se o conteúdo puder ser arquivado
 */
export function canArchiveContent(status: ContentStatus): boolean {
  return status === ContentStatus.PUBLISHED;
}

/**
 * Verifica se um conteúdo com o status especificado pode ser enviado para revisão
 *
 * @param status Status de conteúdo
 * @returns Verdadeiro se o conteúdo puder ser enviado para revisão
 */
export function canSendToReview(status: ContentStatus): boolean {
  return [ContentStatus.DRAFT, ContentStatus.REJECTED].includes(status);
}
