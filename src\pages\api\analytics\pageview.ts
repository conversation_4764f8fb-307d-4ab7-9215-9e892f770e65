/**
 * Analytics Pageview API
 *
 * Endpoint para rastrear visualizações de página de analytics no lado do servidor.
 * Parte da implementação da tarefa 8.9.3 - Analytics
 */

import type { APIRoute } from 'astro';
import { AnalyticsService } from '../../../domain/services/AnalyticsService';
import { GoogleAnalyticsService } from '../../../infrastructure/services/GoogleAnalyticsService';

// Inicializar serviço de analytics
const analyticsService: AnalyticsService = new GoogleAnalyticsService();

// Configurar serviço
await analyticsService.initialize({
  trackingId: process.env.GOOGLE_ANALYTICS_ID || '',
  anonymizeIp: true,
});

export const POST: APIRoute = async ({ request }) => {
  try {
    // Obter dados do corpo da requisição
    const body = await request.json();

    // Validar dados
    if (!body.path) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Parâmetro obrigatório: path',
        }),
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    // Rastrear visualização de página
    const success = await analyticsService.trackPageView({
      path: body.path,
      title: body.title,
      referrer: body.referrer,
      userAgent: body.userAgent,
      clientId: body.clientId,
      timestamp: body.timestamp ? new Date(body.timestamp) : new Date(),
      sessionId: body.sessionId,
      userId: body.userId,
      customDimensions: body.customDimensions,
    });

    if (!success) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Erro ao rastrear visualização de página',
        }),
        {
          status: 500,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    return new Response(
      JSON.stringify({
        success: true,
        message: 'Visualização de página rastreada com sucesso',
      }),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  } catch (error) {
    console.error('Erro ao processar requisição de visualização de página de analytics:', error);

    return new Response(
      JSON.stringify({
        success: false,
        error: 'Erro ao processar requisição',
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  }
};
