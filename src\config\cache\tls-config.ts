/**
 * Configuração de TLS/SSL para o Valkey
 * 
 * Este arquivo define as configurações de TLS/SSL para o Valkey,
 * incluindo certificados, chaves e opções de segurança.
 */

import * as fs from 'fs';
import * as path from 'path';
import { logger } from '@utils/logger';

/**
 * Interface para configuração de TLS/SSL
 */
export interface TLSConfig {
  /**
   * Se TLS/SSL está habilitado
   */
  enabled: boolean;
  
  /**
   * Caminho para o certificado
   */
  certPath: string;
  
  /**
   * Caminho para a chave privada
   */
  keyPath: string;
  
  /**
   * Caminho para o CA (Certificate Authority)
   */
  caPath?: string;
  
  /**
   * Se deve verificar certificados de cliente
   */
  verifyClient: boolean;
  
  /**
   * Protocolos TLS permitidos
   */
  protocols: string[];
  
  /**
   * Cifras permitidas
   */
  ciphers?: string[];
  
  /**
   * Se deve preferir cifras do servidor
   */
  preferServerCiphers: boolean;
  
  /**
   * Descrição da configuração
   */
  description: string;
}

/**
 * Configurações de TLS/SSL para diferentes ambientes
 */
export const tlsConfigs: Record<string, TLSConfig> = {
  // Ambiente de desenvolvimento
  'development': {
    enabled: process.env.VALKEY_TLS === 'true',
    certPath: process.env.VALKEY_CERT_PATH || path.join(process.cwd(), 'certs', 'valkey-dev.crt'),
    keyPath: process.env.VALKEY_KEY_PATH || path.join(process.cwd(), 'certs', 'valkey-dev.key'),
    caPath: process.env.VALKEY_CA_PATH,
    verifyClient: false,
    protocols: ['TLSv1.2', 'TLSv1.3'],
    preferServerCiphers: true,
    description: 'Configuração TLS para ambiente de desenvolvimento',
  },
  
  // Ambiente de teste
  'test': {
    enabled: process.env.VALKEY_TLS === 'true',
    certPath: process.env.VALKEY_CERT_PATH || path.join(process.cwd(), 'certs', 'valkey-test.crt'),
    keyPath: process.env.VALKEY_KEY_PATH || path.join(process.cwd(), 'certs', 'valkey-test.key'),
    caPath: process.env.VALKEY_CA_PATH,
    verifyClient: false,
    protocols: ['TLSv1.2', 'TLSv1.3'],
    preferServerCiphers: true,
    description: 'Configuração TLS para ambiente de teste',
  },
  
  // Ambiente de produção
  'production': {
    enabled: process.env.VALKEY_TLS === 'true',
    certPath: process.env.VALKEY_CERT_PATH || '/etc/valkey/certs/valkey.crt',
    keyPath: process.env.VALKEY_KEY_PATH || '/etc/valkey/certs/valkey.key',
    caPath: process.env.VALKEY_CA_PATH || '/etc/valkey/certs/ca.crt',
    verifyClient: true,
    protocols: ['TLSv1.2', 'TLSv1.3'],
    ciphers: [
      'ECDHE-ECDSA-AES128-GCM-SHA256',
      'ECDHE-RSA-AES128-GCM-SHA256',
      'ECDHE-ECDSA-AES256-GCM-SHA384',
      'ECDHE-RSA-AES256-GCM-SHA384',
      'ECDHE-ECDSA-CHACHA20-POLY1305',
      'ECDHE-RSA-CHACHA20-POLY1305',
    ],
    preferServerCiphers: true,
    description: 'Configuração TLS para ambiente de produção',
  },
};

/**
 * Obtém a configuração de TLS/SSL para o ambiente atual
 * @returns Configuração de TLS/SSL
 */
export function getTLSConfig(): TLSConfig {
  const env = process.env.NODE_ENV || 'development';
  return tlsConfigs[env] || tlsConfigs['development'];
}

/**
 * Verifica se os certificados existem
 * @returns Verdadeiro se os certificados existem
 */
export function certificatesExist(): boolean {
  const config = getTLSConfig();
  
  if (!config.enabled) {
    return true;
  }
  
  try {
    if (!fs.existsSync(config.certPath)) {
      logger.warn(`Certificado não encontrado: ${config.certPath}`);
      return false;
    }
    
    if (!fs.existsSync(config.keyPath)) {
      logger.warn(`Chave privada não encontrada: ${config.keyPath}`);
      return false;
    }
    
    if (config.caPath && !fs.existsSync(config.caPath)) {
      logger.warn(`CA não encontrado: ${config.caPath}`);
      return false;
    }
    
    return true;
  } catch (error) {
    logger.error('Erro ao verificar certificados:', error);
    return false;
  }
}

/**
 * Gera a configuração TLS para o arquivo valkey.conf
 * @returns Linhas de configuração para valkey.conf
 */
export function generateTLSConfig(): string[] {
  const config = getTLSConfig();
  const lines: string[] = [];
  
  if (!config.enabled) {
    return lines;
  }
  
  // Adicionar cabeçalho
  lines.push(`# Configuração de TLS/SSL`);
  lines.push(`# Gerado automaticamente em ${new Date().toISOString()}`);
  lines.push(``);
  
  // Configuração de certificados
  lines.push(`tls-cert-file ${config.certPath}`);
  lines.push(`tls-key-file ${config.keyPath}`);
  
  if (config.caPath) {
    lines.push(`tls-ca-cert-file ${config.caPath}`);
  }
  
  // Configuração de verificação de cliente
  if (config.verifyClient) {
    lines.push(`tls-auth-clients yes`);
  } else {
    lines.push(`tls-auth-clients optional`);
  }
  
  // Configuração de protocolos
  lines.push(`tls-protocols "${config.protocols.join(' ')}"`);
  
  // Configuração de cifras
  if (config.ciphers && config.ciphers.length > 0) {
    lines.push(`tls-ciphers "${config.ciphers.join(':')}"`);
  }
  
  // Configuração de preferência de cifras
  if (config.preferServerCiphers) {
    lines.push(`tls-prefer-server-ciphers yes`);
  } else {
    lines.push(`tls-prefer-server-ciphers no`);
  }
  
  lines.push(``);
  
  return lines;
}

/**
 * Gera certificados auto-assinados para desenvolvimento
 * @param certPath Caminho para o certificado
 * @param keyPath Caminho para a chave privada
 * @returns Verdadeiro se os certificados foram gerados com sucesso
 */
export async function generateSelfSignedCertificates(
  certPath: string,
  keyPath: string
): Promise<boolean> {
  try {
    // Verificar se os certificados já existem
    if (fs.existsSync(certPath) && fs.existsSync(keyPath)) {
      logger.info('Certificados já existem, pulando geração');
      return true;
    }
    
    // Criar diretório se não existir
    const certDir = path.dirname(certPath);
    const keyDir = path.dirname(keyPath);
    
    if (!fs.existsSync(certDir)) {
      fs.mkdirSync(certDir, { recursive: true });
    }
    
    if (!fs.existsSync(keyDir)) {
      fs.mkdirSync(keyDir, { recursive: true });
    }
    
    // Gerar certificados usando OpenSSL
    const { execSync } = require('child_process');
    
    // Gerar chave privada
    execSync(`openssl genrsa -out "${keyPath}" 2048`);
    
    // Gerar certificado auto-assinado
    execSync(`openssl req -new -key "${keyPath}" -x509 -days 365 -out "${certPath}" -subj "/C=BR/ST=SP/L=Sao Paulo/O=Estacao Alfabetizacao/CN=localhost"`);
    
    logger.info(`Certificados auto-assinados gerados em ${certPath} e ${keyPath}`);
    return true;
  } catch (error) {
    logger.error('Erro ao gerar certificados auto-assinados:', error);
    return false;
  }
}
