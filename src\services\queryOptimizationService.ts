/**
 * Serviço de otimização de consultas SQL
 *
 * Este serviço fornece funções otimizadas para consultas comuns
 * e ajuda a melhorar a performance do banco de dados.
 */

import { pgHelper } from '@repository/pgHelper';
import { queryCacheService } from '@services/queryCacheService';
import { logger } from '@utils/logger';

/**
 * Interface para opções de paginação
 */
export interface PaginationOptions {
  /**
   * Página atual (começando em 1)
   * @default 1
   */
  page: number;

  /**
   * Número de itens por página
   * @default 20
   */
  pageSize: number;

  /**
   * Campo para ordenação
   */
  orderBy?: string;

  /**
   * Direção da ordenação (asc ou desc)
   * @default 'asc'
   */
  orderDirection?: 'asc' | 'desc';

  /**
   * Se deve incluir contagem total
   * @default true
   */
  includeTotal?: boolean;
}

/**
 * Interface para resultado paginado
 */
export interface PaginatedResult<T> {
  /**
   * Itens da página atual
   */
  items: T[];

  /**
   * Metadados de paginação
   */
  pagination: {
    /**
     * Página atual
     */
    page: number;

    /**
     * Número de itens por página
     */
    pageSize: number;

    /**
     * Total de itens (se includeTotal for true)
     */
    total?: number;

    /**
     * Total de páginas (se includeTotal for true)
     */
    totalPages?: number;

    /**
     * Se há página anterior
     */
    hasPrevPage: boolean;

    /**
     * Se há próxima página
     */
    hasNextPage: boolean;
  };
}

/**
 * Serviço de otimização de consultas
 */
export const queryOptimizationService = {
  /**
   * Executa uma consulta paginada otimizada
   * @param baseQuery - Consulta SQL base (sem ORDER BY e LIMIT)
   * @param countQuery - Consulta SQL para contagem (opcional)
   * @param params - Parâmetros da consulta
   * @param options - Opções de paginação
   * @returns Resultado paginado
   */
  async paginatedQuery<T>(
    baseQuery: string,
    countQuery: string | null = null,
    params: any[] = [],
    options: Partial<PaginationOptions> = {}
  ): Promise<PaginatedResult<T>> {
    try {
      // Configuração padrão
      const fullOptions: PaginationOptions = {
        page: 1,
        pageSize: 20,
        orderDirection: 'asc',
        includeTotal: true,
        ...options,
      };

      // Validar opções
      fullOptions.page = Math.max(1, fullOptions.page);
      fullOptions.pageSize = Math.min(100, Math.max(1, fullOptions.pageSize));

      // Calcular offset
      const offset = (fullOptions.page - 1) * fullOptions.pageSize;

      // Construir consulta completa
      let fullQuery = baseQuery;

      // Adicionar ordenação se especificada
      if (fullOptions.orderBy) {
        fullQuery += ` ORDER BY ${fullOptions.orderBy} ${fullOptions.orderDirection}`;
      }

      // Adicionar paginação
      fullQuery += ` LIMIT ${fullOptions.pageSize} OFFSET ${offset}`;

      // Executar consulta principal
      const result = await queryCacheService.queryRead(fullQuery, params);
      const items = result.rows as T[];

      // Inicializar metadados de paginação
      const pagination = {
        page: fullOptions.page,
        pageSize: fullOptions.pageSize,
        hasPrevPage: fullOptions.page > 1,
        hasNextPage: items.length === fullOptions.pageSize, // Estimativa
      };

      // Executar consulta de contagem se necessário
      if (fullOptions.includeTotal) {
        const actualCountQuery = countQuery || `SELECT COUNT(*) FROM (${baseQuery}) AS count_query`;

        const countResult = await queryCacheService.queryRead(actualCountQuery, params);
        const total = Number.parseInt(countResult.rows[0].count, 10);

        // Atualizar metadados com contagem
        pagination.total = total;
        pagination.totalPages = Math.ceil(total / fullOptions.pageSize);
        pagination.hasNextPage = fullOptions.page < pagination.totalPages;
      }

      return {
        items,
        pagination,
      };
    } catch (error) {
      logger.error('Erro ao executar consulta paginada:', error);
      throw error;
    }
  },

  /**
   * Executa uma consulta paginada com cursor para melhor performance
   * @param baseQuery - Consulta SQL base (sem WHERE, ORDER BY e LIMIT)
   * @param cursorColumn - Coluna para usar como cursor
   * @param cursorValue - Valor do cursor (null para primeira página)
   * @param params - Parâmetros da consulta
   * @param pageSize - Número de itens por página
   * @param isDescending - Se a ordenação é descendente
   * @returns Resultado com itens e próximo cursor
   */
  async cursorPaginatedQuery<T>(
    baseQuery: string,
    cursorColumn: string,
    cursorValue: any = null,
    params: any[] = [],
    pageSize = 20,
    isDescending = false
  ): Promise<{ items: T[]; nextCursor: any | null }> {
    try {
      // Validar tamanho da página
      pageSize = Math.min(100, Math.max(1, pageSize));

      // Construir consulta com cursor
      let fullQuery = baseQuery;
      const fullParams = [...params];

      // Adicionar condição de cursor se especificado
      if (cursorValue !== null) {
        const operator = isDescending ? '<' : '>';
        fullQuery += ` WHERE ${cursorColumn} ${operator} $${fullParams.length + 1}`;
        fullParams.push(cursorValue);
      }

      // Adicionar ordenação
      const direction = isDescending ? 'DESC' : 'ASC';
      fullQuery += ` ORDER BY ${cursorColumn} ${direction}`;

      // Adicionar limite
      fullQuery += ` LIMIT ${pageSize + 1}`; // +1 para verificar se há próxima página

      // Executar consulta
      const result = await queryCacheService.queryRead(fullQuery, fullParams);

      // Verificar se há próxima página
      const hasNextPage = result.rows.length > pageSize;

      // Remover item extra usado para verificar próxima página
      const items = hasNextPage ? result.rows.slice(0, pageSize) : result.rows;

      // Determinar próximo cursor
      const nextCursor = hasNextPage ? items[items.length - 1][cursorColumn] : null;

      return {
        items: items as T[],
        nextCursor,
      };
    } catch (error) {
      logger.error('Erro ao executar consulta paginada com cursor:', error);
      throw error;
    }
  },

  /**
   * Executa uma consulta otimizada para busca de texto
   * @param table - Nome da tabela
   * @param searchColumns - Colunas para buscar
   * @param searchTerm - Termo de busca
   * @param additionalWhere - Condições adicionais (opcional)
   * @param options - Opções de paginação
   * @returns Resultado paginado
   */
  async textSearchQuery<T>(
    table: string,
    searchColumns: string[],
    searchTerm: string,
    additionalWhere = '',
    options: Partial<PaginationOptions> = {}
  ): Promise<PaginatedResult<T>> {
    try {
      // Preparar termo de busca
      const normalizedTerm = searchTerm.trim().replace(/\s+/g, ' & ');

      // Construir expressão de busca para cada coluna
      const searchConditions = searchColumns
        .map((column) => `to_tsvector('portuguese', ${column}) @@ to_tsquery('portuguese', $1)`)
        .join(' OR ');

      // Construir consulta base
      let baseQuery = `SELECT * FROM ${table} WHERE (${searchConditions})`;

      // Adicionar condições adicionais se especificadas
      if (additionalWhere) {
        baseQuery += ` AND (${additionalWhere})`;
      }

      // Construir consulta de contagem
      const countQuery = `SELECT COUNT(*) FROM ${table} WHERE (${searchConditions})${additionalWhere ? ` AND (${additionalWhere})` : ''}`;

      // Executar consulta paginada
      return this.paginatedQuery<T>(baseQuery, countQuery, [normalizedTerm], options);
    } catch (error) {
      logger.error('Erro ao executar consulta de busca de texto:', error);
      throw error;
    }
  },

  /**
   * Executa uma consulta com cache inteligente
   * @param query - Consulta SQL
   * @param params - Parâmetros da consulta
   * @param ttl - Tempo de vida em cache (em segundos)
   * @param tags - Tags para categorizar a consulta
   * @returns Resultado da consulta
   */
  async smartCachedQuery(
    query: string,
    params: any[] = [],
    ttl = 300,
    tags: string[] = []
  ): Promise<any> {
    try {
      // Detectar se é uma consulta de leitura
      const isReadQuery = pgHelper.isReadQuery(query);

      // Para consultas de leitura, usar cache
      if (isReadQuery) {
        return queryCacheService.queryRead(query, params, ttl);
      }

      // Para consultas de escrita, invalidar cache
      return queryCacheService.queryWrite(query, params);
    } catch (error) {
      logger.error('Erro ao executar consulta com cache inteligente:', error);
      throw error;
    }
  },
};
