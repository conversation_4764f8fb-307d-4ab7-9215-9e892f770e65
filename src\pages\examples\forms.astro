---
import DaisyButton from '../../components/ui/DaisyButton.astro';
import DaisyCard from '../../components/ui/DaisyCard.astro';
import Breadcrumbs from '../../components/ui/navigation/Breadcrumbs.astro';
import Tabs from '../../components/ui/navigation/Tabs.astro';
import BaseLayout from '../../layouts/BaseLayout.astro';

// Importar componentes de layout
import { Container, Section } from '../../layouts/grid';

// Importar componentes de formulário
import {
  Checkbox,
  Form,
  FormActions,
  FormGroup,
  Input,
  Radio,
  RadioGroup,
  Select,
  Slider,
  Switch,
  Textarea,
} from '../../components/ui/form';

const title = 'Componentes de Formulário';

// Breadcrumbs para a página atual
const breadcrumbItems = [
  { href: '/', label: 'Início' },
  { href: '/examples', label: 'Exemplos' },
  { label: 'Formulários' },
];

// Tabs para as categorias
const formTabs = [
  { id: 'inputs', label: 'Campos de Texto', isActive: true },
  { id: 'selects', label: 'Seleção' },
  { id: 'checkboxes', label: 'Checkbox e Radio' },
  { id: 'layout', label: 'Layout de Formulário' },
  { id: 'validation', label: 'Validação' },
];

// Opções para o select
const selectOptions = [
  { value: '', label: 'Selecione uma opção', disabled: true },
  { value: 'option1', label: 'Opção 1' },
  { value: 'option2', label: 'Opção 2' },
  { value: 'option3', label: 'Opção 3' },
  { value: 'option4', label: 'Opção 4' },
];

// Opções para o select com grupos
const selectGroupOptions = [
  {
    label: 'Grupo 1',
    options: [
      { value: 'g1-option1', label: 'Opção 1.1' },
      { value: 'g1-option2', label: 'Opção 1.2' },
      { value: 'g1-option3', label: 'Opção 1.3' },
    ],
  },
  {
    label: 'Grupo 2',
    options: [
      { value: 'g2-option1', label: 'Opção 2.1' },
      { value: 'g2-option2', label: 'Opção 2.2' },
      { value: 'g2-option3', label: 'Opção 2.3' },
    ],
  },
];

// Opções para o radio group
const radioOptions = [
  { value: 'option1', label: 'Opção 1' },
  { value: 'option2', label: 'Opção 2' },
  { value: 'option3', label: 'Opção 3' },
];
---

<BaseLayout title={title}>
  <Container>
    <Breadcrumbs items={breadcrumbItems} class="my-4" />
    
    <Section title={title} subtitle="Componentes de formulário acessíveis e responsivos">
      <div class="mb-8">
        <p>
          Os componentes de formulário foram desenvolvidos para serem acessíveis, responsivos e fáceis de usar.
          Eles seguem as melhores práticas de acessibilidade e design.
        </p>
      </div>
      
      <Tabs tabs={formTabs}>
        <!-- Campos de Texto -->
        <div slot="inputs" class="py-4">
          <h2 class="text-2xl font-bold mb-4">Campos de Texto</h2>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            <DaisyCard title="Input Básico">
              <Input
                label="Nome"
                placeholder="Digite seu nome"
                helperText="Digite seu nome completo"
                required
              />
            </DaisyCard>
            
            <DaisyCard title="Input com Erro">
              <Input
                label="Email"
                type="email"
                placeholder="Digite seu email"
                value="email-invalido"
                errorMessage="Email inválido"
                required
              />
            </DaisyCard>
            
            <DaisyCard title="Input com Label à Esquerda">
              <Input
                label="Telefone"
                type="tel"
                placeholder="(00) 00000-0000"
                labelPlacement="left"
              />
            </DaisyCard>
            
            <DaisyCard title="Input com Label Flutuante">
              <Input
                label="Endereço"
                placeholder="Digite seu endereço"
                labelPlacement="floating"
              />
            </DaisyCard>
            
            <DaisyCard title="Input com Tamanhos">
              <div class="space-y-4">
                <Input
                  label="Pequeno"
                  placeholder="Tamanho pequeno"
                  size="sm"
                />
                <Input
                  label="Médio (padrão)"
                  placeholder="Tamanho médio"
                />
                <Input
                  label="Grande"
                  placeholder="Tamanho grande"
                  size="lg"
                />
              </div>
            </DaisyCard>
            
            <DaisyCard title="Input com Variantes">
              <div class="space-y-4">
                <Input
                  label="Padrão com borda"
                  placeholder="Variante padrão"
                  variant="bordered"
                />
                <Input
                  label="Ghost"
                  placeholder="Variante ghost"
                  variant="ghost"
                />
              </div>
            </DaisyCard>
          </div>
          
          <h3 class="text-xl font-bold mb-4">Textarea</h3>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            <DaisyCard title="Textarea Básico">
              <Textarea
                label="Mensagem"
                placeholder="Digite sua mensagem"
                rows={4}
                helperText="Máximo de 500 caracteres"
              />
            </DaisyCard>
            
            <DaisyCard title="Textarea com Resize">
              <Textarea
                label="Comentário"
                placeholder="Digite seu comentário"
                rows={3}
                resize="both"
              />
            </DaisyCard>
          </div>
        </div>
        
        <!-- Seleção -->
        <div slot="selects" class="py-4">
          <h2 class="text-2xl font-bold mb-4">Campos de Seleção</h2>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            <DaisyCard title="Select Básico">
              <Select
                label="Selecione uma opção"
                placeholder="Selecione..."
                options={selectOptions}
                helperText="Escolha uma das opções disponíveis"
                required
              />
            </DaisyCard>
            
            <DaisyCard title="Select com Grupos">
              <Select
                label="Categorias"
                options={selectGroupOptions}
                helperText="Selecione uma categoria"
              />
            </DaisyCard>
            
            <DaisyCard title="Select Múltiplo">
              <Select
                label="Selecione várias opções"
                options={selectOptions.filter(opt => !opt.disabled)}
                multiple
                helperText="Pressione Ctrl para selecionar múltiplas opções"
              />
            </DaisyCard>
            
            <DaisyCard title="Select com Tamanhos">
              <div class="space-y-4">
                <Select
                  label="Pequeno"
                  options={selectOptions}
                  size="sm"
                />
                <Select
                  label="Médio (padrão)"
                  options={selectOptions}
                />
                <Select
                  label="Grande"
                  options={selectOptions}
                  size="lg"
                />
              </div>
            </DaisyCard>
          </div>
          
          <h3 class="text-xl font-bold mb-4">Slider</h3>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            <DaisyCard title="Slider Básico">
              <Slider
                label="Volume"
                min={0}
                max={100}
                defaultValue={50}
                showValue
                valueSuffix="%"
              />
            </DaisyCard>
            
            <DaisyCard title="Slider com Passos">
              <Slider
                label="Temperatura"
                min={15}
                max={30}
                step={0.5}
                defaultValue={22.5}
                showValue
                valueSuffix="°C"
                color="warning"
              />
            </DaisyCard>
          </div>
        </div>
        
        <!-- Checkbox e Radio -->
        <div slot="checkboxes" class="py-4">
          <h2 class="text-2xl font-bold mb-4">Checkbox e Radio</h2>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            <DaisyCard title="Checkbox Básico">
              <div class="space-y-2">
                <Checkbox
                  label="Aceito os termos e condições"
                  required
                />
                <Checkbox
                  label="Inscrever-se na newsletter"
                  defaultChecked
                />
                <Checkbox
                  label="Opção desabilitada"
                  disabled
                />
                <Checkbox
                  label="Estado indeterminado"
                  indeterminate
                />
              </div>
            </DaisyCard>
            
            <DaisyCard title="Checkbox com Cores">
              <div class="space-y-2">
                <Checkbox
                  label="Primário (padrão)"
                  color="primary"
                />
                <Checkbox
                  label="Secundário"
                  color="secondary"
                />
                <Checkbox
                  label="Acento"
                  color="accent"
                />
                <Checkbox
                  label="Sucesso"
                  color="success"
                />
              </div>
            </DaisyCard>
            
            <DaisyCard title="Radio Básico">
              <div class="space-y-2">
                <Radio
                  name="radio-demo"
                  label="Opção 1"
                  value="option1"
                  defaultChecked
                />
                <Radio
                  name="radio-demo"
                  label="Opção 2"
                  value="option2"
                />
                <Radio
                  name="radio-demo"
                  label="Opção 3 (desabilitada)"
                  value="option3"
                  disabled
                />
              </div>
            </DaisyCard>
            
            <DaisyCard title="Radio Group">
              <RadioGroup
                name="radio-group-demo"
                label="Escolha uma opção"
                options={radioOptions}
                defaultValue="option1"
                helperText="Selecione apenas uma opção"
              />
            </DaisyCard>
            
            <DaisyCard title="Radio Group Horizontal">
              <RadioGroup
                name="radio-group-horizontal"
                label="Layout"
                options={[
                  { value: "horizontal", label: "Horizontal" },
                  { value: "vertical", label: "Vertical" }
                ]}
                defaultValue="horizontal"
                layout="horizontal"
              />
            </DaisyCard>
            
            <DaisyCard title="Switch">
              <div class="space-y-4">
                <Switch
                  label="Modo escuro"
                  defaultChecked
                />
                <Switch
                  label="Notificações"
                  color="success"
                />
                <Switch
                  label="Modo de manutenção"
                  color="warning"
                  disabled
                />
              </div>
            </DaisyCard>
          </div>
        </div>
        
        <!-- Layout de Formulário -->
        <div slot="layout" class="py-4">
          <h2 class="text-2xl font-bold mb-4">Layout de Formulário</h2>
          
          <div class="grid grid-cols-1 gap-6 mb-8">
            <DaisyCard title="Formulário Vertical (Padrão)">
              <Form spacing="md">
                <Input
                  label="Nome"
                  placeholder="Digite seu nome"
                  required
                />
                <Input
                  label="Email"
                  type="email"
                  placeholder="Digite seu email"
                  required
                />
                <Textarea
                  label="Mensagem"
                  placeholder="Digite sua mensagem"
                  rows={4}
                />
                <Checkbox
                  label="Aceito os termos e condições"
                  required
                />
                <FormActions>
                  <DaisyButton variant="ghost">Cancelar</DaisyButton>
                  <DaisyButton variant="primary">Enviar</DaisyButton>
                </FormActions>
              </Form>
            </DaisyCard>
            
            <DaisyCard title="Formulário Horizontal">
              <Form layout="horizontal">
                <Input
                  label="Nome"
                  placeholder="Digite seu nome"
                  required
                />
                <Input
                  label="Email"
                  type="email"
                  placeholder="Digite seu email"
                  required
                />
                <Textarea
                  label="Mensagem"
                  placeholder="Digite sua mensagem"
                  rows={4}
                />
                <Checkbox
                  label="Aceito os termos e condições"
                  required
                />
                <FormActions>
                  <DaisyButton variant="ghost">Cancelar</DaisyButton>
                  <DaisyButton variant="primary">Enviar</DaisyButton>
                </FormActions>
              </Form>
            </DaisyCard>
            
            <DaisyCard title="Formulário com Grupos">
              <Form>
                <FormGroup
                  title="Informações Pessoais"
                  description="Preencha seus dados pessoais"
                  bordered
                >
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <Input
                      label="Nome"
                      placeholder="Digite seu nome"
                      required
                    />
                    <Input
                      label="Sobrenome"
                      placeholder="Digite seu sobrenome"
                      required
                    />
                  </div>
                  <Input
                    label="Email"
                    type="email"
                    placeholder="Digite seu email"
                    required
                  />
                  <Input
                    label="Telefone"
                    type="tel"
                    placeholder="(00) 00000-0000"
                  />
                </FormGroup>
                
                <FormGroup
                  title="Endereço"
                  description="Preencha seu endereço de entrega"
                  bordered
                >
                  <Input
                    label="Endereço"
                    placeholder="Rua, número, complemento"
                    required
                  />
                  <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <Input
                      label="Cidade"
                      placeholder="Cidade"
                      required
                    />
                    <Input
                      label="Estado"
                      placeholder="Estado"
                      required
                    />
                    <Input
                      label="CEP"
                      placeholder="00000-000"
                      required
                    />
                  </div>
                </FormGroup>
                
                <FormActions>
                  <DaisyButton variant="ghost">Cancelar</DaisyButton>
                  <DaisyButton variant="primary">Salvar</DaisyButton>
                </FormActions>
              </Form>
            </DaisyCard>
          </div>
        </div>
        
        <!-- Validação -->
        <div slot="validation" class="py-4">
          <h2 class="text-2xl font-bold mb-4">Validação de Formulário</h2>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            <DaisyCard title="Validação HTML5">
              <Form novalidate>
                <Input
                  label="Nome"
                  placeholder="Digite seu nome"
                  required
                  helperText="Campo obrigatório"
                />
                <Input
                  label="Email"
                  type="email"
                  placeholder="Digite seu email"
                  required
                  helperText="Digite um email válido"
                />
                <Input
                  label="Senha"
                  type="password"
                  placeholder="Digite sua senha"
                  required
                  minLength={8}
                  pattern="^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$"
                  helperText="Mínimo 8 caracteres, incluindo maiúsculas, minúsculas, números e caracteres especiais"
                />
                <FormActions>
                  <DaisyButton variant="primary" type="submit">Validar</DaisyButton>
                </FormActions>
              </Form>
            </DaisyCard>
            
            <DaisyCard title="Estados de Erro">
              <Form>
                <Input
                  label="Nome de usuário"
                  placeholder="Digite seu nome de usuário"
                  value="user123"
                  errorMessage="Nome de usuário já existe"
                />
                <Input
                  label="Email"
                  type="email"
                  placeholder="Digite seu email"
                  value="email-invalido"
                  errorMessage="Email inválido"
                />
                <Select
                  label="País"
                  options={selectOptions}
                  errorMessage="Selecione um país"
                />
                <RadioGroup
                  name="gender"
                  label="Gênero"
                  options={[
                    { value: "male", label: "Masculino" },
                    { value: "female", label: "Feminino" },
                    { value: "other", label: "Outro" }
                  ]}
                  errorMessage="Selecione uma opção"
                />
                <FormActions>
                  <DaisyButton variant="primary">Corrigir</DaisyButton>
                </FormActions>
              </Form>
            </DaisyCard>
          </div>
          
          <DaisyCard title="Validação em Tempo Real">
            <p class="mb-4">
              Para implementar validação em tempo real, você pode usar JavaScript para validar os campos
              conforme o usuário digita e exibir mensagens de erro apropriadas.
            </p>
            
            <pre class="bg-base-300 p-4 rounded-box overflow-x-auto text-sm mb-4"><code>// Exemplo de validação em tempo real
document.querySelector('input[type="email"]').addEventListener('input', function(e) {
  const email = e.target.value;
  const isValid = /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
  
  if (!isValid && email !== '') {
    e.target.setAttribute('aria-invalid', 'true');
    // Exibir mensagem de erro
  } else {
    e.target.removeAttribute('aria-invalid');
    // Remover mensagem de erro
  }
});</code></pre>
            
            <p>
              Os componentes de formulário suportam os atributos <code>aria-invalid</code> e <code>aria-describedby</code>
              para acessibilidade, facilitando a implementação de validação personalizada.
            </p>
          </DaisyCard>
        </div>
      </Tabs>
      
      <div class="mt-12 p-6 bg-base-200 rounded-box">
        <h2 class="text-2xl font-bold mb-4">Importação</h2>
        
        <p class="mb-4">
          Para usar os componentes de formulário, importe-os do módulo <code>components/ui/form</code>:
        </p>
        
        <pre class="bg-base-300 p-4 rounded-box overflow-x-auto text-sm"><code>import {
  Form,
  FormGroup,
  FormActions,
  Input,
  Textarea,
  Select,
  Checkbox,
  Radio,
  RadioGroup,
  Switch,
  Slider
} from '../components/ui/form';</code></pre>
      </div>
    </Section>
  </Container>
</BaseLayout>
