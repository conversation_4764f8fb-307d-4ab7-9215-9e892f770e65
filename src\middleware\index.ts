/**
 * Exportações de Middlewares
 * 
 * Este arquivo exporta todos os middlewares disponíveis na aplicação.
 */

// Middlewares de autenticação e autorização
export * from './authMiddleware';
export * from './authorizationMiddleware';
export * from './jwtAuthMiddleware';
export * from './policyEnforcementMiddleware';
export * from './csrfMiddleware';

// Middlewares de cache e performance
export * from './cacheMiddleware';
export * from './cacheControlMiddleware';
export * from './compressionMiddleware';
export * from './queryOptimizationMiddleware';
export * from './rateLimitMiddleware';
export * from './valkeyRateLimitMiddleware';

// Middlewares de sessão
export * from './sessionMiddleware';

// Middlewares de segurança e auditoria
export * from './auditMiddleware';

// Middlewares de revalidação e renderização sob demanda
export * from './revalidationMiddleware';
export * from './odrMiddleware';

// Middlewares de monitoramento
export * from './monitoringMiddleware';

// Aviso de depreciação para o diretório 'middlewares'
console.warn(
  'DEPRECATED: O diretório src/middlewares está depreciado. ' +
  'Use o diretório src/middleware para todos os middlewares.'
);
