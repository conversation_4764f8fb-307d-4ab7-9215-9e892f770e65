/**
 * Serviço de validação de esquema
 *
 * Este serviço fornece funcionalidades para validação de esquema de mensagens
 * usando JSON Schema.
 */

import { logger } from '@utils/logger';
import Ajv, { JSONSchemaType } from 'ajv';
import addFormats from 'ajv-formats';
import { cacheService } from './cacheService';
import { SchemaValidator } from './messageProcessingService';

// Instância do validador AJV
const ajv = new Ajv({
  allErrors: true,
  verbose: true,
  validateFormats: true,
});

// Adicionar formatos padrão
addFormats(ajv);

/**
 * Implementação de validador de esquema usando AJV
 */
export class JsonSchemaValidator<T extends Record<string, unknown>> implements SchemaValidator {
  private validator: ReturnType<typeof ajv.compile>;
  private errors: string[] = [];

  /**
   * Cria um novo validador de esquema
   * @param schema - Esquema JSON para validação
   */
  constructor(private schema: JSONSchemaType<T>) {
    this.validator = ajv.compile(schema);
  }

  /**
   * Valida um evento contra o esquema
   * @param event - Evento a ser validado
   * @returns Verdadeiro se o evento for válido
   */
  validate(event: Record<string, unknown>): boolean {
    const isValid = this.validator(event);

    if (!isValid && this.validator.errors) {
      this.errors = this.validator.errors.map((err) => {
        return `${err.instancePath} ${err.message}`;
      });
    } else {
      this.errors = [];
    }

    return isValid;
  }

  /**
   * Obtém erros de validação
   * @returns Lista de erros de validação
   */
  getErrors(): string[] {
    return this.errors;
  }
}

/**
 * Serviço de validação de esquema
 */
export const schemaValidationService = {
  /**
   * Cache de validadores
   */
  validators: new Map<string, SchemaValidator>(),

  /**
   * Obtém um validador para um esquema
   * @param schemaName - Nome do esquema
   * @returns Validador de esquema
   */
  async getValidator(schemaName: string): Promise<SchemaValidator | null> {
    try {
      // Verificar cache em memória
      if (this.validators.has(schemaName)) {
        return this.validators.get(schemaName)!;
      }

      // Verificar cache distribuído
      const cacheKey = `schema:${schemaName}`;
      const cachedSchema = await cacheService.get(cacheKey);

      if (cachedSchema) {
        const schema = JSON.parse(cachedSchema);
        const validator = new JsonSchemaValidator(schema);
        this.validators.set(schemaName, validator);
        return validator;
      }

      // Buscar esquema do banco de dados
      const schemaResult = await this.getSchemaFromDatabase(schemaName);

      if (!schemaResult) {
        logger.warn(`Esquema não encontrado: ${schemaName}`);
        return null;
      }

      // Criar validador
      const schema = JSON.parse(schemaResult.schema_definition);
      const validator = new JsonSchemaValidator(schema);

      // Armazenar em cache
      this.validators.set(schemaName, validator);
      await cacheService.set(cacheKey, schemaResult.schema_definition, 3600); // 1 hora

      return validator;
    } catch (error) {
      logger.error(`Erro ao obter validador para esquema ${schemaName}:`, error);
      return null;
    }
  },

  /**
   * Obtém um esquema do banco de dados
   * @param schemaName - Nome do esquema
   * @returns Esquema do banco de dados
   */
  async getSchemaFromDatabase(schemaName: string): Promise<{
    schema_id: string;
    schema_name: string;
    schema_version: number;
    schema_definition: string;
  } | null> {
    try {
      // Implementação fictícia - substituir pela consulta real ao banco de dados
      // Na implementação real, buscar da tabela tab_message_schema

      // Esquemas pré-definidos para desenvolvimento
      const predefinedSchemas: Record<string, any> = {
        'payment.transaction': {
          type: 'object',
          properties: {
            paymentId: { type: 'string' },
            orderId: { type: 'string' },
            userId: { type: 'string' },
            value: { type: 'number', minimum: 0 },
            paymentType: { type: 'string' },
            status: { type: 'string' },
            externalId: { type: 'string', nullable: true },
            metadata: {
              type: 'object',
              nullable: true,
              additionalProperties: true,
            },
          },
          required: ['paymentId', 'value', 'paymentType', 'status'],
          additionalProperties: false,
        },
        'order.event': {
          type: 'object',
          properties: {
            orderId: { type: 'string' },
            userId: { type: 'string' },
            total: { type: 'number', minimum: 0 },
            status: { type: 'string' },
            items: {
              type: 'array',
              nullable: true,
              items: {
                type: 'object',
                properties: {
                  productId: { type: 'string' },
                  quantity: { type: 'number', minimum: 1 },
                  price: { type: 'number', minimum: 0 },
                },
                required: ['productId', 'quantity', 'price'],
              },
            },
            metadata: {
              type: 'object',
              nullable: true,
              additionalProperties: true,
            },
          },
          required: ['orderId', 'userId', 'total', 'status'],
          additionalProperties: false,
        },
        'notification.event': {
          type: 'object',
          properties: {
            notificationId: { type: 'string' },
            userId: { type: 'string', nullable: true },
            type: { type: 'string' },
            subject: { type: 'string', nullable: true },
            recipientEmail: { type: 'string', nullable: true },
            content: { type: 'string', nullable: true },
            metadata: {
              type: 'object',
              nullable: true,
              additionalProperties: true,
            },
          },
          required: ['notificationId', 'type'],
          additionalProperties: false,
        },
      };

      if (predefinedSchemas[schemaName]) {
        return {
          schema_id: `schema-${schemaName}`,
          schema_name: schemaName,
          schema_version: 1,
          schema_definition: JSON.stringify(predefinedSchemas[schemaName]),
        };
      }

      return null;
    } catch (error) {
      logger.error(`Erro ao obter esquema ${schemaName} do banco de dados:`, error);
      return null;
    }
  },

  /**
   * Registra um novo esquema
   * @param schemaName - Nome do esquema
   * @param schemaDefinition - Definição do esquema
   * @returns ID do esquema registrado
   */
  async registerSchema(
    schemaName: string,
    schemaDefinition: Record<string, unknown>
  ): Promise<string | null> {
    try {
      // Validar esquema
      try {
        ajv.compile(schemaDefinition as JSONSchemaType<any>);
      } catch (validationError) {
        logger.error(`Esquema inválido ${schemaName}:`, validationError);
        return null;
      }

      // Implementação fictícia - substituir pela inserção real no banco de dados
      // Na implementação real, inserir na tabela tab_message_schema

      const schemaId = `schema-${schemaName}-${Date.now()}`;

      // Limpar caches
      this.validators.delete(schemaName);
      await cacheService.delete(`schema:${schemaName}`);

      // Armazenar novo esquema em cache
      const cacheKey = `schema:${schemaName}`;
      await cacheService.set(cacheKey, JSON.stringify(schemaDefinition), 3600); // 1 hora

      return schemaId;
    } catch (error) {
      logger.error(`Erro ao registrar esquema ${schemaName}:`, error);
      return null;
    }
  },

  /**
   * Valida uma mensagem contra um esquema
   * @param message - Mensagem a ser validada
   * @param schemaName - Nome do esquema
   * @returns Resultado da validação
   */
  async validateMessage(
    message: Record<string, unknown>,
    schemaName: string
  ): Promise<{ isValid: boolean; errors: string[] }> {
    try {
      const validator = await this.getValidator(schemaName);

      if (!validator) {
        return {
          isValid: false,
          errors: [`Esquema não encontrado: ${schemaName}`],
        };
      }

      const isValid = validator.validate(message);

      return {
        isValid,
        errors: isValid ? [] : validator.getErrors(),
      };
    } catch (error) {
      logger.error(`Erro ao validar mensagem contra esquema ${schemaName}:`, error);
      return {
        isValid: false,
        errors: [
          `Erro interno ao validar mensagem: ${error instanceof Error ? error.message : 'Erro desconhecido'}`,
        ],
      };
    }
  },
};
