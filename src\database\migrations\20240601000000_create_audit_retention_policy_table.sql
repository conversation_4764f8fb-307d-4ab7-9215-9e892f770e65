-- Criação da tabela de políticas de retenção de logs de auditoria
CREATE TABLE IF NOT EXISTS tab_audit_retention_policy (
  ulid_policy VARCHAR(36) PRIMARY KEY,
  name VARCHAR(100) NOT NULL UNIQUE,
  description TEXT,
  retention_days INTEGER NOT NULL,
  event_types JSONB NOT NULL,
  severities JSONB NOT NULL,
  archive_before_delete BOOLEAN NOT NULL DEFAULT TRUE,
  archive_format VARCHAR(10) NOT NULL DEFAULT 'json',
  compress_archive BOOLEAN NOT NULL DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL,
  
  -- Índices para consultas comuns
  INDEX idx_audit_retention_policy_name (name)
);

-- <PERSON><PERSON><PERSON> da tabela de arquivos de auditoria
CREATE TABLE IF NOT EXISTS tab_audit_archive (
  ulid_archive VARCHAR(36) PRIMARY KEY,
  ulid_policy VARCHAR(36) NOT NULL,
  file_name VARCHAR(255) NOT NULL,
  file_path VARCHAR(255) NOT NULL,
  file_size BIGINT NOT NULL,
  record_count INTEGER NOT NULL,
  start_date TIMESTAMP WITH TIME ZONE NOT NULL,
  end_date TIMESTAMP WITH TIME ZONE NOT NULL,
  format VARCHAR(10) NOT NULL,
  compressed BOOLEAN NOT NULL DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL,
  
  -- Chave estrangeira
  FOREIGN KEY (ulid_policy) REFERENCES tab_audit_retention_policy (ulid_policy),
  
  -- Índices para consultas comuns
  INDEX idx_audit_archive_policy (ulid_policy),
  INDEX idx_audit_archive_date_range (start_date, end_date)
);

-- Adicionar índices à tabela de logs de auditoria para melhorar performance de consultas
CREATE INDEX IF NOT EXISTS idx_audit_log_event_type_severity ON tab_audit_log (event_type, severity);
CREATE INDEX IF NOT EXISTS idx_audit_log_created_at_event_type ON tab_audit_log (created_at, event_type);

-- Adicionar coluna para rastreamento de arquivamento
ALTER TABLE tab_audit_log ADD COLUMN IF NOT EXISTS archived BOOLEAN DEFAULT FALSE;
ALTER TABLE tab_audit_log ADD COLUMN IF NOT EXISTS archive_id VARCHAR(36) DEFAULT NULL;

-- Adicionar índice para consultas de arquivamento
CREATE INDEX IF NOT EXISTS idx_audit_log_archived ON tab_audit_log (archived);

-- Comentários nas tabelas
COMMENT ON TABLE tab_audit_retention_policy IS 'Políticas de retenção para logs de auditoria';
COMMENT ON TABLE tab_audit_archive IS 'Registro de arquivos de auditoria arquivados';
COMMENT ON COLUMN tab_audit_log.archived IS 'Indica se o log foi arquivado';
COMMENT ON COLUMN tab_audit_log.archive_id IS 'ID do arquivo de arquivamento';
