{"config": {"configFile": "C:\\src\\astro\\estacao_alfabetizacao\\tests\\integration\\playwright.config.ts", "rootDir": "C:/src/astro/estacao_alfabetizacao/tests/integration/tests/integration", "forbidOnly": false, "fullyParallel": true, "globalSetup": null, "globalTeardown": null, "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {}, "preserveOutput": "always", "reporter": [["html", {"outputFolder": "./tests/integration/reports"}], ["json", {"outputFile": "./tests/integration/reports/results.json"}], ["list", null]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "C:/src/astro/estacao_alfabetizacao/test-results", "repeatEach": 1, "retries": 1, "metadata": {}, "id": "integration-chrome", "name": "integration-chrome", "testDir": "C:/src/astro/estacao_alfabetizacao/tests/integration/tests/integration", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "C:/src/astro/estacao_alfabetizacao/test-results", "repeatEach": 1, "retries": 1, "metadata": {}, "id": "api", "name": "api", "testDir": "C:/src/astro/estacao_alfabetizacao/tests/integration/tests/integration", "testIgnore": [], "testMatch": ["/api\\.spec\\.ts/"], "timeout": 30000}, {"outputDir": "C:/src/astro/estacao_alfabetizacao/test-results", "repeatEach": 1, "retries": 1, "metadata": {}, "id": "database", "name": "database", "testDir": "C:/src/astro/estacao_alfabetizacao/tests/integration/tests/integration", "testIgnore": [], "testMatch": ["/database\\.spec\\.ts/"], "timeout": 30000}, {"outputDir": "C:/src/astro/estacao_alfabetizacao/test-results", "repeatEach": 1, "retries": 1, "metadata": {}, "id": "services", "name": "services", "testDir": "C:/src/astro/estacao_alfabetizacao/tests/integration/tests/integration", "testIgnore": [], "testMatch": ["/services\\.spec\\.ts/"], "timeout": 30000}, {"outputDir": "C:/src/astro/estacao_alfabetizacao/test-results", "repeatEach": 1, "retries": 1, "metadata": {}, "id": "user-flows", "name": "user-flows", "testDir": "C:/src/astro/estacao_alfabetizacao/tests/integration/tests/integration", "testIgnore": [], "testMatch": ["/user-flows\\.spec\\.ts/"], "timeout": 30000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.52.0", "workers": 2, "webServer": null}, "suites": [], "errors": [{"message": "Error: No tests found", "stack": "Error: No tests found"}], "stats": {"startTime": "2025-05-22T02:20:07.392Z", "duration": 17.054999999999836, "expected": 0, "skipped": 0, "unexpected": 0, "flaky": 0}}