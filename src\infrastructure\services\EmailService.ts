/**
 * Serviço de envio de emails
 *
 * Este serviço é responsável por enviar emails usando o Nodemailer.
 */

import nodemailer from 'nodemailer';
import { ConsoleLogger } from '../logging/ConsoleLogger';
import { LogLevel } from '../../application/interfaces/services/Logger';

// Interface para opções de email
interface EmailOptions {
  to: string | string[];
  subject: string;
  text?: string;
  html?: string;
  from?: string;
  cc?: string | string[];
  bcc?: string | string[];
  attachments?: Array<{
    filename: string;
    content: Buffer | string;
    contentType?: string;
  }>;
}

// Configuração do serviço de email
const emailConfig = {
  host: process.env.EMAIL_HOST || 'smtp.example.com',
  port: parseInt(process.env.EMAIL_PORT || '587', 10),
  secure: process.env.EMAIL_SECURE === 'true',
  auth: {
    user: process.env.EMAIL_USER || '<EMAIL>',
    pass: process.env.EMAIL_PASSWORD || 'password'
  },
  defaultFrom: process.env.EMAIL_FROM || 'Estação Alfabetização <<EMAIL>>'
};

// Inicializar logger
const logger = new ConsoleLogger('EmailService', {
  level: LogLevel.INFO,
  useColors: true,
  format: 'text'
});

// Criar transportador de email
const transporter = nodemailer.createTransport({
  host: emailConfig.host,
  port: emailConfig.port,
  secure: emailConfig.secure,
  auth: emailConfig.auth
});

/**
 * Envia um email
 *
 * @param options Opções do email
 * @returns Resultado do envio
 */
export async function sendEmail(options: EmailOptions): Promise<boolean> {
  try {
    // Validar opções
    if (!options.to) {
      throw new Error('Destinatário (to) é obrigatório');
    }

    if (!options.subject) {
      throw new Error('Assunto (subject) é obrigatório');
    }

    if (!options.text && !options.html) {
      throw new Error('Conteúdo (text ou html) é obrigatório');
    }

    // Configurar email
    const mailOptions = {
      from: options.from || emailConfig.defaultFrom,
      to: options.to,
      cc: options.cc,
      bcc: options.bcc,
      subject: options.subject,
      text: options.text,
      html: options.html,
      attachments: options.attachments
    };

    // Enviar email
    logger.info(`Enviando email para ${options.to}`, { subject: options.subject });
    const info = await transporter.sendMail(mailOptions);

    // Registrar sucesso
    logger.info(`Email enviado com sucesso: ${info.messageId}`);

    return true;
  } catch (error) {
    // Registrar erro
    logger.error('Erro ao enviar email', error as Error, {
      to: options.to,
      subject: options.subject
    });

    return false;
  }
}

/**
 * Verifica se o serviço de email está funcionando
 *
 * @returns Verdadeiro se o serviço estiver funcionando
 */
export async function verifyEmailService(): Promise<boolean> {
  try {
    await transporter.verify();
    return true;
  } catch (error) {
    logger.error('Erro ao verificar serviço de email', error as Error);
    return false;
  }
}
