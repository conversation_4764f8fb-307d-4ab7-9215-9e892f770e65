/**
 * Update Content Use Case
 *
 * Caso de uso para atualizar um conteúdo existente.
 * Parte da implementação da tarefa 8.8.3 - Gestão de conteúdo
 */

import { nanoid } from 'nanoid';
import { Content, ContentMeta, ContentSEO, ContentVersion } from '../../entities/Content';
import { ContentCategoryRepository } from '../../repositories/ContentCategoryRepository';
import { ContentRepository } from '../../repositories/ContentRepository';
import { ContentTagRepository } from '../../repositories/ContentTagRepository';
import { SlugService } from '../../services/SlugService';

export interface UpdateContentRequest {
  id: string;
  title?: string;
  body?: string;
  meta?: Partial<ContentMeta>;
  seo?: Partial<ContentSEO>;
  categoryIds?: string[];
  tagIds?: string[];
  slug?: string;
  createVersion?: boolean;
  versionComment?: string;
  userId: string;
}

export interface UpdateContentResponse {
  success: boolean;
  data?: Content;
  version?: ContentVersion;
  error?: string;
}

export class UpdateContentUseCase {
  constructor(
    private contentRepository: ContentRepository,
    private categoryRepository: ContentCategoryRepository,
    private tagRepository: ContentTagRepository,
    private slugService: SlugService
  ) {}

  async execute(request: UpdateContentRequest): Promise<UpdateContentResponse> {
    try {
      // Validar os dados de entrada
      if (!request.id) {
        return {
          success: false,
          error: 'O ID do conteúdo é obrigatório.',
        };
      }

      if (!request.userId) {
        return {
          success: false,
          error: 'O ID do usuário é obrigatório.',
        };
      }

      // Obter conteúdo
      const content = await this.contentRepository.getById(request.id);

      if (!content) {
        return {
          success: false,
          error: `Conteúdo com ID ${request.id} não encontrado.`,
        };
      }

      // Verificar slug
      let slug = request.slug;

      if (slug && slug !== content.slug) {
        slug = this.slugService.normalize(slug);

        // Verificar se o slug já existe
        const slugExists = await this.contentRepository.slugExists(slug, content.id);

        if (slugExists) {
          // Adicionar um sufixo único ao slug
          slug = `${slug}-${Date.now().toString().slice(-6)}`;
        }
      }

      // Validar categorias
      if (request.categoryIds && request.categoryIds.length > 0) {
        for (const categoryId of request.categoryIds) {
          const category = await this.categoryRepository.getById(categoryId);

          if (!category) {
            return {
              success: false,
              error: `Categoria com ID ${categoryId} não encontrada.`,
            };
          }
        }
      }

      // Validar tags
      if (request.tagIds && request.tagIds.length > 0) {
        for (const tagId of request.tagIds) {
          const tag = await this.tagRepository.getById(tagId);

          if (!tag) {
            return {
              success: false,
              error: `Tag com ID ${tagId} não encontrada.`,
            };
          }
        }
      }

      // Criar versão se solicitado
      let version: ContentVersion | undefined;

      if (request.createVersion) {
        version = {
          id: uuidv4(),
          contentId: content.id,
          versionNumber: content.currentVersionNumber + 1,
          title: content.title,
          body: content.body,
          meta: content.meta,
          seo: content.seo,
          createdAt: new Date(),
          createdBy: request.userId,
          comment: request.versionComment,
        };

        // Salvar versão
        await this.contentRepository.createVersion(version);

        // Atualizar versão atual do conteúdo
        content.updateVersion(version.id, version.versionNumber);
      }

      // Atualizar conteúdo
      content.update({
        title: request.title,
        body: request.body,
        meta: request.meta,
        seo: request.seo,
        categoryIds: request.categoryIds,
        tagIds: request.tagIds,
        updatedBy: request.userId,
      });

      // Atualizar slug se fornecido
      if (slug && slug !== content.slug) {
        content.slug = slug;
      }

      // Salvar conteúdo
      const updatedContent = await this.contentRepository.update(content);

      return {
        success: true,
        data: updatedContent,
        version,
      };
    } catch (error) {
      console.error('Erro ao atualizar conteúdo:', error);

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Erro desconhecido ao atualizar conteúdo.',
      };
    }
  }
}
