import {
  WebhookDelivery,
  WebhookDeliveryStatus,
  WebhookEvent,
  WebhookSubscription,
} from '@models/WebhookSubscription';
// src/services/outgoingWebhookService.ts
import { webhookRepository } from '@repositories/webhookRepository';
import { generateHmacSignature, getCurrentTimestamp } from '@utils/crypto';
import { logger } from '@utils/logger';
import axios, { AxiosRequestConfig, AxiosResponse, AxiosError } from 'axios';

/**
 * Interface para opções de envio de webhook
 */
interface SendWebhookOptions {
  retryOnFailure?: boolean;
  maxRetries?: number;
  retryDelayMs?: number;
}

/**
 * Serviço para gerenciamento e envio de webhooks para sistemas externos
 */
export const outgoingWebhookService = {
  /**
   * Envia um evento de webhook para todas as assinaturas ativas
   * @param event - Tipo de evento
   * @param payload - Dados do evento
   * @param options - Opções de envio
   * @returns Array com resultados de entrega
   */
  async sendWebhookEvent(
    event: WebhookEvent,
    payload: Record<string, any>,
    options: SendWebhookOptions = {}
  ): Promise<WebhookDelivery[]> {
    try {
      // Encontrar todas as assinaturas ativas para este evento
      const subscriptions = await webhookRepository.findActiveSubscriptionsForEvent(event);

      if (subscriptions.length === 0) {
        logger.debug(`Nenhuma assinatura ativa encontrada para o evento ${event}`);
        return [];
      }

      // Enviar webhook para cada assinatura
      const deliveryPromises = subscriptions.map((subscription) =>
        this.sendToSubscription(subscription, event, payload, options)
      );

      // Aguardar todas as entregas
      return await Promise.all(deliveryPromises);
    } catch (error) {
      logger.error(`Erro ao enviar evento de webhook ${event}:`, error);
      throw error;
    }
  },

  /**
   * Envia um webhook para uma assinatura específica
   * @param subscription - Assinatura de webhook
   * @param event - Tipo de evento
   * @param payload - Dados do evento
   * @param options - Opções de envio
   * @returns Resultado da entrega
   */
  async sendToSubscription(
    subscription: WebhookSubscription,
    event: WebhookEvent,
    payload: Record<string, any>,
    options: SendWebhookOptions = {}
  ): Promise<WebhookDelivery> {
    // Criar registro de entrega
    const delivery = await webhookRepository.createDelivery({
      subscriptionId: subscription.id,
      event,
      payload,
    });

    try {
      // Preparar payload com metadados
      const webhookPayload = {
        id: delivery.id,
        event,
        timestamp: getCurrentTimestamp(),
        data: payload,
      };

      // Converter payload para string
      const payloadString = JSON.stringify(webhookPayload);

      // Gerar assinatura
      const signature = generateHmacSignature(payloadString, subscription.secretKey);

      // Configurar cabeçalhos
      const headers = {
        'Content-Type': 'application/json',
        'X-Webhook-Signature': signature,
        'X-Webhook-Event': event,
        'X-Webhook-ID': delivery.id,
        'X-Webhook-Timestamp': webhookPayload.timestamp.toString(),
        ...(subscription.headers || {}),
      };

      // Configurar requisição
      const requestConfig: AxiosRequestConfig = {
        headers,
        timeout: 10000, // 10 segundos de timeout
      };

      // Enviar requisição
      const response = await axios.post(subscription.url, webhookPayload, requestConfig);

      // Atualizar registro de entrega com sucesso
      const updatedDelivery = await webhookRepository.updateDelivery(delivery.id, {
        responseCode: response.status,
        responseBody: JSON.stringify({
          status: response.status,
          statusText: response.statusText,
          headers: response.headers,
          data: response.data,
        }),
        status: WebhookDeliveryStatus.SUCCESS,
        completedAt: new Date(),
      });

      // Atualizar timestamp de último uso da assinatura
      await webhookRepository.updateLastUsed(subscription.id);

      logger.info(`Webhook ${delivery.id} enviado com sucesso para ${subscription.url}`);
      return updatedDelivery;
    } catch (error) {
      // Tratar erro
      const axiosError = error as AxiosError;
      const errorMessage = axiosError.response
        ? `Status: ${axiosError.response.status}, Resposta: ${JSON.stringify(axiosError.response.data)}`
        : axiosError.message;

      logger.error(
        `Erro ao enviar webhook ${delivery.id} para ${subscription.url}: ${errorMessage}`
      );

      // Atualizar registro de entrega com falha
      const updatedDelivery = await webhookRepository.updateDelivery(delivery.id, {
        responseCode: axiosError.response?.status,
        responseBody: axiosError.response
          ? JSON.stringify({
              status: axiosError.response.status,
              statusText: axiosError.response.statusText,
              headers: axiosError.response.headers,
              data: axiosError.response.data,
            })
          : undefined,
        status: WebhookDeliveryStatus.FAILED,
        errorMessage,
        completedAt: new Date(),
      });

      // Tentar novamente se configurado
      if (options.retryOnFailure && updatedDelivery.attemptCount < (options.maxRetries || 3)) {
        // Agendar nova tentativa
        const retryDelay = options.retryDelayMs || 60000; // 1 minuto por padrão

        logger.info(`Agendando nova tentativa para webhook ${delivery.id} em ${retryDelay}ms`);

        setTimeout(() => {
          this.retryDelivery(updatedDelivery.id, options).catch((retryError) => {
            logger.error(`Erro ao retentar webhook ${delivery.id}:`, retryError);
          });
        }, retryDelay);
      }

      return updatedDelivery;
    }
  },

  /**
   * Tenta novamente uma entrega de webhook
   * @param deliveryId - ID da entrega
   * @param options - Opções de envio
   * @returns Resultado da nova tentativa
   */
  async retryDelivery(
    deliveryId: string,
    options: SendWebhookOptions = {}
  ): Promise<WebhookDelivery> {
    // Obter entrega
    const delivery = await webhookRepository.getDeliveryById(deliveryId);

    if (!delivery) {
      throw new Error(`Entrega de webhook ${deliveryId} não encontrada`);
    }

    // Obter assinatura
    const subscription = await webhookRepository.getSubscriptionById(delivery.subscriptionId);

    if (!subscription) {
      throw new Error(`Assinatura de webhook ${delivery.subscriptionId} não encontrada`);
    }

    // Atualizar contador de tentativas
    await webhookRepository.updateDelivery(deliveryId, {
      status: WebhookDeliveryStatus.RETRYING,
      attemptCount: delivery.attemptCount + 1,
    });

    // Tentar enviar novamente
    return this.sendToSubscription(subscription, delivery.event, delivery.payload, options);
  },

  /**
   * Verifica se uma assinatura de webhook existe
   * @param id - ID da assinatura
   * @returns true se existir
   */
  async subscriptionExists(id: string): Promise<boolean> {
    const subscription = await webhookRepository.getSubscriptionById(id);
    return !!subscription;
  },
};
