---
import { PageTransition } from '../../../components/transitions';
import DaisyButton from '../../../components/ui/DaisyButton.astro';
import DaisyCard from '../../../components/ui/DaisyCard.astro';
import DaisyTabs from '../../../components/ui/DaisyTabs.astro';
import Breadcrumbs from '../../../components/ui/navigation/Breadcrumbs.astro';
import { FiscalDocumentStatus, FiscalDocumentType } from '../../../domain/entities/FiscalDocument';
import { FiscalDocumentRepository } from '../../../domain/repositories/FiscalDocumentRepository';
import { PostgresFiscalDocumentRepository } from '../../../infrastructure/database/repositories/PostgresFiscalDocumentRepository';
/**
 * Página de Repositório de Documentos Fiscais
 *
 * Interface para gerenciar o repositório de documentos fiscais.
 * Parte da implementação da tarefa 8.7.2 - Gestão de documentos fiscais
 */
import BaseLayout from '../../../layouts/BaseLayout.astro';
import { Container, Section } from '../../../layouts/grid';

// Título da página
const title = 'Repositório de Documentos Fiscais';

// Breadcrumbs para a página atual
const breadcrumbItems = [
  { href: '/', label: 'Início' },
  { href: '/admin', label: 'Administração' },
  { href: '/admin/fiscal', label: 'Gestão Fiscal' },
  { label: 'Repositório' },
];

// Obter parâmetros de consulta
const type = (Astro.url.searchParams.get('type') as FiscalDocumentType) || '';
const status = (Astro.url.searchParams.get('status') as FiscalDocumentStatus) || 'ISSUED';
const query = Astro.url.searchParams.get('q') || '';
const page = Number.parseInt(Astro.url.searchParams.get('page') || '1');
const limit = Number.parseInt(Astro.url.searchParams.get('limit') || '20');

// Inicializar repositório
const fiscalDocumentRepository: FiscalDocumentRepository = new PostgresFiscalDocumentRepository();

// Construir filtro
const filter: any = {
  status: status || 'ISSUED', // Por padrão, mostrar apenas documentos emitidos
};

if (type) {
  filter.type = type;
}

if (query) {
  if (query.match(/^\d{1,3}(\.\d{3})*,\d{2}$/)) {
    // Formato de valor monetário (ex: 1.234,56)
    const value = Number.parseFloat(query.replace(/\./g, '').replace(',', '.'));
    filter.minValue = value * 0.95; // 5% de tolerância para baixo
    filter.maxValue = value * 1.05; // 5% de tolerância para cima
  } else if (query.match(/^\d{2}\/\d{2}\/\d{4}$/)) {
    // Formato de data (ex: 01/01/2023)
    const parts = query.split('/');
    const date = new Date(`${parts[2]}-${parts[1]}-${parts[0]}`);
    filter.startDate = date;
    filter.endDate = new Date(date.getTime() + 24 * 60 * 60 * 1000);
  } else if (query.match(/^\d{1,6}$/)) {
    // Formato de número de documento
    filter.number = query;
  } else if (query.match(/^\d{11}$/) || query.match(/^\d{14}$/)) {
    // Formato de CPF ou CNPJ
    filter.customerDocumentNumber = query;
  } else {
    // Busca por nome do cliente
    filter.customerName = query;
  }
}

// Obter documentos fiscais
const result = await fiscalDocumentRepository.find(
  filter,
  { field: 'issueDate', direction: 'desc' },
  { page, limit }
);

const documents = result.documents;
const totalItems = result.total;
const totalPages = result.totalPages;

// Tipos de documentos disponíveis
const documentTypes = [
  { value: '', label: 'Todos os tipos' },
  { value: 'NFE', label: 'NF-e' },
  { value: 'NFSE', label: 'NFS-e' },
  { value: 'NFCE', label: 'NFC-e' },
];

// Status disponíveis
const documentStatuses = [
  { value: 'ISSUED', label: 'Emitidos' },
  { value: 'CANCELLED', label: 'Cancelados' },
];

// Função para formatar data
const formatDate = (dateString: string | null | undefined): string => {
  if (!dateString) return '-';
  const date = new Date(dateString);
  return date.toLocaleDateString('pt-BR', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
  });
};

// Função para formatar valor monetário
const formatCurrency = (value: number): string => {
  return value.toLocaleString('pt-BR', {
    style: 'currency',
    currency: 'BRL',
  });
};

// Função para formatar documento do cliente
const formatDocument = (type: 'CPF' | 'CNPJ', number: string): string => {
  if (type === 'CPF') {
    return number.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, '$1.$2.$3-$4');
  }
  return number.replace(/(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})/, '$1.$2.$3/$4-$5');
};

// Função para obter label do tipo de documento
const getTypeLabel = (type: FiscalDocumentType): string => {
  const typeLabels: Record<FiscalDocumentType, string> = {
    NFE: 'NF-e',
    NFSE: 'NFS-e',
    NFCE: 'NFC-e',
  };

  return typeLabels[type] || type;
};
---

<BaseLayout title={title} enableViewTransitions={true} transitionType="fade">
  <Container>
    <PageTransition type="fade" duration="0.5s">
      <Section>
        <Breadcrumbs items={breadcrumbItems} class="mb-6" />
        
        <div class="flex justify-between items-center mb-6">
          <h1 class="text-3xl font-bold">{title}</h1>
          
          <div class="flex gap-2">
            <DaisyButton 
              href="/admin/fiscal/relatorios" 
              variant="outline" 
              icon="bar-chart-2"
              text="Relatórios"
            />
            
            <DaisyButton 
              href="/admin/fiscal" 
              variant="outline" 
              icon="arrow-left"
              text="Voltar"
            />
          </div>
        </div>
        
        <div class="bg-base-200 p-4 rounded-lg mb-6">
          <form action="/admin/fiscal/repositorio" method="GET" class="flex flex-col md:flex-row gap-4">
            <div class="form-control flex-1">
              <div class="input-group">
                <input 
                  type="text" 
                  name="q" 
                  placeholder="Buscar por número, cliente, CPF/CNPJ, valor ou data..." 
                  class="input input-bordered w-full" 
                  value={query}
                />
                <button type="submit" class="btn btn-square">
                  <i class="icon icon-search"></i>
                </button>
              </div>
            </div>
            
            <div class="form-control">
              <select name="type" class="select select-bordered">
                {documentTypes.map(docType => (
                  <option 
                    value={docType.value} 
                    selected={docType.value === type}
                  >
                    {docType.label}
                  </option>
                ))}
              </select>
            </div>
            
            <div class="form-control">
              <select name="status" class="select select-bordered">
                {documentStatuses.map(docStatus => (
                  <option 
                    value={docStatus.value} 
                    selected={docStatus.value === status}
                  >
                    {docStatus.label}
                  </option>
                ))}
              </select>
            </div>
            
            <button type="submit" class="btn btn-primary">
              Filtrar
            </button>
            
            {(query || type || status !== 'ISSUED') && (
              <a href="/admin/fiscal/repositorio" class="btn btn-outline">
                Limpar Filtros
              </a>
            )}
          </form>
        </div>
        
        <DaisyTabs
          tabs={[
            { id: 'ISSUED', label: 'Documentos Emitidos', active: status === 'ISSUED' },
            { id: 'CANCELLED', label: 'Documentos Cancelados', active: status === 'CANCELLED' }
          ]}
          baseUrl={`/admin/fiscal/repositorio?type=${type}&q=${query}`}
          paramName="status"
        />
        
        <div class="overflow-x-auto mt-4">
          {documents.length === 0 ? (
            <div class="text-center py-12">
              <div class="text-4xl text-gray-400 mb-4">
                <i class="icon icon-file-text"></i>
              </div>
              <h3 class="text-xl font-bold mb-2">Nenhum documento fiscal encontrado</h3>
              <p class="text-gray-500 mb-4">
                {query || type
                  ? `Não encontramos resultados para os filtros aplicados.` 
                  : 'Não há documentos fiscais no repositório.'}
              </p>
            </div>
          ) : (
            <table class="table w-full">
              <thead>
                <tr>
                  <th>Número</th>
                  <th>Tipo</th>
                  <th>Cliente</th>
                  <th>CPF/CNPJ</th>
                  <th>Emissão</th>
                  <th>Valor</th>
                  <th>Ações</th>
                </tr>
              </thead>
              <tbody>
                {documents.map(document => (
                  <tr>
                    <td>
                      <div class="font-medium">
                        {document.number || <span class="text-gray-400">Não emitido</span>}
                      </div>
                      {document.series && <div class="text-xs text-gray-500">Série: {document.series}</div>}
                    </td>
                    <td>
                      <div class="badge badge-outline">
                        {getTypeLabel(document.type)}
                      </div>
                    </td>
                    <td>{document.customer.name}</td>
                    <td>{formatDocument(document.customer.documentType, document.customer.documentNumber)}</td>
                    <td>{formatDate(document.issueDate?.toString())}</td>
                    <td>{formatCurrency(document.finalValue)}</td>
                    <td>
                      <div class="flex gap-1">
                        <a 
                          href={`/admin/fiscal/${document.id}`} 
                          class="btn btn-sm btn-ghost"
                          aria-label="Visualizar"
                        >
                          <i class="icon icon-eye"></i>
                        </a>
                        
                        {document.pdfUrl && (
                          <a 
                            href={document.pdfUrl} 
                            target="_blank" 
                            class="btn btn-sm btn-ghost text-info"
                            aria-label="Download PDF"
                          >
                            <i class="icon icon-download"></i>
                          </a>
                        )}
                        
                        <button 
                          class="btn btn-sm btn-ghost text-primary"
                          data-action="xml"
                          data-id={document.id}
                          aria-label="Download XML"
                        >
                          <i class="icon icon-code"></i>
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          )}
        </div>
        
        {totalPages > 1 && (
          <div class="flex justify-center mt-8">
            <div class="join">
              {Array.from({ length: totalPages }, (_, i) => i + 1).map(p => (
                <a 
                  href={`/admin/fiscal/repositorio?page=${p}&limit=${limit}&type=${type}&status=${status}&q=${query}`} 
                  class={`join-item btn ${p === page ? 'btn-active' : ''}`}
                >
                  {p}
                </a>
              ))}
            </div>
          </div>
        )}
      </Section>
    </PageTransition>
  </Container>
</BaseLayout>

<script>
  // Script para funcionalidade da página de repositório fiscal
  document.addEventListener('DOMContentLoaded', () => {
    // Botões de ação
    const actionButtons = document.querySelectorAll('[data-action]');
    
    actionButtons.forEach(button => {
      button.addEventListener('click', async () => {
        const action = button.getAttribute('data-action');
        const id = button.getAttribute('data-id');
        
        if (!action || !id) {
          return;
        }
        
        if (action === 'xml') {
          try {
            const response = await fetch(`/api/fiscal/xml/${id}`);
            
            if (response.ok) {
              const blob = await response.blob();
              const url = window.URL.createObjectURL(blob);
              const a = document.createElement('a');
              a.href = url;
              a.download = `documento-fiscal-${id}.xml`;
              document.body.appendChild(a);
              a.click();
              window.URL.revokeObjectURL(url);
              document.body.removeChild(a);
            } else {
              const data = await response.json();
              alert(`Erro ao baixar XML: ${data.error || 'Erro desconhecido'}`);
            }
          } catch (error) {
            console.error('Erro ao baixar XML:', error);
            alert('Erro ao baixar XML. Por favor, tente novamente.');
          }
        }
      });
    });
  });
</script>
