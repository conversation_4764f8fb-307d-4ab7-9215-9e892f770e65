---
/**
 * Componente de Seção
 *
 * Este componente cria uma seção de conteúdo com título, subtítulo e container opcional.
 */

import Container from './Container.astro';

interface Props {
  title?: string;
  subtitle?: string;
  description?: string;
  id?: string;
  container?: boolean;
  containerSize?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | 'full' | 'none';
  py?: number;
  px?: number;
  pt?: number;
  pb?: number;
  mt?: number;
  mb?: number;
  background?:
    | 'primary'
    | 'secondary'
    | 'accent'
    | 'neutral'
    | 'base-100'
    | 'base-200'
    | 'base-300'
    | 'none';
  textColor?: string;
  centered?: boolean;
  class?: string;
  titleClass?: string;
  subtitleClass?: string;
  contentClass?: string;
}

const {
  title,
  subtitle,
  description,
  id,
  container = true,
  containerSize = 'lg',
  py = 12,
  px,
  pt,
  pb,
  mt,
  mb,
  background = 'none',
  textColor,
  centered = false,
  class: className = '',
  titleClass = '',
  subtitleClass = '',
  contentClass = '',
} = Astro.props;

// Gerar classes para a seção
const paddingY = py !== undefined && !pt && !pb ? `py-${py}` : '';
const paddingX = px !== undefined ? `px-${px}` : '';
const paddingTop = pt !== undefined ? `pt-${pt}` : '';
const paddingBottom = pb !== undefined ? `pb-${pb}` : '';
const marginTop = mt !== undefined ? `mt-${mt}` : '';
const marginBottom = mb !== undefined ? `mb-${mb}` : '';
const bgClass = background !== 'none' ? `bg-${background}` : '';
const textClass = textColor ? `text-${textColor}` : '';
const centeredClass = centered ? 'text-center' : '';

// Combinar todas as classes
const sectionClasses = [
  paddingY,
  paddingX,
  paddingTop,
  paddingBottom,
  marginTop,
  marginBottom,
  bgClass,
  textClass,
  className,
]
  .filter(Boolean)
  .join(' ');

// Classes para o título e subtítulo
const titleClasses = ['text-2xl md:text-3xl font-bold', centered ? 'text-center' : '', titleClass]
  .filter(Boolean)
  .join(' ');

const subtitleClasses = ['text-lg mt-2', centered ? 'text-center' : '', subtitleClass]
  .filter(Boolean)
  .join(' ');

const descriptionClasses = ['mt-4', centered ? 'text-center' : '', subtitleClass]
  .filter(Boolean)
  .join(' ');

// Classes para o conteúdo
const contentClasses = ['mt-8', contentClass].filter(Boolean).join(' ');

// Verificar se há cabeçalho
const hasHeader = title || subtitle || description;
---

<section id={id} class={sectionClasses}>
  {container ? (
    <Container size={containerSize}>
      {hasHeader && (
        <div class="section-header mb-8">
          {title && <h2 class={titleClasses}>{title}</h2>}
          {subtitle && <h3 class={subtitleClasses}>{subtitle}</h3>}
          {description && <p class={descriptionClasses}>{description}</p>}
        </div>
      )}
      <div class={contentClasses}>
        <slot />
      </div>
    </Container>
  ) : (
    <>
      {hasHeader && (
        <div class="section-header mb-8">
          {title && <h2 class={titleClasses}>{title}</h2>}
          {subtitle && <h3 class={subtitleClasses}>{subtitle}</h3>}
          {description && <p class={descriptionClasses}>{description}</p>}
        </div>
      )}
      <div class={contentClasses}>
        <slot />
      </div>
    </>
  )}
</section>
