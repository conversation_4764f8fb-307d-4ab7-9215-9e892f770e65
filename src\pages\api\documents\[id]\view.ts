/**
 * API para visualização segura de documentos
 *
 * Este endpoint serve documentos PDF com verificação de token de acesso
 * e registro de visualizações.
 */

import { documentService } from '@services/documentService';
import { logger } from '@utils/logger';
import type { APIRoute } from 'astro';

/**
 * Verifica um token de acesso
 * @param token - Token de acesso
 * @returns Payload decodificado ou null se inválido
 */
function verifyAccessToken(
  token: string
): { userId: string; documentId: string; expiresAt: number } | null {
  try {
    // Decodificar token
    const payload = JSON.parse(atob(token));

    // Verificar expiração
    if (payload.expiresAt < Date.now()) {
      return null;
    }

    return {
      userId: payload.userId,
      documentId: payload.documentId,
      expiresAt: payload.expiresAt,
    };
  } catch (error) {
    return null;
  }
}

export const GET: APIRoute = async ({ params, request }) => {
  try {
    // Obter ID do documento
    const { id } = params;

    if (!id) {
      return new Response(JSON.stringify({ error: 'ID do documento não fornecido' }), {
        status: 400,
        headers: {
          'Content-Type': 'application/json',
        },
      });
    }

    // Obter token de acesso
    const url = new URL(request.url);
    const token = url.searchParams.get('token');

    if (!token) {
      return new Response(JSON.stringify({ error: 'Token de acesso não fornecido' }), {
        status: 401,
        headers: {
          'Content-Type': 'application/json',
        },
      });
    }

    // Verificar token
    const payload = verifyAccessToken(token);

    if (!payload) {
      return new Response(JSON.stringify({ error: 'Token de acesso inválido ou expirado' }), {
        status: 401,
        headers: {
          'Content-Type': 'application/json',
        },
      });
    }

    // Verificar se o token é para o documento correto
    if (payload.documentId !== id) {
      return new Response(
        JSON.stringify({ error: 'Token de acesso não corresponde ao documento solicitado' }),
        {
          status: 403,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    // Obter documento
    const document = await documentService.getDocumentById(id);

    if (!document) {
      return new Response(JSON.stringify({ error: 'Documento não encontrado' }), {
        status: 404,
        headers: {
          'Content-Type': 'application/json',
        },
      });
    }

    // Verificar permissão
    const hasAccess = await documentService.checkReadAccess(id, payload.userId);

    if (!hasAccess.allowed) {
      return new Response(JSON.stringify({ error: `Acesso negado: ${hasAccess.reason}` }), {
        status: 403,
        headers: {
          'Content-Type': 'application/json',
        },
      });
    }

    // Obter conteúdo do documento
    const content = await documentService.getDocumentContent(id, {
      userId: payload.userId,
      logDownload: false, // Registrar como visualização, não download
    });

    if (!content) {
      return new Response(
        JSON.stringify({ error: 'Não foi possível obter o conteúdo do documento' }),
        {
          status: 404,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    // Registrar visualização
    await documentService.logDocumentAccess(id, payload.userId, 'view');

    // Retornar documento
    return new Response(content, {
      status: 200,
      headers: {
        'Content-Type': document.type,
        'Content-Length': document.size.toString(),
        'Content-Disposition': `inline; filename="${encodeURIComponent(document.title)}.pdf"`,
        'Cache-Control': 'private, no-cache, no-store, must-revalidate',
        Pragma: 'no-cache',
        Expires: '0',
      },
    });
  } catch (error) {
    logger.error('Erro ao servir documento:', error);

    return new Response(JSON.stringify({ error: 'Erro interno do servidor' }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  }
};
