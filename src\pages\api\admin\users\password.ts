/**
 * API de Alteração de Senha de Usuário Administrador
 *
 * Endpoint para alteração de senha de usuários administradores.
 * Parte da implementação da tarefa 8.8.2 - Gestão de usuários
 */

import type { APIRoute } from 'astro';
import { AdminUserRepository } from '../../../../domain/repositories/AdminUserRepository';
import { PasswordService } from '../../../../domain/services/PasswordService';
import { TokenService } from '../../../../domain/services/TokenService';
import { ChangeAdminPasswordUseCase } from '../../../../domain/usecases/admin/ChangeAdminPasswordUseCase';
import { PostgresAdminUserRepository } from '../../../../infrastructure/database/repositories/PostgresAdminUserRepository';
import { BcryptPasswordService } from '../../../../infrastructure/services/BcryptPasswordService';
import { JwtTokenService } from '../../../../infrastructure/services/JwtTokenService';

// Inicializar repositório
const adminUserRepository: AdminUserRepository = new PostgresAdminUserRepository();

// Inicializar serviços
const passwordService: PasswordService = new BcryptPasswordService();
const tokenService: TokenService = new JwtTokenService(
  process.env.JWT_SECRET || 'default-secret-key',
  process.env.PASSWORD_RESET_SECRET || 'password-reset-secret-key',
  process.env.EMAIL_VERIFICATION_SECRET || 'email-verification-secret-key'
);

// Inicializar caso de uso
const changeAdminPasswordUseCase = new ChangeAdminPasswordUseCase(
  adminUserRepository,
  passwordService
);

// Verificar autenticação
const checkAuth = (request: Request): string | null => {
  const authHeader = request.headers.get('Authorization');

  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null;
  }

  const token = authHeader.substring(7);
  const payload = tokenService.verifyToken(token);

  if (!payload) {
    return null;
  }

  return payload.id;
};

export const POST: APIRoute = async ({ request }) => {
  try {
    // Verificar autenticação
    const userId = checkAuth(request);

    if (!userId) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Não autorizado. Faça login para continuar.',
        }),
        {
          status: 401,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    // Obter dados do corpo da requisição
    const body = await request.json();

    // Validar dados
    if (!body.currentPassword || !body.newPassword || !body.confirmPassword) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Todos os campos são obrigatórios.',
        }),
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    // Executar caso de uso
    const result = await changeAdminPasswordUseCase.execute({
      id: userId,
      currentPassword: body.currentPassword,
      newPassword: body.newPassword,
      confirmPassword: body.confirmPassword,
    });

    if (result.success) {
      return new Response(
        JSON.stringify({
          success: true,
          message: 'Senha alterada com sucesso.',
        }),
        {
          status: 200,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }
    return new Response(
      JSON.stringify({
        success: false,
        error: result.error || 'Erro desconhecido ao alterar senha.',
      }),
      {
        status: 400,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  } catch (error) {
    console.error('Erro ao processar alteração de senha:', error);

    return new Response(
      JSON.stringify({
        success: false,
        error: 'Ocorreu um erro ao processar a alteração de senha. Por favor, tente novamente.',
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  }
};
