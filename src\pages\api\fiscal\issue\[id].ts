/**
 * API de Emissão de Documento Fiscal
 *
 * Endpoint para emitir um documento fiscal.
 * Parte da implementação da tarefa 8.7.1 - Integração fiscal
 */

import type { APIRoute } from 'astro';
import { FiscalDocumentRepository } from '../../../../domain/repositories/FiscalDocumentRepository';
import { FiscalProviderService } from '../../../../domain/services/FiscalProviderService';
import { IssueFiscalDocumentUseCase } from '../../../../domain/usecases/fiscal/IssueFiscalDocumentUseCase';
import { PostgresFiscalDocumentRepository } from '../../../../infrastructure/database/repositories/PostgresFiscalDocumentRepository';
import { EfiPayFiscalProvider } from '../../../../infrastructure/services/EfiPayFiscalProvider';

// Inicializar repositório
const fiscalDocumentRepository: FiscalDocumentRepository = new PostgresFiscalDocumentRepository();

// Inicializar provedor fiscal
const fiscalProviderService: FiscalProviderService = new EfiPayFiscalProvider();

// Inicializar caso de uso
const issueFiscalDocumentUseCase = new IssueFiscalDocumentUseCase(
  fiscalDocumentRepository,
  fiscalProviderService
);

// Configurar provedor fiscal
(async () => {
  try {
    // Em um cenário real, estas configurações viriam do banco de dados
    await fiscalProviderService.initialize({
      apiKey: process.env.EFI_PAY_API_KEY || 'sandbox_api_key',
      apiSecret: process.env.EFI_PAY_API_SECRET || 'sandbox_api_secret',
      environment: 'homologation',
      companyDocument: '12345678000199',
      companyName: 'Estação da Alfabetização LTDA',
      timeout: 30000,
    });
  } catch (error) {
    console.error('Erro ao inicializar provedor fiscal:', error);
  }
})();

export const POST: APIRoute = async ({ params, request }) => {
  try {
    const { id } = params;

    if (!id) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'ID do documento é obrigatório.',
        }),
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    // Emitir documento fiscal
    const result = await issueFiscalDocumentUseCase.execute({
      documentId: id,
    });

    if (result.success) {
      return new Response(
        JSON.stringify({
          success: true,
          document: result.document,
        }),
        {
          status: 200,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }
    return new Response(
      JSON.stringify({
        success: false,
        error: result.error || 'Erro desconhecido ao emitir documento fiscal.',
      }),
      {
        status: 400,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  } catch (error) {
    console.error('Erro ao processar emissão de documento fiscal:', error);

    return new Response(
      JSON.stringify({
        success: false,
        error:
          'Ocorreu um erro ao processar a emissão do documento fiscal. Por favor, tente novamente.',
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  }
};
