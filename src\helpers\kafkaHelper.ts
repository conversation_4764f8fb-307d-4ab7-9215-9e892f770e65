import { consumer, getTopic<PERSON><PERSON>, producer, sendMessage } from '@config/kafka';
import { logger } from '@utils/logger';

/**
 * Helper para interação com o Kafka
 */
export const kafkaHelper = {
  /**
   * Conecta ao Kafka
   */
  connect: async (): Promise<void> => {
    await producer.connect();
    await consumer.connect();
  },

  /**
   * Desconecta do Kafka
   */
  disconnect: async (): Promise<void> => {
    await producer.disconnect();
    await consumer.disconnect();
  },

  /**
   * Envia uma mensagem para um tópico específico
   * @param topic - Nome do tópico
   * @param message - Mensagem a ser enviada
   * @param key - Chave da mensagem (opcional)
   */
  sendMessage: async (
    topic: string,
    message: Record<string, unknown>,
    key?: string
  ): Promise<void> => {
    try {
      await producer.send({
        topic,
        messages: [
          {
            key: key || undefined,
            value: JSON.stringify(message),
            headers: {
              timestamp: Date.now().toString(),
              'content-type': 'application/json',
            },
          },
        ],
      });

      logger.debug(`Mensagem enviada para tópico ${topic}`, { key });
    } catch (error) {
      logger.error(`Erro ao enviar mensagem para tópico ${topic}:`, error);
      throw error;
    }
  },

  /**
   * Envia uma mensagem usando a estrutura de domínios
   * @param domain - Domínio da mensagem
   * @param entity - Entidade da mensagem
   * @param event - Evento da mensagem
   * @param data - Dados da mensagem
   * @param key - Chave da mensagem (opcional)
   */
  sendDomainMessage: async (
    domain: string,
    entity: string,
    event: string,
    data: Record<string, unknown>,
    key?: string
  ): Promise<void> => {
    try {
      await sendMessage(domain, entity, event, key || '', data);
    } catch (error) {
      logger.error(`Erro ao enviar mensagem de domínio ${domain}.${entity}.${event}:`, error);
      throw error;
    }
  },

  /**
   * Inscreve-se em um tópico específico
   * @param topic - Nome do tópico
   * @param callback - Função de callback para processar mensagens
   */
  subscribe: async (
    topic: string,
    callback: (message: Record<string, unknown>, key?: string) => Promise<void>
  ): Promise<void> => {
    try {
      await consumer.subscribe({ topic, fromBeginning: true });

      await consumer.run({
        eachMessage: async ({ topic, partition, message }) => {
          try {
            if (message.value) {
              const value = JSON.parse(message.value.toString());
              const key = message.key?.toString();

              logger.debug(`Mensagem recebida do tópico ${topic}`, {
                partition,
                offset: message.offset,
                key,
              });

              await callback(value, key);
            }
          } catch (error) {
            logger.error(`Erro ao processar mensagem do tópico ${topic}:`, error);
          }
        },
      });

      logger.info(`Inscrito no tópico ${topic}`);
    } catch (error) {
      logger.error(`Erro ao inscrever-se no tópico ${topic}:`, error);
      throw error;
    }
  },

  /**
   * Inscreve-se em um tópico usando a estrutura de domínios
   * @param domain - Domínio da mensagem
   * @param entity - Entidade da mensagem
   * @param event - Evento da mensagem
   * @param callback - Função de callback para processar mensagens
   */
  subscribeToDomain: async (
    domain: string,
    entity: string,
    event: string,
    callback: (message: Record<string, unknown>, key?: string) => Promise<void>
  ): Promise<void> => {
    try {
      const topic = getTopicName(domain, entity, event);
      await kafkaHelper.subscribe(topic, callback);
    } catch (error) {
      logger.error(`Erro ao inscrever-se no domínio ${domain}.${entity}.${event}:`, error);
      throw error;
    }
  },
};
