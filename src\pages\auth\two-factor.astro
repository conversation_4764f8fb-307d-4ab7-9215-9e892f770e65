---
/**
 * Página de verificação de autenticação de dois fatores
 *
 * Esta página é exibida durante o login quando o usuário tem 2FA ativado.
 * Permite inserir um código TOTP ou código de backup para completar o login.
 *
 * Parte da implementação da tarefa 6.1.2 - Implementação de 2FA
 */

import { verifyTwoFactorToken } from '../../actions/twoFactorAuthAction';
import Layout from '../../layouts/AuthLayout.astro';
import { csrfHelper } from '../../utils/csrfHelper';

// Verificar se há uma sessão temporária
const tempSession = Astro.cookies.get('temp_auth_session')?.value;

// Se não houver sessão temporária, redirecionar para login
if (!tempSession) {
  return Astro.redirect('/auth/login');
}

// Decodificar sessão temporária
const sessionData = JSON.parse(Buffer.from(tempSession, 'base64').toString());

// Verificar se a sessão requer 2FA
if (!sessionData.requires2FA) {
  return Astro.redirect('/auth/login');
}

// Gerar token CSRF
const csrfToken = await csrfHelper.generateToken(Astro);

// Processar submissão do formulário
let error = '';
const success = false;

if (Astro.request.method === 'POST') {
  const formData = await Astro.request.formData();
  const token = formData.get('token')?.toString() || '';
  const csrf = formData.get('_csrf')?.toString() || '';

  const result = await verifyTwoFactorToken.submit(
    {
      token,
      _csrf: csrf,
    },
    Astro
  );

  if (result.success) {
    return Astro.redirect(result.redirect || '/dashboard');
  }
  error = result.error || 'Erro ao verificar código';
}
---

<Layout title="Verificação de Dois Fatores">
  <div class="auth-container">
    <div class="auth-card">
      <div class="auth-header">
        <h1>Verificação de Dois Fatores</h1>
        <p>Digite o código do seu aplicativo autenticador ou um código de backup para continuar.</p>
      </div>
      
      {error && (
        <div class="alert alert-danger">
          {error}
        </div>
      )}
      
      <form method="POST" class="auth-form">
        <input type="hidden" name="_csrf" value={csrfToken} />
        
        <div class="form-group">
          <label for="token">Código de verificação</label>
          <input 
            type="text" 
            id="token" 
            name="token" 
            placeholder="Digite o código de 6 dígitos" 
            autocomplete="one-time-code"
            inputmode="numeric"
            pattern="[0-9]*"
            maxlength="8"
            autofocus
            required
          />
          <small>Digite o código de 6 dígitos do seu aplicativo autenticador ou um código de backup.</small>
        </div>
        
        <button type="submit" class="btn btn-primary btn-block">Verificar</button>
        
        <div class="auth-links">
          <a href="/auth/login" class="link">Voltar para o login</a>
        </div>
      </form>
    </div>
    
    <div class="auth-help">
      <h3>Problemas para acessar?</h3>
      <ul>
        <li>Verifique se o código está correto</li>
        <li>Certifique-se de que o relógio do seu dispositivo está sincronizado</li>
        <li>Tente usar um código de backup se você tiver</li>
        <li>Entre em contato com o suporte se continuar tendo problemas</li>
      </ul>
    </div>
  </div>
</Layout>

<style>
  .auth-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    max-width: 1200px;
    margin: 0 auto;
  }
  
  .auth-card {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    padding: 2rem;
    width: 100%;
    max-width: 480px;
    margin-bottom: 2rem;
  }
  
  .auth-header {
    text-align: center;
    margin-bottom: 2rem;
  }
  
  .auth-header h1 {
    font-size: 1.8rem;
    color: var(--color-primary);
    margin-bottom: 0.5rem;
  }
  
  .auth-header p {
    color: var(--color-text-muted);
    font-size: 0.95rem;
  }
  
  .auth-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
  }
  
  .form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .form-group label {
    font-weight: 500;
    font-size: 0.95rem;
  }
  
  .form-group input {
    padding: 0.75rem 1rem;
    border: 1px solid var(--color-border);
    border-radius: 4px;
    font-size: 1rem;
    transition: border-color 0.2s;
  }
  
  .form-group input:focus {
    border-color: var(--color-primary);
    outline: none;
    box-shadow: 0 0 0 2px rgba(var(--color-primary-rgb), 0.2);
  }
  
  .form-group small {
    font-size: 0.8rem;
    color: var(--color-text-muted);
  }
  
  .btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 4px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s, transform 0.1s;
  }
  
  .btn:active {
    transform: translateY(1px);
  }
  
  .btn-primary {
    background-color: var(--color-primary);
    color: white;
  }
  
  .btn-primary:hover {
    background-color: var(--color-primary-dark);
  }
  
  .btn-block {
    width: 100%;
  }
  
  .auth-links {
    display: flex;
    justify-content: center;
    margin-top: 1rem;
    font-size: 0.9rem;
  }
  
  .link {
    color: var(--color-primary);
    text-decoration: none;
  }
  
  .link:hover {
    text-decoration: underline;
  }
  
  .alert {
    padding: 0.75rem 1rem;
    border-radius: 4px;
    margin-bottom: 1.5rem;
  }
  
  .alert-danger {
    background-color: rgba(220, 53, 69, 0.1);
    color: #dc3545;
    border: 1px solid rgba(220, 53, 69, 0.2);
  }
  
  .auth-help {
    background-color: rgba(var(--color-primary-rgb), 0.05);
    border-radius: 8px;
    padding: 1.5rem;
    width: 100%;
    max-width: 480px;
  }
  
  .auth-help h3 {
    font-size: 1.2rem;
    margin-bottom: 1rem;
    color: var(--color-primary);
  }
  
  .auth-help ul {
    padding-left: 1.5rem;
    color: var(--color-text);
  }
  
  .auth-help li {
    margin-bottom: 0.5rem;
  }
  
  @media (max-width: 768px) {
    .auth-container {
      padding: 1rem;
    }
    
    .auth-card, .auth-help {
      padding: 1.5rem;
    }
  }
</style>
