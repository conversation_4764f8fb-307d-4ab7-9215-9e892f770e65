/**
 * Produtor de eventos de pedidos
 *
 * Este serviço é responsável por produzir eventos relacionados a pedidos
 * para o Kafka.
 */

import { OrderEvent, eventProducerService } from '@services/eventProducerService';
import { logger } from '@utils/logger';

/**
 * Interface para item de pedido
 */
interface OrderItem {
  productId: string;
  name: string;
  quantity: number;
  price: number;
}

/**
 * Interface para dados de pedido
 */
interface OrderData {
  orderId: string;
  userId: string;
  status: string;
  total: number;
  items?: OrderItem[];
  metadata?: Record<string, unknown>;
}

/**
 * Produtor de eventos de pedido
 */
export const orderEventProducer = {
  /**
   * Envia evento de criação de pedido
   * @param orderData - Dados do pedido
   */
  async orderCreated(orderData: OrderData): Promise<void> {
    try {
      const event: OrderEvent = {
        ...eventProducerService.createBaseEvent(
          `order-created-${orderData.orderId}`,
          orderData.metadata || {}
        ),
        orderId: orderData.orderId,
        userId: orderData.userId,
        status: orderData.status,
        total: orderData.total,
        items: orderData.items?.map((item) => ({
          productId: item.productId,
          quantity: item.quantity,
          price: item.price,
        })),
      };

      await eventProducerService.sendOrderEvent('created', event);

      logger.info(`Evento de criação de pedido enviado: ${orderData.orderId}`);
    } catch (error) {
      logger.error(`Erro ao enviar evento de criação de pedido ${orderData.orderId}:`, error);
      // Não propagar erro para não interromper o fluxo principal
    }
  },

  /**
   * Envia evento de atualização de pedido
   * @param orderData - Dados do pedido
   */
  async orderUpdated(orderData: OrderData): Promise<void> {
    try {
      const event: OrderEvent = {
        ...eventProducerService.createBaseEvent(
          `order-updated-${orderData.orderId}`,
          orderData.metadata || {}
        ),
        orderId: orderData.orderId,
        userId: orderData.userId,
        status: orderData.status,
        total: orderData.total,
        items: orderData.items?.map((item) => ({
          productId: item.productId,
          quantity: item.quantity,
          price: item.price,
        })),
      };

      await eventProducerService.sendOrderEvent('updated', event);

      logger.info(`Evento de atualização de pedido enviado: ${orderData.orderId}`);
    } catch (error) {
      logger.error(`Erro ao enviar evento de atualização de pedido ${orderData.orderId}:`, error);
      // Não propagar erro para não interromper o fluxo principal
    }
  },

  /**
   * Envia evento de mudança de status de pedido
   * @param orderData - Dados do pedido
   * @param oldStatus - Status anterior
   */
  async orderStatusChanged(orderData: OrderData, oldStatus: string): Promise<void> {
    try {
      const event: OrderEvent = {
        ...eventProducerService.createBaseEvent(`order-status-changed-${orderData.orderId}`, {
          ...(orderData.metadata || {}),
          oldStatus,
          newStatus: orderData.status,
        }),
        orderId: orderData.orderId,
        userId: orderData.userId,
        status: orderData.status,
        total: orderData.total,
      };

      await eventProducerService.sendOrderEvent('status.changed', event);

      logger.info(
        `Evento de mudança de status de pedido enviado: ${orderData.orderId} (${oldStatus} -> ${orderData.status})`
      );
    } catch (error) {
      logger.error(
        `Erro ao enviar evento de mudança de status de pedido ${orderData.orderId}:`,
        error
      );
      // Não propagar erro para não interromper o fluxo principal
    }
  },

  /**
   * Envia evento de adição de item ao pedido
   * @param orderData - Dados do pedido
   * @param item - Item adicionado
   */
  async orderItemAdded(orderData: OrderData, item: OrderItem): Promise<void> {
    try {
      const event: OrderEvent = {
        ...eventProducerService.createBaseEvent(
          `order-item-added-${orderData.orderId}-${item.productId}`,
          {
            ...(orderData.metadata || {}),
            item: {
              productId: item.productId,
              name: item.name,
              quantity: item.quantity,
              price: item.price,
            },
          }
        ),
        orderId: orderData.orderId,
        userId: orderData.userId,
        status: orderData.status,
        total: orderData.total,
      };

      await eventProducerService.sendDomainEvent(
        'order',
        'item',
        'added',
        event,
        orderData.orderId
      );

      logger.info(
        `Evento de adição de item ao pedido enviado: ${orderData.orderId} (${item.productId})`
      );
    } catch (error) {
      logger.error(
        `Erro ao enviar evento de adição de item ao pedido ${orderData.orderId}:`,
        error
      );
      // Não propagar erro para não interromper o fluxo principal
    }
  },

  /**
   * Envia evento de remoção de item do pedido
   * @param orderData - Dados do pedido
   * @param item - Item removido
   */
  async orderItemRemoved(orderData: OrderData, item: OrderItem): Promise<void> {
    try {
      const event: OrderEvent = {
        ...eventProducerService.createBaseEvent(
          `order-item-removed-${orderData.orderId}-${item.productId}`,
          {
            ...(orderData.metadata || {}),
            item: {
              productId: item.productId,
              name: item.name,
              quantity: item.quantity,
              price: item.price,
            },
          }
        ),
        orderId: orderData.orderId,
        userId: orderData.userId,
        status: orderData.status,
        total: orderData.total,
      };

      await eventProducerService.sendDomainEvent(
        'order',
        'item',
        'removed',
        event,
        orderData.orderId
      );

      logger.info(
        `Evento de remoção de item do pedido enviado: ${orderData.orderId} (${item.productId})`
      );
    } catch (error) {
      logger.error(
        `Erro ao enviar evento de remoção de item do pedido ${orderData.orderId}:`,
        error
      );
      // Não propagar erro para não interromper o fluxo principal
    }
  },

  /**
   * Envia evento de cancelamento de pedido
   * @param orderData - Dados do pedido
   * @param reason - Motivo do cancelamento
   */
  async orderCancelled(orderData: OrderData, reason: string): Promise<void> {
    try {
      const event: OrderEvent = {
        ...eventProducerService.createBaseEvent(`order-cancelled-${orderData.orderId}`, {
          ...(orderData.metadata || {}),
          cancellationReason: reason,
        }),
        orderId: orderData.orderId,
        userId: orderData.userId,
        status: 'cancelled',
        total: orderData.total,
      };

      await eventProducerService.sendOrderEvent('cancelled', event);

      logger.info(`Evento de cancelamento de pedido enviado: ${orderData.orderId}`);
    } catch (error) {
      logger.error(`Erro ao enviar evento de cancelamento de pedido ${orderData.orderId}:`, error);
      // Não propagar erro para não interromper o fluxo principal
    }
  },

  /**
   * Envia evento de conclusão de pedido
   * @param orderData - Dados do pedido
   */
  async orderFulfilled(orderData: OrderData): Promise<void> {
    try {
      const event: OrderEvent = {
        ...eventProducerService.createBaseEvent(
          `order-fulfilled-${orderData.orderId}`,
          orderData.metadata || {}
        ),
        orderId: orderData.orderId,
        userId: orderData.userId,
        status: 'fulfilled',
        total: orderData.total,
      };

      await eventProducerService.sendOrderEvent('fulfilled', event);

      logger.info(`Evento de conclusão de pedido enviado: ${orderData.orderId}`);
    } catch (error) {
      logger.error(`Erro ao enviar evento de conclusão de pedido ${orderData.orderId}:`, error);
      // Não propagar erro para não interromper o fluxo principal
    }
  },
};
