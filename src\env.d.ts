/// <reference types="astro/client" />

/**
 * Type definitions for environment variables and Astro globals
 */

// Environment variables
interface ImportMetaEnv {
  readonly PUBLIC_SITE_URL: string;
  readonly DATABASE_URL: string;
  readonly REDIS_URL: string;
  readonly JWT_SECRET: string;
}

interface ImportMeta {
  readonly env: ImportMetaEnv;
}

// Extend Astro's locals interface
declare namespace App {
  interface Locals {
    user?: AuthUser;
    requestInfo?: {
      url: string;
      method: string;
      userAgent: string | null;
      timestamp: string;
    };
  }

  interface AuthUser {
    ulid_user: string;
    name: string;
    email: string;
    is_teacher: boolean;
    ulid_user_type: string;
    ulid_school_type: string;
    state: string;
    county: string;
  }

  interface SessionData {
    user?: AuthUser;
    isAuthenticated: boolean;
  }
}
