---
/**
 * Página de configuração de auditoria
 *
 * Esta página permite configurar as opções de auditoria do sistema.
 */

import Notification from '@components/admin/Notification.astro';
import PermissionGate from '@components/auth/PermissionGate.astro';
// Importações
import MainLayout from '@layouts/MainLayout.astro';
import { auditRepository } from '@repository/auditRepository';
import { getCurrentUser } from '@utils/authUtils';

// Verificar autenticação
const user = await getCurrentUser(Astro.cookies);

// Redirecionar se não estiver autenticado
if (!user) {
  return Astro.redirect('/signin?redirect=/admin/audit/config');
}

// Obter configurações de auditoria
const configResult = await auditRepository.getConfig();
const auditConfig = configResult.rows;

// Agrupar configurações por categoria
const configByCategory: Record<string, any[]> = {};

auditConfig.forEach((config) => {
  const category = config.event_type.split('.')[0];

  if (!configByCategory[category]) {
    configByCategory[category] = [];
  }

  configByCategory[category].push(config);
});

// Ordenar categorias
const orderedCategories = ['auth', 'admin', 'resource', 'system'];

// Título da página
const title = 'Configuração de Auditoria';

// Mensagens de sucesso/erro
let successMessage = '';
let errorMessage = '';

// Processar formulário
if (Astro.request.method === 'POST') {
  try {
    const formData = await Astro.request.formData();
    const configId = formData.get('config_id') as string;
    const isEnabled = formData.get('is_enabled') === 'true';
    const retentionDays = Number.parseInt(formData.get('retention_days') as string, 10);
    const description = formData.get('description') as string;

    // Validar dados
    if (!configId) {
      errorMessage = 'ID da configuração não fornecido.';
    } else if (Number.isNaN(retentionDays) || retentionDays < 1) {
      errorMessage = 'Período de retenção inválido.';
    } else {
      // Atualizar configuração
      await auditRepository.updateConfig(configId, isEnabled, retentionDays, description);

      successMessage = 'Configuração atualizada com sucesso.';
    }
  } catch (error) {
    console.error('Erro ao atualizar configuração:', error);
    errorMessage = 'Erro ao atualizar configuração.';
  }
}
---

<MainLayout title={title}>
  <main class="container mx-auto px-4 py-8">
    <PermissionGate resource="audit" action="configure">
      <div class="flex items-center justify-between mb-6">
        <h1 class="text-3xl font-bold">{title}</h1>
        
        <div class="flex space-x-2">
          <a 
            href="/admin/audit" 
            class="bg-gray-200 text-gray-800 px-4 py-2 rounded hover:bg-gray-300 transition"
          >
            Voltar para Logs
          </a>
        </div>
      </div>
      
      {successMessage && (
        <div class="mb-6">
          <Notification 
            type="success" 
            message={successMessage}
          />
        </div>
      )}
      
      {errorMessage && (
        <div class="mb-6">
          <Notification 
            type="error" 
            message={errorMessage}
          />
        </div>
      )}
      
      <div class="bg-white rounded-lg shadow p-6 mb-6">
        <h2 class="text-xl font-semibold mb-4">Configurações de Auditoria</h2>
        <p class="text-gray-600 mb-6">
          Configure quais eventos serão registrados e por quanto tempo serão mantidos no sistema.
        </p>
        
        <div class="space-y-8">
          {orderedCategories.map(category => (
            <div>
              <h3 class="text-lg font-medium mb-4 capitalize">{category}</h3>
              
              <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                  <thead class="bg-gray-50">
                    <tr>
                      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Evento
                      </th>
                      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Descrição
                      </th>
                      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Retenção (dias)
                      </th>
                      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Ações
                      </th>
                    </tr>
                  </thead>
                  <tbody class="bg-white divide-y divide-gray-200">
                    {configByCategory[category]?.map(config => (
                      <tr>
                        <td class="px-6 py-4 whitespace-nowrap">
                          <div class="text-sm font-medium text-gray-900">{config.event_type}</div>
                        </td>
                        <td class="px-6 py-4">
                          <div class="text-sm text-gray-500">{config.description}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                          <span class={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${config.is_enabled ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                            {config.is_enabled ? 'Ativo' : 'Inativo'}
                          </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {config.retention_days} dias
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          <button 
                            type="button"
                            class="text-blue-600 hover:text-blue-800"
                            data-config-id={config.id}
                            data-config-type={config.event_type}
                            data-config-description={config.description}
                            data-config-enabled={config.is_enabled}
                            data-config-retention={config.retention_days}
                          >
                            Editar
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          ))}
        </div>
      </div>
      
      <slot name="fallback">
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mt-6">
          <p>Você não tem permissão para configurar a auditoria do sistema.</p>
          <p class="mt-2">
            <a href="/" class="text-red-700 font-medium hover:underline">Voltar para a página inicial</a>
          </p>
        </div>
      </slot>
    </PermissionGate>
  </main>
  
  <!-- Modal de edição -->
  <div id="edit-config-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center hidden z-50">
    <div class="bg-white rounded-lg shadow-lg max-w-md w-full">
      <div class="p-6 border-b">
        <div class="flex items-center justify-between">
          <h3 class="text-xl font-semibold" id="modal-title">Editar Configuração</h3>
          <button type="button" class="text-gray-400 hover:text-gray-500" id="close-modal">
            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
      </div>
      
      <form method="POST" class="p-6 space-y-4">
        <input type="hidden" name="config_id" id="config-id">
        
        <div>
          <label for="event-type" class="block text-sm font-medium text-gray-700 mb-1">Tipo de Evento</label>
          <input 
            type="text" 
            id="event-type" 
            class="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50"
            readonly
          >
        </div>
        
        <div>
          <label for="description" class="block text-sm font-medium text-gray-700 mb-1">Descrição</label>
          <input 
            type="text" 
            id="description" 
            name="description"
            class="w-full px-3 py-2 border border-gray-300 rounded-md"
          >
        </div>
        
        <div>
          <label for="is-enabled" class="block text-sm font-medium text-gray-700 mb-1">Status</label>
          <select 
            id="is-enabled" 
            name="is_enabled"
            class="w-full px-3 py-2 border border-gray-300 rounded-md"
          >
            <option value="true">Ativo</option>
            <option value="false">Inativo</option>
          </select>
        </div>
        
        <div>
          <label for="retention-days" class="block text-sm font-medium text-gray-700 mb-1">Período de Retenção (dias)</label>
          <input 
            type="number" 
            id="retention-days" 
            name="retention_days"
            min="1"
            class="w-full px-3 py-2 border border-gray-300 rounded-md"
          >
          <p class="mt-1 text-sm text-gray-500">
            Número de dias que os logs deste tipo serão mantidos no sistema.
          </p>
        </div>
      
        <div class="pt-4">
          <button 
            type="submit"
            class="w-full bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition"
          >
            Salvar Alterações
          </button>
        </div>
      </form>
    </div>
  </div>
</MainLayout>

<script>
  // Script para interatividade no cliente
  document.addEventListener('DOMContentLoaded', () => {
    // Modal de edição
    const modal = document.getElementById('edit-config-modal');
    const closeModalBtn = document.getElementById('close-modal');
    const configIdInput = document.getElementById('config-id') as HTMLInputElement;
    const eventTypeInput = document.getElementById('event-type') as HTMLInputElement;
    const descriptionInput = document.getElementById('description') as HTMLInputElement;
    const isEnabledSelect = document.getElementById('is-enabled') as HTMLSelectElement;
    const retentionDaysInput = document.getElementById('retention-days') as HTMLInputElement;
    
    // Botões de edição
    const editButtons = document.querySelectorAll('[data-config-id]');
    
    // Função para abrir modal de edição
    const openEditModal = (button: Element) => {
      const configId = button.getAttribute('data-config-id');
      const configType = button.getAttribute('data-config-type');
      const configDescription = button.getAttribute('data-config-description');
      const configEnabled = button.getAttribute('data-config-enabled');
      const configRetention = button.getAttribute('data-config-retention');
      
      // Preencher formulário
      configIdInput.value = configId;
      eventTypeInput.value = configType;
      descriptionInput.value = configDescription;
      isEnabledSelect.value = configEnabled === 'true' ? 'true' : 'false';
      retentionDaysInput.value = configRetention;
      
      // Exibir modal
      modal.classList.remove('hidden');
    };
    
    // Adicionar evento de clique aos botões de edição
    editButtons.forEach(button => {
      button.addEventListener('click', () => {
        openEditModal(button);
      });
    });
    
    // Fechar modal
    const closeModal = () => {
      modal.classList.add('hidden');
    };
    
    closeModalBtn.addEventListener('click', closeModal);
    
    // Fechar modal ao clicar fora
    modal.addEventListener('click', (e) => {
      if (e.target === modal) {
        closeModal();
      }
    });
    
    // Fechar modal com tecla ESC
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape' && !modal.classList.contains('hidden')) {
        closeModal();
      }
    });
  });
</script>

<style>
  /* Estilos específicos da página */
</style>
