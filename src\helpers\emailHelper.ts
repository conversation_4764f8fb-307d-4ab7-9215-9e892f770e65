import {
  ERROR_EMAIL_ADDRESS,
  ERROR_EMAIL_PASSWORD,
  ERROR_EMAIL_USER,
  SERVICE_EMAIL_NAME,
  SERVICE_EMAIL_PASSWORD,
  SERVICE_EMAIL_USER,
} from 'astro:env/server';
import nodemailer, { type Transporter } from 'nodemailer';

let errorTransporter: Transporter;
let serviceTransporter: Transporter;

async function sendingError(error: Error): Promise<boolean> {
  if (!errorTransporter) {
    errorTransporter = nodemailer.createTransport({
      service: 'gmail',
      auth: {
        user: ERROR_EMAIL_USER,
        pass: ERROR_EMAIL_PASSWORD,
      },
    });
  }

  const mailOptions = {
    from: errorTransporter.options.sender,
    to: ERROR_EMAIL_ADDRESS,
    subject: 'Error',
    text: `Cause: ${error.cause}\nMessage: ${error.message}\nName: ${error.name}\nStack: ${error.stack}`,
  };

  try {
    await errorTransporter.sendMail(mailOptions);
    return true;
  } catch (error) {
    console.error('Erro ao enviar email de erro: ', error);
    return false;
  }
}

async function sendingByService(to: string, subject: string, message: string): Promise<boolean> {
  if (!serviceTransporter) {
    serviceTransporter = nodemailer.createTransport({
      service: SERVICE_EMAIL_NAME,
      auth: {
        user: SERVICE_EMAIL_USER,
        pass: SERVICE_EMAIL_PASSWORD,
        accessToken: undefined,
      },
    });
  }

  const mailOptions = {
    from: serviceTransporter.options.sender,
    to: to,
    subject: subject,
    text: message,
  };

  try {
    await serviceTransporter.sendMail(mailOptions);
    return true;
  } catch (error) {
    console.error('Erro ao enviar email de serviço: ', error);
    return false;
  }
}

export const email = {
  sendingByService,
  sendingError,
};
