/**
 * Serviço de warm-up de cache
 *
 * Este serviço gerencia o pré-carregamento de dados frequentemente acessados
 * para melhorar a performance do sistema.
 */

import { pgHelper } from '@repository/pgHelper';
import { CacheableDataType, dataCacheService } from '@services/dataCacheService';
import { EntityType, entityCacheService } from '@services/entityCacheService';
import { queryCacheService } from '@services/queryCacheService';
import { strategicCacheService } from '@services/strategicCacheService';
import { logger } from '@utils/logger';

/**
 * Interface para configuração de warm-up
 */
export interface WarmupConfig {
  /**
   * Se o warm-up está habilitado
   * @default true
   */
  enabled: boolean;

  /**
   * Intervalo em minutos para recarregamento periódico
   * @default 60 (1 hora)
   */
  refreshInterval: number;

  /**
   * Tempo de vida em segundos para itens pré-carregados
   * @default 3600 (1 hora)
   */
  cacheTTL: number;

  /**
   * Número máximo de itens a serem pré-carregados por tipo
   * @default 100
   */
  maxItemsPerType: number;
}

/**
 * Configuração padrão de warm-up
 */
const DEFAULT_CONFIG: WarmupConfig = {
  enabled: true,
  refreshInterval: 60,
  cacheTTL: 3600,
  maxItemsPerType: 100,
};

/**
 * Serviço de warm-up de cache
 */
export const cacheWarmupService = {
  /**
   * Configuração atual
   */
  config: { ...DEFAULT_CONFIG },

  /**
   * Inicializa o serviço de warm-up
   * @param config - Configuração personalizada (opcional)
   */
  initialize(config: Partial<WarmupConfig> = {}): void {
    // Mesclar configuração personalizada com padrão
    this.config = {
      ...DEFAULT_CONFIG,
      ...config,
    };

    // Iniciar warm-up se habilitado
    if (this.config.enabled) {
      // Executar warm-up inicial
      this.warmupCache();

      // Agendar recarregamentos periódicos
      if (this.config.refreshInterval > 0) {
        setInterval(
          () => {
            this.warmupCache();
          },
          this.config.refreshInterval * 60 * 1000
        );
      }

      logger.info('Serviço de warm-up de cache inicializado', {
        refreshInterval: this.config.refreshInterval,
        cacheTTL: this.config.cacheTTL,
      });
    }
  },

  /**
   * Executa o warm-up do cache
   */
  async warmupCache(): Promise<void> {
    try {
      logger.info('Iniciando warm-up de cache');

      const startTime = Date.now();

      // Executar warm-up para cada tipo de dado
      await Promise.all([
        this.warmupUsers(),
        this.warmupCategories(),
        this.warmupProducts(),
        this.warmupPermissions(),
        this.warmupRoles(),
        this.warmupCommonQueries(),
        this.warmupEntities(),
        this.warmupStrategicData(),
      ]);

      const duration = Date.now() - startTime;

      logger.info(`Warm-up de cache concluído em ${duration}ms`);
    } catch (error) {
      logger.error('Erro durante warm-up de cache:', error);
    }
  },

  /**
   * Executa warm-up para usuários frequentes
   */
  async warmupUsers(): Promise<void> {
    try {
      // Consulta para obter usuários mais ativos
      const query = `
        SELECT u.ulid_user, u.name, u.email, u.active
        FROM users u
        JOIN sessions s ON u.ulid_user = s.user_id
        GROUP BY u.ulid_user, u.name, u.email, u.active
        ORDER BY COUNT(s.id) DESC
        LIMIT $1
      `;

      // Executar consulta
      const result = await pgHelper.query(query, [this.config.maxItemsPerType]);

      if (result.rowCount > 0) {
        // Armazenar cada usuário em cache
        for (const user of result.rows) {
          await dataCacheService.set(CacheableDataType.USER, user.ulid_user, user, {
            ttl: this.config.cacheTTL,
            tags: ['user', 'warmup'],
          });
        }

        logger.info(`Warm-up de ${result.rowCount} usuários concluído`);
      }
    } catch (error) {
      logger.error('Erro durante warm-up de usuários:', error);
    }
  },

  /**
   * Executa warm-up para categorias
   */
  async warmupCategories(): Promise<void> {
    try {
      // Consulta para obter todas as categorias
      const query = `
        SELECT id, name, slug, parent_id, active
        FROM categories
        WHERE active = true
        ORDER BY parent_id NULLS FIRST, name
        LIMIT $1
      `;

      // Executar consulta
      const result = await pgHelper.query(query, [this.config.maxItemsPerType]);

      if (result.rowCount > 0) {
        // Armazenar cada categoria em cache
        for (const category of result.rows) {
          await dataCacheService.set(CacheableDataType.CATEGORY, category.id.toString(), category, {
            ttl: this.config.cacheTTL,
            tags: ['category', 'warmup'],
          });

          // Armazenar também por slug
          await dataCacheService.set(
            CacheableDataType.CATEGORY,
            `slug:${category.slug}`,
            category,
            {
              ttl: this.config.cacheTTL,
              tags: ['category', 'slug', 'warmup'],
            }
          );
        }

        // Armazenar lista completa
        await dataCacheService.set(CacheableDataType.CATEGORY, 'all', result.rows, {
          ttl: this.config.cacheTTL,
          tags: ['category', 'list', 'warmup'],
        });

        logger.info(`Warm-up de ${result.rowCount} categorias concluído`);
      }
    } catch (error) {
      logger.error('Erro durante warm-up de categorias:', error);
    }
  },

  /**
   * Executa warm-up para produtos mais acessados
   */
  async warmupProducts(): Promise<void> {
    try {
      // Consulta para obter produtos mais acessados
      const query = `
        SELECT p.id, p.name, p.slug, p.description, p.price, p.active
        FROM products p
        JOIN product_views pv ON p.id = pv.product_id
        WHERE p.active = true
        GROUP BY p.id, p.name, p.slug, p.description, p.price, p.active
        ORDER BY COUNT(pv.id) DESC
        LIMIT $1
      `;

      // Executar consulta
      const result = await pgHelper.query(query, [this.config.maxItemsPerType]);

      if (result.rowCount > 0) {
        // Armazenar cada produto em cache
        for (const product of result.rows) {
          await dataCacheService.set(CacheableDataType.PRODUCT, product.id.toString(), product, {
            ttl: this.config.cacheTTL,
            tags: ['product', 'warmup'],
          });

          // Armazenar também por slug
          await dataCacheService.set(CacheableDataType.PRODUCT, `slug:${product.slug}`, product, {
            ttl: this.config.cacheTTL,
            tags: ['product', 'slug', 'warmup'],
          });
        }

        logger.info(`Warm-up de ${result.rowCount} produtos concluído`);
      }
    } catch (error) {
      logger.error('Erro durante warm-up de produtos:', error);
    }
  },

  /**
   * Executa warm-up para permissões
   */
  async warmupPermissions(): Promise<void> {
    try {
      // Consulta para obter todas as permissões
      const query = `
        SELECT id, name, description, resource
        FROM permissions
        ORDER BY resource, name
        LIMIT $1
      `;

      // Executar consulta
      const result = await pgHelper.query(query, [this.config.maxItemsPerType]);

      if (result.rowCount > 0) {
        // Armazenar cada permissão em cache
        for (const permission of result.rows) {
          await dataCacheService.set(
            CacheableDataType.PERMISSION,
            permission.id.toString(),
            permission,
            {
              ttl: this.config.cacheTTL,
              tags: ['permission', 'auth', 'warmup'],
            }
          );
        }

        // Armazenar lista completa
        await dataCacheService.set(CacheableDataType.PERMISSION, 'all', result.rows, {
          ttl: this.config.cacheTTL,
          tags: ['permission', 'list', 'auth', 'warmup'],
        });

        logger.info(`Warm-up de ${result.rowCount} permissões concluído`);
      }
    } catch (error) {
      logger.error('Erro durante warm-up de permissões:', error);
    }
  },

  /**
   * Executa warm-up para papéis (roles)
   */
  async warmupRoles(): Promise<void> {
    try {
      // Consulta para obter todos os papéis
      const query = `
        SELECT id, name, description
        FROM roles
        ORDER BY name
        LIMIT $1
      `;

      // Executar consulta
      const result = await pgHelper.query(query, [this.config.maxItemsPerType]);

      if (result.rowCount > 0) {
        // Armazenar cada papel em cache
        for (const role of result.rows) {
          await dataCacheService.set(CacheableDataType.ROLE, role.id.toString(), role, {
            ttl: this.config.cacheTTL,
            tags: ['role', 'auth', 'warmup'],
          });
        }

        // Armazenar lista completa
        await dataCacheService.set(CacheableDataType.ROLE, 'all', result.rows, {
          ttl: this.config.cacheTTL,
          tags: ['role', 'list', 'auth', 'warmup'],
        });

        logger.info(`Warm-up de ${result.rowCount} papéis concluído`);
      }
    } catch (error) {
      logger.error('Erro durante warm-up de papéis:', error);
    }
  },

  /**
   * Executa warm-up para consultas comuns
   */
  async warmupCommonQueries(): Promise<void> {
    try {
      // Lista de consultas comuns para pré-carregar
      const commonQueries = [
        {
          name: 'categorias_ativas',
          query: 'SELECT id, name, slug FROM categories WHERE active = true ORDER BY name',
          params: [],
        },
        {
          name: 'produtos_destaque',
          query:
            'SELECT id, name, slug, price FROM products WHERE featured = true AND active = true ORDER BY name LIMIT 10',
          params: [],
        },
        {
          name: 'permissoes_por_recurso',
          query:
            'SELECT resource, COUNT(*) as count FROM permissions GROUP BY resource ORDER BY resource',
          params: [],
        },
      ];

      // Executar cada consulta e armazenar em cache
      for (const item of commonQueries) {
        const result = await queryCacheService.queryRead(
          item.query,
          item.params,
          this.config.cacheTTL
        );

        logger.info(`Warm-up da consulta '${item.name}' concluído`);
      }

      logger.info(`Warm-up de ${commonQueries.length} consultas comuns concluído`);
    } catch (error) {
      logger.error('Erro durante warm-up de consultas comuns:', error);
    }
  },

  /**
   * Executa warm-up para entidades usando o novo serviço de cache de entidades
   */
  async warmupEntities(): Promise<void> {
    try {
      logger.info('Iniciando warm-up de entidades com o novo serviço de cache');

      // Pré-carregar usuários
      const userCount = await entityCacheService.preload(
        EntityType.USER,
        async () => {
          const query = `
            SELECT u.ulid_user as id, u.name, u.email, u.active
            FROM users u
            JOIN sessions s ON u.ulid_user = s.user_id
            GROUP BY u.ulid_user, u.name, u.email, u.active
            ORDER BY COUNT(s.id) DESC
            LIMIT $1
          `;

          const result = await pgHelper.query(query, [this.config.maxItemsPerType]);
          return result.rows;
        },
        this.config.maxItemsPerType
      );

      // Pré-carregar produtos
      const productCount = await entityCacheService.preload(
        EntityType.PRODUCT,
        async () => {
          const query = `
            SELECT p.id, p.name, p.slug, p.description, p.price, p.active
            FROM products p
            JOIN product_views pv ON p.id = pv.product_id
            WHERE p.active = true
            GROUP BY p.id, p.name, p.slug, p.description, p.price, p.active
            ORDER BY COUNT(pv.id) DESC
            LIMIT $1
          `;

          const result = await pgHelper.query(query, [this.config.maxItemsPerType]);
          return result.rows;
        },
        this.config.maxItemsPerType
      );

      // Pré-carregar categorias
      const categoryCount = await entityCacheService.preload(
        EntityType.CATEGORY,
        async () => {
          const query = `
            SELECT id, name, slug, parent_id, active
            FROM categories
            WHERE active = true
            ORDER BY parent_id NULLS FIRST, name
            LIMIT $1
          `;

          const result = await pgHelper.query(query, [this.config.maxItemsPerType]);
          return result.rows;
        },
        this.config.maxItemsPerType
      );

      logger.info(
        `Warm-up de entidades concluído: ${userCount} usuários, ${productCount} produtos, ${categoryCount} categorias`
      );
    } catch (error) {
      logger.error('Erro durante warm-up de entidades:', error);
    }
  },

  /**
   * Executa warm-up para dados estratégicos
   */
  async warmupStrategicData(): Promise<void> {
    try {
      logger.info('Iniciando warm-up de dados estratégicos');

      // Lista de consultas estratégicas para pré-carregar
      const strategicQueries = [
        {
          type: 'query',
          params: { query: 'categorias_populares', limit: 10 },
          fetchFn: async () => {
            const query = `
              SELECT c.id, c.name, c.slug, COUNT(p.id) as product_count
              FROM categories c
              JOIN product_categories pc ON c.id = pc.category_id
              JOIN products p ON pc.product_id = p.id
              WHERE c.active = true AND p.active = true
              GROUP BY c.id, c.name, c.slug
              ORDER BY COUNT(p.id) DESC
              LIMIT 10
            `;

            const result = await pgHelper.query(query);
            return result.rows;
          },
        },
        {
          type: 'product',
          params: { query: 'produtos_recentes', limit: 10 },
          fetchFn: async () => {
            const query = `
              SELECT id, name, slug, price, created_at
              FROM products
              WHERE active = true
              ORDER BY created_at DESC
              LIMIT 10
            `;

            const result = await pgHelper.query(query);
            return result.rows;
          },
        },
        {
          type: 'static',
          params: { path: 'home-content' },
          fetchFn: async () => {
            return {
              title: 'Estação da Alfabetização',
              description: 'Plataforma educacional para alfabetização',
              features: [
                'Conteúdo interativo',
                'Jogos educativos',
                'Material didático',
                'Acompanhamento de progresso',
              ],
              lastUpdated: new Date().toISOString(),
            };
          },
        },
      ];

      // Executar cada consulta estratégica
      for (const item of strategicQueries) {
        await strategicCacheService.get(item.type, item.params, item.fetchFn);

        logger.info(
          `Warm-up estratégico para ${item.type}:${JSON.stringify(item.params)} concluído`
        );
      }

      logger.info(`Warm-up de ${strategicQueries.length} consultas estratégicas concluído`);
    } catch (error) {
      logger.error('Erro durante warm-up de dados estratégicos:', error);
    }
  },
};
