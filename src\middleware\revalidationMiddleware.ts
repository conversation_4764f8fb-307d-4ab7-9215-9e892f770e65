/**
 * Middleware de Revalidação de Conteúdo
 *
 * Este middleware implementa a revalidação de conteúdo para o On-Demand Rendering (ODR).
 * Ele permite que o conteúdo em cache seja revalidado sob demanda, garantindo que os
 * usuários sempre vejam o conteúdo mais recente.
 */

import { defineMiddleware } from 'astro:middleware';
import type { MiddlewareResponseHandler } from 'astro';
import { deleteCache, getCache, setCache } from '../infrastructure/cache/CacheService';

// Configuração de revalidação
const REVALIDATION_CONFIG = {
  // Rotas que podem ser revalidadas
  revalidatableRoutes: ['/conteudos/', '/content/', '/courses/', '/materials/'],

  // Parâmetros de URL para revalidação
  revalidationParams: ['revalidate', 'refresh', 'atualizar'],

  // Chave secreta para revalidação (deve ser configurada como variável de ambiente em produção)
  secretKey: import.meta.env.REVALIDATION_SECRET_KEY || 'estacao-alfabetizacao-secret',

  // Tempo mínimo entre revalidações (em segundos)
  minRevalidationInterval: 60, // 1 minuto

  // Prefixos de cache para revalidação
  cachePrefixes: ['odr:', 'odr:content:', 'odr:search:'],
};

/**
 * Verifica se uma rota pode ser revalidada
 * @param path Caminho da URL
 * @returns Verdadeiro se a rota pode ser revalidada
 */
function isRevalidatablePath(path: string): boolean {
  return REVALIDATION_CONFIG.revalidatableRoutes.some((route) => path.startsWith(route));
}

/**
 * Verifica se a URL contém parâmetros de revalidação
 * @param url URL da requisição
 * @returns Verdadeiro se a URL contém parâmetros de revalidação
 */
function hasRevalidationParams(url: URL): boolean {
  return REVALIDATION_CONFIG.revalidationParams.some((param) => url.searchParams.has(param));
}

/**
 * Verifica se a chave secreta é válida
 * @param url URL da requisição
 * @returns Verdadeiro se a chave secreta é válida
 */
function isValidSecretKey(url: URL): boolean {
  const secretKey = url.searchParams.get('secret');
  return secretKey === REVALIDATION_CONFIG.secretKey;
}

/**
 * Gera um padrão de chave de cache para revalidação
 * @param path Caminho da URL
 * @returns Padrão de chave de cache
 */
function generateCacheKeyPattern(path: string): string {
  // Remover parâmetros da URL
  const cleanPath = path.split('?')[0];

  // Se for uma rota específica de conteúdo (com ID)
  if (cleanPath.match(/\/conteudos\/[^\/]+$/)) {
    const contentId = cleanPath.split('/').pop();
    return `odr:content:/conteudos/${contentId}`;
  }

  // Se for uma rota de listagem de conteúdos
  if (cleanPath === '/conteudos') {
    return 'odr:/conteudos';
  }

  // Padrão genérico baseado no caminho
  for (const prefix of REVALIDATION_CONFIG.cachePrefixes) {
    if (cleanPath.startsWith('/conteudos/')) {
      return `${prefix}/conteudos/`;
    }
    if (cleanPath.startsWith('/content/')) {
      return `${prefix}/content/`;
    }
  }

  // Fallback para o caminho limpo
  return `odr:${cleanPath}`;
}

/**
 * Revalida o conteúdo em cache
 * @param keyPattern Padrão de chave de cache
 * @returns Número de chaves revalidadas
 */
async function revalidateCache(keyPattern: string): Promise<number> {
  try {
    // Obter todas as chaves que correspondem ao padrão
    const keys = await getMatchingCacheKeys(keyPattern);

    // Excluir cada chave do cache
    for (const key of keys) {
      await deleteCache(key);
    }

    return keys.length;
  } catch (error) {
    console.error('Erro ao revalidar cache:', error);
    return 0;
  }
}

/**
 * Obtém todas as chaves de cache que correspondem a um padrão
 * @param pattern Padrão de chave de cache
 * @returns Lista de chaves de cache
 */
async function getMatchingCacheKeys(pattern: string): Promise<string[]> {
  // Implementação simplificada - em um ambiente real, isso dependeria
  // da implementação específica do cache (Redis, Memcached, etc.)

  // Simulação de obtenção de chaves
  // Em uma implementação real, isso usaria algo como KEYS ou SCAN do Redis
  return [pattern];
}

/**
 * Verifica se uma revalidação recente já ocorreu
 * @param path Caminho da URL
 * @returns Verdadeiro se uma revalidação recente ocorreu
 */
async function hasRecentRevalidation(path: string): Promise<boolean> {
  const revalidationKey = `revalidation:${path}`;

  try {
    // Verificar se existe um registro de revalidação recente
    const lastRevalidation = await getCache<number>(revalidationKey);

    if (lastRevalidation) {
      const now = Date.now();
      const timeSinceLastRevalidation = (now - lastRevalidation) / 1000;

      // Verificar se o tempo desde a última revalidação é menor que o intervalo mínimo
      return timeSinceLastRevalidation < REVALIDATION_CONFIG.minRevalidationInterval;
    }

    return false;
  } catch (error) {
    console.error('Erro ao verificar revalidação recente:', error);
    return false;
  }
}

/**
 * Registra uma revalidação para um caminho
 * @param path Caminho da URL
 */
async function recordRevalidation(path: string): Promise<void> {
  const revalidationKey = `revalidation:${path}`;

  try {
    // Registrar o timestamp da revalidação
    await setCache(revalidationKey, Date.now(), REVALIDATION_CONFIG.minRevalidationInterval);
  } catch (error) {
    console.error('Erro ao registrar revalidação:', error);
  }
}

/**
 * Middleware de revalidação
 */
export const onRequest: MiddlewareResponseHandler = defineMiddleware(
  async ({ request, locals }, next) => {
    const url = new URL(request.url);
    const path = url.pathname;

    // Verificar se é uma rota que pode ser revalidada
    if (isRevalidatablePath(path)) {
      // Verificar se a URL contém parâmetros de revalidação
      if (hasRevalidationParams(url)) {
        // Verificar se a chave secreta é válida (para revalidações administrativas)
        const isAdmin = isValidSecretKey(url);

        // Se for uma revalidação administrativa ou não houver revalidação recente
        if (isAdmin || !(await hasRecentRevalidation(path))) {
          // Gerar padrão de chave de cache
          const keyPattern = generateCacheKeyPattern(path);

          // Revalidar o cache
          const revalidatedCount = await revalidateCache(keyPattern);

          // Registrar a revalidação
          await recordRevalidation(path);

          // Se for uma requisição de API de revalidação, retornar resposta JSON
          if (url.searchParams.has('api')) {
            return new Response(
              JSON.stringify({
                success: true,
                message: `Cache revalidado com sucesso. ${revalidatedCount} chaves afetadas.`,
                path,
                timestamp: new Date().toISOString(),
              }),
              {
                status: 200,
                headers: {
                  'Content-Type': 'application/json',
                },
              }
            );
          }

          // Adicionar informações de revalidação ao contexto local
          locals.revalidation = {
            performed: true,
            count: revalidatedCount,
            path,
          };
        }
      }
    }

    // Continuar para o próximo middleware ou rota
    return next();
  }
);
