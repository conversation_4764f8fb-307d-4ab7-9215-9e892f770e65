/**
 * API de Download de Documento Fiscal para Cliente
 *
 * Endpoint para download de documentos fiscais por um cliente.
 * Parte da implementação da tarefa 8.7.3 - Portal do cliente para documentos fiscais
 */

import type { APIRoute } from 'astro';
import { FiscalDocumentRepository } from '../../../../../domain/repositories/FiscalDocumentRepository';
import { FiscalProviderService } from '../../../../../domain/services/FiscalProviderService';
import {
  DocumentFormat,
  DownloadFiscalDocumentUseCase,
} from '../../../../../domain/usecases/fiscal/DownloadFiscalDocumentUseCase';
import { PostgresFiscalDocumentRepository } from '../../../../../infrastructure/database/repositories/PostgresFiscalDocumentRepository';
import { EfiPayFiscalProvider } from '../../../../../infrastructure/services/EfiPayFiscalProvider';

// Inicializar repositório
const fiscalDocumentRepository: FiscalDocumentRepository = new PostgresFiscalDocumentRepository();

// Inicializar provedor fiscal
const fiscalProviderService: FiscalProviderService = new EfiPayFiscalProvider();

// Inicializar caso de uso
const downloadFiscalDocumentUseCase = new DownloadFiscalDocumentUseCase(
  fiscalDocumentRepository,
  fiscalProviderService
);

// Configurar provedor fiscal
(async () => {
  try {
    // Em um cenário real, estas configurações viriam do banco de dados
    await fiscalProviderService.initialize({
      apiKey: process.env.EFI_PAY_API_KEY || 'sandbox_api_key',
      apiSecret: process.env.EFI_PAY_API_SECRET || 'sandbox_api_secret',
      environment: 'homologation',
      companyDocument: '12345678000199',
      companyName: 'Estação da Alfabetização LTDA',
      timeout: 30000,
    });
  } catch (error) {
    console.error('Erro ao inicializar provedor fiscal:', error);
  }
})();

export const GET: APIRoute = async ({ params, request }) => {
  try {
    const { id } = params;
    const url = new URL(request.url);
    const customerId = url.searchParams.get('customerId');
    const format = (url.searchParams.get('format') as DocumentFormat) || 'pdf';

    if (!id) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'ID do documento é obrigatório.',
        }),
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    if (!customerId) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'ID do cliente é obrigatório.',
        }),
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    if (!['xml', 'pdf'].includes(format)) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Formato inválido. Formatos suportados: xml, pdf.',
        }),
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    // Fazer download do documento fiscal
    const result = await downloadFiscalDocumentUseCase.execute({
      documentId: id,
      customerId,
      format,
    });

    if (result.success && result.data) {
      // Se for uma URL de PDF, redirecionar
      if (
        format === 'pdf' &&
        typeof result.data.content === 'string' &&
        result.data.content.startsWith('http')
      ) {
        return new Response(null, {
          status: 302,
          headers: {
            Location: result.data.content,
          },
        });
      }

      // Caso contrário, retornar o conteúdo para download
      return new Response(result.data.content, {
        status: 200,
        headers: {
          'Content-Type': result.data.contentType,
          'Content-Disposition': `attachment; filename="${result.data.filename}"`,
        },
      });
    }
    return new Response(
      JSON.stringify({
        success: false,
        error: result.error || 'Erro desconhecido ao fazer download do documento fiscal.',
      }),
      {
        status: 400,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  } catch (error) {
    console.error('Erro ao processar download de documento fiscal:', error);

    return new Response(
      JSON.stringify({
        success: false,
        error:
          'Ocorreu um erro ao processar o download do documento fiscal. Por favor, tente novamente.',
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  }
};
