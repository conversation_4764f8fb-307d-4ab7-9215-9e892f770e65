/**
 * Camada de compatibilidade para o repositório de itens de pedido
 *
 * Este arquivo mantém a compatibilidade com o código existente que
 * utiliza a estrutura antiga do repositório de itens de pedido.
 *
 * @deprecated Use a nova estrutura de repositórios em src/repositories
 */

import type { QueryResult } from 'pg';
import { pgHelper } from '../../repository/pgHelper';
import { repositories } from '../index';

/**
 * Cria um novo item de pedido
 *
 * @param ulid_order ID do pedido
 * @param ulid_product ID do produto
 * @param qty Quantidade
 * @param price Preço
 * @returns Resultado da consulta
 */
async function create(
  ulid_order: string,
  ulid_product: string,
  qty: number,
  price: number
): Promise<QueryResult> {
  try {
    // Usar a nova implementação
    const orderItem = await repositories.orderItemRepository.create({
      orderId: ulid_order,
      productId: ulid_product,
      quantity: qty,
      price,
    });

    // Converter para o formato esperado pelo código antigo
    return {
      rows: [orderItem],
      rowCount: 1,
      command: 'INSERT',
      oid: 0,
      fields: [],
    };
  } catch (error) {
    console.error('Error in compatibility layer (orderItemRepository.create):', error);

    // Fallback para a implementação antiga em caso de erro
    // Corrigindo o SQL que tinha um erro na query original (aspas simples nos placeholders)
    return pgHelper.query(
      `INSERT INTO tab_order_item (
         ulid_order, 
         ulid_product, 
         qty, 
         price) 
       VALUES ($1, $2, $3, $4)
       RETURNING *`,
      [ulid_order, ulid_product, qty, price]
    );
  }
}

/**
 * Busca itens de pedido
 *
 * @param ulid_order_item ID do item de pedido (opcional)
 * @param ulid_order ID do pedido (opcional)
 * @returns Resultado da consulta
 */
async function read(ulid_order_item?: string, ulid_order?: string): Promise<QueryResult> {
  try {
    // Usar a nova implementação
    const orderItems = await repositories.orderItemRepository.findAll({
      id: ulid_order_item,
      orderId: ulid_order,
    });

    // Converter para o formato esperado pelo código antigo
    return {
      rows: orderItems,
      rowCount: orderItems.length,
      command: 'SELECT',
      oid: 0,
      fields: [],
    };
  } catch (error) {
    console.error('Error in compatibility layer (orderItemRepository.read):', error);

    // Fallback para a implementação antiga em caso de erro
    // Corrigindo o SQL que tinha um erro na query original (aspas simples nos placeholders)
    return pgHelper.query(
      `SELECT * 
         FROM tab_order_item 
        WHERE TRUE 
        ${ulid_order_item ? ' AND ulid_order_item = $1' : ''}
        ${ulid_order ? ' AND ulid_order      = $2' : ''}`,
      [ulid_order_item, ulid_order]
    );
  }
}

/**
 * Atualiza um item de pedido
 *
 * @param ulid_order_item ID do item de pedido
 * @param qty Quantidade
 * @param price Preço
 * @returns Resultado da consulta
 */
async function update(ulid_order_item: string, qty: number, price: number): Promise<QueryResult> {
  try {
    // Usar a nova implementação
    const orderItem = await repositories.orderItemRepository.update(ulid_order_item, {
      quantity: qty,
      price,
    });

    // Converter para o formato esperado pelo código antigo
    return {
      rows: [orderItem],
      rowCount: 1,
      command: 'UPDATE',
      oid: 0,
      fields: [],
    };
  } catch (error) {
    console.error('Error in compatibility layer (orderItemRepository.update):', error);

    // Fallback para a implementação antiga em caso de erro
    // Corrigindo o SQL que tinha um erro na query original (ordem dos parâmetros)
    return pgHelper.query(
      `UPDATE tab_order_item 
          SET qty        = $2, 
              price      = $3,
              updated_at = NOW()
        WHERE ulid_order_item = $1 
       RETURNING *`,
      [ulid_order_item, qty, price]
    );
  }
}

/**
 * Remove um item de pedido
 *
 * @param ulid_order_item ID do item de pedido
 * @returns Resultado da consulta
 */
async function deleteByUlid(ulid_order_item: string): Promise<QueryResult> {
  try {
    // Usar a nova implementação
    const success = await repositories.orderItemRepository.delete(ulid_order_item);

    // Converter para o formato esperado pelo código antigo
    return {
      rows: success ? [{ ulid_order_item }] : [],
      rowCount: success ? 1 : 0,
      command: 'DELETE',
      oid: 0,
      fields: [],
    };
  } catch (error) {
    console.error('Error in compatibility layer (orderItemRepository.deleteByUlid):', error);

    // Fallback para a implementação antiga em caso de erro
    return pgHelper.query(
      `DELETE FROM tab_order_item 
        WHERE ulid_order_item = $1 
       RETURNING *`,
      [ulid_order_item]
    );
  }
}

export const orderItemRepository = {
  create,
  read,
  update,
  deleteByUlid,
};
