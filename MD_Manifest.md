# Estação Alfabetização - Project Manifest

## Project Overview

**Estação Alfabetização** is a comprehensive educational platform designed to support literacy and educational content management. The platform serves as a digital learning environment with robust content management, user authentication, payment processing, and analytics capabilities.

### Mission
Provide an accessible, scalable, and secure educational platform that facilitates literacy learning through interactive content, progress tracking, and comprehensive administrative tools.

## Technology Stack

### Core Framework
- **Astro.js 5.8.0**: Modern web framework for content-focused websites
- **TypeScript 5.8.3**: Type-safe JavaScript development
- **Node.js**: Server-side runtime environment

### Frontend Technologies
- **Tailwind CSS 3.4.0**: Utility-first CSS framework
- **DaisyUI 5.0.37**: Component library for Tailwind CSS
- **Chart.js 4.4.9**: Data visualization library
- **Anime.js 4.0.2**: Animation library

### Backend & Database
- **PostgreSQL**: Primary database with ULID-based schema
- **Valkey (@valkey/client 1.0.0)**: High-performance cache layer
- **Apache Kafka (kafkajs 2.2.4)**: Event streaming and messaging

### Authentication & Security
- **Argon2 0.41.1**: Password hashing (industry standard)
- **JSON Web Tokens (jsonwebtoken 9.0.2)**: Authentication tokens
- **Rate Limiting (rate-limiter-flexible 6.2.1)**: API protection

### Payment & Integration
- **Efí Pay SDK (sdk-node-apis-efi 1.2.20)**: Brazilian payment gateway
- **PDF Generation (pdfkit 0.17.1)**: Document generation
- **QR Code (qrcode 1.5.4)**: QR code generation
- **Nodemailer 6.10.1**: Email services

### Development & Quality
- **Biome.js 1.9.4**: Linting and formatting
- **Vitest 3.1.4**: Testing framework
- **Playwright 1.52.0**: End-to-end testing
- **Lighthouse**: Performance auditing

## Architecture Overview

### Clean Architecture Implementation

The project follows Clean Architecture principles with clear separation of concerns:

```
src/
├── domain/              # Business entities and rules
│   ├── entities/        # Core business entities
│   ├── repositories/    # Repository interfaces
│   └── usecases/        # Business logic use cases
├── application/         # Application-specific logic
│   ├── interfaces/      # Service interfaces
│   ├── usecases/        # Application use cases
│   └── dtos/           # Data transfer objects
├── adapters/           # Interface adapters
│   ├── controllers/    # Request/response handling
│   ├── repositories/   # Repository implementations
│   ├── presenters/     # Data presentation
│   └── gateways/       # External service adapters
├── infrastructure/     # External frameworks & tools
│   ├── database/       # Database configuration
│   ├── cache/          # Cache implementation
│   ├── services/       # External service implementations
│   └── config/         # Configuration management
├── components/         # Astro UI components
├── layouts/           # Astro layout templates
├── pages/             # Astro pages and API routes
├── middleware/        # Request processing middleware
├── services/          # Application services
├── utils/             # Utility functions
└── config/            # Application configuration
```

### Key Architectural Patterns
- **Repository Pattern**: Data access abstraction
- **Use Case Pattern**: Business logic encapsulation
- **Dependency Injection**: Loose coupling between layers
- **Event-Driven Architecture**: Kafka-based messaging
- **CQRS**: Command Query Responsibility Segregation
- **Cache-Aside Pattern**: Performance optimization

## Database Schema Summary

### Primary Database: PostgreSQL
- **ID Strategy**: ULID (26-character) for most entities
- **Legacy Support**: Some UUID fields for backward compatibility
- **Schema Management**: Migration-based with versioning

### Key Tables Structure
- **User Management**: `tab_user`, `tab_role`, `tab_permission`
- **Educational Content**: `tab_educational_content`, `tab_content_metadata`
- **Orders & Payments**: `tab_order`, `tab_payment`, `tab_invoice`
- **Audit & Security**: `tab_audit_log`, `tab_security_alerts`
- **System Monitoring**: `tab_dead_letter_queue`, `tab_alert_log`

### Cache Layer: Valkey
- **Session Storage**: User sessions and temporary data
- **Content Caching**: Educational content and metadata
- **Performance Optimization**: Query result caching
- **Rate Limiting**: API request tracking

## API Structure

### Authentication Endpoints
- `POST /api/auth/login` - User authentication
- `POST /api/auth/register` - User registration
- `POST /api/auth/logout` - Session termination
- `POST /api/auth/refresh` - Token refresh

### Content Management
- `GET /api/content` - List educational content
- `POST /api/content` - Create new content
- `PUT /api/content/{id}` - Update content
- `DELETE /api/content/{id}` - Remove content

### User Management
- `GET /api/users` - List users (admin)
- `GET /api/users/{id}` - Get user details
- `PUT /api/users/{id}` - Update user profile

### Payment Processing
- `POST /api/payments/process` - Process payment
- `GET /api/payments/{id}` - Payment status
- `POST /api/payments/webhook` - Payment webhook

### Analytics & Reporting
- `GET /api/metrics` - System metrics
- `GET /api/reports/export` - Export reports
- `GET /api/analytics` - Usage analytics

## Key Services

### Core Services
- **AuthenticationService**: User authentication and authorization
- **ContentService**: Educational content management
- **PaymentService**: Payment processing and validation
- **NotificationService**: Email and system notifications
- **AnalyticsService**: Usage tracking and reporting

### Infrastructure Services
- **CacheService**: Valkey-based caching with fallback
- **DatabaseService**: PostgreSQL connection and query management
- **KafkaService**: Event streaming and message processing
- **LoggingService**: Structured logging with multiple outputs
- **SecurityService**: CSRF protection and rate limiting

### Specialized Services
- **DocumentService**: PDF generation and management
- **ValidationService**: Form and data validation
- **EmailService**: Template-based email sending
- **FileService**: File upload and management
- **ReportService**: Data export and reporting

## Recent Changes & Migrations

### Completed Migrations
1. **UUID to nanoid Migration** (2025-01-24)
   - Replaced `uuid` library with `nanoid` for better performance
   - Reduced bundle size by ~11.5KB
   - Maintained database compatibility

2. **ActionsCantBeLoaded Fixes** (2025-01-24)
   - Fixed syntax errors in `alertService.ts`
   - Resolved import path issues in Kafka services
   - Added missing dependencies (`uuid`, `ajv`)
   - Ensured Astro actions system stability

3. **Valkey Migration** (Ongoing)
   - Migrated from Redis to Valkey for cache layer
   - Updated client library to `@valkey/client`
   - Maintained API compatibility

### Pending Migrations (from MD_Package.md)
- **High Priority**: Tailwind CSS update to v4.0.0
- **Medium Priority**: Winston to Pino logging migration
- **Medium Priority**: PostgreSQL client migration (`pg` to `postgres`)
- **Low Priority**: PDF library migration (`pdfkit` to `pdf-lib`)

## Development Guidelines

### Code Standards
- **TypeScript**: Strict mode enabled with comprehensive type checking
- **Linting**: Biome.js for code quality and formatting
- **Testing**: Vitest for unit tests, Playwright for E2E
- **Documentation**: JSDoc comments for all public APIs

### Naming Conventions
- **Files**: PascalCase for classes, camelCase for utilities
- **Variables**: camelCase for variables and functions
- **Constants**: UPPER_SNAKE_CASE for constants
- **Database**: snake_case for tables and columns

### Git Workflow
- **Branching**: Feature branches from main
- **Commits**: Conventional commit messages
- **Reviews**: Required for all changes
- **CI/CD**: Automated testing and deployment

## Known Issues & Technical Debt

### Code Quality Issues (from Biome Analysis)
1. **Type Safety**: 520 errors, 620 warnings detected
   - Excessive use of `any` types (1,164+ instances)
   - Missing type annotations in variable declarations
   - Implicit any types in function parameters

2. **Critical Issues**:
   - Parameter reassignment in utility functions
   - Untyped external API integrations
   - Missing error handling in async operations

### Performance Concerns
- **Bundle Size**: Large dependency footprint
- **Database Queries**: Some N+1 query patterns
- **Cache Efficiency**: Inconsistent cache key strategies

### Security Considerations
- **Input Validation**: Inconsistent validation patterns
- **Error Exposure**: Potential information leakage in error messages
- **Rate Limiting**: Not applied to all endpoints

## Deployment & Environment

### Environment Configuration
- **Development**: Local PostgreSQL, Valkey, and Kafka
- **Testing**: Isolated test database and services
- **Production**: Containerized deployment with Docker Compose

### Infrastructure Requirements
- **Database**: PostgreSQL 16+ with ULID extension
- **Cache**: Valkey 8.0+ or Redis-compatible
- **Message Queue**: Apache Kafka 3.0+
- **Node.js**: Version 18+ with ES modules support

### Monitoring & Observability
- **Logging**: Structured JSON logs with multiple outputs
- **Metrics**: Custom metrics collection and reporting
- **Alerts**: Kafka-based alerting system
- **Health Checks**: Comprehensive service health monitoring

## Educational Platform Specific Requirements

### Content Management Features
- **Multi-format Support**: Text, images, videos, interactive exercises
- **Progress Tracking**: Student advancement monitoring
- **Assessment Tools**: Quizzes, assignments, and evaluations
- **Accessibility**: WCAG 2.1 AA compliance for inclusive learning
- **Offline Capability**: Content caching for limited connectivity

### User Roles & Permissions
- **Students**: Content access, progress tracking, assignments
- **Teachers**: Content creation, student monitoring, grading
- **Administrators**: System management, user administration, analytics
- **Content Creators**: Educational material development and publishing

### Brazilian Market Compliance
- **LGPD**: Data protection and privacy compliance
- **Payment Integration**: Efí Pay for Brazilian payment methods (PIX, Boleto)
- **Localization**: Portuguese language support with Brazilian conventions
- **Educational Standards**: Alignment with Brazilian educational curriculum

## Configuration Management

### Environment Variables
```bash
# Database
DATABASE_URL=postgresql://user:pass@host:port/db
DB_SSL=true|false
DB_MAX_CONNECTIONS=20

# Cache
VALKEY_HOST=localhost
VALKEY_PORT=6379
VALKEY_PASSWORD=optional

# Kafka
KAFKA_BROKERS=broker1:9092,broker2:9092
KAFKA_CLIENT_ID=estacao_alfabetizacao

# Security
JWT_SECRET=your-secret-key
CSRF_SECRET=your-csrf-secret

# Payment
EFI_CLIENT_ID=your-efi-client-id
EFI_CLIENT_SECRET=your-efi-client-secret
EFI_SANDBOX=true|false

# Email
SMTP_HOST=smtp.example.com
SMTP_PORT=587
SMTP_USER=user
SMTP_PASS=password
```

### Feature Flags
- **ENABLE_ANALYTICS**: Analytics collection toggle
- **ENABLE_CACHE**: Cache layer activation
- **ENABLE_KAFKA**: Event streaming activation
- **MAINTENANCE_MODE**: System maintenance state

## Testing Strategy

### Test Coverage Requirements
- **Unit Tests**: Minimum 80% coverage for business logic
- **Integration Tests**: API endpoint validation
- **E2E Tests**: Critical user journeys
- **Performance Tests**: Load testing with K6
- **Security Tests**: OWASP ZAP integration

### Test Data Management
- **Fixtures**: Standardized test data sets
- **Factories**: Dynamic test data generation
- **Cleanup**: Automated test data cleanup
- **Isolation**: Test environment separation

## Monitoring & Alerting

### Key Metrics
- **Performance**: Response times, throughput, error rates
- **Business**: User engagement, content consumption, payment success
- **Infrastructure**: Database connections, cache hit rates, queue depths
- **Security**: Failed login attempts, suspicious activities

### Alert Thresholds
- **Critical**: System downtime, payment failures
- **Warning**: High response times, cache misses
- **Info**: User registrations, content uploads

## Backup & Recovery

### Data Backup Strategy
- **Database**: Daily full backups, hourly incremental
- **Files**: Content and user uploads backup
- **Configuration**: Environment and deployment configs
- **Retention**: 30 days for daily, 7 days for incremental

### Disaster Recovery
- **RTO**: Recovery Time Objective < 4 hours
- **RPO**: Recovery Point Objective < 1 hour
- **Failover**: Automated failover procedures
- **Testing**: Monthly disaster recovery drills

---

*This manifest serves as the authoritative reference for AI context and development decisions. Last updated: 2025-01-24*
