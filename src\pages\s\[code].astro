---
/**
 * Página de Redirecionamento de Link de Compartilhamento
 *
 * Esta página processa links de compartilhamento e redireciona para o destino.
 * Parte da implementação da tarefa 8.4.2 - Compartilhamento
 */

import { AffiliateRepository } from '../../domain/repositories/AffiliateRepository';
import { ShareLinkRepository } from '../../domain/repositories/ShareLinkRepository';
import { ProcessShareLinkUseCase } from '../../domain/usecases/share/ProcessShareLinkUseCase';

// Obter código do link da URL
const { code } = Astro.params;

// Verificar se o código foi fornecido
if (!code) {
  return Astro.redirect('/404');
}

// Em um cenário real, aqui seriam inicializados os repositórios e o caso de uso
// Por enquanto, vamos simular o processamento do link

// Simular repositórios
const shareLinkRepository: ShareLinkRepository = {
  // Implementação simulada
} as any;

const affiliateRepository: AffiliateRepository = {
  // Implementação simulada
} as any;

// Simular caso de uso
const processShareLinkUseCase = new ProcessShareLinkUseCase(
  shareLinkRepository,
  affiliateRepository,
  Astro.url.origin
);

// Simular processamento do link
// Em um cenário real, aqui seria chamado o caso de uso
const result = {
  success: true,
  redirectUrl: '/products/cartilha-de-alfabetizacao',
  isAffiliate: false,
};

// Obter informações do cliente
const ipAddress = Astro.clientAddress;
const userAgent = Astro.request.headers.get('user-agent') || '';
const referrer = Astro.request.headers.get('referer') || '';

// Registrar informações de acesso (em um cenário real)
console.log(`Link acessado: ${code}`);
console.log(`IP: ${ipAddress}`);
console.log(`User Agent: ${userAgent}`);
console.log(`Referrer: ${referrer}`);

// Redirecionar para a URL de destino
if (result.success && result.redirectUrl) {
  return Astro.redirect(result.redirectUrl);
} else {
  // Redirecionar para página de erro
  return Astro.redirect('/404');
}
---

<!-- Esta página não renderiza conteúdo, apenas redireciona -->
