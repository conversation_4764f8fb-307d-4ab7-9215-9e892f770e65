/**
 * Controlador de Monitoramento
 * 
 * Este controlador fornece endpoints para acesso às métricas
 * e estatísticas de monitoramento do sistema.
 */

import { Request, Response } from 'express';
import { applicationMonitoringService, MetricType } from '@services/applicationMonitoringService';
import { getRequestStats } from '@middlewares/monitoringMiddleware';
import { getQueryStats } from '@repository/dbMonitoringHelper';
import { getCacheOperationStats } from '@services/cacheMonitoringHelper';
import { valkeyMonitoringService } from '@services/valkeyMonitoringService';
import { kafkaService } from '@services/kafkaService';
import os from 'os';

/**
 * Obtém estatísticas gerais do sistema
 * @param req Requisição
 * @param res Resposta
 */
export async function getSystemStats(req: Request, res: Response): Promise<void> {
  try {
    // Obter estatísticas do sistema
    const stats = applicationMonitoringService.getSystemStats();
    
    // Adicionar informações do sistema operacional
    stats.os = {
      platform: process.platform,
      arch: process.arch,
      version: process.version,
      cpus: os.cpus().length,
      totalMemory: os.totalmem(),
      freeMemory: os.freemem(),
      uptime: os.uptime()
    };
    
    // Adicionar informações do processo
    stats.process = {
      pid: process.pid,
      uptime: process.uptime(),
      memoryUsage: process.memoryUsage(),
      cpuUsage: process.cpuUsage()
    };
    
    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
}

/**
 * Obtém métricas por tipo
 * @param req Requisição
 * @param res Resposta
 */
export async function getMetrics(req: Request, res: Response): Promise<void> {
  try {
    const { type, limit } = req.query;
    
    // Validar tipo de métrica
    if (!type || !Object.values(MetricType).includes(type as MetricType)) {
      return res.status(400).json({
        success: false,
        error: 'Tipo de métrica inválido'
      });
    }
    
    // Obter métricas
    const metrics = applicationMonitoringService.getMetrics(
      type as MetricType,
      limit ? parseInt(limit as string, 10) : undefined
    );
    
    res.json({
      success: true,
      data: metrics
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
}

/**
 * Obtém alertas ativos
 * @param req Requisição
 * @param res Resposta
 */
export async function getAlerts(req: Request, res: Response): Promise<void> {
  try {
    // Obter alertas ativos
    const alerts = applicationMonitoringService.getActiveAlerts();
    
    res.json({
      success: true,
      data: alerts
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
}

/**
 * Obtém estatísticas de requisições
 * @param req Requisição
 * @param res Resposta
 */
export async function getRequestMetrics(req: Request, res: Response): Promise<void> {
  try {
    // Obter estatísticas de requisições
    const stats = getRequestStats();
    
    // Adicionar métricas de requisições
    stats.totalRequests = applicationMonitoringService.getCounterValue(MetricType.REQUEST_COUNT);
    stats.errorRequests = applicationMonitoringService.getCounterValue(MetricType.REQUEST_ERROR_COUNT);
    stats.errorRate = applicationMonitoringService.getLatestMetricValue(MetricType.ERROR_RATE);
    stats.avgRequestDuration = applicationMonitoringService.getLatestMetricValue(MetricType.REQUEST_DURATION);
    
    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
}

/**
 * Obtém estatísticas de banco de dados
 * @param req Requisição
 * @param res Resposta
 */
export async function getDatabaseMetrics(req: Request, res: Response): Promise<void> {
  try {
    // Obter estatísticas de consultas
    const stats = getQueryStats();
    
    // Adicionar métricas de banco de dados
    stats.totalQueries = applicationMonitoringService.getCounterValue(MetricType.DB_QUERY_COUNT);
    stats.errorQueries = applicationMonitoringService.getCounterValue(MetricType.DB_ERROR_COUNT);
    stats.avgQueryDuration = applicationMonitoringService.getLatestMetricValue(MetricType.DB_QUERY_DURATION);
    stats.connectionCount = applicationMonitoringService.getLatestMetricValue(MetricType.DB_CONNECTION_COUNT);
    
    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
}

/**
 * Obtém estatísticas de cache
 * @param req Requisição
 * @param res Resposta
 */
export async function getCacheMetrics(req: Request, res: Response): Promise<void> {
  try {
    // Obter estatísticas de operações de cache
    const stats = getCacheOperationStats();
    
    // Adicionar métricas de cache
    stats.hitCount = applicationMonitoringService.getCounterValue(MetricType.CACHE_HIT_COUNT);
    stats.missCount = applicationMonitoringService.getCounterValue(MetricType.CACHE_MISS_COUNT);
    stats.hitRate = applicationMonitoringService.getLatestMetricValue(MetricType.CACHE_HIT_RATE);
    stats.size = applicationMonitoringService.getLatestMetricValue(MetricType.CACHE_SIZE);
    
    // Adicionar estatísticas do Valkey
    stats.valkey = await valkeyMonitoringService.getStats();
    
    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
}

/**
 * Obtém estatísticas do Kafka
 * @param req Requisição
 * @param res Resposta
 */
export async function getKafkaMetrics(req: Request, res: Response): Promise<void> {
  try {
    // Obter estatísticas do Kafka
    const stats = {
      producerCount: applicationMonitoringService.getCounterValue(MetricType.KAFKA_PRODUCER_COUNT),
      consumerCount: applicationMonitoringService.getCounterValue(MetricType.KAFKA_CONSUMER_COUNT),
      errorCount: applicationMonitoringService.getCounterValue(MetricType.KAFKA_ERROR_COUNT),
      lag: applicationMonitoringService.getLatestMetricValue(MetricType.KAFKA_LAG)
    };
    
    // Adicionar informações do Kafka
    stats.topics = await kafkaService.getTopics();
    stats.consumerGroups = await kafkaService.getConsumerGroups();
    
    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
}

/**
 * Obtém estatísticas de negócio
 * @param req Requisição
 * @param res Resposta
 */
export async function getBusinessMetrics(req: Request, res: Response): Promise<void> {
  try {
    // Obter estatísticas de negócio
    const stats = {
      activeUsers: applicationMonitoringService.getLatestMetricValue(MetricType.ACTIVE_USERS),
      transactionCount: applicationMonitoringService.getCounterValue(MetricType.TRANSACTION_COUNT),
      transactionValue: applicationMonitoringService.getCounterValue(MetricType.TRANSACTION_VALUE)
    };
    
    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
}

/**
 * Reseta contadores de métricas
 * @param req Requisição
 * @param res Resposta
 */
export async function resetCounters(req: Request, res: Response): Promise<void> {
  try {
    // Verificar permissão
    if (!req.user || !req.user.isAdmin) {
      return res.status(403).json({
        success: false,
        error: 'Permissão negada'
      });
    }
    
    // Obter tipos de contadores a resetar
    const { types } = req.body;
    
    if (!types || !Array.isArray(types)) {
      return res.status(400).json({
        success: false,
        error: 'Tipos de contadores inválidos'
      });
    }
    
    // Resetar contadores
    for (const type of types) {
      if (Object.values(MetricType).includes(type as MetricType)) {
        applicationMonitoringService.resetCounter(type as MetricType);
      }
    }
    
    res.json({
      success: true,
      message: 'Contadores resetados com sucesso'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
}
