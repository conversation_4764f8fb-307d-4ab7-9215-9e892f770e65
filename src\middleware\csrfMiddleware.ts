import { csrfHelper } from '@helpers/csrfHelper';
// src/middleware/csrfMiddleware.ts
import type { AstroGlobal } from 'astro';

/**
 * Middleware para validação de tokens CSRF em actions
 * @param Astro - Contexto Astro global
 * @param formData - Dados do formulário
 * @returns Objeto com status da validação e mensagem de erro (se houver)
 */
export const validateCsrfToken = async (
  Astro: AstroGlobal,
  formData: FormData
): Promise<{ isValid: boolean; error?: string }> => {
  const token = formData.get('csrf_token') as string;

  if (!token) {
    return {
      isValid: false,
      error: 'Token CSRF ausente. Por favor, tente novamente.',
    };
  }

  const isValid = await csrfHelper.validateToken(Astro, token);

  if (!isValid) {
    return {
      isValid: false,
      error: 'Token CSRF inválido. Por favor, tente novamente.',
    };
  }

  // Rotacionar token após uso bem-sucedido
  await csrfHelper.rotateToken(Astro);

  return { isValid: true };
};
