/**
 * API de Usuário Administrador por ID
 *
 * Endpoint para gerenciamento de um usuário administrador específico.
 * Parte da implementação da tarefa 8.8.2 - Gestão de usuários
 */

import type { APIRoute } from 'astro';
import { AdminPermission, AdminRole } from '../../../../domain/entities/AdminUser';
import { AdminUserRepository } from '../../../../domain/repositories/AdminUserRepository';
import { TokenService } from '../../../../domain/services/TokenService';
import { DeleteAdminUserUseCase } from '../../../../domain/usecases/admin/DeleteAdminUserUseCase';
import { GetAdminUserUseCase } from '../../../../domain/usecases/admin/GetAdminUserUseCase';
import { UpdateAdminUserUseCase } from '../../../../domain/usecases/admin/UpdateAdminUserUseCase';
import { PostgresAdminUserRepository } from '../../../../infrastructure/database/repositories/PostgresAdminUserRepository';
import { JwtTokenService } from '../../../../infrastructure/services/JwtTokenService';

// Inicializar repositório
const adminUserRepository: AdminUserRepository = new PostgresAdminUserRepository();

// Inicializar serviços
const tokenService: TokenService = new JwtTokenService(
  process.env.JWT_SECRET || 'default-secret-key',
  process.env.PASSWORD_RESET_SECRET || 'password-reset-secret-key',
  process.env.EMAIL_VERIFICATION_SECRET || 'email-verification-secret-key'
);

// Inicializar casos de uso
const getAdminUserUseCase = new GetAdminUserUseCase(adminUserRepository);
const updateAdminUserUseCase = new UpdateAdminUserUseCase(adminUserRepository);
const deleteAdminUserUseCase = new DeleteAdminUserUseCase(adminUserRepository);

// Verificar autenticação e permissões
const checkAuth = (
  request: Request
): { userId: string; isAuthorized: boolean; canWrite: boolean; canDelete: boolean } | null => {
  const authHeader = request.headers.get('Authorization');

  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null;
  }

  const token = authHeader.substring(7);
  const payload = tokenService.verifyToken(token);

  if (!payload) {
    return null;
  }

  // Verificar permissões
  const isAuthorized = payload.role === 'admin' || payload.permissions?.includes('users:read');

  const canWrite = payload.role === 'admin' || payload.permissions?.includes('users:write');

  const canDelete = payload.role === 'admin' || payload.permissions?.includes('users:delete');

  return {
    userId: payload.id,
    isAuthorized,
    canWrite,
    canDelete,
  };
};

export const GET: APIRoute = async ({ params, request }) => {
  try {
    const id = params.id;

    if (!id) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'ID do usuário é obrigatório.',
        }),
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    // Verificar autenticação
    const auth = checkAuth(request);

    if (!auth) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Não autorizado. Faça login para continuar.',
        }),
        {
          status: 401,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    if (!auth.isAuthorized) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Você não tem permissão para acessar este recurso.',
        }),
        {
          status: 403,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    // Executar caso de uso
    const result = await getAdminUserUseCase.execute({ id });

    if (result.success && result.data) {
      // Remover senha hash da resposta
      const { passwordHash, ...userWithoutPassword } = result.data;

      return new Response(
        JSON.stringify({
          success: true,
          data: userWithoutPassword,
        }),
        {
          status: 200,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }
    return new Response(
      JSON.stringify({
        success: false,
        error: result.error || 'Usuário não encontrado.',
      }),
      {
        status: 404,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  } catch (error) {
    console.error('Erro ao processar obtenção de usuário administrativo:', error);

    return new Response(
      JSON.stringify({
        success: false,
        error: 'Ocorreu um erro ao processar a obtenção do usuário. Por favor, tente novamente.',
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  }
};

export const PUT: APIRoute = async ({ params, request }) => {
  try {
    const id = params.id;

    if (!id) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'ID do usuário é obrigatório.',
        }),
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    // Verificar autenticação
    const auth = checkAuth(request);

    if (!auth) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Não autorizado. Faça login para continuar.',
        }),
        {
          status: 401,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    if (!auth.canWrite) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Você não tem permissão para atualizar usuários.',
        }),
        {
          status: 403,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    // Obter dados do corpo da requisição
    const body = await request.json();

    // Verificar se o usuário atual pode atualizar o usuário com a função especificada
    if (body.role === 'admin') {
      const currentUser = await adminUserRepository.getById(auth.userId);

      if (!currentUser || currentUser.role !== 'admin') {
        return new Response(
          JSON.stringify({
            success: false,
            error: 'Apenas administradores podem atualizar outros administradores.',
          }),
          {
            status: 403,
            headers: {
              'Content-Type': 'application/json',
            },
          }
        );
      }
    }

    // Executar caso de uso
    const result = await updateAdminUserUseCase.execute({
      id,
      username: body.username,
      role: body.role as AdminRole,
      permissions: body.permissions as AdminPermission[],
      profile: body.profile,
      isActive: body.isActive,
    });

    if (result.success && result.data) {
      // Remover senha hash da resposta
      const { passwordHash, ...userWithoutPassword } = result.data;

      return new Response(
        JSON.stringify({
          success: true,
          data: userWithoutPassword,
        }),
        {
          status: 200,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }
    return new Response(
      JSON.stringify({
        success: false,
        error: result.error || 'Erro desconhecido ao atualizar usuário.',
      }),
      {
        status: 400,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  } catch (error) {
    console.error('Erro ao processar atualização de usuário administrativo:', error);

    return new Response(
      JSON.stringify({
        success: false,
        error: 'Ocorreu um erro ao processar a atualização do usuário. Por favor, tente novamente.',
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  }
};

export const DELETE: APIRoute = async ({ params, request }) => {
  try {
    const id = params.id;

    if (!id) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'ID do usuário é obrigatório.',
        }),
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    // Verificar autenticação
    const auth = checkAuth(request);

    if (!auth) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Não autorizado. Faça login para continuar.',
        }),
        {
          status: 401,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    if (!auth.canDelete) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Você não tem permissão para excluir usuários.',
        }),
        {
          status: 403,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    // Executar caso de uso
    const result = await deleteAdminUserUseCase.execute({
      id,
      currentUserId: auth.userId,
    });

    if (result.success) {
      return new Response(
        JSON.stringify({
          success: true,
          message: 'Usuário excluído com sucesso.',
        }),
        {
          status: 200,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }
    return new Response(
      JSON.stringify({
        success: false,
        error: result.error || 'Erro desconhecido ao excluir usuário.',
      }),
      {
        status: 400,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  } catch (error) {
    console.error('Erro ao processar exclusão de usuário administrativo:', error);

    return new Response(
      JSON.stringify({
        success: false,
        error: 'Ocorreu um erro ao processar a exclusão do usuário. Por favor, tente novamente.',
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  }
};
