/**
 * Produtor de eventos de usuários
 *
 * Este serviço é responsável por produzir eventos relacionados a usuários
 * para o Kafka.
 */

import { UserEvent, eventProducerService } from '@services/eventProducerService';
import { logger } from '@utils/logger';

/**
 * Interface para dados de usuário
 */
interface UserData {
  userId: string;
  email?: string;
  name?: string;
  metadata?: Record<string, unknown>;
}

/**
 * Interface para dados de autenticação
 */
interface AuthData {
  userId: string;
  email: string;
  ipAddress?: string;
  userAgent?: string;
  metadata?: Record<string, unknown>;
}

/**
 * Interface para dados de assinatura
 */
interface SubscriptionData {
  userId: string;
  subscriptionId: string;
  plan: string;
  status: string;
  startDate?: string;
  endDate?: string;
  metadata?: Record<string, unknown>;
}

/**
 * Produtor de eventos de usuário
 */
export const userEventProducer = {
  /**
   * Envia evento de registro de usuário
   * @param userData - Dados do usuário
   */
  async userRegistered(userData: UserData): Promise<void> {
    try {
      const event: UserEvent = {
        ...eventProducerService.createBaseEvent(`user-registered-${userData.userId}`, {
          ...(userData.metadata || {}),
          name: userData.name,
        }),
        userId: userData.userId,
        email: userData.email,
        action: 'registered',
      };

      await eventProducerService.sendUserEvent('registered', event);

      logger.info(`Evento de registro de usuário enviado: ${userData.userId}`);
    } catch (error) {
      logger.error(`Erro ao enviar evento de registro de usuário ${userData.userId}:`, error);
      // Não propagar erro para não interromper o fluxo principal
    }
  },

  /**
   * Envia evento de atualização de perfil de usuário
   * @param userData - Dados do usuário
   * @param changedFields - Campos alterados
   */
  async profileUpdated(userData: UserData, changedFields: string[]): Promise<void> {
    try {
      const event: UserEvent = {
        ...eventProducerService.createBaseEvent(`user-profile-updated-${userData.userId}`, {
          ...(userData.metadata || {}),
          name: userData.name,
          changedFields,
        }),
        userId: userData.userId,
        email: userData.email,
        action: 'profile_updated',
      };

      await eventProducerService.sendUserEvent('profile.updated', event);

      logger.info(`Evento de atualização de perfil de usuário enviado: ${userData.userId}`);
    } catch (error) {
      logger.error(
        `Erro ao enviar evento de atualização de perfil de usuário ${userData.userId}:`,
        error
      );
      // Não propagar erro para não interromper o fluxo principal
    }
  },

  /**
   * Envia evento de login de usuário
   * @param authData - Dados de autenticação
   */
  async userLoggedIn(authData: AuthData): Promise<void> {
    try {
      const event: UserEvent = {
        ...eventProducerService.createBaseEvent(`user-login-${authData.userId}-${Date.now()}`, {
          ...(authData.metadata || {}),
          ipAddress: authData.ipAddress,
          userAgent: authData.userAgent,
        }),
        userId: authData.userId,
        email: authData.email,
        action: 'login',
      };

      await eventProducerService.sendUserEvent('auth.login', event);

      logger.info(`Evento de login de usuário enviado: ${authData.userId}`);
    } catch (error) {
      logger.error(`Erro ao enviar evento de login de usuário ${authData.userId}:`, error);
      // Não propagar erro para não interromper o fluxo principal
    }
  },

  /**
   * Envia evento de logout de usuário
   * @param authData - Dados de autenticação
   */
  async userLoggedOut(authData: AuthData): Promise<void> {
    try {
      const event: UserEvent = {
        ...eventProducerService.createBaseEvent(`user-logout-${authData.userId}-${Date.now()}`, {
          ...(authData.metadata || {}),
          ipAddress: authData.ipAddress,
          userAgent: authData.userAgent,
        }),
        userId: authData.userId,
        email: authData.email,
        action: 'logout',
      };

      await eventProducerService.sendUserEvent('auth.logout', event);

      logger.info(`Evento de logout de usuário enviado: ${authData.userId}`);
    } catch (error) {
      logger.error(`Erro ao enviar evento de logout de usuário ${authData.userId}:`, error);
      // Não propagar erro para não interromper o fluxo principal
    }
  },

  /**
   * Envia evento de falha de autenticação
   * @param authData - Dados de autenticação
   * @param reason - Motivo da falha
   */
  async authenticationFailed(authData: Partial<AuthData>, reason: string): Promise<void> {
    try {
      const event: UserEvent = {
        ...eventProducerService.createBaseEvent(
          `auth-failed-${authData.email || 'unknown'}-${Date.now()}`,
          {
            ...(authData.metadata || {}),
            ipAddress: authData.ipAddress,
            userAgent: authData.userAgent,
            reason,
          }
        ),
        userId: authData.userId || 'unknown',
        email: authData.email,
        action: 'auth_failed',
      };

      await eventProducerService.sendUserEvent('auth.failed', event);

      logger.info(`Evento de falha de autenticação enviado: ${authData.email || 'unknown'}`);
    } catch (error) {
      logger.error('Erro ao enviar evento de falha de autenticação:', error);
      // Não propagar erro para não interromper o fluxo principal
    }
  },

  /**
   * Envia evento de mudança de assinatura
   * @param subscriptionData - Dados da assinatura
   * @param previousStatus - Status anterior
   */
  async subscriptionChanged(
    subscriptionData: SubscriptionData,
    previousStatus?: string
  ): Promise<void> {
    try {
      const event: UserEvent = {
        ...eventProducerService.createBaseEvent(
          `subscription-changed-${subscriptionData.userId}-${subscriptionData.subscriptionId}`,
          {
            ...(subscriptionData.metadata || {}),
            subscriptionId: subscriptionData.subscriptionId,
            plan: subscriptionData.plan,
            status: subscriptionData.status,
            previousStatus,
            startDate: subscriptionData.startDate,
            endDate: subscriptionData.endDate,
          }
        ),
        userId: subscriptionData.userId,
        action: 'subscription_changed',
      };

      await eventProducerService.sendUserEvent('subscription.changed', event);

      logger.info(
        `Evento de mudança de assinatura enviado: ${subscriptionData.userId} (${subscriptionData.subscriptionId})`
      );
    } catch (error) {
      logger.error(
        `Erro ao enviar evento de mudança de assinatura ${subscriptionData.subscriptionId}:`,
        error
      );
      // Não propagar erro para não interromper o fluxo principal
    }
  },
};
