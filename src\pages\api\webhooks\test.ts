import { isAuthenticated } from '@middleware/authMiddleware';
import { WebhookEvent } from '@models/WebhookSubscription';
import { webhookRepository } from '@repositories/webhookRepository';
import { outgoingWebhookService } from '@services/outgoingWebhookService';
import { validateSchema } from '@utils/validation';
// src/pages/api/webhooks/test.ts
import type { APIRoute } from 'astro';
import { z } from 'zod';

// Schema para validação de teste de webhook
const testWebhookSchema = z.object({
  subscriptionId: z.string().uuid(),
  event: z.nativeEnum(WebhookEvent),
  payload: z.record(z.any()).optional(),
});

/**
 * Endpoint para testar o envio de um webhook
 */
export const POST: APIRoute = async ({ request, cookies }) => {
  try {
    // Verificar autenticação
    const authResult = await isAuthenticated({ request, cookies } as any);

    if (!authResult.isAuthenticated) {
      return new Response(JSON.stringify({ error: 'Não autorizado' }), {
        status: 401,
        headers: {
          'Content-Type': 'application/json',
        },
      });
    }

    // Obter corpo da requisição
    const body = await request.json();

    // Validar dados
    const validationResult = validateSchema(testWebhookSchema, body);

    if (!validationResult.success) {
      return new Response(
        JSON.stringify({
          error: 'Dados inválidos',
          details: validationResult.errors,
        }),
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    // Verificar se assinatura existe
    const subscription = await webhookRepository.getSubscriptionById(body.subscriptionId);

    if (!subscription) {
      return new Response(JSON.stringify({ error: 'Assinatura de webhook não encontrada' }), {
        status: 404,
        headers: {
          'Content-Type': 'application/json',
        },
      });
    }

    // Criar payload de teste
    const testPayload = body.payload || {
      test: true,
      message: 'Este é um webhook de teste',
      timestamp: new Date().toISOString(),
    };

    // Enviar webhook de teste
    const delivery = await outgoingWebhookService.sendToSubscription(
      subscription,
      body.event,
      testPayload
    );

    // Retornar resposta
    return new Response(
      JSON.stringify({
        message: 'Webhook de teste enviado',
        delivery,
      }),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  } catch (error) {
    console.error('Erro ao testar webhook:', error);

    return new Response(
      JSON.stringify({
        error: error instanceof Error ? error.message : 'Erro interno',
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  }
};
