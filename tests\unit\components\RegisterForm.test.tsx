/**
 * Testes para o componente RegisterForm
 * 
 * Este arquivo contém testes unitários para o componente de formulário de registro.
 * Parte da implementação da tarefa 9.1.1 - Testes unitários
 */

import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { RegisterForm } from '../../../src/components/RegisterForm';

// Mock do serviço de usuário
const mockRegister = vi.fn();
vi.mock('../../../src/domain/services/UserService', () => ({
  UserService: vi.fn().mockImplementation(() => ({
    register: mockRegister
  }))
}));

// Mock do hook de navegação
const mockNavigate = vi.fn();
vi.mock('react-router-dom', () => ({
  useNavigate: () => mockNavigate
}));

describe('RegisterForm', () => {
  beforeEach(() => {
    // Limpar mocks antes de cada teste
    vi.clearAllMocks();
  });
  
  it('should render the register form correctly', () => {
    render(<RegisterForm />);
    
    // Verificar se os elementos do formulário estão presentes
    expect(screen.getByLabelText(/nome/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/email/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/senha/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/confirmar senha/i)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /criar conta/i })).toBeInTheDocument();
    expect(screen.getByText(/já tem uma conta/i)).toBeInTheDocument();
  });
  
  it('should show validation errors for empty fields', async () => {
    render(<RegisterForm />);
    
    // Clicar no botão de registro sem preencher os campos
    fireEvent.click(screen.getByRole('button', { name: /criar conta/i }));
    
    // Verificar se as mensagens de erro são exibidas
    await waitFor(() => {
      expect(screen.getByText(/nome é obrigatório/i)).toBeInTheDocument();
      expect(screen.getByText(/email é obrigatório/i)).toBeInTheDocument();
      expect(screen.getByText(/senha é obrigatória/i)).toBeInTheDocument();
      expect(screen.getByText(/confirmação de senha é obrigatória/i)).toBeInTheDocument();
    });
  });
  
  it('should show validation error for invalid email', async () => {
    render(<RegisterForm />);
    
    // Preencher os campos
    fireEvent.change(screen.getByLabelText(/nome/i), {
      target: { value: 'Test User' }
    });
    
    fireEvent.change(screen.getByLabelText(/email/i), {
      target: { value: 'invalid-email' }
    });
    
    fireEvent.change(screen.getByLabelText(/senha/i), {
      target: { value: 'password123' }
    });
    
    fireEvent.change(screen.getByLabelText(/confirmar senha/i), {
      target: { value: 'password123' }
    });
    
    // Clicar no botão de registro
    fireEvent.click(screen.getByRole('button', { name: /criar conta/i }));
    
    // Verificar se a mensagem de erro é exibida
    await waitFor(() => {
      expect(screen.getByText(/email inválido/i)).toBeInTheDocument();
    });
  });
  
  it('should show validation error when passwords do not match', async () => {
    render(<RegisterForm />);
    
    // Preencher os campos
    fireEvent.change(screen.getByLabelText(/nome/i), {
      target: { value: 'Test User' }
    });
    
    fireEvent.change(screen.getByLabelText(/email/i), {
      target: { value: '<EMAIL>' }
    });
    
    fireEvent.change(screen.getByLabelText(/senha/i), {
      target: { value: 'password123' }
    });
    
    fireEvent.change(screen.getByLabelText(/confirmar senha/i), {
      target: { value: 'different_password' }
    });
    
    // Clicar no botão de registro
    fireEvent.click(screen.getByRole('button', { name: /criar conta/i }));
    
    // Verificar se a mensagem de erro é exibida
    await waitFor(() => {
      expect(screen.getByText(/as senhas não coincidem/i)).toBeInTheDocument();
    });
  });
  
  it('should show validation error for short password', async () => {
    render(<RegisterForm />);
    
    // Preencher os campos
    fireEvent.change(screen.getByLabelText(/nome/i), {
      target: { value: 'Test User' }
    });
    
    fireEvent.change(screen.getByLabelText(/email/i), {
      target: { value: '<EMAIL>' }
    });
    
    fireEvent.change(screen.getByLabelText(/senha/i), {
      target: { value: '123' }
    });
    
    fireEvent.change(screen.getByLabelText(/confirmar senha/i), {
      target: { value: '123' }
    });
    
    // Clicar no botão de registro
    fireEvent.click(screen.getByRole('button', { name: /criar conta/i }));
    
    // Verificar se a mensagem de erro é exibida
    await waitFor(() => {
      expect(screen.getByText(/senha deve ter pelo menos 8 caracteres/i)).toBeInTheDocument();
    });
  });
  
  it('should call register service and navigate on successful registration', async () => {
    // Configurar mock para retornar sucesso
    mockRegister.mockResolvedValue({
      id: '1',
      name: 'Test User',
      email: '<EMAIL>',
      role: 'user'
    });
    
    render(<RegisterForm />);
    
    // Preencher o formulário
    fireEvent.change(screen.getByLabelText(/nome/i), {
      target: { value: 'Test User' }
    });
    
    fireEvent.change(screen.getByLabelText(/email/i), {
      target: { value: '<EMAIL>' }
    });
    
    fireEvent.change(screen.getByLabelText(/senha/i), {
      target: { value: 'password123' }
    });
    
    fireEvent.change(screen.getByLabelText(/confirmar senha/i), {
      target: { value: 'password123' }
    });
    
    // Clicar no botão de registro
    fireEvent.click(screen.getByRole('button', { name: /criar conta/i }));
    
    // Verificar se o serviço de registro foi chamado com os parâmetros corretos
    await waitFor(() => {
      expect(mockRegister).toHaveBeenCalledWith({
        name: 'Test User',
        email: '<EMAIL>',
        password: 'password123'
      });
    });
    
    // Verificar se o usuário foi redirecionado
    await waitFor(() => {
      expect(mockNavigate).toHaveBeenCalledWith('/login', { state: { message: 'Conta criada com sucesso! Faça login para continuar.' } });
    });
  });
  
  it('should show error message on registration failure', async () => {
    // Configurar mock para retornar erro
    mockRegister.mockRejectedValue(new Error('Email já está em uso'));
    
    render(<RegisterForm />);
    
    // Preencher o formulário
    fireEvent.change(screen.getByLabelText(/nome/i), {
      target: { value: 'Test User' }
    });
    
    fireEvent.change(screen.getByLabelText(/email/i), {
      target: { value: '<EMAIL>' }
    });
    
    fireEvent.change(screen.getByLabelText(/senha/i), {
      target: { value: 'password123' }
    });
    
    fireEvent.change(screen.getByLabelText(/confirmar senha/i), {
      target: { value: 'password123' }
    });
    
    // Clicar no botão de registro
    fireEvent.click(screen.getByRole('button', { name: /criar conta/i }));
    
    // Verificar se a mensagem de erro é exibida
    await waitFor(() => {
      expect(screen.getByText(/email já está em uso/i)).toBeInTheDocument();
    });
    
    // Verificar se o usuário não foi redirecionado
    expect(mockNavigate).not.toHaveBeenCalled();
  });
  
  it('should disable the register button while submitting', async () => {
    // Configurar mock para demorar um pouco para resolver
    mockRegister.mockImplementation(() => new Promise(resolve => {
      setTimeout(() => {
        resolve({
          id: '1',
          name: 'Test User',
          email: '<EMAIL>',
          role: 'user'
        });
      }, 100);
    }));
    
    render(<RegisterForm />);
    
    // Preencher o formulário
    fireEvent.change(screen.getByLabelText(/nome/i), {
      target: { value: 'Test User' }
    });
    
    fireEvent.change(screen.getByLabelText(/email/i), {
      target: { value: '<EMAIL>' }
    });
    
    fireEvent.change(screen.getByLabelText(/senha/i), {
      target: { value: 'password123' }
    });
    
    fireEvent.change(screen.getByLabelText(/confirmar senha/i), {
      target: { value: 'password123' }
    });
    
    // Clicar no botão de registro
    fireEvent.click(screen.getByRole('button', { name: /criar conta/i }));
    
    // Verificar se o botão está desabilitado durante o envio
    expect(screen.getByRole('button', { name: /criar conta/i })).toBeDisabled();
    
    // Verificar se o botão é habilitado novamente após o registro
    await waitFor(() => {
      expect(screen.getByRole('button', { name: /criar conta/i })).not.toBeDisabled();
    });
  });
  
  it('should navigate to login page when clicking on login link', () => {
    render(<RegisterForm />);
    
    // Clicar no link de login
    fireEvent.click(screen.getByText(/já tem uma conta/i));
    
    // Verificar se o usuário foi redirecionado
    expect(mockNavigate).toHaveBeenCalledWith('/login');
  });
});
