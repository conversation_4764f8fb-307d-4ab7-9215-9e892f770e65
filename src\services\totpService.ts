/**
 * Serviço para autenticação de dois fatores usando TOTP (Time-based One-Time Password)
 *
 * Este serviço implementa a geração e validação de códigos TOTP para autenticação de dois fatores,
 * seguindo a especificação RFC 6238.
 *
 * Parte da implementação da tarefa 6.1.2 - Implementação de 2FA
 */

import crypto from 'node:crypto';
import { base32Decode, base32Encode } from '../utils/base32';
import { logger } from '../utils/logger';

// Configurações padrão para TOTP
const DEFAULT_OPTIONS = {
  digits: 6, // Número de dígitos no código
  step: 30, // Intervalo de tempo em segundos
  window: 1, // Janela de validação (número de passos antes/depois)
  algorithm: 'sha1', // Algoritmo de hash
  encoding: 'base32', // Codificação do segredo
  issuer: 'Efi Educacional', // Emissor para URI
};

// Interface para opções do TOTP
export interface TotpOptions {
  digits?: number; // Número de dígitos no código
  step?: number; // Intervalo de tempo em segundos
  window?: number; // Janela de validação (número de passos antes/depois)
  algorithm?: 'sha1' | 'sha256' | 'sha512'; // Algoritmo de hash
  encoding?: 'base32' | 'hex'; // Codificação do segredo
  issuer?: string; // Emissor para URI
}

// Interface para resultado da verificação
export interface VerifyResult {
  success: boolean; // Resultado da verificação
  delta?: number; // Diferença de passos (se success=true)
}

/**
 * Serviço TOTP para autenticação de dois fatores
 */
export const totpService = {
  /**
   * Gera um segredo aleatório para TOTP
   * @param length - Comprimento do segredo em bytes (padrão: 20)
   * @returns Segredo codificado em base32
   */
  generateSecret(length = 20): string {
    try {
      // Gerar bytes aleatórios
      const buffer = crypto.randomBytes(length);

      // Codificar em base32
      return base32Encode(buffer);
    } catch (error) {
      logger.error('Erro ao gerar segredo TOTP:', error);
      throw new Error('Falha ao gerar segredo TOTP');
    }
  },

  /**
   * Gera um código TOTP baseado no segredo e no tempo atual
   * @param secret - Segredo codificado em base32
   * @param options - Opções para geração do código
   * @returns Código TOTP
   */
  generateCode(secret: string, options: TotpOptions = {}): string {
    try {
      // Mesclar opções com padrões
      const opts = { ...DEFAULT_OPTIONS, ...options };

      // Obter timestamp atual em segundos
      const now = Math.floor(Date.now() / 1000);

      // Calcular o passo de tempo atual
      const counter = Math.floor(now / opts.step);

      // Gerar código
      return this._generateCodeForCounter(secret, counter, opts);
    } catch (error) {
      logger.error('Erro ao gerar código TOTP:', error);
      throw new Error('Falha ao gerar código TOTP');
    }
  },

  /**
   * Verifica se um código TOTP é válido
   * @param token - Código TOTP fornecido pelo usuário
   * @param secret - Segredo codificado em base32
   * @param options - Opções para verificação do código
   * @returns Resultado da verificação
   */
  verifyCode(token: string, secret: string, options: TotpOptions = {}): VerifyResult {
    try {
      // Mesclar opções com padrões
      const opts = { ...DEFAULT_OPTIONS, ...options };

      // Obter timestamp atual em segundos
      const now = Math.floor(Date.now() / 1000);

      // Calcular o passo de tempo atual
      const currentCounter = Math.floor(now / opts.step);

      // Verificar o código na janela de tempo
      for (let i = -opts.window; i <= opts.window; i++) {
        const counter = currentCounter + i;
        const generatedToken = this._generateCodeForCounter(secret, counter, opts);

        if (this._constantTimeCompare(token, generatedToken)) {
          return { success: true, delta: i };
        }
      }

      // Código inválido
      return { success: false };
    } catch (error) {
      logger.error('Erro ao verificar código TOTP:', error);
      return { success: false };
    }
  },

  /**
   * Gera uma URI para configuração de aplicativos TOTP (como Google Authenticator)
   * @param secret - Segredo codificado em base32
   * @param accountName - Nome da conta (geralmente email do usuário)
   * @param options - Opções para geração da URI
   * @returns URI para configuração
   */
  generateUri(secret: string, accountName: string, options: TotpOptions = {}): string {
    try {
      // Mesclar opções com padrões
      const opts = { ...DEFAULT_OPTIONS, ...options };

      // Codificar parâmetros para URL
      const encodedIssuer = encodeURIComponent(opts.issuer);
      const encodedAccount = encodeURIComponent(accountName);
      const encodedSecret = encodeURIComponent(secret);

      // Construir URI
      let uri = `otpauth://totp/${encodedIssuer}:${encodedAccount}?secret=${encodedSecret}`;
      uri += `&issuer=${encodedIssuer}`;
      uri += `&algorithm=${opts.algorithm.toUpperCase()}`;
      uri += `&digits=${opts.digits}`;
      uri += `&period=${opts.step}`;

      return uri;
    } catch (error) {
      logger.error('Erro ao gerar URI TOTP:', error);
      throw new Error('Falha ao gerar URI TOTP');
    }
  },

  /**
   * Gera códigos de backup para recuperação
   * @param count - Número de códigos a serem gerados (padrão: 10)
   * @param length - Comprimento de cada código (padrão: 8)
   * @returns Array de códigos de backup
   */
  generateBackupCodes(count = 10, length = 8): string[] {
    try {
      const codes: string[] = [];

      for (let i = 0; i < count; i++) {
        // Gerar bytes aleatórios
        const buffer = crypto.randomBytes(Math.ceil(length / 2));

        // Converter para string hexadecimal e limitar ao comprimento desejado
        const code = buffer.toString('hex').slice(0, length).toUpperCase();

        // Adicionar código ao array
        codes.push(code);
      }

      return codes;
    } catch (error) {
      logger.error('Erro ao gerar códigos de backup:', error);
      throw new Error('Falha ao gerar códigos de backup');
    }
  },

  /**
   * Gera um código TOTP para um contador específico
   * @private
   */
  _generateCodeForCounter(secret: string, counter: number, options: TotpOptions): string {
    // Decodificar segredo
    let secretBuffer: Buffer;
    if (options.encoding === 'base32') {
      secretBuffer = base32Decode(secret);
    } else {
      secretBuffer = Buffer.from(secret, 'hex');
    }

    // Converter contador para buffer de 8 bytes (big-endian)
    const counterBuffer = Buffer.alloc(8);
    for (let i = 0; i < 8; i++) {
      counterBuffer[7 - i] = counter & 0xff;
      counter = counter >> 8;
    }

    // Calcular HMAC
    const hmac = crypto.createHmac(options.algorithm, secretBuffer);
    hmac.update(counterBuffer);
    const digest = hmac.digest();

    // Extrair valor do código usando o método de truncamento dinâmico
    const offset = digest[digest.length - 1] & 0xf;
    const binary =
      ((digest[offset] & 0x7f) << 24) |
      ((digest[offset + 1] & 0xff) << 16) |
      ((digest[offset + 2] & 0xff) << 8) |
      (digest[offset + 3] & 0xff);

    // Converter para string com o número correto de dígitos
    const token = binary % 10 ** options.digits;
    return token.toString().padStart(options.digits, '0');
  },

  /**
   * Compara duas strings em tempo constante para evitar ataques de timing
   * @private
   */
  _constantTimeCompare(a: string, b: string): boolean {
    if (a.length !== b.length) {
      return false;
    }

    let result = 0;
    for (let i = 0; i < a.length; i++) {
      result |= a.charCodeAt(i) ^ b.charCodeAt(i);
    }

    return result === 0;
  },
};

export default totpService;
