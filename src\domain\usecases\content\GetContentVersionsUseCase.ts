/**
 * Get Content Versions Use Case
 *
 * Caso de uso para obter as versões de um conteúdo.
 * Parte da implementação da tarefa 8.8.3 - Gestão de conteúdo
 */

import { ContentVersion } from '../../entities/Content';
import { ContentRepository } from '../../repositories/ContentRepository';

export interface GetContentVersionsRequest {
  contentId: string;
  page?: number;
  limit?: number;
}

export interface GetContentVersionsResponse {
  success: boolean;
  data?: {
    items: ContentVersion[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
  error?: string;
}

export class GetContentVersionsUseCase {
  constructor(private contentRepository: ContentRepository) {}

  async execute(request: GetContentVersionsRequest): Promise<GetContentVersionsResponse> {
    try {
      // Validar os dados de entrada
      if (!request.contentId) {
        return {
          success: false,
          error: 'O ID do conteúdo é obrigatório.',
        };
      }

      // Verificar se o conteúdo existe
      const content = await this.contentRepository.getById(request.contentId);

      if (!content) {
        return {
          success: false,
          error: `Conteúdo com ID ${request.contentId} não encontrado.`,
        };
      }

      // Definir paginação
      const pagination =
        request.page && request.limit
          ? { page: request.page, limit: request.limit }
          : { page: 1, limit: 10 };

      // Obter versões
      const versions = await this.contentRepository.getVersionsByContentId(
        request.contentId,
        pagination
      );

      return {
        success: true,
        data: versions,
      };
    } catch (error) {
      console.error('Erro ao obter versões do conteúdo:', error);

      return {
        success: false,
        error:
          error instanceof Error
            ? error.message
            : 'Erro desconhecido ao obter versões do conteúdo.',
      };
    }
  }
}
