-- Migração para criar tabelas de alertas do Kafka

-- Tabela para armazenar alertas do Kafka
CREATE TABLE IF NOT EXISTS tab_kafka_alerts (
  alert_id UUID PRIMARY KEY,
  type VARCHAR(50) NOT NULL,
  severity VARCHAR(20) NOT NULL,
  timestamp TIMESTAMP NOT NULL,
  message TEXT NOT NULL,
  details JSONB,
  status VARCHAR(20) NOT NULL DEFAULT 'active',
  resolved_at TIMESTAMP,
  acknowledged_by VARCHAR(100),
  acknowledged_at TIMESTAMP,
  created_at TIMESTAMP NOT NULL DEFAULT NOW()
);

-- Índices para melhorar performance de consultas
CREATE INDEX IF NOT EXISTS idx_kafka_alerts_type ON tab_kafka_alerts (type);
CREATE INDEX IF NOT EXISTS idx_kafka_alerts_severity ON tab_kafka_alerts (severity);
CREATE INDEX IF NOT EXISTS idx_kafka_alerts_timestamp ON tab_kafka_alerts (timestamp);
CREATE INDEX IF NOT EXISTS idx_kafka_alerts_status ON tab_kafka_alerts (status);

-- Tabela para armazenar métricas de alertas
CREATE TABLE IF NOT EXISTS tab_kafka_alert_metrics (
  metric_id UUID PRIMARY KEY,
  type VARCHAR(50) NOT NULL,
  severity VARCHAR(20) NOT NULL,
  count INTEGER NOT NULL,
  period_start TIMESTAMP NOT NULL,
  period_end TIMESTAMP NOT NULL,
  created_at TIMESTAMP NOT NULL DEFAULT NOW()
);

-- Índices para métricas
CREATE INDEX IF NOT EXISTS idx_kafka_alert_metrics_type ON tab_kafka_alert_metrics (type);
CREATE INDEX IF NOT EXISTS idx_kafka_alert_metrics_period ON tab_kafka_alert_metrics (period_start, period_end);

-- Função para limpar alertas antigos
CREATE OR REPLACE FUNCTION fn_clean_kafka_alerts(p_days INTEGER)
RETURNS INTEGER AS $$
DECLARE
  v_count INTEGER;
BEGIN
  DELETE FROM tab_kafka_alerts
  WHERE timestamp < NOW() - (p_days || ' days')::INTERVAL
  AND status != 'active';
  
  GET DIAGNOSTICS v_count = ROW_COUNT;
  RETURN v_count;
END;
$$ LANGUAGE plpgsql;

-- Visualização para resumo de alertas por tipo
CREATE OR REPLACE VIEW vw_kafka_alerts_summary AS
SELECT
  type,
  severity,
  status,
  COUNT(*) AS alert_count,
  MIN(timestamp) AS first_occurrence,
  MAX(timestamp) AS last_occurrence
FROM
  tab_kafka_alerts
WHERE
  timestamp > NOW() - INTERVAL '30 days'
GROUP BY
  type,
  severity,
  status
ORDER BY
  type,
  severity,
  status;

-- Visualização para alertas recentes
CREATE OR REPLACE VIEW vw_kafka_recent_alerts AS
SELECT
  alert_id,
  type,
  severity,
  timestamp,
  message,
  details,
  status,
  resolved_at,
  acknowledged_by,
  acknowledged_at
FROM
  tab_kafka_alerts
WHERE
  timestamp > NOW() - INTERVAL '24 hours'
ORDER BY
  timestamp DESC;

-- Função para obter alertas por tipo
CREATE OR REPLACE FUNCTION fn_get_kafka_alerts_by_type(
  p_type VARCHAR,
  p_start_time TIMESTAMP,
  p_end_time TIMESTAMP,
  p_status VARCHAR DEFAULT NULL
)
RETURNS TABLE (
  alert_id UUID,
  type VARCHAR,
  severity VARCHAR,
  timestamp TIMESTAMP,
  message TEXT,
  details JSONB,
  status VARCHAR,
  resolved_at TIMESTAMP,
  acknowledged_by VARCHAR,
  acknowledged_at TIMESTAMP
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    ka.alert_id,
    ka.type,
    ka.severity,
    ka.timestamp,
    ka.message,
    ka.details,
    ka.status,
    ka.resolved_at,
    ka.acknowledged_by,
    ka.acknowledged_at
  FROM
    tab_kafka_alerts ka
  WHERE
    ka.type = p_type
    AND ka.timestamp BETWEEN p_start_time AND p_end_time
    AND (p_status IS NULL OR ka.status = p_status)
  ORDER BY
    ka.timestamp DESC;
END;
$$ LANGUAGE plpgsql;

-- Função para gerar métricas de alertas
CREATE OR REPLACE FUNCTION fn_generate_kafka_alert_metrics(
  p_period_start TIMESTAMP,
  p_period_end TIMESTAMP
)
RETURNS VOID AS $$
BEGIN
  -- Inserir métricas por tipo e severidade
  INSERT INTO tab_kafka_alert_metrics (
    metric_id,
    type,
    severity,
    count,
    period_start,
    period_end,
    created_at
  )
  SELECT
    gen_random_uuid(),
    type,
    severity,
    COUNT(*),
    p_period_start,
    p_period_end,
    NOW()
  FROM
    tab_kafka_alerts
  WHERE
    timestamp BETWEEN p_period_start AND p_period_end
  GROUP BY
    type,
    severity;
END;
$$ LANGUAGE plpgsql;

-- Função para obter contagem de alertas por severidade
CREATE OR REPLACE FUNCTION fn_get_kafka_alert_counts_by_severity(
  p_start_time TIMESTAMP,
  p_end_time TIMESTAMP
)
RETURNS TABLE (
  severity VARCHAR,
  alert_count BIGINT
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    ka.severity,
    COUNT(*) AS alert_count
  FROM
    tab_kafka_alerts ka
  WHERE
    ka.timestamp BETWEEN p_start_time AND p_end_time
  GROUP BY
    ka.severity
  ORDER BY
    CASE
      WHEN ka.severity = 'info' THEN 1
      WHEN ka.severity = 'warning' THEN 2
      WHEN ka.severity = 'error' THEN 3
      WHEN ka.severity = 'critical' THEN 4
      ELSE 5
    END;
END;
$$ LANGUAGE plpgsql;
