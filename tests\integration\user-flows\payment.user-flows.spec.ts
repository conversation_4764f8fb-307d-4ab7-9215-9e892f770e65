/**
 * Testes de integração para fluxo de pagamento
 *
 * Este arquivo contém testes que verificam o fluxo completo de pagamento,
 * desde a seleção de produtos até a finalização do pedido.
 * Parte da implementação da tarefa 9.1.2 - Testes de integração
 */

import { expect, test } from '@playwright/test';

// Dados de teste
const testUser = {
  email: '<EMAIL>',
  password: 'User@123456',
  name: 'User Payment Test',
};

// Dados de cartão de crédito de teste
const testCreditCard = {
  number: '****************',
  name: 'User Test',
  expiry: '12/30',
  cvv: '123',
};

// Endereço de teste
const testAddress = {
  street: 'Rua de Teste',
  number: '123',
  complement: 'Apto 101',
  neighborhood: 'Bairro Teste',
  city: 'Cidade Teste',
  state: 'ST',
  zipCode: '12345-678',
  country: 'Brasil',
};

test.describe('Fluxo de Pagamento', () => {
  // Antes de cada teste, fazer login
  test.beforeEach(async ({ page }) => {
    // Registrar usuário se não existir
    await page.goto('/register');

    try {
      await page.fill('[data-testid="register-name"]', testUser.name);
      await page.fill('[data-testid="register-email"]', testUser.email);
      await page.fill('[data-testid="register-password"]', testUser.password);
      await page.fill('[data-testid="register-confirm-password"]', testUser.password);
      await page.click('[data-testid="register-button"]');

      // Esperar redirecionamento ou mensagem de sucesso
      await page
        .waitForURL('/login', { timeout: 5000 })
        .catch(() => console.log('Usuário já existe'));
    } catch (error) {
      console.log('Erro ao registrar ou usuário já existe:', error);
    }

    // Fazer login
    await page.goto('/login');
    await page.fill('[data-testid="login-email"]', testUser.email);
    await page.fill('[data-testid="login-password"]', testUser.password);
    await page.click('[data-testid="login-button"]');

    // Esperar redirecionamento para página inicial após login
    await page.waitForURL('/', { timeout: 5000 });
  });

  // Após todos os testes, excluir o usuário de teste
  test.afterAll(async ({ request }) => {
    try {
      // Fazer login para obter token
      const loginResponse = await request.post('/api/auth/login', {
        data: {
          email: testUser.email,
          password: testUser.password,
        },
      });

      if (loginResponse.ok()) {
        const userData = await loginResponse.json();
        const token = userData.accessToken;

        // Excluir usuário
        await request.delete('/api/users/me', {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });
      }
    } catch (error) {
      console.error('Erro ao limpar usuário de teste:', error);
    }
  });

  test('deve completar o fluxo de compra com cartão de crédito', async ({ page }) => {
    // Ir para a página de produtos
    await page.goto('/produtos');

    // Selecionar um produto
    await page.click('[data-testid="product-card"]:first-child');

    // Esperar carregamento da página de detalhes do produto
    await page.waitForSelector('[data-testid="product-details"]');

    // Adicionar ao carrinho
    await page.click('[data-testid="add-to-cart-button"]');

    // Verificar notificação de produto adicionado
    await expect(page.locator('[data-testid="notification"]')).toBeVisible();

    // Ir para o carrinho
    await page.click('[data-testid="cart-icon"]');

    // Verificar se o produto está no carrinho
    await expect(page.locator('[data-testid="cart-item"]')).toBeVisible();

    // Prosseguir para o checkout
    await page.click('[data-testid="checkout-button"]');

    // Preencher endereço de entrega
    await page.fill('[data-testid="shipping-street"]', testAddress.street);
    await page.fill('[data-testid="shipping-number"]', testAddress.number);
    await page.fill('[data-testid="shipping-complement"]', testAddress.complement);
    await page.fill('[data-testid="shipping-neighborhood"]', testAddress.neighborhood);
    await page.fill('[data-testid="shipping-city"]', testAddress.city);
    await page.fill('[data-testid="shipping-state"]', testAddress.state);
    await page.fill('[data-testid="shipping-zipcode"]', testAddress.zipCode);

    // Continuar para pagamento
    await page.click('[data-testid="continue-to-payment-button"]');

    // Selecionar método de pagamento: cartão de crédito
    await page.click('[data-testid="payment-method-credit-card"]');

    // Preencher dados do cartão
    await page.fill('[data-testid="card-number"]', testCreditCard.number);
    await page.fill('[data-testid="card-name"]', testCreditCard.name);
    await page.fill('[data-testid="card-expiry"]', testCreditCard.expiry);
    await page.fill('[data-testid="card-cvv"]', testCreditCard.cvv);

    // Finalizar pedido
    await page.click('[data-testid="place-order-button"]');

    // Esperar redirecionamento para página de confirmação
    await page.waitForURL(/\/pedidos\/[a-zA-Z0-9-]+\/confirmacao/);

    // Verificar elementos na página de confirmação
    await expect(page.locator('[data-testid="order-confirmation"]')).toBeVisible();
    await expect(page.locator('[data-testid="order-number"]')).toBeVisible();

    // Verificar status do pedido
    const orderStatus = await page.locator('[data-testid="order-status"]').textContent();
    expect(orderStatus).toContain('Pagamento aprovado');
  });

  test('deve completar o fluxo de compra com boleto', async ({ page }) => {
    // Ir para a página de produtos
    await page.goto('/produtos');

    // Selecionar um produto
    await page.click('[data-testid="product-card"]:first-child');

    // Esperar carregamento da página de detalhes do produto
    await page.waitForSelector('[data-testid="product-details"]');

    // Adicionar ao carrinho
    await page.click('[data-testid="add-to-cart-button"]');

    // Verificar notificação de produto adicionado
    await expect(page.locator('[data-testid="notification"]')).toBeVisible();

    // Ir para o carrinho
    await page.click('[data-testid="cart-icon"]');

    // Verificar se o produto está no carrinho
    await expect(page.locator('[data-testid="cart-item"]')).toBeVisible();

    // Prosseguir para o checkout
    await page.click('[data-testid="checkout-button"]');

    // Preencher endereço de entrega
    await page.fill('[data-testid="shipping-street"]', testAddress.street);
    await page.fill('[data-testid="shipping-number"]', testAddress.number);
    await page.fill('[data-testid="shipping-complement"]', testAddress.complement);
    await page.fill('[data-testid="shipping-neighborhood"]', testAddress.neighborhood);
    await page.fill('[data-testid="shipping-city"]', testAddress.city);
    await page.fill('[data-testid="shipping-state"]', testAddress.state);
    await page.fill('[data-testid="shipping-zipcode"]', testAddress.zipCode);

    // Continuar para pagamento
    await page.click('[data-testid="continue-to-payment-button"]');

    // Selecionar método de pagamento: boleto
    await page.click('[data-testid="payment-method-boleto"]');

    // Preencher CPF para boleto
    await page.fill('[data-testid="boleto-cpf"]', '123.456.789-09');

    // Finalizar pedido
    await page.click('[data-testid="place-order-button"]');

    // Esperar redirecionamento para página de confirmação
    await page.waitForURL(/\/pedidos\/[a-zA-Z0-9-]+\/confirmacao/);

    // Verificar elementos na página de confirmação
    await expect(page.locator('[data-testid="order-confirmation"]')).toBeVisible();
    await expect(page.locator('[data-testid="order-number"]')).toBeVisible();

    // Verificar status do pedido
    const orderStatus = await page.locator('[data-testid="order-status"]').textContent();
    expect(orderStatus).toContain('Aguardando pagamento');

    // Verificar se o boleto foi gerado
    await expect(page.locator('[data-testid="boleto-code"]')).toBeVisible();
    await expect(page.locator('[data-testid="boleto-barcode"]')).toBeVisible();
  });

  test('deve exibir erro ao tentar comprar produto sem estoque', async ({ page }) => {
    // Ir para a página de produtos
    await page.goto('/produtos');

    // Selecionar um produto sem estoque (assumindo que existe um produto marcado como sem estoque)
    await page.click('[data-testid="product-card"][data-out-of-stock="true"]');

    // Esperar carregamento da página de detalhes do produto
    await page.waitForSelector('[data-testid="product-details"]');

    // Verificar se o botão de adicionar ao carrinho está desabilitado
    await expect(page.locator('[data-testid="add-to-cart-button"]')).toBeDisabled();

    // Verificar se a mensagem de sem estoque está visível
    await expect(page.locator('[data-testid="out-of-stock-message"]')).toBeVisible();
  });

  test('deve validar campos obrigatórios no checkout', async ({ page }) => {
    // Ir para a página de produtos
    await page.goto('/produtos');

    // Selecionar um produto
    await page.click('[data-testid="product-card"]:first-child');

    // Adicionar ao carrinho
    await page.click('[data-testid="add-to-cart-button"]');

    // Ir para o carrinho
    await page.click('[data-testid="cart-icon"]');

    // Prosseguir para o checkout
    await page.click('[data-testid="checkout-button"]');

    // Tentar continuar sem preencher os campos
    await page.click('[data-testid="continue-to-payment-button"]');

    // Verificar mensagens de erro
    await expect(page.locator('[data-testid="error-shipping-street"]')).toBeVisible();
    await expect(page.locator('[data-testid="error-shipping-number"]')).toBeVisible();
    await expect(page.locator('[data-testid="error-shipping-city"]')).toBeVisible();
    await expect(page.locator('[data-testid="error-shipping-state"]')).toBeVisible();
    await expect(page.locator('[data-testid="error-shipping-zipcode"]')).toBeVisible();
  });
});
