---
/**
 * Página de alertas de segurança
 *
 * Esta página exibe e permite gerenciar alertas de segurança do sistema.
 */

import SecurityAlertsList from '@components/admin/SecurityAlertsList.astro';
import PermissionGate from '@components/auth/PermissionGate.astro';
// Importações
import MainLayout from '@layouts/MainLayout.astro';
import { SecurityAlertSeverity } from '@services/securityAlertService';
import { getCurrentUser } from '@utils/authUtils';
import { logger } from '@utils/logger';

// Verificar autenticação
const user = await getCurrentUser(Astro.cookies);

// Redirecionar se não estiver autenticado
if (!user) {
  return Astro.redirect('/signin?redirect=/admin/security/alerts');
}

// Obter parâmetros de consulta
const showResolved = Astro.url.searchParams.get('showResolved') === 'true';
const severity = Astro.url.searchParams.get('severity') || undefined;
const page = Number.parseInt(Astro.url.searchParams.get('page') || '1', 10);
const pageSize = Number.parseInt(Astro.url.searchParams.get('pageSize') || '20', 10);

// Buscar alertas de segurança
let alerts = [];
let totalAlerts = 0;
let totalPages = 1;

try {
  // Construir consulta
  const query = `
    SELECT 
      a.ulid_security_alert as id,
      a.alert_type,
      a.title,
      a.description,
      a.severity,
      a.ulid_user,
      a.user_name,
      a.ip_address,
      a.resource,
      a.resource_id,
      a.related_event_ids,
      a.metadata,
      a.is_resolved,
      a.resolved_by,
      u.name as resolved_by_name,
      a.resolved_at,
      a.resolution_comment,
      a.created_at
    FROM tab_security_alerts a
    LEFT JOIN tab_users u ON a.resolved_by = u.ulid_user
    WHERE 1=1
    ${!showResolved ? 'AND a.is_resolved = FALSE' : ''}
    ${severity ? `AND a.severity = '${severity}'` : ''}
    ORDER BY a.created_at DESC
    LIMIT $1 OFFSET $2
  `;

  // Executar consulta
  const result = await pgHelper.query(query, [pageSize, (page - 1) * pageSize]);

  // Processar resultados
  alerts = result.rows.map((row) => ({
    ...row,
    metadata: row.metadata ? JSON.parse(row.metadata) : null,
    related_event_ids: row.related_event_ids ? JSON.parse(row.related_event_ids) : [],
  }));

  // Contar total de alertas
  const countQuery = `
    SELECT COUNT(*) as total
    FROM tab_security_alerts
    WHERE 1=1
    ${!showResolved ? 'AND is_resolved = FALSE' : ''}
    ${severity ? `AND severity = '${severity}'` : ''}
  `;

  const countResult = await pgHelper.query(countQuery);
  totalAlerts = Number.parseInt(countResult.rows[0].total, 10);
  totalPages = Math.ceil(totalAlerts / pageSize);
} catch (error) {
  logger.error('Erro ao buscar alertas de segurança:', error);
}

// Título da página
const title = 'Alertas de Segurança';
---

<MainLayout title={title}>
  <main class="container mx-auto px-4 py-8">
    <PermissionGate resource="security" action="read">
      <div class="flex items-center justify-between mb-6">
        <h1 class="text-3xl font-bold">{title}</h1>
        
        <div class="flex space-x-2">
          <a 
            href="/admin" 
            class="bg-gray-200 text-gray-800 px-4 py-2 rounded hover:bg-gray-300 transition"
          >
            Voltar
          </a>
        </div>
      </div>
      
      <!-- Filtros -->
      <div class="bg-white rounded-lg shadow p-6 mb-6">
        <form method="get" class="flex flex-wrap gap-4 items-end">
          <!-- Mostrar resolvidos -->
          <div>
            <label class="flex items-center">
              <input 
                type="checkbox" 
                name="showResolved" 
                value="true"
                checked={showResolved}
                class="h-4 w-4 text-blue-600 border-gray-300 rounded"
              >
              <span class="ml-2 text-sm text-gray-700">Mostrar alertas resolvidos</span>
            </label>
          </div>
          
          <!-- Severidade -->
          <div>
            <label for="severity" class="block text-sm font-medium text-gray-700 mb-1">Severidade</label>
            <select 
              id="severity" 
              name="severity" 
              class="px-3 py-2 border border-gray-300 rounded-md"
            >
              <option value="">Todas</option>
              <option value={SecurityAlertSeverity.LOW} selected={severity === SecurityAlertSeverity.LOW}>Baixa</option>
              <option value={SecurityAlertSeverity.MEDIUM} selected={severity === SecurityAlertSeverity.MEDIUM}>Média</option>
              <option value={SecurityAlertSeverity.HIGH} selected={severity === SecurityAlertSeverity.HIGH}>Alta</option>
              <option value={SecurityAlertSeverity.CRITICAL} selected={severity === SecurityAlertSeverity.CRITICAL}>Crítica</option>
            </select>
          </div>
          
          <!-- Botões -->
          <div class="ml-auto">
            <button 
              type="submit"
              class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition"
            >
              Filtrar
            </button>
          </div>
        </form>
      </div>
      
      <!-- Resumo -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h3 class="text-lg font-medium text-blue-800">Baixa</h3>
          <p class="text-3xl font-bold text-blue-600 mt-2">
            {alerts.filter(a => a.severity === SecurityAlertSeverity.LOW && !a.is_resolved).length}
          </p>
          <p class="text-sm text-blue-600 mt-1">alertas ativos</p>
        </div>
        
        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <h3 class="text-lg font-medium text-yellow-800">Média</h3>
          <p class="text-3xl font-bold text-yellow-600 mt-2">
            {alerts.filter(a => a.severity === SecurityAlertSeverity.MEDIUM && !a.is_resolved).length}
          </p>
          <p class="text-sm text-yellow-600 mt-1">alertas ativos</p>
        </div>
        
        <div class="bg-red-50 border border-red-200 rounded-lg p-4">
          <h3 class="text-lg font-medium text-red-800">Alta</h3>
          <p class="text-3xl font-bold text-red-600 mt-2">
            {alerts.filter(a => a.severity === SecurityAlertSeverity.HIGH && !a.is_resolved).length}
          </p>
          <p class="text-sm text-red-600 mt-1">alertas ativos</p>
        </div>
        
        <div class="bg-purple-50 border border-purple-200 rounded-lg p-4">
          <h3 class="text-lg font-medium text-purple-800">Crítica</h3>
          <p class="text-3xl font-bold text-purple-600 mt-2">
            {alerts.filter(a => a.severity === SecurityAlertSeverity.CRITICAL && !a.is_resolved).length}
          </p>
          <p class="text-sm text-purple-600 mt-1">alertas ativos</p>
        </div>
      </div>
      
      <!-- Lista de alertas -->
      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center justify-between mb-4">
          <h2 class="text-xl font-semibold">Alertas</h2>
          <p class="text-sm text-gray-600">
            Mostrando {alerts.length} de {totalAlerts} alertas
          </p>
        </div>
        
        <SecurityAlertsList 
          alerts={alerts}
          showUnresolvedOnly={!showResolved}
          allowResolve={true}
        />
        
        <!-- Paginação -->
        {totalPages > 1 && (
          <div class="mt-6 flex items-center justify-between">
            <div class="text-sm text-gray-700">
              Página <span class="font-medium">{page}</span> de <span class="font-medium">{totalPages}</span>
            </div>
            
            <div class="flex space-x-2">
              {page > 1 && (
                <a 
                  href={`/admin/security/alerts?page=${page - 1}&pageSize=${pageSize}${showResolved ? '&showResolved=true' : ''}${severity ? `&severity=${severity}` : ''}`}
                  class="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50 transition"
                >
                  Anterior
                </a>
              )}
              
              {page < totalPages && (
                <a 
                  href={`/admin/security/alerts?page=${page + 1}&pageSize=${pageSize}${showResolved ? '&showResolved=true' : ''}${severity ? `&severity=${severity}` : ''}`}
                  class="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50 transition"
                >
                  Próxima
                </a>
              )}
            </div>
          </div>
        )}
      </div>
      
      <slot name="fallback">
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mt-6">
          <p>Você não tem permissão para acessar os alertas de segurança.</p>
          <p class="mt-2">
            <a href="/" class="text-red-700 font-medium hover:underline">Voltar para a página inicial</a>
          </p>
        </div>
      </slot>
    </PermissionGate>
  </main>
</MainLayout>

<style>
  /* Estilos específicos da página */
</style>
