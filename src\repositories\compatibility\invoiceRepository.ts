/**
 * Camada de compatibilidade para o repositório de faturas
 *
 * Este arquivo mantém a compatibilidade com o código existente que
 * utiliza a estrutura antiga do repositório de faturas.
 *
 * @deprecated Use a nova estrutura de repositórios em src/repositories
 */

import type { QueryResult } from 'pg';
import { pgHelper } from '../../repository/pgHelper';
import { repositories } from '../index';

/**
 * Cria uma nova fatura
 *
 * @param ulid_order ID do pedido
 * @param ulid_user ID do usuário
 * @param cod_status Código do status
 * @param value Valor da fatura
 * @param tax Valor do imposto
 * @param invoice_no Número da fatura
 * @returns Resultado da consulta
 */
async function create(
  ulid_order: string,
  ulid_user: string,
  cod_status: number,
  value: number,
  tax: number,
  invoice_no: string
): Promise<QueryResult> {
  try {
    // Usar a nova implementação
    const invoice = await repositories.invoiceRepository.create({
      orderId: ulid_order,
      userId: ulid_user,
      statusCode: cod_status,
      value,
      tax,
      invoiceNumber: invoice_no,
    });

    // Converter para o formato esperado pelo código antigo
    return {
      rows: [invoice],
      rowCount: 1,
      command: 'INSERT',
      oid: 0,
      fields: [],
    };
  } catch (error) {
    console.error('Error in compatibility layer (invoiceRepository.create):', error);

    // Fallback para a implementação antiga em caso de erro
    // Corrigindo o SQL que tinha um erro na query original (aspas simples nos placeholders)
    return pgHelper.query(
      `INSERT INTO tab_invoice(
         ulid_order,
         ulid_user,
         cod_status,
         value,
         tax,
         invoice_no) 
       VALUES ($1, $2, $3, $4, $5, $6) 
       RETURNING *`,
      [ulid_order, ulid_user, cod_status, value, tax, invoice_no]
    );
  }
}

/**
 * Busca faturas
 *
 * @param ulid_invoice ID da fatura (opcional)
 * @param ulid_order ID do pedido (opcional)
 * @param ulid_user ID do usuário (opcional)
 * @param cod_status Código do status (opcional)
 * @returns Resultado da consulta
 */
async function read(
  ulid_invoice?: string,
  ulid_order?: string,
  ulid_user?: string,
  cod_status?: number
): Promise<QueryResult> {
  try {
    // Usar a nova implementação
    const invoices = await repositories.invoiceRepository.findAll({
      id: ulid_invoice,
      orderId: ulid_order,
      userId: ulid_user,
      statusCode: cod_status,
    });

    // Converter para o formato esperado pelo código antigo
    return {
      rows: invoices,
      rowCount: invoices.length,
      command: 'SELECT',
      oid: 0,
      fields: [],
    };
  } catch (error) {
    console.error('Error in compatibility layer (invoiceRepository.read):', error);

    // Fallback para a implementação antiga em caso de erro
    return pgHelper.query(
      `SELECT * 
         FROM tab_invoice 
        WHERE TRUE 
          ${ulid_invoice ? 'AND ulid_invoice = $1' : ''}
          ${ulid_order ? 'AND ulid_order = $2' : ''}
          ${ulid_user ? 'AND ulid_user = $3' : ''}
          ${cod_status ? 'AND cod_status = $4' : ''}`,
      [ulid_invoice, ulid_order, ulid_user, cod_status]
    );
  }
}

/**
 * Atualiza uma fatura
 *
 * @param ulid_invoice ID da fatura
 * @param cod_status Código do status
 * @returns Resultado da consulta
 */
async function update(ulid_invoice: string, cod_status: number): Promise<QueryResult> {
  try {
    // Usar a nova implementação
    const invoice = await repositories.invoiceRepository.update(ulid_invoice, {
      statusCode: cod_status,
    });

    // Converter para o formato esperado pelo código antigo
    return {
      rows: [invoice],
      rowCount: 1,
      command: 'UPDATE',
      oid: 0,
      fields: [],
    };
  } catch (error) {
    console.error('Error in compatibility layer (invoiceRepository.update):', error);

    // Fallback para a implementação antiga em caso de erro
    return pgHelper.query(
      `UPDATE tab_invoice 
          SET cod_status   = $2 
        WHERE ulid_invoice = $1 
       RETURNING *`,
      [ulid_invoice, cod_status]
    );
  }
}

/**
 * Remove uma fatura
 *
 * @param ulid_invoice ID da fatura
 * @returns Resultado da consulta
 */
async function deleteByUlid(ulid_invoice: string): Promise<QueryResult> {
  try {
    // Usar a nova implementação
    const success = await repositories.invoiceRepository.delete(ulid_invoice);

    // Converter para o formato esperado pelo código antigo
    return {
      rows: success ? [{ ulid_invoice }] : [],
      rowCount: success ? 1 : 0,
      command: 'DELETE',
      oid: 0,
      fields: [],
    };
  } catch (error) {
    console.error('Error in compatibility layer (invoiceRepository.deleteByUlid):', error);

    // Fallback para a implementação antiga em caso de erro
    return pgHelper.query(
      `DELETE FROM tab_invoice 
        WHERE ulid_invoice = $1 
       RETURNING *`,
      [ulid_invoice]
    );
  }
}

export const invoiceRepository = {
  create,
  read,
  update,
  deleteByUlid,
};
