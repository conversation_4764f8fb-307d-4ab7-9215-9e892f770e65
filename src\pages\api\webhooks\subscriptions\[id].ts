import { isAuthenticated } from '@middleware/authMiddleware';
import { WebhookEvent, WebhookStatus } from '@models/WebhookSubscription';
import { webhookRepository } from '@repositories/webhookRepository';
import { validateSchema } from '@utils/validation';
// src/pages/api/webhooks/subscriptions/[id].ts
import type { APIRoute } from 'astro';
import { z } from 'zod';

// Schema para validação de atualização de assinatura
const updateSubscriptionSchema = z.object({
  name: z.string().min(3).max(100).optional(),
  url: z.string().url().optional(),
  events: z.array(z.nativeEnum(WebhookEvent)).min(1).optional(),
  status: z.nativeEnum(WebhookStatus).optional(),
  secretKey: z.string().min(16).max(64).optional(),
  description: z.string().max(500).optional(),
  headers: z.record(z.string()).optional(),
});

/**
 * Endpoint para obter uma assinatura de webhook específica
 */
export const GET: APIRoute = async ({ request, cookies, params }) => {
  try {
    // Verificar autenticação
    const authResult = await isAuthenticated({ request, cookies } as any);

    if (!authResult.isAuthenticated) {
      return new Response(JSON.stringify({ error: 'Não autorizado' }), {
        status: 401,
        headers: {
          'Content-Type': 'application/json',
        },
      });
    }

    // Obter ID da assinatura
    const { id } = params;

    if (!id) {
      return new Response(JSON.stringify({ error: 'ID da assinatura não fornecido' }), {
        status: 400,
        headers: {
          'Content-Type': 'application/json',
        },
      });
    }

    // Obter assinatura
    const subscription = await webhookRepository.getSubscriptionById(id);

    if (!subscription) {
      return new Response(JSON.stringify({ error: 'Assinatura de webhook não encontrada' }), {
        status: 404,
        headers: {
          'Content-Type': 'application/json',
        },
      });
    }

    // Remover chave secreta da resposta
    const { secretKey, ...sanitizedSubscription } = subscription;

    // Retornar resposta
    return new Response(JSON.stringify({ subscription: sanitizedSubscription }), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  } catch (error) {
    console.error('Erro ao obter assinatura de webhook:', error);

    return new Response(
      JSON.stringify({
        error: error instanceof Error ? error.message : 'Erro interno',
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  }
};

/**
 * Endpoint para atualizar uma assinatura de webhook
 */
export const PUT: APIRoute = async ({ request, cookies, params }) => {
  try {
    // Verificar autenticação
    const authResult = await isAuthenticated({ request, cookies } as any);

    if (!authResult.isAuthenticated) {
      return new Response(JSON.stringify({ error: 'Não autorizado' }), {
        status: 401,
        headers: {
          'Content-Type': 'application/json',
        },
      });
    }

    // Obter ID da assinatura
    const { id } = params;

    if (!id) {
      return new Response(JSON.stringify({ error: 'ID da assinatura não fornecido' }), {
        status: 400,
        headers: {
          'Content-Type': 'application/json',
        },
      });
    }

    // Verificar se assinatura existe
    const existingSubscription = await webhookRepository.getSubscriptionById(id);

    if (!existingSubscription) {
      return new Response(JSON.stringify({ error: 'Assinatura de webhook não encontrada' }), {
        status: 404,
        headers: {
          'Content-Type': 'application/json',
        },
      });
    }

    // Obter corpo da requisição
    const body = await request.json();

    // Validar dados
    const validationResult = validateSchema(updateSubscriptionSchema, body);

    if (!validationResult.success) {
      return new Response(
        JSON.stringify({
          error: 'Dados inválidos',
          details: validationResult.errors,
        }),
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    // Atualizar assinatura
    const updatedSubscription = await webhookRepository.updateSubscription(id, body);

    // Remover chave secreta da resposta
    const { secretKey, ...sanitizedSubscription } = updatedSubscription;

    // Retornar resposta
    return new Response(
      JSON.stringify({
        message: 'Assinatura de webhook atualizada com sucesso',
        subscription: sanitizedSubscription,
      }),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  } catch (error) {
    console.error('Erro ao atualizar assinatura de webhook:', error);

    return new Response(
      JSON.stringify({
        error: error instanceof Error ? error.message : 'Erro interno',
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  }
};

/**
 * Endpoint para excluir uma assinatura de webhook
 */
export const DELETE: APIRoute = async ({ request, cookies, params }) => {
  try {
    // Verificar autenticação
    const authResult = await isAuthenticated({ request, cookies } as any);

    if (!authResult.isAuthenticated) {
      return new Response(JSON.stringify({ error: 'Não autorizado' }), {
        status: 401,
        headers: {
          'Content-Type': 'application/json',
        },
      });
    }

    // Obter ID da assinatura
    const { id } = params;

    if (!id) {
      return new Response(JSON.stringify({ error: 'ID da assinatura não fornecido' }), {
        status: 400,
        headers: {
          'Content-Type': 'application/json',
        },
      });
    }

    // Verificar se assinatura existe
    const existingSubscription = await webhookRepository.getSubscriptionById(id);

    if (!existingSubscription) {
      return new Response(JSON.stringify({ error: 'Assinatura de webhook não encontrada' }), {
        status: 404,
        headers: {
          'Content-Type': 'application/json',
        },
      });
    }

    // Excluir assinatura
    await webhookRepository.deleteSubscription(id);

    // Retornar resposta
    return new Response(
      JSON.stringify({
        message: 'Assinatura de webhook excluída com sucesso',
      }),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  } catch (error) {
    console.error('Erro ao excluir assinatura de webhook:', error);

    return new Response(
      JSON.stringify({
        error: error instanceof Error ? error.message : 'Erro interno',
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  }
};
