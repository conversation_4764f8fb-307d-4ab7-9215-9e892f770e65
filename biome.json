{"$schema": "https://biomejs.dev/schemas/1.9.4/schema.json", "organizeImports": {"enabled": true}, "linter": {"enabled": true, "rules": {"recommended": true, "style": {"noNonNullAssertion": "off", "useImportType": "off"}, "suspicious": {"noExplicitAny": "warn", "noArrayIndexKey": "off"}, "complexity": {"noForEach": "off"}}}, "formatter": {"enabled": true, "formatWithErrors": false, "indentStyle": "space", "indentWidth": 2, "lineWidth": 100, "lineEnding": "lf"}, "javascript": {"formatter": {"quoteStyle": "single", "trailingCommas": "es5", "semicolons": "always"}}, "files": {"include": ["src/**/*.ts", "src/**/*.js", "src/**/*.astro", "*.ts", "*.js"], "ignore": ["node_modules/**", "dist/**", ".astro/**", "public/**", "coverage/**"]}}