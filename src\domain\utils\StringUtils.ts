/**
 * Utilitários para manipulação de strings
 *
 * Este arquivo contém funções utilitárias para manipulação e validação de strings.
 * Parte da implementação da tarefa 9.1.1 - Testes unitários
 */

/**
 * Converte uma string para um formato de slug (URL amigável)
 * @param str String a ser convertida
 * @returns String no formato de slug
 */
export function slugify(str: string): string {
  if (str === null || str === undefined) return '';

  return String(str)
    .normalize('NFD')
    .replace(/[\u0300-\u036f]/g, '')
    .toLowerCase()
    .trim()
    .replace(/[^a-z0-9 ]/g, '')
    .replace(/\s+/g, '-')
    .replace(/-+/g, '-');
}

/**
 * Trunca uma string para um tamanho máximo
 * @param str String a ser truncada
 * @param maxLength Tamanho máximo da string
 * @param suffix Sufixo a ser adicionado quando a string for truncada (padrão: '...')
 * @returns String truncada
 */
export function truncate(str: string, maxLength: number, suffix = '...'): string {
  if (str === null || str === undefined) return '';

  const strValue = String(str);
  if (strValue.length <= maxLength) return strValue;

  return strValue.substring(0, maxLength) + suffix;
}

/**
 * Capitaliza a primeira letra de uma string
 * @param str String a ser capitalizada
 * @returns String com a primeira letra maiúscula
 */
export function capitalize(str: string): string {
  if (str === null || str === undefined) return '';

  const strValue = String(str);
  if (strValue.length === 0) return strValue;

  return strValue.charAt(0).toUpperCase() + strValue.slice(1);
}

/**
 * Formata um número como moeda
 * @param value Valor a ser formatado
 * @param locale Localização para formatação (padrão: 'pt-BR')
 * @param currency Moeda para formatação (padrão: 'BRL')
 * @returns String formatada como moeda
 */
export function formatCurrency(value: number, locale = 'pt-BR', currency = 'BRL'): string {
  const numValue = Number(value) || 0;

  return new Intl.NumberFormat(locale, {
    style: 'currency',
    currency,
  }).format(numValue);
}

/**
 * Formata um CPF
 * @param cpf CPF a ser formatado
 * @returns CPF formatado (123.456.789-09)
 */
export function formatCpf(cpf: string): string {
  if (cpf === null || cpf === undefined) return '';

  const cpfValue = String(cpf).replace(/\D/g, '');

  if (cpfValue.length !== 11) return cpfValue;

  return cpfValue.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, '$1.$2.$3-$4');
}

/**
 * Formata um CNPJ
 * @param cnpj CNPJ a ser formatado
 * @returns CNPJ formatado (12.345.678/0001-99)
 */
export function formatCnpj(cnpj: string): string {
  if (cnpj === null || cnpj === undefined) return '';

  const cnpjValue = String(cnpj).replace(/\D/g, '');

  if (cnpjValue.length !== 14) return cnpjValue;

  return cnpjValue.replace(/(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})/, '$1.$2.$3/$4-$5');
}

/**
 * Formata um número de telefone
 * @param phone Telefone a ser formatado
 * @returns Telefone formatado ((12) 3456-7890 ou (12) 34567-8901)
 */
export function formatPhone(phone: string): string {
  if (phone === null || phone === undefined) return '';

  const phoneValue = String(phone).replace(/\D/g, '');

  if (phoneValue.length < 10) return phoneValue;

  if (phoneValue.length === 10) {
    return phoneValue.replace(/(\d{2})(\d{4})(\d{4})/, '($1) $2-$3');
  }

  return phoneValue.replace(/(\d{2})(\d{5})(\d{4})/, '($1) $2-$3');
}

/**
 * Formata um CEP
 * @param cep CEP a ser formatado
 * @returns CEP formatado (12345-678)
 */
export function formatCep(cep: string): string {
  if (cep === null || cep === undefined) return '';

  const cepValue = String(cep).replace(/\D/g, '');

  if (cepValue.length !== 8) return cepValue;

  return cepValue.replace(/(\d{5})(\d{3})/, '$1-$2');
}

/**
 * Remove acentos de uma string
 * @param str String com acentos
 * @returns String sem acentos
 */
export function removeAccents(str: string): string {
  if (str === null || str === undefined) return '';

  return String(str)
    .normalize('NFD')
    .replace(/[\u0300-\u036f]/g, '');
}

/**
 * Valida se uma string é um email válido
 * @param email Email a ser validado
 * @returns true se o email for válido, false caso contrário
 */
export function isValidEmail(email: string): boolean {
  if (email === null || email === undefined) return false;

  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(String(email));
}

/**
 * Valida se uma string é um CPF válido
 * @param cpf CPF a ser validado
 * @returns true se o CPF for válido, false caso contrário
 */
export function isValidCpf(cpf: string): boolean {
  if (cpf === null || cpf === undefined) return false;

  const cpfValue = String(cpf).replace(/\D/g, '');

  if (cpfValue.length !== 11) return false;

  // Verifica se todos os dígitos são iguais
  if (/^(\d)\1+$/.test(cpfValue)) return false;

  // Validação do primeiro dígito verificador
  let sum = 0;
  for (let i = 0; i < 9; i++) {
    sum += Number.parseInt(cpfValue.charAt(i)) * (10 - i);
  }

  let remainder = sum % 11;
  const digit1 = remainder < 2 ? 0 : 11 - remainder;

  if (Number.parseInt(cpfValue.charAt(9)) !== digit1) return false;

  // Validação do segundo dígito verificador
  sum = 0;
  for (let i = 0; i < 10; i++) {
    sum += Number.parseInt(cpfValue.charAt(i)) * (11 - i);
  }

  remainder = sum % 11;
  const digit2 = remainder < 2 ? 0 : 11 - remainder;

  return Number.parseInt(cpfValue.charAt(10)) === digit2;
}

/**
 * Valida se uma string é um CNPJ válido
 * @param cnpj CNPJ a ser validado
 * @returns true se o CNPJ for válido, false caso contrário
 */
export function isValidCnpj(cnpj: string): boolean {
  if (cnpj === null || cnpj === undefined) return false;

  const cnpjValue = String(cnpj).replace(/\D/g, '');

  if (cnpjValue.length !== 14) return false;

  // Verifica se todos os dígitos são iguais
  if (/^(\d)\1+$/.test(cnpjValue)) return false;

  // Validação do primeiro dígito verificador
  let size = cnpjValue.length - 2;
  let numbers = cnpjValue.substring(0, size);
  const digits = cnpjValue.substring(size);
  let sum = 0;
  let pos = size - 7;

  for (let i = size; i >= 1; i--) {
    sum += Number.parseInt(numbers.charAt(size - i)) * pos--;
    if (pos < 2) pos = 9;
  }

  let result = sum % 11 < 2 ? 0 : 11 - (sum % 11);
  if (result !== Number.parseInt(digits.charAt(0))) return false;

  // Validação do segundo dígito verificador
  size += 1;
  numbers = cnpjValue.substring(0, size);
  sum = 0;
  pos = size - 7;

  for (let i = size; i >= 1; i--) {
    sum += Number.parseInt(numbers.charAt(size - i)) * pos--;
    if (pos < 2) pos = 9;
  }

  result = sum % 11 < 2 ? 0 : 11 - (sum % 11);

  return result === Number.parseInt(digits.charAt(1));
}

/**
 * Valida se uma string é um telefone válido
 * @param phone Telefone a ser validado
 * @returns true se o telefone for válido, false caso contrário
 */
export function isValidPhone(phone: string): boolean {
  if (phone === null || phone === undefined) return false;

  const phoneValue = String(phone).replace(/\D/g, '');

  return phoneValue.length === 10 || phoneValue.length === 11;
}

/**
 * Valida se uma string é um CEP válido
 * @param cep CEP a ser validado
 * @returns true se o CEP for válido, false caso contrário
 */
export function isValidCep(cep: string): boolean {
  if (cep === null || cep === undefined) return false;

  const cepValue = String(cep).replace(/\D/g, '');

  return cepValue.length === 8;
}
