/**
 * OAuth Social Auth Service
 *
 * Implementação do serviço de autenticação social usando OAuth.
 * Parte da implementação da tarefa 8.9.2 - Integração com redes sociais
 */

import axios from 'axios';
import {
  SocialAuthConfig,
  SocialAuthProfile,
  SocialAuthService,
} from '../../domain/services/SocialAuthService';

export class OAuthSocialAuthService implements SocialAuthService {
  private config: SocialAuthConfig = {};

  /**
   * Inicializa o serviço com as configurações de autenticação social
   */
  async initialize(config: SocialAuthConfig): Promise<boolean> {
    try {
      this.config = config;
      return true;
    } catch (error) {
      console.error('Erro ao inicializar serviço de autenticação social:', error);
      return false;
    }
  }

  /**
   * Gera URL para autenticação com uma rede social
   */
  getAuthUrl(
    provider: 'facebook' | 'google' | 'twitter' | 'linkedin' | 'github',
    state?: string,
    options?: Record<string, any>
  ): string {
    switch (provider) {
      case 'facebook': {
        if (!this.config.facebook) {
          throw new Error('Configuração do Facebook não encontrada');
        }

        const fbScope = options?.scope || this.config.facebook.scope || 'email,public_profile';

        return `https://www.facebook.com/v16.0/dialog/oauth?client_id=${this.config.facebook.appId}&redirect_uri=${encodeURIComponent(this.config.facebook.redirectUri)}&scope=${encodeURIComponent(fbScope)}&state=${state || ''}`;
      }

      case 'google': {
        if (!this.config.google) {
          throw new Error('Configuração do Google não encontrada');
        }

        const googleScope = options?.scope || this.config.google.scope || 'profile email';

        return `https://accounts.google.com/o/oauth2/v2/auth?client_id=${this.config.google.clientId}&redirect_uri=${encodeURIComponent(this.config.google.redirectUri)}&response_type=code&scope=${encodeURIComponent(googleScope)}&state=${state || ''}`;
      }

      case 'twitter':
        if (!this.config.twitter) {
          throw new Error('Configuração do Twitter não encontrada');
        }

        // Twitter usa OAuth 1.0a, que é mais complexo
        // Esta é uma implementação simplificada
        return `https://twitter.com/i/oauth2/authorize?client_id=${this.config.twitter.apiKey}&redirect_uri=${encodeURIComponent(this.config.twitter.redirectUri)}&response_type=code&scope=${encodeURIComponent(this.config.twitter.scope || 'tweet.read users.read')}&state=${state || ''}`;

      case 'linkedin': {
        if (!this.config.linkedin) {
          throw new Error('Configuração do LinkedIn não encontrada');
        }

        const linkedinScope =
          options?.scope || this.config.linkedin.scope || 'r_liteprofile r_emailaddress';

        return `https://www.linkedin.com/oauth/v2/authorization?client_id=${this.config.linkedin.clientId}&redirect_uri=${encodeURIComponent(this.config.linkedin.redirectUri)}&response_type=code&scope=${encodeURIComponent(linkedinScope)}&state=${state || ''}`;
      }

      case 'github': {
        if (!this.config.github) {
          throw new Error('Configuração do GitHub não encontrada');
        }

        const githubScope = options?.scope || this.config.github.scope || 'read:user user:email';

        return `https://github.com/login/oauth/authorize?client_id=${this.config.github.clientId}&redirect_uri=${encodeURIComponent(this.config.github.redirectUri)}&scope=${encodeURIComponent(githubScope)}&state=${state || ''}`;
      }

      default:
        throw new Error(`Provedor ${provider} não suportado`);
    }
  }

  /**
   * Processa o retorno da autenticação social
   */
  async handleCallback(
    provider: 'facebook' | 'google' | 'twitter' | 'linkedin' | 'github',
    code: string,
    state?: string
  ): Promise<SocialAuthProfile> {
    try {
      // Obter token de acesso
      const tokenResponse = await this.getAccessToken(provider, code);

      // Obter perfil do usuário
      return await this.getUserProfile(provider, tokenResponse.accessToken);
    } catch (error) {
      console.error(`Erro ao processar callback do ${provider}:`, error);
      throw error;
    }
  }

  /**
   * Obtém o token de acesso
   */
  private async getAccessToken(
    provider: 'facebook' | 'google' | 'twitter' | 'linkedin' | 'github',
    code: string
  ): Promise<{
    accessToken: string;
    refreshToken?: string;
    expiresAt?: Date;
  }> {
    switch (provider) {
      case 'facebook': {
        if (!this.config.facebook) {
          throw new Error('Configuração do Facebook não encontrada');
        }

        const fbResponse = await axios.get('https://graph.facebook.com/v16.0/oauth/access_token', {
          params: {
            client_id: this.config.facebook.appId,
            client_secret: this.config.facebook.appSecret,
            redirect_uri: this.config.facebook.redirectUri,
            code,
          },
        });

        return {
          accessToken: fbResponse.data.access_token,
          expiresAt: new Date(Date.now() + fbResponse.data.expires_in * 1000),
        };
      }

      case 'google': {
        if (!this.config.google) {
          throw new Error('Configuração do Google não encontrada');
        }

        const googleResponse = await axios.post('https://oauth2.googleapis.com/token', {
          client_id: this.config.google.clientId,
          client_secret: this.config.google.clientSecret,
          redirect_uri: this.config.google.redirectUri,
          code,
          grant_type: 'authorization_code',
        });

        return {
          accessToken: googleResponse.data.access_token,
          refreshToken: googleResponse.data.refresh_token,
          expiresAt: new Date(Date.now() + googleResponse.data.expires_in * 1000),
        };
      }

      // Implementações para outros provedores seguiriam um padrão similar

      default:
        throw new Error(`Provedor ${provider} não suportado`);
    }
  }

  /**
   * Obtém o perfil do usuário autenticado
   */
  async getUserProfile(
    provider: 'facebook' | 'google' | 'twitter' | 'linkedin' | 'github',
    accessToken: string
  ): Promise<SocialAuthProfile> {
    switch (provider) {
      case 'facebook': {
        const fbResponse = await axios.get('https://graph.facebook.com/v16.0/me', {
          params: {
            fields: 'id,email,first_name,last_name,name,picture',
            access_token: accessToken,
          },
        });

        return {
          id: fbResponse.data.id,
          provider: 'facebook',
          email: fbResponse.data.email,
          name: fbResponse.data.name,
          firstName: fbResponse.data.first_name,
          lastName: fbResponse.data.last_name,
          profilePicture: fbResponse.data.picture?.data?.url,
          profileUrl: `https://facebook.com/${fbResponse.data.id}`,
          accessToken,
          raw: fbResponse.data,
        };
      }

      case 'google': {
        const googleResponse = await axios.get('https://www.googleapis.com/oauth2/v3/userinfo', {
          headers: {
            Authorization: `Bearer ${accessToken}`,
          },
        });

        return {
          id: googleResponse.data.sub,
          provider: 'google',
          email: googleResponse.data.email,
          name: googleResponse.data.name,
          firstName: googleResponse.data.given_name,
          lastName: googleResponse.data.family_name,
          profilePicture: googleResponse.data.picture,
          accessToken,
          raw: googleResponse.data,
        };
      }

      // Implementações para outros provedores seguiriam um padrão similar

      default:
        throw new Error(`Provedor ${provider} não suportado`);
    }
  }

  /**
   * Atualiza o token de acesso usando o refresh token
   */
  async refreshAccessToken(
    provider: 'facebook' | 'google' | 'twitter' | 'linkedin' | 'github',
    refreshToken: string
  ): Promise<{
    accessToken: string;
    refreshToken?: string;
    expiresAt?: Date;
  }> {
    switch (provider) {
      case 'google': {
        if (!this.config.google) {
          throw new Error('Configuração do Google não encontrada');
        }

        const googleResponse = await axios.post('https://oauth2.googleapis.com/token', {
          client_id: this.config.google.clientId,
          client_secret: this.config.google.clientSecret,
          refresh_token: refreshToken,
          grant_type: 'refresh_token',
        });

        return {
          accessToken: googleResponse.data.access_token,
          refreshToken: googleResponse.data.refresh_token || refreshToken,
          expiresAt: new Date(Date.now() + googleResponse.data.expires_in * 1000),
        };
      }

      // Implementações para outros provedores seguiriam um padrão similar
      // Nem todos os provedores suportam refresh tokens

      default:
        throw new Error(`Refresh token não suportado para o provedor ${provider}`);
    }
  }

  /**
   * Revoga o token de acesso
   */
  async revokeToken(
    provider: 'facebook' | 'google' | 'twitter' | 'linkedin' | 'github',
    accessToken: string
  ): Promise<boolean> {
    try {
      switch (provider) {
        case 'facebook':
          if (!this.config.facebook) {
            throw new Error('Configuração do Facebook não encontrada');
          }

          await axios.delete('https://graph.facebook.com/v16.0/me/permissions', {
            params: {
              access_token: accessToken,
            },
          });

          return true;

        case 'google':
          if (!this.config.google) {
            throw new Error('Configuração do Google não encontrada');
          }

          await axios.post('https://oauth2.googleapis.com/revoke', null, {
            params: {
              token: accessToken,
            },
          });

          return true;

        // Implementações para outros provedores seguiriam um padrão similar

        default:
          throw new Error(`Revogação de token não suportada para o provedor ${provider}`);
      }
    } catch (error) {
      console.error(`Erro ao revogar token do ${provider}:`, error);
      return false;
    }
  }

  /**
   * Verifica se um token de acesso é válido
   */
  async validateToken(
    provider: 'facebook' | 'google' | 'twitter' | 'linkedin' | 'github',
    accessToken: string
  ): Promise<boolean> {
    try {
      switch (provider) {
        case 'facebook': {
          const fbResponse = await axios.get('https://graph.facebook.com/debug_token', {
            params: {
              input_token: accessToken,
              access_token: `${this.config.facebook?.appId}|${this.config.facebook?.appSecret}`,
            },
          });

          return fbResponse.data.data.is_valid;
        }

        case 'google': {
          const googleResponse = await axios.get('https://oauth2.googleapis.com/tokeninfo', {
            params: {
              access_token: accessToken,
            },
          });

          return !!googleResponse.data.exp;
        }

        // Implementações para outros provedores seguiriam um padrão similar

        default:
          throw new Error(`Validação de token não suportada para o provedor ${provider}`);
      }
    } catch (error) {
      console.error(`Erro ao validar token do ${provider}:`, error);
      return false;
    }
  }

  /**
   * Gera código para inicialização dos SDKs de autenticação social
   */
  generateAuthSdkInitCode(
    providers?: Array<'facebook' | 'google' | 'twitter' | 'linkedin' | 'github'>
  ): string {
    const defaultProviders: Array<'facebook' | 'google' | 'twitter' | 'linkedin' | 'github'> = [
      'facebook',
      'google',
    ];

    const initProviders = providers || defaultProviders;
    let code = '';

    // Facebook SDK
    if (initProviders.includes('facebook') && this.config.facebook) {
      code += `
        <script>
          window.fbAsyncInit = function() {
            FB.init({
              appId: '${this.config.facebook.appId}',
              cookie: true,
              xfbml: true,
              version: 'v16.0'
            });
          };
          
          (function(d, s, id) {
            var js, fjs = d.getElementsByTagName(s)[0];
            if (d.getElementById(id)) return;
            js = d.createElement(s); js.id = id;
            js.src = "https://connect.facebook.net/pt_BR/sdk.js";
            fjs.parentNode.insertBefore(js, fjs);
          }(document, 'script', 'facebook-jssdk'));
        </script>
      `;
    }

    // Google SDK
    if (initProviders.includes('google') && this.config.google) {
      code += `
        <script src="https://accounts.google.com/gsi/client" async defer></script>
        <script>
          function handleGoogleCredentialResponse(response) {
            // Função a ser implementada pelo cliente
            console.log("Google credential response:", response);
          }
        </script>
      `;
    }

    return code;
  }

  /**
   * Gera botões de login social
   */
  generateLoginButtons(
    providers?: Array<'facebook' | 'google' | 'twitter' | 'linkedin' | 'github'>,
    buttonStyle: 'icon' | 'text' | 'icon-text' = 'icon-text',
    redirectUrl?: string
  ): string {
    const defaultProviders: Array<'facebook' | 'google' | 'twitter' | 'linkedin' | 'github'> = [
      'facebook',
      'google',
    ];

    const loginProviders = providers || defaultProviders;
    let html = '<div class="social-login-buttons">';

    loginProviders.forEach((provider) => {
      const authUrl = this.getAuthUrl(provider, '', { redirect_uri: redirectUrl });
      const providerName = this.getProviderName(provider);
      const iconClass = this.getProviderIconClass(provider);

      let buttonContent = '';

      switch (buttonStyle) {
        case 'icon':
          buttonContent = `<i class="${iconClass}" aria-hidden="true"></i><span class="sr-only">Entrar com ${providerName}</span>`;
          break;
        case 'text':
          buttonContent = `Entrar com ${providerName}`;
          break;
        case 'icon-text':
          buttonContent = `<i class="${iconClass}" aria-hidden="true"></i> Entrar com ${providerName}`;
          break;
      }

      html += `
        <a href="${authUrl}" 
           class="social-login-button social-login-${provider}" 
           aria-label="Entrar com ${providerName}">
          ${buttonContent}
        </a>
      `;
    });

    html += '</div>';

    return html;
  }

  /**
   * Obtém o nome do provedor
   */
  private getProviderName(provider: string): string {
    switch (provider) {
      case 'facebook':
        return 'Facebook';
      case 'google':
        return 'Google';
      case 'twitter':
        return 'Twitter';
      case 'linkedin':
        return 'LinkedIn';
      case 'github':
        return 'GitHub';
      default:
        return provider.charAt(0).toUpperCase() + provider.slice(1);
    }
  }

  /**
   * Obtém a classe do ícone do provedor
   */
  private getProviderIconClass(provider: string): string {
    switch (provider) {
      case 'facebook':
        return 'icon icon-facebook';
      case 'google':
        return 'icon icon-google';
      case 'twitter':
        return 'icon icon-twitter';
      case 'linkedin':
        return 'icon icon-linkedin';
      case 'github':
        return 'icon icon-github';
      default:
        return `icon icon-${provider}`;
    }
  }
}
