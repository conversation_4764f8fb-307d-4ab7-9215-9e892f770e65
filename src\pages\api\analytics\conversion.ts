/**
 * Analytics Conversion API
 *
 * Endpoint para rastrear conversões de analytics no lado do servidor.
 * Parte da implementação da tarefa 8.9.3 - Analytics
 */

import type { APIRoute } from 'astro';
import { AnalyticsService } from '../../../domain/services/AnalyticsService';
import { GoogleAnalyticsService } from '../../../infrastructure/services/GoogleAnalyticsService';

// Inicializar serviço de analytics
const analyticsService: AnalyticsService = new GoogleAnalyticsService();

// Configurar serviço
await analyticsService.initialize({
  trackingId: process.env.GOOGLE_ANALYTICS_ID || '',
  anonymizeIp: true,
});

export const POST: APIRoute = async ({ request }) => {
  try {
    // Obter dados do corpo da requisição
    const body = await request.json();

    // Validar dados
    if (!body.goalId || !body.goalName) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Parâmetros obrigatórios: goalId, goalName',
        }),
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    // Rastrear conversão
    const success = await analyticsService.trackConversion({
      goalId: body.goalId,
      goalName: body.goalName,
      value: body.value,
      currency: body.currency,
      userId: body.userId,
      clientId: body.clientId,
      timestamp: body.timestamp ? new Date(body.timestamp) : new Date(),
      sessionId: body.sessionId,
      customDimensions: body.customDimensions,
    });

    if (!success) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Erro ao rastrear conversão',
        }),
        {
          status: 500,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    return new Response(
      JSON.stringify({
        success: true,
        message: 'Conversão rastreada com sucesso',
      }),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  } catch (error) {
    console.error('Erro ao processar requisição de conversão de analytics:', error);

    return new Response(
      JSON.stringify({
        success: false,
        error: 'Erro ao processar requisição',
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  }
};
